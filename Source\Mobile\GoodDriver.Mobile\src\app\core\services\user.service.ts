import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { CreateUserRequestDto } from '../dtos/user/create-user-requestDto';
import { CreateUserResponseDto } from '../dtos/user/create-user-responseDto';
import { ApiService } from './api.service';

@Injectable({
    providedIn: 'root'
  })
export class UserService
{
    constructor(
      private http: HttpClient,
      private apiService: ApiService
    ) {}

  create(data: CreateUserRequestDto): Observable<CreateUserResponseDto> {
    const url = this.apiService.getUrl('create');
    return this.http.post<CreateUserResponseDto>(url, data);
  }
}
