{"version": 3, "sources": ["node_modules/@stencil/core/internal/app-data/index.js", "node_modules/@stencil/core/internal/client/index.js"], "sourcesContent": ["// src/app-data/index.ts\nvar BUILD = {\n  allRenderFn: false,\n  cmpDidLoad: true,\n  cmpDidUnload: false,\n  cmpDidUpdate: true,\n  cmpDidRender: true,\n  cmpWillLoad: true,\n  cmpWillUpdate: true,\n  cmpWillRender: true,\n  connectedCallback: true,\n  disconnectedCallback: true,\n  element: true,\n  event: true,\n  hasRenderFn: true,\n  lifecycle: true,\n  hostListener: true,\n  hostListenerTargetWindow: true,\n  hostListenerTargetDocument: true,\n  hostListenerTargetBody: true,\n  hostListenerTargetParent: false,\n  hostListenerTarget: true,\n  member: true,\n  method: true,\n  mode: true,\n  observeAttribute: true,\n  prop: true,\n  propMutable: true,\n  reflect: true,\n  scoped: true,\n  shadowDom: true,\n  slot: true,\n  cssAnnotations: true,\n  state: true,\n  style: true,\n  formAssociated: false,\n  svg: true,\n  updatable: true,\n  vdomAttribute: true,\n  vdomXlink: true,\n  vdomClass: true,\n  vdomFunctional: true,\n  vdomKey: true,\n  vdomListener: true,\n  vdomRef: true,\n  vdomPropOrAttr: true,\n  vdomRender: true,\n  vdomStyle: true,\n  vdomText: true,\n  watchCallback: true,\n  taskQueue: true,\n  hotModuleReplacement: false,\n  isDebug: false,\n  isDev: false,\n  isTesting: false,\n  hydrateServerSide: false,\n  hydrateClientSide: false,\n  lifecycleDOMEvents: false,\n  lazyLoad: false,\n  profile: false,\n  slotRelocation: true,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  appendChildSlotFix: false,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  cloneNodeFix: false,\n  hydratedAttribute: false,\n  hydratedClass: true,\n  // TODO(STENCIL-1305): remove this option\n  scriptDataOpts: false,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  scopedSlotTextContentFix: false,\n  // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n  shadowDomShim: false,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  slotChildNodesFix: false,\n  invisiblePrehydration: true,\n  propBoolean: true,\n  propNumber: true,\n  propString: true,\n  constructableCSS: true,\n  cmpShouldUpdate: true,\n  devTools: false,\n  shadowDelegatesFocus: true,\n  initializeNextTick: false,\n  asyncLoading: false,\n  asyncQueue: false,\n  transformTagName: false,\n  attachStyles: true,\n  // TODO(STENCIL-914): remove this option when `experimentalSlotFixes` is the default behavior\n  experimentalSlotFixes: false\n};\nvar Env = {};\nvar NAMESPACE = /* default */\n\"app\";\nexport { BUILD, Env, NAMESPACE };", "/*\n Stencil Client Platform v4.20.0 | MIT Licensed | https://stenciljs.com\n */\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all) __defProp(target, name, {\n    get: all[name],\n    enumerable: true\n  });\n};\n\n// src/client/client-build.ts\nimport { BUILD } from \"@stencil/core/internal/app-data\";\nvar Build = {\n  isDev: BUILD.isDev ? true : false,\n  isBrowser: true,\n  isServer: false,\n  isTesting: BUILD.isTesting ? true : false\n};\n\n// src/client/client-host-ref.ts\nimport { BUILD as BUILD2 } from \"@stencil/core/internal/app-data\";\nvar hostRefs = BUILD2.hotModuleReplacement ? window.__STENCIL_HOSTREFS__ || (window.__STENCIL_HOSTREFS__ = /* @__PURE__ */new WeakMap()) : /* @__PURE__ */new WeakMap();\nvar getHostRef = ref => hostRefs.get(ref);\nvar registerInstance = (lazyInstance, hostRef) => hostRefs.set(hostRef.$lazyInstance$ = lazyInstance, hostRef);\nvar registerHost = (hostElement, cmpMeta) => {\n  const hostRef = {\n    $flags$: 0,\n    $hostElement$: hostElement,\n    $cmpMeta$: cmpMeta,\n    $instanceValues$: /* @__PURE__ */new Map()\n  };\n  if (BUILD2.isDev) {\n    hostRef.$renderCount$ = 0;\n  }\n  if (BUILD2.method && BUILD2.lazyLoad) {\n    hostRef.$onInstancePromise$ = new Promise(r => hostRef.$onInstanceResolve$ = r);\n  }\n  if (BUILD2.asyncLoading) {\n    hostRef.$onReadyPromise$ = new Promise(r => hostRef.$onReadyResolve$ = r);\n    hostElement[\"s-p\"] = [];\n    hostElement[\"s-rc\"] = [];\n  }\n  return hostRefs.set(hostElement, hostRef);\n};\nvar isMemberInElement = (elm, memberName) => memberName in elm;\n\n// src/client/client-load-module.ts\nimport { BUILD as BUILD4 } from \"@stencil/core/internal/app-data\";\n\n// src/client/client-log.ts\nimport { BUILD as BUILD3 } from \"@stencil/core/internal/app-data\";\nvar customError;\nvar consoleError = (e, el) => (customError || console.error)(e, el);\nvar STENCIL_DEV_MODE = BUILD3.isTesting ? [\"STENCIL:\"] : [\"%cstencil\", \"color: white;background:#4c47ff;font-weight: bold; font-size:10px; padding:2px 6px; border-radius: 5px\"];\nvar consoleDevError = (...m) => console.error(...STENCIL_DEV_MODE, ...m);\nvar consoleDevWarn = (...m) => console.warn(...STENCIL_DEV_MODE, ...m);\nvar consoleDevInfo = (...m) => console.info(...STENCIL_DEV_MODE, ...m);\nvar setErrorHandler = handler => customError = handler;\n\n// src/client/client-load-module.ts\nvar cmpModules = /* @__PURE__ */new Map();\nvar MODULE_IMPORT_PREFIX = \"./\";\nvar loadModule = (cmpMeta, hostRef, hmrVersionId) => {\n  const exportName = cmpMeta.$tagName$.replace(/-/g, \"_\");\n  const bundleId = cmpMeta.$lazyBundleId$;\n  if (BUILD4.isDev && typeof bundleId !== \"string\") {\n    consoleDevError(`Trying to lazily load component <${cmpMeta.$tagName$}> with style mode \"${hostRef.$modeName$}\", but it does not exist.`);\n    return void 0;\n  } else if (!bundleId) {\n    return void 0;\n  }\n  const module = !BUILD4.hotModuleReplacement ? cmpModules.get(bundleId) : false;\n  if (module) {\n    return module[exportName];\n  }\n  /*!__STENCIL_STATIC_IMPORT_SWITCH__*/\n  return import(/* @vite-ignore */\n  /* webpackInclude: /\\.entry\\.js$/ */\n  /* webpackExclude: /\\.system\\.entry\\.js$/ */\n  /* webpackMode: \"lazy\" */\n  `./${bundleId}.entry.js${BUILD4.hotModuleReplacement && hmrVersionId ? \"?s-hmr=\" + hmrVersionId : \"\"}`).then(importedModule => {\n    if (!BUILD4.hotModuleReplacement) {\n      cmpModules.set(bundleId, importedModule);\n    }\n    return importedModule[exportName];\n  }, consoleError);\n};\n\n// src/client/client-style.ts\nvar styles = /* @__PURE__ */new Map();\nvar modeResolutionChain = [];\n\n// src/client/client-task-queue.ts\nimport { BUILD as BUILD6 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/runtime-constants.ts\nvar CONTENT_REF_ID = \"r\";\nvar ORG_LOCATION_ID = \"o\";\nvar SLOT_NODE_ID = \"s\";\nvar TEXT_NODE_ID = \"t\";\nvar HYDRATE_ID = \"s-id\";\nvar HYDRATED_STYLE_ID = \"sty-id\";\nvar HYDRATE_CHILD_ID = \"c-id\";\nvar HYDRATED_CSS = \"{visibility:hidden}.hydrated{visibility:inherit}\";\nvar SLOT_FB_CSS = \"slot-fb{display:contents}slot-fb[hidden]{display:none}\";\nvar XLINK_NS = \"http://www.w3.org/1999/xlink\";\nvar FORM_ASSOCIATED_CUSTOM_ELEMENT_CALLBACKS = [\"formAssociatedCallback\", \"formResetCallback\", \"formDisabledCallback\", \"formStateRestoreCallback\"];\n\n// src/client/client-window.ts\nimport { BUILD as BUILD5 } from \"@stencil/core/internal/app-data\";\nvar win = typeof window !== \"undefined\" ? window : {};\nvar doc = win.document || {\n  head: {}\n};\nvar H = win.HTMLElement || class {};\nvar plt = {\n  $flags$: 0,\n  $resourcesUrl$: \"\",\n  jmp: h2 => h2(),\n  raf: h2 => requestAnimationFrame(h2),\n  ael: (el, eventName, listener, opts) => el.addEventListener(eventName, listener, opts),\n  rel: (el, eventName, listener, opts) => el.removeEventListener(eventName, listener, opts),\n  ce: (eventName, opts) => new CustomEvent(eventName, opts)\n};\nvar setPlatformHelpers = helpers => {\n  Object.assign(plt, helpers);\n};\nvar supportsShadow = BUILD5.shadowDom;\nvar supportsListenerOptions = /* @__PURE__ */(() => {\n  let supportsListenerOptions2 = false;\n  try {\n    doc.addEventListener(\"e\", null, Object.defineProperty({}, \"passive\", {\n      get() {\n        supportsListenerOptions2 = true;\n      }\n    }));\n  } catch (e) {}\n  return supportsListenerOptions2;\n})();\nvar promiseResolve = v => Promise.resolve(v);\nvar supportsConstructableStylesheets = BUILD5.constructableCSS ? /* @__PURE__ */(() => {\n  try {\n    new CSSStyleSheet();\n    return typeof new CSSStyleSheet().replaceSync === \"function\";\n  } catch (e) {}\n  return false;\n})() : false;\n\n// src/client/client-task-queue.ts\nvar queueCongestion = 0;\nvar queuePending = false;\nvar queueDomReads = [];\nvar queueDomWrites = [];\nvar queueDomWritesLow = [];\nvar queueTask = (queue, write) => cb => {\n  queue.push(cb);\n  if (!queuePending) {\n    queuePending = true;\n    if (write && plt.$flags$ & 4 /* queueSync */) {\n      nextTick(flush);\n    } else {\n      plt.raf(flush);\n    }\n  }\n};\nvar consume = queue => {\n  for (let i2 = 0; i2 < queue.length; i2++) {\n    try {\n      queue[i2](performance.now());\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  queue.length = 0;\n};\nvar consumeTimeout = (queue, timeout) => {\n  let i2 = 0;\n  let ts = 0;\n  while (i2 < queue.length && (ts = performance.now()) < timeout) {\n    try {\n      queue[i2++](ts);\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  if (i2 === queue.length) {\n    queue.length = 0;\n  } else if (i2 !== 0) {\n    queue.splice(0, i2);\n  }\n};\nvar flush = () => {\n  if (BUILD6.asyncQueue) {\n    queueCongestion++;\n  }\n  consume(queueDomReads);\n  if (BUILD6.asyncQueue) {\n    const timeout = (plt.$flags$ & 6 /* queueMask */) === 2 /* appLoaded */ ? performance.now() + 14 * Math.ceil(queueCongestion * (1 / 10)) : Infinity;\n    consumeTimeout(queueDomWrites, timeout);\n    consumeTimeout(queueDomWritesLow, timeout);\n    if (queueDomWrites.length > 0) {\n      queueDomWritesLow.push(...queueDomWrites);\n      queueDomWrites.length = 0;\n    }\n    if (queuePending = queueDomReads.length + queueDomWrites.length + queueDomWritesLow.length > 0) {\n      plt.raf(flush);\n    } else {\n      queueCongestion = 0;\n    }\n  } else {\n    consume(queueDomWrites);\n    if (queuePending = queueDomReads.length > 0) {\n      plt.raf(flush);\n    }\n  }\n};\nvar nextTick = cb => promiseResolve().then(cb);\nvar readTask = /* @__PURE__ */queueTask(queueDomReads, false);\nvar writeTask = /* @__PURE__ */queueTask(queueDomWrites, true);\n\n// src/client/index.ts\nimport { BUILD as BUILD27, Env, NAMESPACE as NAMESPACE2 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/asset-path.ts\nvar getAssetPath = path => {\n  const assetUrl = new URL(path, plt.$resourcesUrl$);\n  return assetUrl.origin !== win.location.origin ? assetUrl.href : assetUrl.pathname;\n};\nvar setAssetPath = path => plt.$resourcesUrl$ = path;\n\n// src/runtime/bootstrap-custom-element.ts\nimport { BUILD as BUILD24 } from \"@stencil/core/internal/app-data\";\n\n// src/utils/constants.ts\nvar EMPTY_OBJ = {};\nvar SVG_NS = \"http://www.w3.org/2000/svg\";\nvar HTML_NS = \"http://www.w3.org/1999/xhtml\";\n\n// src/utils/helpers.ts\nvar isDef = v => v != null;\nvar isComplexType = o => {\n  o = typeof o;\n  return o === \"object\" || o === \"function\";\n};\n\n// src/utils/query-nonce-meta-tag-content.ts\nfunction queryNonceMetaTagContent(doc2) {\n  var _a, _b, _c;\n  return (_c = (_b = (_a = doc2.head) == null ? void 0 : _a.querySelector('meta[name=\"csp-nonce\"]')) == null ? void 0 : _b.getAttribute(\"content\")) != null ? _c : void 0;\n}\n\n// src/utils/result.ts\nvar result_exports = {};\n__export(result_exports, {\n  err: () => err,\n  map: () => map,\n  ok: () => ok,\n  unwrap: () => unwrap,\n  unwrapErr: () => unwrapErr\n});\nvar ok = value => ({\n  isOk: true,\n  isErr: false,\n  value\n});\nvar err = value => ({\n  isOk: false,\n  isErr: true,\n  value\n});\nfunction map(result, fn) {\n  if (result.isOk) {\n    const val = fn(result.value);\n    if (val instanceof Promise) {\n      return val.then(newVal => ok(newVal));\n    } else {\n      return ok(val);\n    }\n  }\n  if (result.isErr) {\n    const value = result.value;\n    return err(value);\n  }\n  throw \"should never get here\";\n}\nvar unwrap = result => {\n  if (result.isOk) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\nvar unwrapErr = result => {\n  if (result.isErr) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\n\n// src/runtime/connected-callback.ts\nimport { BUILD as BUILD21 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/client-hydrate.ts\nimport { BUILD as BUILD9 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/profile.ts\nimport { BUILD as BUILD7 } from \"@stencil/core/internal/app-data\";\nvar i = 0;\nvar createTime = (fnName, tagName = \"\") => {\n  if (BUILD7.profile && performance.mark) {\n    const key = `st:${fnName}:${tagName}:${i++}`;\n    performance.mark(key);\n    return () => performance.measure(`[Stencil] ${fnName}() <${tagName}>`, key);\n  } else {\n    return () => {\n      return;\n    };\n  }\n};\nvar uniqueTime = (key, measureText) => {\n  if (BUILD7.profile && performance.mark) {\n    if (performance.getEntriesByName(key, \"mark\").length === 0) {\n      performance.mark(key);\n    }\n    return () => {\n      if (performance.getEntriesByName(measureText, \"measure\").length === 0) {\n        performance.measure(measureText, key);\n      }\n    };\n  } else {\n    return () => {\n      return;\n    };\n  }\n};\nvar inspect = ref => {\n  const hostRef = getHostRef(ref);\n  if (!hostRef) {\n    return void 0;\n  }\n  const flags = hostRef.$flags$;\n  const hostElement = hostRef.$hostElement$;\n  return {\n    renderCount: hostRef.$renderCount$,\n    flags: {\n      hasRendered: !!(flags & 2 /* hasRendered */),\n      hasConnected: !!(flags & 1 /* hasConnected */),\n      isWaitingForChildren: !!(flags & 4 /* isWaitingForChildren */),\n      isConstructingInstance: !!(flags & 8 /* isConstructingInstance */),\n      isQueuedForUpdate: !!(flags & 16 /* isQueuedForUpdate */),\n      hasInitializedComponent: !!(flags & 32 /* hasInitializedComponent */),\n      hasLoadedComponent: !!(flags & 64 /* hasLoadedComponent */),\n      isWatchReady: !!(flags & 128 /* isWatchReady */),\n      isListenReady: !!(flags & 256 /* isListenReady */),\n      needsRerender: !!(flags & 512 /* needsRerender */)\n    },\n    instanceValues: hostRef.$instanceValues$,\n    ancestorComponent: hostRef.$ancestorComponent$,\n    hostElement,\n    lazyInstance: hostRef.$lazyInstance$,\n    vnode: hostRef.$vnode$,\n    modeName: hostRef.$modeName$,\n    onReadyPromise: hostRef.$onReadyPromise$,\n    onReadyResolve: hostRef.$onReadyResolve$,\n    onInstancePromise: hostRef.$onInstancePromise$,\n    onInstanceResolve: hostRef.$onInstanceResolve$,\n    onRenderResolve: hostRef.$onRenderResolve$,\n    queuedListeners: hostRef.$queuedListeners$,\n    rmListeners: hostRef.$rmListeners$,\n    [\"s-id\"]: hostElement[\"s-id\"],\n    [\"s-cr\"]: hostElement[\"s-cr\"],\n    [\"s-lr\"]: hostElement[\"s-lr\"],\n    [\"s-p\"]: hostElement[\"s-p\"],\n    [\"s-rc\"]: hostElement[\"s-rc\"],\n    [\"s-sc\"]: hostElement[\"s-sc\"]\n  };\n};\nvar installDevTools = () => {\n  if (BUILD7.devTools) {\n    const stencil = win.stencil = win.stencil || {};\n    const originalInspect = stencil.inspect;\n    stencil.inspect = ref => {\n      let result = inspect(ref);\n      if (!result && typeof originalInspect === \"function\") {\n        result = originalInspect(ref);\n      }\n      return result;\n    };\n  }\n};\n\n// src/runtime/vdom/h.ts\nimport { BUILD as BUILD8 } from \"@stencil/core/internal/app-data\";\nvar h = (nodeName, vnodeData, ...children) => {\n  let child = null;\n  let key = null;\n  let slotName = null;\n  let simple = false;\n  let lastSimple = false;\n  const vNodeChildren = [];\n  const walk = c => {\n    for (let i2 = 0; i2 < c.length; i2++) {\n      child = c[i2];\n      if (Array.isArray(child)) {\n        walk(child);\n      } else if (child != null && typeof child !== \"boolean\") {\n        if (simple = typeof nodeName !== \"function\" && !isComplexType(child)) {\n          child = String(child);\n        } else if (BUILD8.isDev && typeof nodeName !== \"function\" && child.$flags$ === void 0) {\n          consoleDevError(`vNode passed as children has unexpected type.\nMake sure it's using the correct h() function.\nEmpty objects can also be the cause, look for JSX comments that became objects.`);\n        }\n        if (simple && lastSimple) {\n          vNodeChildren[vNodeChildren.length - 1].$text$ += child;\n        } else {\n          vNodeChildren.push(simple ? newVNode(null, child) : child);\n        }\n        lastSimple = simple;\n      }\n    }\n  };\n  walk(children);\n  if (vnodeData) {\n    if (BUILD8.isDev && nodeName === \"input\") {\n      validateInputProperties(vnodeData);\n    }\n    if (BUILD8.vdomKey && vnodeData.key) {\n      key = vnodeData.key;\n    }\n    if (BUILD8.slotRelocation && vnodeData.name) {\n      slotName = vnodeData.name;\n    }\n    if (BUILD8.vdomClass) {\n      const classData = vnodeData.className || vnodeData.class;\n      if (classData) {\n        vnodeData.class = typeof classData !== \"object\" ? classData : Object.keys(classData).filter(k => classData[k]).join(\" \");\n      }\n    }\n  }\n  if (BUILD8.isDev && vNodeChildren.some(isHost)) {\n    consoleDevError(`The <Host> must be the single root component. Make sure:\n- You are NOT using hostData() and <Host> in the same component.\n- <Host> is used once, and it's the single root component of the render() function.`);\n  }\n  if (BUILD8.vdomFunctional && typeof nodeName === \"function\") {\n    return nodeName(vnodeData === null ? {} : vnodeData, vNodeChildren, vdomFnUtils);\n  }\n  const vnode = newVNode(nodeName, null);\n  vnode.$attrs$ = vnodeData;\n  if (vNodeChildren.length > 0) {\n    vnode.$children$ = vNodeChildren;\n  }\n  if (BUILD8.vdomKey) {\n    vnode.$key$ = key;\n  }\n  if (BUILD8.slotRelocation) {\n    vnode.$name$ = slotName;\n  }\n  return vnode;\n};\nvar newVNode = (tag, text) => {\n  const vnode = {\n    $flags$: 0,\n    $tag$: tag,\n    $text$: text,\n    $elm$: null,\n    $children$: null\n  };\n  if (BUILD8.vdomAttribute) {\n    vnode.$attrs$ = null;\n  }\n  if (BUILD8.vdomKey) {\n    vnode.$key$ = null;\n  }\n  if (BUILD8.slotRelocation) {\n    vnode.$name$ = null;\n  }\n  return vnode;\n};\nvar Host = {};\nvar isHost = node => node && node.$tag$ === Host;\nvar vdomFnUtils = {\n  forEach: (children, cb) => children.map(convertToPublic).forEach(cb),\n  map: (children, cb) => children.map(convertToPublic).map(cb).map(convertToPrivate)\n};\nvar convertToPublic = node => ({\n  vattrs: node.$attrs$,\n  vchildren: node.$children$,\n  vkey: node.$key$,\n  vname: node.$name$,\n  vtag: node.$tag$,\n  vtext: node.$text$\n});\nvar convertToPrivate = node => {\n  if (typeof node.vtag === \"function\") {\n    const vnodeData = {\n      ...node.vattrs\n    };\n    if (node.vkey) {\n      vnodeData.key = node.vkey;\n    }\n    if (node.vname) {\n      vnodeData.name = node.vname;\n    }\n    return h(node.vtag, vnodeData, ...(node.vchildren || []));\n  }\n  const vnode = newVNode(node.vtag, node.vtext);\n  vnode.$attrs$ = node.vattrs;\n  vnode.$children$ = node.vchildren;\n  vnode.$key$ = node.vkey;\n  vnode.$name$ = node.vname;\n  return vnode;\n};\nvar validateInputProperties = inputElm => {\n  const props = Object.keys(inputElm);\n  const value = props.indexOf(\"value\");\n  if (value === -1) {\n    return;\n  }\n  const typeIndex = props.indexOf(\"type\");\n  const minIndex = props.indexOf(\"min\");\n  const maxIndex = props.indexOf(\"max\");\n  const stepIndex = props.indexOf(\"step\");\n  if (value < typeIndex || value < minIndex || value < maxIndex || value < stepIndex) {\n    consoleDevWarn(`The \"value\" prop of <input> should be set after \"min\", \"max\", \"type\" and \"step\"`);\n  }\n};\n\n// src/runtime/client-hydrate.ts\nvar initializeClientHydrate = (hostElm, tagName, hostId, hostRef) => {\n  const endHydrate = createTime(\"hydrateClient\", tagName);\n  const shadowRoot = hostElm.shadowRoot;\n  const childRenderNodes = [];\n  const slotNodes = [];\n  const shadowRootNodes = BUILD9.shadowDom && shadowRoot ? [] : null;\n  const vnode = hostRef.$vnode$ = newVNode(tagName, null);\n  if (!plt.$orgLocNodes$) {\n    initializeDocumentHydrate(doc.body, plt.$orgLocNodes$ = /* @__PURE__ */new Map());\n  }\n  hostElm[HYDRATE_ID] = hostId;\n  hostElm.removeAttribute(HYDRATE_ID);\n  clientHydrate(vnode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, hostElm, hostId);\n  childRenderNodes.map(c => {\n    const orgLocationId = c.$hostId$ + \".\" + c.$nodeId$;\n    const orgLocationNode = plt.$orgLocNodes$.get(orgLocationId);\n    const node = c.$elm$;\n    if (orgLocationNode && supportsShadow && orgLocationNode[\"s-en\"] === \"\") {\n      orgLocationNode.parentNode.insertBefore(node, orgLocationNode.nextSibling);\n    }\n    if (!shadowRoot) {\n      node[\"s-hn\"] = tagName;\n      if (orgLocationNode) {\n        node[\"s-ol\"] = orgLocationNode;\n        node[\"s-ol\"][\"s-nr\"] = node;\n      }\n    }\n    plt.$orgLocNodes$.delete(orgLocationId);\n  });\n  if (BUILD9.shadowDom && shadowRoot) {\n    shadowRootNodes.map(shadowRootNode => {\n      if (shadowRootNode) {\n        shadowRoot.appendChild(shadowRootNode);\n      }\n    });\n  }\n  endHydrate();\n};\nvar clientHydrate = (parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node, hostId) => {\n  let childNodeType;\n  let childIdSplt;\n  let childVNode;\n  let i2;\n  if (node.nodeType === 1 /* ElementNode */) {\n    childNodeType = node.getAttribute(HYDRATE_CHILD_ID);\n    if (childNodeType) {\n      childIdSplt = childNodeType.split(\".\");\n      if (childIdSplt[0] === hostId || childIdSplt[0] === \"0\") {\n        childVNode = {\n          $flags$: 0,\n          $hostId$: childIdSplt[0],\n          $nodeId$: childIdSplt[1],\n          $depth$: childIdSplt[2],\n          $index$: childIdSplt[3],\n          $tag$: node.tagName.toLowerCase(),\n          $elm$: node,\n          $attrs$: null,\n          $children$: null,\n          $key$: null,\n          $name$: null,\n          $text$: null\n        };\n        childRenderNodes.push(childVNode);\n        node.removeAttribute(HYDRATE_CHILD_ID);\n        if (!parentVNode.$children$) {\n          parentVNode.$children$ = [];\n        }\n        parentVNode.$children$[childVNode.$index$] = childVNode;\n        parentVNode = childVNode;\n        if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n          shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n        }\n      }\n    }\n    if (node.shadowRoot) {\n      for (i2 = node.shadowRoot.childNodes.length - 1; i2 >= 0; i2--) {\n        clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node.shadowRoot.childNodes[i2], hostId);\n      }\n    }\n    for (i2 = node.childNodes.length - 1; i2 >= 0; i2--) {\n      clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node.childNodes[i2], hostId);\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[1] === hostId || childIdSplt[1] === \"0\") {\n      childNodeType = childIdSplt[0];\n      childVNode = {\n        $flags$: 0,\n        $hostId$: childIdSplt[1],\n        $nodeId$: childIdSplt[2],\n        $depth$: childIdSplt[3],\n        $index$: childIdSplt[4],\n        $elm$: node,\n        $attrs$: null,\n        $children$: null,\n        $key$: null,\n        $name$: null,\n        $tag$: null,\n        $text$: null\n      };\n      if (childNodeType === TEXT_NODE_ID) {\n        childVNode.$elm$ = node.nextSibling;\n        if (childVNode.$elm$ && childVNode.$elm$.nodeType === 3 /* TextNode */) {\n          childVNode.$text$ = childVNode.$elm$.textContent;\n          childRenderNodes.push(childVNode);\n          node.remove();\n          if (!parentVNode.$children$) {\n            parentVNode.$children$ = [];\n          }\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n          if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n            shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n          }\n        }\n      } else if (childVNode.$hostId$ === hostId) {\n        if (childNodeType === SLOT_NODE_ID) {\n          childVNode.$tag$ = \"slot\";\n          if (childIdSplt[5]) {\n            node[\"s-sn\"] = childVNode.$name$ = childIdSplt[5];\n          } else {\n            node[\"s-sn\"] = \"\";\n          }\n          node[\"s-sr\"] = true;\n          if (BUILD9.shadowDom && shadowRootNodes) {\n            childVNode.$elm$ = doc.createElement(childVNode.$tag$);\n            if (childVNode.$name$) {\n              childVNode.$elm$.setAttribute(\"name\", childVNode.$name$);\n            }\n            node.parentNode.insertBefore(childVNode.$elm$, node);\n            node.remove();\n            if (childVNode.$depth$ === \"0\") {\n              shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n            }\n          }\n          slotNodes.push(childVNode);\n          if (!parentVNode.$children$) {\n            parentVNode.$children$ = [];\n          }\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n        } else if (childNodeType === CONTENT_REF_ID) {\n          if (BUILD9.shadowDom && shadowRootNodes) {\n            node.remove();\n          } else if (BUILD9.slotRelocation) {\n            hostElm[\"s-cr\"] = node;\n            node[\"s-cn\"] = true;\n          }\n        }\n      }\n    }\n  } else if (parentVNode && parentVNode.$tag$ === \"style\") {\n    const vnode = newVNode(null, node.textContent);\n    vnode.$elm$ = node;\n    vnode.$index$ = \"0\";\n    parentVNode.$children$ = [vnode];\n  }\n};\nvar initializeDocumentHydrate = (node, orgLocNodes) => {\n  if (node.nodeType === 1 /* ElementNode */) {\n    let i2 = 0;\n    if (node.shadowRoot) {\n      for (; i2 < node.shadowRoot.childNodes.length; i2++) {\n        initializeDocumentHydrate(node.shadowRoot.childNodes[i2], orgLocNodes);\n      }\n    }\n    for (i2 = 0; i2 < node.childNodes.length; i2++) {\n      initializeDocumentHydrate(node.childNodes[i2], orgLocNodes);\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    const childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[0] === ORG_LOCATION_ID) {\n      orgLocNodes.set(childIdSplt[1] + \".\" + childIdSplt[2], node);\n      node.nodeValue = \"\";\n      node[\"s-en\"] = childIdSplt[3];\n    }\n  }\n};\n\n// src/runtime/initialize-component.ts\nimport { BUILD as BUILD20 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/mode.ts\nvar computeMode = elm => modeResolutionChain.map(h2 => h2(elm)).find(m => !!m);\nvar setMode = handler => modeResolutionChain.push(handler);\nvar getMode = ref => getHostRef(ref).$modeName$;\n\n// src/runtime/proxy-component.ts\nimport { BUILD as BUILD19 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/set-value.ts\nimport { BUILD as BUILD18 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/parse-property-value.ts\nimport { BUILD as BUILD10 } from \"@stencil/core/internal/app-data\";\nvar parsePropertyValue = (propValue, propType) => {\n  if (propValue != null && !isComplexType(propValue)) {\n    if (BUILD10.propBoolean && propType & 4 /* Boolean */) {\n      return propValue === \"false\" ? false : propValue === \"\" || !!propValue;\n    }\n    if (BUILD10.propNumber && propType & 2 /* Number */) {\n      return parseFloat(propValue);\n    }\n    if (BUILD10.propString && propType & 1 /* String */) {\n      return String(propValue);\n    }\n    return propValue;\n  }\n  return propValue;\n};\n\n// src/runtime/update-component.ts\nimport { BUILD as BUILD17, NAMESPACE } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/event-emitter.ts\nimport { BUILD as BUILD12 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/element.ts\nimport { BUILD as BUILD11 } from \"@stencil/core/internal/app-data\";\nvar getElement = ref => BUILD11.lazyLoad ? getHostRef(ref).$hostElement$ : ref;\n\n// src/runtime/event-emitter.ts\nvar createEvent = (ref, name, flags) => {\n  const elm = getElement(ref);\n  return {\n    emit: detail => {\n      if (BUILD12.isDev && !elm.isConnected) {\n        consoleDevWarn(`The \"${name}\" event was emitted, but the dispatcher node is no longer connected to the dom.`);\n      }\n      return emitEvent(elm, name, {\n        bubbles: !!(flags & 4 /* Bubbles */),\n        composed: !!(flags & 2 /* Composed */),\n        cancelable: !!(flags & 1 /* Cancellable */),\n        detail\n      });\n    }\n  };\n};\nvar emitEvent = (elm, name, opts) => {\n  const ev = plt.ce(name, opts);\n  elm.dispatchEvent(ev);\n  return ev;\n};\n\n// src/runtime/styles.ts\nimport { BUILD as BUILD13 } from \"@stencil/core/internal/app-data\";\nvar rootAppliedStyles = /* @__PURE__ */new WeakMap();\nvar registerStyle = (scopeId2, cssText, allowCS) => {\n  let style = styles.get(scopeId2);\n  if (supportsConstructableStylesheets && allowCS) {\n    style = style || new CSSStyleSheet();\n    if (typeof style === \"string\") {\n      style = cssText;\n    } else {\n      style.replaceSync(cssText);\n    }\n  } else {\n    style = cssText;\n  }\n  styles.set(scopeId2, style);\n};\nvar addStyle = (styleContainerNode, cmpMeta, mode) => {\n  var _a;\n  const scopeId2 = getScopeId(cmpMeta, mode);\n  const style = styles.get(scopeId2);\n  if (!BUILD13.attachStyles) {\n    return scopeId2;\n  }\n  styleContainerNode = styleContainerNode.nodeType === 11 /* DocumentFragment */ ? styleContainerNode : doc;\n  if (style) {\n    if (typeof style === \"string\") {\n      styleContainerNode = styleContainerNode.head || styleContainerNode;\n      let appliedStyles = rootAppliedStyles.get(styleContainerNode);\n      let styleElm;\n      if (!appliedStyles) {\n        rootAppliedStyles.set(styleContainerNode, appliedStyles = /* @__PURE__ */new Set());\n      }\n      if (!appliedStyles.has(scopeId2)) {\n        if (BUILD13.hydrateClientSide && styleContainerNode.host && (styleElm = styleContainerNode.querySelector(`[${HYDRATED_STYLE_ID}=\"${scopeId2}\"]`))) {\n          styleElm.innerHTML = style;\n        } else {\n          styleElm = doc.createElement(\"style\");\n          styleElm.innerHTML = style;\n          const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(doc);\n          if (nonce != null) {\n            styleElm.setAttribute(\"nonce\", nonce);\n          }\n          if ((BUILD13.hydrateServerSide || BUILD13.hotModuleReplacement) && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n            styleElm.setAttribute(HYDRATED_STYLE_ID, scopeId2);\n          }\n          const injectStyle =\n          /**\n           * we render a scoped component\n           */\n          !(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) ||\n          /**\n          * we are using shadow dom and render the style tag within the shadowRoot\n          */\n          cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */ && styleContainerNode.nodeName !== \"HEAD\";\n          if (injectStyle) {\n            styleContainerNode.insertBefore(styleElm, styleContainerNode.querySelector(\"link\"));\n          }\n        }\n        if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n          styleElm.innerHTML += SLOT_FB_CSS;\n        }\n        if (appliedStyles) {\n          appliedStyles.add(scopeId2);\n        }\n      }\n    } else if (BUILD13.constructableCSS && !styleContainerNode.adoptedStyleSheets.includes(style)) {\n      styleContainerNode.adoptedStyleSheets = [...styleContainerNode.adoptedStyleSheets, style];\n    }\n  }\n  return scopeId2;\n};\nvar attachStyles = hostRef => {\n  const cmpMeta = hostRef.$cmpMeta$;\n  const elm = hostRef.$hostElement$;\n  const flags = cmpMeta.$flags$;\n  const endAttachStyles = createTime(\"attachStyles\", cmpMeta.$tagName$);\n  const scopeId2 = addStyle(BUILD13.shadowDom && supportsShadow && elm.shadowRoot ? elm.shadowRoot : elm.getRootNode(), cmpMeta, hostRef.$modeName$);\n  if ((BUILD13.shadowDom || BUILD13.scoped) && BUILD13.cssAnnotations && flags & 10 /* needsScopedEncapsulation */ && flags & 2 /* scopedCssEncapsulation */) {\n    elm[\"s-sc\"] = scopeId2;\n    elm.classList.add(scopeId2 + \"-h\");\n    if (BUILD13.scoped && flags & 2 /* scopedCssEncapsulation */) {\n      elm.classList.add(scopeId2 + \"-s\");\n    }\n  }\n  endAttachStyles();\n};\nvar getScopeId = (cmp, mode) => \"sc-\" + (BUILD13.mode && mode && cmp.$flags$ & 32 /* hasMode */ ? cmp.$tagName$ + \"-\" + mode : cmp.$tagName$);\n\n// src/runtime/vdom/vdom-render.ts\nimport { BUILD as BUILD16 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/vdom/update-element.ts\nimport { BUILD as BUILD15 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/vdom/set-accessor.ts\nimport { BUILD as BUILD14 } from \"@stencil/core/internal/app-data\";\nvar setAccessor = (elm, memberName, oldValue, newValue, isSvg, flags) => {\n  if (oldValue !== newValue) {\n    let isProp = isMemberInElement(elm, memberName);\n    let ln = memberName.toLowerCase();\n    if (BUILD14.vdomClass && memberName === \"class\") {\n      const classList = elm.classList;\n      const oldClasses = parseClassList(oldValue);\n      const newClasses = parseClassList(newValue);\n      classList.remove(...oldClasses.filter(c => c && !newClasses.includes(c)));\n      classList.add(...newClasses.filter(c => c && !oldClasses.includes(c)));\n    } else if (BUILD14.vdomStyle && memberName === \"style\") {\n      if (BUILD14.updatable) {\n        for (const prop in oldValue) {\n          if (!newValue || newValue[prop] == null) {\n            if (!BUILD14.hydrateServerSide && prop.includes(\"-\")) {\n              elm.style.removeProperty(prop);\n            } else {\n              elm.style[prop] = \"\";\n            }\n          }\n        }\n      }\n      for (const prop in newValue) {\n        if (!oldValue || newValue[prop] !== oldValue[prop]) {\n          if (!BUILD14.hydrateServerSide && prop.includes(\"-\")) {\n            elm.style.setProperty(prop, newValue[prop]);\n          } else {\n            elm.style[prop] = newValue[prop];\n          }\n        }\n      }\n    } else if (BUILD14.vdomKey && memberName === \"key\") {} else if (BUILD14.vdomRef && memberName === \"ref\") {\n      if (newValue) {\n        newValue(elm);\n      }\n    } else if (BUILD14.vdomListener && (BUILD14.lazyLoad ? !isProp : !elm.__lookupSetter__(memberName)) && memberName[0] === \"o\" && memberName[1] === \"n\") {\n      if (memberName[2] === \"-\") {\n        memberName = memberName.slice(3);\n      } else if (isMemberInElement(win, ln)) {\n        memberName = ln.slice(2);\n      } else {\n        memberName = ln[2] + memberName.slice(3);\n      }\n      if (oldValue || newValue) {\n        const capture = memberName.endsWith(CAPTURE_EVENT_SUFFIX);\n        memberName = memberName.replace(CAPTURE_EVENT_REGEX, \"\");\n        if (oldValue) {\n          plt.rel(elm, memberName, oldValue, capture);\n        }\n        if (newValue) {\n          plt.ael(elm, memberName, newValue, capture);\n        }\n      }\n    } else if (BUILD14.vdomPropOrAttr) {\n      const isComplex = isComplexType(newValue);\n      if ((isProp || isComplex && newValue !== null) && !isSvg) {\n        try {\n          if (!elm.tagName.includes(\"-\")) {\n            const n = newValue == null ? \"\" : newValue;\n            if (memberName === \"list\") {\n              isProp = false;\n            } else if (oldValue == null || elm[memberName] != n) {\n              elm[memberName] = n;\n            }\n          } else {\n            elm[memberName] = newValue;\n          }\n        } catch (e) {}\n      }\n      let xlink = false;\n      if (BUILD14.vdomXlink) {\n        if (ln !== (ln = ln.replace(/^xlink\\:?/, \"\"))) {\n          memberName = ln;\n          xlink = true;\n        }\n      }\n      if (newValue == null || newValue === false) {\n        if (newValue !== false || elm.getAttribute(memberName) === \"\") {\n          if (BUILD14.vdomXlink && xlink) {\n            elm.removeAttributeNS(XLINK_NS, memberName);\n          } else {\n            elm.removeAttribute(memberName);\n          }\n        }\n      } else if ((!isProp || flags & 4 /* isHost */ || isSvg) && !isComplex) {\n        newValue = newValue === true ? \"\" : newValue;\n        if (BUILD14.vdomXlink && xlink) {\n          elm.setAttributeNS(XLINK_NS, memberName, newValue);\n        } else {\n          elm.setAttribute(memberName, newValue);\n        }\n      }\n    }\n  }\n};\nvar parseClassListRegex = /\\s/;\nvar parseClassList = value => !value ? [] : value.split(parseClassListRegex);\nvar CAPTURE_EVENT_SUFFIX = \"Capture\";\nvar CAPTURE_EVENT_REGEX = new RegExp(CAPTURE_EVENT_SUFFIX + \"$\");\n\n// src/runtime/vdom/update-element.ts\nvar updateElement = (oldVnode, newVnode, isSvgMode2) => {\n  const elm = newVnode.$elm$.nodeType === 11 /* DocumentFragment */ && newVnode.$elm$.host ? newVnode.$elm$.host : newVnode.$elm$;\n  const oldVnodeAttrs = oldVnode && oldVnode.$attrs$ || EMPTY_OBJ;\n  const newVnodeAttrs = newVnode.$attrs$ || EMPTY_OBJ;\n  if (BUILD15.updatable) {\n    for (const memberName of sortedAttrNames(Object.keys(oldVnodeAttrs))) {\n      if (!(memberName in newVnodeAttrs)) {\n        setAccessor(elm, memberName, oldVnodeAttrs[memberName], void 0, isSvgMode2, newVnode.$flags$);\n      }\n    }\n  }\n  for (const memberName of sortedAttrNames(Object.keys(newVnodeAttrs))) {\n    setAccessor(elm, memberName, oldVnodeAttrs[memberName], newVnodeAttrs[memberName], isSvgMode2, newVnode.$flags$);\n  }\n};\nfunction sortedAttrNames(attrNames) {\n  return attrNames.includes(\"ref\") ?\n  // we need to sort these to ensure that `'ref'` is the last attr\n  [...attrNames.filter(attr => attr !== \"ref\"), \"ref\"] :\n  // no need to sort, return the original array\n  attrNames;\n}\n\n// src/runtime/vdom/vdom-render.ts\nvar scopeId;\nvar contentRef;\nvar hostTagName;\nvar useNativeShadowDom = false;\nvar checkSlotFallbackVisibility = false;\nvar checkSlotRelocate = false;\nvar isSvgMode = false;\nvar createElm = (oldParentVNode, newParentVNode, childIndex, parentElm) => {\n  var _a;\n  const newVNode2 = newParentVNode.$children$[childIndex];\n  let i2 = 0;\n  let elm;\n  let childNode;\n  let oldVNode;\n  if (BUILD16.slotRelocation && !useNativeShadowDom) {\n    checkSlotRelocate = true;\n    if (newVNode2.$tag$ === \"slot\") {\n      if (scopeId) {\n        parentElm.classList.add(scopeId + \"-s\");\n      }\n      newVNode2.$flags$ |= newVNode2.$children$ ?\n      // slot element has fallback content\n      // still create an element that \"mocks\" the slot element\n      2 /* isSlotFallback */ :\n      // slot element does not have fallback content\n      // create an html comment we'll use to always reference\n      // where actual slot content should sit next to\n      1 /* isSlotReference */;\n    }\n  }\n  if (BUILD16.isDev && newVNode2.$elm$) {\n    consoleDevError(`The JSX ${newVNode2.$text$ !== null ? `\"${newVNode2.$text$}\" text` : `\"${newVNode2.$tag$}\" element`} node should not be shared within the same renderer. The renderer caches element lookups in order to improve performance. However, a side effect from this is that the exact same JSX node should not be reused. For more information please see https://stenciljs.com/docs/templating-jsx#avoid-shared-jsx-nodes`);\n  }\n  if (BUILD16.vdomText && newVNode2.$text$ !== null) {\n    elm = newVNode2.$elm$ = doc.createTextNode(newVNode2.$text$);\n  } else if (BUILD16.slotRelocation && newVNode2.$flags$ & 1 /* isSlotReference */) {\n    elm = newVNode2.$elm$ = BUILD16.isDebug || BUILD16.hydrateServerSide ? slotReferenceDebugNode(newVNode2) : doc.createTextNode(\"\");\n  } else {\n    if (BUILD16.svg && !isSvgMode) {\n      isSvgMode = newVNode2.$tag$ === \"svg\";\n    }\n    elm = newVNode2.$elm$ = BUILD16.svg ? doc.createElementNS(isSvgMode ? SVG_NS : HTML_NS, !useNativeShadowDom && BUILD16.slotRelocation && newVNode2.$flags$ & 2 /* isSlotFallback */ ? \"slot-fb\" : newVNode2.$tag$) : doc.createElement(!useNativeShadowDom && BUILD16.slotRelocation && newVNode2.$flags$ & 2 /* isSlotFallback */ ? \"slot-fb\" : newVNode2.$tag$);\n    if (BUILD16.svg && isSvgMode && newVNode2.$tag$ === \"foreignObject\") {\n      isSvgMode = false;\n    }\n    if (BUILD16.vdomAttribute) {\n      updateElement(null, newVNode2, isSvgMode);\n    }\n    const rootNode = elm.getRootNode();\n    const isElementWithinShadowRoot = !rootNode.querySelector(\"body\");\n    if (!isElementWithinShadowRoot && BUILD16.scoped && isDef(scopeId) && elm[\"s-si\"] !== scopeId) {\n      elm.classList.add(elm[\"s-si\"] = scopeId);\n    }\n    if (BUILD16.scoped) {\n      updateElementScopeIds(elm, parentElm);\n    }\n    if (newVNode2.$children$) {\n      for (i2 = 0; i2 < newVNode2.$children$.length; ++i2) {\n        childNode = createElm(oldParentVNode, newVNode2, i2, elm);\n        if (childNode) {\n          elm.appendChild(childNode);\n        }\n      }\n    }\n    if (BUILD16.svg) {\n      if (newVNode2.$tag$ === \"svg\") {\n        isSvgMode = false;\n      } else if (elm.tagName === \"foreignObject\") {\n        isSvgMode = true;\n      }\n    }\n  }\n  elm[\"s-hn\"] = hostTagName;\n  if (BUILD16.slotRelocation) {\n    if (newVNode2.$flags$ & (2 /* isSlotFallback */ | 1 /* isSlotReference */)) {\n      elm[\"s-sr\"] = true;\n      elm[\"s-cr\"] = contentRef;\n      elm[\"s-sn\"] = newVNode2.$name$ || \"\";\n      elm[\"s-rf\"] = (_a = newVNode2.$attrs$) == null ? void 0 : _a.ref;\n      oldVNode = oldParentVNode && oldParentVNode.$children$ && oldParentVNode.$children$[childIndex];\n      if (oldVNode && oldVNode.$tag$ === newVNode2.$tag$ && oldParentVNode.$elm$) {\n        if (BUILD16.experimentalSlotFixes) {\n          relocateToHostRoot(oldParentVNode.$elm$);\n        } else {\n          putBackInOriginalLocation(oldParentVNode.$elm$, false);\n        }\n      }\n    }\n  }\n  return elm;\n};\nvar relocateToHostRoot = parentElm => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const host = parentElm.closest(hostTagName.toLowerCase());\n  if (host != null) {\n    const contentRefNode = Array.from(host.childNodes).find(ref => ref[\"s-cr\"]);\n    const childNodeArray = Array.from(parentElm.childNodes);\n    for (const childNode of contentRefNode ? childNodeArray.reverse() : childNodeArray) {\n      if (childNode[\"s-sh\"] != null) {\n        insertBefore(host, childNode, contentRefNode != null ? contentRefNode : null);\n        childNode[\"s-sh\"] = void 0;\n        checkSlotRelocate = true;\n      }\n    }\n  }\n  plt.$flags$ &= ~1 /* isTmpDisconnected */;\n};\nvar putBackInOriginalLocation = (parentElm, recursive) => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const oldSlotChildNodes = Array.from(parentElm.childNodes);\n  if (parentElm[\"s-sr\"] && BUILD16.experimentalSlotFixes) {\n    let node = parentElm;\n    while (node = node.nextSibling) {\n      if (node && node[\"s-sn\"] === parentElm[\"s-sn\"] && node[\"s-sh\"] === hostTagName) {\n        oldSlotChildNodes.push(node);\n      }\n    }\n  }\n  for (let i2 = oldSlotChildNodes.length - 1; i2 >= 0; i2--) {\n    const childNode = oldSlotChildNodes[i2];\n    if (childNode[\"s-hn\"] !== hostTagName && childNode[\"s-ol\"]) {\n      insertBefore(parentReferenceNode(childNode), childNode, referenceNode(childNode));\n      childNode[\"s-ol\"].remove();\n      childNode[\"s-ol\"] = void 0;\n      childNode[\"s-sh\"] = void 0;\n      checkSlotRelocate = true;\n    }\n    if (recursive) {\n      putBackInOriginalLocation(childNode, recursive);\n    }\n  }\n  plt.$flags$ &= ~1 /* isTmpDisconnected */;\n};\nvar addVnodes = (parentElm, before, parentVNode, vnodes, startIdx, endIdx) => {\n  let containerElm = BUILD16.slotRelocation && parentElm[\"s-cr\"] && parentElm[\"s-cr\"].parentNode || parentElm;\n  let childNode;\n  if (BUILD16.shadowDom && containerElm.shadowRoot && containerElm.tagName === hostTagName) {\n    containerElm = containerElm.shadowRoot;\n  }\n  for (; startIdx <= endIdx; ++startIdx) {\n    if (vnodes[startIdx]) {\n      childNode = createElm(null, parentVNode, startIdx, parentElm);\n      if (childNode) {\n        vnodes[startIdx].$elm$ = childNode;\n        insertBefore(containerElm, childNode, BUILD16.slotRelocation ? referenceNode(before) : before);\n      }\n    }\n  }\n};\nvar removeVnodes = (vnodes, startIdx, endIdx) => {\n  for (let index = startIdx; index <= endIdx; ++index) {\n    const vnode = vnodes[index];\n    if (vnode) {\n      const elm = vnode.$elm$;\n      nullifyVNodeRefs(vnode);\n      if (elm) {\n        if (BUILD16.slotRelocation) {\n          checkSlotFallbackVisibility = true;\n          if (elm[\"s-ol\"]) {\n            elm[\"s-ol\"].remove();\n          } else {\n            putBackInOriginalLocation(elm, true);\n          }\n        }\n        elm.remove();\n      }\n    }\n  }\n};\nvar updateChildren = (parentElm, oldCh, newVNode2, newCh, isInitialRender = false) => {\n  let oldStartIdx = 0;\n  let newStartIdx = 0;\n  let idxInOld = 0;\n  let i2 = 0;\n  let oldEndIdx = oldCh.length - 1;\n  let oldStartVnode = oldCh[0];\n  let oldEndVnode = oldCh[oldEndIdx];\n  let newEndIdx = newCh.length - 1;\n  let newStartVnode = newCh[0];\n  let newEndVnode = newCh[newEndIdx];\n  let node;\n  let elmToMove;\n  while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n    if (oldStartVnode == null) {\n      oldStartVnode = oldCh[++oldStartIdx];\n    } else if (oldEndVnode == null) {\n      oldEndVnode = oldCh[--oldEndIdx];\n    } else if (newStartVnode == null) {\n      newStartVnode = newCh[++newStartIdx];\n    } else if (newEndVnode == null) {\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newStartVnode, isInitialRender)) {\n      patch(oldStartVnode, newStartVnode, isInitialRender);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else if (isSameVnode(oldEndVnode, newEndVnode, isInitialRender)) {\n      patch(oldEndVnode, newEndVnode, isInitialRender);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newEndVnode, isInitialRender)) {\n      if (BUILD16.slotRelocation && (oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\")) {\n        putBackInOriginalLocation(oldStartVnode.$elm$.parentNode, false);\n      }\n      patch(oldStartVnode, newEndVnode, isInitialRender);\n      insertBefore(parentElm, oldStartVnode.$elm$, oldEndVnode.$elm$.nextSibling);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldEndVnode, newStartVnode, isInitialRender)) {\n      if (BUILD16.slotRelocation && (oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\")) {\n        putBackInOriginalLocation(oldEndVnode.$elm$.parentNode, false);\n      }\n      patch(oldEndVnode, newStartVnode, isInitialRender);\n      insertBefore(parentElm, oldEndVnode.$elm$, oldStartVnode.$elm$);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else {\n      idxInOld = -1;\n      if (BUILD16.vdomKey) {\n        for (i2 = oldStartIdx; i2 <= oldEndIdx; ++i2) {\n          if (oldCh[i2] && oldCh[i2].$key$ !== null && oldCh[i2].$key$ === newStartVnode.$key$) {\n            idxInOld = i2;\n            break;\n          }\n        }\n      }\n      if (BUILD16.vdomKey && idxInOld >= 0) {\n        elmToMove = oldCh[idxInOld];\n        if (elmToMove.$tag$ !== newStartVnode.$tag$) {\n          node = createElm(oldCh && oldCh[newStartIdx], newVNode2, idxInOld, parentElm);\n        } else {\n          patch(elmToMove, newStartVnode, isInitialRender);\n          oldCh[idxInOld] = void 0;\n          node = elmToMove.$elm$;\n        }\n        newStartVnode = newCh[++newStartIdx];\n      } else {\n        node = createElm(oldCh && oldCh[newStartIdx], newVNode2, newStartIdx, parentElm);\n        newStartVnode = newCh[++newStartIdx];\n      }\n      if (node) {\n        if (BUILD16.slotRelocation) {\n          insertBefore(parentReferenceNode(oldStartVnode.$elm$), node, referenceNode(oldStartVnode.$elm$));\n        } else {\n          insertBefore(oldStartVnode.$elm$.parentNode, node, oldStartVnode.$elm$);\n        }\n      }\n    }\n  }\n  if (oldStartIdx > oldEndIdx) {\n    addVnodes(parentElm, newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].$elm$, newVNode2, newCh, newStartIdx, newEndIdx);\n  } else if (BUILD16.updatable && newStartIdx > newEndIdx) {\n    removeVnodes(oldCh, oldStartIdx, oldEndIdx);\n  }\n};\nvar isSameVnode = (leftVNode, rightVNode, isInitialRender = false) => {\n  if (leftVNode.$tag$ === rightVNode.$tag$) {\n    if (BUILD16.slotRelocation && leftVNode.$tag$ === \"slot\") {\n      if (\n      // The component gets hydrated and no VDOM has been initialized.\n      // Here the comparison can't happen as $name$ property is not set for `leftNode`.\n      \"$nodeId$\" in leftVNode && isInitialRender &&\n      // `leftNode` is not from type HTMLComment which would cause many\n      // hydration comments to be removed\n      leftVNode.$elm$.nodeType !== 8) {\n        return false;\n      }\n      return leftVNode.$name$ === rightVNode.$name$;\n    }\n    if (BUILD16.vdomKey && !isInitialRender) {\n      return leftVNode.$key$ === rightVNode.$key$;\n    }\n    return true;\n  }\n  return false;\n};\nvar referenceNode = node => {\n  return node && node[\"s-ol\"] || node;\n};\nvar parentReferenceNode = node => (node[\"s-ol\"] ? node[\"s-ol\"] : node).parentNode;\nvar patch = (oldVNode, newVNode2, isInitialRender = false) => {\n  const elm = newVNode2.$elm$ = oldVNode.$elm$;\n  const oldChildren = oldVNode.$children$;\n  const newChildren = newVNode2.$children$;\n  const tag = newVNode2.$tag$;\n  const text = newVNode2.$text$;\n  let defaultHolder;\n  if (!BUILD16.vdomText || text === null) {\n    if (BUILD16.svg) {\n      isSvgMode = tag === \"svg\" ? true : tag === \"foreignObject\" ? false : isSvgMode;\n    }\n    if (BUILD16.vdomAttribute || BUILD16.reflect) {\n      if (BUILD16.slot && tag === \"slot\" && !useNativeShadowDom) {\n        if (BUILD16.experimentalSlotFixes && oldVNode.$name$ !== newVNode2.$name$) {\n          newVNode2.$elm$[\"s-sn\"] = newVNode2.$name$ || \"\";\n          relocateToHostRoot(newVNode2.$elm$.parentElement);\n        }\n      } else {\n        updateElement(oldVNode, newVNode2, isSvgMode);\n      }\n    }\n    if (BUILD16.updatable && oldChildren !== null && newChildren !== null) {\n      updateChildren(elm, oldChildren, newVNode2, newChildren, isInitialRender);\n    } else if (newChildren !== null) {\n      if (BUILD16.updatable && BUILD16.vdomText && oldVNode.$text$ !== null) {\n        elm.textContent = \"\";\n      }\n      addVnodes(elm, null, newVNode2, newChildren, 0, newChildren.length - 1);\n    } else if (\n    // don't do this on initial render as it can cause non-hydrated content to be removed\n    !isInitialRender && BUILD16.updatable && oldChildren !== null) {\n      removeVnodes(oldChildren, 0, oldChildren.length - 1);\n    }\n    if (BUILD16.svg && isSvgMode && tag === \"svg\") {\n      isSvgMode = false;\n    }\n  } else if (BUILD16.vdomText && BUILD16.slotRelocation && (defaultHolder = elm[\"s-cr\"])) {\n    defaultHolder.parentNode.textContent = text;\n  } else if (BUILD16.vdomText && oldVNode.$text$ !== text) {\n    elm.data = text;\n  }\n};\nvar updateFallbackSlotVisibility = elm => {\n  const childNodes = elm.childNodes;\n  for (const childNode of childNodes) {\n    if (childNode.nodeType === 1 /* ElementNode */) {\n      if (childNode[\"s-sr\"]) {\n        const slotName = childNode[\"s-sn\"];\n        childNode.hidden = false;\n        for (const siblingNode of childNodes) {\n          if (siblingNode !== childNode) {\n            if (siblingNode[\"s-hn\"] !== childNode[\"s-hn\"] || slotName !== \"\") {\n              if (siblingNode.nodeType === 1 /* ElementNode */ && (slotName === siblingNode.getAttribute(\"slot\") || slotName === siblingNode[\"s-sn\"]) || siblingNode.nodeType === 3 /* TextNode */ && slotName === siblingNode[\"s-sn\"]) {\n                childNode.hidden = true;\n                break;\n              }\n            } else {\n              if (siblingNode.nodeType === 1 /* ElementNode */ || siblingNode.nodeType === 3 /* TextNode */ && siblingNode.textContent.trim() !== \"\") {\n                childNode.hidden = true;\n                break;\n              }\n            }\n          }\n        }\n      }\n      updateFallbackSlotVisibility(childNode);\n    }\n  }\n};\nvar relocateNodes = [];\nvar markSlotContentForRelocation = elm => {\n  let node;\n  let hostContentNodes;\n  let j;\n  for (const childNode of elm.childNodes) {\n    if (childNode[\"s-sr\"] && (node = childNode[\"s-cr\"]) && node.parentNode) {\n      hostContentNodes = node.parentNode.childNodes;\n      const slotName = childNode[\"s-sn\"];\n      for (j = hostContentNodes.length - 1; j >= 0; j--) {\n        node = hostContentNodes[j];\n        if (!node[\"s-cn\"] && !node[\"s-nr\"] && node[\"s-hn\"] !== childNode[\"s-hn\"] && (!BUILD16.experimentalSlotFixes || !node[\"s-sh\"] || node[\"s-sh\"] !== childNode[\"s-hn\"])) {\n          if (isNodeLocatedInSlot(node, slotName)) {\n            let relocateNodeData = relocateNodes.find(r => r.$nodeToRelocate$ === node);\n            checkSlotFallbackVisibility = true;\n            node[\"s-sn\"] = node[\"s-sn\"] || slotName;\n            if (relocateNodeData) {\n              relocateNodeData.$nodeToRelocate$[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodeData.$slotRefNode$ = childNode;\n            } else {\n              node[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodes.push({\n                $slotRefNode$: childNode,\n                $nodeToRelocate$: node\n              });\n            }\n            if (node[\"s-sr\"]) {\n              relocateNodes.map(relocateNode => {\n                if (isNodeLocatedInSlot(relocateNode.$nodeToRelocate$, node[\"s-sn\"])) {\n                  relocateNodeData = relocateNodes.find(r => r.$nodeToRelocate$ === node);\n                  if (relocateNodeData && !relocateNode.$slotRefNode$) {\n                    relocateNode.$slotRefNode$ = relocateNodeData.$slotRefNode$;\n                  }\n                }\n              });\n            }\n          } else if (!relocateNodes.some(r => r.$nodeToRelocate$ === node)) {\n            relocateNodes.push({\n              $nodeToRelocate$: node\n            });\n          }\n        }\n      }\n    }\n    if (childNode.nodeType === 1 /* ElementNode */) {\n      markSlotContentForRelocation(childNode);\n    }\n  }\n};\nvar isNodeLocatedInSlot = (nodeToRelocate, slotName) => {\n  if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n    if (nodeToRelocate.getAttribute(\"slot\") === null && slotName === \"\") {\n      return true;\n    }\n    if (nodeToRelocate.getAttribute(\"slot\") === slotName) {\n      return true;\n    }\n    return false;\n  }\n  if (nodeToRelocate[\"s-sn\"] === slotName) {\n    return true;\n  }\n  return slotName === \"\";\n};\nvar nullifyVNodeRefs = vNode => {\n  if (BUILD16.vdomRef) {\n    vNode.$attrs$ && vNode.$attrs$.ref && vNode.$attrs$.ref(null);\n    vNode.$children$ && vNode.$children$.map(nullifyVNodeRefs);\n  }\n};\nvar insertBefore = (parent, newNode, reference) => {\n  const inserted = parent == null ? void 0 : parent.insertBefore(newNode, reference);\n  if (BUILD16.scoped) {\n    updateElementScopeIds(newNode, parent);\n  }\n  return inserted;\n};\nvar findScopeIds = element => {\n  const scopeIds = [];\n  if (element) {\n    scopeIds.push(...(element[\"s-scs\"] || []), element[\"s-si\"], element[\"s-sc\"], ...findScopeIds(element.parentElement));\n  }\n  return scopeIds;\n};\nvar updateElementScopeIds = (element, parent, iterateChildNodes = false) => {\n  var _a;\n  if (element && parent && element.nodeType === 1 /* ElementNode */) {\n    const scopeIds = new Set(findScopeIds(parent).filter(Boolean));\n    if (scopeIds.size) {\n      (_a = element.classList) == null ? void 0 : _a.add(...(element[\"s-scs\"] = [...scopeIds]));\n      if (element[\"s-ol\"] || iterateChildNodes) {\n        for (const childNode of Array.from(element.childNodes)) {\n          updateElementScopeIds(childNode, element, true);\n        }\n      }\n    }\n  }\n};\nvar renderVdom = (hostRef, renderFnResults, isInitialLoad = false) => {\n  var _a, _b, _c, _d, _e;\n  const hostElm = hostRef.$hostElement$;\n  const cmpMeta = hostRef.$cmpMeta$;\n  const oldVNode = hostRef.$vnode$ || newVNode(null, null);\n  const rootVnode = isHost(renderFnResults) ? renderFnResults : h(null, null, renderFnResults);\n  hostTagName = hostElm.tagName;\n  if (BUILD16.isDev && Array.isArray(renderFnResults) && renderFnResults.some(isHost)) {\n    throw new Error(`The <Host> must be the single root component.\nLooks like the render() function of \"${hostTagName.toLowerCase()}\" is returning an array that contains the <Host>.\n\nThe render() function should look like this instead:\n\nrender() {\n  // Do not return an array\n  return (\n    <Host>{content}</Host>\n  );\n}\n  `);\n  }\n  if (BUILD16.reflect && cmpMeta.$attrsToReflect$) {\n    rootVnode.$attrs$ = rootVnode.$attrs$ || {};\n    cmpMeta.$attrsToReflect$.map(([propName, attribute]) => rootVnode.$attrs$[attribute] = hostElm[propName]);\n  }\n  if (isInitialLoad && rootVnode.$attrs$) {\n    for (const key of Object.keys(rootVnode.$attrs$)) {\n      if (hostElm.hasAttribute(key) && ![\"key\", \"ref\", \"style\", \"class\"].includes(key)) {\n        rootVnode.$attrs$[key] = hostElm[key];\n      }\n    }\n  }\n  rootVnode.$tag$ = null;\n  rootVnode.$flags$ |= 4 /* isHost */;\n  hostRef.$vnode$ = rootVnode;\n  rootVnode.$elm$ = oldVNode.$elm$ = BUILD16.shadowDom ? hostElm.shadowRoot || hostElm : hostElm;\n  if (BUILD16.scoped || BUILD16.shadowDom) {\n    scopeId = hostElm[\"s-sc\"];\n  }\n  useNativeShadowDom = supportsShadow && (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) !== 0;\n  if (BUILD16.slotRelocation) {\n    contentRef = hostElm[\"s-cr\"];\n    checkSlotFallbackVisibility = false;\n  }\n  patch(oldVNode, rootVnode, isInitialLoad);\n  if (BUILD16.slotRelocation) {\n    plt.$flags$ |= 1 /* isTmpDisconnected */;\n    if (checkSlotRelocate) {\n      markSlotContentForRelocation(rootVnode.$elm$);\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        if (!nodeToRelocate[\"s-ol\"]) {\n          const orgLocationNode = BUILD16.isDebug || BUILD16.hydrateServerSide ? originalLocationDebugNode(nodeToRelocate) : doc.createTextNode(\"\");\n          orgLocationNode[\"s-nr\"] = nodeToRelocate;\n          insertBefore(nodeToRelocate.parentNode, nodeToRelocate[\"s-ol\"] = orgLocationNode, nodeToRelocate);\n        }\n      }\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        const slotRefNode = relocateData.$slotRefNode$;\n        if (slotRefNode) {\n          const parentNodeRef = slotRefNode.parentNode;\n          let insertBeforeNode = slotRefNode.nextSibling;\n          if (!BUILD16.experimentalSlotFixes || insertBeforeNode && insertBeforeNode.nodeType === 1 /* ElementNode */) {\n            let orgLocationNode = (_a = nodeToRelocate[\"s-ol\"]) == null ? void 0 : _a.previousSibling;\n            while (orgLocationNode) {\n              let refNode = (_b = orgLocationNode[\"s-nr\"]) != null ? _b : null;\n              if (refNode && refNode[\"s-sn\"] === nodeToRelocate[\"s-sn\"] && parentNodeRef === refNode.parentNode) {\n                refNode = refNode.nextSibling;\n                while (refNode === nodeToRelocate || (refNode == null ? void 0 : refNode[\"s-sr\"])) {\n                  refNode = refNode == null ? void 0 : refNode.nextSibling;\n                }\n                if (!refNode || !refNode[\"s-nr\"]) {\n                  insertBeforeNode = refNode;\n                  break;\n                }\n              }\n              orgLocationNode = orgLocationNode.previousSibling;\n            }\n          }\n          if (!insertBeforeNode && parentNodeRef !== nodeToRelocate.parentNode || nodeToRelocate.nextSibling !== insertBeforeNode) {\n            if (nodeToRelocate !== insertBeforeNode) {\n              if (!BUILD16.experimentalSlotFixes && !nodeToRelocate[\"s-hn\"] && nodeToRelocate[\"s-ol\"]) {\n                nodeToRelocate[\"s-hn\"] = nodeToRelocate[\"s-ol\"].parentNode.nodeName;\n              }\n              insertBefore(parentNodeRef, nodeToRelocate, insertBeforeNode);\n              if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n                nodeToRelocate.hidden = (_c = nodeToRelocate[\"s-ih\"]) != null ? _c : false;\n              }\n            }\n          }\n          nodeToRelocate && typeof slotRefNode[\"s-rf\"] === \"function\" && slotRefNode[\"s-rf\"](nodeToRelocate);\n        } else {\n          if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n            if (isInitialLoad) {\n              nodeToRelocate[\"s-ih\"] = (_d = nodeToRelocate.hidden) != null ? _d : false;\n            }\n            nodeToRelocate.hidden = true;\n          }\n        }\n      }\n    }\n    if (checkSlotFallbackVisibility) {\n      updateFallbackSlotVisibility(rootVnode.$elm$);\n    }\n    plt.$flags$ &= ~1 /* isTmpDisconnected */;\n    relocateNodes.length = 0;\n  }\n  if (BUILD16.experimentalScopedSlotChanges && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n    for (const childNode of rootVnode.$elm$.childNodes) {\n      if (childNode[\"s-hn\"] !== hostTagName && !childNode[\"s-sh\"]) {\n        if (isInitialLoad && childNode[\"s-ih\"] == null) {\n          childNode[\"s-ih\"] = (_e = childNode.hidden) != null ? _e : false;\n        }\n        childNode.hidden = true;\n      }\n    }\n  }\n  contentRef = void 0;\n};\nvar slotReferenceDebugNode = slotVNode => doc.createComment(`<slot${slotVNode.$name$ ? ' name=\"' + slotVNode.$name$ + '\"' : \"\"}> (host=${hostTagName.toLowerCase()})`);\nvar originalLocationDebugNode = nodeToRelocate => doc.createComment(`org-location for ` + (nodeToRelocate.localName ? `<${nodeToRelocate.localName}> (host=${nodeToRelocate[\"s-hn\"]})` : `[${nodeToRelocate.textContent}]`));\n\n// src/runtime/update-component.ts\nvar attachToAncestor = (hostRef, ancestorComponent) => {\n  if (BUILD17.asyncLoading && ancestorComponent && !hostRef.$onRenderResolve$ && ancestorComponent[\"s-p\"]) {\n    ancestorComponent[\"s-p\"].push(new Promise(r => hostRef.$onRenderResolve$ = r));\n  }\n};\nvar scheduleUpdate = (hostRef, isInitialLoad) => {\n  if (BUILD17.taskQueue && BUILD17.updatable) {\n    hostRef.$flags$ |= 16 /* isQueuedForUpdate */;\n  }\n  if (BUILD17.asyncLoading && hostRef.$flags$ & 4 /* isWaitingForChildren */) {\n    hostRef.$flags$ |= 512 /* needsRerender */;\n    return;\n  }\n  attachToAncestor(hostRef, hostRef.$ancestorComponent$);\n  const dispatch = () => dispatchHooks(hostRef, isInitialLoad);\n  return BUILD17.taskQueue ? writeTask(dispatch) : dispatch();\n};\nvar dispatchHooks = (hostRef, isInitialLoad) => {\n  const elm = hostRef.$hostElement$;\n  const endSchedule = createTime(\"scheduleUpdate\", hostRef.$cmpMeta$.$tagName$);\n  const instance = BUILD17.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  if (!instance) {\n    throw new Error(`Can't render component <${elm.tagName.toLowerCase()} /> with invalid Stencil runtime! Make sure this imported component is compiled with a \\`externalRuntime: true\\` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime`);\n  }\n  let maybePromise;\n  if (isInitialLoad) {\n    if (BUILD17.lazyLoad && BUILD17.hostListener) {\n      hostRef.$flags$ |= 256 /* isListenReady */;\n      if (hostRef.$queuedListeners$) {\n        hostRef.$queuedListeners$.map(([methodName, event]) => safeCall(instance, methodName, event));\n        hostRef.$queuedListeners$ = void 0;\n      }\n    }\n    emitLifecycleEvent(elm, \"componentWillLoad\");\n    if (BUILD17.cmpWillLoad) {\n      maybePromise = safeCall(instance, \"componentWillLoad\");\n    }\n  } else {\n    emitLifecycleEvent(elm, \"componentWillUpdate\");\n    if (BUILD17.cmpWillUpdate) {\n      maybePromise = safeCall(instance, \"componentWillUpdate\");\n    }\n  }\n  emitLifecycleEvent(elm, \"componentWillRender\");\n  if (BUILD17.cmpWillRender) {\n    maybePromise = enqueue(maybePromise, () => safeCall(instance, \"componentWillRender\"));\n  }\n  endSchedule();\n  return enqueue(maybePromise, () => updateComponent(hostRef, instance, isInitialLoad));\n};\nvar enqueue = (maybePromise, fn) => isPromisey(maybePromise) ? maybePromise.then(fn).catch(err2 => {\n  console.error(err2);\n  fn();\n}) : fn();\nvar isPromisey = maybePromise => maybePromise instanceof Promise || maybePromise && maybePromise.then && typeof maybePromise.then === \"function\";\nvar updateComponent = async (hostRef, instance, isInitialLoad) => {\n  var _a;\n  const elm = hostRef.$hostElement$;\n  const endUpdate = createTime(\"update\", hostRef.$cmpMeta$.$tagName$);\n  const rc = elm[\"s-rc\"];\n  if (BUILD17.style && isInitialLoad) {\n    attachStyles(hostRef);\n  }\n  const endRender = createTime(\"render\", hostRef.$cmpMeta$.$tagName$);\n  if (BUILD17.isDev) {\n    hostRef.$flags$ |= 1024 /* devOnRender */;\n  }\n  if (BUILD17.hydrateServerSide) {\n    await callRender(hostRef, instance, elm, isInitialLoad);\n  } else {\n    callRender(hostRef, instance, elm, isInitialLoad);\n  }\n  if (BUILD17.isDev) {\n    hostRef.$renderCount$ = hostRef.$renderCount$ === void 0 ? 1 : hostRef.$renderCount$ + 1;\n    hostRef.$flags$ &= ~1024 /* devOnRender */;\n  }\n  if (BUILD17.hydrateServerSide) {\n    try {\n      serverSideConnected(elm);\n      if (isInitialLoad) {\n        if (hostRef.$cmpMeta$.$flags$ & 1 /* shadowDomEncapsulation */) {\n          elm[\"s-en\"] = \"\";\n        } else if (hostRef.$cmpMeta$.$flags$ & 2 /* scopedCssEncapsulation */) {\n          elm[\"s-en\"] = \"c\";\n        }\n      }\n    } catch (e) {\n      consoleError(e, elm);\n    }\n  }\n  if (BUILD17.asyncLoading && rc) {\n    rc.map(cb => cb());\n    elm[\"s-rc\"] = void 0;\n  }\n  endRender();\n  endUpdate();\n  if (BUILD17.asyncLoading) {\n    const childrenPromises = (_a = elm[\"s-p\"]) != null ? _a : [];\n    const postUpdate = () => postUpdateComponent(hostRef);\n    if (childrenPromises.length === 0) {\n      postUpdate();\n    } else {\n      Promise.all(childrenPromises).then(postUpdate);\n      hostRef.$flags$ |= 4 /* isWaitingForChildren */;\n      childrenPromises.length = 0;\n    }\n  } else {\n    postUpdateComponent(hostRef);\n  }\n};\nvar renderingRef = null;\nvar callRender = (hostRef, instance, elm, isInitialLoad) => {\n  const allRenderFn = BUILD17.allRenderFn ? true : false;\n  const lazyLoad = BUILD17.lazyLoad ? true : false;\n  const taskQueue = BUILD17.taskQueue ? true : false;\n  const updatable = BUILD17.updatable ? true : false;\n  try {\n    renderingRef = instance;\n    instance = allRenderFn ? instance.render() : instance.render && instance.render();\n    if (updatable && taskQueue) {\n      hostRef.$flags$ &= ~16 /* isQueuedForUpdate */;\n    }\n    if (updatable || lazyLoad) {\n      hostRef.$flags$ |= 2 /* hasRendered */;\n    }\n    if (BUILD17.hasRenderFn || BUILD17.reflect) {\n      if (BUILD17.vdomRender || BUILD17.reflect) {\n        if (BUILD17.hydrateServerSide) {\n          return Promise.resolve(instance).then(value => renderVdom(hostRef, value, isInitialLoad));\n        } else {\n          renderVdom(hostRef, instance, isInitialLoad);\n        }\n      } else {\n        const shadowRoot = elm.shadowRoot;\n        if (hostRef.$cmpMeta$.$flags$ & 1 /* shadowDomEncapsulation */) {\n          shadowRoot.textContent = instance;\n        } else {\n          elm.textContent = instance;\n        }\n      }\n    }\n  } catch (e) {\n    consoleError(e, hostRef.$hostElement$);\n  }\n  renderingRef = null;\n  return null;\n};\nvar getRenderingRef = () => renderingRef;\nvar postUpdateComponent = hostRef => {\n  const tagName = hostRef.$cmpMeta$.$tagName$;\n  const elm = hostRef.$hostElement$;\n  const endPostUpdate = createTime(\"postUpdate\", tagName);\n  const instance = BUILD17.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  if (BUILD17.cmpDidRender) {\n    if (BUILD17.isDev) {\n      hostRef.$flags$ |= 1024 /* devOnRender */;\n    }\n    safeCall(instance, \"componentDidRender\");\n    if (BUILD17.isDev) {\n      hostRef.$flags$ &= ~1024 /* devOnRender */;\n    }\n  }\n  emitLifecycleEvent(elm, \"componentDidRender\");\n  if (!(hostRef.$flags$ & 64 /* hasLoadedComponent */)) {\n    hostRef.$flags$ |= 64 /* hasLoadedComponent */;\n    if (BUILD17.asyncLoading && BUILD17.cssAnnotations) {\n      addHydratedFlag(elm);\n    }\n    if (BUILD17.cmpDidLoad) {\n      if (BUILD17.isDev) {\n        hostRef.$flags$ |= 2048 /* devOnDidLoad */;\n      }\n      safeCall(instance, \"componentDidLoad\");\n      if (BUILD17.isDev) {\n        hostRef.$flags$ &= ~2048 /* devOnDidLoad */;\n      }\n    }\n    emitLifecycleEvent(elm, \"componentDidLoad\");\n    endPostUpdate();\n    if (BUILD17.asyncLoading) {\n      hostRef.$onReadyResolve$(elm);\n      if (!ancestorComponent) {\n        appDidLoad(tagName);\n      }\n    }\n  } else {\n    if (BUILD17.cmpDidUpdate) {\n      if (BUILD17.isDev) {\n        hostRef.$flags$ |= 1024 /* devOnRender */;\n      }\n      safeCall(instance, \"componentDidUpdate\");\n      if (BUILD17.isDev) {\n        hostRef.$flags$ &= ~1024 /* devOnRender */;\n      }\n    }\n    emitLifecycleEvent(elm, \"componentDidUpdate\");\n    endPostUpdate();\n  }\n  if (BUILD17.method && BUILD17.lazyLoad) {\n    hostRef.$onInstanceResolve$(elm);\n  }\n  if (BUILD17.asyncLoading) {\n    if (hostRef.$onRenderResolve$) {\n      hostRef.$onRenderResolve$();\n      hostRef.$onRenderResolve$ = void 0;\n    }\n    if (hostRef.$flags$ & 512 /* needsRerender */) {\n      nextTick(() => scheduleUpdate(hostRef, false));\n    }\n    hostRef.$flags$ &= ~(4 /* isWaitingForChildren */ | 512 /* needsRerender */);\n  }\n};\nvar forceUpdate = ref => {\n  if (BUILD17.updatable && (Build.isBrowser || Build.isTesting)) {\n    const hostRef = getHostRef(ref);\n    const isConnected = hostRef.$hostElement$.isConnected;\n    if (isConnected && (hostRef.$flags$ & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n      scheduleUpdate(hostRef, false);\n    }\n    return isConnected;\n  }\n  return false;\n};\nvar appDidLoad = who => {\n  if (BUILD17.cssAnnotations) {\n    addHydratedFlag(doc.documentElement);\n  }\n  if (BUILD17.asyncQueue) {\n    plt.$flags$ |= 2 /* appLoaded */;\n  }\n  nextTick(() => emitEvent(win, \"appload\", {\n    detail: {\n      namespace: NAMESPACE\n    }\n  }));\n  if (BUILD17.profile && performance.measure) {\n    performance.measure(`[Stencil] ${NAMESPACE} initial load (by ${who})`, \"st:app:start\");\n  }\n};\nvar safeCall = (instance, method, arg) => {\n  if (instance && instance[method]) {\n    try {\n      return instance[method](arg);\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  return void 0;\n};\nvar emitLifecycleEvent = (elm, lifecycleName) => {\n  if (BUILD17.lifecycleDOMEvents) {\n    emitEvent(elm, \"stencil_\" + lifecycleName, {\n      bubbles: true,\n      composed: true,\n      detail: {\n        namespace: NAMESPACE\n      }\n    });\n  }\n};\nvar addHydratedFlag = elm => {\n  var _a, _b;\n  return BUILD17.hydratedClass ? elm.classList.add((_a = BUILD17.hydratedSelectorName) != null ? _a : \"hydrated\") : BUILD17.hydratedAttribute ? elm.setAttribute((_b = BUILD17.hydratedSelectorName) != null ? _b : \"hydrated\", \"\") : void 0;\n};\nvar serverSideConnected = elm => {\n  const children = elm.children;\n  if (children != null) {\n    for (let i2 = 0, ii = children.length; i2 < ii; i2++) {\n      const childElm = children[i2];\n      if (typeof childElm.connectedCallback === \"function\") {\n        childElm.connectedCallback();\n      }\n      serverSideConnected(childElm);\n    }\n  }\n};\n\n// src/runtime/set-value.ts\nvar getValue = (ref, propName) => getHostRef(ref).$instanceValues$.get(propName);\nvar setValue = (ref, propName, newVal, cmpMeta) => {\n  const hostRef = getHostRef(ref);\n  if (BUILD18.lazyLoad && !hostRef) {\n    throw new Error(`Couldn't find host element for \"${cmpMeta.$tagName$}\" as it is unknown to this Stencil runtime. This usually happens when integrating a 3rd party Stencil component with another Stencil component or application. Please reach out to the maintainers of the 3rd party Stencil component or report this on the Stencil Discord server (https://chat.stenciljs.com) or comment on this similar [GitHub issue](https://github.com/ionic-team/stencil/issues/5457).`);\n  }\n  const elm = BUILD18.lazyLoad ? hostRef.$hostElement$ : ref;\n  const oldVal = hostRef.$instanceValues$.get(propName);\n  const flags = hostRef.$flags$;\n  const instance = BUILD18.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  newVal = parsePropertyValue(newVal, cmpMeta.$members$[propName][0]);\n  const areBothNaN = Number.isNaN(oldVal) && Number.isNaN(newVal);\n  const didValueChange = newVal !== oldVal && !areBothNaN;\n  if ((!BUILD18.lazyLoad || !(flags & 8 /* isConstructingInstance */) || oldVal === void 0) && didValueChange) {\n    hostRef.$instanceValues$.set(propName, newVal);\n    if (BUILD18.isDev) {\n      if (hostRef.$flags$ & 1024 /* devOnRender */) {\n        consoleDevWarn(`The state/prop \"${propName}\" changed during rendering. This can potentially lead to infinite-loops and other bugs.`, \"\\nElement\", elm, \"\\nNew value\", newVal, \"\\nOld value\", oldVal);\n      } else if (hostRef.$flags$ & 2048 /* devOnDidLoad */) {\n        consoleDevWarn(`The state/prop \"${propName}\" changed during \"componentDidLoad()\", this triggers extra re-renders, try to setup on \"componentWillLoad()\"`, \"\\nElement\", elm, \"\\nNew value\", newVal, \"\\nOld value\", oldVal);\n      }\n    }\n    if (!BUILD18.lazyLoad || instance) {\n      if (BUILD18.watchCallback && cmpMeta.$watchers$ && flags & 128 /* isWatchReady */) {\n        const watchMethods = cmpMeta.$watchers$[propName];\n        if (watchMethods) {\n          watchMethods.map(watchMethodName => {\n            try {\n              instance[watchMethodName](newVal, oldVal, propName);\n            } catch (e) {\n              consoleError(e, elm);\n            }\n          });\n        }\n      }\n      if (BUILD18.updatable && (flags & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n        if (BUILD18.cmpShouldUpdate && instance.componentShouldUpdate) {\n          if (instance.componentShouldUpdate(newVal, oldVal, propName) === false) {\n            return;\n          }\n        }\n        scheduleUpdate(hostRef, false);\n      }\n    }\n  }\n};\n\n// src/runtime/proxy-component.ts\nvar proxyComponent = (Cstr, cmpMeta, flags) => {\n  var _a, _b;\n  const prototype = Cstr.prototype;\n  if (BUILD19.formAssociated && cmpMeta.$flags$ & 64 /* formAssociated */ && flags & 1 /* isElementConstructor */) {\n    FORM_ASSOCIATED_CUSTOM_ELEMENT_CALLBACKS.forEach(cbName => Object.defineProperty(prototype, cbName, {\n      value(...args) {\n        const hostRef = getHostRef(this);\n        const elm = BUILD19.lazyLoad ? hostRef.$hostElement$ : this;\n        const instance = BUILD19.lazyLoad ? hostRef.$lazyInstance$ : elm;\n        if (!instance) {\n          hostRef.$onReadyPromise$.then(instance2 => {\n            const cb = instance2[cbName];\n            typeof cb === \"function\" && cb.call(instance2, ...args);\n          });\n        } else {\n          const cb = instance[cbName];\n          typeof cb === \"function\" && cb.call(instance, ...args);\n        }\n      }\n    }));\n  }\n  if (BUILD19.member && cmpMeta.$members$ || BUILD19.watchCallback && (cmpMeta.$watchers$ || Cstr.watchers)) {\n    if (BUILD19.watchCallback && Cstr.watchers && !cmpMeta.$watchers$) {\n      cmpMeta.$watchers$ = Cstr.watchers;\n    }\n    const members = Object.entries((_a = cmpMeta.$members$) != null ? _a : {});\n    members.map(([memberName, [memberFlags]]) => {\n      if ((BUILD19.prop || BUILD19.state) && (memberFlags & 31 /* Prop */ || (!BUILD19.lazyLoad || flags & 2 /* proxyState */) && memberFlags & 32 /* State */)) {\n        Object.defineProperty(prototype, memberName, {\n          get() {\n            return getValue(this, memberName);\n          },\n          set(newValue) {\n            if (BUILD19.isDev) {\n              const ref = getHostRef(this);\n              if (\n              // we are proxying the instance (not element)\n              (flags & 1 /* isElementConstructor */) === 0 &&\n              // the element is not constructing\n              (ref && ref.$flags$ & 8 /* isConstructingInstance */) === 0 &&\n              // the member is a prop\n              (memberFlags & 31 /* Prop */) !== 0 &&\n              // the member is not mutable\n              (memberFlags & 1024 /* Mutable */) === 0) {\n                consoleDevWarn(`@Prop() \"${memberName}\" on <${cmpMeta.$tagName$}> is immutable but was modified from within the component.\nMore information: https://stenciljs.com/docs/properties#prop-mutability`);\n              }\n            }\n            setValue(this, memberName, newValue, cmpMeta);\n          },\n          configurable: true,\n          enumerable: true\n        });\n      } else if (BUILD19.lazyLoad && BUILD19.method && flags & 1 /* isElementConstructor */ && memberFlags & 64 /* Method */) {\n        Object.defineProperty(prototype, memberName, {\n          value(...args) {\n            var _a2;\n            const ref = getHostRef(this);\n            return (_a2 = ref == null ? void 0 : ref.$onInstancePromise$) == null ? void 0 : _a2.then(() => {\n              var _a3;\n              return (_a3 = ref.$lazyInstance$) == null ? void 0 : _a3[memberName](...args);\n            });\n          }\n        });\n      }\n    });\n    if (BUILD19.observeAttribute && (!BUILD19.lazyLoad || flags & 1 /* isElementConstructor */)) {\n      const attrNameToPropName = /* @__PURE__ */new Map();\n      prototype.attributeChangedCallback = function (attrName, oldValue, newValue) {\n        plt.jmp(() => {\n          var _a2;\n          const propName = attrNameToPropName.get(attrName);\n          if (this.hasOwnProperty(propName)) {\n            newValue = this[propName];\n            delete this[propName];\n          } else if (prototype.hasOwnProperty(propName) && typeof this[propName] === \"number\" &&\n          // cast type to number to avoid TS compiler issues\n          this[propName] == newValue) {\n            return;\n          } else if (propName == null) {\n            const hostRef = getHostRef(this);\n            const flags2 = hostRef == null ? void 0 : hostRef.$flags$;\n            if (flags2 && !(flags2 & 8 /* isConstructingInstance */) && flags2 & 128 /* isWatchReady */ && newValue !== oldValue) {\n              const elm = BUILD19.lazyLoad ? hostRef.$hostElement$ : this;\n              const instance = BUILD19.lazyLoad ? hostRef.$lazyInstance$ : elm;\n              const entry = (_a2 = cmpMeta.$watchers$) == null ? void 0 : _a2[attrName];\n              entry == null ? void 0 : entry.forEach(callbackName => {\n                if (instance[callbackName] != null) {\n                  instance[callbackName].call(instance, newValue, oldValue, attrName);\n                }\n              });\n            }\n            return;\n          }\n          this[propName] = newValue === null && typeof this[propName] === \"boolean\" ? false : newValue;\n        });\n      };\n      Cstr.observedAttributes = Array.from(/* @__PURE__ */new Set([...Object.keys((_b = cmpMeta.$watchers$) != null ? _b : {}), ...members.filter(([_, m]) => m[0] & 15 /* HasAttribute */).map(([propName, m]) => {\n        var _a2;\n        const attrName = m[1] || propName;\n        attrNameToPropName.set(attrName, propName);\n        if (BUILD19.reflect && m[0] & 512 /* ReflectAttr */) {\n          (_a2 = cmpMeta.$attrsToReflect$) == null ? void 0 : _a2.push([propName, attrName]);\n        }\n        return attrName;\n      })]));\n    }\n  }\n  return Cstr;\n};\n\n// src/runtime/initialize-component.ts\nvar initializeComponent = async (elm, hostRef, cmpMeta, hmrVersionId) => {\n  let Cstr;\n  if ((hostRef.$flags$ & 32 /* hasInitializedComponent */) === 0) {\n    hostRef.$flags$ |= 32 /* hasInitializedComponent */;\n    const bundleId = cmpMeta.$lazyBundleId$;\n    if ((BUILD20.lazyLoad || BUILD20.hydrateClientSide) && bundleId) {\n      const CstrImport = loadModule(cmpMeta, hostRef, hmrVersionId);\n      if (CstrImport && \"then\" in CstrImport) {\n        const endLoad = uniqueTime(`st:load:${cmpMeta.$tagName$}:${hostRef.$modeName$}`, `[Stencil] Load module for <${cmpMeta.$tagName$}>`);\n        Cstr = await CstrImport;\n        endLoad();\n      } else {\n        Cstr = CstrImport;\n      }\n      if (!Cstr) {\n        throw new Error(`Constructor for \"${cmpMeta.$tagName$}#${hostRef.$modeName$}\" was not found`);\n      }\n      if (BUILD20.member && !Cstr.isProxied) {\n        if (BUILD20.watchCallback) {\n          cmpMeta.$watchers$ = Cstr.watchers;\n        }\n        proxyComponent(Cstr, cmpMeta, 2 /* proxyState */);\n        Cstr.isProxied = true;\n      }\n      const endNewInstance = createTime(\"createInstance\", cmpMeta.$tagName$);\n      if (BUILD20.member) {\n        hostRef.$flags$ |= 8 /* isConstructingInstance */;\n      }\n      try {\n        new Cstr(hostRef);\n      } catch (e) {\n        consoleError(e);\n      }\n      if (BUILD20.member) {\n        hostRef.$flags$ &= ~8 /* isConstructingInstance */;\n      }\n      if (BUILD20.watchCallback) {\n        hostRef.$flags$ |= 128 /* isWatchReady */;\n      }\n      endNewInstance();\n      fireConnectedCallback(hostRef.$lazyInstance$);\n    } else {\n      Cstr = elm.constructor;\n      const cmpTag = elm.localName;\n      customElements.whenDefined(cmpTag).then(() => hostRef.$flags$ |= 128 /* isWatchReady */);\n    }\n    if (BUILD20.style && Cstr && Cstr.style) {\n      let style;\n      if (typeof Cstr.style === \"string\") {\n        style = Cstr.style;\n      } else if (BUILD20.mode && typeof Cstr.style !== \"string\") {\n        hostRef.$modeName$ = computeMode(elm);\n        if (hostRef.$modeName$) {\n          style = Cstr.style[hostRef.$modeName$];\n        }\n        if (BUILD20.hydrateServerSide && hostRef.$modeName$) {\n          elm.setAttribute(\"s-mode\", hostRef.$modeName$);\n        }\n      }\n      const scopeId2 = getScopeId(cmpMeta, hostRef.$modeName$);\n      if (!styles.has(scopeId2)) {\n        const endRegisterStyles = createTime(\"registerStyles\", cmpMeta.$tagName$);\n        if (!BUILD20.hydrateServerSide && BUILD20.shadowDom &&\n        // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n        BUILD20.shadowDomShim && cmpMeta.$flags$ & 8 /* needsShadowDomShim */) {\n          style = await import(\"./shadow-css.js\").then(m => m.scopeCss(style, scopeId2));\n        }\n        registerStyle(scopeId2, style, !!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */));\n        endRegisterStyles();\n      }\n    }\n  }\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  const schedule = () => scheduleUpdate(hostRef, true);\n  if (BUILD20.asyncLoading && ancestorComponent && ancestorComponent[\"s-rc\"]) {\n    ancestorComponent[\"s-rc\"].push(schedule);\n  } else {\n    schedule();\n  }\n};\nvar fireConnectedCallback = instance => {\n  if (BUILD20.lazyLoad && BUILD20.connectedCallback) {\n    safeCall(instance, \"connectedCallback\");\n  }\n};\n\n// src/runtime/connected-callback.ts\nvar connectedCallback = elm => {\n  if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    const cmpMeta = hostRef.$cmpMeta$;\n    const endConnected = createTime(\"connectedCallback\", cmpMeta.$tagName$);\n    if (BUILD21.hostListenerTargetParent) {\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$, true);\n    }\n    if (!(hostRef.$flags$ & 1 /* hasConnected */)) {\n      hostRef.$flags$ |= 1 /* hasConnected */;\n      let hostId;\n      if (BUILD21.hydrateClientSide) {\n        hostId = elm.getAttribute(HYDRATE_ID);\n        if (hostId) {\n          if (BUILD21.shadowDom && supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            const scopeId2 = BUILD21.mode ? addStyle(elm.shadowRoot, cmpMeta, elm.getAttribute(\"s-mode\")) : addStyle(elm.shadowRoot, cmpMeta);\n            elm.classList.remove(scopeId2 + \"-h\", scopeId2 + \"-s\");\n          }\n          initializeClientHydrate(elm, cmpMeta.$tagName$, hostId, hostRef);\n        }\n      }\n      if (BUILD21.slotRelocation && !hostId) {\n        if (BUILD21.hydrateServerSide || (BUILD21.slot || BUILD21.shadowDom) &&\n        // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n        cmpMeta.$flags$ & (4 /* hasSlotRelocation */ | 8 /* needsShadowDomShim */)) {\n          setContentReference(elm);\n        }\n      }\n      if (BUILD21.asyncLoading) {\n        let ancestorComponent = elm;\n        while (ancestorComponent = ancestorComponent.parentNode || ancestorComponent.host) {\n          if (BUILD21.hydrateClientSide && ancestorComponent.nodeType === 1 /* ElementNode */ && ancestorComponent.hasAttribute(\"s-id\") && ancestorComponent[\"s-p\"] || ancestorComponent[\"s-p\"]) {\n            attachToAncestor(hostRef, hostRef.$ancestorComponent$ = ancestorComponent);\n            break;\n          }\n        }\n      }\n      if (BUILD21.prop && !BUILD21.hydrateServerSide && cmpMeta.$members$) {\n        Object.entries(cmpMeta.$members$).map(([memberName, [memberFlags]]) => {\n          if (memberFlags & 31 /* Prop */ && elm.hasOwnProperty(memberName)) {\n            const value = elm[memberName];\n            delete elm[memberName];\n            elm[memberName] = value;\n          }\n        });\n      }\n      if (BUILD21.initializeNextTick) {\n        nextTick(() => initializeComponent(elm, hostRef, cmpMeta));\n      } else {\n        initializeComponent(elm, hostRef, cmpMeta);\n      }\n    } else {\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$, false);\n      if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n        fireConnectedCallback(hostRef.$lazyInstance$);\n      } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => fireConnectedCallback(hostRef.$lazyInstance$));\n      }\n    }\n    endConnected();\n  }\n};\nvar setContentReference = elm => {\n  const contentRefElm = elm[\"s-cr\"] = doc.createComment(BUILD21.isDebug ? `content-ref (host=${elm.localName})` : \"\");\n  contentRefElm[\"s-cn\"] = true;\n  insertBefore(elm, contentRefElm, elm.firstChild);\n};\n\n// src/runtime/disconnected-callback.ts\nimport { BUILD as BUILD22 } from \"@stencil/core/internal/app-data\";\nvar disconnectInstance = instance => {\n  if (BUILD22.lazyLoad && BUILD22.disconnectedCallback) {\n    safeCall(instance, \"disconnectedCallback\");\n  }\n  if (BUILD22.cmpDidUnload) {\n    safeCall(instance, \"componentDidUnload\");\n  }\n};\nvar disconnectedCallback = async elm => {\n  if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    if (BUILD22.hostListener) {\n      if (hostRef.$rmListeners$) {\n        hostRef.$rmListeners$.map(rmListener => rmListener());\n        hostRef.$rmListeners$ = void 0;\n      }\n    }\n    if (!BUILD22.lazyLoad) {\n      disconnectInstance(elm);\n    } else if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n      disconnectInstance(hostRef.$lazyInstance$);\n    } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n      hostRef.$onReadyPromise$.then(() => disconnectInstance(hostRef.$lazyInstance$));\n    }\n  }\n};\n\n// src/runtime/dom-extras.ts\nimport { BUILD as BUILD23 } from \"@stencil/core/internal/app-data\";\nvar patchPseudoShadowDom = (hostElementPrototype, descriptorPrototype) => {\n  patchCloneNode(hostElementPrototype);\n  patchSlotAppendChild(hostElementPrototype);\n  patchSlotAppend(hostElementPrototype);\n  patchSlotPrepend(hostElementPrototype);\n  patchSlotInsertAdjacentElement(hostElementPrototype);\n  patchSlotInsertAdjacentHTML(hostElementPrototype);\n  patchSlotInsertAdjacentText(hostElementPrototype);\n  patchTextContent(hostElementPrototype);\n  patchChildSlotNodes(hostElementPrototype, descriptorPrototype);\n  patchSlotRemoveChild(hostElementPrototype);\n};\nvar patchCloneNode = HostElementPrototype => {\n  const orgCloneNode = HostElementPrototype.cloneNode;\n  HostElementPrototype.cloneNode = function (deep) {\n    const srcNode = this;\n    const isShadowDom = BUILD23.shadowDom ? srcNode.shadowRoot && supportsShadow : false;\n    const clonedNode = orgCloneNode.call(srcNode, isShadowDom ? deep : false);\n    if (BUILD23.slot && !isShadowDom && deep) {\n      let i2 = 0;\n      let slotted, nonStencilNode;\n      const stencilPrivates = [\"s-id\", \"s-cr\", \"s-lr\", \"s-rc\", \"s-sc\", \"s-p\", \"s-cn\", \"s-sr\", \"s-sn\", \"s-hn\", \"s-ol\", \"s-nr\", \"s-si\", \"s-rf\", \"s-scs\"];\n      for (; i2 < srcNode.childNodes.length; i2++) {\n        slotted = srcNode.childNodes[i2][\"s-nr\"];\n        nonStencilNode = stencilPrivates.every(privateField => !srcNode.childNodes[i2][privateField]);\n        if (slotted) {\n          if (BUILD23.appendChildSlotFix && clonedNode.__appendChild) {\n            clonedNode.__appendChild(slotted.cloneNode(true));\n          } else {\n            clonedNode.appendChild(slotted.cloneNode(true));\n          }\n        }\n        if (nonStencilNode) {\n          clonedNode.appendChild(srcNode.childNodes[i2].cloneNode(true));\n        }\n      }\n    }\n    return clonedNode;\n  };\n};\nvar patchSlotAppendChild = HostElementPrototype => {\n  HostElementPrototype.__appendChild = HostElementPrototype.appendChild;\n  HostElementPrototype.appendChild = function (newChild) {\n    const slotName = newChild[\"s-sn\"] = getSlotName(newChild);\n    const slotNode = getHostSlotNode(this.childNodes, slotName, this.tagName);\n    if (slotNode) {\n      const slotChildNodes = getHostSlotChildNodes(slotNode, slotName);\n      const appendAfter = slotChildNodes[slotChildNodes.length - 1];\n      const insertedNode = insertBefore(appendAfter.parentNode, newChild, appendAfter.nextSibling);\n      updateFallbackSlotVisibility(this);\n      return insertedNode;\n    }\n    return this.__appendChild(newChild);\n  };\n};\nvar patchSlotRemoveChild = ElementPrototype => {\n  ElementPrototype.__removeChild = ElementPrototype.removeChild;\n  ElementPrototype.removeChild = function (toRemove) {\n    if (toRemove && typeof toRemove[\"s-sn\"] !== \"undefined\") {\n      const slotNode = getHostSlotNode(this.childNodes, toRemove[\"s-sn\"], this.tagName);\n      if (slotNode) {\n        const slotChildNodes = getHostSlotChildNodes(slotNode, toRemove[\"s-sn\"]);\n        const existingNode = slotChildNodes.find(n => n === toRemove);\n        if (existingNode) {\n          existingNode.remove();\n          updateFallbackSlotVisibility(this);\n          return;\n        }\n      }\n    }\n    return this.__removeChild(toRemove);\n  };\n};\nvar patchSlotPrepend = HostElementPrototype => {\n  const originalPrepend = HostElementPrototype.prepend;\n  HostElementPrototype.prepend = function (...newChildren) {\n    newChildren.forEach(newChild => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      const slotName = newChild[\"s-sn\"] = getSlotName(newChild);\n      const slotNode = getHostSlotNode(this.childNodes, slotName, this.tagName);\n      if (slotNode) {\n        const slotPlaceholder = document.createTextNode(\"\");\n        slotPlaceholder[\"s-nr\"] = newChild;\n        slotNode[\"s-cr\"].parentNode.__appendChild(slotPlaceholder);\n        newChild[\"s-ol\"] = slotPlaceholder;\n        const slotChildNodes = getHostSlotChildNodes(slotNode, slotName);\n        const appendAfter = slotChildNodes[0];\n        return insertBefore(appendAfter.parentNode, newChild, appendAfter.nextSibling);\n      }\n      if (newChild.nodeType === 1 && !!newChild.getAttribute(\"slot\")) {\n        newChild.hidden = true;\n      }\n      return originalPrepend.call(this, newChild);\n    });\n  };\n};\nvar patchSlotAppend = HostElementPrototype => {\n  HostElementPrototype.append = function (...newChildren) {\n    newChildren.forEach(newChild => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      this.appendChild(newChild);\n    });\n  };\n};\nvar patchSlotInsertAdjacentHTML = HostElementPrototype => {\n  const originalInsertAdjacentHtml = HostElementPrototype.insertAdjacentHTML;\n  HostElementPrototype.insertAdjacentHTML = function (position, text) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentHtml.call(this, position, text);\n    }\n    const container = this.ownerDocument.createElement(\"_\");\n    let node;\n    container.innerHTML = text;\n    if (position === \"afterbegin\") {\n      while (node = container.firstChild) {\n        this.prepend(node);\n      }\n    } else if (position === \"beforeend\") {\n      while (node = container.firstChild) {\n        this.append(node);\n      }\n    }\n  };\n};\nvar patchSlotInsertAdjacentText = HostElementPrototype => {\n  HostElementPrototype.insertAdjacentText = function (position, text) {\n    this.insertAdjacentHTML(position, text);\n  };\n};\nvar patchSlotInsertAdjacentElement = HostElementPrototype => {\n  const originalInsertAdjacentElement = HostElementPrototype.insertAdjacentElement;\n  HostElementPrototype.insertAdjacentElement = function (position, element) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentElement.call(this, position, element);\n    }\n    if (position === \"afterbegin\") {\n      this.prepend(element);\n      return element;\n    } else if (position === \"beforeend\") {\n      this.append(element);\n      return element;\n    }\n    return element;\n  };\n};\nvar patchTextContent = hostElementPrototype => {\n  const descriptor = Object.getOwnPropertyDescriptor(Node.prototype, \"textContent\");\n  Object.defineProperty(hostElementPrototype, \"__textContent\", descriptor);\n  if (BUILD23.experimentalScopedSlotChanges) {\n    Object.defineProperty(hostElementPrototype, \"textContent\", {\n      // To mimic shadow root behavior, we need to return the text content of all\n      // nodes in a slot reference node\n      get() {\n        const slotRefNodes = getAllChildSlotNodes(this.childNodes);\n        const textContent = slotRefNodes.map(node => {\n          var _a, _b;\n          const text = [];\n          let slotContent = node.nextSibling;\n          while (slotContent && slotContent[\"s-sn\"] === node[\"s-sn\"]) {\n            if (slotContent.nodeType === 3 /* TEXT_NODE */ || slotContent.nodeType === 1 /* ELEMENT_NODE */) {\n              text.push((_b = (_a = slotContent.textContent) == null ? void 0 : _a.trim()) != null ? _b : \"\");\n            }\n            slotContent = slotContent.nextSibling;\n          }\n          return text.filter(ref => ref !== \"\").join(\" \");\n        }).filter(text => text !== \"\").join(\" \");\n        return \" \" + textContent + \" \";\n      },\n      // To mimic shadow root behavior, we need to overwrite all nodes in a slot\n      // reference node. If a default slot reference node exists, the text content will be\n      // placed there. Otherwise, the new text node will be hidden\n      set(value) {\n        const slotRefNodes = getAllChildSlotNodes(this.childNodes);\n        slotRefNodes.forEach(node => {\n          let slotContent = node.nextSibling;\n          while (slotContent && slotContent[\"s-sn\"] === node[\"s-sn\"]) {\n            const tmp = slotContent;\n            slotContent = slotContent.nextSibling;\n            tmp.remove();\n          }\n          if (node[\"s-sn\"] === \"\") {\n            const textNode = this.ownerDocument.createTextNode(value);\n            textNode[\"s-sn\"] = \"\";\n            insertBefore(node.parentElement, textNode, node.nextSibling);\n          } else {\n            node.remove();\n          }\n        });\n      }\n    });\n  } else {\n    Object.defineProperty(hostElementPrototype, \"textContent\", {\n      get() {\n        var _a;\n        const slotNode = getHostSlotNode(this.childNodes, \"\", this.tagName);\n        if (((_a = slotNode == null ? void 0 : slotNode.nextSibling) == null ? void 0 : _a.nodeType) === 3 /* TEXT_NODE */) {\n          return slotNode.nextSibling.textContent;\n        } else if (slotNode) {\n          return slotNode.textContent;\n        } else {\n          return this.__textContent;\n        }\n      },\n      set(value) {\n        var _a;\n        const slotNode = getHostSlotNode(this.childNodes, \"\", this.tagName);\n        if (((_a = slotNode == null ? void 0 : slotNode.nextSibling) == null ? void 0 : _a.nodeType) === 3 /* TEXT_NODE */) {\n          slotNode.nextSibling.textContent = value;\n        } else if (slotNode) {\n          slotNode.textContent = value;\n        } else {\n          this.__textContent = value;\n          const contentRefElm = this[\"s-cr\"];\n          if (contentRefElm) {\n            insertBefore(this, contentRefElm, this.firstChild);\n          }\n        }\n      }\n    });\n  }\n};\nvar patchChildSlotNodes = (elm, cmpMeta) => {\n  class FakeNodeList extends Array {\n    item(n) {\n      return this[n];\n    }\n  }\n  if (cmpMeta.$flags$ & 8 /* needsShadowDomShim */) {\n    const childNodesFn = elm.__lookupGetter__(\"childNodes\");\n    Object.defineProperty(elm, \"children\", {\n      get() {\n        return this.childNodes.map(n => n.nodeType === 1);\n      }\n    });\n    Object.defineProperty(elm, \"childElementCount\", {\n      get() {\n        return elm.children.length;\n      }\n    });\n    Object.defineProperty(elm, \"childNodes\", {\n      get() {\n        const childNodes = childNodesFn.call(this);\n        if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0 && getHostRef(this).$flags$ & 2 /* hasRendered */) {\n          const result = new FakeNodeList();\n          for (let i2 = 0; i2 < childNodes.length; i2++) {\n            const slot = childNodes[i2][\"s-nr\"];\n            if (slot) {\n              result.push(slot);\n            }\n          }\n          return result;\n        }\n        return FakeNodeList.from(childNodes);\n      }\n    });\n  }\n};\nvar getAllChildSlotNodes = childNodes => {\n  const slotRefNodes = [];\n  for (const childNode of Array.from(childNodes)) {\n    if (childNode[\"s-sr\"]) {\n      slotRefNodes.push(childNode);\n    }\n    slotRefNodes.push(...getAllChildSlotNodes(childNode.childNodes));\n  }\n  return slotRefNodes;\n};\nvar getSlotName = node => node[\"s-sn\"] || node.nodeType === 1 && node.getAttribute(\"slot\") || \"\";\nvar getHostSlotNode = (childNodes, slotName, hostName) => {\n  let i2 = 0;\n  let childNode;\n  for (; i2 < childNodes.length; i2++) {\n    childNode = childNodes[i2];\n    if (childNode[\"s-sr\"] && childNode[\"s-sn\"] === slotName && childNode[\"s-hn\"] === hostName) {\n      return childNode;\n    }\n    childNode = getHostSlotNode(childNode.childNodes, slotName, hostName);\n    if (childNode) {\n      return childNode;\n    }\n  }\n  return null;\n};\nvar getHostSlotChildNodes = (n, slotName) => {\n  const childNodes = [n];\n  while ((n = n.nextSibling) && n[\"s-sn\"] === slotName) {\n    childNodes.push(n);\n  }\n  return childNodes;\n};\n\n// src/runtime/bootstrap-custom-element.ts\nvar defineCustomElement = (Cstr, compactMeta) => {\n  customElements.define(compactMeta[1], proxyCustomElement(Cstr, compactMeta));\n};\nvar proxyCustomElement = (Cstr, compactMeta) => {\n  const cmpMeta = {\n    $flags$: compactMeta[0],\n    $tagName$: compactMeta[1]\n  };\n  if (BUILD24.member) {\n    cmpMeta.$members$ = compactMeta[2];\n  }\n  if (BUILD24.hostListener) {\n    cmpMeta.$listeners$ = compactMeta[3];\n  }\n  if (BUILD24.watchCallback) {\n    cmpMeta.$watchers$ = Cstr.$watchers$;\n  }\n  if (BUILD24.reflect) {\n    cmpMeta.$attrsToReflect$ = [];\n  }\n  if (BUILD24.shadowDom && !supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n    cmpMeta.$flags$ |= 8 /* needsShadowDomShim */;\n  }\n  if (BUILD24.experimentalSlotFixes) {\n    if (BUILD24.scoped && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n      patchPseudoShadowDom(Cstr.prototype, cmpMeta);\n    }\n  } else {\n    if (BUILD24.slotChildNodesFix) {\n      patchChildSlotNodes(Cstr.prototype, cmpMeta);\n    }\n    if (BUILD24.cloneNodeFix) {\n      patchCloneNode(Cstr.prototype);\n    }\n    if (BUILD24.appendChildSlotFix) {\n      patchSlotAppendChild(Cstr.prototype);\n    }\n    if (BUILD24.scopedSlotTextContentFix && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n      patchTextContent(Cstr.prototype);\n    }\n  }\n  const originalConnectedCallback = Cstr.prototype.connectedCallback;\n  const originalDisconnectedCallback = Cstr.prototype.disconnectedCallback;\n  Object.assign(Cstr.prototype, {\n    __registerHost() {\n      registerHost(this, cmpMeta);\n    },\n    connectedCallback() {\n      const hostRef = getHostRef(this);\n      addHostEventListeners(this, hostRef, cmpMeta.$listeners$, false);\n      connectedCallback(this);\n      if (BUILD24.connectedCallback && originalConnectedCallback) {\n        originalConnectedCallback.call(this);\n      }\n    },\n    disconnectedCallback() {\n      disconnectedCallback(this);\n      if (BUILD24.disconnectedCallback && originalDisconnectedCallback) {\n        originalDisconnectedCallback.call(this);\n      }\n    },\n    __attachShadow() {\n      if (supportsShadow) {\n        if (!this.shadowRoot) {\n          if (BUILD24.shadowDelegatesFocus) {\n            this.attachShadow({\n              mode: \"open\",\n              delegatesFocus: !!(cmpMeta.$flags$ & 16 /* shadowDelegatesFocus */)\n            });\n          } else {\n            this.attachShadow({\n              mode: \"open\"\n            });\n          }\n        } else {\n          if (this.shadowRoot.mode !== \"open\") {\n            throw new Error(`Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${this.shadowRoot.mode} but Stencil only supports open shadow roots.`);\n          }\n        }\n      } else {\n        this.shadowRoot = this;\n      }\n    }\n  });\n  Cstr.is = cmpMeta.$tagName$;\n  return proxyComponent(Cstr, cmpMeta, 1 /* isElementConstructor */ | 2 /* proxyState */);\n};\nvar forceModeUpdate = elm => {\n  if (BUILD24.style && BUILD24.mode && !BUILD24.lazyLoad) {\n    const mode = computeMode(elm);\n    const hostRef = getHostRef(elm);\n    if (hostRef.$modeName$ !== mode) {\n      const cmpMeta = hostRef.$cmpMeta$;\n      const oldScopeId = elm[\"s-sc\"];\n      const scopeId2 = getScopeId(cmpMeta, mode);\n      const style = elm.constructor.style[mode];\n      const flags = cmpMeta.$flags$;\n      if (style) {\n        if (!styles.has(scopeId2)) {\n          registerStyle(scopeId2, style, !!(flags & 1 /* shadowDomEncapsulation */));\n        }\n        hostRef.$modeName$ = mode;\n        elm.classList.remove(oldScopeId + \"-h\", oldScopeId + \"-s\");\n        attachStyles(hostRef);\n        forceUpdate(elm);\n      }\n    }\n  }\n};\n\n// src/runtime/bootstrap-lazy.ts\nimport { BUILD as BUILD25 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/hmr-component.ts\nvar hmrStart = (hostElement, cmpMeta, hmrVersionId) => {\n  const hostRef = getHostRef(hostElement);\n  hostRef.$flags$ = 1 /* hasConnected */;\n  initializeComponent(hostElement, hostRef, cmpMeta, hmrVersionId);\n};\n\n// src/runtime/bootstrap-lazy.ts\nvar bootstrapLazy = (lazyBundles, options = {}) => {\n  var _a;\n  if (BUILD25.profile && performance.mark) {\n    performance.mark(\"st:app:start\");\n  }\n  installDevTools();\n  const endBootstrap = createTime(\"bootstrapLazy\");\n  const cmpTags = [];\n  const exclude = options.exclude || [];\n  const customElements2 = win.customElements;\n  const head = doc.head;\n  const metaCharset = /* @__PURE__ */head.querySelector(\"meta[charset]\");\n  const dataStyles = /* @__PURE__ */doc.createElement(\"style\");\n  const deferredConnectedCallbacks = [];\n  let appLoadFallback;\n  let isBootstrapping = true;\n  Object.assign(plt, options);\n  plt.$resourcesUrl$ = new URL(options.resourcesUrl || \"./\", doc.baseURI).href;\n  if (BUILD25.asyncQueue) {\n    if (options.syncQueue) {\n      plt.$flags$ |= 4 /* queueSync */;\n    }\n  }\n  if (BUILD25.hydrateClientSide) {\n    plt.$flags$ |= 2 /* appLoaded */;\n  }\n  let hasSlotRelocation = false;\n  lazyBundles.map(lazyBundle => {\n    lazyBundle[1].map(compactMeta => {\n      var _a2;\n      const cmpMeta = {\n        $flags$: compactMeta[0],\n        $tagName$: compactMeta[1],\n        $members$: compactMeta[2],\n        $listeners$: compactMeta[3]\n      };\n      if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n        hasSlotRelocation = true;\n      }\n      if (BUILD25.member) {\n        cmpMeta.$members$ = compactMeta[2];\n      }\n      if (BUILD25.hostListener) {\n        cmpMeta.$listeners$ = compactMeta[3];\n      }\n      if (BUILD25.reflect) {\n        cmpMeta.$attrsToReflect$ = [];\n      }\n      if (BUILD25.watchCallback) {\n        cmpMeta.$watchers$ = (_a2 = compactMeta[4]) != null ? _a2 : {};\n      }\n      if (BUILD25.shadowDom && !supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n        cmpMeta.$flags$ |= 8 /* needsShadowDomShim */;\n      }\n      const tagName = BUILD25.transformTagName && options.transformTagName ? options.transformTagName(cmpMeta.$tagName$) : cmpMeta.$tagName$;\n      const HostElement = class extends HTMLElement {\n        // StencilLazyHost\n        constructor(self) {\n          super(self);\n          this.hasRegisteredEventListeners = false;\n          self = this;\n          registerHost(self, cmpMeta);\n          if (BUILD25.shadowDom && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            if (supportsShadow) {\n              if (!self.shadowRoot) {\n                if (BUILD25.shadowDelegatesFocus) {\n                  self.attachShadow({\n                    mode: \"open\",\n                    delegatesFocus: !!(cmpMeta.$flags$ & 16 /* shadowDelegatesFocus */)\n                  });\n                } else {\n                  self.attachShadow({\n                    mode: \"open\"\n                  });\n                }\n              } else {\n                if (self.shadowRoot.mode !== \"open\") {\n                  throw new Error(`Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${self.shadowRoot.mode} but Stencil only supports open shadow roots.`);\n                }\n              }\n            } else if (!BUILD25.hydrateServerSide && !(\"shadowRoot\" in self)) {\n              self.shadowRoot = self;\n            }\n          }\n        }\n        connectedCallback() {\n          const hostRef = getHostRef(this);\n          if (!this.hasRegisteredEventListeners) {\n            this.hasRegisteredEventListeners = true;\n            addHostEventListeners(this, hostRef, cmpMeta.$listeners$, false);\n          }\n          if (appLoadFallback) {\n            clearTimeout(appLoadFallback);\n            appLoadFallback = null;\n          }\n          if (isBootstrapping) {\n            deferredConnectedCallbacks.push(this);\n          } else {\n            plt.jmp(() => connectedCallback(this));\n          }\n        }\n        disconnectedCallback() {\n          plt.jmp(() => disconnectedCallback(this));\n        }\n        componentOnReady() {\n          return getHostRef(this).$onReadyPromise$;\n        }\n      };\n      if (BUILD25.experimentalSlotFixes) {\n        if (BUILD25.scoped && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n          patchPseudoShadowDom(HostElement.prototype, cmpMeta);\n        }\n      } else {\n        if (BUILD25.slotChildNodesFix) {\n          patchChildSlotNodes(HostElement.prototype, cmpMeta);\n        }\n        if (BUILD25.cloneNodeFix) {\n          patchCloneNode(HostElement.prototype);\n        }\n        if (BUILD25.appendChildSlotFix) {\n          patchSlotAppendChild(HostElement.prototype);\n        }\n        if (BUILD25.scopedSlotTextContentFix && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n          patchTextContent(HostElement.prototype);\n        }\n      }\n      if (BUILD25.formAssociated && cmpMeta.$flags$ & 64 /* formAssociated */) {\n        HostElement.formAssociated = true;\n      }\n      if (BUILD25.hotModuleReplacement) {\n        HostElement.prototype[\"s-hmr\"] = function (hmrVersionId) {\n          hmrStart(this, cmpMeta, hmrVersionId);\n        };\n      }\n      cmpMeta.$lazyBundleId$ = lazyBundle[0];\n      if (!exclude.includes(tagName) && !customElements2.get(tagName)) {\n        cmpTags.push(tagName);\n        customElements2.define(tagName, proxyComponent(HostElement, cmpMeta, 1 /* isElementConstructor */));\n      }\n    });\n  });\n  if (cmpTags.length > 0) {\n    if (hasSlotRelocation) {\n      dataStyles.textContent += SLOT_FB_CSS;\n    }\n    if (BUILD25.invisiblePrehydration && (BUILD25.hydratedClass || BUILD25.hydratedAttribute)) {\n      dataStyles.textContent += cmpTags.sort() + HYDRATED_CSS;\n    }\n    if (dataStyles.innerHTML.length) {\n      dataStyles.setAttribute(\"data-styles\", \"\");\n      const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(doc);\n      if (nonce != null) {\n        dataStyles.setAttribute(\"nonce\", nonce);\n      }\n      head.insertBefore(dataStyles, metaCharset ? metaCharset.nextSibling : head.firstChild);\n    }\n  }\n  isBootstrapping = false;\n  if (deferredConnectedCallbacks.length) {\n    deferredConnectedCallbacks.map(host => host.connectedCallback());\n  } else {\n    if (BUILD25.profile) {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30, \"timeout\"));\n    } else {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30));\n    }\n  }\n  endBootstrap();\n};\n\n// src/runtime/fragment.ts\nvar Fragment = (_, children) => children;\n\n// src/runtime/host-listener.ts\nimport { BUILD as BUILD26 } from \"@stencil/core/internal/app-data\";\nvar addHostEventListeners = (elm, hostRef, listeners, attachParentListeners) => {\n  if (BUILD26.hostListener && listeners) {\n    if (BUILD26.hostListenerTargetParent) {\n      if (attachParentListeners) {\n        listeners = listeners.filter(([flags]) => flags & 32 /* TargetParent */);\n      } else {\n        listeners = listeners.filter(([flags]) => !(flags & 32 /* TargetParent */));\n      }\n    }\n    listeners.map(([flags, name, method]) => {\n      const target = BUILD26.hostListenerTarget ? getHostListenerTarget(elm, flags) : elm;\n      const handler = hostListenerProxy(hostRef, method);\n      const opts = hostListenerOpts(flags);\n      plt.ael(target, name, handler, opts);\n      (hostRef.$rmListeners$ = hostRef.$rmListeners$ || []).push(() => plt.rel(target, name, handler, opts));\n    });\n  }\n};\nvar hostListenerProxy = (hostRef, methodName) => ev => {\n  var _a;\n  try {\n    if (BUILD26.lazyLoad) {\n      if (hostRef.$flags$ & 256 /* isListenReady */) {\n        (_a = hostRef.$lazyInstance$) == null ? void 0 : _a[methodName](ev);\n      } else {\n        (hostRef.$queuedListeners$ = hostRef.$queuedListeners$ || []).push([methodName, ev]);\n      }\n    } else {\n      hostRef.$hostElement$[methodName](ev);\n    }\n  } catch (e) {\n    consoleError(e);\n  }\n};\nvar getHostListenerTarget = (elm, flags) => {\n  if (BUILD26.hostListenerTargetDocument && flags & 4 /* TargetDocument */) return doc;\n  if (BUILD26.hostListenerTargetWindow && flags & 8 /* TargetWindow */) return win;\n  if (BUILD26.hostListenerTargetBody && flags & 16 /* TargetBody */) return doc.body;\n  if (BUILD26.hostListenerTargetParent && flags & 32 /* TargetParent */ && elm.parentElement) return elm.parentElement;\n  return elm;\n};\nvar hostListenerOpts = flags => supportsListenerOptions ? {\n  passive: (flags & 1 /* Passive */) !== 0,\n  capture: (flags & 2 /* Capture */) !== 0\n} : (flags & 2 /* Capture */) !== 0;\n\n// src/runtime/nonce.ts\nvar setNonce = nonce => plt.$nonce$ = nonce;\n\n// src/runtime/platform-options.ts\nvar setPlatformOptions = opts => Object.assign(plt, opts);\n\n// src/runtime/vdom/vdom-annotations.ts\nvar insertVdomAnnotations = (doc2, staticComponents) => {\n  if (doc2 != null) {\n    const docData = {\n      hostIds: 0,\n      rootLevelIds: 0,\n      staticComponents: new Set(staticComponents)\n    };\n    const orgLocationNodes = [];\n    parseVNodeAnnotations(doc2, doc2.body, docData, orgLocationNodes);\n    orgLocationNodes.forEach(orgLocationNode => {\n      var _a;\n      if (orgLocationNode != null && orgLocationNode[\"s-nr\"]) {\n        const nodeRef = orgLocationNode[\"s-nr\"];\n        let hostId = nodeRef[\"s-host-id\"];\n        let nodeId = nodeRef[\"s-node-id\"];\n        let childId = `${hostId}.${nodeId}`;\n        if (hostId == null) {\n          hostId = 0;\n          docData.rootLevelIds++;\n          nodeId = docData.rootLevelIds;\n          childId = `${hostId}.${nodeId}`;\n          if (nodeRef.nodeType === 1 /* ElementNode */) {\n            nodeRef.setAttribute(HYDRATE_CHILD_ID, childId);\n          } else if (nodeRef.nodeType === 3 /* TextNode */) {\n            if (hostId === 0) {\n              const textContent = (_a = nodeRef.nodeValue) == null ? void 0 : _a.trim();\n              if (textContent === \"\") {\n                orgLocationNode.remove();\n                return;\n              }\n            }\n            const commentBeforeTextNode = doc2.createComment(childId);\n            commentBeforeTextNode.nodeValue = `${TEXT_NODE_ID}.${childId}`;\n            insertBefore(nodeRef.parentNode, commentBeforeTextNode, nodeRef);\n          }\n        }\n        let orgLocationNodeId = `${ORG_LOCATION_ID}.${childId}`;\n        const orgLocationParentNode = orgLocationNode.parentElement;\n        if (orgLocationParentNode) {\n          if (orgLocationParentNode[\"s-en\"] === \"\") {\n            orgLocationNodeId += `.`;\n          } else if (orgLocationParentNode[\"s-en\"] === \"c\") {\n            orgLocationNodeId += `.c`;\n          }\n        }\n        orgLocationNode.nodeValue = orgLocationNodeId;\n      }\n    });\n  }\n};\nvar parseVNodeAnnotations = (doc2, node, docData, orgLocationNodes) => {\n  var _a;\n  if (node == null) {\n    return;\n  }\n  if (node[\"s-nr\"] != null) {\n    orgLocationNodes.push(node);\n  }\n  if (node.nodeType === 1 /* ElementNode */) {\n    const childNodes = [...Array.from(node.childNodes), ...Array.from(((_a = node.shadowRoot) == null ? void 0 : _a.childNodes) || [])];\n    childNodes.forEach(childNode => {\n      const hostRef = getHostRef(childNode);\n      if (hostRef != null && !docData.staticComponents.has(childNode.nodeName.toLowerCase())) {\n        const cmpData = {\n          nodeIds: 0\n        };\n        insertVNodeAnnotations(doc2, childNode, hostRef.$vnode$, docData, cmpData);\n      }\n      parseVNodeAnnotations(doc2, childNode, docData, orgLocationNodes);\n    });\n  }\n};\nvar insertVNodeAnnotations = (doc2, hostElm, vnode, docData, cmpData) => {\n  if (vnode != null) {\n    const hostId = ++docData.hostIds;\n    hostElm.setAttribute(HYDRATE_ID, hostId);\n    if (hostElm[\"s-cr\"] != null) {\n      hostElm[\"s-cr\"].nodeValue = `${CONTENT_REF_ID}.${hostId}`;\n    }\n    if (vnode.$children$ != null) {\n      const depth = 0;\n      vnode.$children$.forEach((vnodeChild, index) => {\n        insertChildVNodeAnnotations(doc2, vnodeChild, cmpData, hostId, depth, index);\n      });\n    }\n    if (hostElm && vnode && vnode.$elm$ && !hostElm.hasAttribute(HYDRATE_CHILD_ID)) {\n      const parent = hostElm.parentElement;\n      if (parent && parent.childNodes) {\n        const parentChildNodes = Array.from(parent.childNodes);\n        const comment = parentChildNodes.find(node => node.nodeType === 8 /* CommentNode */ && node[\"s-sr\"]);\n        if (comment) {\n          const index = parentChildNodes.indexOf(hostElm) - 1;\n          vnode.$elm$.setAttribute(HYDRATE_CHILD_ID, `${comment[\"s-host-id\"]}.${comment[\"s-node-id\"]}.0.${index}`);\n        }\n      }\n    }\n  }\n};\nvar insertChildVNodeAnnotations = (doc2, vnodeChild, cmpData, hostId, depth, index) => {\n  const childElm = vnodeChild.$elm$;\n  if (childElm == null) {\n    return;\n  }\n  const nodeId = cmpData.nodeIds++;\n  const childId = `${hostId}.${nodeId}.${depth}.${index}`;\n  childElm[\"s-host-id\"] = hostId;\n  childElm[\"s-node-id\"] = nodeId;\n  if (childElm.nodeType === 1 /* ElementNode */) {\n    childElm.setAttribute(HYDRATE_CHILD_ID, childId);\n  } else if (childElm.nodeType === 3 /* TextNode */) {\n    const parentNode = childElm.parentNode;\n    const nodeName = parentNode == null ? void 0 : parentNode.nodeName;\n    if (nodeName !== \"STYLE\" && nodeName !== \"SCRIPT\") {\n      const textNodeId = `${TEXT_NODE_ID}.${childId}`;\n      const commentBeforeTextNode = doc2.createComment(textNodeId);\n      insertBefore(parentNode, commentBeforeTextNode, childElm);\n    }\n  } else if (childElm.nodeType === 8 /* CommentNode */) {\n    if (childElm[\"s-sr\"]) {\n      const slotName = childElm[\"s-sn\"] || \"\";\n      const slotNodeId = `${SLOT_NODE_ID}.${childId}.${slotName}`;\n      childElm.nodeValue = slotNodeId;\n    }\n  }\n  if (vnodeChild.$children$ != null) {\n    const childDepth = depth + 1;\n    vnodeChild.$children$.forEach((vnode, index2) => {\n      insertChildVNodeAnnotations(doc2, vnode, cmpData, hostId, childDepth, index2);\n    });\n  }\n};\nexport { BUILD27 as BUILD, Build, Env, Fragment, H, H as HTMLElement, Host, NAMESPACE2 as NAMESPACE, STENCIL_DEV_MODE, addHostEventListeners, bootstrapLazy, cmpModules, connectedCallback, consoleDevError, consoleDevInfo, consoleDevWarn, consoleError, createEvent, defineCustomElement, disconnectedCallback, doc, forceModeUpdate, forceUpdate, getAssetPath, getElement, getHostRef, getMode, getRenderingRef, getValue, h, insertVdomAnnotations, isMemberInElement, loadModule, modeResolutionChain, nextTick, parsePropertyValue, plt, postUpdateComponent, promiseResolve, proxyComponent, proxyCustomElement, readTask, registerHost, registerInstance, renderVdom, setAssetPath, setErrorHandler, setMode, setNonce, setPlatformHelpers, setPlatformOptions, setValue, styles, supportsConstructableStylesheets, supportsListenerOptions, supportsShadow, win, writeTask };"], "mappings": ";;;;;;;;AAAA,IACI,OA2FA;AA5FJ;AAAA;AAAA;AACA,IAAI,QAAQ;AAAA,MACV,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,cAAc;AAAA,MACd,cAAc;AAAA,MACd,aAAa;AAAA,MACb,eAAe;AAAA,MACf,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,sBAAsB;AAAA,MACtB,SAAS;AAAA,MACT,OAAO;AAAA,MACP,aAAa;AAAA,MACb,WAAW;AAAA,MACX,cAAc;AAAA,MACd,0BAA0B;AAAA,MAC1B,4BAA4B;AAAA,MAC5B,wBAAwB;AAAA,MACxB,0BAA0B;AAAA,MAC1B,oBAAoB;AAAA,MACpB,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,MAAM;AAAA,MACN,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,KAAK;AAAA,MACL,WAAW;AAAA,MACX,eAAe;AAAA,MACf,WAAW;AAAA,MACX,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,SAAS;AAAA,MACT,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,UAAU;AAAA,MACV,eAAe;AAAA,MACf,WAAW;AAAA,MACX,sBAAsB;AAAA,MACtB,SAAS;AAAA,MACT,OAAO;AAAA,MACP,WAAW;AAAA,MACX,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,SAAS;AAAA,MACT,gBAAgB;AAAA;AAAA,MAEhB,oBAAoB;AAAA;AAAA,MAEpB,cAAc;AAAA,MACd,mBAAmB;AAAA,MACnB,eAAe;AAAA;AAAA,MAEf,gBAAgB;AAAA;AAAA,MAEhB,0BAA0B;AAAA;AAAA,MAE1B,eAAe;AAAA;AAAA,MAEf,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,oBAAoB;AAAA,MACpB,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB,cAAc;AAAA;AAAA,MAEd,uBAAuB;AAAA,IACzB;AAEA,IAAI;AAAA,IACJ;AAAA;AAAA;;;;;;;;;;;AC0JA,SAAS,yBAAyB,MAAM;AACtC,MAAI,IAAI,IAAI;AACZ,UAAQ,MAAM,MAAM,KAAK,KAAK,SAAS,OAAO,SAAS,GAAG,cAAc,wBAAwB,MAAM,OAAO,SAAS,GAAG,aAAa,SAAS,MAAM,OAAO,KAAK;AACnK;AAqBA,SAAS,IAAI,QAAQ,IAAI;AACvB,MAAI,OAAO,MAAM;AACf,UAAM,MAAM,GAAG,OAAO,KAAK;AAC3B,QAAI,eAAe,SAAS;AAC1B,aAAO,IAAI,KAAK,YAAU,GAAG,MAAM,CAAC;AAAA,IACtC,OAAO;AACL,aAAO,GAAG,GAAG;AAAA,IACf;AAAA,EACF;AACA,MAAI,OAAO,OAAO;AAChB,UAAM,QAAQ,OAAO;AACrB,WAAO,IAAI,KAAK;AAAA,EAClB;AACA,QAAM;AACR;AA8rBA,SAAS,gBAAgB,WAAW;AAClC,SAAO,UAAU,SAAS,KAAK;AAAA;AAAA,IAE/B,CAAC,GAAG,UAAU,OAAO,UAAQ,SAAS,KAAK,GAAG,KAAK;AAAA;AAAA;AAAA,IAEnD;AAAA;AACF;AAj+BA,IAGI,WACA,UASA,OASA,UACA,YAEA,cAoBA,mBAOA,aACA,cACA,kBACA,iBACA,gBAKA,YAEA,YA2BA,QACA,qBAMA,gBACA,iBACA,cACA,cACA,YACA,mBACA,kBAEA,aACA,UACA,0CAIA,KACA,KAGA,GACA,KAYA,gBACA,yBAWA,gBACA,kCASA,iBACA,cACA,eACA,gBACA,mBACA,WAWA,SAUA,gBAgBA,OAyBA,UACA,UACA,WAMA,cAUA,WACA,QACA,SAGA,OACA,eAYA,gBAQA,IAKA,KAoBA,QAOA,WAgBA,GACA,YAWA,YA0EA,GAoEA,UAmBA,MACA,QACA,aAIA,iBAQA,kBAoBA,yBAgBA,yBAsCA,eAsHA,2BAyBA,aACA,SACA,SAUA,oBAwBA,YAGA,aAgBA,WAQA,mBACA,eAcA,UAuDA,cAeA,YAUA,aA+FA,qBACA,gBACA,sBACA,qBAGA,eAwBA,SACA,YACA,aACA,oBACA,6BACA,mBACA,WACA,WAoFA,oBAgBA,2BA0BA,WAgBA,cAoBA,gBAqFA,aAqBA,eAGA,qBACA,OA0CA,8BA2BA,eACA,8BAgDA,qBAeA,kBAMA,cAOA,cAOA,uBAcA,YAwHA,wBACA,2BAGA,kBAKA,gBAYA,eAiCA,SAIA,YACA,iBAuDA,cACA,YAqCA,qBAiEA,aAWA,YAgBA,UAUA,oBAWA,iBAIA,qBAcA,UACA,UA+CA,gBAgHA,qBAgFA,uBAOA,mBA8DA,qBAQA,oBAQA,sBAqBA,sBAYA,gBA4BA,sBAeA,sBAkBA,kBAyBA,iBAUA,6BAoBA,6BAKA,gCAgBA,kBA4EA,qBAoCA,sBAUA,aACA,iBAeA,uBAYA,oBAqSA,uBAkBA,mBAgBA,uBAOA;AAxwFJ;AAAA;AAAA;AAYA;AASA;AA2BA;AAGA;AA2CA;AAgBA;AA0HA;AAsEA;AAGA;AAGA;AAsFA;AA4TA;AAQA;AAGA;AAGA;AAkBA;AAGA;AAGA;AA2BA;AAyFA;AAGA;AAGA;AA2wCA;AA6BA;AAglBA;AA7oFE;AAAA;AAAA;AAAA;AAAA;AA9EF,IAAI,YAAY,OAAO;AACvB,IAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ,IAAK,WAAU,QAAQ,MAAM;AAAA,QAC5C,KAAK,IAAI,IAAI;AAAA,QACb,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AAIA,IAAI,QAAQ;AAAA,MACV,OAAO,MAAM,QAAQ,OAAO;AAAA,MAC5B,WAAW;AAAA,MACX,UAAU;AAAA,MACV,WAAW,MAAM,YAAY,OAAO;AAAA,IACtC;AAIA,IAAI,WAAW,MAAO,uBAAuB,OAAO,yBAAyB,OAAO,uBAAsC,oBAAI,QAAQ,KAAoB,oBAAI,QAAQ;AACtK,IAAI,aAAa,SAAO,SAAS,IAAI,GAAG;AAExC,IAAI,eAAe,CAAC,aAAa,YAAY;AAC3C,YAAM,UAAU;AAAA,QACd,SAAS;AAAA,QACT,eAAe;AAAA,QACf,WAAW;AAAA,QACX,kBAAiC,oBAAI,IAAI;AAAA,MAC3C;AACA,UAAI,MAAO,OAAO;AAChB,gBAAQ,gBAAgB;AAAA,MAC1B;AACA,UAAI,MAAO,UAAU,MAAO,UAAU;AACpC,gBAAQ,sBAAsB,IAAI,QAAQ,OAAK,QAAQ,sBAAsB,CAAC;AAAA,MAChF;AACA,UAAI,MAAO,cAAc;AACvB,gBAAQ,mBAAmB,IAAI,QAAQ,OAAK,QAAQ,mBAAmB,CAAC;AACxE,oBAAY,KAAK,IAAI,CAAC;AACtB,oBAAY,MAAM,IAAI,CAAC;AAAA,MACzB;AACA,aAAO,SAAS,IAAI,aAAa,OAAO;AAAA,IAC1C;AACA,IAAI,oBAAoB,CAAC,KAAK,eAAe,cAAc;AAQ3D,IAAI,eAAe,CAAC,GAAG,QAAQ,eAAe,QAAQ,OAAO,GAAG,EAAE;AAClE,IAAI,mBAAmB,MAAO,YAAY,CAAC,UAAU,IAAI,CAAC,aAAa,wGAAwG;AAC/K,IAAI,kBAAkB,IAAI,MAAM,QAAQ,MAAM,GAAG,kBAAkB,GAAG,CAAC;AACvE,IAAI,iBAAiB,IAAI,MAAM,QAAQ,KAAK,GAAG,kBAAkB,GAAG,CAAC;AAKrE,IAAI,aAA4B,oBAAI,IAAI;AAExC,IAAI,aAAa,CAAC,SAAS,SAAS,iBAAiB;AACnD,YAAM,aAAa,QAAQ,UAAU,QAAQ,MAAM,GAAG;AACtD,YAAM,WAAW,QAAQ;AACzB,UAAI,MAAO,SAAS,OAAO,aAAa,UAAU;AAChD,wBAAgB,oCAAoC,QAAQ,SAAS,sBAAsB,QAAQ,UAAU,2BAA2B;AACxI,eAAO;AAAA,MACT,WAAW,CAAC,UAAU;AACpB,eAAO;AAAA,MACT;AACA,YAAM,SAAS,CAAC,MAAO,uBAAuB,WAAW,IAAI,QAAQ,IAAI;AACzE,UAAI,QAAQ;AACV,eAAO,OAAO,UAAU;AAAA,MAC1B;AAEA,aAIA,yBAAK,QAAQ,YAAY,MAAO,wBAAwB,eAAe,YAAY,eAAe,EAAE,IAAI,KAAK,oBAAkB;AAC7H,YAAI,CAAC,MAAO,sBAAsB;AAChC,qBAAW,IAAI,UAAU,cAAc;AAAA,QACzC;AACA,eAAO,eAAe,UAAU;AAAA,MAClC,GAAG,YAAY;AAAA,IACjB;AAGA,IAAI,SAAwB,oBAAI,IAAI;AACpC,IAAI,sBAAsB,CAAC;AAM3B,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAI,aAAa;AACjB,IAAI,oBAAoB;AACxB,IAAI,mBAAmB;AAEvB,IAAI,cAAc;AAClB,IAAI,WAAW;AACf,IAAI,2CAA2C,CAAC,0BAA0B,qBAAqB,wBAAwB,0BAA0B;AAIjJ,IAAI,MAAM,OAAO,WAAW,cAAc,SAAS,CAAC;AACpD,IAAI,MAAM,IAAI,YAAY;AAAA,MACxB,MAAM,CAAC;AAAA,IACT;AACA,IAAI,IAAI,IAAI,eAAe,MAAM;AAAA,IAAC;AAClC,IAAI,MAAM;AAAA,MACR,SAAS;AAAA,MACT,gBAAgB;AAAA,MAChB,KAAK,QAAM,GAAG;AAAA,MACd,KAAK,QAAM,sBAAsB,EAAE;AAAA,MACnC,KAAK,CAAC,IAAI,WAAW,UAAU,SAAS,GAAG,iBAAiB,WAAW,UAAU,IAAI;AAAA,MACrF,KAAK,CAAC,IAAI,WAAW,UAAU,SAAS,GAAG,oBAAoB,WAAW,UAAU,IAAI;AAAA,MACxF,IAAI,CAAC,WAAW,SAAS,IAAI,YAAY,WAAW,IAAI;AAAA,IAC1D;AAIA,IAAI,iBAAiB,MAAO;AAC5B,IAAI,0BAA0C,uBAAM;AAClD,UAAI,2BAA2B;AAC/B,UAAI;AACF,YAAI,iBAAiB,KAAK,MAAM,OAAO,eAAe,CAAC,GAAG,WAAW;AAAA,UACnE,MAAM;AACJ,uCAA2B;AAAA,UAC7B;AAAA,QACF,CAAC,CAAC;AAAA,MACJ,SAAS,GAAG;AAAA,MAAC;AACb,aAAO;AAAA,IACT,GAAG;AACH,IAAI,iBAAiB,OAAK,QAAQ,QAAQ,CAAC;AAC3C,IAAI,mCAAmC,MAAO,mBAAmC,uBAAM;AACrF,UAAI;AACF,YAAI,cAAc;AAClB,eAAO,OAAO,IAAI,cAAc,EAAE,gBAAgB;AAAA,MACpD,SAAS,GAAG;AAAA,MAAC;AACb,aAAO;AAAA,IACT,GAAG,IAAI;AAGP,IAAI,kBAAkB;AACtB,IAAI,eAAe;AACnB,IAAI,gBAAgB,CAAC;AACrB,IAAI,iBAAiB,CAAC;AACtB,IAAI,oBAAoB,CAAC;AACzB,IAAI,YAAY,CAAC,OAAO,UAAU,QAAM;AACtC,YAAM,KAAK,EAAE;AACb,UAAI,CAAC,cAAc;AACjB,uBAAe;AACf,YAAI,SAAS,IAAI,UAAU,GAAmB;AAC5C,mBAAS,KAAK;AAAA,QAChB,OAAO;AACL,cAAI,IAAI,KAAK;AAAA,QACf;AAAA,MACF;AAAA,IACF;AACA,IAAI,UAAU,WAAS;AACrB,eAAS,KAAK,GAAG,KAAK,MAAM,QAAQ,MAAM;AACxC,YAAI;AACF,gBAAM,EAAE,EAAE,YAAY,IAAI,CAAC;AAAA,QAC7B,SAAS,GAAG;AACV,uBAAa,CAAC;AAAA,QAChB;AAAA,MACF;AACA,YAAM,SAAS;AAAA,IACjB;AACA,IAAI,iBAAiB,CAAC,OAAO,YAAY;AACvC,UAAI,KAAK;AACT,UAAI,KAAK;AACT,aAAO,KAAK,MAAM,WAAW,KAAK,YAAY,IAAI,KAAK,SAAS;AAC9D,YAAI;AACF,gBAAM,IAAI,EAAE,EAAE;AAAA,QAChB,SAAS,GAAG;AACV,uBAAa,CAAC;AAAA,QAChB;AAAA,MACF;AACA,UAAI,OAAO,MAAM,QAAQ;AACvB,cAAM,SAAS;AAAA,MACjB,WAAW,OAAO,GAAG;AACnB,cAAM,OAAO,GAAG,EAAE;AAAA,MACpB;AAAA,IACF;AACA,IAAI,QAAQ,MAAM;AAChB,UAAI,MAAO,YAAY;AACrB;AAAA,MACF;AACA,cAAQ,aAAa;AACrB,UAAI,MAAO,YAAY;AACrB,cAAM,WAAW,IAAI,UAAU,OAAuB,IAAoB,YAAY,IAAI,IAAI,KAAK,KAAK,KAAK,mBAAmB,IAAI,GAAG,IAAI;AAC3I,uBAAe,gBAAgB,OAAO;AACtC,uBAAe,mBAAmB,OAAO;AACzC,YAAI,eAAe,SAAS,GAAG;AAC7B,4BAAkB,KAAK,GAAG,cAAc;AACxC,yBAAe,SAAS;AAAA,QAC1B;AACA,YAAI,eAAe,cAAc,SAAS,eAAe,SAAS,kBAAkB,SAAS,GAAG;AAC9F,cAAI,IAAI,KAAK;AAAA,QACf,OAAO;AACL,4BAAkB;AAAA,QACpB;AAAA,MACF,OAAO;AACL,gBAAQ,cAAc;AACtB,YAAI,eAAe,cAAc,SAAS,GAAG;AAC3C,cAAI,IAAI,KAAK;AAAA,QACf;AAAA,MACF;AAAA,IACF;AACA,IAAI,WAAW,QAAM,eAAe,EAAE,KAAK,EAAE;AAC7C,IAAI,WAA0B,0BAAU,eAAe,KAAK;AAC5D,IAAI,YAA2B,0BAAU,gBAAgB,IAAI;AAM7D,IAAI,eAAe,UAAQ;AACzB,YAAM,WAAW,IAAI,IAAI,MAAM,IAAI,cAAc;AACjD,aAAO,SAAS,WAAW,IAAI,SAAS,SAAS,SAAS,OAAO,SAAS;AAAA,IAC5E;AAOA,IAAI,YAAY,CAAC;AACjB,IAAI,SAAS;AACb,IAAI,UAAU;AAGd,IAAI,QAAQ,OAAK,KAAK;AACtB,IAAI,gBAAgB,OAAK;AACvB,UAAI,OAAO;AACX,aAAO,MAAM,YAAY,MAAM;AAAA,IACjC;AASA,IAAI,iBAAiB,CAAC;AACtB,aAAS,gBAAgB;AAAA,MACvB,KAAK,MAAM;AAAA,MACX,KAAK,MAAM;AAAA,MACX,IAAI,MAAM;AAAA,MACV,QAAQ,MAAM;AAAA,MACd,WAAW,MAAM;AAAA,IACnB,CAAC;AACD,IAAI,KAAK,YAAU;AAAA,MACjB,MAAM;AAAA,MACN,OAAO;AAAA,MACP;AAAA,IACF;AACA,IAAI,MAAM,YAAU;AAAA,MAClB,MAAM;AAAA,MACN,OAAO;AAAA,MACP;AAAA,IACF;AAgBA,IAAI,SAAS,YAAU;AACrB,UAAI,OAAO,MAAM;AACf,eAAO,OAAO;AAAA,MAChB,OAAO;AACL,cAAM,OAAO;AAAA,MACf;AAAA,IACF;AACA,IAAI,YAAY,YAAU;AACxB,UAAI,OAAO,OAAO;AAChB,eAAO,OAAO;AAAA,MAChB,OAAO;AACL,cAAM,OAAO;AAAA,MACf;AAAA,IACF;AAUA,IAAI,IAAI;AACR,IAAI,aAAa,CAAC,QAAQ,UAAU,OAAO;AACzC,UAAI,MAAO,WAAW,YAAY,MAAM;AACtC,cAAM,MAAM,MAAM,MAAM,IAAI,OAAO,IAAI,GAAG;AAC1C,oBAAY,KAAK,GAAG;AACpB,eAAO,MAAM,YAAY,QAAQ,aAAa,MAAM,OAAO,OAAO,KAAK,GAAG;AAAA,MAC5E,OAAO;AACL,eAAO,MAAM;AACX;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,IAAI,aAAa,CAAC,KAAK,gBAAgB;AACrC,UAAI,MAAO,WAAW,YAAY,MAAM;AACtC,YAAI,YAAY,iBAAiB,KAAK,MAAM,EAAE,WAAW,GAAG;AAC1D,sBAAY,KAAK,GAAG;AAAA,QACtB;AACA,eAAO,MAAM;AACX,cAAI,YAAY,iBAAiB,aAAa,SAAS,EAAE,WAAW,GAAG;AACrE,wBAAY,QAAQ,aAAa,GAAG;AAAA,UACtC;AAAA,QACF;AAAA,MACF,OAAO;AACL,eAAO,MAAM;AACX;AAAA,QACF;AAAA,MACF;AAAA,IACF;AA2DA,IAAI,IAAI,CAAC,UAAU,cAAc,aAAa;AAC5C,UAAI,QAAQ;AACZ,UAAI,MAAM;AACV,UAAI,WAAW;AACf,UAAI,SAAS;AACb,UAAI,aAAa;AACjB,YAAM,gBAAgB,CAAC;AACvB,YAAM,OAAO,OAAK;AAChB,iBAAS,KAAK,GAAG,KAAK,EAAE,QAAQ,MAAM;AACpC,kBAAQ,EAAE,EAAE;AACZ,cAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,iBAAK,KAAK;AAAA,UACZ,WAAW,SAAS,QAAQ,OAAO,UAAU,WAAW;AACtD,gBAAI,SAAS,OAAO,aAAa,cAAc,CAAC,cAAc,KAAK,GAAG;AACpE,sBAAQ,OAAO,KAAK;AAAA,YACtB,WAAW,MAAO,SAAS,OAAO,aAAa,cAAc,MAAM,YAAY,QAAQ;AACrF,8BAAgB;AAAA;AAAA,gFAEsD;AAAA,YACxE;AACA,gBAAI,UAAU,YAAY;AACxB,4BAAc,cAAc,SAAS,CAAC,EAAE,UAAU;AAAA,YACpD,OAAO;AACL,4BAAc,KAAK,SAAS,SAAS,MAAM,KAAK,IAAI,KAAK;AAAA,YAC3D;AACA,yBAAa;AAAA,UACf;AAAA,QACF;AAAA,MACF;AACA,WAAK,QAAQ;AACb,UAAI,WAAW;AACb,YAAI,MAAO,SAAS,aAAa,SAAS;AACxC,kCAAwB,SAAS;AAAA,QACnC;AACA,YAAI,MAAO,WAAW,UAAU,KAAK;AACnC,gBAAM,UAAU;AAAA,QAClB;AACA,YAAI,MAAO,kBAAkB,UAAU,MAAM;AAC3C,qBAAW,UAAU;AAAA,QACvB;AACA,YAAI,MAAO,WAAW;AACpB,gBAAM,YAAY,UAAU,aAAa,UAAU;AACnD,cAAI,WAAW;AACb,sBAAU,QAAQ,OAAO,cAAc,WAAW,YAAY,OAAO,KAAK,SAAS,EAAE,OAAO,OAAK,UAAU,CAAC,CAAC,EAAE,KAAK,GAAG;AAAA,UACzH;AAAA,QACF;AAAA,MACF;AACA,UAAI,MAAO,SAAS,cAAc,KAAK,MAAM,GAAG;AAC9C,wBAAgB;AAAA;AAAA,oFAEgE;AAAA,MAClF;AACA,UAAI,MAAO,kBAAkB,OAAO,aAAa,YAAY;AAC3D,eAAO,SAAS,cAAc,OAAO,CAAC,IAAI,WAAW,eAAe,WAAW;AAAA,MACjF;AACA,YAAM,QAAQ,SAAS,UAAU,IAAI;AACrC,YAAM,UAAU;AAChB,UAAI,cAAc,SAAS,GAAG;AAC5B,cAAM,aAAa;AAAA,MACrB;AACA,UAAI,MAAO,SAAS;AAClB,cAAM,QAAQ;AAAA,MAChB;AACA,UAAI,MAAO,gBAAgB;AACzB,cAAM,SAAS;AAAA,MACjB;AACA,aAAO;AAAA,IACT;AACA,IAAI,WAAW,CAAC,KAAK,SAAS;AAC5B,YAAM,QAAQ;AAAA,QACZ,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,YAAY;AAAA,MACd;AACA,UAAI,MAAO,eAAe;AACxB,cAAM,UAAU;AAAA,MAClB;AACA,UAAI,MAAO,SAAS;AAClB,cAAM,QAAQ;AAAA,MAChB;AACA,UAAI,MAAO,gBAAgB;AACzB,cAAM,SAAS;AAAA,MACjB;AACA,aAAO;AAAA,IACT;AACA,IAAI,OAAO,CAAC;AACZ,IAAI,SAAS,UAAQ,QAAQ,KAAK,UAAU;AAC5C,IAAI,cAAc;AAAA,MAChB,SAAS,CAAC,UAAU,OAAO,SAAS,IAAI,eAAe,EAAE,QAAQ,EAAE;AAAA,MACnE,KAAK,CAAC,UAAU,OAAO,SAAS,IAAI,eAAe,EAAE,IAAI,EAAE,EAAE,IAAI,gBAAgB;AAAA,IACnF;AACA,IAAI,kBAAkB,WAAS;AAAA,MAC7B,QAAQ,KAAK;AAAA,MACb,WAAW,KAAK;AAAA,MAChB,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,MACZ,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,IACd;AACA,IAAI,mBAAmB,UAAQ;AAC7B,UAAI,OAAO,KAAK,SAAS,YAAY;AACnC,cAAM,YAAY,mBACb,KAAK;AAEV,YAAI,KAAK,MAAM;AACb,oBAAU,MAAM,KAAK;AAAA,QACvB;AACA,YAAI,KAAK,OAAO;AACd,oBAAU,OAAO,KAAK;AAAA,QACxB;AACA,eAAO,EAAE,KAAK,MAAM,WAAW,GAAI,KAAK,aAAa,CAAC,CAAE;AAAA,MAC1D;AACA,YAAM,QAAQ,SAAS,KAAK,MAAM,KAAK,KAAK;AAC5C,YAAM,UAAU,KAAK;AACrB,YAAM,aAAa,KAAK;AACxB,YAAM,QAAQ,KAAK;AACnB,YAAM,SAAS,KAAK;AACpB,aAAO;AAAA,IACT;AACA,IAAI,0BAA0B,cAAY;AACxC,YAAM,QAAQ,OAAO,KAAK,QAAQ;AAClC,YAAM,QAAQ,MAAM,QAAQ,OAAO;AACnC,UAAI,UAAU,IAAI;AAChB;AAAA,MACF;AACA,YAAM,YAAY,MAAM,QAAQ,MAAM;AACtC,YAAM,WAAW,MAAM,QAAQ,KAAK;AACpC,YAAM,WAAW,MAAM,QAAQ,KAAK;AACpC,YAAM,YAAY,MAAM,QAAQ,MAAM;AACtC,UAAI,QAAQ,aAAa,QAAQ,YAAY,QAAQ,YAAY,QAAQ,WAAW;AAClF,uBAAe,iFAAiF;AAAA,MAClG;AAAA,IACF;AAGA,IAAI,0BAA0B,CAAC,SAAS,SAAS,QAAQ,YAAY;AACnE,YAAM,aAAa,WAAW,iBAAiB,OAAO;AACtD,YAAM,aAAa,QAAQ;AAC3B,YAAM,mBAAmB,CAAC;AAC1B,YAAM,YAAY,CAAC;AACnB,YAAM,kBAAkB,MAAO,aAAa,aAAa,CAAC,IAAI;AAC9D,YAAM,QAAQ,QAAQ,UAAU,SAAS,SAAS,IAAI;AACtD,UAAI,CAAC,IAAI,eAAe;AACtB,kCAA0B,IAAI,MAAM,IAAI,gBAA+B,oBAAI,IAAI,CAAC;AAAA,MAClF;AACA,cAAQ,UAAU,IAAI;AACtB,cAAQ,gBAAgB,UAAU;AAClC,oBAAc,OAAO,kBAAkB,WAAW,iBAAiB,SAAS,SAAS,MAAM;AAC3F,uBAAiB,IAAI,OAAK;AACxB,cAAM,gBAAgB,EAAE,WAAW,MAAM,EAAE;AAC3C,cAAM,kBAAkB,IAAI,cAAc,IAAI,aAAa;AAC3D,cAAM,OAAO,EAAE;AACf,YAAI,mBAAmB,kBAAkB,gBAAgB,MAAM,MAAM,IAAI;AACvE,0BAAgB,WAAW,aAAa,MAAM,gBAAgB,WAAW;AAAA,QAC3E;AACA,YAAI,CAAC,YAAY;AACf,eAAK,MAAM,IAAI;AACf,cAAI,iBAAiB;AACnB,iBAAK,MAAM,IAAI;AACf,iBAAK,MAAM,EAAE,MAAM,IAAI;AAAA,UACzB;AAAA,QACF;AACA,YAAI,cAAc,OAAO,aAAa;AAAA,MACxC,CAAC;AACD,UAAI,MAAO,aAAa,YAAY;AAClC,wBAAgB,IAAI,oBAAkB;AACpC,cAAI,gBAAgB;AAClB,uBAAW,YAAY,cAAc;AAAA,UACvC;AAAA,QACF,CAAC;AAAA,MACH;AACA,iBAAW;AAAA,IACb;AACA,IAAI,gBAAgB,CAAC,aAAa,kBAAkB,WAAW,iBAAiB,SAAS,MAAM,WAAW;AACxG,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,KAAK,aAAa,GAAqB;AACzC,wBAAgB,KAAK,aAAa,gBAAgB;AAClD,YAAI,eAAe;AACjB,wBAAc,cAAc,MAAM,GAAG;AACrC,cAAI,YAAY,CAAC,MAAM,UAAU,YAAY,CAAC,MAAM,KAAK;AACvD,yBAAa;AAAA,cACX,SAAS;AAAA,cACT,UAAU,YAAY,CAAC;AAAA,cACvB,UAAU,YAAY,CAAC;AAAA,cACvB,SAAS,YAAY,CAAC;AAAA,cACtB,SAAS,YAAY,CAAC;AAAA,cACtB,OAAO,KAAK,QAAQ,YAAY;AAAA,cAChC,OAAO;AAAA,cACP,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,QAAQ;AAAA,YACV;AACA,6BAAiB,KAAK,UAAU;AAChC,iBAAK,gBAAgB,gBAAgB;AACrC,gBAAI,CAAC,YAAY,YAAY;AAC3B,0BAAY,aAAa,CAAC;AAAA,YAC5B;AACA,wBAAY,WAAW,WAAW,OAAO,IAAI;AAC7C,0BAAc;AACd,gBAAI,mBAAmB,WAAW,YAAY,KAAK;AACjD,8BAAgB,WAAW,OAAO,IAAI,WAAW;AAAA,YACnD;AAAA,UACF;AAAA,QACF;AACA,YAAI,KAAK,YAAY;AACnB,eAAK,KAAK,KAAK,WAAW,WAAW,SAAS,GAAG,MAAM,GAAG,MAAM;AAC9D,0BAAc,aAAa,kBAAkB,WAAW,iBAAiB,SAAS,KAAK,WAAW,WAAW,EAAE,GAAG,MAAM;AAAA,UAC1H;AAAA,QACF;AACA,aAAK,KAAK,KAAK,WAAW,SAAS,GAAG,MAAM,GAAG,MAAM;AACnD,wBAAc,aAAa,kBAAkB,WAAW,iBAAiB,SAAS,KAAK,WAAW,EAAE,GAAG,MAAM;AAAA,QAC/G;AAAA,MACF,WAAW,KAAK,aAAa,GAAqB;AAChD,sBAAc,KAAK,UAAU,MAAM,GAAG;AACtC,YAAI,YAAY,CAAC,MAAM,UAAU,YAAY,CAAC,MAAM,KAAK;AACvD,0BAAgB,YAAY,CAAC;AAC7B,uBAAa;AAAA,YACX,SAAS;AAAA,YACT,UAAU,YAAY,CAAC;AAAA,YACvB,UAAU,YAAY,CAAC;AAAA,YACvB,SAAS,YAAY,CAAC;AAAA,YACtB,SAAS,YAAY,CAAC;AAAA,YACtB,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,QAAQ;AAAA,UACV;AACA,cAAI,kBAAkB,cAAc;AAClC,uBAAW,QAAQ,KAAK;AACxB,gBAAI,WAAW,SAAS,WAAW,MAAM,aAAa,GAAkB;AACtE,yBAAW,SAAS,WAAW,MAAM;AACrC,+BAAiB,KAAK,UAAU;AAChC,mBAAK,OAAO;AACZ,kBAAI,CAAC,YAAY,YAAY;AAC3B,4BAAY,aAAa,CAAC;AAAA,cAC5B;AACA,0BAAY,WAAW,WAAW,OAAO,IAAI;AAC7C,kBAAI,mBAAmB,WAAW,YAAY,KAAK;AACjD,gCAAgB,WAAW,OAAO,IAAI,WAAW;AAAA,cACnD;AAAA,YACF;AAAA,UACF,WAAW,WAAW,aAAa,QAAQ;AACzC,gBAAI,kBAAkB,cAAc;AAClC,yBAAW,QAAQ;AACnB,kBAAI,YAAY,CAAC,GAAG;AAClB,qBAAK,MAAM,IAAI,WAAW,SAAS,YAAY,CAAC;AAAA,cAClD,OAAO;AACL,qBAAK,MAAM,IAAI;AAAA,cACjB;AACA,mBAAK,MAAM,IAAI;AACf,kBAAI,MAAO,aAAa,iBAAiB;AACvC,2BAAW,QAAQ,IAAI,cAAc,WAAW,KAAK;AACrD,oBAAI,WAAW,QAAQ;AACrB,6BAAW,MAAM,aAAa,QAAQ,WAAW,MAAM;AAAA,gBACzD;AACA,qBAAK,WAAW,aAAa,WAAW,OAAO,IAAI;AACnD,qBAAK,OAAO;AACZ,oBAAI,WAAW,YAAY,KAAK;AAC9B,kCAAgB,WAAW,OAAO,IAAI,WAAW;AAAA,gBACnD;AAAA,cACF;AACA,wBAAU,KAAK,UAAU;AACzB,kBAAI,CAAC,YAAY,YAAY;AAC3B,4BAAY,aAAa,CAAC;AAAA,cAC5B;AACA,0BAAY,WAAW,WAAW,OAAO,IAAI;AAAA,YAC/C,WAAW,kBAAkB,gBAAgB;AAC3C,kBAAI,MAAO,aAAa,iBAAiB;AACvC,qBAAK,OAAO;AAAA,cACd,WAAW,MAAO,gBAAgB;AAChC,wBAAQ,MAAM,IAAI;AAClB,qBAAK,MAAM,IAAI;AAAA,cACjB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,WAAW,eAAe,YAAY,UAAU,SAAS;AACvD,cAAM,QAAQ,SAAS,MAAM,KAAK,WAAW;AAC7C,cAAM,QAAQ;AACd,cAAM,UAAU;AAChB,oBAAY,aAAa,CAAC,KAAK;AAAA,MACjC;AAAA,IACF;AACA,IAAI,4BAA4B,CAAC,MAAM,gBAAgB;AACrD,UAAI,KAAK,aAAa,GAAqB;AACzC,YAAI,KAAK;AACT,YAAI,KAAK,YAAY;AACnB,iBAAO,KAAK,KAAK,WAAW,WAAW,QAAQ,MAAM;AACnD,sCAA0B,KAAK,WAAW,WAAW,EAAE,GAAG,WAAW;AAAA,UACvE;AAAA,QACF;AACA,aAAK,KAAK,GAAG,KAAK,KAAK,WAAW,QAAQ,MAAM;AAC9C,oCAA0B,KAAK,WAAW,EAAE,GAAG,WAAW;AAAA,QAC5D;AAAA,MACF,WAAW,KAAK,aAAa,GAAqB;AAChD,cAAM,cAAc,KAAK,UAAU,MAAM,GAAG;AAC5C,YAAI,YAAY,CAAC,MAAM,iBAAiB;AACtC,sBAAY,IAAI,YAAY,CAAC,IAAI,MAAM,YAAY,CAAC,GAAG,IAAI;AAC3D,eAAK,YAAY;AACjB,eAAK,MAAM,IAAI,YAAY,CAAC;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAMA,IAAI,cAAc,SAAO,oBAAoB,IAAI,QAAM,GAAG,GAAG,CAAC,EAAE,KAAK,OAAK,CAAC,CAAC,CAAC;AAC7E,IAAI,UAAU,aAAW,oBAAoB,KAAK,OAAO;AACzD,IAAI,UAAU,SAAO,WAAW,GAAG,EAAE;AAUrC,IAAI,qBAAqB,CAAC,WAAW,aAAa;AAChD,UAAI,aAAa,QAAQ,CAAC,cAAc,SAAS,GAAG;AAClD,YAAI,MAAQ,eAAe,WAAW,GAAiB;AACrD,iBAAO,cAAc,UAAU,QAAQ,cAAc,MAAM,CAAC,CAAC;AAAA,QAC/D;AACA,YAAI,MAAQ,cAAc,WAAW,GAAgB;AACnD,iBAAO,WAAW,SAAS;AAAA,QAC7B;AACA,YAAI,MAAQ,cAAc,WAAW,GAAgB;AACnD,iBAAO,OAAO,SAAS;AAAA,QACzB;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAUA,IAAI,aAAa,SAAO,MAAQ,WAAW,WAAW,GAAG,EAAE,gBAAgB;AAG3E,IAAI,cAAc,CAAC,KAAK,MAAM,UAAU;AACtC,YAAM,MAAM,WAAW,GAAG;AAC1B,aAAO;AAAA,QACL,MAAM,YAAU;AACd,cAAI,MAAQ,SAAS,CAAC,IAAI,aAAa;AACrC,2BAAe,QAAQ,IAAI,iFAAiF;AAAA,UAC9G;AACA,iBAAO,UAAU,KAAK,MAAM;AAAA,YAC1B,SAAS,CAAC,EAAE,QAAQ;AAAA,YACpB,UAAU,CAAC,EAAE,QAAQ;AAAA,YACrB,YAAY,CAAC,EAAE,QAAQ;AAAA,YACvB;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AACA,IAAI,YAAY,CAAC,KAAK,MAAM,SAAS;AACnC,YAAM,KAAK,IAAI,GAAG,MAAM,IAAI;AAC5B,UAAI,cAAc,EAAE;AACpB,aAAO;AAAA,IACT;AAIA,IAAI,oBAAmC,oBAAI,QAAQ;AACnD,IAAI,gBAAgB,CAAC,UAAU,SAAS,YAAY;AAClD,UAAI,QAAQ,OAAO,IAAI,QAAQ;AAC/B,UAAI,oCAAoC,SAAS;AAC/C,gBAAQ,SAAS,IAAI,cAAc;AACnC,YAAI,OAAO,UAAU,UAAU;AAC7B,kBAAQ;AAAA,QACV,OAAO;AACL,gBAAM,YAAY,OAAO;AAAA,QAC3B;AAAA,MACF,OAAO;AACL,gBAAQ;AAAA,MACV;AACA,aAAO,IAAI,UAAU,KAAK;AAAA,IAC5B;AACA,IAAI,WAAW,CAAC,oBAAoB,SAAS,SAAS;AACpD,UAAI;AACJ,YAAM,WAAW,WAAW,SAAS,IAAI;AACzC,YAAM,QAAQ,OAAO,IAAI,QAAQ;AACjC,UAAI,CAAC,MAAQ,cAAc;AACzB,eAAO;AAAA,MACT;AACA,2BAAqB,mBAAmB,aAAa,KAA4B,qBAAqB;AACtG,UAAI,OAAO;AACT,YAAI,OAAO,UAAU,UAAU;AAC7B,+BAAqB,mBAAmB,QAAQ;AAChD,cAAI,gBAAgB,kBAAkB,IAAI,kBAAkB;AAC5D,cAAI;AACJ,cAAI,CAAC,eAAe;AAClB,8BAAkB,IAAI,oBAAoB,gBAA+B,oBAAI,IAAI,CAAC;AAAA,UACpF;AACA,cAAI,CAAC,cAAc,IAAI,QAAQ,GAAG;AAChC,gBAAI,MAAQ,qBAAqB,mBAAmB,SAAS,WAAW,mBAAmB,cAAc,IAAI,iBAAiB,KAAK,QAAQ,IAAI,IAAI;AACjJ,uBAAS,YAAY;AAAA,YACvB,OAAO;AACL,yBAAW,IAAI,cAAc,OAAO;AACpC,uBAAS,YAAY;AACrB,oBAAM,SAAS,KAAK,IAAI,YAAY,OAAO,KAAK,yBAAyB,GAAG;AAC5E,kBAAI,SAAS,MAAM;AACjB,yBAAS,aAAa,SAAS,KAAK;AAAA,cACtC;AACA,mBAAK,MAAQ,qBAAqB,MAAQ,yBAAyB,QAAQ,UAAU,GAAgC;AACnH,yBAAS,aAAa,mBAAmB,QAAQ;AAAA,cACnD;AACA,oBAAM;AAAA;AAAA;AAAA;AAAA,gBAIN,EAAE,QAAQ,UAAU;AAAA;AAAA;AAAA,gBAIpB,QAAQ,UAAU,KAAkC,mBAAmB,aAAa;AAAA;AACpF,kBAAI,aAAa;AACf,mCAAmB,aAAa,UAAU,mBAAmB,cAAc,MAAM,CAAC;AAAA,cACpF;AAAA,YACF;AACA,gBAAI,QAAQ,UAAU,GAA2B;AAC/C,uBAAS,aAAa;AAAA,YACxB;AACA,gBAAI,eAAe;AACjB,4BAAc,IAAI,QAAQ;AAAA,YAC5B;AAAA,UACF;AAAA,QACF,WAAW,MAAQ,oBAAoB,CAAC,mBAAmB,mBAAmB,SAAS,KAAK,GAAG;AAC7F,6BAAmB,qBAAqB,CAAC,GAAG,mBAAmB,oBAAoB,KAAK;AAAA,QAC1F;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,IAAI,eAAe,aAAW;AAC5B,YAAM,UAAU,QAAQ;AACxB,YAAM,MAAM,QAAQ;AACpB,YAAM,QAAQ,QAAQ;AACtB,YAAM,kBAAkB,WAAW,gBAAgB,QAAQ,SAAS;AACpE,YAAM,WAAW,SAAS,MAAQ,aAAa,kBAAkB,IAAI,aAAa,IAAI,aAAa,IAAI,YAAY,GAAG,SAAS,QAAQ,UAAU;AACjJ,WAAK,MAAQ,aAAa,MAAQ,WAAW,MAAQ,kBAAkB,QAAQ,MAAqC,QAAQ,GAAgC;AAC1J,YAAI,MAAM,IAAI;AACd,YAAI,UAAU,IAAI,WAAW,IAAI;AACjC,YAAI,MAAQ,UAAU,QAAQ,GAAgC;AAC5D,cAAI,UAAU,IAAI,WAAW,IAAI;AAAA,QACnC;AAAA,MACF;AACA,sBAAgB;AAAA,IAClB;AACA,IAAI,aAAa,CAAC,KAAK,SAAS,SAAS,MAAQ,QAAQ,QAAQ,IAAI,UAAU,KAAmB,IAAI,YAAY,MAAM,OAAO,IAAI;AAUnI,IAAI,cAAc,CAAC,KAAK,YAAY,UAAU,UAAU,OAAO,UAAU;AACvE,UAAI,aAAa,UAAU;AACzB,YAAI,SAAS,kBAAkB,KAAK,UAAU;AAC9C,YAAI,KAAK,WAAW,YAAY;AAChC,YAAI,MAAQ,aAAa,eAAe,SAAS;AAC/C,gBAAM,YAAY,IAAI;AACtB,gBAAM,aAAa,eAAe,QAAQ;AAC1C,gBAAM,aAAa,eAAe,QAAQ;AAC1C,oBAAU,OAAO,GAAG,WAAW,OAAO,OAAK,KAAK,CAAC,WAAW,SAAS,CAAC,CAAC,CAAC;AACxE,oBAAU,IAAI,GAAG,WAAW,OAAO,OAAK,KAAK,CAAC,WAAW,SAAS,CAAC,CAAC,CAAC;AAAA,QACvE,WAAW,MAAQ,aAAa,eAAe,SAAS;AACtD,cAAI,MAAQ,WAAW;AACrB,uBAAW,QAAQ,UAAU;AAC3B,kBAAI,CAAC,YAAY,SAAS,IAAI,KAAK,MAAM;AACvC,oBAAI,CAAC,MAAQ,qBAAqB,KAAK,SAAS,GAAG,GAAG;AACpD,sBAAI,MAAM,eAAe,IAAI;AAAA,gBAC/B,OAAO;AACL,sBAAI,MAAM,IAAI,IAAI;AAAA,gBACpB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,qBAAW,QAAQ,UAAU;AAC3B,gBAAI,CAAC,YAAY,SAAS,IAAI,MAAM,SAAS,IAAI,GAAG;AAClD,kBAAI,CAAC,MAAQ,qBAAqB,KAAK,SAAS,GAAG,GAAG;AACpD,oBAAI,MAAM,YAAY,MAAM,SAAS,IAAI,CAAC;AAAA,cAC5C,OAAO;AACL,oBAAI,MAAM,IAAI,IAAI,SAAS,IAAI;AAAA,cACjC;AAAA,YACF;AAAA,UACF;AAAA,QACF,WAAW,MAAQ,WAAW,eAAe,OAAO;AAAA,QAAC,WAAW,MAAQ,WAAW,eAAe,OAAO;AACvG,cAAI,UAAU;AACZ,qBAAS,GAAG;AAAA,UACd;AAAA,QACF,WAAW,MAAQ,iBAAiB,MAAQ,WAAW,CAAC,SAAS,CAAC,IAAI,iBAAiB,UAAU,MAAM,WAAW,CAAC,MAAM,OAAO,WAAW,CAAC,MAAM,KAAK;AACrJ,cAAI,WAAW,CAAC,MAAM,KAAK;AACzB,yBAAa,WAAW,MAAM,CAAC;AAAA,UACjC,WAAW,kBAAkB,KAAK,EAAE,GAAG;AACrC,yBAAa,GAAG,MAAM,CAAC;AAAA,UACzB,OAAO;AACL,yBAAa,GAAG,CAAC,IAAI,WAAW,MAAM,CAAC;AAAA,UACzC;AACA,cAAI,YAAY,UAAU;AACxB,kBAAM,UAAU,WAAW,SAAS,oBAAoB;AACxD,yBAAa,WAAW,QAAQ,qBAAqB,EAAE;AACvD,gBAAI,UAAU;AACZ,kBAAI,IAAI,KAAK,YAAY,UAAU,OAAO;AAAA,YAC5C;AACA,gBAAI,UAAU;AACZ,kBAAI,IAAI,KAAK,YAAY,UAAU,OAAO;AAAA,YAC5C;AAAA,UACF;AAAA,QACF,WAAW,MAAQ,gBAAgB;AACjC,gBAAM,YAAY,cAAc,QAAQ;AACxC,eAAK,UAAU,aAAa,aAAa,SAAS,CAAC,OAAO;AACxD,gBAAI;AACF,kBAAI,CAAC,IAAI,QAAQ,SAAS,GAAG,GAAG;AAC9B,sBAAM,IAAI,YAAY,OAAO,KAAK;AAClC,oBAAI,eAAe,QAAQ;AACzB,2BAAS;AAAA,gBACX,WAAW,YAAY,QAAQ,IAAI,UAAU,KAAK,GAAG;AACnD,sBAAI,UAAU,IAAI;AAAA,gBACpB;AAAA,cACF,OAAO;AACL,oBAAI,UAAU,IAAI;AAAA,cACpB;AAAA,YACF,SAAS,GAAG;AAAA,YAAC;AAAA,UACf;AACA,cAAI,QAAQ;AACZ,cAAI,MAAQ,WAAW;AACrB,gBAAI,QAAQ,KAAK,GAAG,QAAQ,aAAa,EAAE,IAAI;AAC7C,2BAAa;AACb,sBAAQ;AAAA,YACV;AAAA,UACF;AACA,cAAI,YAAY,QAAQ,aAAa,OAAO;AAC1C,gBAAI,aAAa,SAAS,IAAI,aAAa,UAAU,MAAM,IAAI;AAC7D,kBAAI,MAAQ,aAAa,OAAO;AAC9B,oBAAI,kBAAkB,UAAU,UAAU;AAAA,cAC5C,OAAO;AACL,oBAAI,gBAAgB,UAAU;AAAA,cAChC;AAAA,YACF;AAAA,UACF,YAAY,CAAC,UAAU,QAAQ,KAAkB,UAAU,CAAC,WAAW;AACrE,uBAAW,aAAa,OAAO,KAAK;AACpC,gBAAI,MAAQ,aAAa,OAAO;AAC9B,kBAAI,eAAe,UAAU,YAAY,QAAQ;AAAA,YACnD,OAAO;AACL,kBAAI,aAAa,YAAY,QAAQ;AAAA,YACvC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,IAAI,sBAAsB;AAC1B,IAAI,iBAAiB,WAAS,CAAC,QAAQ,CAAC,IAAI,MAAM,MAAM,mBAAmB;AAC3E,IAAI,uBAAuB;AAC3B,IAAI,sBAAsB,IAAI,OAAO,uBAAuB,GAAG;AAG/D,IAAI,gBAAgB,CAAC,UAAU,UAAU,eAAe;AACtD,YAAM,MAAM,SAAS,MAAM,aAAa,MAA6B,SAAS,MAAM,OAAO,SAAS,MAAM,OAAO,SAAS;AAC1H,YAAM,gBAAgB,YAAY,SAAS,WAAW;AACtD,YAAM,gBAAgB,SAAS,WAAW;AAC1C,UAAI,MAAQ,WAAW;AACrB,mBAAW,cAAc,gBAAgB,OAAO,KAAK,aAAa,CAAC,GAAG;AACpE,cAAI,EAAE,cAAc,gBAAgB;AAClC,wBAAY,KAAK,YAAY,cAAc,UAAU,GAAG,QAAQ,YAAY,SAAS,OAAO;AAAA,UAC9F;AAAA,QACF;AAAA,MACF;AACA,iBAAW,cAAc,gBAAgB,OAAO,KAAK,aAAa,CAAC,GAAG;AACpE,oBAAY,KAAK,YAAY,cAAc,UAAU,GAAG,cAAc,UAAU,GAAG,YAAY,SAAS,OAAO;AAAA,MACjH;AAAA,IACF;AAaA,IAAI,qBAAqB;AACzB,IAAI,8BAA8B;AAClC,IAAI,oBAAoB;AACxB,IAAI,YAAY;AAChB,IAAI,YAAY,CAAC,gBAAgB,gBAAgB,YAAY,cAAc;AACzE,UAAI;AACJ,YAAM,YAAY,eAAe,WAAW,UAAU;AACtD,UAAI,KAAK;AACT,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,MAAQ,kBAAkB,CAAC,oBAAoB;AACjD,4BAAoB;AACpB,YAAI,UAAU,UAAU,QAAQ;AAC9B,cAAI,SAAS;AACX,sBAAU,UAAU,IAAI,UAAU,IAAI;AAAA,UACxC;AACA,oBAAU,WAAW,UAAU;AAAA;AAAA;AAAA,YAG/B;AAAA;AAAA;AAAA;AAAA;AAAA,YAIA;AAAA;AAAA,QACF;AAAA,MACF;AACA,UAAI,MAAQ,SAAS,UAAU,OAAO;AACpC,wBAAgB,WAAW,UAAU,WAAW,OAAO,IAAI,UAAU,MAAM,WAAW,IAAI,UAAU,KAAK,WAAW,mTAAmT;AAAA,MACza;AACA,UAAI,MAAQ,YAAY,UAAU,WAAW,MAAM;AACjD,cAAM,UAAU,QAAQ,IAAI,eAAe,UAAU,MAAM;AAAA,MAC7D,WAAW,MAAQ,kBAAkB,UAAU,UAAU,GAAyB;AAChF,cAAM,UAAU,QAAQ,MAAQ,WAAW,MAAQ,oBAAoB,uBAAuB,SAAS,IAAI,IAAI,eAAe,EAAE;AAAA,MAClI,OAAO;AACL,YAAI,MAAQ,OAAO,CAAC,WAAW;AAC7B,sBAAY,UAAU,UAAU;AAAA,QAClC;AACA,cAAM,UAAU,QAAQ,MAAQ,MAAM,IAAI,gBAAgB,YAAY,SAAS,SAAS,CAAC,sBAAsB,MAAQ,kBAAkB,UAAU,UAAU,IAAyB,YAAY,UAAU,KAAK,IAAI,IAAI,cAAc,CAAC,sBAAsB,MAAQ,kBAAkB,UAAU,UAAU,IAAyB,YAAY,UAAU,KAAK;AAChW,YAAI,MAAQ,OAAO,aAAa,UAAU,UAAU,iBAAiB;AACnE,sBAAY;AAAA,QACd;AACA,YAAI,MAAQ,eAAe;AACzB,wBAAc,MAAM,WAAW,SAAS;AAAA,QAC1C;AACA,cAAM,WAAW,IAAI,YAAY;AACjC,cAAM,4BAA4B,CAAC,SAAS,cAAc,MAAM;AAChE,YAAI,CAAC,6BAA6B,MAAQ,UAAU,MAAM,OAAO,KAAK,IAAI,MAAM,MAAM,SAAS;AAC7F,cAAI,UAAU,IAAI,IAAI,MAAM,IAAI,OAAO;AAAA,QACzC;AACA,YAAI,MAAQ,QAAQ;AAClB,gCAAsB,KAAK,SAAS;AAAA,QACtC;AACA,YAAI,UAAU,YAAY;AACxB,eAAK,KAAK,GAAG,KAAK,UAAU,WAAW,QAAQ,EAAE,IAAI;AACnD,wBAAY,UAAU,gBAAgB,WAAW,IAAI,GAAG;AACxD,gBAAI,WAAW;AACb,kBAAI,YAAY,SAAS;AAAA,YAC3B;AAAA,UACF;AAAA,QACF;AACA,YAAI,MAAQ,KAAK;AACf,cAAI,UAAU,UAAU,OAAO;AAC7B,wBAAY;AAAA,UACd,WAAW,IAAI,YAAY,iBAAiB;AAC1C,wBAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AACA,UAAI,MAAM,IAAI;AACd,UAAI,MAAQ,gBAAgB;AAC1B,YAAI,UAAU,WAAW,IAAyB,IAA0B;AAC1E,cAAI,MAAM,IAAI;AACd,cAAI,MAAM,IAAI;AACd,cAAI,MAAM,IAAI,UAAU,UAAU;AAClC,cAAI,MAAM,KAAK,KAAK,UAAU,YAAY,OAAO,SAAS,GAAG;AAC7D,qBAAW,kBAAkB,eAAe,cAAc,eAAe,WAAW,UAAU;AAC9F,cAAI,YAAY,SAAS,UAAU,UAAU,SAAS,eAAe,OAAO;AAC1E,gBAAI,MAAQ,uBAAuB;AACjC,iCAAmB,eAAe,KAAK;AAAA,YACzC,OAAO;AACL,wCAA0B,eAAe,OAAO,KAAK;AAAA,YACvD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,IAAI,qBAAqB,eAAa;AACpC,UAAI,WAAW;AACf,YAAM,OAAO,UAAU,QAAQ,YAAY,YAAY,CAAC;AACxD,UAAI,QAAQ,MAAM;AAChB,cAAM,iBAAiB,MAAM,KAAK,KAAK,UAAU,EAAE,KAAK,SAAO,IAAI,MAAM,CAAC;AAC1E,cAAM,iBAAiB,MAAM,KAAK,UAAU,UAAU;AACtD,mBAAW,aAAa,iBAAiB,eAAe,QAAQ,IAAI,gBAAgB;AAClF,cAAI,UAAU,MAAM,KAAK,MAAM;AAC7B,yBAAa,MAAM,WAAW,kBAAkB,OAAO,iBAAiB,IAAI;AAC5E,sBAAU,MAAM,IAAI;AACpB,gCAAoB;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AACA,UAAI,WAAW,CAAC;AAAA,IAClB;AACA,IAAI,4BAA4B,CAAC,WAAW,cAAc;AACxD,UAAI,WAAW;AACf,YAAM,oBAAoB,MAAM,KAAK,UAAU,UAAU;AACzD,UAAI,UAAU,MAAM,KAAK,MAAQ,uBAAuB;AACtD,YAAI,OAAO;AACX,eAAO,OAAO,KAAK,aAAa;AAC9B,cAAI,QAAQ,KAAK,MAAM,MAAM,UAAU,MAAM,KAAK,KAAK,MAAM,MAAM,aAAa;AAC9E,8BAAkB,KAAK,IAAI;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AACA,eAAS,KAAK,kBAAkB,SAAS,GAAG,MAAM,GAAG,MAAM;AACzD,cAAM,YAAY,kBAAkB,EAAE;AACtC,YAAI,UAAU,MAAM,MAAM,eAAe,UAAU,MAAM,GAAG;AAC1D,uBAAa,oBAAoB,SAAS,GAAG,WAAW,cAAc,SAAS,CAAC;AAChF,oBAAU,MAAM,EAAE,OAAO;AACzB,oBAAU,MAAM,IAAI;AACpB,oBAAU,MAAM,IAAI;AACpB,8BAAoB;AAAA,QACtB;AACA,YAAI,WAAW;AACb,oCAA0B,WAAW,SAAS;AAAA,QAChD;AAAA,MACF;AACA,UAAI,WAAW,CAAC;AAAA,IAClB;AACA,IAAI,YAAY,CAAC,WAAW,QAAQ,aAAa,QAAQ,UAAU,WAAW;AAC5E,UAAI,eAAe,MAAQ,kBAAkB,UAAU,MAAM,KAAK,UAAU,MAAM,EAAE,cAAc;AAClG,UAAI;AACJ,UAAI,MAAQ,aAAa,aAAa,cAAc,aAAa,YAAY,aAAa;AACxF,uBAAe,aAAa;AAAA,MAC9B;AACA,aAAO,YAAY,QAAQ,EAAE,UAAU;AACrC,YAAI,OAAO,QAAQ,GAAG;AACpB,sBAAY,UAAU,MAAM,aAAa,UAAU,SAAS;AAC5D,cAAI,WAAW;AACb,mBAAO,QAAQ,EAAE,QAAQ;AACzB,yBAAa,cAAc,WAAW,MAAQ,iBAAiB,cAAc,MAAM,IAAI,MAAM;AAAA,UAC/F;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,IAAI,eAAe,CAAC,QAAQ,UAAU,WAAW;AAC/C,eAAS,QAAQ,UAAU,SAAS,QAAQ,EAAE,OAAO;AACnD,cAAM,QAAQ,OAAO,KAAK;AAC1B,YAAI,OAAO;AACT,gBAAM,MAAM,MAAM;AAClB,2BAAiB,KAAK;AACtB,cAAI,KAAK;AACP,gBAAI,MAAQ,gBAAgB;AAC1B,4CAA8B;AAC9B,kBAAI,IAAI,MAAM,GAAG;AACf,oBAAI,MAAM,EAAE,OAAO;AAAA,cACrB,OAAO;AACL,0CAA0B,KAAK,IAAI;AAAA,cACrC;AAAA,YACF;AACA,gBAAI,OAAO;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,IAAI,iBAAiB,CAAC,WAAW,OAAO,WAAW,OAAO,kBAAkB,UAAU;AACpF,UAAI,cAAc;AAClB,UAAI,cAAc;AAClB,UAAI,WAAW;AACf,UAAI,KAAK;AACT,UAAI,YAAY,MAAM,SAAS;AAC/B,UAAI,gBAAgB,MAAM,CAAC;AAC3B,UAAI,cAAc,MAAM,SAAS;AACjC,UAAI,YAAY,MAAM,SAAS;AAC/B,UAAI,gBAAgB,MAAM,CAAC;AAC3B,UAAI,cAAc,MAAM,SAAS;AACjC,UAAI;AACJ,UAAI;AACJ,aAAO,eAAe,aAAa,eAAe,WAAW;AAC3D,YAAI,iBAAiB,MAAM;AACzB,0BAAgB,MAAM,EAAE,WAAW;AAAA,QACrC,WAAW,eAAe,MAAM;AAC9B,wBAAc,MAAM,EAAE,SAAS;AAAA,QACjC,WAAW,iBAAiB,MAAM;AAChC,0BAAgB,MAAM,EAAE,WAAW;AAAA,QACrC,WAAW,eAAe,MAAM;AAC9B,wBAAc,MAAM,EAAE,SAAS;AAAA,QACjC,WAAW,YAAY,eAAe,eAAe,eAAe,GAAG;AACrE,gBAAM,eAAe,eAAe,eAAe;AACnD,0BAAgB,MAAM,EAAE,WAAW;AACnC,0BAAgB,MAAM,EAAE,WAAW;AAAA,QACrC,WAAW,YAAY,aAAa,aAAa,eAAe,GAAG;AACjE,gBAAM,aAAa,aAAa,eAAe;AAC/C,wBAAc,MAAM,EAAE,SAAS;AAC/B,wBAAc,MAAM,EAAE,SAAS;AAAA,QACjC,WAAW,YAAY,eAAe,aAAa,eAAe,GAAG;AACnE,cAAI,MAAQ,mBAAmB,cAAc,UAAU,UAAU,YAAY,UAAU,SAAS;AAC9F,sCAA0B,cAAc,MAAM,YAAY,KAAK;AAAA,UACjE;AACA,gBAAM,eAAe,aAAa,eAAe;AACjD,uBAAa,WAAW,cAAc,OAAO,YAAY,MAAM,WAAW;AAC1E,0BAAgB,MAAM,EAAE,WAAW;AACnC,wBAAc,MAAM,EAAE,SAAS;AAAA,QACjC,WAAW,YAAY,aAAa,eAAe,eAAe,GAAG;AACnE,cAAI,MAAQ,mBAAmB,cAAc,UAAU,UAAU,YAAY,UAAU,SAAS;AAC9F,sCAA0B,YAAY,MAAM,YAAY,KAAK;AAAA,UAC/D;AACA,gBAAM,aAAa,eAAe,eAAe;AACjD,uBAAa,WAAW,YAAY,OAAO,cAAc,KAAK;AAC9D,wBAAc,MAAM,EAAE,SAAS;AAC/B,0BAAgB,MAAM,EAAE,WAAW;AAAA,QACrC,OAAO;AACL,qBAAW;AACX,cAAI,MAAQ,SAAS;AACnB,iBAAK,KAAK,aAAa,MAAM,WAAW,EAAE,IAAI;AAC5C,kBAAI,MAAM,EAAE,KAAK,MAAM,EAAE,EAAE,UAAU,QAAQ,MAAM,EAAE,EAAE,UAAU,cAAc,OAAO;AACpF,2BAAW;AACX;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,MAAQ,WAAW,YAAY,GAAG;AACpC,wBAAY,MAAM,QAAQ;AAC1B,gBAAI,UAAU,UAAU,cAAc,OAAO;AAC3C,qBAAO,UAAU,SAAS,MAAM,WAAW,GAAG,WAAW,UAAU,SAAS;AAAA,YAC9E,OAAO;AACL,oBAAM,WAAW,eAAe,eAAe;AAC/C,oBAAM,QAAQ,IAAI;AAClB,qBAAO,UAAU;AAAA,YACnB;AACA,4BAAgB,MAAM,EAAE,WAAW;AAAA,UACrC,OAAO;AACL,mBAAO,UAAU,SAAS,MAAM,WAAW,GAAG,WAAW,aAAa,SAAS;AAC/E,4BAAgB,MAAM,EAAE,WAAW;AAAA,UACrC;AACA,cAAI,MAAM;AACR,gBAAI,MAAQ,gBAAgB;AAC1B,2BAAa,oBAAoB,cAAc,KAAK,GAAG,MAAM,cAAc,cAAc,KAAK,CAAC;AAAA,YACjG,OAAO;AACL,2BAAa,cAAc,MAAM,YAAY,MAAM,cAAc,KAAK;AAAA,YACxE;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,cAAc,WAAW;AAC3B,kBAAU,WAAW,MAAM,YAAY,CAAC,KAAK,OAAO,OAAO,MAAM,YAAY,CAAC,EAAE,OAAO,WAAW,OAAO,aAAa,SAAS;AAAA,MACjI,WAAW,MAAQ,aAAa,cAAc,WAAW;AACvD,qBAAa,OAAO,aAAa,SAAS;AAAA,MAC5C;AAAA,IACF;AACA,IAAI,cAAc,CAAC,WAAW,YAAY,kBAAkB,UAAU;AACpE,UAAI,UAAU,UAAU,WAAW,OAAO;AACxC,YAAI,MAAQ,kBAAkB,UAAU,UAAU,QAAQ;AACxD;AAAA;AAAA;AAAA,YAGA,cAAc,aAAa;AAAA;AAAA,YAG3B,UAAU,MAAM,aAAa;AAAA,YAAG;AAC9B,mBAAO;AAAA,UACT;AACA,iBAAO,UAAU,WAAW,WAAW;AAAA,QACzC;AACA,YAAI,MAAQ,WAAW,CAAC,iBAAiB;AACvC,iBAAO,UAAU,UAAU,WAAW;AAAA,QACxC;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,IAAI,gBAAgB,UAAQ;AAC1B,aAAO,QAAQ,KAAK,MAAM,KAAK;AAAA,IACjC;AACA,IAAI,sBAAsB,WAAS,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,MAAM;AACvE,IAAI,QAAQ,CAAC,UAAU,WAAW,kBAAkB,UAAU;AAC5D,YAAM,MAAM,UAAU,QAAQ,SAAS;AACvC,YAAM,cAAc,SAAS;AAC7B,YAAM,cAAc,UAAU;AAC9B,YAAM,MAAM,UAAU;AACtB,YAAM,OAAO,UAAU;AACvB,UAAI;AACJ,UAAI,CAAC,MAAQ,YAAY,SAAS,MAAM;AACtC,YAAI,MAAQ,KAAK;AACf,sBAAY,QAAQ,QAAQ,OAAO,QAAQ,kBAAkB,QAAQ;AAAA,QACvE;AACA,YAAI,MAAQ,iBAAiB,MAAQ,SAAS;AAC5C,cAAI,MAAQ,QAAQ,QAAQ,UAAU,CAAC,oBAAoB;AACzD,gBAAI,MAAQ,yBAAyB,SAAS,WAAW,UAAU,QAAQ;AACzE,wBAAU,MAAM,MAAM,IAAI,UAAU,UAAU;AAC9C,iCAAmB,UAAU,MAAM,aAAa;AAAA,YAClD;AAAA,UACF,OAAO;AACL,0BAAc,UAAU,WAAW,SAAS;AAAA,UAC9C;AAAA,QACF;AACA,YAAI,MAAQ,aAAa,gBAAgB,QAAQ,gBAAgB,MAAM;AACrE,yBAAe,KAAK,aAAa,WAAW,aAAa,eAAe;AAAA,QAC1E,WAAW,gBAAgB,MAAM;AAC/B,cAAI,MAAQ,aAAa,MAAQ,YAAY,SAAS,WAAW,MAAM;AACrE,gBAAI,cAAc;AAAA,UACpB;AACA,oBAAU,KAAK,MAAM,WAAW,aAAa,GAAG,YAAY,SAAS,CAAC;AAAA,QACxE;AAAA;AAAA,UAEA,CAAC,mBAAmB,MAAQ,aAAa,gBAAgB;AAAA,UAAM;AAC7D,uBAAa,aAAa,GAAG,YAAY,SAAS,CAAC;AAAA,QACrD;AACA,YAAI,MAAQ,OAAO,aAAa,QAAQ,OAAO;AAC7C,sBAAY;AAAA,QACd;AAAA,MACF,WAAW,MAAQ,YAAY,MAAQ,mBAAmB,gBAAgB,IAAI,MAAM,IAAI;AACtF,sBAAc,WAAW,cAAc;AAAA,MACzC,WAAW,MAAQ,YAAY,SAAS,WAAW,MAAM;AACvD,YAAI,OAAO;AAAA,MACb;AAAA,IACF;AACA,IAAI,+BAA+B,SAAO;AACxC,YAAM,aAAa,IAAI;AACvB,iBAAW,aAAa,YAAY;AAClC,YAAI,UAAU,aAAa,GAAqB;AAC9C,cAAI,UAAU,MAAM,GAAG;AACrB,kBAAM,WAAW,UAAU,MAAM;AACjC,sBAAU,SAAS;AACnB,uBAAW,eAAe,YAAY;AACpC,kBAAI,gBAAgB,WAAW;AAC7B,oBAAI,YAAY,MAAM,MAAM,UAAU,MAAM,KAAK,aAAa,IAAI;AAChE,sBAAI,YAAY,aAAa,MAAwB,aAAa,YAAY,aAAa,MAAM,KAAK,aAAa,YAAY,MAAM,MAAM,YAAY,aAAa,KAAoB,aAAa,YAAY,MAAM,GAAG;AACxN,8BAAU,SAAS;AACnB;AAAA,kBACF;AAAA,gBACF,OAAO;AACL,sBAAI,YAAY,aAAa,KAAuB,YAAY,aAAa,KAAoB,YAAY,YAAY,KAAK,MAAM,IAAI;AACtI,8BAAU,SAAS;AACnB;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,uCAA6B,SAAS;AAAA,QACxC;AAAA,MACF;AAAA,IACF;AACA,IAAI,gBAAgB,CAAC;AACrB,IAAI,+BAA+B,SAAO;AACxC,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,iBAAW,aAAa,IAAI,YAAY;AACtC,YAAI,UAAU,MAAM,MAAM,OAAO,UAAU,MAAM,MAAM,KAAK,YAAY;AACtE,6BAAmB,KAAK,WAAW;AACnC,gBAAM,WAAW,UAAU,MAAM;AACjC,eAAK,IAAI,iBAAiB,SAAS,GAAG,KAAK,GAAG,KAAK;AACjD,mBAAO,iBAAiB,CAAC;AACzB,gBAAI,CAAC,KAAK,MAAM,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,MAAM,MAAM,UAAU,MAAM,MAAM,CAAC,MAAQ,yBAAyB,CAAC,KAAK,MAAM,KAAK,KAAK,MAAM,MAAM,UAAU,MAAM,IAAI;AACnK,kBAAI,oBAAoB,MAAM,QAAQ,GAAG;AACvC,oBAAI,mBAAmB,cAAc,KAAK,OAAK,EAAE,qBAAqB,IAAI;AAC1E,8CAA8B;AAC9B,qBAAK,MAAM,IAAI,KAAK,MAAM,KAAK;AAC/B,oBAAI,kBAAkB;AACpB,mCAAiB,iBAAiB,MAAM,IAAI,UAAU,MAAM;AAC5D,mCAAiB,gBAAgB;AAAA,gBACnC,OAAO;AACL,uBAAK,MAAM,IAAI,UAAU,MAAM;AAC/B,gCAAc,KAAK;AAAA,oBACjB,eAAe;AAAA,oBACf,kBAAkB;AAAA,kBACpB,CAAC;AAAA,gBACH;AACA,oBAAI,KAAK,MAAM,GAAG;AAChB,gCAAc,IAAI,kBAAgB;AAChC,wBAAI,oBAAoB,aAAa,kBAAkB,KAAK,MAAM,CAAC,GAAG;AACpE,yCAAmB,cAAc,KAAK,OAAK,EAAE,qBAAqB,IAAI;AACtE,0BAAI,oBAAoB,CAAC,aAAa,eAAe;AACnD,qCAAa,gBAAgB,iBAAiB;AAAA,sBAChD;AAAA,oBACF;AAAA,kBACF,CAAC;AAAA,gBACH;AAAA,cACF,WAAW,CAAC,cAAc,KAAK,OAAK,EAAE,qBAAqB,IAAI,GAAG;AAChE,8BAAc,KAAK;AAAA,kBACjB,kBAAkB;AAAA,gBACpB,CAAC;AAAA,cACH;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,UAAU,aAAa,GAAqB;AAC9C,uCAA6B,SAAS;AAAA,QACxC;AAAA,MACF;AAAA,IACF;AACA,IAAI,sBAAsB,CAAC,gBAAgB,aAAa;AACtD,UAAI,eAAe,aAAa,GAAqB;AACnD,YAAI,eAAe,aAAa,MAAM,MAAM,QAAQ,aAAa,IAAI;AACnE,iBAAO;AAAA,QACT;AACA,YAAI,eAAe,aAAa,MAAM,MAAM,UAAU;AACpD,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AACA,UAAI,eAAe,MAAM,MAAM,UAAU;AACvC,eAAO;AAAA,MACT;AACA,aAAO,aAAa;AAAA,IACtB;AACA,IAAI,mBAAmB,WAAS;AAC9B,UAAI,MAAQ,SAAS;AACnB,cAAM,WAAW,MAAM,QAAQ,OAAO,MAAM,QAAQ,IAAI,IAAI;AAC5D,cAAM,cAAc,MAAM,WAAW,IAAI,gBAAgB;AAAA,MAC3D;AAAA,IACF;AACA,IAAI,eAAe,CAAC,QAAQ,SAAS,cAAc;AACjD,YAAM,WAAW,UAAU,OAAO,SAAS,OAAO,aAAa,SAAS,SAAS;AACjF,UAAI,MAAQ,QAAQ;AAClB,8BAAsB,SAAS,MAAM;AAAA,MACvC;AACA,aAAO;AAAA,IACT;AACA,IAAI,eAAe,aAAW;AAC5B,YAAM,WAAW,CAAC;AAClB,UAAI,SAAS;AACX,iBAAS,KAAK,GAAI,QAAQ,OAAO,KAAK,CAAC,GAAI,QAAQ,MAAM,GAAG,QAAQ,MAAM,GAAG,GAAG,aAAa,QAAQ,aAAa,CAAC;AAAA,MACrH;AACA,aAAO;AAAA,IACT;AACA,IAAI,wBAAwB,CAAC,SAAS,QAAQ,oBAAoB,UAAU;AAC1E,UAAI;AACJ,UAAI,WAAW,UAAU,QAAQ,aAAa,GAAqB;AACjE,cAAM,WAAW,IAAI,IAAI,aAAa,MAAM,EAAE,OAAO,OAAO,CAAC;AAC7D,YAAI,SAAS,MAAM;AACjB,WAAC,KAAK,QAAQ,cAAc,OAAO,SAAS,GAAG,IAAI,GAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,QAAQ,CAAE;AACxF,cAAI,QAAQ,MAAM,KAAK,mBAAmB;AACxC,uBAAW,aAAa,MAAM,KAAK,QAAQ,UAAU,GAAG;AACtD,oCAAsB,WAAW,SAAS,IAAI;AAAA,YAChD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,IAAI,aAAa,CAAC,SAAS,iBAAiB,gBAAgB,UAAU;AACpE,UAAI,IAAI,IAAI,IAAI,IAAI;AACpB,YAAM,UAAU,QAAQ;AACxB,YAAM,UAAU,QAAQ;AACxB,YAAM,WAAW,QAAQ,WAAW,SAAS,MAAM,IAAI;AACvD,YAAM,YAAY,OAAO,eAAe,IAAI,kBAAkB,EAAE,MAAM,MAAM,eAAe;AAC3F,oBAAc,QAAQ;AACtB,UAAI,MAAQ,SAAS,MAAM,QAAQ,eAAe,KAAK,gBAAgB,KAAK,MAAM,GAAG;AACnF,cAAM,IAAI,MAAM;AAAA,uCACmB,YAAY,YAAY,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAU7D;AAAA,MACD;AACA,UAAI,MAAQ,WAAW,QAAQ,kBAAkB;AAC/C,kBAAU,UAAU,UAAU,WAAW,CAAC;AAC1C,gBAAQ,iBAAiB,IAAI,CAAC,CAAC,UAAU,SAAS,MAAM,UAAU,QAAQ,SAAS,IAAI,QAAQ,QAAQ,CAAC;AAAA,MAC1G;AACA,UAAI,iBAAiB,UAAU,SAAS;AACtC,mBAAW,OAAO,OAAO,KAAK,UAAU,OAAO,GAAG;AAChD,cAAI,QAAQ,aAAa,GAAG,KAAK,CAAC,CAAC,OAAO,OAAO,SAAS,OAAO,EAAE,SAAS,GAAG,GAAG;AAChF,sBAAU,QAAQ,GAAG,IAAI,QAAQ,GAAG;AAAA,UACtC;AAAA,QACF;AAAA,MACF;AACA,gBAAU,QAAQ;AAClB,gBAAU,WAAW;AACrB,cAAQ,UAAU;AAClB,gBAAU,QAAQ,SAAS,QAAQ,MAAQ,YAAY,QAAQ,cAAc,UAAU;AACvF,UAAI,MAAQ,UAAU,MAAQ,WAAW;AACvC,kBAAU,QAAQ,MAAM;AAAA,MAC1B;AACA,2BAAqB,mBAAmB,QAAQ,UAAU,OAAoC;AAC9F,UAAI,MAAQ,gBAAgB;AAC1B,qBAAa,QAAQ,MAAM;AAC3B,sCAA8B;AAAA,MAChC;AACA,YAAM,UAAU,WAAW,aAAa;AACxC,UAAI,MAAQ,gBAAgB;AAC1B,YAAI,WAAW;AACf,YAAI,mBAAmB;AACrB,uCAA6B,UAAU,KAAK;AAC5C,qBAAW,gBAAgB,eAAe;AACxC,kBAAM,iBAAiB,aAAa;AACpC,gBAAI,CAAC,eAAe,MAAM,GAAG;AAC3B,oBAAM,kBAAkB,MAAQ,WAAW,MAAQ,oBAAoB,0BAA0B,cAAc,IAAI,IAAI,eAAe,EAAE;AACxI,8BAAgB,MAAM,IAAI;AAC1B,2BAAa,eAAe,YAAY,eAAe,MAAM,IAAI,iBAAiB,cAAc;AAAA,YAClG;AAAA,UACF;AACA,qBAAW,gBAAgB,eAAe;AACxC,kBAAM,iBAAiB,aAAa;AACpC,kBAAM,cAAc,aAAa;AACjC,gBAAI,aAAa;AACf,oBAAM,gBAAgB,YAAY;AAClC,kBAAI,mBAAmB,YAAY;AACnC,kBAAI,CAAC,MAAQ,yBAAyB,oBAAoB,iBAAiB,aAAa,GAAqB;AAC3G,oBAAI,mBAAmB,KAAK,eAAe,MAAM,MAAM,OAAO,SAAS,GAAG;AAC1E,uBAAO,iBAAiB;AACtB,sBAAI,WAAW,KAAK,gBAAgB,MAAM,MAAM,OAAO,KAAK;AAC5D,sBAAI,WAAW,QAAQ,MAAM,MAAM,eAAe,MAAM,KAAK,kBAAkB,QAAQ,YAAY;AACjG,8BAAU,QAAQ;AAClB,2BAAO,YAAY,mBAAmB,WAAW,OAAO,SAAS,QAAQ,MAAM,IAAI;AACjF,gCAAU,WAAW,OAAO,SAAS,QAAQ;AAAA,oBAC/C;AACA,wBAAI,CAAC,WAAW,CAAC,QAAQ,MAAM,GAAG;AAChC,yCAAmB;AACnB;AAAA,oBACF;AAAA,kBACF;AACA,oCAAkB,gBAAgB;AAAA,gBACpC;AAAA,cACF;AACA,kBAAI,CAAC,oBAAoB,kBAAkB,eAAe,cAAc,eAAe,gBAAgB,kBAAkB;AACvH,oBAAI,mBAAmB,kBAAkB;AACvC,sBAAI,CAAC,MAAQ,yBAAyB,CAAC,eAAe,MAAM,KAAK,eAAe,MAAM,GAAG;AACvF,mCAAe,MAAM,IAAI,eAAe,MAAM,EAAE,WAAW;AAAA,kBAC7D;AACA,+BAAa,eAAe,gBAAgB,gBAAgB;AAC5D,sBAAI,eAAe,aAAa,GAAqB;AACnD,mCAAe,UAAU,KAAK,eAAe,MAAM,MAAM,OAAO,KAAK;AAAA,kBACvE;AAAA,gBACF;AAAA,cACF;AACA,gCAAkB,OAAO,YAAY,MAAM,MAAM,cAAc,YAAY,MAAM,EAAE,cAAc;AAAA,YACnG,OAAO;AACL,kBAAI,eAAe,aAAa,GAAqB;AACnD,oBAAI,eAAe;AACjB,iCAAe,MAAM,KAAK,KAAK,eAAe,WAAW,OAAO,KAAK;AAAA,gBACvE;AACA,+BAAe,SAAS;AAAA,cAC1B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,6BAA6B;AAC/B,uCAA6B,UAAU,KAAK;AAAA,QAC9C;AACA,YAAI,WAAW,CAAC;AAChB,sBAAc,SAAS;AAAA,MACzB;AACA,UAAI,MAAQ,iCAAiC,QAAQ,UAAU,GAAgC;AAC7F,mBAAW,aAAa,UAAU,MAAM,YAAY;AAClD,cAAI,UAAU,MAAM,MAAM,eAAe,CAAC,UAAU,MAAM,GAAG;AAC3D,gBAAI,iBAAiB,UAAU,MAAM,KAAK,MAAM;AAC9C,wBAAU,MAAM,KAAK,KAAK,UAAU,WAAW,OAAO,KAAK;AAAA,YAC7D;AACA,sBAAU,SAAS;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AACA,mBAAa;AAAA,IACf;AACA,IAAI,yBAAyB,eAAa,IAAI,cAAc,QAAQ,UAAU,SAAS,YAAY,UAAU,SAAS,MAAM,EAAE,WAAW,YAAY,YAAY,CAAC,GAAG;AACrK,IAAI,4BAA4B,oBAAkB,IAAI,cAAc,uBAAuB,eAAe,YAAY,IAAI,eAAe,SAAS,WAAW,eAAe,MAAM,CAAC,MAAM,IAAI,eAAe,WAAW,IAAI;AAG3N,IAAI,mBAAmB,CAAC,SAAS,sBAAsB;AACrD,UAAI,MAAQ,gBAAgB,qBAAqB,CAAC,QAAQ,qBAAqB,kBAAkB,KAAK,GAAG;AACvG,0BAAkB,KAAK,EAAE,KAAK,IAAI,QAAQ,OAAK,QAAQ,oBAAoB,CAAC,CAAC;AAAA,MAC/E;AAAA,IACF;AACA,IAAI,iBAAiB,CAAC,SAAS,kBAAkB;AAC/C,UAAI,MAAQ,aAAa,MAAQ,WAAW;AAC1C,gBAAQ,WAAW;AAAA,MACrB;AACA,UAAI,MAAQ,gBAAgB,QAAQ,UAAU,GAA8B;AAC1E,gBAAQ,WAAW;AACnB;AAAA,MACF;AACA,uBAAiB,SAAS,QAAQ,mBAAmB;AACrD,YAAM,WAAW,MAAM,cAAc,SAAS,aAAa;AAC3D,aAAO,MAAQ,YAAY,UAAU,QAAQ,IAAI,SAAS;AAAA,IAC5D;AACA,IAAI,gBAAgB,CAAC,SAAS,kBAAkB;AAC9C,YAAM,MAAM,QAAQ;AACpB,YAAM,cAAc,WAAW,kBAAkB,QAAQ,UAAU,SAAS;AAC5E,YAAM,WAAW,MAAQ,WAAW,QAAQ,iBAAiB;AAC7D,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,2BAA2B,IAAI,QAAQ,YAAY,CAAC,yNAAyN;AAAA,MAC/R;AACA,UAAI;AACJ,UAAI,eAAe;AACjB,YAAI,MAAQ,YAAY,MAAQ,cAAc;AAC5C,kBAAQ,WAAW;AACnB,cAAI,QAAQ,mBAAmB;AAC7B,oBAAQ,kBAAkB,IAAI,CAAC,CAAC,YAAY,KAAK,MAAM,SAAS,UAAU,YAAY,KAAK,CAAC;AAC5F,oBAAQ,oBAAoB;AAAA,UAC9B;AAAA,QACF;AACA,2BAAmB,KAAK,mBAAmB;AAC3C,YAAI,MAAQ,aAAa;AACvB,yBAAe,SAAS,UAAU,mBAAmB;AAAA,QACvD;AAAA,MACF,OAAO;AACL,2BAAmB,KAAK,qBAAqB;AAC7C,YAAI,MAAQ,eAAe;AACzB,yBAAe,SAAS,UAAU,qBAAqB;AAAA,QACzD;AAAA,MACF;AACA,yBAAmB,KAAK,qBAAqB;AAC7C,UAAI,MAAQ,eAAe;AACzB,uBAAe,QAAQ,cAAc,MAAM,SAAS,UAAU,qBAAqB,CAAC;AAAA,MACtF;AACA,kBAAY;AACZ,aAAO,QAAQ,cAAc,MAAM,gBAAgB,SAAS,UAAU,aAAa,CAAC;AAAA,IACtF;AACA,IAAI,UAAU,CAAC,cAAc,OAAO,WAAW,YAAY,IAAI,aAAa,KAAK,EAAE,EAAE,MAAM,UAAQ;AACjG,cAAQ,MAAM,IAAI;AAClB,SAAG;AAAA,IACL,CAAC,IAAI,GAAG;AACR,IAAI,aAAa,kBAAgB,wBAAwB,WAAW,gBAAgB,aAAa,QAAQ,OAAO,aAAa,SAAS;AACtI,IAAI,kBAAkB,CAAO,SAAS,UAAU,kBAAkB;AAChE,UAAI;AACJ,YAAM,MAAM,QAAQ;AACpB,YAAM,YAAY,WAAW,UAAU,QAAQ,UAAU,SAAS;AAClE,YAAM,KAAK,IAAI,MAAM;AACrB,UAAI,MAAQ,SAAS,eAAe;AAClC,qBAAa,OAAO;AAAA,MACtB;AACA,YAAM,YAAY,WAAW,UAAU,QAAQ,UAAU,SAAS;AAClE,UAAI,MAAQ,OAAO;AACjB,gBAAQ,WAAW;AAAA,MACrB;AACA,UAAI,MAAQ,mBAAmB;AAC7B,cAAM,WAAW,SAAS,UAAU,KAAK,aAAa;AAAA,MACxD,OAAO;AACL,mBAAW,SAAS,UAAU,KAAK,aAAa;AAAA,MAClD;AACA,UAAI,MAAQ,OAAO;AACjB,gBAAQ,gBAAgB,QAAQ,kBAAkB,SAAS,IAAI,QAAQ,gBAAgB;AACvF,gBAAQ,WAAW,CAAC;AAAA,MACtB;AACA,UAAI,MAAQ,mBAAmB;AAC7B,YAAI;AACF,8BAAoB,GAAG;AACvB,cAAI,eAAe;AACjB,gBAAI,QAAQ,UAAU,UAAU,GAAgC;AAC9D,kBAAI,MAAM,IAAI;AAAA,YAChB,WAAW,QAAQ,UAAU,UAAU,GAAgC;AACrE,kBAAI,MAAM,IAAI;AAAA,YAChB;AAAA,UACF;AAAA,QACF,SAAS,GAAG;AACV,uBAAa,GAAG,GAAG;AAAA,QACrB;AAAA,MACF;AACA,UAAI,MAAQ,gBAAgB,IAAI;AAC9B,WAAG,IAAI,QAAM,GAAG,CAAC;AACjB,YAAI,MAAM,IAAI;AAAA,MAChB;AACA,gBAAU;AACV,gBAAU;AACV,UAAI,MAAQ,cAAc;AACxB,cAAM,oBAAoB,KAAK,IAAI,KAAK,MAAM,OAAO,KAAK,CAAC;AAC3D,cAAM,aAAa,MAAM,oBAAoB,OAAO;AACpD,YAAI,iBAAiB,WAAW,GAAG;AACjC,qBAAW;AAAA,QACb,OAAO;AACL,kBAAQ,IAAI,gBAAgB,EAAE,KAAK,UAAU;AAC7C,kBAAQ,WAAW;AACnB,2BAAiB,SAAS;AAAA,QAC5B;AAAA,MACF,OAAO;AACL,4BAAoB,OAAO;AAAA,MAC7B;AAAA,IACF;AACA,IAAI,eAAe;AACnB,IAAI,aAAa,CAAC,SAAS,UAAU,KAAK,kBAAkB;AAC1D,YAAM,cAAc,MAAQ,cAAc,OAAO;AACjD,YAAM,WAAW,MAAQ,WAAW,OAAO;AAC3C,YAAM,YAAY,MAAQ,YAAY,OAAO;AAC7C,YAAM,YAAY,MAAQ,YAAY,OAAO;AAC7C,UAAI;AACF,uBAAe;AACf,mBAAW,cAAc,SAAS,OAAO,IAAI,SAAS,UAAU,SAAS,OAAO;AAChF,YAAI,aAAa,WAAW;AAC1B,kBAAQ,WAAW,CAAC;AAAA,QACtB;AACA,YAAI,aAAa,UAAU;AACzB,kBAAQ,WAAW;AAAA,QACrB;AACA,YAAI,MAAQ,eAAe,MAAQ,SAAS;AAC1C,cAAI,MAAQ,cAAc,MAAQ,SAAS;AACzC,gBAAI,MAAQ,mBAAmB;AAC7B,qBAAO,QAAQ,QAAQ,QAAQ,EAAE,KAAK,WAAS,WAAW,SAAS,OAAO,aAAa,CAAC;AAAA,YAC1F,OAAO;AACL,yBAAW,SAAS,UAAU,aAAa;AAAA,YAC7C;AAAA,UACF,OAAO;AACL,kBAAM,aAAa,IAAI;AACvB,gBAAI,QAAQ,UAAU,UAAU,GAAgC;AAC9D,yBAAW,cAAc;AAAA,YAC3B,OAAO;AACL,kBAAI,cAAc;AAAA,YACpB;AAAA,UACF;AAAA,QACF;AAAA,MACF,SAAS,GAAG;AACV,qBAAa,GAAG,QAAQ,aAAa;AAAA,MACvC;AACA,qBAAe;AACf,aAAO;AAAA,IACT;AAEA,IAAI,sBAAsB,aAAW;AACnC,YAAM,UAAU,QAAQ,UAAU;AAClC,YAAM,MAAM,QAAQ;AACpB,YAAM,gBAAgB,WAAW,cAAc,OAAO;AACtD,YAAM,WAAW,MAAQ,WAAW,QAAQ,iBAAiB;AAC7D,YAAM,oBAAoB,QAAQ;AAClC,UAAI,MAAQ,cAAc;AACxB,YAAI,MAAQ,OAAO;AACjB,kBAAQ,WAAW;AAAA,QACrB;AACA,iBAAS,UAAU,oBAAoB;AACvC,YAAI,MAAQ,OAAO;AACjB,kBAAQ,WAAW,CAAC;AAAA,QACtB;AAAA,MACF;AACA,yBAAmB,KAAK,oBAAoB;AAC5C,UAAI,EAAE,QAAQ,UAAU,KAA8B;AACpD,gBAAQ,WAAW;AACnB,YAAI,MAAQ,gBAAgB,MAAQ,gBAAgB;AAClD,0BAAgB,GAAG;AAAA,QACrB;AACA,YAAI,MAAQ,YAAY;AACtB,cAAI,MAAQ,OAAO;AACjB,oBAAQ,WAAW;AAAA,UACrB;AACA,mBAAS,UAAU,kBAAkB;AACrC,cAAI,MAAQ,OAAO;AACjB,oBAAQ,WAAW,CAAC;AAAA,UACtB;AAAA,QACF;AACA,2BAAmB,KAAK,kBAAkB;AAC1C,sBAAc;AACd,YAAI,MAAQ,cAAc;AACxB,kBAAQ,iBAAiB,GAAG;AAC5B,cAAI,CAAC,mBAAmB;AACtB,uBAAW,OAAO;AAAA,UACpB;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI,MAAQ,cAAc;AACxB,cAAI,MAAQ,OAAO;AACjB,oBAAQ,WAAW;AAAA,UACrB;AACA,mBAAS,UAAU,oBAAoB;AACvC,cAAI,MAAQ,OAAO;AACjB,oBAAQ,WAAW,CAAC;AAAA,UACtB;AAAA,QACF;AACA,2BAAmB,KAAK,oBAAoB;AAC5C,sBAAc;AAAA,MAChB;AACA,UAAI,MAAQ,UAAU,MAAQ,UAAU;AACtC,gBAAQ,oBAAoB,GAAG;AAAA,MACjC;AACA,UAAI,MAAQ,cAAc;AACxB,YAAI,QAAQ,mBAAmB;AAC7B,kBAAQ,kBAAkB;AAC1B,kBAAQ,oBAAoB;AAAA,QAC9B;AACA,YAAI,QAAQ,UAAU,KAAyB;AAC7C,mBAAS,MAAM,eAAe,SAAS,KAAK,CAAC;AAAA,QAC/C;AACA,gBAAQ,WAAW,EAAE,IAA+B;AAAA,MACtD;AAAA,IACF;AACA,IAAI,cAAc,SAAO;AACvB,UAAI,MAAQ,cAAc,MAAM,aAAa,MAAM,YAAY;AAC7D,cAAM,UAAU,WAAW,GAAG;AAC9B,cAAM,cAAc,QAAQ,cAAc;AAC1C,YAAI,gBAAgB,QAAQ,WAAW,IAAsB,SAAiC,GAAqB;AACjH,yBAAe,SAAS,KAAK;AAAA,QAC/B;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,IAAI,aAAa,SAAO;AACtB,UAAI,MAAQ,gBAAgB;AAC1B,wBAAgB,IAAI,eAAe;AAAA,MACrC;AACA,UAAI,MAAQ,YAAY;AACtB,YAAI,WAAW;AAAA,MACjB;AACA,eAAS,MAAM,UAAU,KAAK,WAAW;AAAA,QACvC,QAAQ;AAAA,UACN,WAAW;AAAA,QACb;AAAA,MACF,CAAC,CAAC;AACF,UAAI,MAAQ,WAAW,YAAY,SAAS;AAC1C,oBAAY,QAAQ,aAAa,SAAS,qBAAqB,GAAG,KAAK,cAAc;AAAA,MACvF;AAAA,IACF;AACA,IAAI,WAAW,CAAC,UAAU,QAAQ,QAAQ;AACxC,UAAI,YAAY,SAAS,MAAM,GAAG;AAChC,YAAI;AACF,iBAAO,SAAS,MAAM,EAAE,GAAG;AAAA,QAC7B,SAAS,GAAG;AACV,uBAAa,CAAC;AAAA,QAChB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,IAAI,qBAAqB,CAAC,KAAK,kBAAkB;AAC/C,UAAI,MAAQ,oBAAoB;AAC9B,kBAAU,KAAK,aAAa,eAAe;AAAA,UACzC,SAAS;AAAA,UACT,UAAU;AAAA,UACV,QAAQ;AAAA,YACN,WAAW;AAAA,UACb;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,IAAI,kBAAkB,SAAO;AAC3B,UAAI,IAAI;AACR,aAAO,MAAQ,gBAAgB,IAAI,UAAU,KAAK,KAAK,MAAQ,yBAAyB,OAAO,KAAK,UAAU,IAAI,MAAQ,oBAAoB,IAAI,cAAc,KAAK,MAAQ,yBAAyB,OAAO,KAAK,YAAY,EAAE,IAAI;AAAA,IACtO;AACA,IAAI,sBAAsB,SAAO;AAC/B,YAAM,WAAW,IAAI;AACrB,UAAI,YAAY,MAAM;AACpB,iBAAS,KAAK,GAAG,KAAK,SAAS,QAAQ,KAAK,IAAI,MAAM;AACpD,gBAAM,WAAW,SAAS,EAAE;AAC5B,cAAI,OAAO,SAAS,sBAAsB,YAAY;AACpD,qBAAS,kBAAkB;AAAA,UAC7B;AACA,8BAAoB,QAAQ;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAGA,IAAI,WAAW,CAAC,KAAK,aAAa,WAAW,GAAG,EAAE,iBAAiB,IAAI,QAAQ;AAC/E,IAAI,WAAW,CAAC,KAAK,UAAU,QAAQ,YAAY;AACjD,YAAM,UAAU,WAAW,GAAG;AAC9B,UAAI,MAAQ,YAAY,CAAC,SAAS;AAChC,cAAM,IAAI,MAAM,mCAAmC,QAAQ,SAAS,+YAA+Y;AAAA,MACrd;AACA,YAAM,MAAM,MAAQ,WAAW,QAAQ,gBAAgB;AACvD,YAAM,SAAS,QAAQ,iBAAiB,IAAI,QAAQ;AACpD,YAAM,QAAQ,QAAQ;AACtB,YAAM,WAAW,MAAQ,WAAW,QAAQ,iBAAiB;AAC7D,eAAS,mBAAmB,QAAQ,QAAQ,UAAU,QAAQ,EAAE,CAAC,CAAC;AAClE,YAAM,aAAa,OAAO,MAAM,MAAM,KAAK,OAAO,MAAM,MAAM;AAC9D,YAAM,iBAAiB,WAAW,UAAU,CAAC;AAC7C,WAAK,CAAC,MAAQ,YAAY,EAAE,QAAQ,MAAmC,WAAW,WAAW,gBAAgB;AAC3G,gBAAQ,iBAAiB,IAAI,UAAU,MAAM;AAC7C,YAAI,MAAQ,OAAO;AACjB,cAAI,QAAQ,UAAU,MAAwB;AAC5C,2BAAe,mBAAmB,QAAQ,2FAA2F,aAAa,KAAK,eAAe,QAAQ,eAAe,MAAM;AAAA,UACrM,WAAW,QAAQ,UAAU,MAAyB;AACpD,2BAAe,mBAAmB,QAAQ,gHAAgH,aAAa,KAAK,eAAe,QAAQ,eAAe,MAAM;AAAA,UAC1N;AAAA,QACF;AACA,YAAI,CAAC,MAAQ,YAAY,UAAU;AACjC,cAAI,MAAQ,iBAAiB,QAAQ,cAAc,QAAQ,KAAwB;AACjF,kBAAM,eAAe,QAAQ,WAAW,QAAQ;AAChD,gBAAI,cAAc;AAChB,2BAAa,IAAI,qBAAmB;AAClC,oBAAI;AACF,2BAAS,eAAe,EAAE,QAAQ,QAAQ,QAAQ;AAAA,gBACpD,SAAS,GAAG;AACV,+BAAa,GAAG,GAAG;AAAA,gBACrB;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF;AACA,cAAI,MAAQ,cAAc,SAAS,IAAsB,SAAiC,GAAqB;AAC7G,gBAAI,MAAQ,mBAAmB,SAAS,uBAAuB;AAC7D,kBAAI,SAAS,sBAAsB,QAAQ,QAAQ,QAAQ,MAAM,OAAO;AACtE;AAAA,cACF;AAAA,YACF;AACA,2BAAe,SAAS,KAAK;AAAA,UAC/B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,IAAI,iBAAiB,CAAC,MAAM,SAAS,UAAU;AAC7C,UAAI,IAAI;AACR,YAAM,YAAY,KAAK;AACvB,UAAI,MAAQ,kBAAkB,QAAQ,UAAU,MAA2B,QAAQ,GAA8B;AAC/G,iDAAyC,QAAQ,YAAU,OAAO,eAAe,WAAW,QAAQ;AAAA,UAClG,SAAS,MAAM;AACb,kBAAM,UAAU,WAAW,IAAI;AAC/B,kBAAM,MAAM,MAAQ,WAAW,QAAQ,gBAAgB;AACvD,kBAAM,WAAW,MAAQ,WAAW,QAAQ,iBAAiB;AAC7D,gBAAI,CAAC,UAAU;AACb,sBAAQ,iBAAiB,KAAK,eAAa;AACzC,sBAAM,KAAK,UAAU,MAAM;AAC3B,uBAAO,OAAO,cAAc,GAAG,KAAK,WAAW,GAAG,IAAI;AAAA,cACxD,CAAC;AAAA,YACH,OAAO;AACL,oBAAM,KAAK,SAAS,MAAM;AAC1B,qBAAO,OAAO,cAAc,GAAG,KAAK,UAAU,GAAG,IAAI;AAAA,YACvD;AAAA,UACF;AAAA,QACF,CAAC,CAAC;AAAA,MACJ;AACA,UAAI,MAAQ,UAAU,QAAQ,aAAa,MAAQ,kBAAkB,QAAQ,cAAc,KAAK,WAAW;AACzG,YAAI,MAAQ,iBAAiB,KAAK,YAAY,CAAC,QAAQ,YAAY;AACjE,kBAAQ,aAAa,KAAK;AAAA,QAC5B;AACA,cAAM,UAAU,OAAO,SAAS,KAAK,QAAQ,cAAc,OAAO,KAAK,CAAC,CAAC;AACzE,gBAAQ,IAAI,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM;AAC3C,eAAK,MAAQ,QAAQ,MAAQ,WAAW,cAAc,OAAkB,CAAC,MAAQ,YAAY,QAAQ,MAAuB,cAAc,KAAiB;AACzJ,mBAAO,eAAe,WAAW,YAAY;AAAA,cAC3C,MAAM;AACJ,uBAAO,SAAS,MAAM,UAAU;AAAA,cAClC;AAAA,cACA,IAAI,UAAU;AACZ,oBAAI,MAAQ,OAAO;AACjB,wBAAM,MAAM,WAAW,IAAI;AAC3B;AAAA;AAAA,qBAEC,QAAQ,OAAkC;AAAA,qBAE1C,OAAO,IAAI,UAAU,OAAoC;AAAA,qBAEzD,cAAc,QAAmB;AAAA,qBAEjC,cAAc,UAAwB;AAAA,oBAAG;AACxC,mCAAe,YAAY,UAAU,SAAS,QAAQ,SAAS;AAAA,wEACP;AAAA,kBAC1D;AAAA,gBACF;AACA,yBAAS,MAAM,YAAY,UAAU,OAAO;AAAA,cAC9C;AAAA,cACA,cAAc;AAAA,cACd,YAAY;AAAA,YACd,CAAC;AAAA,UACH,WAAW,MAAQ,YAAY,MAAQ,UAAU,QAAQ,KAAgC,cAAc,IAAiB;AACtH,mBAAO,eAAe,WAAW,YAAY;AAAA,cAC3C,SAAS,MAAM;AACb,oBAAI;AACJ,sBAAM,MAAM,WAAW,IAAI;AAC3B,wBAAQ,MAAM,OAAO,OAAO,SAAS,IAAI,wBAAwB,OAAO,SAAS,IAAI,KAAK,MAAM;AAC9F,sBAAI;AACJ,0BAAQ,MAAM,IAAI,mBAAmB,OAAO,SAAS,IAAI,UAAU,EAAE,GAAG,IAAI;AAAA,gBAC9E,CAAC;AAAA,cACH;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AACD,YAAI,MAAQ,qBAAqB,CAAC,MAAQ,YAAY,QAAQ,IAA+B;AAC3F,gBAAM,qBAAoC,oBAAI,IAAI;AAClD,oBAAU,2BAA2B,SAAU,UAAU,UAAU,UAAU;AAC3E,gBAAI,IAAI,MAAM;AACZ,kBAAI;AACJ,oBAAM,WAAW,mBAAmB,IAAI,QAAQ;AAChD,kBAAI,KAAK,eAAe,QAAQ,GAAG;AACjC,2BAAW,KAAK,QAAQ;AACxB,uBAAO,KAAK,QAAQ;AAAA,cACtB,WAAW,UAAU,eAAe,QAAQ,KAAK,OAAO,KAAK,QAAQ,MAAM;AAAA,cAE3E,KAAK,QAAQ,KAAK,UAAU;AAC1B;AAAA,cACF,WAAW,YAAY,MAAM;AAC3B,sBAAM,UAAU,WAAW,IAAI;AAC/B,sBAAM,SAAS,WAAW,OAAO,SAAS,QAAQ;AAClD,oBAAI,UAAU,EAAE,SAAS,MAAmC,SAAS,OAA0B,aAAa,UAAU;AACpH,wBAAM,MAAM,MAAQ,WAAW,QAAQ,gBAAgB;AACvD,wBAAM,WAAW,MAAQ,WAAW,QAAQ,iBAAiB;AAC7D,wBAAM,SAAS,MAAM,QAAQ,eAAe,OAAO,SAAS,IAAI,QAAQ;AACxE,2BAAS,OAAO,SAAS,MAAM,QAAQ,kBAAgB;AACrD,wBAAI,SAAS,YAAY,KAAK,MAAM;AAClC,+BAAS,YAAY,EAAE,KAAK,UAAU,UAAU,UAAU,QAAQ;AAAA,oBACpE;AAAA,kBACF,CAAC;AAAA,gBACH;AACA;AAAA,cACF;AACA,mBAAK,QAAQ,IAAI,aAAa,QAAQ,OAAO,KAAK,QAAQ,MAAM,YAAY,QAAQ;AAAA,YACtF,CAAC;AAAA,UACH;AACA,eAAK,qBAAqB,MAAM,KAAoB,oBAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,QAAQ,eAAe,OAAO,KAAK,CAAC,CAAC,GAAG,GAAG,QAAQ;AAAA,YAAO,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI;AAAA;AAAA,UAAqB,EAAE,IAAI,CAAC,CAAC,UAAU,CAAC,MAAM;AAC3M,gBAAI;AACJ,kBAAM,WAAW,EAAE,CAAC,KAAK;AACzB,+BAAmB,IAAI,UAAU,QAAQ;AACzC,gBAAI,MAAQ,WAAW,EAAE,CAAC,IAAI,KAAuB;AACnD,eAAC,MAAM,QAAQ,qBAAqB,OAAO,SAAS,IAAI,KAAK,CAAC,UAAU,QAAQ,CAAC;AAAA,YACnF;AACA,mBAAO;AAAA,UACT,CAAC,CAAC,CAAC,CAAC;AAAA,QACN;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAGA,IAAI,sBAAsB,CAAO,KAAK,SAAS,SAAS,iBAAiB;AACvE,UAAI;AACJ,WAAK,QAAQ,UAAU,QAAsC,GAAG;AAC9D,gBAAQ,WAAW;AACnB,cAAM,WAAW,QAAQ;AACzB,aAAK,MAAQ,YAAY,MAAQ,sBAAsB,UAAU;AAC/D,gBAAM,aAAa,WAAW,SAAS,SAAS,YAAY;AAC5D,cAAI,cAAc,UAAU,YAAY;AACtC,kBAAM,UAAU,WAAW,WAAW,QAAQ,SAAS,IAAI,QAAQ,UAAU,IAAI,8BAA8B,QAAQ,SAAS,GAAG;AACnI,mBAAO,MAAM;AACb,oBAAQ;AAAA,UACV,OAAO;AACL,mBAAO;AAAA,UACT;AACA,cAAI,CAAC,MAAM;AACT,kBAAM,IAAI,MAAM,oBAAoB,QAAQ,SAAS,IAAI,QAAQ,UAAU,iBAAiB;AAAA,UAC9F;AACA,cAAI,MAAQ,UAAU,CAAC,KAAK,WAAW;AACrC,gBAAI,MAAQ,eAAe;AACzB,sBAAQ,aAAa,KAAK;AAAA,YAC5B;AACA;AAAA,cAAe;AAAA,cAAM;AAAA,cAAS;AAAA;AAAA,YAAkB;AAChD,iBAAK,YAAY;AAAA,UACnB;AACA,gBAAM,iBAAiB,WAAW,kBAAkB,QAAQ,SAAS;AACrE,cAAI,MAAQ,QAAQ;AAClB,oBAAQ,WAAW;AAAA,UACrB;AACA,cAAI;AACF,gBAAI,KAAK,OAAO;AAAA,UAClB,SAAS,GAAG;AACV,yBAAa,CAAC;AAAA,UAChB;AACA,cAAI,MAAQ,QAAQ;AAClB,oBAAQ,WAAW,CAAC;AAAA,UACtB;AACA,cAAI,MAAQ,eAAe;AACzB,oBAAQ,WAAW;AAAA,UACrB;AACA,yBAAe;AACf,gCAAsB,QAAQ,cAAc;AAAA,QAC9C,OAAO;AACL,iBAAO,IAAI;AACX,gBAAM,SAAS,IAAI;AACnB,yBAAe,YAAY,MAAM,EAAE;AAAA,YAAK,MAAM,QAAQ,WAAW;AAAA;AAAA,UAAsB;AAAA,QACzF;AACA,YAAI,MAAQ,SAAS,QAAQ,KAAK,OAAO;AACvC,cAAI;AACJ,cAAI,OAAO,KAAK,UAAU,UAAU;AAClC,oBAAQ,KAAK;AAAA,UACf,WAAW,MAAQ,QAAQ,OAAO,KAAK,UAAU,UAAU;AACzD,oBAAQ,aAAa,YAAY,GAAG;AACpC,gBAAI,QAAQ,YAAY;AACtB,sBAAQ,KAAK,MAAM,QAAQ,UAAU;AAAA,YACvC;AACA,gBAAI,MAAQ,qBAAqB,QAAQ,YAAY;AACnD,kBAAI,aAAa,UAAU,QAAQ,UAAU;AAAA,YAC/C;AAAA,UACF;AACA,gBAAM,WAAW,WAAW,SAAS,QAAQ,UAAU;AACvD,cAAI,CAAC,OAAO,IAAI,QAAQ,GAAG;AACzB,kBAAM,oBAAoB,WAAW,kBAAkB,QAAQ,SAAS;AACxE,gBAAI,CAAC,MAAQ,qBAAqB,MAAQ;AAAA,YAE1C,MAAQ,iBAAiB,QAAQ,UAAU,GAA4B;AACrE,sBAAQ,MAAM,OAAO,0BAAiB,EAAE,KAAK,OAAK,EAAE,SAAS,OAAO,QAAQ,CAAC;AAAA,YAC/E;AACA,0BAAc,UAAU,OAAO,CAAC,EAAE,QAAQ,UAAU,EAA+B;AACnF,8BAAkB;AAAA,UACpB;AAAA,QACF;AAAA,MACF;AACA,YAAM,oBAAoB,QAAQ;AAClC,YAAM,WAAW,MAAM,eAAe,SAAS,IAAI;AACnD,UAAI,MAAQ,gBAAgB,qBAAqB,kBAAkB,MAAM,GAAG;AAC1E,0BAAkB,MAAM,EAAE,KAAK,QAAQ;AAAA,MACzC,OAAO;AACL,iBAAS;AAAA,MACX;AAAA,IACF;AACA,IAAI,wBAAwB,cAAY;AACtC,UAAI,MAAQ,YAAY,MAAQ,mBAAmB;AACjD,iBAAS,UAAU,mBAAmB;AAAA,MACxC;AAAA,IACF;AAGA,IAAI,oBAAoB,SAAO;AAC7B,WAAK,IAAI,UAAU,OAA+B,GAAG;AACnD,cAAM,UAAU,WAAW,GAAG;AAC9B,cAAM,UAAU,QAAQ;AACxB,cAAM,eAAe,WAAW,qBAAqB,QAAQ,SAAS;AACtE,YAAI,MAAQ,0BAA0B;AACpC,gCAAsB,KAAK,SAAS,QAAQ,aAAa,IAAI;AAAA,QAC/D;AACA,YAAI,EAAE,QAAQ,UAAU,IAAuB;AAC7C,kBAAQ,WAAW;AACnB,cAAI;AACJ,cAAI,MAAQ,mBAAmB;AAC7B,qBAAS,IAAI,aAAa,UAAU;AACpC,gBAAI,QAAQ;AACV,kBAAI,MAAQ,aAAa,kBAAkB,QAAQ,UAAU,GAAgC;AAC3F,sBAAM,WAAW,MAAQ,OAAO,SAAS,IAAI,YAAY,SAAS,IAAI,aAAa,QAAQ,CAAC,IAAI,SAAS,IAAI,YAAY,OAAO;AAChI,oBAAI,UAAU,OAAO,WAAW,MAAM,WAAW,IAAI;AAAA,cACvD;AACA,sCAAwB,KAAK,QAAQ,WAAW,QAAQ,OAAO;AAAA,YACjE;AAAA,UACF;AACA,cAAI,MAAQ,kBAAkB,CAAC,QAAQ;AACrC,gBAAI,MAAQ,sBAAsB,MAAQ,QAAQ,MAAQ;AAAA,YAE1D,QAAQ,WAAW,IAA4B,IAA6B;AAC1E,kCAAoB,GAAG;AAAA,YACzB;AAAA,UACF;AACA,cAAI,MAAQ,cAAc;AACxB,gBAAI,oBAAoB;AACxB,mBAAO,oBAAoB,kBAAkB,cAAc,kBAAkB,MAAM;AACjF,kBAAI,MAAQ,qBAAqB,kBAAkB,aAAa,KAAuB,kBAAkB,aAAa,MAAM,KAAK,kBAAkB,KAAK,KAAK,kBAAkB,KAAK,GAAG;AACrL,iCAAiB,SAAS,QAAQ,sBAAsB,iBAAiB;AACzE;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,MAAQ,QAAQ,CAAC,MAAQ,qBAAqB,QAAQ,WAAW;AACnE,mBAAO,QAAQ,QAAQ,SAAS,EAAE,IAAI,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM;AACrE,kBAAI,cAAc,MAAiB,IAAI,eAAe,UAAU,GAAG;AACjE,sBAAM,QAAQ,IAAI,UAAU;AAC5B,uBAAO,IAAI,UAAU;AACrB,oBAAI,UAAU,IAAI;AAAA,cACpB;AAAA,YACF,CAAC;AAAA,UACH;AACA,cAAI,MAAQ,oBAAoB;AAC9B,qBAAS,MAAM,oBAAoB,KAAK,SAAS,OAAO,CAAC;AAAA,UAC3D,OAAO;AACL,gCAAoB,KAAK,SAAS,OAAO;AAAA,UAC3C;AAAA,QACF,OAAO;AACL,gCAAsB,KAAK,SAAS,QAAQ,aAAa,KAAK;AAC9D,cAAI,WAAW,OAAO,SAAS,QAAQ,gBAAgB;AACrD,kCAAsB,QAAQ,cAAc;AAAA,UAC9C,WAAW,WAAW,OAAO,SAAS,QAAQ,kBAAkB;AAC9D,oBAAQ,iBAAiB,KAAK,MAAM,sBAAsB,QAAQ,cAAc,CAAC;AAAA,UACnF;AAAA,QACF;AACA,qBAAa;AAAA,MACf;AAAA,IACF;AACA,IAAI,sBAAsB,SAAO;AAC/B,YAAM,gBAAgB,IAAI,MAAM,IAAI,IAAI,cAAc,MAAQ,UAAU,qBAAqB,IAAI,SAAS,MAAM,EAAE;AAClH,oBAAc,MAAM,IAAI;AACxB,mBAAa,KAAK,eAAe,IAAI,UAAU;AAAA,IACjD;AAIA,IAAI,qBAAqB,cAAY;AACnC,UAAI,MAAQ,YAAY,MAAQ,sBAAsB;AACpD,iBAAS,UAAU,sBAAsB;AAAA,MAC3C;AACA,UAAI,MAAQ,cAAc;AACxB,iBAAS,UAAU,oBAAoB;AAAA,MACzC;AAAA,IACF;AACA,IAAI,uBAAuB,CAAM,QAAO;AACtC,WAAK,IAAI,UAAU,OAA+B,GAAG;AACnD,cAAM,UAAU,WAAW,GAAG;AAC9B,YAAI,MAAQ,cAAc;AACxB,cAAI,QAAQ,eAAe;AACzB,oBAAQ,cAAc,IAAI,gBAAc,WAAW,CAAC;AACpD,oBAAQ,gBAAgB;AAAA,UAC1B;AAAA,QACF;AACA,YAAI,CAAC,MAAQ,UAAU;AACrB,6BAAmB,GAAG;AAAA,QACxB,WAAW,WAAW,OAAO,SAAS,QAAQ,gBAAgB;AAC5D,6BAAmB,QAAQ,cAAc;AAAA,QAC3C,WAAW,WAAW,OAAO,SAAS,QAAQ,kBAAkB;AAC9D,kBAAQ,iBAAiB,KAAK,MAAM,mBAAmB,QAAQ,cAAc,CAAC;AAAA,QAChF;AAAA,MACF;AAAA,IACF;AAIA,IAAI,uBAAuB,CAAC,sBAAsB,wBAAwB;AACxE,qBAAe,oBAAoB;AACnC,2BAAqB,oBAAoB;AACzC,sBAAgB,oBAAoB;AACpC,uBAAiB,oBAAoB;AACrC,qCAA+B,oBAAoB;AACnD,kCAA4B,oBAAoB;AAChD,kCAA4B,oBAAoB;AAChD,uBAAiB,oBAAoB;AACrC,0BAAoB,sBAAsB,mBAAmB;AAC7D,2BAAqB,oBAAoB;AAAA,IAC3C;AACA,IAAI,iBAAiB,0BAAwB;AAC3C,YAAM,eAAe,qBAAqB;AAC1C,2BAAqB,YAAY,SAAU,MAAM;AAC/C,cAAM,UAAU;AAChB,cAAM,cAAc,MAAQ,YAAY,QAAQ,cAAc,iBAAiB;AAC/E,cAAM,aAAa,aAAa,KAAK,SAAS,cAAc,OAAO,KAAK;AACxE,YAAI,MAAQ,QAAQ,CAAC,eAAe,MAAM;AACxC,cAAI,KAAK;AACT,cAAI,SAAS;AACb,gBAAM,kBAAkB,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,OAAO;AAC/I,iBAAO,KAAK,QAAQ,WAAW,QAAQ,MAAM;AAC3C,sBAAU,QAAQ,WAAW,EAAE,EAAE,MAAM;AACvC,6BAAiB,gBAAgB,MAAM,kBAAgB,CAAC,QAAQ,WAAW,EAAE,EAAE,YAAY,CAAC;AAC5F,gBAAI,SAAS;AACX,kBAAI,MAAQ,sBAAsB,WAAW,eAAe;AAC1D,2BAAW,cAAc,QAAQ,UAAU,IAAI,CAAC;AAAA,cAClD,OAAO;AACL,2BAAW,YAAY,QAAQ,UAAU,IAAI,CAAC;AAAA,cAChD;AAAA,YACF;AACA,gBAAI,gBAAgB;AAClB,yBAAW,YAAY,QAAQ,WAAW,EAAE,EAAE,UAAU,IAAI,CAAC;AAAA,YAC/D;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,IAAI,uBAAuB,0BAAwB;AACjD,2BAAqB,gBAAgB,qBAAqB;AAC1D,2BAAqB,cAAc,SAAU,UAAU;AACrD,cAAM,WAAW,SAAS,MAAM,IAAI,YAAY,QAAQ;AACxD,cAAM,WAAW,gBAAgB,KAAK,YAAY,UAAU,KAAK,OAAO;AACxE,YAAI,UAAU;AACZ,gBAAM,iBAAiB,sBAAsB,UAAU,QAAQ;AAC/D,gBAAM,cAAc,eAAe,eAAe,SAAS,CAAC;AAC5D,gBAAM,eAAe,aAAa,YAAY,YAAY,UAAU,YAAY,WAAW;AAC3F,uCAA6B,IAAI;AACjC,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,cAAc,QAAQ;AAAA,MACpC;AAAA,IACF;AACA,IAAI,uBAAuB,sBAAoB;AAC7C,uBAAiB,gBAAgB,iBAAiB;AAClD,uBAAiB,cAAc,SAAU,UAAU;AACjD,YAAI,YAAY,OAAO,SAAS,MAAM,MAAM,aAAa;AACvD,gBAAM,WAAW,gBAAgB,KAAK,YAAY,SAAS,MAAM,GAAG,KAAK,OAAO;AAChF,cAAI,UAAU;AACZ,kBAAM,iBAAiB,sBAAsB,UAAU,SAAS,MAAM,CAAC;AACvE,kBAAM,eAAe,eAAe,KAAK,OAAK,MAAM,QAAQ;AAC5D,gBAAI,cAAc;AAChB,2BAAa,OAAO;AACpB,2CAA6B,IAAI;AACjC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,eAAO,KAAK,cAAc,QAAQ;AAAA,MACpC;AAAA,IACF;AACA,IAAI,mBAAmB,0BAAwB;AAC7C,YAAM,kBAAkB,qBAAqB;AAC7C,2BAAqB,UAAU,YAAa,aAAa;AACvD,oBAAY,QAAQ,cAAY;AAC9B,cAAI,OAAO,aAAa,UAAU;AAChC,uBAAW,KAAK,cAAc,eAAe,QAAQ;AAAA,UACvD;AACA,gBAAM,WAAW,SAAS,MAAM,IAAI,YAAY,QAAQ;AACxD,gBAAM,WAAW,gBAAgB,KAAK,YAAY,UAAU,KAAK,OAAO;AACxE,cAAI,UAAU;AACZ,kBAAM,kBAAkB,SAAS,eAAe,EAAE;AAClD,4BAAgB,MAAM,IAAI;AAC1B,qBAAS,MAAM,EAAE,WAAW,cAAc,eAAe;AACzD,qBAAS,MAAM,IAAI;AACnB,kBAAM,iBAAiB,sBAAsB,UAAU,QAAQ;AAC/D,kBAAM,cAAc,eAAe,CAAC;AACpC,mBAAO,aAAa,YAAY,YAAY,UAAU,YAAY,WAAW;AAAA,UAC/E;AACA,cAAI,SAAS,aAAa,KAAK,CAAC,CAAC,SAAS,aAAa,MAAM,GAAG;AAC9D,qBAAS,SAAS;AAAA,UACpB;AACA,iBAAO,gBAAgB,KAAK,MAAM,QAAQ;AAAA,QAC5C,CAAC;AAAA,MACH;AAAA,IACF;AACA,IAAI,kBAAkB,0BAAwB;AAC5C,2BAAqB,SAAS,YAAa,aAAa;AACtD,oBAAY,QAAQ,cAAY;AAC9B,cAAI,OAAO,aAAa,UAAU;AAChC,uBAAW,KAAK,cAAc,eAAe,QAAQ;AAAA,UACvD;AACA,eAAK,YAAY,QAAQ;AAAA,QAC3B,CAAC;AAAA,MACH;AAAA,IACF;AACA,IAAI,8BAA8B,0BAAwB;AACxD,YAAM,6BAA6B,qBAAqB;AACxD,2BAAqB,qBAAqB,SAAU,UAAU,MAAM;AAClE,YAAI,aAAa,gBAAgB,aAAa,aAAa;AACzD,iBAAO,2BAA2B,KAAK,MAAM,UAAU,IAAI;AAAA,QAC7D;AACA,cAAM,YAAY,KAAK,cAAc,cAAc,GAAG;AACtD,YAAI;AACJ,kBAAU,YAAY;AACtB,YAAI,aAAa,cAAc;AAC7B,iBAAO,OAAO,UAAU,YAAY;AAClC,iBAAK,QAAQ,IAAI;AAAA,UACnB;AAAA,QACF,WAAW,aAAa,aAAa;AACnC,iBAAO,OAAO,UAAU,YAAY;AAClC,iBAAK,OAAO,IAAI;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,IAAI,8BAA8B,0BAAwB;AACxD,2BAAqB,qBAAqB,SAAU,UAAU,MAAM;AAClE,aAAK,mBAAmB,UAAU,IAAI;AAAA,MACxC;AAAA,IACF;AACA,IAAI,iCAAiC,0BAAwB;AAC3D,YAAM,gCAAgC,qBAAqB;AAC3D,2BAAqB,wBAAwB,SAAU,UAAU,SAAS;AACxE,YAAI,aAAa,gBAAgB,aAAa,aAAa;AACzD,iBAAO,8BAA8B,KAAK,MAAM,UAAU,OAAO;AAAA,QACnE;AACA,YAAI,aAAa,cAAc;AAC7B,eAAK,QAAQ,OAAO;AACpB,iBAAO;AAAA,QACT,WAAW,aAAa,aAAa;AACnC,eAAK,OAAO,OAAO;AACnB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,IAAI,mBAAmB,0BAAwB;AAC7C,YAAM,aAAa,OAAO,yBAAyB,KAAK,WAAW,aAAa;AAChF,aAAO,eAAe,sBAAsB,iBAAiB,UAAU;AACvE,UAAI,MAAQ,+BAA+B;AACzC,eAAO,eAAe,sBAAsB,eAAe;AAAA;AAAA;AAAA,UAGzD,MAAM;AACJ,kBAAM,eAAe,qBAAqB,KAAK,UAAU;AACzD,kBAAM,cAAc,aAAa,IAAI,UAAQ;AAC3C,kBAAI,IAAI;AACR,oBAAM,OAAO,CAAC;AACd,kBAAI,cAAc,KAAK;AACvB,qBAAO,eAAe,YAAY,MAAM,MAAM,KAAK,MAAM,GAAG;AAC1D,oBAAI,YAAY,aAAa,KAAqB,YAAY,aAAa,GAAsB;AAC/F,uBAAK,MAAM,MAAM,KAAK,YAAY,gBAAgB,OAAO,SAAS,GAAG,KAAK,MAAM,OAAO,KAAK,EAAE;AAAA,gBAChG;AACA,8BAAc,YAAY;AAAA,cAC5B;AACA,qBAAO,KAAK,OAAO,SAAO,QAAQ,EAAE,EAAE,KAAK,GAAG;AAAA,YAChD,CAAC,EAAE,OAAO,UAAQ,SAAS,EAAE,EAAE,KAAK,GAAG;AACvC,mBAAO,MAAM,cAAc;AAAA,UAC7B;AAAA;AAAA;AAAA;AAAA,UAIA,IAAI,OAAO;AACT,kBAAM,eAAe,qBAAqB,KAAK,UAAU;AACzD,yBAAa,QAAQ,UAAQ;AAC3B,kBAAI,cAAc,KAAK;AACvB,qBAAO,eAAe,YAAY,MAAM,MAAM,KAAK,MAAM,GAAG;AAC1D,sBAAM,MAAM;AACZ,8BAAc,YAAY;AAC1B,oBAAI,OAAO;AAAA,cACb;AACA,kBAAI,KAAK,MAAM,MAAM,IAAI;AACvB,sBAAM,WAAW,KAAK,cAAc,eAAe,KAAK;AACxD,yBAAS,MAAM,IAAI;AACnB,6BAAa,KAAK,eAAe,UAAU,KAAK,WAAW;AAAA,cAC7D,OAAO;AACL,qBAAK,OAAO;AAAA,cACd;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,eAAO,eAAe,sBAAsB,eAAe;AAAA,UACzD,MAAM;AACJ,gBAAI;AACJ,kBAAM,WAAW,gBAAgB,KAAK,YAAY,IAAI,KAAK,OAAO;AAClE,kBAAM,KAAK,YAAY,OAAO,SAAS,SAAS,gBAAgB,OAAO,SAAS,GAAG,cAAc,GAAmB;AAClH,qBAAO,SAAS,YAAY;AAAA,YAC9B,WAAW,UAAU;AACnB,qBAAO,SAAS;AAAA,YAClB,OAAO;AACL,qBAAO,KAAK;AAAA,YACd;AAAA,UACF;AAAA,UACA,IAAI,OAAO;AACT,gBAAI;AACJ,kBAAM,WAAW,gBAAgB,KAAK,YAAY,IAAI,KAAK,OAAO;AAClE,kBAAM,KAAK,YAAY,OAAO,SAAS,SAAS,gBAAgB,OAAO,SAAS,GAAG,cAAc,GAAmB;AAClH,uBAAS,YAAY,cAAc;AAAA,YACrC,WAAW,UAAU;AACnB,uBAAS,cAAc;AAAA,YACzB,OAAO;AACL,mBAAK,gBAAgB;AACrB,oBAAM,gBAAgB,KAAK,MAAM;AACjC,kBAAI,eAAe;AACjB,6BAAa,MAAM,eAAe,KAAK,UAAU;AAAA,cACnD;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,IAAI,sBAAsB,CAAC,KAAK,YAAY;AAAA,MAC1C,MAAM,qBAAqB,MAAM;AAAA,QAC/B,KAAK,GAAG;AACN,iBAAO,KAAK,CAAC;AAAA,QACf;AAAA,MACF;AACA,UAAI,QAAQ,UAAU,GAA4B;AAChD,cAAM,eAAe,IAAI,iBAAiB,YAAY;AACtD,eAAO,eAAe,KAAK,YAAY;AAAA,UACrC,MAAM;AACJ,mBAAO,KAAK,WAAW,IAAI,OAAK,EAAE,aAAa,CAAC;AAAA,UAClD;AAAA,QACF,CAAC;AACD,eAAO,eAAe,KAAK,qBAAqB;AAAA,UAC9C,MAAM;AACJ,mBAAO,IAAI,SAAS;AAAA,UACtB;AAAA,QACF,CAAC;AACD,eAAO,eAAe,KAAK,cAAc;AAAA,UACvC,MAAM;AACJ,kBAAM,aAAa,aAAa,KAAK,IAAI;AACzC,iBAAK,IAAI,UAAU,OAA+B,KAAK,WAAW,IAAI,EAAE,UAAU,GAAqB;AACrG,oBAAM,SAAS,IAAI,aAAa;AAChC,uBAAS,KAAK,GAAG,KAAK,WAAW,QAAQ,MAAM;AAC7C,sBAAM,OAAO,WAAW,EAAE,EAAE,MAAM;AAClC,oBAAI,MAAM;AACR,yBAAO,KAAK,IAAI;AAAA,gBAClB;AAAA,cACF;AACA,qBAAO;AAAA,YACT;AACA,mBAAO,aAAa,KAAK,UAAU;AAAA,UACrC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,IAAI,uBAAuB,gBAAc;AACvC,YAAM,eAAe,CAAC;AACtB,iBAAW,aAAa,MAAM,KAAK,UAAU,GAAG;AAC9C,YAAI,UAAU,MAAM,GAAG;AACrB,uBAAa,KAAK,SAAS;AAAA,QAC7B;AACA,qBAAa,KAAK,GAAG,qBAAqB,UAAU,UAAU,CAAC;AAAA,MACjE;AACA,aAAO;AAAA,IACT;AACA,IAAI,cAAc,UAAQ,KAAK,MAAM,KAAK,KAAK,aAAa,KAAK,KAAK,aAAa,MAAM,KAAK;AAC9F,IAAI,kBAAkB,CAAC,YAAY,UAAU,aAAa;AACxD,UAAI,KAAK;AACT,UAAI;AACJ,aAAO,KAAK,WAAW,QAAQ,MAAM;AACnC,oBAAY,WAAW,EAAE;AACzB,YAAI,UAAU,MAAM,KAAK,UAAU,MAAM,MAAM,YAAY,UAAU,MAAM,MAAM,UAAU;AACzF,iBAAO;AAAA,QACT;AACA,oBAAY,gBAAgB,UAAU,YAAY,UAAU,QAAQ;AACpE,YAAI,WAAW;AACb,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,IAAI,wBAAwB,CAAC,GAAG,aAAa;AAC3C,YAAM,aAAa,CAAC,CAAC;AACrB,cAAQ,IAAI,EAAE,gBAAgB,EAAE,MAAM,MAAM,UAAU;AACpD,mBAAW,KAAK,CAAC;AAAA,MACnB;AACA,aAAO;AAAA,IACT;AAMA,IAAI,qBAAqB,CAAC,MAAM,gBAAgB;AAC9C,YAAM,UAAU;AAAA,QACd,SAAS,YAAY,CAAC;AAAA,QACtB,WAAW,YAAY,CAAC;AAAA,MAC1B;AACA,UAAI,MAAQ,QAAQ;AAClB,gBAAQ,YAAY,YAAY,CAAC;AAAA,MACnC;AACA,UAAI,MAAQ,cAAc;AACxB,gBAAQ,cAAc,YAAY,CAAC;AAAA,MACrC;AACA,UAAI,MAAQ,eAAe;AACzB,gBAAQ,aAAa,KAAK;AAAA,MAC5B;AACA,UAAI,MAAQ,SAAS;AACnB,gBAAQ,mBAAmB,CAAC;AAAA,MAC9B;AACA,UAAI,MAAQ,aAAa,CAAC,kBAAkB,QAAQ,UAAU,GAAgC;AAC5F,gBAAQ,WAAW;AAAA,MACrB;AACA,UAAI,MAAQ,uBAAuB;AACjC,YAAI,MAAQ,UAAU,QAAQ,UAAU,GAAgC;AACtE,+BAAqB,KAAK,WAAW,OAAO;AAAA,QAC9C;AAAA,MACF,OAAO;AACL,YAAI,MAAQ,mBAAmB;AAC7B,8BAAoB,KAAK,WAAW,OAAO;AAAA,QAC7C;AACA,YAAI,MAAQ,cAAc;AACxB,yBAAe,KAAK,SAAS;AAAA,QAC/B;AACA,YAAI,MAAQ,oBAAoB;AAC9B,+BAAqB,KAAK,SAAS;AAAA,QACrC;AACA,YAAI,MAAQ,4BAA4B,QAAQ,UAAU,GAAgC;AACxF,2BAAiB,KAAK,SAAS;AAAA,QACjC;AAAA,MACF;AACA,YAAM,4BAA4B,KAAK,UAAU;AACjD,YAAM,+BAA+B,KAAK,UAAU;AACpD,aAAO,OAAO,KAAK,WAAW;AAAA,QAC5B,iBAAiB;AACf,uBAAa,MAAM,OAAO;AAAA,QAC5B;AAAA,QACA,oBAAoB;AAClB,gBAAM,UAAU,WAAW,IAAI;AAC/B,gCAAsB,MAAM,SAAS,QAAQ,aAAa,KAAK;AAC/D,4BAAkB,IAAI;AACtB,cAAI,MAAQ,qBAAqB,2BAA2B;AAC1D,sCAA0B,KAAK,IAAI;AAAA,UACrC;AAAA,QACF;AAAA,QACA,uBAAuB;AACrB,+BAAqB,IAAI;AACzB,cAAI,MAAQ,wBAAwB,8BAA8B;AAChE,yCAA6B,KAAK,IAAI;AAAA,UACxC;AAAA,QACF;AAAA,QACA,iBAAiB;AACf,cAAI,gBAAgB;AAClB,gBAAI,CAAC,KAAK,YAAY;AACpB,kBAAI,MAAQ,sBAAsB;AAChC,qBAAK,aAAa;AAAA,kBAChB,MAAM;AAAA,kBACN,gBAAgB,CAAC,EAAE,QAAQ,UAAU;AAAA,gBACvC,CAAC;AAAA,cACH,OAAO;AACL,qBAAK,aAAa;AAAA,kBAChB,MAAM;AAAA,gBACR,CAAC;AAAA,cACH;AAAA,YACF,OAAO;AACL,kBAAI,KAAK,WAAW,SAAS,QAAQ;AACnC,sBAAM,IAAI,MAAM,6CAA6C,QAAQ,SAAS,oBAAoB,KAAK,WAAW,IAAI,+CAA+C;AAAA,cACvK;AAAA,YACF;AAAA,UACF,OAAO;AACL,iBAAK,aAAa;AAAA,UACpB;AAAA,QACF;AAAA,MACF,CAAC;AACD,WAAK,KAAK,QAAQ;AAClB,aAAO;AAAA,QAAe;AAAA,QAAM;AAAA,QAAS,IAA+B;AAAA;AAAA,MAAkB;AAAA,IACxF;AAkNA,IAAI,wBAAwB,CAAC,KAAK,SAAS,WAAW,0BAA0B;AAC9E,UAAI,MAAQ,gBAAgB,WAAW;AACrC,YAAI,MAAQ,0BAA0B;AACpC,cAAI,uBAAuB;AACzB,wBAAY,UAAU;AAAA,cAAO,CAAC,CAAC,KAAK,MAAM,QAAQ;AAAA;AAAA,YAAqB;AAAA,UACzE,OAAO;AACL,wBAAY,UAAU,OAAO,CAAC,CAAC,KAAK,MAAM,EAAE,QAAQ,GAAsB;AAAA,UAC5E;AAAA,QACF;AACA,kBAAU,IAAI,CAAC,CAAC,OAAO,MAAM,MAAM,MAAM;AACvC,gBAAM,SAAS,MAAQ,qBAAqB,sBAAsB,KAAK,KAAK,IAAI;AAChF,gBAAM,UAAU,kBAAkB,SAAS,MAAM;AACjD,gBAAM,OAAO,iBAAiB,KAAK;AACnC,cAAI,IAAI,QAAQ,MAAM,SAAS,IAAI;AACnC,WAAC,QAAQ,gBAAgB,QAAQ,iBAAiB,CAAC,GAAG,KAAK,MAAM,IAAI,IAAI,QAAQ,MAAM,SAAS,IAAI,CAAC;AAAA,QACvG,CAAC;AAAA,MACH;AAAA,IACF;AACA,IAAI,oBAAoB,CAAC,SAAS,eAAe,QAAM;AACrD,UAAI;AACJ,UAAI;AACF,YAAI,MAAQ,UAAU;AACpB,cAAI,QAAQ,UAAU,KAAyB;AAC7C,aAAC,KAAK,QAAQ,mBAAmB,OAAO,SAAS,GAAG,UAAU,EAAE,EAAE;AAAA,UACpE,OAAO;AACL,aAAC,QAAQ,oBAAoB,QAAQ,qBAAqB,CAAC,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;AAAA,UACrF;AAAA,QACF,OAAO;AACL,kBAAQ,cAAc,UAAU,EAAE,EAAE;AAAA,QACtC;AAAA,MACF,SAAS,GAAG;AACV,qBAAa,CAAC;AAAA,MAChB;AAAA,IACF;AACA,IAAI,wBAAwB,CAAC,KAAK,UAAU;AAC1C,UAAI,MAAQ,8BAA8B,QAAQ,EAAwB,QAAO;AACjF,UAAI,MAAQ,4BAA4B,QAAQ,EAAsB,QAAO;AAC7E,UAAI,MAAQ,0BAA0B,QAAQ,GAAqB,QAAO,IAAI;AAC9E,UAAI,MAAQ,4BAA4B,QAAQ,MAAyB,IAAI,cAAe,QAAO,IAAI;AACvG,aAAO;AAAA,IACT;AACA,IAAI,mBAAmB,WAAS,0BAA0B;AAAA,MACxD,UAAU,QAAQ,OAAqB;AAAA,MACvC,UAAU,QAAQ,OAAqB;AAAA,IACzC,KAAK,QAAQ,OAAqB;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0, 1]}