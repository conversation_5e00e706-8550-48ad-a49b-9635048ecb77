{"version": 3, "sources": ["node_modules/@ionic/core/components/ios.transition.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as createAnimation } from './animation.js';\nimport { g as getIonPageElement } from './index2.js';\nconst DURATION = 540;\n// TODO(FW-2832): types\nconst getClonedElement = tagName => {\n  return document.querySelector(`${tagName}.ion-cloned-element`);\n};\nconst shadow = el => {\n  return el.shadowRoot || el;\n};\nconst getLargeTitle = refEl => {\n  const tabs = refEl.tagName === 'ION-TABS' ? refEl : refEl.querySelector('ion-tabs');\n  const query = 'ion-content ion-header:not(.header-collapse-condense-inactive) ion-title.title-large';\n  if (tabs != null) {\n    const activeTab = tabs.querySelector('ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)');\n    return activeTab != null ? activeTab.querySelector(query) : null;\n  }\n  return refEl.querySelector(query);\n};\nconst getBackButton = (refEl, backDirection) => {\n  const tabs = refEl.tagName === 'ION-TABS' ? refEl : refEl.querySelector('ion-tabs');\n  let buttonsList = [];\n  if (tabs != null) {\n    const activeTab = tabs.querySelector('ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)');\n    if (activeTab != null) {\n      buttonsList = activeTab.querySelectorAll('ion-buttons');\n    }\n  } else {\n    buttonsList = refEl.querySelectorAll('ion-buttons');\n  }\n  for (const buttons of buttonsList) {\n    const parentHeader = buttons.closest('ion-header');\n    const activeHeader = parentHeader && !parentHeader.classList.contains('header-collapse-condense-inactive');\n    const backButton = buttons.querySelector('ion-back-button');\n    const buttonsCollapse = buttons.classList.contains('buttons-collapse');\n    const startSlot = buttons.slot === 'start' || buttons.slot === '';\n    if (backButton !== null && startSlot && (buttonsCollapse && activeHeader && backDirection || !buttonsCollapse)) {\n      return backButton;\n    }\n  }\n  return null;\n};\nconst createLargeTitleTransition = (rootAnimation, rtl, backDirection, enteringEl, leavingEl) => {\n  const enteringBackButton = getBackButton(enteringEl, backDirection);\n  const leavingLargeTitle = getLargeTitle(leavingEl);\n  const enteringLargeTitle = getLargeTitle(enteringEl);\n  const leavingBackButton = getBackButton(leavingEl, backDirection);\n  const shouldAnimationForward = enteringBackButton !== null && leavingLargeTitle !== null && !backDirection;\n  const shouldAnimationBackward = enteringLargeTitle !== null && leavingBackButton !== null && backDirection;\n  if (shouldAnimationForward) {\n    const leavingLargeTitleBox = leavingLargeTitle.getBoundingClientRect();\n    const enteringBackButtonBox = enteringBackButton.getBoundingClientRect();\n    const enteringBackButtonTextEl = shadow(enteringBackButton).querySelector('.button-text');\n    // Text element not rendered if developers pass text=\"\" to the back button\n    const enteringBackButtonTextBox = enteringBackButtonTextEl === null || enteringBackButtonTextEl === void 0 ? void 0 : enteringBackButtonTextEl.getBoundingClientRect();\n    const leavingLargeTitleTextEl = shadow(leavingLargeTitle).querySelector('.toolbar-title');\n    const leavingLargeTitleTextBox = leavingLargeTitleTextEl.getBoundingClientRect();\n    animateLargeTitle(rootAnimation, rtl, backDirection, leavingLargeTitle, leavingLargeTitleBox, leavingLargeTitleTextBox, enteringBackButtonBox, enteringBackButtonTextEl, enteringBackButtonTextBox);\n    animateBackButton(rootAnimation, rtl, backDirection, enteringBackButton, enteringBackButtonBox, enteringBackButtonTextEl, enteringBackButtonTextBox, leavingLargeTitle, leavingLargeTitleTextBox);\n  } else if (shouldAnimationBackward) {\n    const enteringLargeTitleBox = enteringLargeTitle.getBoundingClientRect();\n    const leavingBackButtonBox = leavingBackButton.getBoundingClientRect();\n    const leavingBackButtonTextEl = shadow(leavingBackButton).querySelector('.button-text');\n    // Text element not rendered if developers pass text=\"\" to the back button\n    const leavingBackButtonTextBox = leavingBackButtonTextEl === null || leavingBackButtonTextEl === void 0 ? void 0 : leavingBackButtonTextEl.getBoundingClientRect();\n    const enteringLargeTitleTextEl = shadow(enteringLargeTitle).querySelector('.toolbar-title');\n    const enteringLargeTitleTextBox = enteringLargeTitleTextEl.getBoundingClientRect();\n    animateLargeTitle(rootAnimation, rtl, backDirection, enteringLargeTitle, enteringLargeTitleBox, enteringLargeTitleTextBox, leavingBackButtonBox, leavingBackButtonTextEl, leavingBackButtonTextBox);\n    animateBackButton(rootAnimation, rtl, backDirection, leavingBackButton, leavingBackButtonBox, leavingBackButtonTextEl, leavingBackButtonTextBox, enteringLargeTitle, enteringLargeTitleTextBox);\n  }\n  return {\n    forward: shouldAnimationForward,\n    backward: shouldAnimationBackward\n  };\n};\nconst animateBackButton = (rootAnimation, rtl, backDirection, backButtonEl, backButtonBox, backButtonTextEl, backButtonTextBox, largeTitleEl, largeTitleTextBox) => {\n  var _a, _b;\n  const BACK_BUTTON_START_OFFSET = rtl ? `calc(100% - ${backButtonBox.right + 4}px)` : `${backButtonBox.left - 4}px`;\n  const TEXT_ORIGIN_X = rtl ? 'right' : 'left';\n  const ICON_ORIGIN_X = rtl ? 'left' : 'right';\n  const CONTAINER_ORIGIN_X = rtl ? 'right' : 'left';\n  let WIDTH_SCALE = 1;\n  let HEIGHT_SCALE = 1;\n  let TEXT_START_SCALE = `scale(${HEIGHT_SCALE})`;\n  const TEXT_END_SCALE = 'scale(1)';\n  if (backButtonTextEl && backButtonTextBox) {\n    /**\n     * When the title and back button texts match then they should overlap during the\n     * page transition. If the texts do not match up then the back button text scale\n     * adjusts to not perfectly match the large title text otherwise the proportions\n     * will be incorrect. When the texts match we scale both the width and height to\n     * account for font weight differences between the title and back button.\n     */\n    const doTitleAndButtonTextsMatch = ((_a = backButtonTextEl.textContent) === null || _a === void 0 ? void 0 : _a.trim()) === ((_b = largeTitleEl.textContent) === null || _b === void 0 ? void 0 : _b.trim());\n    WIDTH_SCALE = largeTitleTextBox.width / backButtonTextBox.width;\n    /**\n     * Subtract an offset to account for slight sizing/padding differences between the\n     * title and the back button.\n     */\n    HEIGHT_SCALE = (largeTitleTextBox.height - LARGE_TITLE_SIZE_OFFSET) / backButtonTextBox.height;\n    /**\n     * Even though we set TEXT_START_SCALE to HEIGHT_SCALE above, we potentially need\n     * to re-compute this here since the HEIGHT_SCALE may have changed.\n     */\n    TEXT_START_SCALE = doTitleAndButtonTextsMatch ? `scale(${WIDTH_SCALE}, ${HEIGHT_SCALE})` : `scale(${HEIGHT_SCALE})`;\n  }\n  const backButtonIconEl = shadow(backButtonEl).querySelector('ion-icon');\n  const backButtonIconBox = backButtonIconEl.getBoundingClientRect();\n  /**\n   * We need to offset the container by the icon dimensions\n   * so that the back button text aligns with the large title\n   * text. Otherwise, the back button icon will align with the\n   * large title text but the back button text will not.\n   */\n  const CONTAINER_START_TRANSLATE_X = rtl ? `${backButtonIconBox.width / 2 - (backButtonIconBox.right - backButtonBox.right)}px` : `${backButtonBox.left - backButtonIconBox.width / 2}px`;\n  const CONTAINER_END_TRANSLATE_X = rtl ? `-${window.innerWidth - backButtonBox.right}px` : `${backButtonBox.left}px`;\n  /**\n   * Back button container should be\n   * aligned to the top of the title container\n   * so the texts overlap as the back button\n   * text begins to fade in.\n   */\n  const CONTAINER_START_TRANSLATE_Y = `${largeTitleTextBox.top}px`;\n  /**\n   * The cloned back button should align exactly with the\n   * real back button on the entering page otherwise there will\n   * be a layout shift.\n   */\n  const CONTAINER_END_TRANSLATE_Y = `${backButtonBox.top}px`;\n  /**\n   * In the forward direction, the cloned back button\n   * container should translate from over the large title\n   * to over the back button. In the backward direction,\n   * it should translate from over the back button to over\n   * the large title.\n   */\n  const FORWARD_CONTAINER_KEYFRAMES = [{\n    offset: 0,\n    transform: `translate3d(${CONTAINER_START_TRANSLATE_X}, ${CONTAINER_START_TRANSLATE_Y}, 0)`\n  }, {\n    offset: 1,\n    transform: `translate3d(${CONTAINER_END_TRANSLATE_X}, ${CONTAINER_END_TRANSLATE_Y}, 0)`\n  }];\n  const BACKWARD_CONTAINER_KEYFRAMES = [{\n    offset: 0,\n    transform: `translate3d(${CONTAINER_END_TRANSLATE_X}, ${CONTAINER_END_TRANSLATE_Y}, 0)`\n  }, {\n    offset: 1,\n    transform: `translate3d(${CONTAINER_START_TRANSLATE_X}, ${CONTAINER_START_TRANSLATE_Y}, 0)`\n  }];\n  const CONTAINER_KEYFRAMES = backDirection ? BACKWARD_CONTAINER_KEYFRAMES : FORWARD_CONTAINER_KEYFRAMES;\n  /**\n   * In the forward direction, the text in the cloned back button\n   * should start to be (roughly) the size of the large title\n   * and then scale down to be the size of the actual back button.\n   * The text should also translate, but that translate is handled\n   * by the container keyframes.\n   */\n  const FORWARD_TEXT_KEYFRAMES = [{\n    offset: 0,\n    opacity: 0,\n    transform: TEXT_START_SCALE\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: TEXT_END_SCALE\n  }];\n  const BACKWARD_TEXT_KEYFRAMES = [{\n    offset: 0,\n    opacity: 1,\n    transform: TEXT_END_SCALE\n  }, {\n    offset: 1,\n    opacity: 0,\n    transform: TEXT_START_SCALE\n  }];\n  const TEXT_KEYFRAMES = backDirection ? BACKWARD_TEXT_KEYFRAMES : FORWARD_TEXT_KEYFRAMES;\n  /**\n   * The icon should scale in/out in the second\n   * half of the animation. The icon should also\n   * translate, but that translate is handled by the\n   * container keyframes.\n   */\n  const FORWARD_ICON_KEYFRAMES = [{\n    offset: 0,\n    opacity: 0,\n    transform: 'scale(0.6)'\n  }, {\n    offset: 0.6,\n    opacity: 0,\n    transform: 'scale(0.6)'\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: 'scale(1)'\n  }];\n  const BACKWARD_ICON_KEYFRAMES = [{\n    offset: 0,\n    opacity: 1,\n    transform: 'scale(1)'\n  }, {\n    offset: 0.2,\n    opacity: 0,\n    transform: 'scale(0.6)'\n  }, {\n    offset: 1,\n    opacity: 0,\n    transform: 'scale(0.6)'\n  }];\n  const ICON_KEYFRAMES = backDirection ? BACKWARD_ICON_KEYFRAMES : FORWARD_ICON_KEYFRAMES;\n  const enteringBackButtonTextAnimation = createAnimation();\n  const enteringBackButtonIconAnimation = createAnimation();\n  const enteringBackButtonAnimation = createAnimation();\n  const clonedBackButtonEl = getClonedElement('ion-back-button');\n  const clonedBackButtonTextEl = shadow(clonedBackButtonEl).querySelector('.button-text');\n  const clonedBackButtonIconEl = shadow(clonedBackButtonEl).querySelector('ion-icon');\n  clonedBackButtonEl.text = backButtonEl.text;\n  clonedBackButtonEl.mode = backButtonEl.mode;\n  clonedBackButtonEl.icon = backButtonEl.icon;\n  clonedBackButtonEl.color = backButtonEl.color;\n  clonedBackButtonEl.disabled = backButtonEl.disabled;\n  clonedBackButtonEl.style.setProperty('display', 'block');\n  clonedBackButtonEl.style.setProperty('position', 'fixed');\n  enteringBackButtonIconAnimation.addElement(clonedBackButtonIconEl);\n  enteringBackButtonTextAnimation.addElement(clonedBackButtonTextEl);\n  enteringBackButtonAnimation.addElement(clonedBackButtonEl);\n  enteringBackButtonAnimation.beforeStyles({\n    position: 'absolute',\n    top: '0px',\n    [CONTAINER_ORIGIN_X]: '0px'\n  })\n  /**\n   * The write hooks must be set on this animation as it is guaranteed to run. Other\n   * animations such as the back button text animation will not run if the back button\n   * has no visible text.\n   */.beforeAddWrite(() => {\n    backButtonEl.style.setProperty('display', 'none');\n    clonedBackButtonEl.style.setProperty(TEXT_ORIGIN_X, BACK_BUTTON_START_OFFSET);\n  }).afterAddWrite(() => {\n    backButtonEl.style.setProperty('display', '');\n    clonedBackButtonEl.style.setProperty('display', 'none');\n    clonedBackButtonEl.style.removeProperty(TEXT_ORIGIN_X);\n  }).keyframes(CONTAINER_KEYFRAMES);\n  enteringBackButtonTextAnimation.beforeStyles({\n    'transform-origin': `${TEXT_ORIGIN_X} top`\n  }).keyframes(TEXT_KEYFRAMES);\n  enteringBackButtonIconAnimation.beforeStyles({\n    'transform-origin': `${ICON_ORIGIN_X} center`\n  }).keyframes(ICON_KEYFRAMES);\n  rootAnimation.addAnimation([enteringBackButtonTextAnimation, enteringBackButtonIconAnimation, enteringBackButtonAnimation]);\n};\nconst animateLargeTitle = (rootAnimation, rtl, backDirection, largeTitleEl, largeTitleBox, largeTitleTextBox, backButtonBox, backButtonTextEl, backButtonTextBox) => {\n  var _a, _b;\n  /**\n   * The horizontal transform origin for the large title\n   */\n  const ORIGIN_X = rtl ? 'right' : 'left';\n  const TITLE_START_OFFSET = rtl ? `calc(100% - ${largeTitleBox.right}px)` : `${largeTitleBox.left}px`;\n  /**\n   * The cloned large should align exactly with the\n   * real large title on the leaving page otherwise there will\n   * be a layout shift.\n   */\n  const START_TRANSLATE_X = '0px';\n  const START_TRANSLATE_Y = `${largeTitleBox.top}px`;\n  /**\n   * How much to offset the large title translation by.\n   * This accounts for differences in sizing between the large\n   * title and the back button due to padding and font weight.\n   */\n  const LARGE_TITLE_TRANSLATION_OFFSET = 8;\n  let END_TRANSLATE_X = rtl ? `-${window.innerWidth - backButtonBox.right - LARGE_TITLE_TRANSLATION_OFFSET}px` : `${backButtonBox.x + LARGE_TITLE_TRANSLATION_OFFSET}px`;\n  /**\n   * How much to scale the large title up/down by.\n   */\n  let HEIGHT_SCALE = 0.5;\n  /**\n   * The large title always starts full size.\n   */\n  const START_SCALE = 'scale(1)';\n  /**\n   * By default, we don't worry about having the large title scaled to perfectly\n   * match the back button because we don't know if the back button's text matches\n   * the large title's text.\n   */\n  let END_SCALE = `scale(${HEIGHT_SCALE})`;\n  // Text element not rendered if developers pass text=\"\" to the back button\n  if (backButtonTextEl && backButtonTextBox) {\n    /**\n     * The scaled title should (roughly) overlap the back button. This ensures that\n     * the back button and title overlap during the animation. Note that since both\n     * elements either fade in or fade out over the course of the animation, neither\n     * element will be fully visible on top of the other. As a result, the overlap\n     * does not need to be perfect, so approximate values are acceptable here.\n     */\n    END_TRANSLATE_X = rtl ? `-${window.innerWidth - backButtonTextBox.right - LARGE_TITLE_TRANSLATION_OFFSET}px` : `${backButtonTextBox.x - LARGE_TITLE_TRANSLATION_OFFSET}px`;\n    /**\n     * In the forward direction, the large title should start at its normal size and\n     * then scale down to be (roughly) the size of the back button on the other view.\n     * In the backward direction, the large title should start at (roughly) the size\n     * of the back button and then scale up to its original size.\n     * Note that since both elements either fade in or fade out over the course of the\n     * animation, neither element will be fully visible on top of the other. As a result,\n     * the overlap  does not need to be perfect, so approximate values are acceptable here.\n     */\n    /**\n     * When the title and back button texts match then they should overlap during the\n     * page transition. If the texts do not match up then the large title text scale\n     * adjusts to not perfectly match the back button text otherwise the proportions\n     * will be incorrect. When the texts match we scale both the width and height to\n     * account for font weight differences between the title and back button.\n     */\n    const doTitleAndButtonTextsMatch = ((_a = backButtonTextEl.textContent) === null || _a === void 0 ? void 0 : _a.trim()) === ((_b = largeTitleEl.textContent) === null || _b === void 0 ? void 0 : _b.trim());\n    const WIDTH_SCALE = backButtonTextBox.width / largeTitleTextBox.width;\n    HEIGHT_SCALE = backButtonTextBox.height / (largeTitleTextBox.height - LARGE_TITLE_SIZE_OFFSET);\n    /**\n     * Even though we set TEXT_START_SCALE to HEIGHT_SCALE above, we potentially need\n     * to re-compute this here since the HEIGHT_SCALE may have changed.\n     */\n    END_SCALE = doTitleAndButtonTextsMatch ? `scale(${WIDTH_SCALE}, ${HEIGHT_SCALE})` : `scale(${HEIGHT_SCALE})`;\n  }\n  /**\n   * The midpoints of the back button and the title should align such that the back\n   * button and title appear to be centered with each other.\n   */\n  const backButtonMidPoint = backButtonBox.top + backButtonBox.height / 2;\n  const titleMidPoint = largeTitleBox.height * HEIGHT_SCALE / 2;\n  const END_TRANSLATE_Y = `${backButtonMidPoint - titleMidPoint}px`;\n  const BACKWARDS_KEYFRAMES = [{\n    offset: 0,\n    opacity: 0,\n    transform: `translate3d(${END_TRANSLATE_X}, ${END_TRANSLATE_Y}, 0) ${END_SCALE}`\n  }, {\n    offset: 0.1,\n    opacity: 0\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: `translate3d(${START_TRANSLATE_X}, ${START_TRANSLATE_Y}, 0) ${START_SCALE}`\n  }];\n  const FORWARDS_KEYFRAMES = [{\n    offset: 0,\n    opacity: 0.99,\n    transform: `translate3d(${START_TRANSLATE_X}, ${START_TRANSLATE_Y}, 0) ${START_SCALE}`\n  }, {\n    offset: 0.6,\n    opacity: 0\n  }, {\n    offset: 1,\n    opacity: 0,\n    transform: `translate3d(${END_TRANSLATE_X}, ${END_TRANSLATE_Y}, 0) ${END_SCALE}`\n  }];\n  const KEYFRAMES = backDirection ? BACKWARDS_KEYFRAMES : FORWARDS_KEYFRAMES;\n  const clonedTitleEl = getClonedElement('ion-title');\n  const clonedLargeTitleAnimation = createAnimation();\n  clonedTitleEl.innerText = largeTitleEl.innerText;\n  clonedTitleEl.size = largeTitleEl.size;\n  clonedTitleEl.color = largeTitleEl.color;\n  clonedLargeTitleAnimation.addElement(clonedTitleEl);\n  clonedLargeTitleAnimation.beforeStyles({\n    'transform-origin': `${ORIGIN_X} top`,\n    /**\n     * Since font size changes will cause\n     * the dimension of the large title to change\n     * we need to set the cloned title height\n     * equal to that of the original large title height.\n     */\n    height: `${largeTitleBox.height}px`,\n    display: '',\n    position: 'relative',\n    [ORIGIN_X]: TITLE_START_OFFSET\n  }).beforeAddWrite(() => {\n    largeTitleEl.style.setProperty('opacity', '0');\n  }).afterAddWrite(() => {\n    largeTitleEl.style.setProperty('opacity', '');\n    clonedTitleEl.style.setProperty('display', 'none');\n  }).keyframes(KEYFRAMES);\n  rootAnimation.addAnimation(clonedLargeTitleAnimation);\n};\nconst iosTransitionAnimation = (navEl, opts) => {\n  var _a;\n  try {\n    const EASING = 'cubic-bezier(0.32,0.72,0,1)';\n    const OPACITY = 'opacity';\n    const TRANSFORM = 'transform';\n    const CENTER = '0%';\n    const OFF_OPACITY = 0.8;\n    const isRTL = navEl.ownerDocument.dir === 'rtl';\n    const OFF_RIGHT = isRTL ? '-99.5%' : '99.5%';\n    const OFF_LEFT = isRTL ? '33%' : '-33%';\n    const enteringEl = opts.enteringEl;\n    const leavingEl = opts.leavingEl;\n    const backDirection = opts.direction === 'back';\n    const contentEl = enteringEl.querySelector(':scope > ion-content');\n    const headerEls = enteringEl.querySelectorAll(':scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *');\n    const enteringToolBarEls = enteringEl.querySelectorAll(':scope > ion-header > ion-toolbar');\n    const rootAnimation = createAnimation();\n    const enteringContentAnimation = createAnimation();\n    rootAnimation.addElement(enteringEl).duration(((_a = opts.duration) !== null && _a !== void 0 ? _a : 0) || DURATION).easing(opts.easing || EASING).fill('both').beforeRemoveClass('ion-page-invisible');\n    // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n    if (leavingEl && navEl !== null && navEl !== undefined) {\n      const navDecorAnimation = createAnimation();\n      navDecorAnimation.addElement(navEl);\n      rootAnimation.addAnimation(navDecorAnimation);\n    }\n    if (!contentEl && enteringToolBarEls.length === 0 && headerEls.length === 0) {\n      enteringContentAnimation.addElement(enteringEl.querySelector(':scope > .ion-page, :scope > ion-nav, :scope > ion-tabs')); // REVIEW\n    } else {\n      enteringContentAnimation.addElement(contentEl); // REVIEW\n      enteringContentAnimation.addElement(headerEls);\n    }\n    rootAnimation.addAnimation(enteringContentAnimation);\n    if (backDirection) {\n      enteringContentAnimation.beforeClearStyles([OPACITY]).fromTo('transform', `translateX(${OFF_LEFT})`, `translateX(${CENTER})`).fromTo(OPACITY, OFF_OPACITY, 1);\n    } else {\n      // entering content, forward direction\n      enteringContentAnimation.beforeClearStyles([OPACITY]).fromTo('transform', `translateX(${OFF_RIGHT})`, `translateX(${CENTER})`);\n    }\n    if (contentEl) {\n      const enteringTransitionEffectEl = shadow(contentEl).querySelector('.transition-effect');\n      if (enteringTransitionEffectEl) {\n        const enteringTransitionCoverEl = enteringTransitionEffectEl.querySelector('.transition-cover');\n        const enteringTransitionShadowEl = enteringTransitionEffectEl.querySelector('.transition-shadow');\n        const enteringTransitionEffect = createAnimation();\n        const enteringTransitionCover = createAnimation();\n        const enteringTransitionShadow = createAnimation();\n        enteringTransitionEffect.addElement(enteringTransitionEffectEl).beforeStyles({\n          opacity: '1',\n          display: 'block'\n        }).afterStyles({\n          opacity: '',\n          display: ''\n        });\n        enteringTransitionCover.addElement(enteringTransitionCoverEl) // REVIEW\n        .beforeClearStyles([OPACITY]).fromTo(OPACITY, 0, 0.1);\n        enteringTransitionShadow.addElement(enteringTransitionShadowEl) // REVIEW\n        .beforeClearStyles([OPACITY]).fromTo(OPACITY, 0.03, 0.7);\n        enteringTransitionEffect.addAnimation([enteringTransitionCover, enteringTransitionShadow]);\n        enteringContentAnimation.addAnimation([enteringTransitionEffect]);\n      }\n    }\n    const enteringContentHasLargeTitle = enteringEl.querySelector('ion-header.header-collapse-condense');\n    const {\n      forward,\n      backward\n    } = createLargeTitleTransition(rootAnimation, isRTL, backDirection, enteringEl, leavingEl);\n    enteringToolBarEls.forEach(enteringToolBarEl => {\n      const enteringToolBar = createAnimation();\n      enteringToolBar.addElement(enteringToolBarEl);\n      rootAnimation.addAnimation(enteringToolBar);\n      const enteringTitle = createAnimation();\n      enteringTitle.addElement(enteringToolBarEl.querySelector('ion-title')); // REVIEW\n      const enteringToolBarButtons = createAnimation();\n      const buttons = Array.from(enteringToolBarEl.querySelectorAll('ion-buttons,[menuToggle]'));\n      const parentHeader = enteringToolBarEl.closest('ion-header');\n      const inactiveHeader = parentHeader === null || parentHeader === void 0 ? void 0 : parentHeader.classList.contains('header-collapse-condense-inactive');\n      let buttonsToAnimate;\n      if (backDirection) {\n        buttonsToAnimate = buttons.filter(button => {\n          const isCollapseButton = button.classList.contains('buttons-collapse');\n          return isCollapseButton && !inactiveHeader || !isCollapseButton;\n        });\n      } else {\n        buttonsToAnimate = buttons.filter(button => !button.classList.contains('buttons-collapse'));\n      }\n      enteringToolBarButtons.addElement(buttonsToAnimate);\n      const enteringToolBarItems = createAnimation();\n      enteringToolBarItems.addElement(enteringToolBarEl.querySelectorAll(':scope > *:not(ion-title):not(ion-buttons):not([menuToggle])'));\n      const enteringToolBarBg = createAnimation();\n      enteringToolBarBg.addElement(shadow(enteringToolBarEl).querySelector('.toolbar-background')); // REVIEW\n      const enteringBackButton = createAnimation();\n      const backButtonEl = enteringToolBarEl.querySelector('ion-back-button');\n      if (backButtonEl) {\n        enteringBackButton.addElement(backButtonEl);\n      }\n      enteringToolBar.addAnimation([enteringTitle, enteringToolBarButtons, enteringToolBarItems, enteringToolBarBg, enteringBackButton]);\n      enteringToolBarButtons.fromTo(OPACITY, 0.01, 1);\n      enteringToolBarItems.fromTo(OPACITY, 0.01, 1);\n      if (backDirection) {\n        if (!inactiveHeader) {\n          enteringTitle.fromTo('transform', `translateX(${OFF_LEFT})`, `translateX(${CENTER})`).fromTo(OPACITY, 0.01, 1);\n        }\n        enteringToolBarItems.fromTo('transform', `translateX(${OFF_LEFT})`, `translateX(${CENTER})`);\n        // back direction, entering page has a back button\n        enteringBackButton.fromTo(OPACITY, 0.01, 1);\n      } else {\n        // entering toolbar, forward direction\n        if (!enteringContentHasLargeTitle) {\n          enteringTitle.fromTo('transform', `translateX(${OFF_RIGHT})`, `translateX(${CENTER})`).fromTo(OPACITY, 0.01, 1);\n        }\n        enteringToolBarItems.fromTo('transform', `translateX(${OFF_RIGHT})`, `translateX(${CENTER})`);\n        enteringToolBarBg.beforeClearStyles([OPACITY, 'transform']);\n        const translucentHeader = parentHeader === null || parentHeader === void 0 ? void 0 : parentHeader.translucent;\n        if (!translucentHeader) {\n          enteringToolBarBg.fromTo(OPACITY, 0.01, 'var(--opacity)');\n        } else {\n          enteringToolBarBg.fromTo('transform', isRTL ? 'translateX(-100%)' : 'translateX(100%)', 'translateX(0px)');\n        }\n        // forward direction, entering page has a back button\n        if (!forward) {\n          enteringBackButton.fromTo(OPACITY, 0.01, 1);\n        }\n        if (backButtonEl && !forward) {\n          const enteringBackBtnText = createAnimation();\n          enteringBackBtnText.addElement(shadow(backButtonEl).querySelector('.button-text')) // REVIEW\n          .fromTo(`transform`, isRTL ? 'translateX(-100px)' : 'translateX(100px)', 'translateX(0px)');\n          enteringToolBar.addAnimation(enteringBackBtnText);\n        }\n      }\n    });\n    // setup leaving view\n    if (leavingEl) {\n      const leavingContent = createAnimation();\n      const leavingContentEl = leavingEl.querySelector(':scope > ion-content');\n      const leavingToolBarEls = leavingEl.querySelectorAll(':scope > ion-header > ion-toolbar');\n      const leavingHeaderEls = leavingEl.querySelectorAll(':scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *');\n      if (!leavingContentEl && leavingToolBarEls.length === 0 && leavingHeaderEls.length === 0) {\n        leavingContent.addElement(leavingEl.querySelector(':scope > .ion-page, :scope > ion-nav, :scope > ion-tabs')); // REVIEW\n      } else {\n        leavingContent.addElement(leavingContentEl); // REVIEW\n        leavingContent.addElement(leavingHeaderEls);\n      }\n      rootAnimation.addAnimation(leavingContent);\n      if (backDirection) {\n        // leaving content, back direction\n        leavingContent.beforeClearStyles([OPACITY]).fromTo('transform', `translateX(${CENTER})`, isRTL ? 'translateX(-100%)' : 'translateX(100%)');\n        const leavingPage = getIonPageElement(leavingEl);\n        rootAnimation.afterAddWrite(() => {\n          if (rootAnimation.getDirection() === 'normal') {\n            leavingPage.style.setProperty('display', 'none');\n          }\n        });\n      } else {\n        // leaving content, forward direction\n        leavingContent.fromTo('transform', `translateX(${CENTER})`, `translateX(${OFF_LEFT})`).fromTo(OPACITY, 1, OFF_OPACITY);\n      }\n      if (leavingContentEl) {\n        const leavingTransitionEffectEl = shadow(leavingContentEl).querySelector('.transition-effect');\n        if (leavingTransitionEffectEl) {\n          const leavingTransitionCoverEl = leavingTransitionEffectEl.querySelector('.transition-cover');\n          const leavingTransitionShadowEl = leavingTransitionEffectEl.querySelector('.transition-shadow');\n          const leavingTransitionEffect = createAnimation();\n          const leavingTransitionCover = createAnimation();\n          const leavingTransitionShadow = createAnimation();\n          leavingTransitionEffect.addElement(leavingTransitionEffectEl).beforeStyles({\n            opacity: '1',\n            display: 'block'\n          }).afterStyles({\n            opacity: '',\n            display: ''\n          });\n          leavingTransitionCover.addElement(leavingTransitionCoverEl) // REVIEW\n          .beforeClearStyles([OPACITY]).fromTo(OPACITY, 0.1, 0);\n          leavingTransitionShadow.addElement(leavingTransitionShadowEl) // REVIEW\n          .beforeClearStyles([OPACITY]).fromTo(OPACITY, 0.7, 0.03);\n          leavingTransitionEffect.addAnimation([leavingTransitionCover, leavingTransitionShadow]);\n          leavingContent.addAnimation([leavingTransitionEffect]);\n        }\n      }\n      leavingToolBarEls.forEach(leavingToolBarEl => {\n        const leavingToolBar = createAnimation();\n        leavingToolBar.addElement(leavingToolBarEl);\n        const leavingTitle = createAnimation();\n        leavingTitle.addElement(leavingToolBarEl.querySelector('ion-title')); // REVIEW\n        const leavingToolBarButtons = createAnimation();\n        const buttons = leavingToolBarEl.querySelectorAll('ion-buttons,[menuToggle]');\n        const parentHeader = leavingToolBarEl.closest('ion-header');\n        const inactiveHeader = parentHeader === null || parentHeader === void 0 ? void 0 : parentHeader.classList.contains('header-collapse-condense-inactive');\n        const buttonsToAnimate = Array.from(buttons).filter(button => {\n          const isCollapseButton = button.classList.contains('buttons-collapse');\n          return isCollapseButton && !inactiveHeader || !isCollapseButton;\n        });\n        leavingToolBarButtons.addElement(buttonsToAnimate);\n        const leavingToolBarItems = createAnimation();\n        const leavingToolBarItemEls = leavingToolBarEl.querySelectorAll(':scope > *:not(ion-title):not(ion-buttons):not([menuToggle])');\n        if (leavingToolBarItemEls.length > 0) {\n          leavingToolBarItems.addElement(leavingToolBarItemEls);\n        }\n        const leavingToolBarBg = createAnimation();\n        leavingToolBarBg.addElement(shadow(leavingToolBarEl).querySelector('.toolbar-background')); // REVIEW\n        const leavingBackButton = createAnimation();\n        const backButtonEl = leavingToolBarEl.querySelector('ion-back-button');\n        if (backButtonEl) {\n          leavingBackButton.addElement(backButtonEl);\n        }\n        leavingToolBar.addAnimation([leavingTitle, leavingToolBarButtons, leavingToolBarItems, leavingBackButton, leavingToolBarBg]);\n        rootAnimation.addAnimation(leavingToolBar);\n        // fade out leaving toolbar items\n        leavingBackButton.fromTo(OPACITY, 0.99, 0);\n        leavingToolBarButtons.fromTo(OPACITY, 0.99, 0);\n        leavingToolBarItems.fromTo(OPACITY, 0.99, 0);\n        if (backDirection) {\n          if (!inactiveHeader) {\n            // leaving toolbar, back direction\n            leavingTitle.fromTo('transform', `translateX(${CENTER})`, isRTL ? 'translateX(-100%)' : 'translateX(100%)').fromTo(OPACITY, 0.99, 0);\n          }\n          leavingToolBarItems.fromTo('transform', `translateX(${CENTER})`, isRTL ? 'translateX(-100%)' : 'translateX(100%)');\n          leavingToolBarBg.beforeClearStyles([OPACITY, 'transform']);\n          // leaving toolbar, back direction, and there's no entering toolbar\n          // should just slide out, no fading out\n          const translucentHeader = parentHeader === null || parentHeader === void 0 ? void 0 : parentHeader.translucent;\n          if (!translucentHeader) {\n            leavingToolBarBg.fromTo(OPACITY, 'var(--opacity)', 0);\n          } else {\n            leavingToolBarBg.fromTo('transform', 'translateX(0px)', isRTL ? 'translateX(-100%)' : 'translateX(100%)');\n          }\n          if (backButtonEl && !backward) {\n            const leavingBackBtnText = createAnimation();\n            leavingBackBtnText.addElement(shadow(backButtonEl).querySelector('.button-text')) // REVIEW\n            .fromTo('transform', `translateX(${CENTER})`, `translateX(${(isRTL ? -124 : 124) + 'px'})`);\n            leavingToolBar.addAnimation(leavingBackBtnText);\n          }\n        } else {\n          // leaving toolbar, forward direction\n          if (!inactiveHeader) {\n            leavingTitle.fromTo('transform', `translateX(${CENTER})`, `translateX(${OFF_LEFT})`).fromTo(OPACITY, 0.99, 0).afterClearStyles([TRANSFORM, OPACITY]);\n          }\n          leavingToolBarItems.fromTo('transform', `translateX(${CENTER})`, `translateX(${OFF_LEFT})`).afterClearStyles([TRANSFORM, OPACITY]);\n          leavingBackButton.afterClearStyles([OPACITY]);\n          leavingTitle.afterClearStyles([OPACITY]);\n          leavingToolBarButtons.afterClearStyles([OPACITY]);\n        }\n      });\n    }\n    return rootAnimation;\n  } catch (err) {\n    throw err;\n  }\n};\n/**\n * The scale of the back button during the animation\n * is computed based on the scale of the large title\n * and vice versa. However, we need to account for slight\n * variations in the size of the large title due to\n * padding and font weight. This value should be used to subtract\n * a small amount from the large title height when computing scales\n * to get more accurate scale results.\n */\nconst LARGE_TITLE_SIZE_OFFSET = 10;\nexport { iosTransitionAnimation, shadow };"], "mappings": ";;;;;;;;;;;AAAA,IAKM,UAEA,kBAGA,QAGA,eASA,eAuBA,4BAiCA,mBAgLA,mBAgIA,wBAmQA;AAjoBN;AAAA;AAGA;AACA;AACA,IAAM,WAAW;AAEjB,IAAM,mBAAmB,aAAW;AAClC,aAAO,SAAS,cAAc,GAAG,OAAO,qBAAqB;AAAA,IAC/D;AACA,IAAM,SAAS,QAAM;AACnB,aAAO,GAAG,cAAc;AAAA,IAC1B;AACA,IAAM,gBAAgB,WAAS;AAC7B,YAAM,OAAO,MAAM,YAAY,aAAa,QAAQ,MAAM,cAAc,UAAU;AAClF,YAAM,QAAQ;AACd,UAAI,QAAQ,MAAM;AAChB,cAAM,YAAY,KAAK,cAAc,2DAA2D;AAChG,eAAO,aAAa,OAAO,UAAU,cAAc,KAAK,IAAI;AAAA,MAC9D;AACA,aAAO,MAAM,cAAc,KAAK;AAAA,IAClC;AACA,IAAM,gBAAgB,CAAC,OAAO,kBAAkB;AAC9C,YAAM,OAAO,MAAM,YAAY,aAAa,QAAQ,MAAM,cAAc,UAAU;AAClF,UAAI,cAAc,CAAC;AACnB,UAAI,QAAQ,MAAM;AAChB,cAAM,YAAY,KAAK,cAAc,2DAA2D;AAChG,YAAI,aAAa,MAAM;AACrB,wBAAc,UAAU,iBAAiB,aAAa;AAAA,QACxD;AAAA,MACF,OAAO;AACL,sBAAc,MAAM,iBAAiB,aAAa;AAAA,MACpD;AACA,iBAAW,WAAW,aAAa;AACjC,cAAM,eAAe,QAAQ,QAAQ,YAAY;AACjD,cAAM,eAAe,gBAAgB,CAAC,aAAa,UAAU,SAAS,mCAAmC;AACzG,cAAM,aAAa,QAAQ,cAAc,iBAAiB;AAC1D,cAAM,kBAAkB,QAAQ,UAAU,SAAS,kBAAkB;AACrE,cAAM,YAAY,QAAQ,SAAS,WAAW,QAAQ,SAAS;AAC/D,YAAI,eAAe,QAAQ,cAAc,mBAAmB,gBAAgB,iBAAiB,CAAC,kBAAkB;AAC9G,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,IAAM,6BAA6B,CAAC,eAAe,KAAK,eAAe,YAAY,cAAc;AAC/F,YAAM,qBAAqB,cAAc,YAAY,aAAa;AAClE,YAAM,oBAAoB,cAAc,SAAS;AACjD,YAAM,qBAAqB,cAAc,UAAU;AACnD,YAAM,oBAAoB,cAAc,WAAW,aAAa;AAChE,YAAM,yBAAyB,uBAAuB,QAAQ,sBAAsB,QAAQ,CAAC;AAC7F,YAAM,0BAA0B,uBAAuB,QAAQ,sBAAsB,QAAQ;AAC7F,UAAI,wBAAwB;AAC1B,cAAM,uBAAuB,kBAAkB,sBAAsB;AACrE,cAAM,wBAAwB,mBAAmB,sBAAsB;AACvE,cAAM,2BAA2B,OAAO,kBAAkB,EAAE,cAAc,cAAc;AAExF,cAAM,4BAA4B,6BAA6B,QAAQ,6BAA6B,SAAS,SAAS,yBAAyB,sBAAsB;AACrK,cAAM,0BAA0B,OAAO,iBAAiB,EAAE,cAAc,gBAAgB;AACxF,cAAM,2BAA2B,wBAAwB,sBAAsB;AAC/E,0BAAkB,eAAe,KAAK,eAAe,mBAAmB,sBAAsB,0BAA0B,uBAAuB,0BAA0B,yBAAyB;AAClM,0BAAkB,eAAe,KAAK,eAAe,oBAAoB,uBAAuB,0BAA0B,2BAA2B,mBAAmB,wBAAwB;AAAA,MAClM,WAAW,yBAAyB;AAClC,cAAM,wBAAwB,mBAAmB,sBAAsB;AACvE,cAAM,uBAAuB,kBAAkB,sBAAsB;AACrE,cAAM,0BAA0B,OAAO,iBAAiB,EAAE,cAAc,cAAc;AAEtF,cAAM,2BAA2B,4BAA4B,QAAQ,4BAA4B,SAAS,SAAS,wBAAwB,sBAAsB;AACjK,cAAM,2BAA2B,OAAO,kBAAkB,EAAE,cAAc,gBAAgB;AAC1F,cAAM,4BAA4B,yBAAyB,sBAAsB;AACjF,0BAAkB,eAAe,KAAK,eAAe,oBAAoB,uBAAuB,2BAA2B,sBAAsB,yBAAyB,wBAAwB;AAClM,0BAAkB,eAAe,KAAK,eAAe,mBAAmB,sBAAsB,yBAAyB,0BAA0B,oBAAoB,yBAAyB;AAAA,MAChM;AACA,aAAO;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AACA,IAAM,oBAAoB,CAAC,eAAe,KAAK,eAAe,cAAc,eAAe,kBAAkB,mBAAmB,cAAc,sBAAsB;AAClK,UAAI,IAAI;AACR,YAAM,2BAA2B,MAAM,eAAe,cAAc,QAAQ,CAAC,QAAQ,GAAG,cAAc,OAAO,CAAC;AAC9G,YAAM,gBAAgB,MAAM,UAAU;AACtC,YAAM,gBAAgB,MAAM,SAAS;AACrC,YAAM,qBAAqB,MAAM,UAAU;AAC3C,UAAI,cAAc;AAClB,UAAI,eAAe;AACnB,UAAI,mBAAmB,SAAS,YAAY;AAC5C,YAAM,iBAAiB;AACvB,UAAI,oBAAoB,mBAAmB;AAQzC,cAAM,+BAA+B,KAAK,iBAAiB,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,SAAS,KAAK,aAAa,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAC1M,sBAAc,kBAAkB,QAAQ,kBAAkB;AAK1D,wBAAgB,kBAAkB,SAAS,2BAA2B,kBAAkB;AAKxF,2BAAmB,6BAA6B,SAAS,WAAW,KAAK,YAAY,MAAM,SAAS,YAAY;AAAA,MAClH;AACA,YAAM,mBAAmB,OAAO,YAAY,EAAE,cAAc,UAAU;AACtE,YAAM,oBAAoB,iBAAiB,sBAAsB;AAOjE,YAAM,8BAA8B,MAAM,GAAG,kBAAkB,QAAQ,KAAK,kBAAkB,QAAQ,cAAc,MAAM,OAAO,GAAG,cAAc,OAAO,kBAAkB,QAAQ,CAAC;AACpL,YAAM,4BAA4B,MAAM,IAAI,OAAO,aAAa,cAAc,KAAK,OAAO,GAAG,cAAc,IAAI;AAO/G,YAAM,8BAA8B,GAAG,kBAAkB,GAAG;AAM5D,YAAM,4BAA4B,GAAG,cAAc,GAAG;AAQtD,YAAM,8BAA8B,CAAC;AAAA,QACnC,QAAQ;AAAA,QACR,WAAW,eAAe,2BAA2B,KAAK,2BAA2B;AAAA,MACvF,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,WAAW,eAAe,yBAAyB,KAAK,yBAAyB;AAAA,MACnF,CAAC;AACD,YAAM,+BAA+B,CAAC;AAAA,QACpC,QAAQ;AAAA,QACR,WAAW,eAAe,yBAAyB,KAAK,yBAAyB;AAAA,MACnF,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,WAAW,eAAe,2BAA2B,KAAK,2BAA2B;AAAA,MACvF,CAAC;AACD,YAAM,sBAAsB,gBAAgB,+BAA+B;AAQ3E,YAAM,yBAAyB,CAAC;AAAA,QAC9B,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,WAAW;AAAA,MACb,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,WAAW;AAAA,MACb,CAAC;AACD,YAAM,0BAA0B,CAAC;AAAA,QAC/B,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,WAAW;AAAA,MACb,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,WAAW;AAAA,MACb,CAAC;AACD,YAAM,iBAAiB,gBAAgB,0BAA0B;AAOjE,YAAM,yBAAyB,CAAC;AAAA,QAC9B,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,WAAW;AAAA,MACb,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,WAAW;AAAA,MACb,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,WAAW;AAAA,MACb,CAAC;AACD,YAAM,0BAA0B,CAAC;AAAA,QAC/B,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,WAAW;AAAA,MACb,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,WAAW;AAAA,MACb,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,WAAW;AAAA,MACb,CAAC;AACD,YAAM,iBAAiB,gBAAgB,0BAA0B;AACjE,YAAM,kCAAkC,gBAAgB;AACxD,YAAM,kCAAkC,gBAAgB;AACxD,YAAM,8BAA8B,gBAAgB;AACpD,YAAM,qBAAqB,iBAAiB,iBAAiB;AAC7D,YAAM,yBAAyB,OAAO,kBAAkB,EAAE,cAAc,cAAc;AACtF,YAAM,yBAAyB,OAAO,kBAAkB,EAAE,cAAc,UAAU;AAClF,yBAAmB,OAAO,aAAa;AACvC,yBAAmB,OAAO,aAAa;AACvC,yBAAmB,OAAO,aAAa;AACvC,yBAAmB,QAAQ,aAAa;AACxC,yBAAmB,WAAW,aAAa;AAC3C,yBAAmB,MAAM,YAAY,WAAW,OAAO;AACvD,yBAAmB,MAAM,YAAY,YAAY,OAAO;AACxD,sCAAgC,WAAW,sBAAsB;AACjE,sCAAgC,WAAW,sBAAsB;AACjE,kCAA4B,WAAW,kBAAkB;AACzD,kCAA4B,aAAa;AAAA,QACvC,UAAU;AAAA,QACV,KAAK;AAAA,QACL,CAAC,kBAAkB,GAAG;AAAA,MACxB,CAAC,EAKG,eAAe,MAAM;AACvB,qBAAa,MAAM,YAAY,WAAW,MAAM;AAChD,2BAAmB,MAAM,YAAY,eAAe,wBAAwB;AAAA,MAC9E,CAAC,EAAE,cAAc,MAAM;AACrB,qBAAa,MAAM,YAAY,WAAW,EAAE;AAC5C,2BAAmB,MAAM,YAAY,WAAW,MAAM;AACtD,2BAAmB,MAAM,eAAe,aAAa;AAAA,MACvD,CAAC,EAAE,UAAU,mBAAmB;AAChC,sCAAgC,aAAa;AAAA,QAC3C,oBAAoB,GAAG,aAAa;AAAA,MACtC,CAAC,EAAE,UAAU,cAAc;AAC3B,sCAAgC,aAAa;AAAA,QAC3C,oBAAoB,GAAG,aAAa;AAAA,MACtC,CAAC,EAAE,UAAU,cAAc;AAC3B,oBAAc,aAAa,CAAC,iCAAiC,iCAAiC,2BAA2B,CAAC;AAAA,IAC5H;AACA,IAAM,oBAAoB,CAAC,eAAe,KAAK,eAAe,cAAc,eAAe,mBAAmB,eAAe,kBAAkB,sBAAsB;AACnK,UAAI,IAAI;AAIR,YAAM,WAAW,MAAM,UAAU;AACjC,YAAM,qBAAqB,MAAM,eAAe,cAAc,KAAK,QAAQ,GAAG,cAAc,IAAI;AAMhG,YAAM,oBAAoB;AAC1B,YAAM,oBAAoB,GAAG,cAAc,GAAG;AAM9C,YAAM,iCAAiC;AACvC,UAAI,kBAAkB,MAAM,IAAI,OAAO,aAAa,cAAc,QAAQ,8BAA8B,OAAO,GAAG,cAAc,IAAI,8BAA8B;AAIlK,UAAI,eAAe;AAInB,YAAM,cAAc;AAMpB,UAAI,YAAY,SAAS,YAAY;AAErC,UAAI,oBAAoB,mBAAmB;AAQzC,0BAAkB,MAAM,IAAI,OAAO,aAAa,kBAAkB,QAAQ,8BAA8B,OAAO,GAAG,kBAAkB,IAAI,8BAA8B;AAiBtK,cAAM,+BAA+B,KAAK,iBAAiB,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,SAAS,KAAK,aAAa,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAC1M,cAAM,cAAc,kBAAkB,QAAQ,kBAAkB;AAChE,uBAAe,kBAAkB,UAAU,kBAAkB,SAAS;AAKtE,oBAAY,6BAA6B,SAAS,WAAW,KAAK,YAAY,MAAM,SAAS,YAAY;AAAA,MAC3G;AAKA,YAAM,qBAAqB,cAAc,MAAM,cAAc,SAAS;AACtE,YAAM,gBAAgB,cAAc,SAAS,eAAe;AAC5D,YAAM,kBAAkB,GAAG,qBAAqB,aAAa;AAC7D,YAAM,sBAAsB,CAAC;AAAA,QAC3B,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,WAAW,eAAe,eAAe,KAAK,eAAe,QAAQ,SAAS;AAAA,MAChF,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,SAAS;AAAA,MACX,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,WAAW,eAAe,iBAAiB,KAAK,iBAAiB,QAAQ,WAAW;AAAA,MACtF,CAAC;AACD,YAAM,qBAAqB,CAAC;AAAA,QAC1B,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,WAAW,eAAe,iBAAiB,KAAK,iBAAiB,QAAQ,WAAW;AAAA,MACtF,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,SAAS;AAAA,MACX,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,WAAW,eAAe,eAAe,KAAK,eAAe,QAAQ,SAAS;AAAA,MAChF,CAAC;AACD,YAAM,YAAY,gBAAgB,sBAAsB;AACxD,YAAM,gBAAgB,iBAAiB,WAAW;AAClD,YAAM,4BAA4B,gBAAgB;AAClD,oBAAc,YAAY,aAAa;AACvC,oBAAc,OAAO,aAAa;AAClC,oBAAc,QAAQ,aAAa;AACnC,gCAA0B,WAAW,aAAa;AAClD,gCAA0B,aAAa;AAAA,QACrC,oBAAoB,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAO/B,QAAQ,GAAG,cAAc,MAAM;AAAA,QAC/B,SAAS;AAAA,QACT,UAAU;AAAA,QACV,CAAC,QAAQ,GAAG;AAAA,MACd,CAAC,EAAE,eAAe,MAAM;AACtB,qBAAa,MAAM,YAAY,WAAW,GAAG;AAAA,MAC/C,CAAC,EAAE,cAAc,MAAM;AACrB,qBAAa,MAAM,YAAY,WAAW,EAAE;AAC5C,sBAAc,MAAM,YAAY,WAAW,MAAM;AAAA,MACnD,CAAC,EAAE,UAAU,SAAS;AACtB,oBAAc,aAAa,yBAAyB;AAAA,IACtD;AACA,IAAM,yBAAyB,CAAC,OAAO,SAAS;AAC9C,UAAI;AACJ,UAAI;AACF,cAAM,SAAS;AACf,cAAM,UAAU;AAChB,cAAM,YAAY;AAClB,cAAM,SAAS;AACf,cAAM,cAAc;AACpB,cAAM,QAAQ,MAAM,cAAc,QAAQ;AAC1C,cAAM,YAAY,QAAQ,WAAW;AACrC,cAAM,WAAW,QAAQ,QAAQ;AACjC,cAAM,aAAa,KAAK;AACxB,cAAM,YAAY,KAAK;AACvB,cAAM,gBAAgB,KAAK,cAAc;AACzC,cAAM,YAAY,WAAW,cAAc,sBAAsB;AACjE,cAAM,YAAY,WAAW,iBAAiB,mEAAmE;AACjH,cAAM,qBAAqB,WAAW,iBAAiB,mCAAmC;AAC1F,cAAM,gBAAgB,gBAAgB;AACtC,cAAM,2BAA2B,gBAAgB;AACjD,sBAAc,WAAW,UAAU,EAAE,WAAW,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,KAAK,MAAM,QAAQ,EAAE,OAAO,KAAK,UAAU,MAAM,EAAE,KAAK,MAAM,EAAE,kBAAkB,oBAAoB;AAEtM,YAAI,aAAa,UAAU,QAAQ,UAAU,QAAW;AACtD,gBAAM,oBAAoB,gBAAgB;AAC1C,4BAAkB,WAAW,KAAK;AAClC,wBAAc,aAAa,iBAAiB;AAAA,QAC9C;AACA,YAAI,CAAC,aAAa,mBAAmB,WAAW,KAAK,UAAU,WAAW,GAAG;AAC3E,mCAAyB,WAAW,WAAW,cAAc,yDAAyD,CAAC;AAAA,QACzH,OAAO;AACL,mCAAyB,WAAW,SAAS;AAC7C,mCAAyB,WAAW,SAAS;AAAA,QAC/C;AACA,sBAAc,aAAa,wBAAwB;AACnD,YAAI,eAAe;AACjB,mCAAyB,kBAAkB,CAAC,OAAO,CAAC,EAAE,OAAO,aAAa,cAAc,QAAQ,KAAK,cAAc,MAAM,GAAG,EAAE,OAAO,SAAS,aAAa,CAAC;AAAA,QAC9J,OAAO;AAEL,mCAAyB,kBAAkB,CAAC,OAAO,CAAC,EAAE,OAAO,aAAa,cAAc,SAAS,KAAK,cAAc,MAAM,GAAG;AAAA,QAC/H;AACA,YAAI,WAAW;AACb,gBAAM,6BAA6B,OAAO,SAAS,EAAE,cAAc,oBAAoB;AACvF,cAAI,4BAA4B;AAC9B,kBAAM,4BAA4B,2BAA2B,cAAc,mBAAmB;AAC9F,kBAAM,6BAA6B,2BAA2B,cAAc,oBAAoB;AAChG,kBAAM,2BAA2B,gBAAgB;AACjD,kBAAM,0BAA0B,gBAAgB;AAChD,kBAAM,2BAA2B,gBAAgB;AACjD,qCAAyB,WAAW,0BAA0B,EAAE,aAAa;AAAA,cAC3E,SAAS;AAAA,cACT,SAAS;AAAA,YACX,CAAC,EAAE,YAAY;AAAA,cACb,SAAS;AAAA,cACT,SAAS;AAAA,YACX,CAAC;AACD,oCAAwB,WAAW,yBAAyB,EAC3D,kBAAkB,CAAC,OAAO,CAAC,EAAE,OAAO,SAAS,GAAG,GAAG;AACpD,qCAAyB,WAAW,0BAA0B,EAC7D,kBAAkB,CAAC,OAAO,CAAC,EAAE,OAAO,SAAS,MAAM,GAAG;AACvD,qCAAyB,aAAa,CAAC,yBAAyB,wBAAwB,CAAC;AACzF,qCAAyB,aAAa,CAAC,wBAAwB,CAAC;AAAA,UAClE;AAAA,QACF;AACA,cAAM,+BAA+B,WAAW,cAAc,qCAAqC;AACnG,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,2BAA2B,eAAe,OAAO,eAAe,YAAY,SAAS;AACzF,2BAAmB,QAAQ,uBAAqB;AAC9C,gBAAM,kBAAkB,gBAAgB;AACxC,0BAAgB,WAAW,iBAAiB;AAC5C,wBAAc,aAAa,eAAe;AAC1C,gBAAM,gBAAgB,gBAAgB;AACtC,wBAAc,WAAW,kBAAkB,cAAc,WAAW,CAAC;AACrE,gBAAM,yBAAyB,gBAAgB;AAC/C,gBAAM,UAAU,MAAM,KAAK,kBAAkB,iBAAiB,0BAA0B,CAAC;AACzF,gBAAM,eAAe,kBAAkB,QAAQ,YAAY;AAC3D,gBAAM,iBAAiB,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,UAAU,SAAS,mCAAmC;AACtJ,cAAI;AACJ,cAAI,eAAe;AACjB,+BAAmB,QAAQ,OAAO,YAAU;AAC1C,oBAAM,mBAAmB,OAAO,UAAU,SAAS,kBAAkB;AACrE,qBAAO,oBAAoB,CAAC,kBAAkB,CAAC;AAAA,YACjD,CAAC;AAAA,UACH,OAAO;AACL,+BAAmB,QAAQ,OAAO,YAAU,CAAC,OAAO,UAAU,SAAS,kBAAkB,CAAC;AAAA,UAC5F;AACA,iCAAuB,WAAW,gBAAgB;AAClD,gBAAM,uBAAuB,gBAAgB;AAC7C,+BAAqB,WAAW,kBAAkB,iBAAiB,8DAA8D,CAAC;AAClI,gBAAM,oBAAoB,gBAAgB;AAC1C,4BAAkB,WAAW,OAAO,iBAAiB,EAAE,cAAc,qBAAqB,CAAC;AAC3F,gBAAM,qBAAqB,gBAAgB;AAC3C,gBAAM,eAAe,kBAAkB,cAAc,iBAAiB;AACtE,cAAI,cAAc;AAChB,+BAAmB,WAAW,YAAY;AAAA,UAC5C;AACA,0BAAgB,aAAa,CAAC,eAAe,wBAAwB,sBAAsB,mBAAmB,kBAAkB,CAAC;AACjI,iCAAuB,OAAO,SAAS,MAAM,CAAC;AAC9C,+BAAqB,OAAO,SAAS,MAAM,CAAC;AAC5C,cAAI,eAAe;AACjB,gBAAI,CAAC,gBAAgB;AACnB,4BAAc,OAAO,aAAa,cAAc,QAAQ,KAAK,cAAc,MAAM,GAAG,EAAE,OAAO,SAAS,MAAM,CAAC;AAAA,YAC/G;AACA,iCAAqB,OAAO,aAAa,cAAc,QAAQ,KAAK,cAAc,MAAM,GAAG;AAE3F,+BAAmB,OAAO,SAAS,MAAM,CAAC;AAAA,UAC5C,OAAO;AAEL,gBAAI,CAAC,8BAA8B;AACjC,4BAAc,OAAO,aAAa,cAAc,SAAS,KAAK,cAAc,MAAM,GAAG,EAAE,OAAO,SAAS,MAAM,CAAC;AAAA,YAChH;AACA,iCAAqB,OAAO,aAAa,cAAc,SAAS,KAAK,cAAc,MAAM,GAAG;AAC5F,8BAAkB,kBAAkB,CAAC,SAAS,WAAW,CAAC;AAC1D,kBAAM,oBAAoB,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa;AACnG,gBAAI,CAAC,mBAAmB;AACtB,gCAAkB,OAAO,SAAS,MAAM,gBAAgB;AAAA,YAC1D,OAAO;AACL,gCAAkB,OAAO,aAAa,QAAQ,sBAAsB,oBAAoB,iBAAiB;AAAA,YAC3G;AAEA,gBAAI,CAAC,SAAS;AACZ,iCAAmB,OAAO,SAAS,MAAM,CAAC;AAAA,YAC5C;AACA,gBAAI,gBAAgB,CAAC,SAAS;AAC5B,oBAAM,sBAAsB,gBAAgB;AAC5C,kCAAoB,WAAW,OAAO,YAAY,EAAE,cAAc,cAAc,CAAC,EAChF,OAAO,aAAa,QAAQ,uBAAuB,qBAAqB,iBAAiB;AAC1F,8BAAgB,aAAa,mBAAmB;AAAA,YAClD;AAAA,UACF;AAAA,QACF,CAAC;AAED,YAAI,WAAW;AACb,gBAAM,iBAAiB,gBAAgB;AACvC,gBAAM,mBAAmB,UAAU,cAAc,sBAAsB;AACvE,gBAAM,oBAAoB,UAAU,iBAAiB,mCAAmC;AACxF,gBAAM,mBAAmB,UAAU,iBAAiB,mEAAmE;AACvH,cAAI,CAAC,oBAAoB,kBAAkB,WAAW,KAAK,iBAAiB,WAAW,GAAG;AACxF,2BAAe,WAAW,UAAU,cAAc,yDAAyD,CAAC;AAAA,UAC9G,OAAO;AACL,2BAAe,WAAW,gBAAgB;AAC1C,2BAAe,WAAW,gBAAgB;AAAA,UAC5C;AACA,wBAAc,aAAa,cAAc;AACzC,cAAI,eAAe;AAEjB,2BAAe,kBAAkB,CAAC,OAAO,CAAC,EAAE,OAAO,aAAa,cAAc,MAAM,KAAK,QAAQ,sBAAsB,kBAAkB;AACzI,kBAAM,cAAc,kBAAkB,SAAS;AAC/C,0BAAc,cAAc,MAAM;AAChC,kBAAI,cAAc,aAAa,MAAM,UAAU;AAC7C,4BAAY,MAAM,YAAY,WAAW,MAAM;AAAA,cACjD;AAAA,YACF,CAAC;AAAA,UACH,OAAO;AAEL,2BAAe,OAAO,aAAa,cAAc,MAAM,KAAK,cAAc,QAAQ,GAAG,EAAE,OAAO,SAAS,GAAG,WAAW;AAAA,UACvH;AACA,cAAI,kBAAkB;AACpB,kBAAM,4BAA4B,OAAO,gBAAgB,EAAE,cAAc,oBAAoB;AAC7F,gBAAI,2BAA2B;AAC7B,oBAAM,2BAA2B,0BAA0B,cAAc,mBAAmB;AAC5F,oBAAM,4BAA4B,0BAA0B,cAAc,oBAAoB;AAC9F,oBAAM,0BAA0B,gBAAgB;AAChD,oBAAM,yBAAyB,gBAAgB;AAC/C,oBAAM,0BAA0B,gBAAgB;AAChD,sCAAwB,WAAW,yBAAyB,EAAE,aAAa;AAAA,gBACzE,SAAS;AAAA,gBACT,SAAS;AAAA,cACX,CAAC,EAAE,YAAY;AAAA,gBACb,SAAS;AAAA,gBACT,SAAS;AAAA,cACX,CAAC;AACD,qCAAuB,WAAW,wBAAwB,EACzD,kBAAkB,CAAC,OAAO,CAAC,EAAE,OAAO,SAAS,KAAK,CAAC;AACpD,sCAAwB,WAAW,yBAAyB,EAC3D,kBAAkB,CAAC,OAAO,CAAC,EAAE,OAAO,SAAS,KAAK,IAAI;AACvD,sCAAwB,aAAa,CAAC,wBAAwB,uBAAuB,CAAC;AACtF,6BAAe,aAAa,CAAC,uBAAuB,CAAC;AAAA,YACvD;AAAA,UACF;AACA,4BAAkB,QAAQ,sBAAoB;AAC5C,kBAAM,iBAAiB,gBAAgB;AACvC,2BAAe,WAAW,gBAAgB;AAC1C,kBAAM,eAAe,gBAAgB;AACrC,yBAAa,WAAW,iBAAiB,cAAc,WAAW,CAAC;AACnE,kBAAM,wBAAwB,gBAAgB;AAC9C,kBAAM,UAAU,iBAAiB,iBAAiB,0BAA0B;AAC5E,kBAAM,eAAe,iBAAiB,QAAQ,YAAY;AAC1D,kBAAM,iBAAiB,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,UAAU,SAAS,mCAAmC;AACtJ,kBAAM,mBAAmB,MAAM,KAAK,OAAO,EAAE,OAAO,YAAU;AAC5D,oBAAM,mBAAmB,OAAO,UAAU,SAAS,kBAAkB;AACrE,qBAAO,oBAAoB,CAAC,kBAAkB,CAAC;AAAA,YACjD,CAAC;AACD,kCAAsB,WAAW,gBAAgB;AACjD,kBAAM,sBAAsB,gBAAgB;AAC5C,kBAAM,wBAAwB,iBAAiB,iBAAiB,8DAA8D;AAC9H,gBAAI,sBAAsB,SAAS,GAAG;AACpC,kCAAoB,WAAW,qBAAqB;AAAA,YACtD;AACA,kBAAM,mBAAmB,gBAAgB;AACzC,6BAAiB,WAAW,OAAO,gBAAgB,EAAE,cAAc,qBAAqB,CAAC;AACzF,kBAAM,oBAAoB,gBAAgB;AAC1C,kBAAM,eAAe,iBAAiB,cAAc,iBAAiB;AACrE,gBAAI,cAAc;AAChB,gCAAkB,WAAW,YAAY;AAAA,YAC3C;AACA,2BAAe,aAAa,CAAC,cAAc,uBAAuB,qBAAqB,mBAAmB,gBAAgB,CAAC;AAC3H,0BAAc,aAAa,cAAc;AAEzC,8BAAkB,OAAO,SAAS,MAAM,CAAC;AACzC,kCAAsB,OAAO,SAAS,MAAM,CAAC;AAC7C,gCAAoB,OAAO,SAAS,MAAM,CAAC;AAC3C,gBAAI,eAAe;AACjB,kBAAI,CAAC,gBAAgB;AAEnB,6BAAa,OAAO,aAAa,cAAc,MAAM,KAAK,QAAQ,sBAAsB,kBAAkB,EAAE,OAAO,SAAS,MAAM,CAAC;AAAA,cACrI;AACA,kCAAoB,OAAO,aAAa,cAAc,MAAM,KAAK,QAAQ,sBAAsB,kBAAkB;AACjH,+BAAiB,kBAAkB,CAAC,SAAS,WAAW,CAAC;AAGzD,oBAAM,oBAAoB,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa;AACnG,kBAAI,CAAC,mBAAmB;AACtB,iCAAiB,OAAO,SAAS,kBAAkB,CAAC;AAAA,cACtD,OAAO;AACL,iCAAiB,OAAO,aAAa,mBAAmB,QAAQ,sBAAsB,kBAAkB;AAAA,cAC1G;AACA,kBAAI,gBAAgB,CAAC,UAAU;AAC7B,sBAAM,qBAAqB,gBAAgB;AAC3C,mCAAmB,WAAW,OAAO,YAAY,EAAE,cAAc,cAAc,CAAC,EAC/E,OAAO,aAAa,cAAc,MAAM,KAAK,eAAe,QAAQ,OAAO,OAAO,IAAI,GAAG;AAC1F,+BAAe,aAAa,kBAAkB;AAAA,cAChD;AAAA,YACF,OAAO;AAEL,kBAAI,CAAC,gBAAgB;AACnB,6BAAa,OAAO,aAAa,cAAc,MAAM,KAAK,cAAc,QAAQ,GAAG,EAAE,OAAO,SAAS,MAAM,CAAC,EAAE,iBAAiB,CAAC,WAAW,OAAO,CAAC;AAAA,cACrJ;AACA,kCAAoB,OAAO,aAAa,cAAc,MAAM,KAAK,cAAc,QAAQ,GAAG,EAAE,iBAAiB,CAAC,WAAW,OAAO,CAAC;AACjI,gCAAkB,iBAAiB,CAAC,OAAO,CAAC;AAC5C,2BAAa,iBAAiB,CAAC,OAAO,CAAC;AACvC,oCAAsB,iBAAiB,CAAC,OAAO,CAAC;AAAA,YAClD;AAAA,UACF,CAAC;AAAA,QACH;AACA,eAAO;AAAA,MACT,SAAS,KAAK;AACZ,cAAM;AAAA,MACR;AAAA,IACF;AAUA,IAAM,0BAA0B;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}