﻿using GoodDriver.Contracts.Users.Events;
using GoodDriver.EventHandlers.Users;
using Microsoft.Extensions.DependencyInjection;
using Rogerio.Cqrs.Bus;
using Rogerio.Cqrs.Events;

namespace GoodDriver.Infrastructure.Installers
{
	public static class EventInstaller
	{
		public static void Install(IServiceCollection services)
		{
			services.AddSingleton<UserEventHandler>();

			services.AddSingleton<IEventBus>(a =>
			{
				var container = a.GetService<MemoryContainerBus>();

				
				//User
				var userEventHandler = a.GetService<UserEventHandler>();
				container.Register<UserCreatedEvent>(userEventHandler);

				
				return container;
			});
		}
	}
}
