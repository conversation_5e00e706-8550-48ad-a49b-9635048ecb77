<ion-content [fullscreen]="true" class="ion-padding">
  <!-- Indicador de carregamento -->
  <div *ngIf="isLoading" class="ion-text-center ion-padding">
    <ion-spinner></ion-spinner>
  </div>

  <div *ngIf="!isLoading">
    <ion-card>
      <ion-card-header>
        <ion-card-title class="ion-text-center">Bem-vindo, {{username}}!</ion-card-title>
      </ion-card-header>
    </ion-card>

    <!-- Alerta de veículo não cadastrado -->
    <ion-card *ngIf="!hasVehicles" color="warning">
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="warning-outline"></ion-icon>
          Atenção
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <p>Você ainda não possui nenhum veículo cadastrado. Para utilizar o rastreamento de viagens, é necessário cadastrar pelo menos um veículo.</p>
        <ion-button expand="block" color="primary" routerLink="/new-vehicle" class="ion-margin-top">
          <ion-icon name="car-outline" slot="start"></ion-icon>
          Cadastrar Veículo
        </ion-button>
      </ion-card-content>
    </ion-card>

  <ion-card>
    <ion-card-header>
      <ion-card-title>Rastreamento de Viagens</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-segment [(ngModel)]="trackingMode" (ionChange)="onTrackingModeChange($event)">
        <ion-segment-button value="manual">
          <ion-label>Manual</ion-label>
        </ion-segment-button>
        <ion-segment-button value="auto">
          <ion-label>Automático</ion-label>
        </ion-segment-button>
      </ion-segment>

      <!-- Modo Manual -->
      <div *ngIf="trackingMode === 'manual'" class="tracking-controls">
        <p class="description">Inicie e finalize suas viagens manualmente.</p>

        <ion-button expand="full" color="success" (click)="startTracking()" [disabled]="tracking || autoDetectionActive">
          <ion-icon name="play" slot="start"></ion-icon>
          Iniciar Viagem
        </ion-button>

        <ion-button expand="full" color="danger" (click)="stopTracking()" [disabled]="!tracking">
          <ion-icon name="stop" slot="start"></ion-icon>
          Finalizar Viagem
        </ion-button>
      </div>

      <!-- Modo Automático -->
      <div *ngIf="trackingMode === 'auto'" class="tracking-controls">
        <p class="description">O aplicativo detectará automaticamente quando você iniciar e finalizar uma viagem.</p>

        <ion-item lines="none" class="status-item">
          <ion-icon [name]="autoDetectionActive ? 'checkmark-circle' : 'close-circle'"
                   [color]="autoDetectionActive ? 'success' : 'medium'"
                   slot="start"></ion-icon>
          <ion-label>
            <h2>Detecção Automática</h2>
            <p>{{ autoDetectionActive ? 'Ativada' : 'Desativada' }}</p>
          </ion-label>
          <ion-toggle [(ngModel)]="autoDetectionActive" (ionChange)="toggleAutoDetection()"
                     [disabled]="tracking"></ion-toggle>
        </ion-item>

        <ion-item lines="none" class="status-item" *ngIf="autoDetectionActive">
          <ion-icon [name]="journeyInProgress ? 'car' : 'car-outline'"
                   [color]="journeyInProgress ? 'primary' : 'medium'"
                   slot="start"></ion-icon>
          <ion-label>
            <h2>Status</h2>
            <p>{{ journeyInProgress ? 'Viagem em andamento' : 'Aguardando movimento' }}</p>
          </ion-label>
        </ion-item>
      </div>
    </ion-card-content>
  </ion-card>

  <ion-list>
    <ion-list-header>
      Viagens Registradas
    </ion-list-header>

    <div *ngIf="journeys?.length === 0" class="ion-text-center">
      <p>Nenhuma viagem registrada.</p>
      <p>Inicie uma viagem para começar a registrar.</p>
    </div>

    <div *ngIf="journeys?.length && journeys.length > 0" class="ion-text-center">
      <p>Você já registrou {{ journeys.length }} viagens.</p>
      <p>Toque em uma viagem para ver mais detalhes.</p>
    </div>

    <ion-item *ngFor="let journey of journeys" [routerLink]="['/journey-details', journey.id]" [queryParams]="{ data: journey | json }">
      <ion-label>
        <h2>Início: {{ journey.startDate | date:'short' }}</h2>
        <p>Fim: {{ journey.endDate ? (journey.endDate | date:'short') : 'Em andamento' }}</p>
        <p>Distância: {{ journey.distance | number:'1.2-2' }} km</p>
      </ion-label>
    </ion-item>
  </ion-list>
  </div>
</ion-content>