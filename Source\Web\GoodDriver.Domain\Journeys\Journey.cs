﻿using GoodDriver.Domain.Devices;
using GoodDriver.Domain.Users;
using GoodDriver.Domain.Vehicles;
using Rogerio.Cqrs.Domains;

namespace GoodDriver.Domain.Journeys
{
	public class Journey : AggregateRoot<string>
	{
        public Journey() //For NHibernate
		{
            
        }
        public Journey(string id) : this()
        {
			this.Id = id;
            this.CreatedOn = this.UpdatedOn = DateTime.Now;
		}
		public Journey(string id, DateTime startDate, DateTime? endDate, float? distance, float? duration, int? score, Vehicle vehicle, Device device) : this(id)
		{	
			if (vehicle == null)
				throw new ArgumentNullException(nameof(vehicle));
			if (device == null)
				throw new ArgumentNullException(nameof(device));

			this.StartDate = startDate;
			this.EndDate = endDate;
			this.Distance = distance;
			this.Duration = duration;
			this.Score = score;
			this.Vehicle = vehicle;
			this.Device = device;
		}		

        public DateTime StartDate { get; private set; }
        public DateTime? EndDate { get; private set; }
		
		public float? Distance { get; private set; }

		/// <summary>
		/// Journey time in seconds
		/// </summary>
		public float? Duration { get; private set; }

        public int? Score { get; private set; }
		
		public User User { get; private set; }

		public Vehicle Vehicle { get; private set; }

        public Device  Device { get; private set; }

		public DateTime CreatedOn { get; private set; }

		public DateTime UpdatedOn { get; private set; }

        public  virtual IList<JourneyInfo> JourneyInfos { get; private set; }
        

		public void FinishJourney(DateTime endDate, float? distance)
		{	
			if (distance == null || !distance.HasValue)
				throw new ArgumentNullException(nameof(distance));			

			this.EndDate = endDate;
			this.Distance = distance;
			this.Duration = (this.StartDate - this.EndDate.Value).Seconds;
			this.UpdatedOn = DateTime.Now;
		}
	}
}
