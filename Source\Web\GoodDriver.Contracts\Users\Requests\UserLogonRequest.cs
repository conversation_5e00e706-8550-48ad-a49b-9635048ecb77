﻿using GoodDriver.Contracts.Users.Responses;
using Rogerio.Cqrs.Commands;

namespace GoodDriver.Contracts.Users.Requests
{
    public class UserLogonRequest : ICommand<UserLogonResponse>
    {
        public UserLogonRequest()
        {
            
        }

        public UserLogonRequest(string email, string password)
        {
            this.Email = email;
            this.Password = password;
        }

        public UserLogonRequest(string id)
        {
            this.Id = id;
        }

        public string Id { get; set; }
        public string Email { get; set; }
        public string Password { get; set; }
    }
}
