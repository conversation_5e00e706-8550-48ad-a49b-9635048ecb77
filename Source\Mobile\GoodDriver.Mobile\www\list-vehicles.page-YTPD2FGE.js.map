{"version": 3, "sources": ["src/app/pages/tabs/vehicles/list-vehicles/list-vehicles.page.ts", "src/app/pages/tabs/vehicles/list-vehicles/list-vehicles.page.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { VehicleService } from 'src/app/core/services/vehicle.service';\nimport { Vehicle } from 'src/app/core/models/vehicle.model';\nimport { IonicModule, RefresherCustomEvent } from '@ionic/angular';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { SessionService } from 'src/app/core/services/session.service';\nimport { ToastService } from 'src/app/core/services/toast.service';\nimport { VehicleStateService } from 'src/app/core/services/vehicle-state.service';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-list-vehicles',\n  templateUrl: './list-vehicles.page.html',\n  styleUrls: ['./list-vehicles.page.scss'],\n  standalone: true,\n  imports: [IonicModule, CommonModule, RouterModule]\n})\nexport class ListVehiclesPage implements OnInit, OnDestroy {\n  vehicles: Vehicle[] = [];\n  isLoading = false;\n\n  // Subscriptions\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private vehicleService: VehicleService,\n    private sessionService: SessionService,\n    private toastService: ToastService,\n    private vehicleStateService: VehicleStateService\n  ) {}\n\n  ngOnInit() {\n    this.loadVehicles();\n\n    // Subscribe to vehicles list changes\n    this.subscriptions.push(\n      this.vehicleStateService.vehicles$.subscribe(vehicles => {\n        if (vehicles.length > 0) {\n          this.vehicles = vehicles;\n        }\n      })\n    );\n  }\n\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  async loadVehicles() {\n    this.isLoading = true;\n    const userId = await this.sessionService.getUserId() || '';\n    try {\n      this.vehicles = await this.vehicleService.listAllLocal(userId);\n    } catch (error) {\n      console.error('Error loading vehicles:', error);\n      this.toastService.showToast('Erro ao carregar veículos', 'danger');\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  /**\n   * Sets a vehicle as the primary vehicle\n   * @param vehicleId The ID of the vehicle to set as primary\n   */\n  async setPrimaryVehicle(vehicleId: string) {\n    const userId = await this.sessionService.getUserId() || '';\n    try {\n      const success = await this.vehicleService.setPrimaryVehicle(vehicleId, userId);\n      if (success) {\n        this.toastService.showToast('Veículo definido como principal');\n        // No need to reload vehicles manually as we're now subscribed to the state service\n        // The VehicleService will update the VehicleStateService,\n        // which will notify all subscribers about the change\n      } else {\n        this.toastService.showToast('Erro ao definir veículo como principal', 'danger');\n      }\n    } catch (error) {\n      console.error('Error setting primary vehicle:', error);\n      this.toastService.showToast('Erro ao definir veículo como principal', 'danger');\n    }\n  }\n\n  async doRefresh(event: RefresherCustomEvent) {\n    await this.loadVehicles();\n    event.target.complete();\n  }\n}", "<!-- src/app/pages/tabs/vehicles/list-vehicles/list-vehicles.page.html -->\n<ion-header>\n  <ion-toolbar>\n    <ion-title>Veículos</ion-title>\n  </ion-toolbar>\n</ion-header>\n\n<ion-content class=\"ion-padding\">\n  <ion-refresher slot=\"fixed\" (ionRefresh)=\"doRefresh($event)\">\n    <ion-refresher-content></ion-refresher-content>\n  </ion-refresher>\n\n  <ion-button expand=\"block\" color=\"primary\" routerLink=\"/new-vehicle\">\n    Cadastrar Novo Veículo\n  </ion-button>\n\n  <!-- Indicador de carregamento -->\n  <div *ngIf=\"isLoading\" class=\"ion-text-center ion-padding\">\n    <ion-spinner></ion-spinner>\n  </div>\n\n  <!-- Lista de veículos-->\n  <ion-list *ngIf=\"vehicles?.length && !isLoading; else noVehicles\">\n    <ion-item-sliding *ngFor=\"let vehicle of vehicles\">\n      <ion-item [routerLink]=\"['/vehicle-details', vehicle.id]\" detail=\"true\">\n        <ion-label>\n          <div class=\"vehicle-header\">\n            <h2>{{ vehicle.plate }}</h2>\n            <ion-badge color=\"success\" *ngIf=\"vehicle.isPrimary\">Principal</ion-badge>\n          </div>\n          <p><b>{{ vehicle.brandName || 'Marca não especificada' }}</b> - {{ vehicle.modelName || 'Modelo não especificado' }}</p>\n          <p>Ano: {{ vehicle.year }}</p>\n        </ion-label>\n      </ion-item>\n\n      <ion-item-options side=\"end\">\n        <ion-item-option color=\"primary\" *ngIf=\"!vehicle.isPrimary\" (click)=\"setPrimaryVehicle(vehicle.id)\">\n          <ion-icon slot=\"icon-only\" name=\"star\"></ion-icon>\n          Principal\n        </ion-item-option>\n      </ion-item-options>\n    </ion-item-sliding>\n  </ion-list>\n\n  <!-- Mensagem caso não existam veículos -->\n  <ng-template #noVehicles>\n    <ion-text color=\"medium\" *ngIf=\"!isLoading\">\n      <p class=\"ion-text-center\">Nenhum veículo cadastrado.</p>\n    </ion-text>\n  </ng-template>\n</ion-content>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiBE,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,oBAAA,GAAA,aAAA;AACF,IAAA,uBAAA;;;;;AASU,IAAA,yBAAA,GAAA,aAAA,EAAA;AAAqD,IAAA,iBAAA,GAAA,WAAA;AAAS,IAAA,uBAAA;;;;;;AAQlE,IAAA,yBAAA,GAAA,mBAAA,EAAA;AAA4D,IAAA,qBAAA,SAAA,SAAA,+GAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,aAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,kBAAA,WAAA,EAAA,CAA6B;IAAA,CAAA;AAChG,IAAA,oBAAA,GAAA,YAAA,EAAA;AACA,IAAA,iBAAA,GAAA,aAAA;AACF,IAAA,uBAAA;;;;;AAhBJ,IAAA,yBAAA,GAAA,kBAAA,EAAmD,GAAA,YAAA,CAAA,EACuB,GAAA,WAAA,EAC3D,GAAA,OAAA,CAAA,EACmB,GAAA,IAAA;AACtB,IAAA,iBAAA,CAAA;AAAmB,IAAA,uBAAA;AACvB,IAAA,qBAAA,GAAA,sEAAA,GAAA,GAAA,aAAA,EAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,GAAA,EAAG,GAAA,GAAA;AAAG,IAAA,iBAAA,CAAA;AAAmD,IAAA,uBAAA;AAAK,IAAA,iBAAA,EAAA;AAAsD,IAAA,uBAAA;AACpH,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,EAAA;AAAuB,IAAA,uBAAA,EAAI,EACpB;AAGd,IAAA,yBAAA,IAAA,oBAAA,EAAA;AACE,IAAA,qBAAA,IAAA,6EAAA,GAAA,GAAA,mBAAA,EAAA;AAIF,IAAA,uBAAA,EAAmB;;;;AAhBT,IAAA,oBAAA;AAAA,IAAA,qBAAA,cAAA,0BAAA,GAAA,KAAA,WAAA,EAAA,CAAA;AAGA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,WAAA,KAAA;AACwB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,WAAA,SAAA;AAExB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,WAAA,aAAA,2BAAA;AAAwD,IAAA,oBAAA;AAAA,IAAA,6BAAA,OAAA,WAAA,aAAA,8BAAA,EAAA;AAC3D,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,SAAA,WAAA,MAAA,EAAA;AAK6B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,WAAA,SAAA;;;;;AAdxC,IAAA,yBAAA,GAAA,UAAA;AACE,IAAA,qBAAA,GAAA,0DAAA,IAAA,GAAA,oBAAA,CAAA;AAmBF,IAAA,uBAAA;;;;AAnBwC,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,QAAA;;;;;AAuBtC,IAAA,yBAAA,GAAA,YAAA,EAAA,EAA4C,GAAA,KAAA,EAAA;AACf,IAAA,iBAAA,GAAA,+BAAA;AAA0B,IAAA,uBAAA,EAAI;;;;;AAD3D,IAAA,qBAAA,GAAA,qDAAA,GAAA,GAAA,YAAA,EAAA;;;;AAA0B,IAAA,qBAAA,QAAA,CAAA,OAAA,SAAA;;;AD9C9B,SAkBa;AAlBb;;;AAGA;AACA;AACA;;;;;;;;;;AAaM,IAAO,oBAAP,MAAO,kBAAgB;MAO3B,YACU,gBACA,gBACA,cACA,qBAAwC;AAHxC,aAAA,iBAAA;AACA,aAAA,iBAAA;AACA,aAAA,eAAA;AACA,aAAA,sBAAA;AAVV,aAAA,WAAsB,CAAA;AACtB,aAAA,YAAY;AAGJ,aAAA,gBAAgC,CAAA;MAOrC;MAEH,WAAQ;AACN,aAAK,aAAY;AAGjB,aAAK,cAAc,KACjB,KAAK,oBAAoB,UAAU,UAAU,cAAW;AACtD,cAAI,SAAS,SAAS,GAAG;AACvB,iBAAK,WAAW;UAClB;QACF,CAAC,CAAC;MAEN;MAEA,cAAW;AAET,aAAK,cAAc,QAAQ,SAAO,IAAI,YAAW,CAAE;MACrD;MAEM,eAAY;;AAChB,eAAK,YAAY;AACjB,gBAAM,UAAS,MAAM,KAAK,eAAe,UAAS,MAAM;AACxD,cAAI;AACF,iBAAK,WAAW,MAAM,KAAK,eAAe,aAAa,MAAM;UAC/D,SAAS,OAAO;AACd,oBAAQ,MAAM,2BAA2B,KAAK;AAC9C,iBAAK,aAAa,UAAU,gCAA6B,QAAQ;UACnE;AACE,iBAAK,YAAY;UACnB;QACF;;;;;;MAMM,kBAAkB,WAAiB;;AACvC,gBAAM,UAAS,MAAM,KAAK,eAAe,UAAS,MAAM;AACxD,cAAI;AACF,kBAAM,UAAU,MAAM,KAAK,eAAe,kBAAkB,WAAW,MAAM;AAC7E,gBAAI,SAAS;AACX,mBAAK,aAAa,UAAU,oCAAiC;YAI/D,OAAO;AACL,mBAAK,aAAa,UAAU,6CAA0C,QAAQ;YAChF;UACF,SAAS,OAAO;AACd,oBAAQ,MAAM,kCAAkC,KAAK;AACrD,iBAAK,aAAa,UAAU,6CAA0C,QAAQ;UAChF;QACF;;MAEM,UAAU,OAA2B;;AACzC,gBAAM,KAAK,aAAY;AACvB,gBAAM,OAAO,SAAQ;QACvB;;;;uCAtEW,mBAAgB,4BAAA,cAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,YAAA,GAAA,4BAAA,mBAAA,CAAA;IAAA;sFAAhB,mBAAgB,WAAA,CAAA,CAAA,mBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,cAAA,EAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,QAAA,SAAA,GAAA,YAAA,GAAA,CAAA,UAAA,SAAA,SAAA,WAAA,cAAA,cAAA,GAAA,CAAA,SAAA,+BAAA,GAAA,MAAA,GAAA,CAAA,GAAA,QAAA,UAAA,GAAA,CAAA,GAAA,mBAAA,aAAA,GAAA,CAAA,GAAA,SAAA,SAAA,GAAA,CAAA,UAAA,QAAA,GAAA,YAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,WAAA,GAAA,MAAA,GAAA,CAAA,QAAA,KAAA,GAAA,CAAA,SAAA,WAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,WAAA,GAAA,OAAA,GAAA,CAAA,QAAA,aAAA,QAAA,MAAA,GAAA,CAAA,SAAA,UAAA,GAAA,MAAA,GAAA,CAAA,SAAA,QAAA,GAAA,CAAA,GAAA,iBAAA,CAAA,GAAA,UAAA,SAAA,0BAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;;ACjB7B,QAAA,yBAAA,GAAA,YAAA,EAAY,GAAA,aAAA,EACG,GAAA,WAAA;AACA,QAAA,iBAAA,GAAA,aAAA;AAAQ,QAAA,uBAAA,EAAY,EACnB;AAGhB,QAAA,yBAAA,GAAA,eAAA,CAAA,EAAiC,GAAA,iBAAA,CAAA;AACH,QAAA,qBAAA,cAAA,SAAA,8DAAA,QAAA;AAAA,UAAA,wBAAA,GAAA;AAAA,iBAAA,sBAAc,IAAA,UAAA,MAAA,CAAiB;QAAA,CAAA;AACzD,QAAA,oBAAA,GAAA,uBAAA;AACF,QAAA,uBAAA;AAEA,QAAA,yBAAA,GAAA,cAAA,CAAA;AACE,QAAA,iBAAA,GAAA,6BAAA;AACF,QAAA,uBAAA;AAGA,QAAA,qBAAA,GAAA,iCAAA,GAAA,GAAA,OAAA,CAAA,EAA2D,IAAA,uCAAA,GAAA,GAAA,YAAA,CAAA,EAKO,IAAA,0CAAA,GAAA,GAAA,eAAA,MAAA,GAAA,gCAAA;AA4BpE,QAAA,uBAAA;;;;AAjCQ,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,SAAA;AAKK,QAAA,oBAAA;AAAA,QAAA,qBAAA,SAAA,IAAA,YAAA,OAAA,OAAA,IAAA,SAAA,WAAA,CAAA,IAAA,SAAA,EAAsC,YAAA,aAAA;;sBDNvC,aAAW,UAAA,WAAA,YAAA,WAAA,SAAA,SAAA,eAAA,gBAAA,gBAAA,UAAA,SAAA,cAAA,qBAAA,YAAA,SAAA,UAAA,YAAA,6BAAE,cAAY,SAAA,MAAE,cAAY,UAAA,GAAA,QAAA,CAAA,scAAA,EAAA,CAAA;AAE7C,IAAO,mBAAP;;0EAAO,kBAAgB,CAAA;cAP5B;2BACW,qBAAmB,YAGjB,MAAI,SACP,CAAC,aAAa,cAAc,YAAY,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iBAAA,QAAA,CAAA,uZAAA,EAAA,CAAA;;;;iFAEvC,kBAAgB,EAAA,WAAA,oBAAA,UAAA,mEAAA,YAAA,GAAA,CAAA;IAAA,GAAA;;;", "names": []}