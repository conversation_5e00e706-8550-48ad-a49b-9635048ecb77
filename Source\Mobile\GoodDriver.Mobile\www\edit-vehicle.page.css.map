{"version": 3, "sources": ["src/app/pages/tabs/vehicles/edit-vehicle/edit-vehicle.page.scss"], "sourcesContent": ["form {\r\n  padding: 16px;\r\n}\r\n\r\nion-list {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.offline-message {\r\n  margin-top: var(--app-spacing-md);\r\n  text-align: center;\r\n\r\n  ion-icon {\r\n    margin-right: var(--app-spacing-xs);\r\n  }\r\n}\r\n\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 50vh;\r\n\r\n  ion-spinner {\r\n    margin-bottom: var(--app-spacing-md);\r\n  }\r\n\r\n  p {\r\n    color: var(--ion-color-medium);\r\n    font-size: 0.9rem;\r\n  }\r\n}\r\n\r\nion-card {\r\n  margin-bottom: var(--app-spacing-md);\r\n\r\n  ion-card-title {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    ion-icon {\r\n      margin-right: var(--app-spacing-sm);\r\n    }\r\n  }\r\n\r\n  ion-card-content {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    ion-icon {\r\n      margin-right: var(--app-spacing-sm);\r\n      font-size: 1.2rem;\r\n    }\r\n  }\r\n}\r\n\r\nion-item {\r\n  margin-bottom: var(--app-spacing-sm);\r\n\r\n  ion-label {\r\n    font-weight: 500;\r\n  }\r\n\r\n  ion-note {\r\n    font-size: 0.8rem;\r\n  }\r\n}\r\n\r\nion-button {\r\n  margin-top: var(--app-spacing-md);\r\n\r\n  ion-spinner {\r\n    margin-right: var(--app-spacing-sm);\r\n  }\r\n}"], "mappings": ";AAAA;AACE,WAAA;;AAGF;AACE,iBAAA;;AAGF,CAAA;AACE,cAAA,IAAA;AACA,cAAA;;AAEA,CAJF,gBAIE;AACE,gBAAA,IAAA;;AAIJ,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,UAAA;;AAEA,CAPF,kBAOE;AACE,iBAAA,IAAA;;AAGF,CAXF,kBAWE;AACE,SAAA,IAAA;AACA,aAAA;;AAIJ;AACE,iBAAA,IAAA;;AAEA,SAAA;AACE,WAAA;AACA,eAAA;;AAEA,SAAA,eAAA;AACE,gBAAA,IAAA;;AAIJ,SAAA;AACE,WAAA;AACA,eAAA;;AAEA,SAAA,iBAAA;AACE,gBAAA,IAAA;AACA,aAAA;;AAKN;AACE,iBAAA,IAAA;;AAEA,SAAA;AACE,eAAA;;AAGF,SAAA;AACE,aAAA;;AAIJ;AACE,cAAA,IAAA;;AAEA,WAAA;AACE,gBAAA,IAAA;;", "names": []}