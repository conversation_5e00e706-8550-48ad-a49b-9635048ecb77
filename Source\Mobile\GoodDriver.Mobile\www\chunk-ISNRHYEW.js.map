{"version": 3, "sources": ["src/app/pages/tabs/home/<USER>", "src/app/pages/tabs/home/<USER>"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { Router, RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { ToastService } from 'src/app/core/services/toast.service';\nimport { SessionService } from 'src/app/core/services/session.service';\nimport { VehicleService } from 'src/app/core/services/vehicle.service';\nimport { VehicleStateService } from 'src/app/core/services/vehicle-state.service';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-home',\n  templateUrl: 'home.page.html',\n  styleUrls: ['home.page.scss'],\n  imports: [\n    IonicModule,\n    CommonModule,\n    ReactiveFormsModule,\n    RouterModule\n  ]\n})\nexport class HomePage implements OnInit, OnDestroy\n{\n  username: string = 'Usuário';\n  hasVehicles: boolean = false;\n  isLoading: boolean = true;\n  primaryVehicle: any = null;\n\n  // Subscriptions\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private router: Router,\n    private toastService: ToastService,\n    private sessionService: SessionService,\n    private vehicleService: VehicleService,\n    private vehicleStateService: VehicleStateService\n  ) {}\n\n  async ngOnInit(): Promise<void> {\n    this.username = await this.sessionService.getUserName() || 'Usuário';\n\n    // Initial check for vehicles\n    await this.checkVehicles();\n\n    // Subscribe to primary vehicle changes\n    this.subscriptions.push(\n      this.vehicleStateService.primaryVehicle$.subscribe(vehicle => {\n        if (vehicle) {\n          this.primaryVehicle = vehicle;\n          this.hasVehicles = true;\n        }\n      })\n    );\n\n    // Subscribe to vehicles list changes\n    this.subscriptions.push(\n      this.vehicleStateService.vehicles$.subscribe(vehicles => {\n        this.hasVehicles = vehicles.length > 0;\n      })\n    );\n  }\n\n  ngOnDestroy(): void {\n    // Unsubscribe from all subscriptions\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  /**\n   * Checks if the user has any vehicles registered\n   */\n  async checkVehicles() {\n    this.isLoading = true;\n    try {\n      const userId = await this.sessionService.getUserId() || '';\n      this.hasVehicles = await this.vehicleService.hasVehicles(userId);\n\n      if (this.hasVehicles) {\n        this.primaryVehicle = await this.vehicleService.getPrimaryVehicle(userId);\n      }\n    } catch (error) {\n      console.error('Error checking vehicles:', error);\n      this.toastService.showToast('Erro ao verificar veículos', 'danger');\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n\n\n\n  logout() {\n    this.sessionService.clearSession();\n    console.log('Logout realizado');\n    this.router.navigate(['/login']);\n  }\n}", "<ion-header [translucent]=\"true\">\n  <ion-toolbar color=\"primary\">\n    <ion-title>\n      Home\n    </ion-title>\n    <ion-buttons slot=\"end\">\n      <ion-button (click)=\"logout()\" fill=\"clear\" color=\"light\">\n        <span class=\"material-icons\">logout</span>\n      </ion-button>\n    </ion-buttons>\n  </ion-toolbar>\n</ion-header>\n\n\n<ion-content [fullscreen]=\"true\" class=\"ion-padding\">\n  <!-- Indicador de carregamento -->\n  <div *ngIf=\"isLoading\" class=\"ion-text-center ion-padding\">\n    <ion-spinner></ion-spinner>\n  </div>\n\n  <div *ngIf=\"!isLoading\">\n    <ion-card>\n      <ion-card-header>\n        <ion-card-title class=\"ion-text-center\">Bem-vindo, {{username}}!</ion-card-title>\n      </ion-card-header>\n\n      <ion-card-content>\n        <p class=\"ion-text-center\">\n          Você está logado com sucesso no aplicativo.\n        </p>\n      </ion-card-content>\n    </ion-card>\n\n    <!-- Alerta de veículo não cadastrado -->\n    <ion-card *ngIf=\"!hasVehicles\" color=\"warning\">\n      <ion-card-header>\n        <ion-card-title>\n          <ion-icon name=\"warning-outline\"></ion-icon>\n          Atenção\n        </ion-card-title>\n      </ion-card-header>\n      <ion-card-content>\n        <p>Você ainda não possui nenhum veículo cadastrado. Para utilizar o rastreamento de viagens, é necessário cadastrar pelo menos um veículo.</p>\n        <ion-button expand=\"block\" color=\"primary\" routerLink=\"/new-vehicle\" class=\"ion-margin-top\">\n          <ion-icon name=\"car-outline\" slot=\"start\"></ion-icon>\n          Cadastrar Veículo\n        </ion-button>\n      </ion-card-content>\n    </ion-card>\n\n    <!-- Informações do veículo principal -->\n    <ion-card *ngIf=\"hasVehicles && primaryVehicle\">\n      <ion-card-header>\n        <ion-card-title>\n          <ion-icon name=\"car-outline\"></ion-icon>\n          Veículo Principal\n        </ion-card-title>\n      </ion-card-header>\n      <ion-card-content>\n        <ion-item lines=\"none\">\n          <ion-label>\n            <h2>{{ primaryVehicle.plate }}</h2>\n            <p>{{ primaryVehicle.brandName }} {{ primaryVehicle.modelName }}</p>\n            <p>Ano: {{ primaryVehicle.year }}</p>\n          </ion-label>\n        </ion-item>\n        <ion-button expand=\"block\" color=\"primary\" routerLink=\"/tabs/vehicles\" class=\"ion-margin-top\">\n          <ion-icon name=\"list-outline\" slot=\"start\"></ion-icon>\n          Gerenciar Veículos\n        </ion-button>\n      </ion-card-content>\n    </ion-card>\n\n    <!-- Card de Sincronização -->\n    <ion-card>\n      <ion-card-header>\n        <ion-card-title>\n          <ion-icon name=\"sync-outline\"></ion-icon>\n          Sincronização\n        </ion-card-title>\n      </ion-card-header>\n      <ion-card-content>\n        <p>Mantenha seus dados sincronizados entre o aplicativo e o servidor.</p>\n        <ion-button expand=\"block\" color=\"primary\" routerLink=\"/tabs/sync\" class=\"ion-margin-top\">\n          <ion-icon name=\"sync-outline\" slot=\"start\"></ion-icon>\n          Ir para Sincronização\n        </ion-button>\n      </ion-card-content>\n    </ion-card>\n  </div>\n</ion-content>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgBE,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,oBAAA,GAAA,aAAA;AACF,IAAA,uBAAA;;;;;AAgBE,IAAA,yBAAA,GAAA,YAAA,EAAA,EAA+C,GAAA,iBAAA,EAC5B,GAAA,gBAAA;AAEb,IAAA,oBAAA,GAAA,YAAA,EAAA;AACA,IAAA,iBAAA,GAAA,iBAAA;AACF,IAAA,uBAAA,EAAiB;AAEnB,IAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,GAAA;AACb,IAAA,iBAAA,GAAA,2JAAA;AAAuI,IAAA,uBAAA;AAC1I,IAAA,yBAAA,GAAA,cAAA,EAAA;AACE,IAAA,oBAAA,GAAA,YAAA,EAAA;AACA,IAAA,iBAAA,IAAA,wBAAA;AACF,IAAA,uBAAA,EAAa,EACI;;;;;AAIrB,IAAA,yBAAA,GAAA,UAAA,EAAgD,GAAA,iBAAA,EAC7B,GAAA,gBAAA;AAEb,IAAA,oBAAA,GAAA,YAAA,EAAA;AACA,IAAA,iBAAA,GAAA,wBAAA;AACF,IAAA,uBAAA,EAAiB;AAEnB,IAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,YAAA,EAAA,EACO,GAAA,WAAA,EACV,GAAA,IAAA;AACL,IAAA,iBAAA,CAAA;AAA0B,IAAA,uBAAA;AAC9B,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,EAAA;AAA6D,IAAA,uBAAA;AAChE,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,EAAA;AAA8B,IAAA,uBAAA,EAAI,EAC3B;AAEd,IAAA,yBAAA,IAAA,cAAA,EAAA;AACE,IAAA,oBAAA,IAAA,YAAA,EAAA;AACA,IAAA,iBAAA,IAAA,yBAAA;AACF,IAAA,uBAAA,EAAa,EACI;;;;AATT,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,eAAA,KAAA;AACD,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,IAAA,OAAA,eAAA,WAAA,KAAA,OAAA,eAAA,WAAA,EAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,SAAA,OAAA,eAAA,MAAA,EAAA;;;;;AA3Cb,IAAA,yBAAA,GAAA,KAAA,EAAwB,GAAA,UAAA,EACZ,GAAA,iBAAA,EACS,GAAA,kBAAA,CAAA;AACyB,IAAA,iBAAA,CAAA;AAAwB,IAAA,uBAAA,EAAiB;AAGnF,IAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,KAAA,CAAA;AAEd,IAAA,iBAAA,GAAA,qDAAA;AACF,IAAA,uBAAA,EAAI,EACa;AAIrB,IAAA,qBAAA,GAAA,qCAAA,IAAA,GAAA,YAAA,EAAA,EAA+C,GAAA,qCAAA,IAAA,GAAA,YAAA,CAAA;AAwC/C,IAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,iBAAA,EACS,IAAA,gBAAA;AAEb,IAAA,oBAAA,IAAA,YAAA,EAAA;AACA,IAAA,iBAAA,IAAA,uBAAA;AACF,IAAA,uBAAA,EAAiB;AAEnB,IAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,GAAA;AACb,IAAA,iBAAA,IAAA,oEAAA;AAAkE,IAAA,uBAAA;AACrE,IAAA,yBAAA,IAAA,cAAA,EAAA;AACE,IAAA,oBAAA,IAAA,YAAA,EAAA;AACA,IAAA,iBAAA,IAAA,+BAAA;AACF,IAAA,uBAAA,EAAa,EACI,EACV;;;;AAjEiC,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,eAAA,OAAA,UAAA,GAAA;AAWjC,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,OAAA,WAAA;AAiBA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,eAAA,OAAA,cAAA;;;ADnDf,IAsBa;AAtBb;;;;AACA;AACA;AACA;AACA;;;;;;;;;AAkBM,IAAO,YAAP,MAAO,UAAQ;MAUnB,YACU,QACA,cACA,gBACA,gBACA,qBAAwC;AAJxC,aAAA,SAAA;AACA,aAAA,eAAA;AACA,aAAA,iBAAA;AACA,aAAA,iBAAA;AACA,aAAA,sBAAA;AAbV,aAAA,WAAmB;AACnB,aAAA,cAAuB;AACvB,aAAA,YAAqB;AACrB,aAAA,iBAAsB;AAGd,aAAA,gBAAgC,CAAA;MAQrC;MAEG,WAAQ;;AACZ,eAAK,YAAW,MAAM,KAAK,eAAe,YAAW,MAAM;AAG3D,gBAAM,KAAK,cAAa;AAGxB,eAAK,cAAc,KACjB,KAAK,oBAAoB,gBAAgB,UAAU,aAAU;AAC3D,gBAAI,SAAS;AACX,mBAAK,iBAAiB;AACtB,mBAAK,cAAc;YACrB;UACF,CAAC,CAAC;AAIJ,eAAK,cAAc,KACjB,KAAK,oBAAoB,UAAU,UAAU,cAAW;AACtD,iBAAK,cAAc,SAAS,SAAS;UACvC,CAAC,CAAC;QAEN;;MAEA,cAAW;AAET,aAAK,cAAc,QAAQ,SAAO,IAAI,YAAW,CAAE;MACrD;;;;MAKM,gBAAa;;AACjB,eAAK,YAAY;AACjB,cAAI;AACF,kBAAM,UAAS,MAAM,KAAK,eAAe,UAAS,MAAM;AACxD,iBAAK,cAAc,MAAM,KAAK,eAAe,YAAY,MAAM;AAE/D,gBAAI,KAAK,aAAa;AACpB,mBAAK,iBAAiB,MAAM,KAAK,eAAe,kBAAkB,MAAM;YAC1E;UACF,SAAS,OAAO;AACd,oBAAQ,MAAM,4BAA4B,KAAK;AAC/C,iBAAK,aAAa,UAAU,iCAA8B,QAAQ;UACpE;AACE,iBAAK,YAAY;UACnB;QACF;;MAKA,SAAM;AACJ,aAAK,eAAe,aAAY;AAChC,gBAAQ,IAAI,kBAAkB;AAC9B,aAAK,OAAO,SAAS,CAAC,QAAQ,CAAC;MACjC;;;uCA1EW,WAAQ,4BAAA,MAAA,GAAA,4BAAA,YAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,mBAAA,CAAA;IAAA;8EAAR,WAAQ,WAAA,CAAA,CAAA,UAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,QAAA,KAAA,GAAA,CAAA,QAAA,SAAA,SAAA,SAAA,GAAA,OAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,YAAA,GAAA,CAAA,SAAA,+BAAA,GAAA,MAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,mBAAA,aAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,SAAA,WAAA,GAAA,MAAA,GAAA,CAAA,QAAA,cAAA,GAAA,CAAA,UAAA,SAAA,SAAA,WAAA,cAAA,cAAA,GAAA,gBAAA,GAAA,CAAA,QAAA,gBAAA,QAAA,OAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,QAAA,iBAAA,GAAA,CAAA,UAAA,SAAA,SAAA,WAAA,cAAA,gBAAA,GAAA,gBAAA,GAAA,CAAA,QAAA,eAAA,QAAA,OAAA,GAAA,CAAA,QAAA,aAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,UAAA,SAAA,SAAA,WAAA,cAAA,kBAAA,GAAA,gBAAA,GAAA,CAAA,QAAA,gBAAA,QAAA,OAAA,CAAA,GAAA,UAAA,SAAA,kBAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;ACtBrB,QAAA,yBAAA,GAAA,cAAA,CAAA,EAAiC,GAAA,eAAA,CAAA,EACF,GAAA,WAAA;AAEzB,QAAA,iBAAA,GAAA,QAAA;AACF,QAAA,uBAAA;AACA,QAAA,yBAAA,GAAA,eAAA,CAAA,EAAwB,GAAA,cAAA,CAAA;AACV,QAAA,qBAAA,SAAA,SAAA,gDAAA;AAAA,iBAAS,IAAA,OAAA;QAAQ,CAAA;AAC3B,QAAA,yBAAA,GAAA,QAAA,CAAA;AAA6B,QAAA,iBAAA,GAAA,QAAA;AAAM,QAAA,uBAAA,EAAO,EAC/B,EACD,EACF;AAIhB,QAAA,yBAAA,GAAA,eAAA,CAAA;AAEE,QAAA,qBAAA,GAAA,yBAAA,GAAA,GAAA,OAAA,CAAA,EAA2D,IAAA,0BAAA,IAAA,GAAA,OAAA,CAAA;AA0E7D,QAAA,uBAAA;;;AA1FY,QAAA,qBAAA,eAAA,IAAA;AAcC,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,cAAA,IAAA;AAEL,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,SAAA;AAIA,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,CAAA,IAAA,SAAA;;;MDJJ;MAAW;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MACX;MAAY;MACZ;MACA;MAAY;IAAA,GAAA,QAAA,CAAA,siBAAA,EAAA,CAAA;AAGV,IAAO,WAAP;;0EAAO,UAAQ,CAAA;cAXpB;2BACW,YAAU,SAGX;UACP;UACA;UACA;UACA;WACD,UAAA,moGAAA,QAAA,CAAA,mfAAA,EAAA,CAAA;;;;iFAEU,UAAQ,EAAA,WAAA,YAAA,UAAA,wCAAA,YAAA,GAAA,CAAA;IAAA,GAAA;;;", "names": []}