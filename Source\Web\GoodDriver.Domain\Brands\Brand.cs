﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Domain.Brands
{
	public class Brand
	{
        public Brand()
        {
			this.CreatedOn = DateTime.Now;
		}

        public Brand(int id)
        {
            this.Id = id;
        }

        public Brand(string name) : this()
		{
			this.Name = name;
			UpdatedOn = DateTime.Now;
		}

        public int Id { get; private set; }

        public string Name { get; private set; }

		public DateTime CreatedOn { get; private set; }

		public DateTime UpdatedOn { get; private set; }

		public string Ico { get; private set; }
	}

}
