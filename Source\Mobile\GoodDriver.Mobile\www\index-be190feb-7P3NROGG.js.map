{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/index-be190feb.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index-a5d50daf.js';\nimport { q as pointerCoord } from './helpers-d94bc8ad.js';\nimport './index-cfd9c1f2.js';\nconst startTapClick = config => {\n  if (doc === undefined) {\n    return;\n  }\n  let lastActivated = 0;\n  let activatableEle;\n  let activeRipple;\n  let activeDefer;\n  const useRippleEffect = config.getBoolean('animated', true) && config.getBoolean('rippleEffect', true);\n  const clearDefers = new WeakMap();\n  const cancelActive = () => {\n    if (activeDefer) clearTimeout(activeDefer);\n    activeDefer = undefined;\n    if (activatableEle) {\n      removeActivated(false);\n      activatableEle = undefined;\n    }\n  };\n  const pointerDown = ev => {\n    // Ignore right clicks\n    if (activatableEle || ev.button === 2) {\n      return;\n    }\n    setActivatedElement(getActivatableTarget(ev), ev);\n  };\n  const pointerUp = ev => {\n    setActivatedElement(undefined, ev);\n  };\n  const setActivatedElement = (el, ev) => {\n    // do nothing\n    if (el && el === activatableEle) {\n      return;\n    }\n    if (activeDefer) clearTimeout(activeDefer);\n    activeDefer = undefined;\n    const {\n      x,\n      y\n    } = pointerCoord(ev);\n    // deactivate selected\n    if (activatableEle) {\n      if (clearDefers.has(activatableEle)) {\n        throw new Error('internal error');\n      }\n      if (!activatableEle.classList.contains(ACTIVATED)) {\n        addActivated(activatableEle, x, y);\n      }\n      removeActivated(true);\n    }\n    // activate\n    if (el) {\n      const deferId = clearDefers.get(el);\n      if (deferId) {\n        clearTimeout(deferId);\n        clearDefers.delete(el);\n      }\n      el.classList.remove(ACTIVATED);\n      const callback = () => {\n        addActivated(el, x, y);\n        activeDefer = undefined;\n      };\n      if (isInstant(el)) {\n        callback();\n      } else {\n        activeDefer = setTimeout(callback, ADD_ACTIVATED_DEFERS);\n      }\n    }\n    activatableEle = el;\n  };\n  const addActivated = (el, x, y) => {\n    lastActivated = Date.now();\n    el.classList.add(ACTIVATED);\n    if (!useRippleEffect) return;\n    const rippleEffect = getRippleEffect(el);\n    if (rippleEffect !== null) {\n      removeRipple();\n      activeRipple = rippleEffect.addRipple(x, y);\n    }\n  };\n  const removeRipple = () => {\n    if (activeRipple !== undefined) {\n      activeRipple.then(remove => remove());\n      activeRipple = undefined;\n    }\n  };\n  const removeActivated = smooth => {\n    removeRipple();\n    const active = activatableEle;\n    if (!active) {\n      return;\n    }\n    const time = CLEAR_STATE_DEFERS - Date.now() + lastActivated;\n    if (smooth && time > 0 && !isInstant(active)) {\n      const deferId = setTimeout(() => {\n        active.classList.remove(ACTIVATED);\n        clearDefers.delete(active);\n      }, CLEAR_STATE_DEFERS);\n      clearDefers.set(active, deferId);\n    } else {\n      active.classList.remove(ACTIVATED);\n    }\n  };\n  doc.addEventListener('ionGestureCaptured', cancelActive);\n  doc.addEventListener('pointerdown', pointerDown, true);\n  doc.addEventListener('pointerup', pointerUp, true);\n  /**\n   * Tap click effects such as the ripple effect should\n   * not happen when scrolling. For example, if a user scrolls\n   * the page but also happens to do a touchstart on a button\n   * as part of the scroll, the ripple effect should not\n   * be dispatched. The ripple effect should only happen\n   * if the button is activated and the page is not scrolling.\n   *\n   * pointercancel is dispatched on a gesture when scrolling\n   * starts, so this lets us avoid having to listen for\n   * ion-content's scroll events.\n   */\n  doc.addEventListener('pointercancel', cancelActive, true);\n};\n// TODO(FW-2832): type\nconst getActivatableTarget = ev => {\n  if (ev.composedPath !== undefined) {\n    /**\n     * composedPath returns EventTarget[]. However,\n     * objects other than Element can be targets too.\n     * For example, AudioContext can be a target. In this\n     * case, we know that the event is a UIEvent so we\n     * can assume that the path will contain either Element\n     * or ShadowRoot.\n     */\n    const path = ev.composedPath();\n    for (let i = 0; i < path.length - 2; i++) {\n      const el = path[i];\n      if (!(el instanceof ShadowRoot) && el.classList.contains('ion-activatable')) {\n        return el;\n      }\n    }\n  } else {\n    return ev.target.closest('.ion-activatable');\n  }\n};\nconst isInstant = el => {\n  return el.classList.contains('ion-activatable-instant');\n};\nconst getRippleEffect = el => {\n  if (el.shadowRoot) {\n    const ripple = el.shadowRoot.querySelector('ion-ripple-effect');\n    if (ripple) {\n      return ripple;\n    }\n  }\n  return el.querySelector('ion-ripple-effect');\n};\nconst ACTIVATED = 'ion-activated';\nconst ADD_ACTIVATED_DEFERS = 100;\nconst CLEAR_STATE_DEFERS = 150;\nexport { startTapClick };"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAMM,eAwHA,sBAqBA,WAGA,iBASA,WACA,sBACA;AAjKN;AAAA;AAGA;AACA;AACA;AACA,IAAM,gBAAgB,YAAU;AAC9B,UAAI,QAAQ,QAAW;AACrB;AAAA,MACF;AACA,UAAI,gBAAgB;AACpB,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,YAAM,kBAAkB,OAAO,WAAW,YAAY,IAAI,KAAK,OAAO,WAAW,gBAAgB,IAAI;AACrG,YAAM,cAAc,oBAAI,QAAQ;AAChC,YAAM,eAAe,MAAM;AACzB,YAAI,YAAa,cAAa,WAAW;AACzC,sBAAc;AACd,YAAI,gBAAgB;AAClB,0BAAgB,KAAK;AACrB,2BAAiB;AAAA,QACnB;AAAA,MACF;AACA,YAAM,cAAc,QAAM;AAExB,YAAI,kBAAkB,GAAG,WAAW,GAAG;AACrC;AAAA,QACF;AACA,4BAAoB,qBAAqB,EAAE,GAAG,EAAE;AAAA,MAClD;AACA,YAAM,YAAY,QAAM;AACtB,4BAAoB,QAAW,EAAE;AAAA,MACnC;AACA,YAAM,sBAAsB,CAAC,IAAI,OAAO;AAEtC,YAAI,MAAM,OAAO,gBAAgB;AAC/B;AAAA,QACF;AACA,YAAI,YAAa,cAAa,WAAW;AACzC,sBAAc;AACd,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,aAAa,EAAE;AAEnB,YAAI,gBAAgB;AAClB,cAAI,YAAY,IAAI,cAAc,GAAG;AACnC,kBAAM,IAAI,MAAM,gBAAgB;AAAA,UAClC;AACA,cAAI,CAAC,eAAe,UAAU,SAAS,SAAS,GAAG;AACjD,yBAAa,gBAAgB,GAAG,CAAC;AAAA,UACnC;AACA,0BAAgB,IAAI;AAAA,QACtB;AAEA,YAAI,IAAI;AACN,gBAAM,UAAU,YAAY,IAAI,EAAE;AAClC,cAAI,SAAS;AACX,yBAAa,OAAO;AACpB,wBAAY,OAAO,EAAE;AAAA,UACvB;AACA,aAAG,UAAU,OAAO,SAAS;AAC7B,gBAAM,WAAW,MAAM;AACrB,yBAAa,IAAI,GAAG,CAAC;AACrB,0BAAc;AAAA,UAChB;AACA,cAAI,UAAU,EAAE,GAAG;AACjB,qBAAS;AAAA,UACX,OAAO;AACL,0BAAc,WAAW,UAAU,oBAAoB;AAAA,UACzD;AAAA,QACF;AACA,yBAAiB;AAAA,MACnB;AACA,YAAM,eAAe,CAAC,IAAI,GAAG,MAAM;AACjC,wBAAgB,KAAK,IAAI;AACzB,WAAG,UAAU,IAAI,SAAS;AAC1B,YAAI,CAAC,gBAAiB;AACtB,cAAM,eAAe,gBAAgB,EAAE;AACvC,YAAI,iBAAiB,MAAM;AACzB,uBAAa;AACb,yBAAe,aAAa,UAAU,GAAG,CAAC;AAAA,QAC5C;AAAA,MACF;AACA,YAAM,eAAe,MAAM;AACzB,YAAI,iBAAiB,QAAW;AAC9B,uBAAa,KAAK,YAAU,OAAO,CAAC;AACpC,yBAAe;AAAA,QACjB;AAAA,MACF;AACA,YAAM,kBAAkB,YAAU;AAChC,qBAAa;AACb,cAAM,SAAS;AACf,YAAI,CAAC,QAAQ;AACX;AAAA,QACF;AACA,cAAM,OAAO,qBAAqB,KAAK,IAAI,IAAI;AAC/C,YAAI,UAAU,OAAO,KAAK,CAAC,UAAU,MAAM,GAAG;AAC5C,gBAAM,UAAU,WAAW,MAAM;AAC/B,mBAAO,UAAU,OAAO,SAAS;AACjC,wBAAY,OAAO,MAAM;AAAA,UAC3B,GAAG,kBAAkB;AACrB,sBAAY,IAAI,QAAQ,OAAO;AAAA,QACjC,OAAO;AACL,iBAAO,UAAU,OAAO,SAAS;AAAA,QACnC;AAAA,MACF;AACA,UAAI,iBAAiB,sBAAsB,YAAY;AACvD,UAAI,iBAAiB,eAAe,aAAa,IAAI;AACrD,UAAI,iBAAiB,aAAa,WAAW,IAAI;AAajD,UAAI,iBAAiB,iBAAiB,cAAc,IAAI;AAAA,IAC1D;AAEA,IAAM,uBAAuB,QAAM;AACjC,UAAI,GAAG,iBAAiB,QAAW;AASjC,cAAM,OAAO,GAAG,aAAa;AAC7B,iBAAS,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK;AACxC,gBAAM,KAAK,KAAK,CAAC;AACjB,cAAI,EAAE,cAAc,eAAe,GAAG,UAAU,SAAS,iBAAiB,GAAG;AAC3E,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF,OAAO;AACL,eAAO,GAAG,OAAO,QAAQ,kBAAkB;AAAA,MAC7C;AAAA,IACF;AACA,IAAM,YAAY,QAAM;AACtB,aAAO,GAAG,UAAU,SAAS,yBAAyB;AAAA,IACxD;AACA,IAAM,kBAAkB,QAAM;AAC5B,UAAI,GAAG,YAAY;AACjB,cAAM,SAAS,GAAG,WAAW,cAAc,mBAAmB;AAC9D,YAAI,QAAQ;AACV,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO,GAAG,cAAc,mBAAmB;AAAA,IAC7C;AACA,IAAM,YAAY;AAClB,IAAM,uBAAuB;AAC7B,IAAM,qBAAqB;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}