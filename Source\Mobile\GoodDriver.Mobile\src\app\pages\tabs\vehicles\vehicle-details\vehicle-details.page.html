<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/tabs/vehicles"></ion-back-button>
    </ion-buttons>
    <ion-title>Detalhes do Veículo</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
  <!-- Indicador de carregamento -->
  <div *ngIf="isLoading" class="ion-text-center ion-padding">
    <ion-spinner></ion-spinner>
  </div>

  <!-- Detalhes do veículo -->
  <div *ngIf="!isLoading && vehicle">
    <ion-card>
      <ion-card-header>
        <div class="vehicle-header">
          <ion-card-title>{{ vehicle.plate }}</ion-card-title>
          <ion-badge color="success" *ngIf="vehicle.isPrimary">Veículo Principal</ion-badge>
        </div>
        <ion-card-subtitle>{{ vehicle.brandName }} {{ vehicle.modelName }}</ion-card-subtitle>
      </ion-card-header>

      <ion-card-content>
        <ion-list lines="none">
          <ion-item>
            <ion-icon name="calendar-outline" slot="start" color="primary"></ion-icon>
            <ion-label>
              <h3>Ano</h3>
              <p>{{ vehicle.year }}</p>
            </ion-label>
          </ion-item>

          <ion-item *ngIf="vehicle.version">
            <ion-icon name="options-outline" slot="start" color="primary"></ion-icon>
            <ion-label>
              <h3>Versão</h3>
              <p>{{ vehicle.version }}</p>
            </ion-label>
          </ion-item>

          <ion-item *ngIf="vehicle.policyNumber">
            <ion-icon name="shield-checkmark-outline" slot="start" color="primary"></ion-icon>
            <ion-label>
              <h3>Número da Apólice</h3>
              <p>{{ vehicle.policyNumber }}</p>
            </ion-label>
          </ion-item>

          <ion-item>
            <ion-icon name="time-outline" slot="start" color="primary"></ion-icon>
            <ion-label>
              <h3>Data de Cadastro</h3>
              <p>{{ vehicle.createdOn | date:'dd/MM/yyyy HH:mm' }}</p>
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>

    <!-- Botões de ação -->
    <div class="ion-padding">
      <ion-button expand="block" color="success" *ngIf="!vehicle.isPrimary" (click)="setPrimaryVehicle()">
        <ion-icon name="star" slot="start"></ion-icon>
        Definir como Veículo Principal
      </ion-button>

      <ion-button expand="block" color="primary" (click)="goToEditVehicle(vehicle.id)">
        <ion-icon name="create-outline" slot="start"></ion-icon>
        Editar Veículo
      </ion-button>

      <ion-button expand="block" color="danger" disabled>
        <ion-icon name="trash-outline" slot="start"></ion-icon>
        Excluir Veículo
      </ion-button>
    </div>
  </div>

  <!-- Mensagem de erro -->
  <ion-text color="danger" *ngIf="!isLoading && !vehicle">
    <p class="ion-text-center">Veículo não encontrado.</p>
  </ion-text>
</ion-content>
