// import { Routes } from '@angular/router';

// export const routes: Routes = [
//   {
//     path: 'home',
//     loadComponent: () => import('./home/<USER>').then((m) => m.HomePage),
//   },
//   {
//     path: '',
//     redirectTo: 'splash',
//     pathMatch: 'full',
//   },
//   { path: 'splash', loadChildren: () => import('/pages/splash/splash.module').then(m => m.SplashPageModule) },
//   { path: 'login', loadChildren: () => import('./pages/login/login.module').then(m => m.LoginPageModule) },
//   { path: 'register', loadChildren: () => import('./pages/register/register.module').then(m => m.RegisterPageModule) },
//   { path: 'recover', loadChildren: () => import('./pages/recover/recover.module').then(m => m.RecoverPageModule) },
// ];


import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';
import { SplashPage } from 'src/app/pages/splash/splash.page';
import { LoginPage } from 'src/app/pages/login/login.page';
import { SignupPage } from 'src/app/pages/signup/signup.page';
import { HomePage } from 'src/app/pages/tabs/home/<USER>';
import { TabsPage } from './pages/tabs/tabs.page';
import { NewVehiclePage } from './pages/tabs/vehicles/create-vehicle/new-vehicle.page';
import { SyncPage } from './pages/sync/sync.page';

export const routes: Routes = [
  { path: '', redirectTo: 'splash', pathMatch: 'full' },

  {
    path: 'splash',
    component: SplashPage,
  },
  {
    path: 'login',
    component: LoginPage,
  },
  {
    path: 'signup',
    component: SignupPage,
  },
  {
    path: 'home',
    component: HomePage,
  },
  {
    path: 'tabs',
    loadChildren: () => import('./pages/tabs/tabs.module').then(m => m.TabsPageModule),
    //component: TabsPage,
  },
  {
    path: 'journey-details/:journeyId',
    loadComponent: () => import('./pages/tabs/journeys/journey-details/journey-details.page').then(m => m.JourneyDetailsPage),
  },
  {
    path: 'new-vehicle',
    loadComponent: () => import('./pages/tabs/vehicles/create-vehicle/new-vehicle.page').then(m => m.NewVehiclePage),
  },
  {
    path: 'vehicle-details/:id',
    loadComponent: () => import('./pages/tabs/vehicles/vehicle-details/vehicle-details.page').then(m => m.VehicleDetailsPage),
  },
  {
    path: 'sync',
    loadComponent: () => import('./pages/sync/sync.page').then(m => m.SyncPage),
  },
];

/*{ path: 'signup', loadComponent: () => import('./pages/signup/signup.page').then(m => m.SignupPage) },
  { path: 'recover', loadComponent: () => import('./pages/recover/recover.page').then(m => m.RecoverPage) },*/
@NgModule({
  imports: [RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })],
  exports: [RouterModule],
})
export class AppRoutingModule {}