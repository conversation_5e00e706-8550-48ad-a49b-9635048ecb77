﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Common.TimeZones
{
    public class TimeZoneManager
    {
        public const string ContextKey = "TZOS";

        public static TimeZoneInfo Default => TimeZoneInfo.FindSystemTimeZoneById("E. South America Standard Time");

        public static DateTime DateTimeNow => TimeZoneInfo.ConvertTime(DateTime.UtcNow, TimeZoneInfo.Utc, Current);

        public static DateTimeOffset Now => TimeZoneInfo.ConvertTime(DateTimeOffset.Now, Current);

        public static TimeSpan OffsetNow => Current.GetUtcOffset(DateTimeOffset.Now);

        public static TimeZoneInfo Current => Default;

        public static DateTime? ConvertToLocal(DateTimeOffset? value)
        {
            if (value.HasValue)
            {
                return TimeZoneInfo.ConvertTimeFromUtc(value.Value.UtcDateTime, Current);
            }

            return null;
        }

        public static DateTime? ConvertToUtc(DateTimeOffset? value)
        {
            if (value.HasValue)
            {
                return value.Value.UtcDateTime;
            }

            return null;
        }

        public static DateTimeOffset? ConvertFromLocal(DateTime? dateLocal)
        {
            if (dateLocal.HasValue)
            {
                DateTime dateTime = DateTime.SpecifyKind(dateLocal.Value, DateTimeKind.Unspecified);
                return new DateTimeOffset(dateTime, Current.GetUtcOffset(dateTime));
            }

            return null;
        }

        public static DateTimeOffset? ConvertFromUtc(DateTime? dateLocal)
        {
            if (dateLocal.HasValue)
            {
                return TimeZoneInfo.ConvertTime(new DateTimeOffset(dateLocal.Value, TimeSpan.Zero), Current);
            }

            return null;
        }

        public static DateTimeOffset? ConvertFrom(DateTimeOffset? value)
        {
            if (value.HasValue)
            {
                return TimeZoneInfo.ConvertTime(value.Value, Current);
            }

            return null;
        }

        public static DateTimeOffset ConvertFrom(DateTimeOffset value)
        {
            return TimeZoneInfo.ConvertTime(value, Current);
        }

        public static bool IsDaylightSavingTime(DateTime timeToCheck)
        {
            return Current.IsDaylightSavingTime(timeToCheck);
        }

        public static TimeSpan OffsetParse(string offset)
        {
            int num = 0;
            int minutes = 0;
            string[] array = offset.Split(':');
            if (array.Length != 0)
            {
                num = Convert.ToInt32(array[0]);
                if (array.Length > 1)
                {
                    minutes = Convert.ToInt32(array[1]);
                }

                return new TimeSpan(num, minutes, 0);
            }

            return default(TimeSpan);
        }

        public static DateTimeOffset? Parse(object value, IFormatProvider formatProvider = null)
        {
            if (value != null && value != DBNull.Value)
            {
                if (formatProvider != null)
                {
                    return DateTimeOffset.Parse(value.ToString(), formatProvider);
                }

                return DateTimeOffset.Parse(value.ToString());
            }

            return null;
        }
    }
}
