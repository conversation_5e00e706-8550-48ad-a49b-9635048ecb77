{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-route_4.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, f as getElement, h, e as Host } from './index-527b9e34.js';\nimport { c as componentOnReady, p as debounce } from './helpers-d94bc8ad.js';\nimport { d as printIonError, p as printIonWarning } from './index-cfd9c1f2.js';\nimport { o as openURL, c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nconst Route = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionRouteDataChanged = createEvent(this, \"ionRouteDataChanged\", 7);\n    this.url = '';\n    this.component = undefined;\n    this.componentProps = undefined;\n    this.beforeLeave = undefined;\n    this.beforeEnter = undefined;\n  }\n  onUpdate(newValue) {\n    this.ionRouteDataChanged.emit(newValue);\n  }\n  onComponentProps(newValue, oldValue) {\n    if (newValue === oldValue) {\n      return;\n    }\n    const keys1 = newValue ? Object.keys(newValue) : [];\n    const keys2 = oldValue ? Object.keys(oldValue) : [];\n    if (keys1.length !== keys2.length) {\n      this.onUpdate(newValue);\n      return;\n    }\n    for (const key of keys1) {\n      if (newValue[key] !== oldValue[key]) {\n        this.onUpdate(newValue);\n        return;\n      }\n    }\n  }\n  connectedCallback() {\n    this.ionRouteDataChanged.emit();\n  }\n  static get watchers() {\n    return {\n      \"url\": [\"onUpdate\"],\n      \"component\": [\"onUpdate\"],\n      \"componentProps\": [\"onComponentProps\"]\n    };\n  }\n};\nconst RouteRedirect = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionRouteRedirectChanged = createEvent(this, \"ionRouteRedirectChanged\", 7);\n    this.from = undefined;\n    this.to = undefined;\n  }\n  propDidChange() {\n    this.ionRouteRedirectChanged.emit();\n  }\n  connectedCallback() {\n    this.ionRouteRedirectChanged.emit();\n  }\n  static get watchers() {\n    return {\n      \"from\": [\"propDidChange\"],\n      \"to\": [\"propDidChange\"]\n    };\n  }\n};\nconst ROUTER_INTENT_NONE = 'root';\nconst ROUTER_INTENT_FORWARD = 'forward';\nconst ROUTER_INTENT_BACK = 'back';\n\n/** Join the non empty segments with \"/\". */\nconst generatePath = segments => {\n  const path = segments.filter(s => s.length > 0).join('/');\n  return '/' + path;\n};\nconst generateUrl = (segments, useHash, queryString) => {\n  let url = generatePath(segments);\n  if (useHash) {\n    url = '#' + url;\n  }\n  if (queryString !== undefined) {\n    url += '?' + queryString;\n  }\n  return url;\n};\nconst writeSegments = (history, root, useHash, segments, direction, state, queryString) => {\n  const url = generateUrl([...parsePath(root).segments, ...segments], useHash, queryString);\n  if (direction === ROUTER_INTENT_FORWARD) {\n    history.pushState(state, '', url);\n  } else {\n    history.replaceState(state, '', url);\n  }\n};\n/**\n * Transforms a chain to a list of segments.\n *\n * Notes:\n * - parameter segments of the form :param are replaced with their value,\n * - null is returned when a value is missing for any parameter segment.\n */\nconst chainToSegments = chain => {\n  const segments = [];\n  for (const route of chain) {\n    for (const segment of route.segments) {\n      if (segment[0] === ':') {\n        // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n        const param = route.params && route.params[segment.slice(1)];\n        if (!param) {\n          return null;\n        }\n        segments.push(param);\n      } else if (segment !== '') {\n        segments.push(segment);\n      }\n    }\n  }\n  return segments;\n};\n/**\n * Removes the prefix segments from the path segments.\n *\n * Return:\n * - null when the path segments do not start with the passed prefix,\n * - the path segments after the prefix otherwise.\n */\nconst removePrefix = (prefix, segments) => {\n  if (prefix.length > segments.length) {\n    return null;\n  }\n  if (prefix.length <= 1 && prefix[0] === '') {\n    return segments;\n  }\n  for (let i = 0; i < prefix.length; i++) {\n    if (prefix[i] !== segments[i]) {\n      return null;\n    }\n  }\n  if (segments.length === prefix.length) {\n    return [''];\n  }\n  return segments.slice(prefix.length);\n};\nconst readSegments = (loc, root, useHash) => {\n  const prefix = parsePath(root).segments;\n  const pathname = useHash ? loc.hash.slice(1) : loc.pathname;\n  const segments = parsePath(pathname).segments;\n  return removePrefix(prefix, segments);\n};\n/**\n * Parses the path to:\n * - segments an array of '/' separated parts,\n * - queryString (undefined when no query string).\n */\nconst parsePath = path => {\n  let segments = [''];\n  let queryString;\n  if (path != null) {\n    const qsStart = path.indexOf('?');\n    if (qsStart > -1) {\n      queryString = path.substring(qsStart + 1);\n      path = path.substring(0, qsStart);\n    }\n    segments = path.split('/').map(s => s.trim()).filter(s => s.length > 0);\n    if (segments.length === 0) {\n      segments = [''];\n    }\n  }\n  return {\n    segments,\n    queryString\n  };\n};\nconst printRoutes = routes => {\n  console.group(`[ion-core] ROUTES[${routes.length}]`);\n  for (const chain of routes) {\n    const segments = [];\n    chain.forEach(r => segments.push(...r.segments));\n    const ids = chain.map(r => r.id);\n    console.debug(`%c ${generatePath(segments)}`, 'font-weight: bold; padding-left: 20px', '=>\\t', `(${ids.join(', ')})`);\n  }\n  console.groupEnd();\n};\nconst printRedirects = redirects => {\n  console.group(`[ion-core] REDIRECTS[${redirects.length}]`);\n  for (const redirect of redirects) {\n    if (redirect.to) {\n      console.debug('FROM: ', `$c ${generatePath(redirect.from)}`, 'font-weight: bold', ' TO: ', `$c ${generatePath(redirect.to.segments)}`, 'font-weight: bold');\n    }\n  }\n  console.groupEnd();\n};\n\n/**\n * Activates the passed route chain.\n *\n * There must be exactly one outlet per route entry in the chain.\n *\n * The methods calls setRouteId on each of the outlet with the corresponding route entry in the chain.\n * setRouteId will create or select the view in the outlet.\n */\nconst writeNavState = async (root, chain, direction, index, changed = false, animation) => {\n  try {\n    // find next navigation outlet in the DOM\n    const outlet = searchNavNode(root);\n    // make sure we can continue interacting the DOM, otherwise abort\n    if (index >= chain.length || !outlet) {\n      return changed;\n    }\n    await new Promise(resolve => componentOnReady(outlet, resolve));\n    const route = chain[index];\n    const result = await outlet.setRouteId(route.id, route.params, direction, animation);\n    // if the outlet changed the page, reset navigation to neutral (no direction)\n    // this means nested outlets will not animate\n    if (result.changed) {\n      direction = ROUTER_INTENT_NONE;\n      changed = true;\n    }\n    // recursively set nested outlets\n    changed = await writeNavState(result.element, chain, direction, index + 1, changed, animation);\n    // once all nested outlets are visible let's make the parent visible too,\n    // using markVisible prevents flickering\n    if (result.markVisible) {\n      await result.markVisible();\n    }\n    return changed;\n  } catch (e) {\n    printIonError('[ion-router] - Exception in writeNavState:', e);\n    return false;\n  }\n};\n/**\n * Recursively walks the outlet in the DOM.\n *\n * The function returns a list of RouteID corresponding to each of the outlet and the last outlet without a RouteID.\n */\nconst readNavState = async root => {\n  const ids = [];\n  let outlet;\n  let node = root;\n  // eslint-disable-next-line no-cond-assign\n  while (outlet = searchNavNode(node)) {\n    const id = await outlet.getRouteId();\n    if (id) {\n      node = id.element;\n      id.element = undefined;\n      ids.push(id);\n    } else {\n      break;\n    }\n  }\n  return {\n    ids,\n    outlet\n  };\n};\nconst waitUntilNavNode = () => {\n  if (searchNavNode(document.body)) {\n    return Promise.resolve();\n  }\n  return new Promise(resolve => {\n    window.addEventListener('ionNavWillLoad', () => resolve(), {\n      once: true\n    });\n  });\n};\n/** Selector for all the outlets supported by the router. */\nconst OUTLET_SELECTOR = ':not([no-router]) ion-nav, :not([no-router]) ion-tabs, :not([no-router]) ion-router-outlet';\nconst searchNavNode = root => {\n  if (!root) {\n    return undefined;\n  }\n  if (root.matches(OUTLET_SELECTOR)) {\n    return root;\n  }\n  const outlet = root.querySelector(OUTLET_SELECTOR);\n  return outlet !== null && outlet !== void 0 ? outlet : undefined;\n};\n\n/**\n * Returns whether the given redirect matches the given path segments.\n *\n * A redirect matches when the segments of the path and redirect.from are equal.\n * Note that segments are only checked until redirect.from contains a '*' which matches any path segment.\n * The path ['some', 'path', 'to', 'page'] matches both ['some', 'path', 'to', 'page'] and ['some', 'path', '*'].\n */\nconst matchesRedirect = (segments, redirect) => {\n  const {\n    from,\n    to\n  } = redirect;\n  if (to === undefined) {\n    return false;\n  }\n  if (from.length > segments.length) {\n    return false;\n  }\n  for (let i = 0; i < from.length; i++) {\n    const expected = from[i];\n    if (expected === '*') {\n      return true;\n    }\n    if (expected !== segments[i]) {\n      return false;\n    }\n  }\n  return from.length === segments.length;\n};\n/** Returns the first redirect matching the path segments or undefined when no match found. */\nconst findRouteRedirect = (segments, redirects) => {\n  return redirects.find(redirect => matchesRedirect(segments, redirect));\n};\nconst matchesIDs = (ids, chain) => {\n  const len = Math.min(ids.length, chain.length);\n  let score = 0;\n  for (let i = 0; i < len; i++) {\n    const routeId = ids[i];\n    const routeChain = chain[i];\n    // Skip results where the route id does not match the chain at the same index\n    if (routeId.id.toLowerCase() !== routeChain.id) {\n      break;\n    }\n    if (routeId.params) {\n      const routeIdParams = Object.keys(routeId.params);\n      // Only compare routes with the chain that have the same number of parameters.\n      if (routeIdParams.length === routeChain.segments.length) {\n        // Maps the route's params into a path based on the path variable names,\n        // to compare against the route chain format.\n        //\n        // Before:\n        // ```ts\n        // {\n        //  params: {\n        //    s1: 'a',\n        //    s2: 'b'\n        //  }\n        // }\n        // ```\n        //\n        // After:\n        // ```ts\n        // [':s1',':s2']\n        // ```\n        //\n        const pathWithParams = routeIdParams.map(key => `:${key}`);\n        for (let j = 0; j < pathWithParams.length; j++) {\n          // Skip results where the path variable is not a match\n          if (pathWithParams[j].toLowerCase() !== routeChain.segments[j]) {\n            break;\n          }\n          // Weight path matches for the same index higher.\n          score++;\n        }\n      }\n    }\n    // Weight id matches\n    score++;\n  }\n  return score;\n};\n/**\n * Matches the segments against the chain.\n *\n * Returns:\n * - null when there is no match,\n * - a chain with the params properties updated with the parameter segments on match.\n */\nconst matchesSegments = (segments, chain) => {\n  const inputSegments = new RouterSegments(segments);\n  let matchesDefault = false;\n  let allparams;\n  for (let i = 0; i < chain.length; i++) {\n    const chainSegments = chain[i].segments;\n    if (chainSegments[0] === '') {\n      matchesDefault = true;\n    } else {\n      for (const segment of chainSegments) {\n        const data = inputSegments.next();\n        // data param\n        if (segment[0] === ':') {\n          if (data === '') {\n            return null;\n          }\n          allparams = allparams || [];\n          const params = allparams[i] || (allparams[i] = {});\n          params[segment.slice(1)] = data;\n        } else if (data !== segment) {\n          return null;\n        }\n      }\n      matchesDefault = false;\n    }\n  }\n  const matches = matchesDefault ? matchesDefault === (inputSegments.next() === '') : true;\n  if (!matches) {\n    return null;\n  }\n  if (allparams) {\n    return chain.map((route, i) => ({\n      id: route.id,\n      segments: route.segments,\n      params: mergeParams(route.params, allparams[i]),\n      beforeEnter: route.beforeEnter,\n      beforeLeave: route.beforeLeave\n    }));\n  }\n  return chain;\n};\n/**\n * Merges the route parameter objects.\n * Returns undefined when both parameters are undefined.\n */\nconst mergeParams = (a, b) => {\n  return a || b ? Object.assign(Object.assign({}, a), b) : undefined;\n};\n/**\n * Finds the best match for the ids in the chains.\n *\n * Returns the best match or null when no match is found.\n * When a chain is returned the parameters are updated from the RouteIDs.\n * That is they contain both the componentProps of the <ion-route> and the parameter segment.\n */\nconst findChainForIDs = (ids, chains) => {\n  let match = null;\n  let maxMatches = 0;\n  for (const chain of chains) {\n    const score = matchesIDs(ids, chain);\n    if (score > maxMatches) {\n      match = chain;\n      maxMatches = score;\n    }\n  }\n  if (match) {\n    return match.map((route, i) => {\n      var _a;\n      return {\n        id: route.id,\n        segments: route.segments,\n        params: mergeParams(route.params, (_a = ids[i]) === null || _a === void 0 ? void 0 : _a.params)\n      };\n    });\n  }\n  return null;\n};\n/**\n * Finds the best match for the segments in the chains.\n *\n * Returns the best match or null when no match is found.\n * When a chain is returned the parameters are updated from the segments.\n * That is they contain both the componentProps of the <ion-route> and the parameter segments.\n */\nconst findChainForSegments = (segments, chains) => {\n  let match = null;\n  let bestScore = 0;\n  for (const chain of chains) {\n    const matchedChain = matchesSegments(segments, chain);\n    if (matchedChain !== null) {\n      const score = computePriority(matchedChain);\n      if (score > bestScore) {\n        bestScore = score;\n        match = matchedChain;\n      }\n    }\n  }\n  return match;\n};\n/**\n * Computes the priority of a chain.\n *\n * Parameter segments are given a lower priority over fixed segments.\n *\n * Considering the following 2 chains matching the path /path/to/page:\n * - /path/to/:where\n * - /path/to/page\n *\n * The second one will be given a higher priority because \"page\" is a fixed segment (vs \":where\", a parameter segment).\n */\nconst computePriority = chain => {\n  let score = 1;\n  let level = 1;\n  for (const route of chain) {\n    for (const segment of route.segments) {\n      if (segment[0] === ':') {\n        score += Math.pow(1, level);\n      } else if (segment !== '') {\n        score += Math.pow(2, level);\n      }\n      level++;\n    }\n  }\n  return score;\n};\nclass RouterSegments {\n  constructor(segments) {\n    this.segments = segments.slice();\n  }\n  next() {\n    if (this.segments.length > 0) {\n      return this.segments.shift();\n    }\n    return '';\n  }\n}\nconst readProp = (el, prop) => {\n  if (prop in el) {\n    return el[prop];\n  }\n  if (el.hasAttribute(prop)) {\n    return el.getAttribute(prop);\n  }\n  return null;\n};\n/**\n * Extracts the redirects (that is <ion-route-redirect> elements inside the root).\n *\n * The redirects are returned as a list of RouteRedirect.\n */\nconst readRedirects = root => {\n  return Array.from(root.children).filter(el => el.tagName === 'ION-ROUTE-REDIRECT').map(el => {\n    const to = readProp(el, 'to');\n    return {\n      from: parsePath(readProp(el, 'from')).segments,\n      to: to == null ? undefined : parsePath(to)\n    };\n  });\n};\n/**\n * Extracts all the routes (that is <ion-route> elements inside the root).\n *\n * The routes are returned as a list of chains - the flattened tree.\n */\nconst readRoutes = root => {\n  return flattenRouterTree(readRouteNodes(root));\n};\n/**\n * Reads the route nodes as a tree modeled after the DOM tree of <ion-route> elements.\n *\n * Note: routes without a component are ignored together with their children.\n */\nconst readRouteNodes = node => {\n  return Array.from(node.children).filter(el => el.tagName === 'ION-ROUTE' && el.component).map(el => {\n    const component = readProp(el, 'component');\n    return {\n      segments: parsePath(readProp(el, 'url')).segments,\n      id: component.toLowerCase(),\n      params: el.componentProps,\n      beforeLeave: el.beforeLeave,\n      beforeEnter: el.beforeEnter,\n      children: readRouteNodes(el)\n    };\n  });\n};\n/**\n * Flattens a RouterTree in a list of chains.\n *\n * Each chain represents a path from the root node to a terminal node.\n */\nconst flattenRouterTree = nodes => {\n  const chains = [];\n  for (const node of nodes) {\n    flattenNode([], chains, node);\n  }\n  return chains;\n};\n/** Flattens a route node recursively and push each branch to the chains list. */\nconst flattenNode = (chain, chains, node) => {\n  chain = [...chain, {\n    id: node.id,\n    segments: node.segments,\n    params: node.params,\n    beforeLeave: node.beforeLeave,\n    beforeEnter: node.beforeEnter\n  }];\n  if (node.children.length === 0) {\n    chains.push(chain);\n    return;\n  }\n  for (const child of node.children) {\n    flattenNode(chain, chains, child);\n  }\n};\nconst Router = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionRouteWillChange = createEvent(this, \"ionRouteWillChange\", 7);\n    this.ionRouteDidChange = createEvent(this, \"ionRouteDidChange\", 7);\n    this.previousPath = null;\n    this.busy = false;\n    this.state = 0;\n    this.lastState = 0;\n    this.root = '/';\n    this.useHash = true;\n  }\n  async componentWillLoad() {\n    await waitUntilNavNode();\n    const canProceed = await this.runGuards(this.getSegments());\n    if (canProceed !== true) {\n      if (typeof canProceed === 'object') {\n        const {\n          redirect\n        } = canProceed;\n        const path = parsePath(redirect);\n        this.setSegments(path.segments, ROUTER_INTENT_NONE, path.queryString);\n        await this.writeNavStateRoot(path.segments, ROUTER_INTENT_NONE);\n      }\n    } else {\n      await this.onRoutesChanged();\n    }\n  }\n  componentDidLoad() {\n    window.addEventListener('ionRouteRedirectChanged', debounce(this.onRedirectChanged.bind(this), 10));\n    window.addEventListener('ionRouteDataChanged', debounce(this.onRoutesChanged.bind(this), 100));\n  }\n  async onPopState() {\n    const direction = this.historyDirection();\n    let segments = this.getSegments();\n    const canProceed = await this.runGuards(segments);\n    if (canProceed !== true) {\n      if (typeof canProceed === 'object') {\n        segments = parsePath(canProceed.redirect).segments;\n      } else {\n        return false;\n      }\n    }\n    return this.writeNavStateRoot(segments, direction);\n  }\n  onBackButton(ev) {\n    ev.detail.register(0, processNextHandler => {\n      this.back();\n      processNextHandler();\n    });\n  }\n  /** @internal */\n  async canTransition() {\n    const canProceed = await this.runGuards();\n    if (canProceed !== true) {\n      if (typeof canProceed === 'object') {\n        return canProceed.redirect;\n      } else {\n        return false;\n      }\n    }\n    return true;\n  }\n  /**\n   * Navigate to the specified path.\n   *\n   * @param path The path to navigate to.\n   * @param direction The direction of the animation. Defaults to `\"forward\"`.\n   */\n  async push(path, direction = 'forward', animation) {\n    var _a;\n    if (path.startsWith('.')) {\n      const currentPath = (_a = this.previousPath) !== null && _a !== void 0 ? _a : '/';\n      // Convert currentPath to an URL by pre-pending a protocol and a host to resolve the relative path.\n      const url = new URL(path, `https://host/${currentPath}`);\n      path = url.pathname + url.search;\n    }\n    let parsedPath = parsePath(path);\n    const canProceed = await this.runGuards(parsedPath.segments);\n    if (canProceed !== true) {\n      if (typeof canProceed === 'object') {\n        parsedPath = parsePath(canProceed.redirect);\n      } else {\n        return false;\n      }\n    }\n    this.setSegments(parsedPath.segments, direction, parsedPath.queryString);\n    return this.writeNavStateRoot(parsedPath.segments, direction, animation);\n  }\n  /** Go back to previous page in the window.history. */\n  back() {\n    window.history.back();\n    return Promise.resolve(this.waitPromise);\n  }\n  /** @internal */\n  async printDebug() {\n    printRoutes(readRoutes(this.el));\n    printRedirects(readRedirects(this.el));\n  }\n  /** @internal */\n  async navChanged(direction) {\n    if (this.busy) {\n      printIonWarning('[ion-router] - Router is busy, navChanged was cancelled.');\n      return false;\n    }\n    const {\n      ids,\n      outlet\n    } = await readNavState(window.document.body);\n    const routes = readRoutes(this.el);\n    const chain = findChainForIDs(ids, routes);\n    if (!chain) {\n      printIonWarning('[ion-router] - No matching URL for', ids.map(i => i.id));\n      return false;\n    }\n    const segments = chainToSegments(chain);\n    if (!segments) {\n      printIonWarning('[ion-router] - Router could not match path because some required param is missing.');\n      return false;\n    }\n    this.setSegments(segments, direction);\n    await this.safeWriteNavState(outlet, chain, ROUTER_INTENT_NONE, segments, null, ids.length);\n    return true;\n  }\n  /** This handler gets called when a `ion-route-redirect` component is added to the DOM or if the from or to property of such node changes. */\n  onRedirectChanged() {\n    const segments = this.getSegments();\n    if (segments && findRouteRedirect(segments, readRedirects(this.el))) {\n      this.writeNavStateRoot(segments, ROUTER_INTENT_NONE);\n    }\n  }\n  /** This handler gets called when a `ion-route` component is added to the DOM or if the from or to property of such node changes. */\n  onRoutesChanged() {\n    return this.writeNavStateRoot(this.getSegments(), ROUTER_INTENT_NONE);\n  }\n  historyDirection() {\n    var _a;\n    const win = window;\n    if (win.history.state === null) {\n      this.state++;\n      win.history.replaceState(this.state, win.document.title, (_a = win.document.location) === null || _a === void 0 ? void 0 : _a.href);\n    }\n    const state = win.history.state;\n    const lastState = this.lastState;\n    this.lastState = state;\n    if (state > lastState || state >= lastState && lastState > 0) {\n      return ROUTER_INTENT_FORWARD;\n    }\n    if (state < lastState) {\n      return ROUTER_INTENT_BACK;\n    }\n    return ROUTER_INTENT_NONE;\n  }\n  async writeNavStateRoot(segments, direction, animation) {\n    if (!segments) {\n      printIonError('[ion-router] - URL is not part of the routing set.');\n      return false;\n    }\n    // lookup redirect rule\n    const redirects = readRedirects(this.el);\n    const redirect = findRouteRedirect(segments, redirects);\n    let redirectFrom = null;\n    if (redirect) {\n      const {\n        segments: toSegments,\n        queryString\n      } = redirect.to;\n      this.setSegments(toSegments, direction, queryString);\n      redirectFrom = redirect.from;\n      segments = toSegments;\n    }\n    // lookup route chain\n    const routes = readRoutes(this.el);\n    const chain = findChainForSegments(segments, routes);\n    if (!chain) {\n      printIonError('[ion-router] - The path does not match any route.');\n      return false;\n    }\n    // write DOM give\n    return this.safeWriteNavState(document.body, chain, direction, segments, redirectFrom, 0, animation);\n  }\n  async safeWriteNavState(node, chain, direction, segments, redirectFrom, index = 0, animation) {\n    const unlock = await this.lock();\n    let changed = false;\n    try {\n      changed = await this.writeNavState(node, chain, direction, segments, redirectFrom, index, animation);\n    } catch (e) {\n      printIonError('[ion-router] - Exception in safeWriteNavState:', e);\n    }\n    unlock();\n    return changed;\n  }\n  async lock() {\n    const p = this.waitPromise;\n    let resolve;\n    this.waitPromise = new Promise(r => resolve = r);\n    if (p !== undefined) {\n      await p;\n    }\n    return resolve;\n  }\n  /**\n   * Executes the beforeLeave hook of the source route and the beforeEnter hook of the target route if they exist.\n   *\n   * When the beforeLeave hook does not return true (to allow navigating) then that value is returned early and the beforeEnter is executed.\n   * Otherwise the beforeEnterHook hook of the target route is executed.\n   */\n  async runGuards(to = this.getSegments(), from) {\n    if (from === undefined) {\n      from = parsePath(this.previousPath).segments;\n    }\n    if (!to || !from) {\n      return true;\n    }\n    const routes = readRoutes(this.el);\n    const fromChain = findChainForSegments(from, routes);\n    // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n    const beforeLeaveHook = fromChain && fromChain[fromChain.length - 1].beforeLeave;\n    const canLeave = beforeLeaveHook ? await beforeLeaveHook() : true;\n    if (canLeave === false || typeof canLeave === 'object') {\n      return canLeave;\n    }\n    const toChain = findChainForSegments(to, routes);\n    // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n    const beforeEnterHook = toChain && toChain[toChain.length - 1].beforeEnter;\n    return beforeEnterHook ? beforeEnterHook() : true;\n  }\n  async writeNavState(node, chain, direction, segments, redirectFrom, index = 0, animation) {\n    if (this.busy) {\n      printIonWarning('[ion-router] - Router is busy, transition was cancelled.');\n      return false;\n    }\n    this.busy = true;\n    // generate route event and emit will change\n    const routeEvent = this.routeChangeEvent(segments, redirectFrom);\n    if (routeEvent) {\n      this.ionRouteWillChange.emit(routeEvent);\n    }\n    const changed = await writeNavState(node, chain, direction, index, false, animation);\n    this.busy = false;\n    // emit did change\n    if (routeEvent) {\n      this.ionRouteDidChange.emit(routeEvent);\n    }\n    return changed;\n  }\n  setSegments(segments, direction, queryString) {\n    this.state++;\n    writeSegments(window.history, this.root, this.useHash, segments, direction, this.state, queryString);\n  }\n  getSegments() {\n    return readSegments(window.location, this.root, this.useHash);\n  }\n  routeChangeEvent(toSegments, redirectFromSegments) {\n    const from = this.previousPath;\n    const to = generatePath(toSegments);\n    this.previousPath = to;\n    if (to === from) {\n      return null;\n    }\n    const redirectedFrom = redirectFromSegments ? generatePath(redirectFromSegments) : null;\n    return {\n      from,\n      redirectedFrom,\n      to\n    };\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nconst routerLinkCss = \":host{--background:transparent;--color:var(--ion-color-primary, #0054e9);background:var(--background);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}a{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit}\";\nconst IonRouterLinkStyle0 = routerLinkCss;\nconst RouterLink = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.onClick = ev => {\n      openURL(this.href, ev, this.routerDirection, this.routerAnimation);\n    };\n    this.color = undefined;\n    this.href = undefined;\n    this.rel = undefined;\n    this.routerDirection = 'forward';\n    this.routerAnimation = undefined;\n    this.target = undefined;\n  }\n  render() {\n    const mode = getIonMode(this);\n    const attrs = {\n      href: this.href,\n      rel: this.rel,\n      target: this.target\n    };\n    return h(Host, {\n      key: '11183264fb6ae0db9a7a47c71b6862d60001b834',\n      onClick: this.onClick,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'ion-activatable': true\n      })\n    }, h(\"a\", Object.assign({\n      key: '3e0e5242161cb0df593d6d573e51b8ba750065a1'\n    }, attrs), h(\"slot\", {\n      key: '5bd808e98a4627bb1236f0d955f4b32971355417'\n    })));\n  }\n};\nRouterLink.style = IonRouterLinkStyle0;\nexport { Route as ion_route, RouteRedirect as ion_route_redirect, Router as ion_router, RouterLink as ion_router_link };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAQM,OAyCA,eAoBA,oBACA,uBACA,oBAGA,cAIA,aAUA,eAeA,iBAyBA,cAiBA,cAWA,WAmBA,aAUA,gBAkBA,eAmCA,cAoBA,kBAWA,iBACA,eAkBA,iBAuBA,mBAGA,YAuDA,iBA6CA,aAUA,iBA6BA,sBA0BA,iBAeA,gBAWA,UAcA,eAcA,YAQA,gBAkBA,mBAQA,aAgBA,QA+QA,eACA,qBACA;AAx1BN;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA,IAAM,QAAQ,MAAM;AAAA,MAClB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,sBAAsB,YAAY,MAAM,uBAAuB,CAAC;AACrE,aAAK,MAAM;AACX,aAAK,YAAY;AACjB,aAAK,iBAAiB;AACtB,aAAK,cAAc;AACnB,aAAK,cAAc;AAAA,MACrB;AAAA,MACA,SAAS,UAAU;AACjB,aAAK,oBAAoB,KAAK,QAAQ;AAAA,MACxC;AAAA,MACA,iBAAiB,UAAU,UAAU;AACnC,YAAI,aAAa,UAAU;AACzB;AAAA,QACF;AACA,cAAM,QAAQ,WAAW,OAAO,KAAK,QAAQ,IAAI,CAAC;AAClD,cAAM,QAAQ,WAAW,OAAO,KAAK,QAAQ,IAAI,CAAC;AAClD,YAAI,MAAM,WAAW,MAAM,QAAQ;AACjC,eAAK,SAAS,QAAQ;AACtB;AAAA,QACF;AACA,mBAAW,OAAO,OAAO;AACvB,cAAI,SAAS,GAAG,MAAM,SAAS,GAAG,GAAG;AACnC,iBAAK,SAAS,QAAQ;AACtB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,oBAAoB;AAClB,aAAK,oBAAoB,KAAK;AAAA,MAChC;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,OAAO,CAAC,UAAU;AAAA,UAClB,aAAa,CAAC,UAAU;AAAA,UACxB,kBAAkB,CAAC,kBAAkB;AAAA,QACvC;AAAA,MACF;AAAA,IACF;AACA,IAAM,gBAAgB,MAAM;AAAA,MAC1B,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,0BAA0B,YAAY,MAAM,2BAA2B,CAAC;AAC7E,aAAK,OAAO;AACZ,aAAK,KAAK;AAAA,MACZ;AAAA,MACA,gBAAgB;AACd,aAAK,wBAAwB,KAAK;AAAA,MACpC;AAAA,MACA,oBAAoB;AAClB,aAAK,wBAAwB,KAAK;AAAA,MACpC;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,QAAQ,CAAC,eAAe;AAAA,UACxB,MAAM,CAAC,eAAe;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,IAAM,qBAAqB;AAC3B,IAAM,wBAAwB;AAC9B,IAAM,qBAAqB;AAG3B,IAAM,eAAe,cAAY;AAC/B,YAAM,OAAO,SAAS,OAAO,OAAK,EAAE,SAAS,CAAC,EAAE,KAAK,GAAG;AACxD,aAAO,MAAM;AAAA,IACf;AACA,IAAM,cAAc,CAAC,UAAU,SAAS,gBAAgB;AACtD,UAAI,MAAM,aAAa,QAAQ;AAC/B,UAAI,SAAS;AACX,cAAM,MAAM;AAAA,MACd;AACA,UAAI,gBAAgB,QAAW;AAC7B,eAAO,MAAM;AAAA,MACf;AACA,aAAO;AAAA,IACT;AACA,IAAM,gBAAgB,CAAC,SAAS,MAAM,SAAS,UAAU,WAAW,OAAO,gBAAgB;AACzF,YAAM,MAAM,YAAY,CAAC,GAAG,UAAU,IAAI,EAAE,UAAU,GAAG,QAAQ,GAAG,SAAS,WAAW;AACxF,UAAI,cAAc,uBAAuB;AACvC,gBAAQ,UAAU,OAAO,IAAI,GAAG;AAAA,MAClC,OAAO;AACL,gBAAQ,aAAa,OAAO,IAAI,GAAG;AAAA,MACrC;AAAA,IACF;AAQA,IAAM,kBAAkB,WAAS;AAC/B,YAAM,WAAW,CAAC;AAClB,iBAAW,SAAS,OAAO;AACzB,mBAAW,WAAW,MAAM,UAAU;AACpC,cAAI,QAAQ,CAAC,MAAM,KAAK;AAEtB,kBAAM,QAAQ,MAAM,UAAU,MAAM,OAAO,QAAQ,MAAM,CAAC,CAAC;AAC3D,gBAAI,CAAC,OAAO;AACV,qBAAO;AAAA,YACT;AACA,qBAAS,KAAK,KAAK;AAAA,UACrB,WAAW,YAAY,IAAI;AACzB,qBAAS,KAAK,OAAO;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAQA,IAAM,eAAe,CAAC,QAAQ,aAAa;AACzC,UAAI,OAAO,SAAS,SAAS,QAAQ;AACnC,eAAO;AAAA,MACT;AACA,UAAI,OAAO,UAAU,KAAK,OAAO,CAAC,MAAM,IAAI;AAC1C,eAAO;AAAA,MACT;AACA,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,OAAO,CAAC,MAAM,SAAS,CAAC,GAAG;AAC7B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,UAAI,SAAS,WAAW,OAAO,QAAQ;AACrC,eAAO,CAAC,EAAE;AAAA,MACZ;AACA,aAAO,SAAS,MAAM,OAAO,MAAM;AAAA,IACrC;AACA,IAAM,eAAe,CAAC,KAAK,MAAM,YAAY;AAC3C,YAAM,SAAS,UAAU,IAAI,EAAE;AAC/B,YAAM,WAAW,UAAU,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI;AACnD,YAAM,WAAW,UAAU,QAAQ,EAAE;AACrC,aAAO,aAAa,QAAQ,QAAQ;AAAA,IACtC;AAMA,IAAM,YAAY,UAAQ;AACxB,UAAI,WAAW,CAAC,EAAE;AAClB,UAAI;AACJ,UAAI,QAAQ,MAAM;AAChB,cAAM,UAAU,KAAK,QAAQ,GAAG;AAChC,YAAI,UAAU,IAAI;AAChB,wBAAc,KAAK,UAAU,UAAU,CAAC;AACxC,iBAAO,KAAK,UAAU,GAAG,OAAO;AAAA,QAClC;AACA,mBAAW,KAAK,MAAM,GAAG,EAAE,IAAI,OAAK,EAAE,KAAK,CAAC,EAAE,OAAO,OAAK,EAAE,SAAS,CAAC;AACtE,YAAI,SAAS,WAAW,GAAG;AACzB,qBAAW,CAAC,EAAE;AAAA,QAChB;AAAA,MACF;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,IAAM,cAAc,YAAU;AAC5B,cAAQ,MAAM,qBAAqB,OAAO,MAAM,GAAG;AACnD,iBAAW,SAAS,QAAQ;AAC1B,cAAM,WAAW,CAAC;AAClB,cAAM,QAAQ,OAAK,SAAS,KAAK,GAAG,EAAE,QAAQ,CAAC;AAC/C,cAAM,MAAM,MAAM,IAAI,OAAK,EAAE,EAAE;AAC/B,gBAAQ,MAAM,MAAM,aAAa,QAAQ,CAAC,IAAI,yCAAyC,OAAQ,IAAI,IAAI,KAAK,IAAI,CAAC,GAAG;AAAA,MACtH;AACA,cAAQ,SAAS;AAAA,IACnB;AACA,IAAM,iBAAiB,eAAa;AAClC,cAAQ,MAAM,wBAAwB,UAAU,MAAM,GAAG;AACzD,iBAAW,YAAY,WAAW;AAChC,YAAI,SAAS,IAAI;AACf,kBAAQ,MAAM,UAAU,MAAM,aAAa,SAAS,IAAI,CAAC,IAAI,qBAAqB,SAAS,MAAM,aAAa,SAAS,GAAG,QAAQ,CAAC,IAAI,mBAAmB;AAAA,QAC5J;AAAA,MACF;AACA,cAAQ,SAAS;AAAA,IACnB;AAUA,IAAM,gBAAgB,CAAO,MAAM,OAAO,WAAW,OAAO,UAAU,OAAO,cAAc;AACzF,UAAI;AAEF,cAAM,SAAS,cAAc,IAAI;AAEjC,YAAI,SAAS,MAAM,UAAU,CAAC,QAAQ;AACpC,iBAAO;AAAA,QACT;AACA,cAAM,IAAI,QAAQ,aAAW,iBAAiB,QAAQ,OAAO,CAAC;AAC9D,cAAM,QAAQ,MAAM,KAAK;AACzB,cAAM,SAAS,MAAM,OAAO,WAAW,MAAM,IAAI,MAAM,QAAQ,WAAW,SAAS;AAGnF,YAAI,OAAO,SAAS;AAClB,sBAAY;AACZ,oBAAU;AAAA,QACZ;AAEA,kBAAU,MAAM,cAAc,OAAO,SAAS,OAAO,WAAW,QAAQ,GAAG,SAAS,SAAS;AAG7F,YAAI,OAAO,aAAa;AACtB,gBAAM,OAAO,YAAY;AAAA,QAC3B;AACA,eAAO;AAAA,MACT,SAAS,GAAG;AACV,sBAAc,8CAA8C,CAAC;AAC7D,eAAO;AAAA,MACT;AAAA,IACF;AAMA,IAAM,eAAe,CAAM,SAAQ;AACjC,YAAM,MAAM,CAAC;AACb,UAAI;AACJ,UAAI,OAAO;AAEX,aAAO,SAAS,cAAc,IAAI,GAAG;AACnC,cAAM,KAAK,MAAM,OAAO,WAAW;AACnC,YAAI,IAAI;AACN,iBAAO,GAAG;AACV,aAAG,UAAU;AACb,cAAI,KAAK,EAAE;AAAA,QACb,OAAO;AACL;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,IAAM,mBAAmB,MAAM;AAC7B,UAAI,cAAc,SAAS,IAAI,GAAG;AAChC,eAAO,QAAQ,QAAQ;AAAA,MACzB;AACA,aAAO,IAAI,QAAQ,aAAW;AAC5B,eAAO,iBAAiB,kBAAkB,MAAM,QAAQ,GAAG;AAAA,UACzD,MAAM;AAAA,QACR,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,IAAM,kBAAkB;AACxB,IAAM,gBAAgB,UAAQ;AAC5B,UAAI,CAAC,MAAM;AACT,eAAO;AAAA,MACT;AACA,UAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,eAAO;AAAA,MACT;AACA,YAAM,SAAS,KAAK,cAAc,eAAe;AACjD,aAAO,WAAW,QAAQ,WAAW,SAAS,SAAS;AAAA,IACzD;AASA,IAAM,kBAAkB,CAAC,UAAU,aAAa;AAC9C,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,OAAO,QAAW;AACpB,eAAO;AAAA,MACT;AACA,UAAI,KAAK,SAAS,SAAS,QAAQ;AACjC,eAAO;AAAA,MACT;AACA,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAM,WAAW,KAAK,CAAC;AACvB,YAAI,aAAa,KAAK;AACpB,iBAAO;AAAA,QACT;AACA,YAAI,aAAa,SAAS,CAAC,GAAG;AAC5B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO,KAAK,WAAW,SAAS;AAAA,IAClC;AAEA,IAAM,oBAAoB,CAAC,UAAU,cAAc;AACjD,aAAO,UAAU,KAAK,cAAY,gBAAgB,UAAU,QAAQ,CAAC;AAAA,IACvE;AACA,IAAM,aAAa,CAAC,KAAK,UAAU;AACjC,YAAM,MAAM,KAAK,IAAI,IAAI,QAAQ,MAAM,MAAM;AAC7C,UAAI,QAAQ;AACZ,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAM,UAAU,IAAI,CAAC;AACrB,cAAM,aAAa,MAAM,CAAC;AAE1B,YAAI,QAAQ,GAAG,YAAY,MAAM,WAAW,IAAI;AAC9C;AAAA,QACF;AACA,YAAI,QAAQ,QAAQ;AAClB,gBAAM,gBAAgB,OAAO,KAAK,QAAQ,MAAM;AAEhD,cAAI,cAAc,WAAW,WAAW,SAAS,QAAQ;AAmBvD,kBAAM,iBAAiB,cAAc,IAAI,SAAO,IAAI,GAAG,EAAE;AACzD,qBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAE9C,kBAAI,eAAe,CAAC,EAAE,YAAY,MAAM,WAAW,SAAS,CAAC,GAAG;AAC9D;AAAA,cACF;AAEA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAQA,IAAM,kBAAkB,CAAC,UAAU,UAAU;AAC3C,YAAM,gBAAgB,IAAI,eAAe,QAAQ;AACjD,UAAI,iBAAiB;AACrB,UAAI;AACJ,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAM,gBAAgB,MAAM,CAAC,EAAE;AAC/B,YAAI,cAAc,CAAC,MAAM,IAAI;AAC3B,2BAAiB;AAAA,QACnB,OAAO;AACL,qBAAW,WAAW,eAAe;AACnC,kBAAM,OAAO,cAAc,KAAK;AAEhC,gBAAI,QAAQ,CAAC,MAAM,KAAK;AACtB,kBAAI,SAAS,IAAI;AACf,uBAAO;AAAA,cACT;AACA,0BAAY,aAAa,CAAC;AAC1B,oBAAM,SAAS,UAAU,CAAC,MAAM,UAAU,CAAC,IAAI,CAAC;AAChD,qBAAO,QAAQ,MAAM,CAAC,CAAC,IAAI;AAAA,YAC7B,WAAW,SAAS,SAAS;AAC3B,qBAAO;AAAA,YACT;AAAA,UACF;AACA,2BAAiB;AAAA,QACnB;AAAA,MACF;AACA,YAAM,UAAU,iBAAiB,oBAAoB,cAAc,KAAK,MAAM,MAAM;AACpF,UAAI,CAAC,SAAS;AACZ,eAAO;AAAA,MACT;AACA,UAAI,WAAW;AACb,eAAO,MAAM,IAAI,CAAC,OAAO,OAAO;AAAA,UAC9B,IAAI,MAAM;AAAA,UACV,UAAU,MAAM;AAAA,UAChB,QAAQ,YAAY,MAAM,QAAQ,UAAU,CAAC,CAAC;AAAA,UAC9C,aAAa,MAAM;AAAA,UACnB,aAAa,MAAM;AAAA,QACrB,EAAE;AAAA,MACJ;AACA,aAAO;AAAA,IACT;AAKA,IAAM,cAAc,CAAC,GAAG,MAAM;AAC5B,aAAO,KAAK,IAAI,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI;AAAA,IAC3D;AAQA,IAAM,kBAAkB,CAAC,KAAK,WAAW;AACvC,UAAI,QAAQ;AACZ,UAAI,aAAa;AACjB,iBAAW,SAAS,QAAQ;AAC1B,cAAM,QAAQ,WAAW,KAAK,KAAK;AACnC,YAAI,QAAQ,YAAY;AACtB,kBAAQ;AACR,uBAAa;AAAA,QACf;AAAA,MACF;AACA,UAAI,OAAO;AACT,eAAO,MAAM,IAAI,CAAC,OAAO,MAAM;AAC7B,cAAI;AACJ,iBAAO;AAAA,YACL,IAAI,MAAM;AAAA,YACV,UAAU,MAAM;AAAA,YAChB,QAAQ,YAAY,MAAM,SAAS,KAAK,IAAI,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,UAChG;AAAA,QACF,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AAQA,IAAM,uBAAuB,CAAC,UAAU,WAAW;AACjD,UAAI,QAAQ;AACZ,UAAI,YAAY;AAChB,iBAAW,SAAS,QAAQ;AAC1B,cAAM,eAAe,gBAAgB,UAAU,KAAK;AACpD,YAAI,iBAAiB,MAAM;AACzB,gBAAM,QAAQ,gBAAgB,YAAY;AAC1C,cAAI,QAAQ,WAAW;AACrB,wBAAY;AACZ,oBAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAYA,IAAM,kBAAkB,WAAS;AAC/B,UAAI,QAAQ;AACZ,UAAI,QAAQ;AACZ,iBAAW,SAAS,OAAO;AACzB,mBAAW,WAAW,MAAM,UAAU;AACpC,cAAI,QAAQ,CAAC,MAAM,KAAK;AACtB,qBAAS,KAAK,IAAI,GAAG,KAAK;AAAA,UAC5B,WAAW,YAAY,IAAI;AACzB,qBAAS,KAAK,IAAI,GAAG,KAAK;AAAA,UAC5B;AACA;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,IAAM,iBAAN,MAAqB;AAAA,MACnB,YAAY,UAAU;AACpB,aAAK,WAAW,SAAS,MAAM;AAAA,MACjC;AAAA,MACA,OAAO;AACL,YAAI,KAAK,SAAS,SAAS,GAAG;AAC5B,iBAAO,KAAK,SAAS,MAAM;AAAA,QAC7B;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,IAAM,WAAW,CAAC,IAAI,SAAS;AAC7B,UAAI,QAAQ,IAAI;AACd,eAAO,GAAG,IAAI;AAAA,MAChB;AACA,UAAI,GAAG,aAAa,IAAI,GAAG;AACzB,eAAO,GAAG,aAAa,IAAI;AAAA,MAC7B;AACA,aAAO;AAAA,IACT;AAMA,IAAM,gBAAgB,UAAQ;AAC5B,aAAO,MAAM,KAAK,KAAK,QAAQ,EAAE,OAAO,QAAM,GAAG,YAAY,oBAAoB,EAAE,IAAI,QAAM;AAC3F,cAAM,KAAK,SAAS,IAAI,IAAI;AAC5B,eAAO;AAAA,UACL,MAAM,UAAU,SAAS,IAAI,MAAM,CAAC,EAAE;AAAA,UACtC,IAAI,MAAM,OAAO,SAAY,UAAU,EAAE;AAAA,QAC3C;AAAA,MACF,CAAC;AAAA,IACH;AAMA,IAAM,aAAa,UAAQ;AACzB,aAAO,kBAAkB,eAAe,IAAI,CAAC;AAAA,IAC/C;AAMA,IAAM,iBAAiB,UAAQ;AAC7B,aAAO,MAAM,KAAK,KAAK,QAAQ,EAAE,OAAO,QAAM,GAAG,YAAY,eAAe,GAAG,SAAS,EAAE,IAAI,QAAM;AAClG,cAAM,YAAY,SAAS,IAAI,WAAW;AAC1C,eAAO;AAAA,UACL,UAAU,UAAU,SAAS,IAAI,KAAK,CAAC,EAAE;AAAA,UACzC,IAAI,UAAU,YAAY;AAAA,UAC1B,QAAQ,GAAG;AAAA,UACX,aAAa,GAAG;AAAA,UAChB,aAAa,GAAG;AAAA,UAChB,UAAU,eAAe,EAAE;AAAA,QAC7B;AAAA,MACF,CAAC;AAAA,IACH;AAMA,IAAM,oBAAoB,WAAS;AACjC,YAAM,SAAS,CAAC;AAChB,iBAAW,QAAQ,OAAO;AACxB,oBAAY,CAAC,GAAG,QAAQ,IAAI;AAAA,MAC9B;AACA,aAAO;AAAA,IACT;AAEA,IAAM,cAAc,CAAC,OAAO,QAAQ,SAAS;AAC3C,cAAQ,CAAC,GAAG,OAAO;AAAA,QACjB,IAAI,KAAK;AAAA,QACT,UAAU,KAAK;AAAA,QACf,QAAQ,KAAK;AAAA,QACb,aAAa,KAAK;AAAA,QAClB,aAAa,KAAK;AAAA,MACpB,CAAC;AACD,UAAI,KAAK,SAAS,WAAW,GAAG;AAC9B,eAAO,KAAK,KAAK;AACjB;AAAA,MACF;AACA,iBAAW,SAAS,KAAK,UAAU;AACjC,oBAAY,OAAO,QAAQ,KAAK;AAAA,MAClC;AAAA,IACF;AACA,IAAM,SAAS,MAAM;AAAA,MACnB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,qBAAqB,YAAY,MAAM,sBAAsB,CAAC;AACnE,aAAK,oBAAoB,YAAY,MAAM,qBAAqB,CAAC;AACjE,aAAK,eAAe;AACpB,aAAK,OAAO;AACZ,aAAK,QAAQ;AACb,aAAK,YAAY;AACjB,aAAK,OAAO;AACZ,aAAK,UAAU;AAAA,MACjB;AAAA,MACM,oBAAoB;AAAA;AACxB,gBAAM,iBAAiB;AACvB,gBAAM,aAAa,MAAM,KAAK,UAAU,KAAK,YAAY,CAAC;AAC1D,cAAI,eAAe,MAAM;AACvB,gBAAI,OAAO,eAAe,UAAU;AAClC,oBAAM;AAAA,gBACJ;AAAA,cACF,IAAI;AACJ,oBAAM,OAAO,UAAU,QAAQ;AAC/B,mBAAK,YAAY,KAAK,UAAU,oBAAoB,KAAK,WAAW;AACpE,oBAAM,KAAK,kBAAkB,KAAK,UAAU,kBAAkB;AAAA,YAChE;AAAA,UACF,OAAO;AACL,kBAAM,KAAK,gBAAgB;AAAA,UAC7B;AAAA,QACF;AAAA;AAAA,MACA,mBAAmB;AACjB,eAAO,iBAAiB,2BAA2B,SAAS,KAAK,kBAAkB,KAAK,IAAI,GAAG,EAAE,CAAC;AAClG,eAAO,iBAAiB,uBAAuB,SAAS,KAAK,gBAAgB,KAAK,IAAI,GAAG,GAAG,CAAC;AAAA,MAC/F;AAAA,MACM,aAAa;AAAA;AACjB,gBAAM,YAAY,KAAK,iBAAiB;AACxC,cAAI,WAAW,KAAK,YAAY;AAChC,gBAAM,aAAa,MAAM,KAAK,UAAU,QAAQ;AAChD,cAAI,eAAe,MAAM;AACvB,gBAAI,OAAO,eAAe,UAAU;AAClC,yBAAW,UAAU,WAAW,QAAQ,EAAE;AAAA,YAC5C,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO,KAAK,kBAAkB,UAAU,SAAS;AAAA,QACnD;AAAA;AAAA,MACA,aAAa,IAAI;AACf,WAAG,OAAO,SAAS,GAAG,wBAAsB;AAC1C,eAAK,KAAK;AACV,6BAAmB;AAAA,QACrB,CAAC;AAAA,MACH;AAAA;AAAA,MAEM,gBAAgB;AAAA;AACpB,gBAAM,aAAa,MAAM,KAAK,UAAU;AACxC,cAAI,eAAe,MAAM;AACvB,gBAAI,OAAO,eAAe,UAAU;AAClC,qBAAO,WAAW;AAAA,YACpB,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOM,KAAK,MAAM,YAAY,WAAW,WAAW;AAAA;AACjD,cAAI;AACJ,cAAI,KAAK,WAAW,GAAG,GAAG;AACxB,kBAAM,eAAe,KAAK,KAAK,kBAAkB,QAAQ,OAAO,SAAS,KAAK;AAE9E,kBAAM,MAAM,IAAI,IAAI,MAAM,gBAAgB,WAAW,EAAE;AACvD,mBAAO,IAAI,WAAW,IAAI;AAAA,UAC5B;AACA,cAAI,aAAa,UAAU,IAAI;AAC/B,gBAAM,aAAa,MAAM,KAAK,UAAU,WAAW,QAAQ;AAC3D,cAAI,eAAe,MAAM;AACvB,gBAAI,OAAO,eAAe,UAAU;AAClC,2BAAa,UAAU,WAAW,QAAQ;AAAA,YAC5C,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,UACF;AACA,eAAK,YAAY,WAAW,UAAU,WAAW,WAAW,WAAW;AACvE,iBAAO,KAAK,kBAAkB,WAAW,UAAU,WAAW,SAAS;AAAA,QACzE;AAAA;AAAA;AAAA,MAEA,OAAO;AACL,eAAO,QAAQ,KAAK;AACpB,eAAO,QAAQ,QAAQ,KAAK,WAAW;AAAA,MACzC;AAAA;AAAA,MAEM,aAAa;AAAA;AACjB,sBAAY,WAAW,KAAK,EAAE,CAAC;AAC/B,yBAAe,cAAc,KAAK,EAAE,CAAC;AAAA,QACvC;AAAA;AAAA;AAAA,MAEM,WAAW,WAAW;AAAA;AAC1B,cAAI,KAAK,MAAM;AACb,4BAAgB,0DAA0D;AAC1E,mBAAO;AAAA,UACT;AACA,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,MAAM,aAAa,OAAO,SAAS,IAAI;AAC3C,gBAAM,SAAS,WAAW,KAAK,EAAE;AACjC,gBAAM,QAAQ,gBAAgB,KAAK,MAAM;AACzC,cAAI,CAAC,OAAO;AACV,4BAAgB,sCAAsC,IAAI,IAAI,OAAK,EAAE,EAAE,CAAC;AACxE,mBAAO;AAAA,UACT;AACA,gBAAM,WAAW,gBAAgB,KAAK;AACtC,cAAI,CAAC,UAAU;AACb,4BAAgB,oFAAoF;AACpG,mBAAO;AAAA,UACT;AACA,eAAK,YAAY,UAAU,SAAS;AACpC,gBAAM,KAAK,kBAAkB,QAAQ,OAAO,oBAAoB,UAAU,MAAM,IAAI,MAAM;AAC1F,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA,MAEA,oBAAoB;AAClB,cAAM,WAAW,KAAK,YAAY;AAClC,YAAI,YAAY,kBAAkB,UAAU,cAAc,KAAK,EAAE,CAAC,GAAG;AACnE,eAAK,kBAAkB,UAAU,kBAAkB;AAAA,QACrD;AAAA,MACF;AAAA;AAAA,MAEA,kBAAkB;AAChB,eAAO,KAAK,kBAAkB,KAAK,YAAY,GAAG,kBAAkB;AAAA,MACtE;AAAA,MACA,mBAAmB;AACjB,YAAI;AACJ,cAAM,MAAM;AACZ,YAAI,IAAI,QAAQ,UAAU,MAAM;AAC9B,eAAK;AACL,cAAI,QAAQ,aAAa,KAAK,OAAO,IAAI,SAAS,QAAQ,KAAK,IAAI,SAAS,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI;AAAA,QACpI;AACA,cAAM,QAAQ,IAAI,QAAQ;AAC1B,cAAM,YAAY,KAAK;AACvB,aAAK,YAAY;AACjB,YAAI,QAAQ,aAAa,SAAS,aAAa,YAAY,GAAG;AAC5D,iBAAO;AAAA,QACT;AACA,YAAI,QAAQ,WAAW;AACrB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,MACM,kBAAkB,UAAU,WAAW,WAAW;AAAA;AACtD,cAAI,CAAC,UAAU;AACb,0BAAc,oDAAoD;AAClE,mBAAO;AAAA,UACT;AAEA,gBAAM,YAAY,cAAc,KAAK,EAAE;AACvC,gBAAM,WAAW,kBAAkB,UAAU,SAAS;AACtD,cAAI,eAAe;AACnB,cAAI,UAAU;AACZ,kBAAM;AAAA,cACJ,UAAU;AAAA,cACV;AAAA,YACF,IAAI,SAAS;AACb,iBAAK,YAAY,YAAY,WAAW,WAAW;AACnD,2BAAe,SAAS;AACxB,uBAAW;AAAA,UACb;AAEA,gBAAM,SAAS,WAAW,KAAK,EAAE;AACjC,gBAAM,QAAQ,qBAAqB,UAAU,MAAM;AACnD,cAAI,CAAC,OAAO;AACV,0BAAc,mDAAmD;AACjE,mBAAO;AAAA,UACT;AAEA,iBAAO,KAAK,kBAAkB,SAAS,MAAM,OAAO,WAAW,UAAU,cAAc,GAAG,SAAS;AAAA,QACrG;AAAA;AAAA,MACM,kBAAkB,MAAM,OAAO,WAAW,UAAU,cAAc,QAAQ,GAAG,WAAW;AAAA;AAC5F,gBAAM,SAAS,MAAM,KAAK,KAAK;AAC/B,cAAI,UAAU;AACd,cAAI;AACF,sBAAU,MAAM,KAAK,cAAc,MAAM,OAAO,WAAW,UAAU,cAAc,OAAO,SAAS;AAAA,UACrG,SAAS,GAAG;AACV,0BAAc,kDAAkD,CAAC;AAAA,UACnE;AACA,iBAAO;AACP,iBAAO;AAAA,QACT;AAAA;AAAA,MACM,OAAO;AAAA;AACX,gBAAM,IAAI,KAAK;AACf,cAAI;AACJ,eAAK,cAAc,IAAI,QAAQ,OAAK,UAAU,CAAC;AAC/C,cAAI,MAAM,QAAW;AACnB,kBAAM;AAAA,UACR;AACA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOM,YAAyC;AAAA,mDAA/B,KAAK,KAAK,YAAY,GAAG,MAAM;AAC7C,cAAI,SAAS,QAAW;AACtB,mBAAO,UAAU,KAAK,YAAY,EAAE;AAAA,UACtC;AACA,cAAI,CAAC,MAAM,CAAC,MAAM;AAChB,mBAAO;AAAA,UACT;AACA,gBAAM,SAAS,WAAW,KAAK,EAAE;AACjC,gBAAM,YAAY,qBAAqB,MAAM,MAAM;AAEnD,gBAAM,kBAAkB,aAAa,UAAU,UAAU,SAAS,CAAC,EAAE;AACrE,gBAAM,WAAW,kBAAkB,MAAM,gBAAgB,IAAI;AAC7D,cAAI,aAAa,SAAS,OAAO,aAAa,UAAU;AACtD,mBAAO;AAAA,UACT;AACA,gBAAM,UAAU,qBAAqB,IAAI,MAAM;AAE/C,gBAAM,kBAAkB,WAAW,QAAQ,QAAQ,SAAS,CAAC,EAAE;AAC/D,iBAAO,kBAAkB,gBAAgB,IAAI;AAAA,QAC/C;AAAA;AAAA,MACM,cAAc,MAAM,OAAO,WAAW,UAAU,cAAc,QAAQ,GAAG,WAAW;AAAA;AACxF,cAAI,KAAK,MAAM;AACb,4BAAgB,0DAA0D;AAC1E,mBAAO;AAAA,UACT;AACA,eAAK,OAAO;AAEZ,gBAAM,aAAa,KAAK,iBAAiB,UAAU,YAAY;AAC/D,cAAI,YAAY;AACd,iBAAK,mBAAmB,KAAK,UAAU;AAAA,UACzC;AACA,gBAAM,UAAU,MAAM,cAAc,MAAM,OAAO,WAAW,OAAO,OAAO,SAAS;AACnF,eAAK,OAAO;AAEZ,cAAI,YAAY;AACd,iBAAK,kBAAkB,KAAK,UAAU;AAAA,UACxC;AACA,iBAAO;AAAA,QACT;AAAA;AAAA,MACA,YAAY,UAAU,WAAW,aAAa;AAC5C,aAAK;AACL,sBAAc,OAAO,SAAS,KAAK,MAAM,KAAK,SAAS,UAAU,WAAW,KAAK,OAAO,WAAW;AAAA,MACrG;AAAA,MACA,cAAc;AACZ,eAAO,aAAa,OAAO,UAAU,KAAK,MAAM,KAAK,OAAO;AAAA,MAC9D;AAAA,MACA,iBAAiB,YAAY,sBAAsB;AACjD,cAAM,OAAO,KAAK;AAClB,cAAM,KAAK,aAAa,UAAU;AAClC,aAAK,eAAe;AACpB,YAAI,OAAO,MAAM;AACf,iBAAO;AAAA,QACT;AACA,cAAM,iBAAiB,uBAAuB,aAAa,oBAAoB,IAAI;AACnF,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,IACF;AACA,IAAM,gBAAgB;AACtB,IAAM,sBAAsB;AAC5B,IAAM,aAAa,MAAM;AAAA,MACvB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,UAAU,QAAM;AACnB,kBAAQ,KAAK,MAAM,IAAI,KAAK,iBAAiB,KAAK,eAAe;AAAA,QACnE;AACA,aAAK,QAAQ;AACb,aAAK,OAAO;AACZ,aAAK,MAAM;AACX,aAAK,kBAAkB;AACvB,aAAK,kBAAkB;AACvB,aAAK,SAAS;AAAA,MAChB;AAAA,MACA,SAAS;AACP,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM,QAAQ;AAAA,UACZ,MAAM,KAAK;AAAA,UACX,KAAK,KAAK;AAAA,UACV,QAAQ,KAAK;AAAA,QACf;AACA,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,SAAS,KAAK;AAAA,UACd,OAAO,mBAAmB,KAAK,OAAO;AAAA,YACpC,CAAC,IAAI,GAAG;AAAA,YACR,mBAAmB;AAAA,UACrB,CAAC;AAAA,QACH,GAAG,EAAE,KAAK,OAAO,OAAO;AAAA,UACtB,KAAK;AAAA,QACP,GAAG,KAAK,GAAG,EAAE,QAAQ;AAAA,UACnB,KAAK;AAAA,QACP,CAAC,CAAC,CAAC;AAAA,MACL;AAAA,IACF;AACA,eAAW,QAAQ;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}