﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.SetupDomain.Data.ConnectionStrings
{
    public static class ConnectionStringSettingsExtensions
    {
        private static object objLock = new object();

        private static ConnectionStringSettingsCollection connectionStringSettings = null;

        public static ConnectionStringSettingsCollection ConnectionStrings(this IConfiguration configuration, string section = "ConnectionStrings")
        {
            if (configuration == null)
            {
                throw new ArgumentNullException("configuration");
            }

            if (connectionStringSettings == null)
            {
                lock (objLock)
                {
                    if (connectionStringSettings == null)
                    {
                        connectionStringSettings = configuration.GetSection(section).Get<ConnectionStringSettingsCollection>();
                        if (connectionStringSettings == null)
                        {
                            connectionStringSettings = new ConnectionStringSettingsCollection();
                        }
                    }
                }
            }

            return connectionStringSettings;
        }

        public static ConnectionStringSettings ConnectionString(this IConfiguration configuration, string name, string section = "ConnectionStrings")
        {
            if (configuration == null)
            {
                throw new ArgumentNullException("configuration");
            }

            if (name == null)
            {
                throw new ArgumentNullException("name");
            }

            ConnectionStringSettings value = null;
            configuration.ConnectionStrings(section).TryGetValue(name, out value);
            return value;
        }
    }
}
