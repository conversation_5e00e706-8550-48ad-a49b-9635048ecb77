ion-card {
  margin: var(--app-spacing-md);
  
  ion-card-header {
    ion-card-title {
      display: flex;
      align-items: center;
      
      ion-icon {
        margin-right: var(--app-spacing-sm);
      }
    }
  }
  
  ion-card-content {
    ion-item {
      --padding-start: 0;
      
      ion-icon {
        font-size: 1.5rem;
      }
      
      h2 {
        font-weight: 500;
        margin-bottom: var(--app-spacing-xs);
      }
      
      p {
        color: var(--ion-color-medium);
      }
    }
    
    ion-progress-bar {
      margin: var(--app-spacing-md) 0;
    }
  }
}
