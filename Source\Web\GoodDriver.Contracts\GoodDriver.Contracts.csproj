﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Folder Include="Brands\Commands\" />
    <Folder Include="Brands\Journeys\Commands\" />
    <Folder Include="Brands\Journeys\Events\" />
    <Folder Include="Brands\Journeys\Requests\" />
    <Folder Include="Brands\Journeys\Responses\" />
    <Folder Include="Brands\Requests\" />
    <Folder Include="Brands\Responses\" />
    <Folder Include="Devices\Commands\" />
    <Folder Include="Devices\Events\" />
    <Folder Include="Devices\Requests\" />
    <Folder Include="Devices\Responses\" />
    <Folder Include="Models\Commands\" />
    <Folder Include="Models\Events\" />
    <Folder Include="Models\Requests\" />
    <Folder Include="Models\Responses\" />
    <Folder Include="Vehicles\Events\" />
    <Folder Include="Vehicles\Requests\" />
    <Folder Include="Vehicles\Responses\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\GoodDriver.Common\GoodDriver.Common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Rogerio.Cqrs">
      <HintPath>..\..\..\..\RogerioFramework\RogerioIT\Rogerio.Cqrs\bin\Debug\net8.0\Rogerio.Cqrs.dll</HintPath>
    </Reference>
    <Reference Include="Rogerio.Cqrs.MassTransit">
      <HintPath>..\..\..\..\RogerioFramework\RogerioIT\Rogerio.Cqrs.MassTransit\bin\Debug\net8.0\Rogerio.Cqrs.MassTransit.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
