{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-checkbox.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { i as inheritAriaAttributes, d as renderHiddenInput } from './helpers-d94bc8ad.js';\nimport { c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport './index-cfd9c1f2.js';\nconst checkboxIosCss = \":host{--checkbox-background-checked:var(--ion-color-primary, #0054e9);--border-color-checked:var(--ion-color-primary, #0054e9);--checkmark-color:var(--ion-color-primary-contrast, #fff);--transition:none;display:inline-block;position:relative;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}:host(.ion-color){--checkbox-background-checked:var(--ion-color-base);--border-color-checked:var(--ion-color-base);--checkmark-color:var(--ion-color-contrast)}.checkbox-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper,:host(.in-item:not(.checkbox-label-placement-stacked):not([slot])) .native-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.checkbox-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.checkbox-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}input{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.checkbox-icon{border-radius:var(--border-radius);position:relative;width:var(--size);height:var(--size);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--checkbox-background);-webkit-box-sizing:border-box;box-sizing:border-box}.checkbox-icon path{fill:none;stroke:var(--checkmark-color);stroke-width:var(--checkmark-width);opacity:0}.checkbox-bottom{padding-top:4px;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;font-size:0.75rem;white-space:normal}:host(.checkbox-label-placement-stacked) .checkbox-bottom{font-size:1rem}.checkbox-bottom .error-text{display:none;color:var(--ion-color-danger, #c5000f)}.checkbox-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .checkbox-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .checkbox-bottom .helper-text{display:none}:host(.checkbox-label-placement-start) .checkbox-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.checkbox-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-end) .checkbox-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse;-ms-flex-pack:start;justify-content:start}:host(.checkbox-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.checkbox-label-placement-stacked) .checkbox-wrapper{-ms-flex-direction:column;flex-direction:column;text-align:center}:host(.checkbox-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.checkbox-justify-space-between) .checkbox-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.checkbox-justify-start) .checkbox-wrapper{-ms-flex-pack:start;justify-content:start}:host(.checkbox-justify-end) .checkbox-wrapper{-ms-flex-pack:end;justify-content:end}:host(.checkbox-alignment-start) .checkbox-wrapper{-ms-flex-align:start;align-items:start}:host(.checkbox-alignment-center) .checkbox-wrapper{-ms-flex-align:center;align-items:center}:host(.checkbox-justify-space-between),:host(.checkbox-justify-start),:host(.checkbox-justify-end),:host(.checkbox-alignment-start),:host(.checkbox-alignment-center){display:block}:host(.checkbox-checked) .checkbox-icon,:host(.checkbox-indeterminate) .checkbox-icon{border-color:var(--border-color-checked);background:var(--checkbox-background-checked)}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{opacity:1}:host(.checkbox-disabled){pointer-events:none}:host{--border-radius:50%;--border-width:0.125rem;--border-style:solid;--border-color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.23);--checkbox-background:var(--ion-item-background, var(--ion-background-color, #fff));--size:min(1.375rem, 55.836px);--checkmark-width:1.5px}:host(.checkbox-disabled){opacity:0.3}\";\nconst IonCheckboxIosStyle0 = checkboxIosCss;\nconst checkboxMdCss = \":host{--checkbox-background-checked:var(--ion-color-primary, #0054e9);--border-color-checked:var(--ion-color-primary, #0054e9);--checkmark-color:var(--ion-color-primary-contrast, #fff);--transition:none;display:inline-block;position:relative;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}:host(.ion-color){--checkbox-background-checked:var(--ion-color-base);--border-color-checked:var(--ion-color-base);--checkmark-color:var(--ion-color-contrast)}.checkbox-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper,:host(.in-item:not(.checkbox-label-placement-stacked):not([slot])) .native-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.checkbox-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.checkbox-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}input{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.checkbox-icon{border-radius:var(--border-radius);position:relative;width:var(--size);height:var(--size);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--checkbox-background);-webkit-box-sizing:border-box;box-sizing:border-box}.checkbox-icon path{fill:none;stroke:var(--checkmark-color);stroke-width:var(--checkmark-width);opacity:0}.checkbox-bottom{padding-top:4px;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;font-size:0.75rem;white-space:normal}:host(.checkbox-label-placement-stacked) .checkbox-bottom{font-size:1rem}.checkbox-bottom .error-text{display:none;color:var(--ion-color-danger, #c5000f)}.checkbox-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .checkbox-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .checkbox-bottom .helper-text{display:none}:host(.checkbox-label-placement-start) .checkbox-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.checkbox-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-end) .checkbox-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse;-ms-flex-pack:start;justify-content:start}:host(.checkbox-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.checkbox-label-placement-stacked) .checkbox-wrapper{-ms-flex-direction:column;flex-direction:column;text-align:center}:host(.checkbox-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.checkbox-justify-space-between) .checkbox-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.checkbox-justify-start) .checkbox-wrapper{-ms-flex-pack:start;justify-content:start}:host(.checkbox-justify-end) .checkbox-wrapper{-ms-flex-pack:end;justify-content:end}:host(.checkbox-alignment-start) .checkbox-wrapper{-ms-flex-align:start;align-items:start}:host(.checkbox-alignment-center) .checkbox-wrapper{-ms-flex-align:center;align-items:center}:host(.checkbox-justify-space-between),:host(.checkbox-justify-start),:host(.checkbox-justify-end),:host(.checkbox-alignment-start),:host(.checkbox-alignment-center){display:block}:host(.checkbox-checked) .checkbox-icon,:host(.checkbox-indeterminate) .checkbox-icon{border-color:var(--border-color-checked);background:var(--checkbox-background-checked)}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{opacity:1}:host(.checkbox-disabled){pointer-events:none}:host{--border-radius:calc(var(--size) * .125);--border-width:2px;--border-style:solid;--border-color:rgb(var(--ion-text-color-rgb, 0, 0, 0), 0.6);--checkmark-width:3;--checkbox-background:var(--ion-item-background, var(--ion-background-color, #fff));--transition:background 180ms cubic-bezier(0.4, 0, 0.2, 1);--size:18px}.checkbox-icon path{stroke-dasharray:30;stroke-dashoffset:30}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{stroke-dashoffset:0;-webkit-transition:stroke-dashoffset 90ms linear 90ms;transition:stroke-dashoffset 90ms linear 90ms}:host(.checkbox-disabled) .label-text-wrapper{opacity:0.38}:host(.checkbox-disabled) .native-wrapper{opacity:0.63}\";\nconst IonCheckboxMdStyle0 = checkboxMdCss;\nconst Checkbox = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.inputId = `ion-cb-${checkboxIds++}`;\n    this.inputLabelId = `${this.inputId}-lbl`;\n    this.helperTextId = `${this.inputId}-helper-text`;\n    this.errorTextId = `${this.inputId}-error-text`;\n    this.inheritedAttributes = {};\n    /**\n     * Sets the checked property and emits\n     * the ionChange event. Use this to update the\n     * checked state in response to user-generated\n     * actions such as a click.\n     */\n    this.setChecked = state => {\n      const isChecked = this.checked = state;\n      this.ionChange.emit({\n        checked: isChecked,\n        value: this.value\n      });\n    };\n    this.toggleChecked = ev => {\n      ev.preventDefault();\n      this.setFocus();\n      this.setChecked(!this.checked);\n      this.indeterminate = false;\n    };\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.onKeyDown = ev => {\n      if (ev.key === ' ') {\n        ev.preventDefault();\n        if (!this.disabled) {\n          this.toggleChecked(ev);\n        }\n      }\n    };\n    this.onClick = ev => {\n      if (this.disabled) {\n        return;\n      }\n      this.toggleChecked(ev);\n    };\n    /**\n     * Stops propagation when the display label is clicked,\n     * otherwise, two clicks will be triggered.\n     */\n    this.onDivLabelClick = ev => {\n      ev.stopPropagation();\n    };\n    this.color = undefined;\n    this.name = this.inputId;\n    this.checked = false;\n    this.indeterminate = false;\n    this.disabled = false;\n    this.errorText = undefined;\n    this.helperText = undefined;\n    this.value = 'on';\n    this.labelPlacement = 'start';\n    this.justify = undefined;\n    this.alignment = undefined;\n    this.required = false;\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = Object.assign({}, inheritAriaAttributes(this.el));\n  }\n  /** @internal */\n  async setFocus() {\n    if (this.focusEl) {\n      this.focusEl.focus();\n    }\n  }\n  getHintTextID() {\n    const {\n      el,\n      helperText,\n      errorText,\n      helperTextId,\n      errorTextId\n    } = this;\n    if (el.classList.contains('ion-touched') && el.classList.contains('ion-invalid') && errorText) {\n      return errorTextId;\n    }\n    if (helperText) {\n      return helperTextId;\n    }\n    return undefined;\n  }\n  /**\n   * Responsible for rendering helper text and error text.\n   * This element should only be rendered if hint text is set.\n   */\n  renderHintText() {\n    const {\n      helperText,\n      errorText,\n      helperTextId,\n      errorTextId\n    } = this;\n    /**\n     * undefined and empty string values should\n     * be treated as not having helper/error text.\n     */\n    const hasHintText = !!helperText || !!errorText;\n    if (!hasHintText) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"checkbox-bottom\"\n    }, h(\"div\", {\n      id: helperTextId,\n      class: \"helper-text\",\n      part: \"supporting-text helper-text\"\n    }, helperText), h(\"div\", {\n      id: errorTextId,\n      class: \"error-text\",\n      part: \"supporting-text error-text\"\n    }, errorText));\n  }\n  render() {\n    const {\n      color,\n      checked,\n      disabled,\n      el,\n      getSVGPath,\n      indeterminate,\n      inheritedAttributes,\n      inputId,\n      justify,\n      labelPlacement,\n      name,\n      value,\n      alignment,\n      required\n    } = this;\n    const mode = getIonMode(this);\n    const path = getSVGPath(mode, indeterminate);\n    const hasLabelContent = el.textContent !== '';\n    renderHiddenInput(true, el, name, checked ? value : '', disabled);\n    // The host element must have a checkbox role to ensure proper VoiceOver\n    // support in Safari for accessibility.\n    return h(Host, {\n      key: '26cbe7220e555107200e9b5deeae754aa534a80b',\n      role: \"checkbox\",\n      \"aria-checked\": indeterminate ? 'mixed' : `${checked}`,\n      \"aria-describedby\": this.getHintTextID(),\n      \"aria-invalid\": this.getHintTextID() === this.errorTextId,\n      \"aria-labelledby\": hasLabelContent ? this.inputLabelId : null,\n      \"aria-label\": inheritedAttributes['aria-label'] || null,\n      \"aria-disabled\": disabled ? 'true' : null,\n      tabindex: disabled ? undefined : 0,\n      onKeyDown: this.onKeyDown,\n      class: createColorClasses(color, {\n        [mode]: true,\n        'in-item': hostContext('ion-item', el),\n        'checkbox-checked': checked,\n        'checkbox-disabled': disabled,\n        'checkbox-indeterminate': indeterminate,\n        interactive: true,\n        [`checkbox-justify-${justify}`]: justify !== undefined,\n        [`checkbox-alignment-${alignment}`]: alignment !== undefined,\n        [`checkbox-label-placement-${labelPlacement}`]: true\n      }),\n      onClick: this.onClick\n    }, h(\"label\", {\n      key: 'f025cec5ff08e8be4487b9cc0324616ca5dfae2a',\n      class: \"checkbox-wrapper\",\n      htmlFor: inputId\n    }, h(\"input\", Object.assign({\n      key: 'dc53f7e4e240dc2e18556e6350df2b5c3169f553',\n      type: \"checkbox\",\n      checked: checked ? true : undefined,\n      disabled: disabled,\n      id: inputId,\n      onChange: this.toggleChecked,\n      onFocus: () => this.onFocus(),\n      onBlur: () => this.onBlur(),\n      ref: focusEl => this.focusEl = focusEl,\n      required: required\n    }, inheritedAttributes)), h(\"div\", {\n      key: 'a625e9b50c3b617de8bbbfd624d772454fecaf2d',\n      class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': !hasLabelContent\n      },\n      part: \"label\",\n      id: this.inputLabelId,\n      onClick: this.onDivLabelClick\n    }, h(\"slot\", {\n      key: '87d1a90691327945f4343406706e4ab27f453844'\n    }), this.renderHintText()), h(\"div\", {\n      key: 'b57fed8cdecee4df1ef0d57f157267ee77fac653',\n      class: \"native-wrapper\"\n    }, h(\"svg\", {\n      key: 'd472a06ec6c8b74dfb651415d2236db8080f6805',\n      class: \"checkbox-icon\",\n      viewBox: \"0 0 24 24\",\n      part: \"container\"\n    }, path))));\n  }\n  getSVGPath(mode, indeterminate) {\n    let path = indeterminate ? h(\"path\", {\n      d: \"M6 12L18 12\",\n      part: \"mark\"\n    }) : h(\"path\", {\n      d: \"M5.9,12.5l3.8,3.8l8.8-8.8\",\n      part: \"mark\"\n    });\n    if (mode === 'md') {\n      path = indeterminate ? h(\"path\", {\n        d: \"M2 12H22\",\n        part: \"mark\"\n      }) : h(\"path\", {\n        d: \"M1.73,12.91 8.1,19.28 22.79,4.59\",\n        part: \"mark\"\n      });\n    }\n    return path;\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nlet checkboxIds = 0;\nCheckbox.style = {\n  ios: IonCheckboxIosStyle0,\n  md: IonCheckboxMdStyle0\n};\nexport { Checkbox as ion_checkbox };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAQM,gBACA,sBACA,eACA,qBACA,UAuOF;AAnPJ;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA,IAAM,iBAAiB;AACvB,IAAM,uBAAuB;AAC7B,IAAM,gBAAgB;AACtB,IAAM,sBAAsB;AAC5B,IAAM,WAAW,MAAM;AAAA,MACrB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,aAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,aAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,aAAK,UAAU,UAAU,aAAa;AACtC,aAAK,eAAe,GAAG,KAAK,OAAO;AACnC,aAAK,eAAe,GAAG,KAAK,OAAO;AACnC,aAAK,cAAc,GAAG,KAAK,OAAO;AAClC,aAAK,sBAAsB,CAAC;AAO5B,aAAK,aAAa,WAAS;AACzB,gBAAM,YAAY,KAAK,UAAU;AACjC,eAAK,UAAU,KAAK;AAAA,YAClB,SAAS;AAAA,YACT,OAAO,KAAK;AAAA,UACd,CAAC;AAAA,QACH;AACA,aAAK,gBAAgB,QAAM;AACzB,aAAG,eAAe;AAClB,eAAK,SAAS;AACd,eAAK,WAAW,CAAC,KAAK,OAAO;AAC7B,eAAK,gBAAgB;AAAA,QACvB;AACA,aAAK,UAAU,MAAM;AACnB,eAAK,SAAS,KAAK;AAAA,QACrB;AACA,aAAK,SAAS,MAAM;AAClB,eAAK,QAAQ,KAAK;AAAA,QACpB;AACA,aAAK,YAAY,QAAM;AACrB,cAAI,GAAG,QAAQ,KAAK;AAClB,eAAG,eAAe;AAClB,gBAAI,CAAC,KAAK,UAAU;AAClB,mBAAK,cAAc,EAAE;AAAA,YACvB;AAAA,UACF;AAAA,QACF;AACA,aAAK,UAAU,QAAM;AACnB,cAAI,KAAK,UAAU;AACjB;AAAA,UACF;AACA,eAAK,cAAc,EAAE;AAAA,QACvB;AAKA,aAAK,kBAAkB,QAAM;AAC3B,aAAG,gBAAgB;AAAA,QACrB;AACA,aAAK,QAAQ;AACb,aAAK,OAAO,KAAK;AACjB,aAAK,UAAU;AACf,aAAK,gBAAgB;AACrB,aAAK,WAAW;AAChB,aAAK,YAAY;AACjB,aAAK,aAAa;AAClB,aAAK,QAAQ;AACb,aAAK,iBAAiB;AACtB,aAAK,UAAU;AACf,aAAK,YAAY;AACjB,aAAK,WAAW;AAAA,MAClB;AAAA,MACA,oBAAoB;AAClB,aAAK,sBAAsB,OAAO,OAAO,CAAC,GAAG,sBAAsB,KAAK,EAAE,CAAC;AAAA,MAC7E;AAAA;AAAA,MAEM,WAAW;AAAA;AACf,cAAI,KAAK,SAAS;AAChB,iBAAK,QAAQ,MAAM;AAAA,UACrB;AAAA,QACF;AAAA;AAAA,MACA,gBAAgB;AACd,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,GAAG,UAAU,SAAS,aAAa,KAAK,GAAG,UAAU,SAAS,aAAa,KAAK,WAAW;AAC7F,iBAAO;AAAA,QACT;AACA,YAAI,YAAY;AACd,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,iBAAiB;AACf,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AAKJ,cAAM,cAAc,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,YAAI,CAAC,aAAa;AAChB;AAAA,QACF;AACA,eAAO,EAAE,OAAO;AAAA,UACd,OAAO;AAAA,QACT,GAAG,EAAE,OAAO;AAAA,UACV,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,GAAG,UAAU,GAAG,EAAE,OAAO;AAAA,UACvB,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,GAAG,SAAS,CAAC;AAAA,MACf;AAAA,MACA,SAAS;AACP,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM,OAAO,WAAW,MAAM,aAAa;AAC3C,cAAM,kBAAkB,GAAG,gBAAgB;AAC3C,0BAAkB,MAAM,IAAI,MAAM,UAAU,QAAQ,IAAI,QAAQ;AAGhE,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,MAAM;AAAA,UACN,gBAAgB,gBAAgB,UAAU,GAAG,OAAO;AAAA,UACpD,oBAAoB,KAAK,cAAc;AAAA,UACvC,gBAAgB,KAAK,cAAc,MAAM,KAAK;AAAA,UAC9C,mBAAmB,kBAAkB,KAAK,eAAe;AAAA,UACzD,cAAc,oBAAoB,YAAY,KAAK;AAAA,UACnD,iBAAiB,WAAW,SAAS;AAAA,UACrC,UAAU,WAAW,SAAY;AAAA,UACjC,WAAW,KAAK;AAAA,UAChB,OAAO,mBAAmB,OAAO;AAAA,YAC/B,CAAC,IAAI,GAAG;AAAA,YACR,WAAW,YAAY,YAAY,EAAE;AAAA,YACrC,oBAAoB;AAAA,YACpB,qBAAqB;AAAA,YACrB,0BAA0B;AAAA,YAC1B,aAAa;AAAA,YACb,CAAC,oBAAoB,OAAO,EAAE,GAAG,YAAY;AAAA,YAC7C,CAAC,sBAAsB,SAAS,EAAE,GAAG,cAAc;AAAA,YACnD,CAAC,4BAA4B,cAAc,EAAE,GAAG;AAAA,UAClD,CAAC;AAAA,UACD,SAAS,KAAK;AAAA,QAChB,GAAG,EAAE,SAAS;AAAA,UACZ,KAAK;AAAA,UACL,OAAO;AAAA,UACP,SAAS;AAAA,QACX,GAAG,EAAE,SAAS,OAAO,OAAO;AAAA,UAC1B,KAAK;AAAA,UACL,MAAM;AAAA,UACN,SAAS,UAAU,OAAO;AAAA,UAC1B;AAAA,UACA,IAAI;AAAA,UACJ,UAAU,KAAK;AAAA,UACf,SAAS,MAAM,KAAK,QAAQ;AAAA,UAC5B,QAAQ,MAAM,KAAK,OAAO;AAAA,UAC1B,KAAK,aAAW,KAAK,UAAU;AAAA,UAC/B;AAAA,QACF,GAAG,mBAAmB,CAAC,GAAG,EAAE,OAAO;AAAA,UACjC,KAAK;AAAA,UACL,OAAO;AAAA,YACL,sBAAsB;AAAA,YACtB,6BAA6B,CAAC;AAAA,UAChC;AAAA,UACA,MAAM;AAAA,UACN,IAAI,KAAK;AAAA,UACT,SAAS,KAAK;AAAA,QAChB,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,QACP,CAAC,GAAG,KAAK,eAAe,CAAC,GAAG,EAAE,OAAO;AAAA,UACnC,KAAK;AAAA,UACL,OAAO;AAAA,QACT,GAAG,EAAE,OAAO;AAAA,UACV,KAAK;AAAA,UACL,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QACR,GAAG,IAAI,CAAC,CAAC,CAAC;AAAA,MACZ;AAAA,MACA,WAAW,MAAM,eAAe;AAC9B,YAAI,OAAO,gBAAgB,EAAE,QAAQ;AAAA,UACnC,GAAG;AAAA,UACH,MAAM;AAAA,QACR,CAAC,IAAI,EAAE,QAAQ;AAAA,UACb,GAAG;AAAA,UACH,MAAM;AAAA,QACR,CAAC;AACD,YAAI,SAAS,MAAM;AACjB,iBAAO,gBAAgB,EAAE,QAAQ;AAAA,YAC/B,GAAG;AAAA,YACH,MAAM;AAAA,UACR,CAAC,IAAI,EAAE,QAAQ;AAAA,YACb,GAAG;AAAA,YACH,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AACA,eAAO;AAAA,MACT;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,IACF;AACA,IAAI,cAAc;AAClB,aAAS,QAAQ;AAAA,MACf,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}