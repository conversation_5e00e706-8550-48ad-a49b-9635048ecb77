.login {
  --background: #f8f8f8;
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.05) 0%, rgba(255, 255, 255, 1) 100%);

  .login-header {
    text-align: center;
    padding: 40px 0 10px;

    .logo-small {
      width: 80px;
      height: 80px;
      margin-bottom: 10px;
    }

    h1 {
      font-size: 24px;
      font-weight: 600;
      color: var(--ion-color-primary);
      margin: 0;
    }
  }

  .login-container {
    width: 90%;
    max-width: 400px;
    margin: 0 auto;
    padding: 20px;
    border-radius: var(--app-border-radius-lg);
    background-color: white;
    box-shadow: var(--app-card-shadow);

    h2 {
      margin-bottom: 24px;
      color: var(--ion-color-primary);
      font-weight: 500;
      text-align: center;
    }

    .custom-input {
      margin-bottom: 16px;
      border-radius: var(--app-border-radius-md);
      --background: var(--app-input-background);
      --border-radius: var(--app-border-radius-md);
      --padding-start: 12px;

      ion-icon {
        color: var(--ion-color-primary);
        margin-right: 8px;
      }
    }

    .login-btn {
      margin-top: 24px;
      --border-radius: var(--app-border-radius-md);
      font-weight: 500;
      height: 48px;
      text-transform: none;
      font-size: 16px;
      box-shadow: 0 4px 8px rgba(46, 125, 50, 0.2);
    }

    .links {
      margin-top: 24px;
      display: flex;
      justify-content: space-between;
      font-size: 0.9em;
      padding: 0 8px;

      a {
        color: var(--ion-color-primary);
        text-decoration: none;
        display: flex;
        align-items: center;

        ion-icon {
          margin-right: 4px;
        }

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}
