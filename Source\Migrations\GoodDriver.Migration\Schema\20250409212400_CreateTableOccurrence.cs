﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Migration.Schema
{
	[FluentMigrator.Migration(20250409212400)]
	public class CreateTableOccurrence : FluentMigrator.Migration
	{
		public override void Down()
		{
			if (Schema.Table("Occurrence").Exists())
			{
				Delete.Table("Occurrence");
			}
		}

		public override void Up()
		{
			if (!Schema.Table("Occurrence").Exists())
			{
				Create.Table("Occurrence")
					.WithColumn("Id").AsInt32().PrimaryKey().NotNullable()
					.WithColumn("Name").AsString(59).NotNullable()
					.WithColumn("OccurrenceType").AsString(200).NotNullable()
					.WithColumn("Ico").AsString(100).Nullable()
					.WithColumn("DiscountScore").AsInt32().NotNullable()
					.WithColumn("CreatedOn").AsDateTime().NotNullable()
					.WithColumn("UpdatedOn").AsDateTime().Nullable()
					.WithColumn("Message").AsString(100).NotNullable(); 

            }
		}	
	}
}
