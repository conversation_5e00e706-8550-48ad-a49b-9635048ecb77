import { Injectable } from '@angular/core';
import { CapacitorSQLite, SQLiteConnection, SQLiteDBConnection } from '@capacitor-community/sqlite';
import { IDataStorage } from './data-storage.interface';
import { Platform } from '@ionic/angular';

@Injectable({
  providedIn: 'root',
})
export class SQLiteStorageService implements IDataStorage {
  private sqlite: SQLiteConnection;
  private db: SQLiteDBConnection | null = null;
  private dbName = 'journeydb';
  private dbVersion = 2; // Incrementada para incluir novos campos
  private isNative = false;

  constructor(private platform: Platform) {
    console.log('SQLiteStorageService constructor called');
    this.sqlite = new SQLiteConnection(CapacitorSQLite);
    this.isNative = this.platform.is('capacitor') || this.platform.is('cordova');
    console.log('Platform detected:', {
      platforms: this.platform.platforms(),
      isNative: this.isNative,
      isCapacitor: this.platform.is('capacitor'),
      isCordova: this.platform.is('cordova')
    });

    // Check if SQLite plugin is available
    this.checkPluginAvailability();
  }

  private async checkPluginAvailability(): Promise<void> {
    console.log('Checking plugin availability...');
    if (this.isNative) {
      try {
        console.log('Checking if CapacitorSQLite plugin is available...');
        // Try to call a simple method to check if plugin is available
        const result = await this.sqlite.checkConnectionsConsistency();
        console.log('CapacitorSQLite plugin is working:', result);
      } catch (error) {
        console.error('Error checking CapacitorSQLite plugin availability:', error);
        console.error('This usually means the plugin is not properly installed or registered');
        console.error('Falling back to non-native storage...');
        this.isNative = false; // Fallback to IndexedDB/LocalStorage
      }
    }
  }

  // Inicializa o banco de dados
  async init(): Promise<void> {
    try {
      const result = await this.sqlite.echo('Hello');
      console.log('SQLite echo:', result); // deve imprimir "Hello"
    } catch (err) {
      console.error('SQLite plugin não está funcionando:', err);
    }
    if (!(await this.sqlite.checkConnectionsConsistency())) {
      throw new Error('SQLite não está disponível');
    }
    console.log('Criando conexão com o banco...');
    if (this.isNative) {
      console.log('Is native db');
      try {
        console.log('Checking SQLite plugin availability...');
        // Check if the database exists and create it if it doesn't
        const dbExists = (await this.sqlite.checkConnectionsConsistency()).result;
        console.log('Existing db...');
        const isConn = (await this.sqlite.isConnection(this.dbName, false)).result;

        if (dbExists && isConn) {
          console.log('Recuperar conexão com o banco...');
          this.db = await this.sqlite.retrieveConnection(this.dbName, false);
        } else {
          console.log('Criando conexão com o banco 2...');
          this.db = await this.sqlite.createConnection(
            this.dbName,
            false,
            'no-encryption',
            this.dbVersion,
            false
          );
        }
        console.log('Abrindo conexão com o banco...');
        await this.db.open();

        console.log('Criando/atualizando tabelas...');
        await this.createTable();
        await this.performMigrations();
        console.log('SQLite database initialized successfully');
      } catch (error) {
        console.error('Error initializing SQLite database:', error);
      }
    } else {
      // No navegador não faz nada, ou pode inicializar outro storage
      console.log('Running on browser: SQLiteStorageService will not initialize.');
    }
  }

  async insert(table: string, data: any): Promise<any> {
    if (!this.db || !this.isNative) return;

    try {
      const columns = Object.keys(data).join(',');
      const placeholders = Object.keys(data).map(() => '?').join(',');
      const values = Object.values(data);

      const query = `INSERT INTO ${table} (${columns}) VALUES (${placeholders})`;
      console.log('SQLite INSERT Query:', query);
      console.log('SQLite INSERT Values:', values);

      const result = await this.db.run(query, values);
      console.log('SQLite INSERT Result:', result);
      return result;
    } catch (error) {
      console.error(`Error inserting into ${table}:`, error);
      throw error;
    }
  }

  async select(table: string, condition: string = ''): Promise<any[]> {
    if (!this.db || !this.isNative) return [];

    try {
      const query = `SELECT * FROM ${table} ${condition}`;
      const result = await this.db.query(query);
      return result.values || [];
    } catch (error) {
      console.error(`Error selecting from ${table}:`, error);
      return [];
    }
  }

  async update(table: string, data: any, condition: string): Promise<any> {
    if (!this.db || !this.isNative) return;

    try {
      const setClause = Object.keys(data)
        .map((key) => `${key} = ?`)
        .join(', ');
      const values = Object.values(data);

      const query = `UPDATE ${table} SET ${setClause} WHERE ${condition}`;
      const result = await this.db.run(query, values);
      return result;
    } catch (error) {
      console.error(`Error updating ${table}:`, error);
      throw error;
    }
  }

  async delete(table: string, condition: string): Promise<void> {
    if (!this.db || !this.isNative) return;

    try {
      const query = `DELETE FROM ${table} WHERE ${condition}`;
      await this.db.run(query);
    } catch (error) {
      console.error(`Error deleting from ${table}:`, error);
      throw error;
    }
  }

  private async createTable() {
    if (!this.db || !this.isNative) return;

    try {
      // Create tables using a transaction for better performance and reliability
      const statements = [
        // Create journeys table
        {
          statement: `
            CREATE TABLE IF NOT EXISTS journeys (
              id TEXT PRIMARY KEY,
              startDate TEXT NOT NULL,
              endDate TEXT,
              distance REAL DEFAULT 0,
              userId TEXT NOT NULL,
              vehicleId TEXT,
              syncStatus TEXT DEFAULT 'SYNCED',
              lastSyncDate TEXT
            );
          `,
          values: []
        },
        // Create journeyInfo table
        {
          statement: `
            CREATE TABLE IF NOT EXISTS journeyInfo (
              id TEXT PRIMARY KEY,
              journeyId TEXT NOT NULL,
              latitude REAL NOT NULL,
              longitude REAL NOT NULL,
              timestamp TEXT NOT NULL,
              address TEXT,
              occurrenceType TEXT,
              accuracy REAL,
              altitude REAL,
              speed REAL,
              syncStatus TEXT DEFAULT 'SYNCED',
              lastSyncDate TEXT,
              FOREIGN KEY (journeyId) REFERENCES journeys(id)
            );
          `,
          values: []
        },
        // Create vehicles table with brandName and modelName columns
        {
          statement: `
            CREATE TABLE IF NOT EXISTS vehicles (
              id TEXT PRIMARY KEY,
              brandId INTEGER NOT NULL,
              brandName TEXT,
              modelId INTEGER NOT NULL,
              modelName TEXT,
              userId TEXT NOT NULL,
              plate TEXT NOT NULL,
              year INTEGER NOT NULL,
              version TEXT,
              policyNumber TEXT,
              isPrimary INTEGER DEFAULT 0,
              createdOn TEXT NOT NULL,
              updatedOn TEXT,
              syncStatus TEXT DEFAULT 'SYNCED',
              lastSyncDate TEXT
            );
          `,
          values: []
        }
      ];

      // Execute all statements in a transaction
      const result = await this.db.executeSet(statements);
      if (result.changes && result.changes.changes !== undefined && result.changes.changes > 0) {
        console.log(`Tables created successfully. Changes: ${result.changes.changes}`);
      } else {
        console.log('Tables already exist or no changes were made');
      }

    } catch (error) {
      console.error('Error creating tables:', error);
    }
  }

  /**
   * Executa migrações do banco de dados para versões anteriores
   */
  private async performMigrations(): Promise<void> {
    if (!this.db || !this.isNative) return;

    try {
      console.log('Verificando necessidade de migrações...');

      // Verificar se os novos campos existem na tabela journeyInfo
      const tableInfo = await this.db.query(`PRAGMA table_info(journeyInfo)`);
      const columns = tableInfo.values?.map((row: any) => row.name) || [];

      console.log('Colunas existentes na tabela journeyInfo:', columns);

      const newColumns = [
        { name: 'address', type: 'TEXT' },
        { name: 'occurrenceType', type: 'TEXT' },
        { name: 'accuracy', type: 'REAL' },
        { name: 'altitude', type: 'REAL' },
        { name: 'speed', type: 'REAL' }
      ];

      // Adicionar colunas que não existem
      for (const column of newColumns) {
        if (!columns.includes(column.name)) {
          console.log(`Adicionando coluna ${column.name} à tabela journeyInfo...`);
          await this.db.run(`ALTER TABLE journeyInfo ADD COLUMN ${column.name} ${column.type}`);
          console.log(`Coluna ${column.name} adicionada com sucesso`);
        } else {
          console.log(`Coluna ${column.name} já existe`);
        }
      }

      console.log('Migrações concluídas com sucesso');

    } catch (error) {
      console.error('Erro durante migrações:', error);
      // Não falhar a inicialização por causa de migrações
      // Em produção, você pode querer implementar uma estratégia de rollback
    }
  }

  /**
   * Obtém informações sobre a estrutura de uma tabela
   */
  async getTableInfo(tableName: string): Promise<any[]> {
    if (!this.db || !this.isNative) return [];

    try {
      const result = await this.db.query(`PRAGMA table_info(${tableName})`);
      return result.values || [];
    } catch (error) {
      console.error(`Erro ao obter informações da tabela ${tableName}:`, error);
      return [];
    }
  }

  /**
   * Verifica se uma coluna existe em uma tabela
   */
  async columnExists(tableName: string, columnName: string): Promise<boolean> {
    const tableInfo = await this.getTableInfo(tableName);
    return tableInfo.some((column: any) => column.name === columnName);
  }

  /**
   * Força a recriação do banco de dados (útil para desenvolvimento)
   */
  async resetDatabase(): Promise<void> {
    if (!this.isNative) return;

    try {
      console.log('Resetando banco de dados...');

      if (this.db) {
        await this.db.close();
        this.db = null;
      }

      // Remove a conexão existente
      const isConn = (await this.sqlite.isConnection(this.dbName, false)).result;
      if (isConn) {
        await this.sqlite.closeConnection(this.dbName, false);
      }

      // Remove o banco de dados (se suportado pela versão do plugin)
      try {
        await this.sqlite.deleteOldDatabases();
      } catch (error) {
        console.log('Método deleteOldDatabases não disponível, continuando...');
      }

      console.log('Banco de dados resetado com sucesso');

      // Reinicializa o banco
      await this.init();

    } catch (error) {
      console.error('Erro ao resetar banco de dados:', error);
    }
  }

  /**
   * Obtém a versão atual do banco de dados
   */
  getDatabaseVersion(): number {
    return this.dbVersion;
  }
}
