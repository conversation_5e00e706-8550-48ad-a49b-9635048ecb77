import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { VehicleService } from 'src/app/core/services/vehicle.service';
import { ToastService } from 'src/app/core/services/toast.service';

import { BrandService } from 'src/app/core/services/brand.service';
import { ModelService } from 'src/app/core/services/model.service';
import { SessionService } from 'src/app/core/services/session.service';
import { NetworkService } from 'src/app/core/services/network.service';
import { Brand } from 'src/app/core/models/brand.model';
import { Model } from 'src/app/core/models/model.model';
import { SyncStatus } from 'src/app/core/models/sync.model';

import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Subscription } from 'rxjs';


@Component({
  selector: 'app-edit-vehicle',
  templateUrl: './edit-vehicle.page.html',
  styleUrls: ['./edit-vehicle.page.scss'],
  standalone: true,
  imports: [IonicModule, ReactiveFormsModule, CommonModule]
})
export class EditVehiclePage implements OnInit, OnDestroy {
  vehicleForm: FormGroup;
  vehicleId: string = '';
  isLoading = true;
  isSubmitting = false;
  isOnline = true;

  brands: Brand[] = [];
  models: Model[] = [];

  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private vehicleService: VehicleService,
    private toastService: ToastService,
    private brandService: BrandService,
    private modelService: ModelService,
    private sessionService: SessionService,
    private networkService: NetworkService
  ) {
    this.vehicleForm = this.fb.group({
      brand: [null, Validators.required],
      model: [null, Validators.required],
      version: [null],
      year: [null, [Validators.required, Validators.min(1990), Validators.max(2026)]],
      plate: [null, [Validators.required]],
      policy: [null],
      isPrimary: [false]
    });
  }

  async ngOnInit() {
    this.vehicleId = this.route.snapshot.paramMap.get('id') || '';

    // Check network status
    this.checkNetworkStatus();

    // Subscribe to network status changes
    this.subscriptions.push(
      this.networkService.getOnlineStatus().subscribe(isOnline => {
        this.isOnline = isOnline;
        if (isOnline) {
          // If we're back online and don't have brands loaded yet, load them
          if (this.brands.length === 0) {
            this.loadBrandsAndVehicle();
          }
        }
      })
    );

    // Load brands and vehicle data if online
    if (this.isOnline) {
      await this.loadBrandsAndVehicle();
    } else if (this.vehicleId) {
      // If offline, still try to load vehicle data (might be cached)
      await this.loadVehicle();
    }

    this.isLoading = false;
  }

  async loadBrandsAndVehicle() {
    try {
      // First load brands
      await this.loadBrands();

      // Then load vehicle data if we have an ID
      if (this.vehicleId) {
        await this.loadVehicle();
      }
    } catch (error) {
      console.error('Error loading brands and vehicle:', error);
    }
  }

  ngOnDestroy() {
    // Clean up subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Check the current network status
   */
  private checkNetworkStatus() {
    this.isOnline = this.networkService.isOnlineNow();
  }

  async loadBrands() {
    try {
      // Check if we're online before trying to load brands
      if (!this.isOnline) {
        this.toastService.showToast('Sem conexão com a internet. Não é possível carregar as marcas de veículos.', 'warning');
        return;
      }

      console.log('Loading brands...');
      const data = await this.brandService.listAll();
      this.brands = data;
      console.log('Brands loaded:', this.brands);

    } catch (err) {
      console.error('Error to load brands:', err);
      this.toastService.showToast('Erro ao carregar marcas de veículos', 'danger');
    }
  }

  async loadVehicle() {
    try {
      const vehicle = await this.vehicleService.getById(this.vehicleId);
      if (vehicle) {
        console.log('Vehicle loaded:', vehicle);

        // First patch the basic vehicle data
        this.vehicleForm.patchValue({
          year: vehicle.year,
          plate: vehicle.plate,
          version: vehicle.version,
          policy: vehicle.policyNumber,
          isPrimary: vehicle.isPrimary
        });

        // Set brand value and load models
        if (vehicle.brandId) {
          console.log('Setting brand:', vehicle.brandId);

          // Load models for the selected brand first
          await this.onBrandChange(vehicle.brandId.toString(), vehicle.modelId.toString());

          // Force update form values to ensure they are set correctly
          this.forceUpdateFormValues(vehicle.brandId.toString(), vehicle.modelId.toString());

          // Debug form values after loading
          setTimeout(() => this.debugFormValues(), 500);
        }
      } else {
        this.toastService.showToast('Veículo não encontrado', 'danger');
        this.router.navigate(['/tabs/vehicles']);
      }
    } catch (error) {
      console.error('Error loading vehicle:', error);
      this.toastService.showToast('Erro ao carregar veículo', 'danger');
    }
  }

  async onBrandChange(brandId: any, modelIdToSelect?: any) {
    try {
      // Check if we're online before trying to load models
      if (!this.isOnline) {
        this.toastService.showToast('Sem conexão com a internet. Não é possível carregar os modelos de veículos.', 'warning');
        return;
      }

      console.log('Loading models for brand:', brandId);

      // Clear model selection first
      this.vehicleForm.patchValue({ model: null });

      // Load models for the selected brand
      const data = await this.modelService.getByBranch({ brandId: parseInt(brandId) });
      this.models = data;

      console.log('Models loaded:', this.models);

      // If we have a specific model to select (when loading existing vehicle)
      if (modelIdToSelect) {
        console.log('Setting model:', modelIdToSelect);
        // Use setTimeout to ensure the models are rendered in the select before setting the value
        setTimeout(() => {
          this.vehicleForm.patchValue({ model: modelIdToSelect });
          console.log('Model set in form:', this.vehicleForm.get('model')?.value);
        }, 100);
      }
    }
    catch (err) {
      console.error('Error to load models:', err);
      this.toastService.showToast('Erro ao carregar modelos de veículos', 'danger');
    }
  }

  // This method is called when the model selection changes
  onModelChange(_modelId: any) {
    // Currently not implemented, but kept for future use
  }

  /**
   * Force update form values - useful when dealing with async loading
   */
  private forceUpdateFormValues(brandId: string, modelId: string) {
    // Use a small delay to ensure the DOM is updated
    setTimeout(() => {
      console.log('Force updating form values - Brand:', brandId, 'Model:', modelId);
      this.vehicleForm.patchValue({
        brand: brandId,
        model: modelId
      });

      // Trigger change detection
      this.vehicleForm.updateValueAndValidity();

      console.log('Form values after force update:', this.vehicleForm.value);
    }, 200);
  }

  async onSubmit() {
    if (this.vehicleForm.invalid) {
      this.toastService.showToast('Por favor, preencha todos os campos obrigatórios corretamente');
      return;
    }

    // Check if we're online before trying to save
    if (!this.isOnline) {
      this.toastService.showToast('Sem conexão com a internet. Não é possível atualizar o veículo.', 'warning');
      return;
    }

    this.isSubmitting = true;

    try {
      const formValues = this.vehicleForm.value;
      const userId = await this.sessionService.getUserId() || '';

      // Find the selected brand and model to get their names
      const selectedBrandId = formValues.brand;
      const selectedModelId = formValues.model;

      // Find the brand and model objects
      const selectedBrand = this.brands.find(brand => brand.id === selectedBrandId);
      const selectedModel = this.models.find(model => model.id === selectedModelId);

      // Validate that we have brand and model information
      if (!selectedBrand || !selectedModel) {
        this.toastService.showToast('Informações de marca ou modelo incompletas. Verifique sua conexão.', 'warning');
        return;
      }

      const updatedVehicleData = {
        id: this.vehicleId,
        userId: userId,
        plate: formValues.plate,
        year: parseInt(formValues.year),
        brandId: parseInt(formValues.brand),
        brandName: selectedBrand?.name || '',
        modelId: parseInt(formValues.model),
        modelName: selectedModel?.name || '',
        version: formValues.version,
        policyNumber: formValues.policy,
        isPrimary: formValues.isPrimary,
        updatedOn: new Date(),
        syncStatus: SyncStatus.PendingUpdate,
        lastSyncDate: undefined
      };

      await this.vehicleService.update(this.vehicleId, updatedVehicleData);
      this.toastService.showToast('Veículo atualizado com sucesso!');
      this.router.navigate(['/tabs/vehicles']);
    } catch (error: any) {
      console.error('Error updating vehicle:', error);
      this.toastService.showToast(error.error?.message || 'Erro ao atualizar veículo', 'danger');
    } finally {
      this.isSubmitting = false;
    }
  }

  public onCancel(): void {
    this.router.navigate(['/tabs/vehicles']);
  }

  /**
   * Compare function for ion-select to properly match values
   */
  compareWith = (o1: any, o2: any) => {
    return o1 && o2 ? o1 === o2 : o1 === o2;
  }

  /**
   * Debug method to check form values
   */
  debugFormValues() {
    console.log('=== FORM DEBUG ===');
    console.log('Form valid:', this.vehicleForm.valid);
    console.log('Form values:', this.vehicleForm.value);
    console.log('Brand control value:', this.vehicleForm.get('brand')?.value);
    console.log('Model control value:', this.vehicleForm.get('model')?.value);
    console.log('Available brands:', this.brands.map(b => ({ id: b.id, name: b.name })));
    console.log('Available models:', this.models.map(m => ({ id: m.id, name: m.name })));
    console.log('==================');
  }
} 