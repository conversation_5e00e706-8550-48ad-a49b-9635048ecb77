import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { VehicleService } from 'src/app/core/services/vehicle.service';
import { ToastService } from 'src/app/core/services/toast.service';
import { Vehicle } from 'src/app/core/models/vehicle.model';
import { IonHeader } from "@ionic/angular/standalone";
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';


@Component({
  selector: 'app-edit-vehicle',
  templateUrl: './edit-vehicle.page.html',
  styleUrls: ['./edit-vehicle.page.scss'],
  standalone: true,
  imports: [IonicModule, ReactiveFormsModule, CommonModule]
})
export class EditVehiclePage implements OnInit {
  vehicleForm: FormGroup;
  vehicleId: string = '';
  isLoading = true;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private vehicleService: VehicleService,
    private toastService: ToastService
  ) {
    this.vehicleForm = this.fb.group({
      id: [{ value: '', disabled: true }],
      brandId: ['', Validators.required],
      brandName: [''],
      modelId: ['', Validators.required],
      modelName: [''],
      userId: ['', Validators.required],
      plate: ['', Validators.required],
      year: ['', Validators.required],
      version: [''],
      policyNumber: [''],
      isPrimary: [false],
      createdOn: [''],
      updatedOn: [''],
      syncStatus: [''],
      lastSyncDate: ['']
    });
  }

  async ngOnInit() {
    this.vehicleId = this.route.snapshot.paramMap.get('id') || '';
    if (this.vehicleId) {
      await this.loadVehicle();
    }
  }

  async loadVehicle() {
    this.isLoading = true;
    try {
      const vehicle = await this.vehicleService.getById(this.vehicleId);
      if (vehicle) {
        this.vehicleForm.patchValue(vehicle);
      } else {
        this.toastService.showToast('Veículo não encontrado', 'danger');
        this.router.navigate(['/tabs/vehicles']);
      }
    } catch (error) {
      this.toastService.showToast('Erro ao carregar veículo', 'danger');
    } finally {
      this.isLoading = false;
    }
  }

  async onSubmit() {
    if (this.vehicleForm.invalid) return;
    const updatedVehicle = {
      ...this.vehicleForm.getRawValue(),
      id: this.vehicleId
    };
    try {
      await this.vehicleService.update(this.vehicleId, updatedVehicle);
      this.toastService.showToast('Veículo atualizado com sucesso');
      this.router.navigate(['/tabs/vehicles']);
    } catch (error) {
      this.toastService.showToast('Erro ao atualizar veículo', 'danger');
    }
  }

  public onCancel(): void {
    this.router.navigate(['/tabs/vehicles']);
  }
} 