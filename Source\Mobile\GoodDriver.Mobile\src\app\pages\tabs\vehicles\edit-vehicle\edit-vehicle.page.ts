import { Component, OnInit, OnDestroy } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { VehicleService } from 'src/app/core/services/vehicle.service';
import { ToastService } from 'src/app/core/services/toast.service';
import { Vehicle } from 'src/app/core/models/vehicle.model';
import { BrandService } from 'src/app/core/services/brand.service';
import { ModelService } from 'src/app/core/services/model.service';
import { SessionService } from 'src/app/core/services/session.service';
import { NetworkService } from 'src/app/core/services/network.service';
import { Brand } from 'src/app/core/models/brand.model';
import { Model } from 'src/app/core/models/model.model';
import { SyncStatus } from 'src/app/core/models/sync.model';
import { IonHeader } from "@ionic/angular/standalone";
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Subscription } from 'rxjs';


@Component({
  selector: 'app-edit-vehicle',
  templateUrl: './edit-vehicle.page.html',
  styleUrls: ['./edit-vehicle.page.scss'],
  standalone: true,
  imports: [IonicModule, ReactiveFormsModule, CommonModule]
})
export class EditVehiclePage implements OnInit, OnDestroy {
  vehicleForm: FormGroup;
  vehicleId: string = '';
  isLoading = true;
  isSubmitting = false;
  isOnline = true;

  brands: Brand[] = [];
  models: Model[] = [];

  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private vehicleService: VehicleService,
    private toastService: ToastService,
    private brandService: BrandService,
    private modelService: ModelService,
    private sessionService: SessionService,
    private networkService: NetworkService
  ) {
    this.vehicleForm = this.fb.group({
      brand: [null, Validators.required],
      model: [null, Validators.required],
      version: [null],
      year: [null, [Validators.required, Validators.min(1990), Validators.max(2026)]],
      plate: [null, [Validators.required]],
      policy: [null],
      isPrimary: [false]
    });
  }

  async ngOnInit() {
    this.vehicleId = this.route.snapshot.paramMap.get('id') || '';

    // Check network status
    this.checkNetworkStatus();

    // Subscribe to network status changes
    this.subscriptions.push(
      this.networkService.getOnlineStatus().subscribe(isOnline => {
        this.isOnline = isOnline;
        if (isOnline) {
          // If we're back online and don't have brands loaded yet, load them
          if (this.brands.length === 0) {
            this.loadBrands();
          }
        }
      })
    );

    // Load brands if online
    if (this.isOnline) {
      await this.loadBrands();
    }

    if (this.vehicleId) {
      await this.loadVehicle();
    }

    this.isLoading = false;
  }

  ngOnDestroy() {
    // Clean up subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Check the current network status
   */
  private checkNetworkStatus() {
    this.isOnline = this.networkService.isOnlineNow();
  }

  async loadBrands() {
    try {
      // Check if we're online before trying to load brands
      if (!this.isOnline) {
        this.toastService.showToast('Sem conexão com a internet. Não é possível carregar as marcas de veículos.', 'warning');
        return;
      }

      const data = await this.brandService.listAll();
      this.brands = data;

    } catch (err) {
      console.error('Error to load brands:', err);
      this.toastService.showToast('Erro ao carregar marcas de veículos', 'danger');
    }
  }

  async loadVehicle() {
    try {
      const vehicle = await this.vehicleService.getById(this.vehicleId);
      if (vehicle) {
        // Patch form with vehicle data
        this.vehicleForm.patchValue({
          brand: vehicle.brandId.toString(),
          year: vehicle.year,
          plate: vehicle.plate,
          version: vehicle.version,
          policy: vehicle.policyNumber,
          isPrimary: vehicle.isPrimary
        });

        // Load models for the selected brand and then select the model
        if (vehicle.brandId) {
          await this.onBrandChange(vehicle.brandId.toString(), vehicle.modelId.toString());
        }
      } else {
        this.toastService.showToast('Veículo não encontrado', 'danger');
        this.router.navigate(['/tabs/vehicles']);
      }
    } catch (error) {
      console.error('Error loading vehicle:', error);
      this.toastService.showToast('Erro ao carregar veículo', 'danger');
    }
  }

  async onBrandChange(brandId: any, modelIdToSelect?: any) {
    try {
      // Check if we're online before trying to load models
      if (!this.isOnline) {
        this.toastService.showToast('Sem conexão com a internet. Não é possível carregar os modelos de veículos.', 'warning');
        return;
      }

      this.vehicleForm.patchValue({ model: null, version: null });
      const data = await this.modelService.getByBranch({ brandId: parseInt(brandId) });
      this.models = data;

      // If we have a specific model to select (when loading existing vehicle)
      if (modelIdToSelect) {
        this.vehicleForm.patchValue({ model: modelIdToSelect });
      }
    }
    catch (err) {
      console.error('Error to load models:', err);
      this.toastService.showToast('Erro ao carregar modelos de veículos', 'danger');
    }
  }

  // This method is called when the model selection changes
  onModelChange(_modelId: any) {
    // Currently not implemented, but kept for future use
  }

  async onSubmit() {
    if (this.vehicleForm.invalid) {
      this.toastService.showToast('Por favor, preencha todos os campos obrigatórios corretamente');
      return;
    }

    // Check if we're online before trying to save
    if (!this.isOnline) {
      this.toastService.showToast('Sem conexão com a internet. Não é possível atualizar o veículo.', 'warning');
      return;
    }

    this.isSubmitting = true;

    try {
      const formValues = this.vehicleForm.value;
      const userId = await this.sessionService.getUserId() || '';

      // Find the selected brand and model to get their names
      const selectedBrandId = formValues.brand;
      const selectedModelId = formValues.model;

      // Find the brand and model objects
      const selectedBrand = this.brands.find(brand => brand.id === selectedBrandId);
      const selectedModel = this.models.find(model => model.id === selectedModelId);

      // Validate that we have brand and model information
      if (!selectedBrand || !selectedModel) {
        this.toastService.showToast('Informações de marca ou modelo incompletas. Verifique sua conexão.', 'warning');
        return;
      }

      const updatedVehicleData = {
        id: this.vehicleId,
        userId: userId,
        plate: formValues.plate,
        year: parseInt(formValues.year),
        brandId: parseInt(formValues.brand),
        brandName: selectedBrand?.name || '',
        modelId: parseInt(formValues.model),
        modelName: selectedModel?.name || '',
        version: formValues.version,
        policyNumber: formValues.policy,
        isPrimary: formValues.isPrimary,
        updatedOn: new Date(),
        syncStatus: SyncStatus.PendingUpdate,
        lastSyncDate: undefined
      };

      await this.vehicleService.update(this.vehicleId, updatedVehicleData);
      this.toastService.showToast('Veículo atualizado com sucesso!');
      this.router.navigate(['/tabs/vehicles']);
    } catch (error: any) {
      console.error('Error updating vehicle:', error);
      this.toastService.showToast(error.error?.message || 'Erro ao atualizar veículo', 'danger');
    } finally {
      this.isSubmitting = false;
    }
  }

  public onCancel(): void {
    this.router.navigate(['/tabs/vehicles']);
  }
} 