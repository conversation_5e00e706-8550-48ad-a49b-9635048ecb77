{"version": 3, "sources": ["src/app/core/services/sync.service.ts", "src/app/shared/components/sync-status/sync-status.component.ts", "src/app/shared/components/sync-status/sync-status.component.html", "src/app/pages/sync/sync.page.ts", "src/app/pages/sync/sync.page.html"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable } from 'rxjs';\nimport { NetworkService } from './network.service';\nimport { VehicleService } from './vehicle.service';\nimport { JourneyStorageService } from './journey-storage.service';\nimport { SessionService } from './session.service';\nimport { ToastService } from './toast.service';\nimport { SyncStatus } from '../models/sync.model';\nimport { Vehicle } from '../models/vehicle.model';\nimport { Journey } from '../models/journey.model';\nimport { JourneyInfo } from '../models/journeyInfo.model';\nimport { User } from '../models/user.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SyncService {\n  // BehaviorSubject to track the sync status\n  private syncInProgressSubject = new BehaviorSubject<boolean>(false);\n  \n  // Observable that components can subscribe to\n  public syncInProgress$: Observable<boolean> = this.syncInProgressSubject.asObservable();\n  \n  // BehaviorSubject to track the last sync time\n  private lastSyncTimeSubject = new BehaviorSubject<Date | null>(null);\n  \n  // Observable that components can subscribe to\n  public lastSyncTime$: Observable<Date | null> = this.lastSyncTimeSubject.asObservable();\n  \n  // BehaviorSubject to track the pending sync count\n  private pendingSyncCountSubject = new BehaviorSubject<number>(0);\n  \n  // Observable that components can subscribe to\n  public pendingSyncCount$: Observable<number> = this.pendingSyncCountSubject.asObservable();\n\n  constructor(\n    private networkService: NetworkService,\n    private vehicleService: VehicleService,\n    private journeyService: JourneyStorageService,\n    private sessionService: SessionService,\n    private toastService: ToastService\n  ) {\n    // Initialize the last sync time from local storage\n    const lastSyncTime = localStorage.getItem('lastSyncTime');\n    if (lastSyncTime) {\n      this.lastSyncTimeSubject.next(new Date(lastSyncTime));\n    }\n    \n    // Check for pending sync items on initialization\n    this.checkPendingSyncItems();\n  }\n\n  /**\n   * Checks for items that need to be synchronized\n   */\n  async checkPendingSyncItems(): Promise<void> {\n    try {\n      const userId = await this.sessionService.getUserId();\n      if (!userId) return;\n      \n      // Get all vehicles for the user\n      const vehicles = await this.vehicleService.listAllLocal(userId);\n      \n      // Get all journeys for the user\n      const journeys = await this.journeyService.getAllJourneysByUser(userId);\n      \n      // Count items that need to be synced\n      const pendingVehicles = vehicles.filter(v => \n        v.syncStatus === SyncStatus.PendingCreate || \n        v.syncStatus === SyncStatus.PendingUpdate || \n        v.syncStatus === SyncStatus.PendingDelete\n      );\n      \n      const pendingJourneys = journeys.filter(j => \n        j.syncStatus === SyncStatus.PendingCreate || \n        j.syncStatus === SyncStatus.PendingUpdate || \n        j.syncStatus === SyncStatus.PendingDelete\n      );\n      \n      // Update the pending sync count\n      const totalPending = pendingVehicles.length + pendingJourneys.length;\n      this.pendingSyncCountSubject.next(totalPending);\n      \n    } catch (error) {\n      console.error('Error checking pending sync items:', error);\n    }\n  }\n\n  /**\n   * Synchronizes all pending items with the server\n   */\n  async syncAll(): Promise<boolean> {\n    // Check if we're online\n    if (!this.networkService.isOnlineNow()) {\n      this.toastService.showToast('Sem conexão com a internet. Não é possível sincronizar.', 'warning');\n      return false;\n    }\n    \n    // Check if sync is already in progress\n    if (this.syncInProgressSubject.getValue()) {\n      this.toastService.showToast('Sincronização já em andamento.', 'info');\n      return false;\n    }\n    \n    // Set sync in progress\n    this.syncInProgressSubject.next(true);\n    \n    try {\n      const userId = await this.sessionService.getUserId();\n      if (!userId) {\n        this.syncInProgressSubject.next(false);\n        return false;\n      }\n      \n      // Sync vehicles\n      const vehicleSuccess = await this.syncVehicles(userId);\n      \n      // Sync journeys\n      const journeySuccess = await this.syncJourneys(userId);\n      \n      // Update last sync time\n      const now = new Date();\n      this.lastSyncTimeSubject.next(now);\n      localStorage.setItem('lastSyncTime', now.toISOString());\n      \n      // Check for pending items again\n      await this.checkPendingSyncItems();\n      \n      // Show success message\n      this.toastService.showToast('Sincronização concluída com sucesso!', 'success');\n      \n      return vehicleSuccess && journeySuccess;\n    } catch (error) {\n      console.error('Error during synchronization:', error);\n      this.toastService.showToast('Erro durante a sincronização.', 'danger');\n      return false;\n    } finally {\n      // Set sync not in progress\n      this.syncInProgressSubject.next(false);\n    }\n  }\n\n  /**\n   * Synchronizes vehicles with the server\n   */\n  private async syncVehicles(userId: string): Promise<boolean> {\n    try {\n      // Get all vehicles for the user\n      const vehicles = await this.vehicleService.listAllLocal(userId);\n      \n      // Filter vehicles that need to be synced\n      const pendingVehicles = vehicles.filter(v => \n        v.syncStatus === SyncStatus.PendingCreate || \n        v.syncStatus === SyncStatus.PendingUpdate || \n        v.syncStatus === SyncStatus.PendingDelete\n      );\n          \n      // Update the sync status of all pending vehicles\n      for (const vehicle of pendingVehicles) {\n        const updatedVehicle = {\n          ...vehicle,\n          syncStatus: SyncStatus.Synced,\n          lastSyncDate: new Date()\n        };\n        \n        // Send the vehicle to the server and Update the vehicle in local storage        \n        await this.vehicleService.sendVehicleToSync({\n          vehicleId: updatedVehicle.id,\n          userId: updatedVehicle.userId,\n          plate: updatedVehicle.plate,\n          year: updatedVehicle.year,\n          brandId: updatedVehicle.brandId,\n          modelId: updatedVehicle.modelId,\n          version: updatedVehicle.version,\n          policyNumber: updatedVehicle.policyNumber\n        });\n        await this.vehicleService.updateVehicleSync(updatedVehicle);\n      }\n      \n      return true;\n    } catch (error) {\n      console.error('Error syncing vehicles:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Synchronizes journeys with the server\n   */\n  private async syncJourneys(userId: string): Promise<boolean> {\n    try {\n      // Get all journeys for the user\n      const journeys = await this.journeyService.getAllJourneysByUser(userId);\n      \n      // Filter journeys that need to be synced\n      const pendingJourneys = journeys.filter(j => \n        j.syncStatus === SyncStatus.PendingCreate || \n        j.syncStatus === SyncStatus.PendingUpdate || \n        j.syncStatus === SyncStatus.PendingDelete\n      );\n           \n      // Update the sync status of all pending journeys\n      for (const journey of pendingJourneys) {\n        const updatedJourney = {\n          ...journey,\n          syncStatus: SyncStatus.Synced,\n          lastSyncDate: new Date()\n        };\n\n        //sync journey API\n        await this.journeyService.sendJourneyToSync({\n          id: updatedJourney.id,\n          startDate: updatedJourney.startDate,\n          endDate: updatedJourney.endDate,\n          distance: updatedJourney.distance,\n          userId: updatedJourney.userId,\n          vehicleId: updatedJourney.vehicleId,\n          infosJourney: updatedJourney.infosJourney\n        });\n        \n        // Update the journey in local storage        \n        await this.journeyService.updateJourneySync(updatedJourney);\n      }\n      \n      return true;\n    } catch (error) {\n      console.error('Error syncing journeys:', error);\n      return false;\n    }\n  }\n}\n\n\n\n", "import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { IonicModule } from '@ionic/angular';\nimport { CommonModule } from '@angular/common';\nimport { SyncService } from 'src/app/core/services/sync.service';\nimport { NetworkService } from 'src/app/core/services/network.service';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-sync-status',\n  templateUrl: './sync-status.component.html',\n  styleUrls: ['./sync-status.component.scss'],\n  standalone: true,\n  imports: [IonicModule, CommonModule]\n})\nexport class SyncStatusComponent implements OnInit, OnDestroy {\n  isOnline = true;\n  syncInProgress = false;\n  pendingSyncCount = 0;\n  lastSyncTime: Date | null = null;\n  \n  // Subscriptions\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private syncService: SyncService,\n    private networkService: NetworkService\n  ) {}\n\n  ngOnInit() {\n    // Subscribe to network status\n    this.subscriptions.push(\n      this.networkService.getOnlineStatus().subscribe(isOnline => {\n        this.isOnline = isOnline;\n      })\n    );\n    \n    // Subscribe to sync status\n    this.subscriptions.push(\n      this.syncService.syncInProgress$.subscribe(inProgress => {\n        this.syncInProgress = inProgress;\n      })\n    );\n    \n    // Subscribe to pending sync count\n    this.subscriptions.push(\n      this.syncService.pendingSyncCount$.subscribe(count => {\n        this.pendingSyncCount = count;\n      })\n    );\n    \n    // Subscribe to last sync time\n    this.subscriptions.push(\n      this.syncService.lastSyncTime$.subscribe(time => {\n        this.lastSyncTime = time;\n      })\n    );\n  }\n  \n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  \n  /**\n   * Returns a formatted string for the last sync time\n   */\n  getLastSyncTimeFormatted(): string {\n    if (!this.lastSyncTime) {\n      return 'Nunca';\n    }\n    \n    return this.lastSyncTime.toLocaleString('pt-BR', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  \n  /**\n   * Returns the appropriate icon for the sync status\n   */\n  getSyncStatusIcon(): string {\n    if (this.syncInProgress) {\n      return 'sync-circle';\n    }\n    \n    if (this.pendingSyncCount > 0) {\n      return 'warning';\n    }\n    \n    return 'checkmark-circle-outline';\n  }\n  \n  /**\n   * Returns the appropriate color for the sync status\n   */\n  getSyncStatusColor(): string {\n    if (this.syncInProgress) {\n      return 'primary';\n    }\n    \n    if (this.pendingSyncCount > 0) {\n      return 'warning';\n    }\n    \n    return 'success';\n  }\n  \n  /**\n   * Returns the appropriate message for the sync status\n   */\n  getSyncStatusMessage(): string {\n    if (this.syncInProgress) {\n      return 'Sincronização em andamento...';\n    }\n    \n    if (this.pendingSyncCount > 0) {\n      return `${this.pendingSyncCount} item(s) aguardando sincronização`;\n    }\n    \n    return 'Todos os dados estão sincronizados';\n  }\n}\n", "<ion-card>\n  <ion-card-header>\n    <ion-card-title>\n      <ion-icon [name]=\"getSyncStatusIcon()\" [color]=\"getSyncStatusColor()\"></ion-icon>\n      Status de Sincronização\n    </ion-card-title>\n  </ion-card-header>\n  <ion-card-content>\n    <ion-item lines=\"none\">\n      <ion-icon slot=\"start\" [name]=\"getSyncStatusIcon()\" [color]=\"getSyncStatusColor()\"></ion-icon>\n      <ion-label>\n        <h2>{{ syncInProgress ? 'Sincronizando...' : (pendingSyncCount > 0 ? 'Pendente' : 'Sincronizado') }}</h2>\n        <p>{{ getSyncStatusMessage() }}</p>\n      </ion-label>\n      <ion-spinner *ngIf=\"syncInProgress\" name=\"crescent\" slot=\"end\"></ion-spinner>\n    </ion-item>\n    \n    <ion-progress-bar *ngIf=\"syncInProgress\" type=\"indeterminate\" [color]=\"getSyncStatusColor()\"></ion-progress-bar>\n    \n    <ion-item lines=\"none\">\n      <ion-icon slot=\"start\" name=\"time-outline\" color=\"medium\"></ion-icon>\n      <ion-label>\n        <h2>Última sincronização</h2>\n        <p>{{ getLastSyncTimeFormatted() }}</p>\n      </ion-label>\n    </ion-item>\n  </ion-card-content>\n</ion-card>\n", "import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport { IonicModule } from '@ionic/angular';\nimport { CommonModule } from '@angular/common';\nimport { SyncService } from 'src/app/core/services/sync.service';\nimport { NetworkService } from 'src/app/core/services/network.service';\nimport { ToastService } from 'src/app/core/services/toast.service';\nimport { SessionService } from 'src/app/core/services/session.service';\nimport { SyncStatusComponent } from 'src/app/shared/components/sync-status/sync-status.component';\nimport { Subscription } from 'rxjs';\nimport { RouterModule } from '@angular/router';\n\n@Component({\n  selector: 'app-sync',\n  templateUrl: './sync.page.html',\n  styleUrls: ['./sync.page.scss'],\n  standalone: true,\n  imports: [IonicModule, CommonModule, SyncStatusComponent, RouterModule]\n})\nexport class SyncPage implements OnInit, OnD<PERSON>roy {\n  isOnline = true;\n  syncInProgress = false;\n  pendingSyncCount = 0;\n  lastSyncTime: Date | null = null;\n  username: string = '';\n  \n  // Subscriptions\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private syncService: SyncService,\n    private networkService: NetworkService,\n    private toastService: ToastService,\n    private sessionService: SessionService\n  ) {}\n\n  async ngOnInit() {\n    // Get username\n    this.username = await this.sessionService.getUserName() || 'Usuário';\n    \n    // Subscribe to network status\n    this.subscriptions.push(\n      this.networkService.getOnlineStatus().subscribe(isOnline => {\n        this.isOnline = isOnline;\n      })\n    );\n    \n    // Subscribe to sync status\n    this.subscriptions.push(\n      this.syncService.syncInProgress$.subscribe(inProgress => {\n        this.syncInProgress = inProgress;\n      })\n    );\n    \n    // Subscribe to pending sync count\n    this.subscriptions.push(\n      this.syncService.pendingSyncCount$.subscribe(count => {\n        this.pendingSyncCount = count;\n      })\n    );\n    \n    // Subscribe to last sync time\n    this.subscriptions.push(\n      this.syncService.lastSyncTime$.subscribe(time => {\n        this.lastSyncTime = time;\n      })\n    );\n  }\n  \n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  \n  /**\n   * Triggers a manual sync\n   */\n  async syncAll() {\n    if (!this.isOnline) {\n      this.toastService.showToast('Sem conexão com a internet. Não é possível sincronizar.', 'warning');\n      return;\n    }\n    \n    if (this.syncInProgress) {\n      this.toastService.showToast('Sincronização já em andamento.', 'info');\n      return;\n    }\n    \n    await this.syncService.syncAll();\n  }\n  \n  /**\n   * Returns a formatted string for the last sync time\n   */\n  getLastSyncTimeFormatted(): string {\n    if (!this.lastSyncTime) {\n      return 'Nunca';\n    }\n    \n    return this.lastSyncTime.toLocaleString('pt-BR', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n}\n", "<ion-header>\n  <ion-toolbar>\n    <ion-title>Sincronização</ion-title>\n  </ion-toolbar>\n</ion-header>\n\n<ion-content class=\"ion-padding\">\n  <ion-card>\n    <ion-card-header>\n      <ion-card-title class=\"ion-text-center\">Be<PERSON>-vindo, {{username}}!</ion-card-title>\n      <ion-card-subtitle class=\"ion-text-center\">Gerenciamento de Sincronização</ion-card-subtitle>\n    </ion-card-header>\n  </ion-card>\n\n  <!-- Status de Conexão -->\n  <ion-card>\n    <ion-card-header>\n      <ion-card-title>\n        <ion-icon [name]=\"isOnline ? 'wifi-outline' : 'wifi-off-outline'\" \n                  [color]=\"isOnline ? 'success' : 'danger'\"></ion-icon>\n        Status de Conexão\n      </ion-card-title>\n    </ion-card-header>\n    <ion-card-content>\n      <ion-item lines=\"none\">\n        <ion-icon slot=\"start\" [name]=\"isOnline ? 'cloud-done-outline' : 'cloud-offline-outline'\" \n                  [color]=\"isOnline ? 'success' : 'danger'\"></ion-icon>\n        <ion-label>\n          <h2>{{ isOnline ? 'Conectado' : 'Desconectado' }}</h2>\n          <p>{{ isOnline ? 'Você está conectado à internet.' : 'Você está offline. Conecte-se para sincronizar seus dados.' }}</p>\n        </ion-label>\n      </ion-item>\n    </ion-card-content>\n  </ion-card>\n\n  <!-- Componente de Status de Sincronização -->\n  <app-sync-status></app-sync-status>\n\n  <!-- Informações de Sincronização -->\n  <ion-card>\n    <ion-card-header>\n      <ion-card-title>\n        <ion-icon name=\"information-circle-outline\"></ion-icon>\n        Informações de Sincronização\n      </ion-card-title>\n    </ion-card-header>\n    <ion-card-content>\n      <ion-list>\n        <ion-item>\n          <ion-icon slot=\"start\" name=\"time-outline\" color=\"primary\"></ion-icon>\n          <ion-label>\n            <h2>Última sincronização</h2>\n            <p>{{ getLastSyncTimeFormatted() }}</p>\n          </ion-label>\n        </ion-item>\n        \n        <ion-item>\n          <ion-icon slot=\"start\" [name]=\"pendingSyncCount > 0 ? 'alert-circle-outline' : 'checkmark-circle-outline'\" \n                    [color]=\"pendingSyncCount > 0 ? 'warning' : 'success'\"></ion-icon>\n          <ion-label>\n            <h2>Itens pendentes</h2>\n            <p *ngIf=\"pendingSyncCount > 0\">{{ pendingSyncCount }} item(s) aguardando sincronização</p>\n            <p *ngIf=\"pendingSyncCount === 0\">Todos os dados estão sincronizados</p>\n          </ion-label>\n        </ion-item>\n      </ion-list>\n    </ion-card-content>\n  </ion-card>\n\n  <!-- Ações de Sincronização -->\n  <ion-card>\n    <ion-card-header>\n      <ion-card-title>\n        <ion-icon name=\"options-outline\"></ion-icon>\n        Ações\n      </ion-card-title>\n    </ion-card-header>\n    <ion-card-content>\n      <ion-button expand=\"block\" \n                  [disabled]=\"!isOnline || syncInProgress\" \n                  (click)=\"syncAll()\"\n                  [color]=\"pendingSyncCount > 0 ? 'warning' : 'primary'\">\n        <ion-icon [name]=\"syncInProgress ? 'sync-circle' : 'sync'\" slot=\"start\"></ion-icon>\n        <ion-spinner *ngIf=\"syncInProgress\" name=\"crescent\" slot=\"start\"></ion-spinner>\n        {{ syncInProgress ? 'Sincronizando...' : 'Sincronizar Agora' }}\n      </ion-button>\n      \n      <ion-button expand=\"block\" fill=\"outline\" routerLink=\"/tabs/home\" class=\"ion-margin-top\">\n        <ion-icon name=\"home-outline\" slot=\"start\"></ion-icon>\n        Voltar para Home\n      </ion-button>\n    </ion-card-content>\n  </ion-card>\n</ion-content>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAgBa;AAhBb;;;;AACA;AAMA;;;;;;;AASM,IAAO,eAAP,MAAO,aAAW;MAmBtB,YACU,gBACA,gBACA,gBACA,gBACA,cAA0B;AAJ1B,aAAA,iBAAA;AACA,aAAA,iBAAA;AACA,aAAA,iBAAA;AACA,aAAA,iBAAA;AACA,aAAA,eAAA;AAtBF,aAAA,wBAAwB,IAAI,gBAAyB,KAAK;AAG3D,aAAA,kBAAuC,KAAK,sBAAsB,aAAY;AAG7E,aAAA,sBAAsB,IAAI,gBAA6B,IAAI;AAG5D,aAAA,gBAAyC,KAAK,oBAAoB,aAAY;AAG7E,aAAA,0BAA0B,IAAI,gBAAwB,CAAC;AAGxD,aAAA,oBAAwC,KAAK,wBAAwB,aAAY;AAUtF,cAAM,eAAe,aAAa,QAAQ,cAAc;AACxD,YAAI,cAAc;AAChB,eAAK,oBAAoB,KAAK,IAAI,KAAK,YAAY,CAAC;QACtD;AAGA,aAAK,sBAAqB;MAC5B;;;;MAKM,wBAAqB;;AACzB,cAAI;AACF,kBAAM,SAAS,MAAM,KAAK,eAAe,UAAS;AAClD,gBAAI,CAAC;AAAQ;AAGb,kBAAM,WAAW,MAAM,KAAK,eAAe,aAAa,MAAM;AAG9D,kBAAM,WAAW,MAAM,KAAK,eAAe,qBAAqB,MAAM;AAGtE,kBAAM,kBAAkB,SAAS,OAAO,OACtC,EAAE,eAAe,WAAW,iBAC5B,EAAE,eAAe,WAAW,iBAC5B,EAAE,eAAe,WAAW,aAAa;AAG3C,kBAAM,kBAAkB,SAAS,OAAO,OACtC,EAAE,eAAe,WAAW,iBAC5B,EAAE,eAAe,WAAW,iBAC5B,EAAE,eAAe,WAAW,aAAa;AAI3C,kBAAM,eAAe,gBAAgB,SAAS,gBAAgB;AAC9D,iBAAK,wBAAwB,KAAK,YAAY;UAEhD,SAAS,OAAO;AACd,oBAAQ,MAAM,sCAAsC,KAAK;UAC3D;QACF;;;;;MAKM,UAAO;;AAEX,cAAI,CAAC,KAAK,eAAe,YAAW,GAAI;AACtC,iBAAK,aAAa,UAAU,uEAA2D,SAAS;AAChG,mBAAO;UACT;AAGA,cAAI,KAAK,sBAAsB,SAAQ,GAAI;AACzC,iBAAK,aAAa,UAAU,2CAAkC,MAAM;AACpE,mBAAO;UACT;AAGA,eAAK,sBAAsB,KAAK,IAAI;AAEpC,cAAI;AACF,kBAAM,SAAS,MAAM,KAAK,eAAe,UAAS;AAClD,gBAAI,CAAC,QAAQ;AACX,mBAAK,sBAAsB,KAAK,KAAK;AACrC,qBAAO;YACT;AAGA,kBAAM,iBAAiB,MAAM,KAAK,aAAa,MAAM;AAGrD,kBAAM,iBAAiB,MAAM,KAAK,aAAa,MAAM;AAGrD,kBAAM,MAAM,oBAAI,KAAI;AACpB,iBAAK,oBAAoB,KAAK,GAAG;AACjC,yBAAa,QAAQ,gBAAgB,IAAI,YAAW,CAAE;AAGtD,kBAAM,KAAK,sBAAqB;AAGhC,iBAAK,aAAa,UAAU,iDAAwC,SAAS;AAE7E,mBAAO,kBAAkB;UAC3B,SAAS,OAAO;AACd,oBAAQ,MAAM,iCAAiC,KAAK;AACpD,iBAAK,aAAa,UAAU,uCAAiC,QAAQ;AACrE,mBAAO;UACT;AAEE,iBAAK,sBAAsB,KAAK,KAAK;UACvC;QACF;;;;;MAKc,aAAa,QAAc;;AACvC,cAAI;AAEF,kBAAM,WAAW,MAAM,KAAK,eAAe,aAAa,MAAM;AAG9D,kBAAM,kBAAkB,SAAS,OAAO,OACtC,EAAE,eAAe,WAAW,iBAC5B,EAAE,eAAe,WAAW,iBAC5B,EAAE,eAAe,WAAW,aAAa;AAI3C,uBAAW,WAAW,iBAAiB;AACrC,oBAAM,iBAAiB,iCAClB,UADkB;gBAErB,YAAY,WAAW;gBACvB,cAAc,oBAAI,KAAI;;AAIxB,oBAAM,KAAK,eAAe,kBAAkB;gBAC1C,WAAW,eAAe;gBAC1B,QAAQ,eAAe;gBACvB,OAAO,eAAe;gBACtB,MAAM,eAAe;gBACrB,SAAS,eAAe;gBACxB,SAAS,eAAe;gBACxB,SAAS,eAAe;gBACxB,cAAc,eAAe;eAC9B;AACD,oBAAM,KAAK,eAAe,kBAAkB,cAAc;YAC5D;AAEA,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,2BAA2B,KAAK;AAC9C,mBAAO;UACT;QACF;;;;;MAKc,aAAa,QAAc;;AACvC,cAAI;AAEF,kBAAM,WAAW,MAAM,KAAK,eAAe,qBAAqB,MAAM;AAGtE,kBAAM,kBAAkB,SAAS,OAAO,OACtC,EAAE,eAAe,WAAW,iBAC5B,EAAE,eAAe,WAAW,iBAC5B,EAAE,eAAe,WAAW,aAAa;AAI3C,uBAAW,WAAW,iBAAiB;AACrC,oBAAM,iBAAiB,iCAClB,UADkB;gBAErB,YAAY,WAAW;gBACvB,cAAc,oBAAI,KAAI;;AAIxB,oBAAM,KAAK,eAAe,kBAAkB;gBAC1C,IAAI,eAAe;gBACnB,WAAW,eAAe;gBAC1B,SAAS,eAAe;gBACxB,UAAU,eAAe;gBACzB,QAAQ,eAAe;gBACvB,WAAW,eAAe;gBAC1B,cAAc,eAAe;eAC9B;AAGD,oBAAM,KAAK,eAAe,kBAAkB,cAAc;YAC5D;AAEA,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,2BAA2B,KAAK;AAC9C,mBAAO;UACT;QACF;;;;uCArNW,cAAW,mBAAA,cAAA,GAAA,mBAAA,cAAA,GAAA,mBAAA,qBAAA,GAAA,mBAAA,cAAA,GAAA,mBAAA,YAAA,CAAA;IAAA;oFAAX,cAAW,SAAX,aAAW,WAAA,YAFV,OAAM,CAAA;AAEd,IAAO,cAAP;;0EAAO,aAAW,CAAA;cAHvB;eAAW;UACV,YAAY;SACb;;;;;;;;;AEDK,IAAA,oBAAA,GAAA,eAAA,CAAA;;;;;AAGF,IAAA,oBAAA,GAAA,oBAAA,CAAA;;;;AAA8D,IAAA,qBAAA,SAAA,OAAA,mBAAA,CAAA;;;ADjBlE,IAca;AAdb;;;;AACA;AACA;;;;;;AAYM,IAAO,uBAAP,MAAO,qBAAmB;MAS9B,YACU,aACA,gBAA8B;AAD9B,aAAA,cAAA;AACA,aAAA,iBAAA;AAVV,aAAA,WAAW;AACX,aAAA,iBAAiB;AACjB,aAAA,mBAAmB;AACnB,aAAA,eAA4B;AAGpB,aAAA,gBAAgC,CAAA;MAKrC;MAEH,WAAQ;AAEN,aAAK,cAAc,KACjB,KAAK,eAAe,gBAAe,EAAG,UAAU,cAAW;AACzD,eAAK,WAAW;QAClB,CAAC,CAAC;AAIJ,aAAK,cAAc,KACjB,KAAK,YAAY,gBAAgB,UAAU,gBAAa;AACtD,eAAK,iBAAiB;QACxB,CAAC,CAAC;AAIJ,aAAK,cAAc,KACjB,KAAK,YAAY,kBAAkB,UAAU,WAAQ;AACnD,eAAK,mBAAmB;QAC1B,CAAC,CAAC;AAIJ,aAAK,cAAc,KACjB,KAAK,YAAY,cAAc,UAAU,UAAO;AAC9C,eAAK,eAAe;QACtB,CAAC,CAAC;MAEN;MAEA,cAAW;AAET,aAAK,cAAc,QAAQ,SAAO,IAAI,YAAW,CAAE;MACrD;;;;MAKA,2BAAwB;AACtB,YAAI,CAAC,KAAK,cAAc;AACtB,iBAAO;QACT;AAEA,eAAO,KAAK,aAAa,eAAe,SAAS;UAC/C,KAAK;UACL,OAAO;UACP,MAAM;UACN,MAAM;UACN,QAAQ;SACT;MACH;;;;MAKA,oBAAiB;AACf,YAAI,KAAK,gBAAgB;AACvB,iBAAO;QACT;AAEA,YAAI,KAAK,mBAAmB,GAAG;AAC7B,iBAAO;QACT;AAEA,eAAO;MACT;;;;MAKA,qBAAkB;AAChB,YAAI,KAAK,gBAAgB;AACvB,iBAAO;QACT;AAEA,YAAI,KAAK,mBAAmB,GAAG;AAC7B,iBAAO;QACT;AAEA,eAAO;MACT;;;;MAKA,uBAAoB;AAClB,YAAI,KAAK,gBAAgB;AACvB,iBAAO;QACT;AAEA,YAAI,KAAK,mBAAmB,GAAG;AAC7B,iBAAO,GAAG,KAAK,gBAAgB;QACjC;AAEA,eAAO;MACT;;;uCA7GW,sBAAmB,4BAAA,WAAA,GAAA,4BAAA,cAAA,CAAA;IAAA;yFAAnB,sBAAmB,WAAA,CAAA,CAAA,iBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,QAAA,OAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,QAAA,SAAA,GAAA,QAAA,OAAA,GAAA,CAAA,QAAA,YAAA,QAAA,OAAA,GAAA,MAAA,GAAA,CAAA,QAAA,iBAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,QAAA,SAAA,QAAA,gBAAA,SAAA,QAAA,GAAA,CAAA,QAAA,YAAA,QAAA,KAAA,GAAA,CAAA,QAAA,iBAAA,GAAA,OAAA,CAAA,GAAA,UAAA,SAAA,6BAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;ACdhC,QAAA,yBAAA,GAAA,UAAA,EAAU,GAAA,iBAAA,EACS,GAAA,gBAAA;AAEb,QAAA,oBAAA,GAAA,YAAA,CAAA;AACA,QAAA,iBAAA,GAAA,iCAAA;AACF,QAAA,uBAAA,EAAiB;AAEnB,QAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,YAAA,CAAA;AAEd,QAAA,oBAAA,GAAA,YAAA,CAAA;AACA,QAAA,yBAAA,GAAA,WAAA,EAAW,GAAA,IAAA;AACL,QAAA,iBAAA,EAAA;AAAgG,QAAA,uBAAA;AACpG,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,EAAA;AAA4B,QAAA,uBAAA,EAAI;AAErC,QAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,eAAA,CAAA;AACF,QAAA,uBAAA;AAEA,QAAA,qBAAA,IAAA,kDAAA,GAAA,GAAA,oBAAA,CAAA;AAEA,QAAA,yBAAA,IAAA,YAAA,CAAA;AACE,QAAA,oBAAA,IAAA,YAAA,CAAA;AACA,QAAA,yBAAA,IAAA,WAAA,EAAW,IAAA,IAAA;AACL,QAAA,iBAAA,IAAA,+BAAA;AAAoB,QAAA,uBAAA;AACxB,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,EAAA;AAAgC,QAAA,uBAAA,EAAI,EAC7B,EACH,EACM;;;AAvBL,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,kBAAA,CAAA,EAA4B,SAAA,IAAA,mBAAA,CAAA;AAMf,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,kBAAA,CAAA,EAA4B,SAAA,IAAA,mBAAA,CAAA;AAE7C,QAAA,oBAAA,CAAA;AAAA,QAAA,4BAAA,IAAA,iBAAA,qBAAA,IAAA,mBAAA,IAAA,aAAA,cAAA;AACD,QAAA,oBAAA,CAAA;AAAA,QAAA,4BAAA,IAAA,qBAAA,CAAA;AAES,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,cAAA;AAGG,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,cAAA;AAMZ,QAAA,oBAAA,CAAA;AAAA,QAAA,4BAAA,IAAA,yBAAA,CAAA;;sBDXC,aAAW,SAAA,gBAAA,eAAA,cAAA,SAAA,SAAA,UAAA,gBAAA,YAAE,cAAY,IAAA,GAAA,QAAA,CAAA,0sCAAA,EAAA,CAAA;AAE/B,IAAO,sBAAP;;0EAAO,qBAAmB,CAAA;cAP/B;2BACW,mBAAiB,YAGf,MAAI,SACP,CAAC,aAAa,YAAY,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,8vBAAA,EAAA,CAAA;;;;iFAEzB,qBAAmB,EAAA,WAAA,uBAAA,UAAA,kEAAA,YAAA,GAAA,CAAA;IAAA,GAAA;;;;;;;AG+CpB,IAAA,yBAAA,GAAA,GAAA;AAAgC,IAAA,iBAAA,CAAA;AAAuD,IAAA,uBAAA;;;;AAAvD,IAAA,oBAAA;AAAA,IAAA,6BAAA,IAAA,OAAA,kBAAA,yCAAA;;;;;AAChC,IAAA,yBAAA,GAAA,GAAA;AAAkC,IAAA,iBAAA,GAAA,uCAAA;AAAkC,IAAA,uBAAA;;;;;AAqBxE,IAAA,oBAAA,GAAA,eAAA,EAAA;;;ADnFR,IAkBa;AAlBb;;;AACA;AACA;AAKA;AAEA;;;;;;;;;AASM,IAAO,YAAP,MAAO,UAAQ;MAUnB,YACU,aACA,gBACA,cACA,gBAA8B;AAH9B,aAAA,cAAA;AACA,aAAA,iBAAA;AACA,aAAA,eAAA;AACA,aAAA,iBAAA;AAbV,aAAA,WAAW;AACX,aAAA,iBAAiB;AACjB,aAAA,mBAAmB;AACnB,aAAA,eAA4B;AAC5B,aAAA,WAAmB;AAGX,aAAA,gBAAgC,CAAA;MAOrC;MAEG,WAAQ;;AAEZ,eAAK,YAAW,MAAM,KAAK,eAAe,YAAW,MAAM;AAG3D,eAAK,cAAc,KACjB,KAAK,eAAe,gBAAe,EAAG,UAAU,cAAW;AACzD,iBAAK,WAAW;UAClB,CAAC,CAAC;AAIJ,eAAK,cAAc,KACjB,KAAK,YAAY,gBAAgB,UAAU,gBAAa;AACtD,iBAAK,iBAAiB;UACxB,CAAC,CAAC;AAIJ,eAAK,cAAc,KACjB,KAAK,YAAY,kBAAkB,UAAU,WAAQ;AACnD,iBAAK,mBAAmB;UAC1B,CAAC,CAAC;AAIJ,eAAK,cAAc,KACjB,KAAK,YAAY,cAAc,UAAU,UAAO;AAC9C,iBAAK,eAAe;UACtB,CAAC,CAAC;QAEN;;MAEA,cAAW;AAET,aAAK,cAAc,QAAQ,SAAO,IAAI,YAAW,CAAE;MACrD;;;;MAKM,UAAO;;AACX,cAAI,CAAC,KAAK,UAAU;AAClB,iBAAK,aAAa,UAAU,uEAA2D,SAAS;AAChG;UACF;AAEA,cAAI,KAAK,gBAAgB;AACvB,iBAAK,aAAa,UAAU,2CAAkC,MAAM;AACpE;UACF;AAEA,gBAAM,KAAK,YAAY,QAAO;QAChC;;;;;MAKA,2BAAwB;AACtB,YAAI,CAAC,KAAK,cAAc;AACtB,iBAAO;QACT;AAEA,eAAO,KAAK,aAAa,eAAe,SAAS;UAC/C,KAAK;UACL,OAAO;UACP,MAAM;UACN,MAAM;UACN,QAAQ;SACT;MACH;;;uCAvFW,WAAQ,4BAAA,WAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,YAAA,GAAA,4BAAA,cAAA,CAAA;IAAA;8EAAR,WAAQ,WAAA,CAAA,CAAA,UAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,QAAA,OAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,QAAA,SAAA,GAAA,QAAA,OAAA,GAAA,CAAA,QAAA,4BAAA,GAAA,CAAA,QAAA,SAAA,QAAA,gBAAA,SAAA,SAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,QAAA,iBAAA,GAAA,CAAA,UAAA,SAAA,GAAA,SAAA,YAAA,OAAA,GAAA,CAAA,QAAA,SAAA,GAAA,MAAA,GAAA,CAAA,QAAA,YAAA,QAAA,SAAA,GAAA,MAAA,GAAA,CAAA,UAAA,SAAA,QAAA,WAAA,cAAA,cAAA,GAAA,gBAAA,GAAA,CAAA,QAAA,gBAAA,QAAA,OAAA,GAAA,CAAA,QAAA,YAAA,QAAA,OAAA,CAAA,GAAA,UAAA,SAAA,kBAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;AClBrB,QAAA,yBAAA,GAAA,YAAA,EAAY,GAAA,aAAA,EACG,GAAA,WAAA;AACA,QAAA,iBAAA,GAAA,qBAAA;AAAa,QAAA,uBAAA,EAAY,EACxB;AAGhB,QAAA,yBAAA,GAAA,eAAA,CAAA,EAAiC,GAAA,UAAA,EACrB,GAAA,iBAAA,EACS,GAAA,kBAAA,CAAA;AACyB,QAAA,iBAAA,CAAA;AAAwB,QAAA,uBAAA;AAChE,QAAA,yBAAA,GAAA,qBAAA,CAAA;AAA2C,QAAA,iBAAA,IAAA,sCAAA;AAA8B,QAAA,uBAAA,EAAoB,EAC7E;AAIpB,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,iBAAA,EACS,IAAA,gBAAA;AAEb,QAAA,oBAAA,IAAA,YAAA,CAAA;AAEA,QAAA,iBAAA,IAAA,wBAAA;AACF,QAAA,uBAAA,EAAiB;AAEnB,QAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,YAAA,CAAA;AAEd,QAAA,oBAAA,IAAA,YAAA,CAAA;AAEA,QAAA,yBAAA,IAAA,WAAA,EAAW,IAAA,IAAA;AACL,QAAA,iBAAA,EAAA;AAA6C,QAAA,uBAAA;AACjD,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,EAAA;AAAiH,QAAA,uBAAA,EAAI,EAC9G,EACH,EACM;AAIrB,QAAA,oBAAA,IAAA,iBAAA;AAGA,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,iBAAA,EACS,IAAA,gBAAA;AAEb,QAAA,oBAAA,IAAA,YAAA,CAAA;AACA,QAAA,iBAAA,IAAA,4CAAA;AACF,QAAA,uBAAA,EAAiB;AAEnB,QAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,UAAA,EACN,IAAA,UAAA;AAEN,QAAA,oBAAA,IAAA,YAAA,CAAA;AACA,QAAA,yBAAA,IAAA,WAAA,EAAW,IAAA,IAAA;AACL,QAAA,iBAAA,IAAA,+BAAA;AAAoB,QAAA,uBAAA;AACxB,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,EAAA;AAAgC,QAAA,uBAAA,EAAI,EAC7B;AAGd,QAAA,yBAAA,IAAA,UAAA;AACE,QAAA,oBAAA,IAAA,YAAA,CAAA;AAEA,QAAA,yBAAA,IAAA,WAAA,EAAW,IAAA,IAAA;AACL,QAAA,iBAAA,IAAA,iBAAA;AAAe,QAAA,uBAAA;AACnB,QAAA,qBAAA,IAAA,wBAAA,GAAA,GAAA,KAAA,CAAA,EAAgC,IAAA,wBAAA,GAAA,GAAA,KAAA,CAAA;AAElC,QAAA,uBAAA,EAAY,EACH,EACF,EACM;AAIrB,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,iBAAA,EACS,IAAA,gBAAA;AAEb,QAAA,oBAAA,IAAA,YAAA,CAAA;AACA,QAAA,iBAAA,IAAA,eAAA;AACF,QAAA,uBAAA,EAAiB;AAEnB,QAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,cAAA,CAAA;AAGJ,QAAA,qBAAA,SAAA,SAAA,iDAAA;AAAA,iBAAS,IAAA,QAAA;QAAS,CAAA;AAE5B,QAAA,oBAAA,IAAA,YAAA,EAAA;AACA,QAAA,qBAAA,IAAA,kCAAA,GAAA,GAAA,eAAA,EAAA;AACA,QAAA,iBAAA,EAAA;AACF,QAAA,uBAAA;AAEA,QAAA,yBAAA,IAAA,cAAA,EAAA;AACE,QAAA,oBAAA,IAAA,YAAA,EAAA;AACA,QAAA,iBAAA,IAAA,oBAAA;AACF,QAAA,uBAAA,EAAa,EACI,EACV;;;AAnFiC,QAAA,oBAAA,CAAA;AAAA,QAAA,6BAAA,eAAA,IAAA,UAAA,GAAA;AAS5B,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,WAAA,iBAAA,kBAAA,EAAuD,SAAA,IAAA,WAAA,YAAA,QAAA;AAO1C,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,WAAA,uBAAA,uBAAA,EAAkE,SAAA,IAAA,WAAA,YAAA,QAAA;AAGnF,QAAA,oBAAA,CAAA;AAAA,QAAA,4BAAA,IAAA,WAAA,cAAA,cAAA;AACD,QAAA,oBAAA,CAAA;AAAA,QAAA,4BAAA,IAAA,WAAA,6CAAA,kEAAA;AAuBE,QAAA,oBAAA,EAAA;AAAA,QAAA,4BAAA,IAAA,yBAAA,CAAA;AAKkB,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,mBAAA,IAAA,yBAAA,0BAAA,EAAmF,SAAA,IAAA,mBAAA,IAAA,YAAA,SAAA;AAIpG,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,mBAAA,CAAA;AACA,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,qBAAA,CAAA;AAiBE,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,YAAA,CAAA,IAAA,YAAA,IAAA,cAAA,EAAwC,SAAA,IAAA,mBAAA,IAAA,YAAA,SAAA;AAGxC,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,iBAAA,gBAAA,MAAA;AACI,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,cAAA;AACd,QAAA,oBAAA;AAAA,QAAA,6BAAA,KAAA,IAAA,iBAAA,qBAAA,qBAAA,GAAA;;sBDpEI,aAAW,WAAA,SAAA,gBAAA,eAAA,iBAAA,cAAA,YAAA,WAAA,SAAA,SAAA,UAAA,SAAA,YAAA,UAAA,YAAA,6BAAE,cAAY,MAAE,qBAAqB,cAAY,UAAA,GAAA,QAAA,CAAA,+0DAAA,EAAA,CAAA;AAElE,IAAO,WAAP;;0EAAO,UAAQ,CAAA;cAPpB;2BACW,YAAU,YAGR,MAAI,SACP,CAAC,aAAa,cAAc,qBAAqB,YAAY,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,82CAAA,EAAA,CAAA;;;;iFAE5D,UAAQ,EAAA,WAAA,YAAA,UAAA,mCAAA,YAAA,GAAA,CAAA;IAAA,GAAA;;;", "names": []}