import { Injectable } from '@angular/core';
import { Drivers } from '@ionic/storage';
import { Storage } from '@ionic/storage-angular';

@Injectable({
  providedIn: 'root'
})
export class SessionService {
  private _storage: Storage | null = null;

  constructor(private storage: Storage) {
    this.init();
  }

  async init() : Promise<void>{
    if (!this._storage) {        
        await this.storage.create();
        this._storage = this.storage;
      }
  }

  // Salvar o token e dados do usuário Good Driver
  async setSession(token: string, userId: string, userName: string, userEmail: string) {
    await this._storage?.set('jwt', token);
    await this._storage?.set('userId', userId);
    await this._storage?.set('userName', userName);
    await this._storage?.set('userEmail', userEmail);
  }

  // Pegar o token
  async getToken() {
    if (!this._storage) {
      console.warn('Storage was not initialized. Creating now...');
      this._storage = await this.storage.create();
    }
    return await this._storage?.get('jwt');
  }


  // Pegar o Id do usuário
  async getUserId() {
    if (!this._storage) {
      console.warn('Storage was not initialized. Creating now...');
      this._storage = await this.storage.create();
    }
    
      const value = await this._storage?.get('userId') ?? null;
      console.log('Fetched userId:', value);
      return value;
  }

  // Pegar o Nome do usuário
  async getUserName() {
    if (!this._storage) {
      console.warn('Storage was not initialized. Creating now...');
      this._storage = await this.storage.create();
    }
    const value = await this._storage?.get('userName');
    console.log('Fetched userName:', value);
    return value;
  }

    // Pegar o email do usuário
    async getUserEmail() {
      if (!this._storage) {
        console.warn('Storage was not initialized. Creating now...');
        this._storage = await this.storage.create();
      }
        return await this._storage?.get('userEmail');
      }

  // Remover sessão
  async clearSession() {
    if (!this._storage) {
      console.warn('Storage was not initialized. Creating now...');
      this._storage = await this.storage.create();
    }
    await this._storage?.remove('jwt');
    await this._storage?.remove('userId');
    await this._storage?.remove('userName');
    await this._storage?.remove('userEmail');
  }
}
