{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-segment-view.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, h, e as Host, f as getElement } from './index-527b9e34.js';\nconst segmentViewIosCss = \":host{display:-ms-flexbox;display:flex;height:100%;overflow-x:scroll;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;scrollbar-width:none;-ms-overflow-style:none}:host::-webkit-scrollbar{display:none}:host(.segment-view-disabled){-ms-touch-action:none;touch-action:none;overflow-x:hidden}:host(.segment-view-scroll-disabled){pointer-events:none}:host(.segment-view-disabled){opacity:0.3}\";\nconst IonSegmentViewIosStyle0 = segmentViewIosCss;\nconst segmentViewMdCss = \":host{display:-ms-flexbox;display:flex;height:100%;overflow-x:scroll;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;scrollbar-width:none;-ms-overflow-style:none}:host::-webkit-scrollbar{display:none}:host(.segment-view-disabled){-ms-touch-action:none;touch-action:none;overflow-x:hidden}:host(.segment-view-scroll-disabled){pointer-events:none}:host(.segment-view-disabled){opacity:0.3}\";\nconst IonSegmentViewMdStyle0 = segmentViewMdCss;\nconst SegmentView = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionSegmentViewScroll = createEvent(this, \"ionSegmentViewScroll\", 7);\n    this.scrollEndTimeout = null;\n    this.isTouching = false;\n    this.disabled = false;\n    this.isManualScroll = undefined;\n  }\n  handleScroll(ev) {\n    var _a;\n    const {\n      scrollLeft,\n      scrollWidth,\n      clientWidth\n    } = ev.target;\n    const scrollRatio = scrollLeft / (scrollWidth - clientWidth);\n    this.ionSegmentViewScroll.emit({\n      scrollRatio,\n      isManualScroll: (_a = this.isManualScroll) !== null && _a !== void 0 ? _a : true\n    });\n    // Reset the timeout to check for scroll end\n    this.resetScrollEndTimeout();\n  }\n  /**\n   * Handle touch start event to know when the user is actively dragging the segment view.\n   */\n  handleScrollStart() {\n    if (this.scrollEndTimeout) {\n      clearTimeout(this.scrollEndTimeout);\n      this.scrollEndTimeout = null;\n    }\n    this.isTouching = true;\n  }\n  /**\n   * Handle touch end event to know when the user is no longer dragging the segment view.\n   */\n  handleTouchEnd() {\n    this.isTouching = false;\n  }\n  /**\n   * Reset the scroll end detection timer. This is called on every scroll event.\n   */\n  resetScrollEndTimeout() {\n    if (this.scrollEndTimeout) {\n      clearTimeout(this.scrollEndTimeout);\n      this.scrollEndTimeout = null;\n    }\n    this.scrollEndTimeout = setTimeout(() => {\n      this.checkForScrollEnd();\n    },\n    // Setting this to a lower value may result in inconsistencies in behavior\n    // across browsers (particularly Firefox).\n    // Ideally, all of this logic is removed once the scroll end event is\n    // supported on all browsers (https://caniuse.com/?search=scrollend)\n    100);\n  }\n  /**\n   * Check if the scroll has ended and the user is not actively touching.\n   * If the conditions are met (active content is enabled and no active touch),\n   * reset the scroll position and emit the scroll end event.\n   */\n  checkForScrollEnd() {\n    // Only emit scroll end event if the active content is not disabled and\n    // the user is not touching the segment view\n    if (!this.isTouching) {\n      this.isManualScroll = undefined;\n    }\n  }\n  /**\n   * @internal\n   *\n   * This method is used to programmatically set the displayed segment content\n   * in the segment view. Calling this method will update the `value` of the\n   * corresponding segment button.\n   *\n   * @param id: The id of the segment content to display.\n   * @param smoothScroll: Whether to animate the scroll transition.\n   */\n  async setContent(id, smoothScroll = true) {\n    const contents = this.getSegmentContents();\n    const index = contents.findIndex(content => content.id === id);\n    if (index === -1) return;\n    this.isManualScroll = false;\n    this.resetScrollEndTimeout();\n    const contentWidth = this.el.offsetWidth;\n    this.el.scrollTo({\n      top: 0,\n      left: index * contentWidth,\n      behavior: smoothScroll ? 'smooth' : 'instant'\n    });\n  }\n  getSegmentContents() {\n    return Array.from(this.el.querySelectorAll('ion-segment-content'));\n  }\n  render() {\n    const {\n      disabled,\n      isManualScroll\n    } = this;\n    return h(Host, {\n      key: 'fa528d2d9ae0f00fc3067defe2a047dce77c814a',\n      class: {\n        'segment-view-disabled': disabled,\n        'segment-view-scroll-disabled': isManualScroll === false\n      }\n    }, h(\"slot\", {\n      key: '74dc8b4d073caeff1bab272d11b9ea3e1a215954'\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nSegmentView.style = {\n  ios: IonSegmentViewIosStyle0,\n  md: IonSegmentViewMdStyle0\n};\nexport { SegmentView as ion_segment_view };"], "mappings": ";;;;;;;;;;;;;;AAAA,IAIM,mBACA,yBACA,kBACA,wBACA;AARN;AAAA;AAGA;AACA,IAAM,oBAAoB;AAC1B,IAAM,0BAA0B;AAChC,IAAM,mBAAmB;AACzB,IAAM,yBAAyB;AAC/B,IAAM,cAAc,MAAM;AAAA,MACxB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,uBAAuB,YAAY,MAAM,wBAAwB,CAAC;AACvE,aAAK,mBAAmB;AACxB,aAAK,aAAa;AAClB,aAAK,WAAW;AAChB,aAAK,iBAAiB;AAAA,MACxB;AAAA,MACA,aAAa,IAAI;AACf,YAAI;AACJ,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI,GAAG;AACP,cAAM,cAAc,cAAc,cAAc;AAChD,aAAK,qBAAqB,KAAK;AAAA,UAC7B;AAAA,UACA,iBAAiB,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,KAAK;AAAA,QAC9E,CAAC;AAED,aAAK,sBAAsB;AAAA,MAC7B;AAAA;AAAA;AAAA;AAAA,MAIA,oBAAoB;AAClB,YAAI,KAAK,kBAAkB;AACzB,uBAAa,KAAK,gBAAgB;AAClC,eAAK,mBAAmB;AAAA,QAC1B;AACA,aAAK,aAAa;AAAA,MACpB;AAAA;AAAA;AAAA;AAAA,MAIA,iBAAiB;AACf,aAAK,aAAa;AAAA,MACpB;AAAA;AAAA;AAAA;AAAA,MAIA,wBAAwB;AACtB,YAAI,KAAK,kBAAkB;AACzB,uBAAa,KAAK,gBAAgB;AAClC,eAAK,mBAAmB;AAAA,QAC1B;AACA,aAAK,mBAAmB;AAAA,UAAW,MAAM;AACvC,iBAAK,kBAAkB;AAAA,UACzB;AAAA;AAAA;AAAA;AAAA;AAAA,UAKA;AAAA,QAAG;AAAA,MACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,oBAAoB;AAGlB,YAAI,CAAC,KAAK,YAAY;AACpB,eAAK,iBAAiB;AAAA,QACxB;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWM,WAAW,IAAI,eAAe,MAAM;AAAA;AACxC,gBAAM,WAAW,KAAK,mBAAmB;AACzC,gBAAM,QAAQ,SAAS,UAAU,aAAW,QAAQ,OAAO,EAAE;AAC7D,cAAI,UAAU,GAAI;AAClB,eAAK,iBAAiB;AACtB,eAAK,sBAAsB;AAC3B,gBAAM,eAAe,KAAK,GAAG;AAC7B,eAAK,GAAG,SAAS;AAAA,YACf,KAAK;AAAA,YACL,MAAM,QAAQ;AAAA,YACd,UAAU,eAAe,WAAW;AAAA,UACtC,CAAC;AAAA,QACH;AAAA;AAAA,MACA,qBAAqB;AACnB,eAAO,MAAM,KAAK,KAAK,GAAG,iBAAiB,qBAAqB,CAAC;AAAA,MACnE;AAAA,MACA,SAAS;AACP,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,OAAO;AAAA,YACL,yBAAyB;AAAA,YACzB,gCAAgC,mBAAmB;AAAA,UACrD;AAAA,QACF,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,QACP,CAAC,CAAC;AAAA,MACJ;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,IACF;AACA,gBAAY,QAAQ;AAAA,MAClB,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}