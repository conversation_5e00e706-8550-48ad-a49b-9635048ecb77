import {
  ActivatedRoute,
  CommonModule,
  Component,
  DatePipe,
  IonBackButton2 as IonBackButton,
  IonBadge,
  IonButton,
  IonButtons,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardSubtitle,
  IonCardTitle,
  IonContent,
  IonHeader,
  IonIcon,
  IonItem,
  IonLabel,
  IonList,
  IonSpinner,
  IonText,
  IonTitle,
  IonToolbar,
  IonicModule,
  NgIf,
  Router,
  SessionService,
  ToastService,
  VehicleService,
  VehicleStateService,
  init_common,
  init_core,
  init_ionic_angular,
  init_router,
  init_session_service,
  init_toast_service,
  init_vehicle_service,
  init_vehicle_state_service,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate2
} from "./chunk-GYTSHG5S.js";
import "./chunk-GJIVKQBF.js";
import "./chunk-WY354WYL.js";
import "./chunk-3TAP6XVY.js";
import "./chunk-BQEXAUCY.js";
import "./chunk-EUHNYIV3.js";
import "./chunk-AY6ZGCP5.js";
import "./chunk-I3UCYTIQ.js";
import "./chunk-4JI7VCBZ.js";
import "./chunk-YY7NCLAA.js";
import "./chunk-GUQ63TTB.js";
import "./chunk-CHFZ6R2P.js";
import "./chunk-XPKADKFH.js";
import "./chunk-V6QEVD67.js";
import "./chunk-WNPNB2PX.js";
import "./chunk-NRG74LSH.js";
import "./chunk-YSMEPS6E.js";
import "./chunk-NHEUXSZ2.js";
import "./chunk-WX4TOLMF.js";
import "./chunk-6SXGDLUH.js";
import "./chunk-NNIHVF7T.js";
import "./chunk-ZNALOMZJ.js";
import "./chunk-GHA2GP6C.js";
import "./chunk-H2CU6QHT.js";
import "./chunk-6PMAHZVS.js";
import "./chunk-DOEZTQY3.js";
import "./chunk-O4BCOVEV.js";
import "./chunk-TD4SIQNM.js";
import "./chunk-VU5YACRM.js";
import {
  __async,
  __esm
} from "./chunk-4W6HR7MY.js";

// src/app/pages/tabs/vehicles/vehicle-details/vehicle-details.page.ts
function VehicleDetailsPage_div_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 6);
    \u0275\u0275element(1, "ion-spinner");
    \u0275\u0275elementEnd();
  }
}
function VehicleDetailsPage_div_8_ion_badge_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-badge", 17);
    \u0275\u0275text(1, "Ve\xEDculo Principal");
    \u0275\u0275elementEnd();
  }
}
function VehicleDetailsPage_div_8_ion_item_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-item");
    \u0275\u0275element(1, "ion-icon", 18);
    \u0275\u0275elementStart(2, "ion-label")(3, "h3");
    \u0275\u0275text(4, "Vers\xE3o");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate(ctx_r1.vehicle.version);
  }
}
function VehicleDetailsPage_div_8_ion_item_19_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-item");
    \u0275\u0275element(1, "ion-icon", 19);
    \u0275\u0275elementStart(2, "ion-label")(3, "h3");
    \u0275\u0275text(4, "N\xFAmero da Ap\xF3lice");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate(ctx_r1.vehicle.policyNumber);
  }
}
function VehicleDetailsPage_div_8_ion_button_29_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "ion-button", 20);
    \u0275\u0275listener("click", function VehicleDetailsPage_div_8_ion_button_29_Template_ion_button_click_0_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.setPrimaryVehicle());
    });
    \u0275\u0275element(1, "ion-icon", 21);
    \u0275\u0275text(2, " Definir como Ve\xEDculo Principal ");
    \u0275\u0275elementEnd();
  }
}
function VehicleDetailsPage_div_8_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div")(1, "ion-card")(2, "ion-card-header")(3, "div", 7)(4, "ion-card-title");
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275template(6, VehicleDetailsPage_div_8_ion_badge_6_Template, 2, 0, "ion-badge", 8);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "ion-card-subtitle");
    \u0275\u0275text(8);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(9, "ion-card-content")(10, "ion-list", 9)(11, "ion-item");
    \u0275\u0275element(12, "ion-icon", 10);
    \u0275\u0275elementStart(13, "ion-label")(14, "h3");
    \u0275\u0275text(15, "Ano");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(16, "p");
    \u0275\u0275text(17);
    \u0275\u0275elementEnd()()();
    \u0275\u0275template(18, VehicleDetailsPage_div_8_ion_item_18_Template, 7, 1, "ion-item", 4)(19, VehicleDetailsPage_div_8_ion_item_19_Template, 7, 1, "ion-item", 4);
    \u0275\u0275elementStart(20, "ion-item");
    \u0275\u0275element(21, "ion-icon", 11);
    \u0275\u0275elementStart(22, "ion-label")(23, "h3");
    \u0275\u0275text(24, "Data de Cadastro");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(25, "p");
    \u0275\u0275text(26);
    \u0275\u0275pipe(27, "date");
    \u0275\u0275elementEnd()()()()()();
    \u0275\u0275elementStart(28, "div", 2);
    \u0275\u0275template(29, VehicleDetailsPage_div_8_ion_button_29_Template, 3, 0, "ion-button", 12);
    \u0275\u0275elementStart(30, "ion-button", 13);
    \u0275\u0275listener("click", function VehicleDetailsPage_div_8_Template_ion_button_click_30_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.goToEditVehicle(ctx_r1.vehicle.id));
    });
    \u0275\u0275element(31, "ion-icon", 14);
    \u0275\u0275text(32, " Editar Ve\xEDculo ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(33, "ion-button", 15);
    \u0275\u0275element(34, "ion-icon", 16);
    \u0275\u0275text(35, " Excluir Ve\xEDculo ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r1.vehicle.plate);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.vehicle.isPrimary);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate2("", ctx_r1.vehicle.brandName, " ", ctx_r1.vehicle.modelName, "");
    \u0275\u0275advance(9);
    \u0275\u0275textInterpolate(ctx_r1.vehicle.year);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.vehicle.version);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.vehicle.policyNumber);
    \u0275\u0275advance(7);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(27, 9, ctx_r1.vehicle.createdOn, "dd/MM/yyyy HH:mm"));
    \u0275\u0275advance(3);
    \u0275\u0275property("ngIf", !ctx_r1.vehicle.isPrimary);
  }
}
function VehicleDetailsPage_ion_text_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-text", 22)(1, "p", 23);
    \u0275\u0275text(2, "Ve\xEDculo n\xE3o encontrado.");
    \u0275\u0275elementEnd()();
  }
}
var _VehicleDetailsPage, VehicleDetailsPage;
var init_vehicle_details_page = __esm({
  "src/app/pages/tabs/vehicles/vehicle-details/vehicle-details.page.ts"() {
    init_core();
    init_ionic_angular();
    init_common();
    init_core();
    init_router();
    init_vehicle_service();
    init_toast_service();
    init_session_service();
    init_vehicle_state_service();
    init_ionic_angular();
    init_common();
    _VehicleDetailsPage = class _VehicleDetailsPage {
      constructor(route, router, vehicleService, toastService, sessionService, vehicleStateService) {
        this.route = route;
        this.router = router;
        this.vehicleService = vehicleService;
        this.toastService = toastService;
        this.sessionService = sessionService;
        this.vehicleStateService = vehicleStateService;
        this.vehicleId = "";
        this.vehicle = null;
        this.isLoading = true;
        this.subscriptions = [];
      }
      ngOnInit() {
        this.vehicleId = this.route.snapshot.paramMap.get("id") || "";
        this.loadVehicleDetails();
        this.subscriptions.push(this.vehicleStateService.primaryVehicle$.subscribe((primaryVehicle) => {
          if (primaryVehicle && this.vehicle && primaryVehicle.id === this.vehicle.id) {
            this.vehicle.isPrimary = primaryVehicle.isPrimary;
          }
        }));
      }
      ngOnDestroy() {
        this.subscriptions.forEach((sub) => sub.unsubscribe());
      }
      loadVehicleDetails() {
        return __async(this, null, function* () {
          if (!this.vehicleId) {
            this.toastService.showToast("ID do ve\xEDculo n\xE3o encontrado", "danger");
            this.router.navigate(["/tabs/vehicles"]);
            return;
          }
          this.isLoading = true;
          try {
            const userId = (yield this.sessionService.getUserId()) || "";
            const vehicles = yield this.vehicleService.listAllLocal(userId);
            this.vehicle = vehicles.find((v) => v.id === this.vehicleId) || null;
            if (!this.vehicle) {
              this.toastService.showToast("Ve\xEDculo n\xE3o encontrado", "danger");
              this.router.navigate(["/tabs/vehicles"]);
            }
          } catch (error) {
            console.error("Error loading vehicle details:", error);
            this.toastService.showToast("Erro ao carregar detalhes do ve\xEDculo", "danger");
          } finally {
            this.isLoading = false;
          }
        });
      }
      goBack() {
        this.router.navigate(["/tabs/vehicles"]);
      }
      /**
       * Sets this vehicle as the primary vehicle
       */
      setPrimaryVehicle() {
        return __async(this, null, function* () {
          if (!this.vehicle)
            return;
          const userId = (yield this.sessionService.getUserId()) || "";
          try {
            const success = yield this.vehicleService.setPrimaryVehicle(this.vehicle.id, userId);
            if (success) {
              this.toastService.showToast("Ve\xEDculo definido como principal");
              this.vehicle.isPrimary = true;
            } else {
              this.toastService.showToast("Erro ao definir ve\xEDculo como principal", "danger");
            }
          } catch (error) {
            console.error("Error setting primary vehicle:", error);
            this.toastService.showToast("Erro ao definir ve\xEDculo como principal", "danger");
          }
        });
      }
      goToEditVehicle(id) {
        this.router.navigate(["/tabs/vehicles/edit/", id]);
      }
    };
    _VehicleDetailsPage.\u0275fac = function VehicleDetailsPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _VehicleDetailsPage)(\u0275\u0275directiveInject(ActivatedRoute), \u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(VehicleService), \u0275\u0275directiveInject(ToastService), \u0275\u0275directiveInject(SessionService), \u0275\u0275directiveInject(VehicleStateService));
    };
    _VehicleDetailsPage.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _VehicleDetailsPage, selectors: [["app-vehicle-details"]], decls: 10, vars: 3, consts: [["slot", "start"], ["defaultHref", "/tabs/vehicles"], [1, "ion-padding"], ["class", "ion-text-center ion-padding", 4, "ngIf"], [4, "ngIf"], ["color", "danger", 4, "ngIf"], [1, "ion-text-center", "ion-padding"], [1, "vehicle-header"], ["color", "success", 4, "ngIf"], ["lines", "none"], ["name", "calendar-outline", "slot", "start", "color", "primary"], ["name", "time-outline", "slot", "start", "color", "primary"], ["expand", "block", "color", "success", 3, "click", 4, "ngIf"], ["expand", "block", "color", "primary", 3, "click"], ["name", "create-outline", "slot", "start"], ["expand", "block", "color", "danger", "disabled", ""], ["name", "trash-outline", "slot", "start"], ["color", "success"], ["name", "options-outline", "slot", "start", "color", "primary"], ["name", "shield-checkmark-outline", "slot", "start", "color", "primary"], ["expand", "block", "color", "success", 3, "click"], ["name", "star", "slot", "start"], ["color", "danger"], [1, "ion-text-center"]], template: function VehicleDetailsPage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header")(1, "ion-toolbar")(2, "ion-buttons", 0);
        \u0275\u0275element(3, "ion-back-button", 1);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(4, "ion-title");
        \u0275\u0275text(5, "Detalhes do Ve\xEDculo");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(6, "ion-content", 2);
        \u0275\u0275template(7, VehicleDetailsPage_div_7_Template, 2, 0, "div", 3)(8, VehicleDetailsPage_div_8_Template, 36, 12, "div", 4)(9, VehicleDetailsPage_ion_text_9_Template, 3, 0, "ion-text", 5);
        \u0275\u0275elementEnd();
      }
      if (rf & 2) {
        \u0275\u0275advance(7);
        \u0275\u0275property("ngIf", ctx.isLoading);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", !ctx.isLoading && ctx.vehicle);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", !ctx.isLoading && !ctx.vehicle);
      }
    }, dependencies: [IonicModule, IonBadge, IonButton, IonButtons, IonCard, IonCardContent, IonCardHeader, IonCardSubtitle, IonCardTitle, IonContent, IonHeader, IonIcon, IonItem, IonLabel, IonList, IonSpinner, IonText, IonTitle, IonToolbar, IonBackButton, CommonModule, NgIf, DatePipe], styles: ["\n\nion-card[_ngcontent-%COMP%] {\n  margin-bottom: 24px;\n}\nion-card[_ngcontent-%COMP%]   .vehicle-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 4px;\n}\nion-card[_ngcontent-%COMP%]   .vehicle-header[_ngcontent-%COMP%]   ion-badge[_ngcontent-%COMP%] {\n  font-size: 12px;\n  padding: 4px 8px;\n}\nion-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%] {\n  font-size: 24px;\n  font-weight: 600;\n}\nion-card[_ngcontent-%COMP%]   ion-card-subtitle[_ngcontent-%COMP%] {\n  font-size: 16px;\n  margin-top: 4px;\n}\nion-item[_ngcontent-%COMP%] {\n  --padding-start: 0;\n}\nion-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 24px;\n  margin-right: 16px;\n}\nion-item[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-weight: 500;\n  margin-bottom: 4px;\n}\nion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: var(--ion-color-medium);\n}\n.action-buttons[_ngcontent-%COMP%] {\n  margin-top: 24px;\n}\n.action-buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\n  margin-bottom: 12px;\n}\n/*# sourceMappingURL=vehicle-details.page.css.map */"] });
    VehicleDetailsPage = _VehicleDetailsPage;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(VehicleDetailsPage, [{
        type: Component,
        args: [{ selector: "app-vehicle-details", standalone: true, imports: [IonicModule, CommonModule], template: `<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/tabs/vehicles"></ion-back-button>
    </ion-buttons>
    <ion-title>Detalhes do Ve\xEDculo</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
  <!-- Indicador de carregamento -->
  <div *ngIf="isLoading" class="ion-text-center ion-padding">
    <ion-spinner></ion-spinner>
  </div>

  <!-- Detalhes do ve\xEDculo -->
  <div *ngIf="!isLoading && vehicle">
    <ion-card>
      <ion-card-header>
        <div class="vehicle-header">
          <ion-card-title>{{ vehicle.plate }}</ion-card-title>
          <ion-badge color="success" *ngIf="vehicle.isPrimary">Ve\xEDculo Principal</ion-badge>
        </div>
        <ion-card-subtitle>{{ vehicle.brandName }} {{ vehicle.modelName }}</ion-card-subtitle>
      </ion-card-header>

      <ion-card-content>
        <ion-list lines="none">
          <ion-item>
            <ion-icon name="calendar-outline" slot="start" color="primary"></ion-icon>
            <ion-label>
              <h3>Ano</h3>
              <p>{{ vehicle.year }}</p>
            </ion-label>
          </ion-item>

          <ion-item *ngIf="vehicle.version">
            <ion-icon name="options-outline" slot="start" color="primary"></ion-icon>
            <ion-label>
              <h3>Vers\xE3o</h3>
              <p>{{ vehicle.version }}</p>
            </ion-label>
          </ion-item>

          <ion-item *ngIf="vehicle.policyNumber">
            <ion-icon name="shield-checkmark-outline" slot="start" color="primary"></ion-icon>
            <ion-label>
              <h3>N\xFAmero da Ap\xF3lice</h3>
              <p>{{ vehicle.policyNumber }}</p>
            </ion-label>
          </ion-item>

          <ion-item>
            <ion-icon name="time-outline" slot="start" color="primary"></ion-icon>
            <ion-label>
              <h3>Data de Cadastro</h3>
              <p>{{ vehicle.createdOn | date:'dd/MM/yyyy HH:mm' }}</p>
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>

    <!-- Bot\xF5es de a\xE7\xE3o -->
    <div class="ion-padding">
      <ion-button expand="block" color="success" *ngIf="!vehicle.isPrimary" (click)="setPrimaryVehicle()">
        <ion-icon name="star" slot="start"></ion-icon>
        Definir como Ve\xEDculo Principal
      </ion-button>

      <ion-button expand="block" color="primary" (click)="goToEditVehicle(vehicle.id)">
        <ion-icon name="create-outline" slot="start"></ion-icon>
        Editar Ve\xEDculo
      </ion-button>

      <ion-button expand="block" color="danger" disabled>
        <ion-icon name="trash-outline" slot="start"></ion-icon>
        Excluir Ve\xEDculo
      </ion-button>
    </div>
  </div>

  <!-- Mensagem de erro -->
  <ion-text color="danger" *ngIf="!isLoading && !vehicle">
    <p class="ion-text-center">Ve\xEDculo n\xE3o encontrado.</p>
  </ion-text>
</ion-content>
`, styles: ["/* src/app/pages/tabs/vehicles/vehicle-details/vehicle-details.page.scss */\nion-card {\n  margin-bottom: 24px;\n}\nion-card .vehicle-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 4px;\n}\nion-card .vehicle-header ion-badge {\n  font-size: 12px;\n  padding: 4px 8px;\n}\nion-card ion-card-title {\n  font-size: 24px;\n  font-weight: 600;\n}\nion-card ion-card-subtitle {\n  font-size: 16px;\n  margin-top: 4px;\n}\nion-item {\n  --padding-start: 0;\n}\nion-item ion-icon {\n  font-size: 24px;\n  margin-right: 16px;\n}\nion-item h3 {\n  font-weight: 500;\n  margin-bottom: 4px;\n}\nion-item p {\n  color: var(--ion-color-medium);\n}\n.action-buttons {\n  margin-top: 24px;\n}\n.action-buttons ion-button {\n  margin-bottom: 12px;\n}\n/*# sourceMappingURL=vehicle-details.page.css.map */\n"] }]
      }], () => [{ type: ActivatedRoute }, { type: Router }, { type: VehicleService }, { type: ToastService }, { type: SessionService }, { type: VehicleStateService }], null);
    })();
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(VehicleDetailsPage, { className: "VehicleDetailsPage", filePath: "src/app/pages/tabs/vehicles/vehicle-details/vehicle-details.page.ts", lineNumber: 19 });
    })();
  }
});
init_vehicle_details_page();
export {
  VehicleDetailsPage
};
//# sourceMappingURL=vehicle-details.page-JZT7UD2X.js.map
