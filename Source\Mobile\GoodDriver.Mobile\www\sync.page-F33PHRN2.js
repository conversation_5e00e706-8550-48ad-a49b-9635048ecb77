import {
  JourneyStorageService,
  init_journey_storage_service
} from "./chunk-NZQXJLXV.js";
import "./chunk-ZCUEDWU7.js";
import {
  <PERSON>ert<PERSON>ontroller,
  BehaviorSubject,
  CommonModule,
  Component,
  Injectable,
  IonButton,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardSubtitle,
  IonCardTitle,
  IonContent,
  IonHeader,
  IonIcon,
  IonItem,
  IonLabel,
  IonList,
  IonProgressBar,
  IonSpinner,
  IonTitle,
  IonToolbar,
  IonicModule,
  NetworkService,
  NgIf,
  RouterLink,
  RouterLinkDelegateDirective2 as RouterLinkDelegateDirective,
  RouterModule,
  SessionService,
  SyncStatus,
  ToastService,
  VehicleService,
  init_common,
  init_core,
  init_esm,
  init_ionic_angular,
  init_network_service,
  init_router,
  init_session_service,
  init_sync_model,
  init_toast_service,
  init_vehicle_service,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵinject,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-LSKZXTNV.js";
import "./chunk-GJIVKQBF.js";
import "./chunk-WY354WYL.js";
import "./chunk-3TAP6XVY.js";
import "./chunk-BQEXAUCY.js";
import "./chunk-EUHNYIV3.js";
import "./chunk-AY6ZGCP5.js";
import "./chunk-I3UCYTIQ.js";
import "./chunk-4JI7VCBZ.js";
import "./chunk-YY7NCLAA.js";
import "./chunk-GUQ63TTB.js";
import "./chunk-CHFZ6R2P.js";
import "./chunk-XPKADKFH.js";
import "./chunk-V6QEVD67.js";
import "./chunk-WNPNB2PX.js";
import "./chunk-NRG74LSH.js";
import "./chunk-YSMEPS6E.js";
import "./chunk-NHEUXSZ2.js";
import "./chunk-WX4TOLMF.js";
import "./chunk-6SXGDLUH.js";
import "./chunk-NNIHVF7T.js";
import "./chunk-ZNALOMZJ.js";
import "./chunk-GHA2GP6C.js";
import "./chunk-H2CU6QHT.js";
import "./chunk-6PMAHZVS.js";
import "./chunk-DOEZTQY3.js";
import "./chunk-O4BCOVEV.js";
import "./chunk-TD4SIQNM.js";
import "./chunk-VU5YACRM.js";
import {
  __async,
  __esm,
  __spreadProps,
  __spreadValues
} from "./chunk-4W6HR7MY.js";

// src/app/core/services/sync.service.ts
var _SyncService, SyncService;
var init_sync_service = __esm({
  "src/app/core/services/sync.service.ts"() {
    "use strict";
    init_core();
    init_esm();
    init_sync_model();
    init_core();
    init_network_service();
    init_vehicle_service();
    init_journey_storage_service();
    init_session_service();
    init_toast_service();
    init_ionic_angular();
    _SyncService = class _SyncService {
      constructor(networkService, vehicleService, journeyService, sessionService, toastService, alertController) {
        this.networkService = networkService;
        this.vehicleService = vehicleService;
        this.journeyService = journeyService;
        this.sessionService = sessionService;
        this.toastService = toastService;
        this.alertController = alertController;
        this.syncInProgressSubject = new BehaviorSubject(false);
        this.syncInProgress$ = this.syncInProgressSubject.asObservable();
        this.lastSyncTimeSubject = new BehaviorSubject(null);
        this.lastSyncTime$ = this.lastSyncTimeSubject.asObservable();
        this.pendingSyncCountSubject = new BehaviorSubject(0);
        this.pendingSyncCount$ = this.pendingSyncCountSubject.asObservable();
        const lastSyncTime = localStorage.getItem("lastSyncTime");
        if (lastSyncTime) {
          this.lastSyncTimeSubject.next(new Date(lastSyncTime));
        }
        this.checkPendingSyncItems();
      }
      /**
       * Checks for items that need to be synchronized
       */
      checkPendingSyncItems() {
        return __async(this, null, function* () {
          try {
            const userId = yield this.sessionService.getUserId();
            if (!userId)
              return;
            const vehicles = yield this.vehicleService.listAllLocal(userId);
            const journeys = yield this.journeyService.getAllJourneysByUser(userId);
            const pendingVehicles = vehicles.filter((v) => v.syncStatus === SyncStatus.PendingCreate || v.syncStatus === SyncStatus.PendingUpdate || v.syncStatus === SyncStatus.PendingDelete);
            const pendingJourneys = journeys.filter((j) => j.syncStatus === SyncStatus.PendingCreate || j.syncStatus === SyncStatus.PendingUpdate || j.syncStatus === SyncStatus.PendingDelete);
            const totalPending = pendingVehicles.length + pendingJourneys.length;
            this.pendingSyncCountSubject.next(totalPending);
          } catch (error) {
            console.error("Error checking pending sync items:", error);
          }
        });
      }
      /**
       * Synchronizes all pending items with the server
       */
      syncAll() {
        return __async(this, null, function* () {
          if (!this.networkService.isOnlineNow()) {
            this.toastService.showToast("Sem conex\xE3o com a internet. N\xE3o \xE9 poss\xEDvel sincronizar.", "warning");
            return false;
          }
          if (this.syncInProgressSubject.getValue()) {
            this.toastService.showToast("Sincroniza\xE7\xE3o j\xE1 em andamento.", "info");
            return false;
          }
          this.syncInProgressSubject.next(true);
          try {
            const userId = yield this.sessionService.getUserId();
            if (!userId) {
              this.toastService.showToast("Erro: usu\xE1rio n\xE3o encontrado.", "danger");
              return false;
            }
            const syncResult = yield this.performDetailedSync(userId);
            if (syncResult.success) {
              const now = /* @__PURE__ */ new Date();
              this.lastSyncTimeSubject.next(now);
              localStorage.setItem("lastSyncTime", now.toISOString());
            }
            yield this.checkPendingSyncItems();
            this.showSyncResultMessage(syncResult);
            return syncResult.success;
          } catch (error) {
            console.error("Error during synchronization:", error);
            this.toastService.showToast("Erro inesperado durante a sincroniza\xE7\xE3o.", "danger");
            return false;
          } finally {
            this.syncInProgressSubject.next(false);
          }
        });
      }
      /**
       * Performs detailed synchronization and returns comprehensive results
       */
      performDetailedSync(userId) {
        return __async(this, null, function* () {
          const result = {
            success: false,
            vehiclesSuccess: false,
            journeysSuccess: false,
            vehiclesSynced: 0,
            vehiclesFailed: 0,
            journeysSynced: 0,
            journeysFailed: 0,
            errors: []
          };
          try {
            const vehicleResult = yield this.syncVehiclesDetailed(userId);
            result.vehiclesSuccess = vehicleResult.success;
            result.vehiclesSynced = vehicleResult.synced;
            result.vehiclesFailed = vehicleResult.failed;
            if (vehicleResult.errors.length > 0) {
              result.errors.push(...vehicleResult.errors);
            }
            const journeyResult = yield this.syncJourneysDetailed(userId);
            result.journeysSuccess = journeyResult.success;
            result.journeysSynced = journeyResult.synced;
            result.journeysFailed = journeyResult.failed;
            if (journeyResult.errors.length > 0) {
              result.errors.push(...journeyResult.errors);
            }
            result.success = result.vehiclesSuccess && result.journeysSuccess;
          } catch (error) {
            result.errors.push(`Erro geral na sincroniza\xE7\xE3o: ${error}`);
            console.error("Error in detailed sync:", error);
          }
          return result;
        });
      }
      /**
       * Shows appropriate message based on sync results
       */
      showSyncResultMessage(result) {
        if (result.success) {
          const totalSynced = result.vehiclesSynced + result.journeysSynced;
          if (totalSynced > 0) {
            this.toastService.showToast(`Sincroniza\xE7\xE3o conclu\xEDda! ${result.vehiclesSynced} ve\xEDculos e ${result.journeysSynced} viagens sincronizados.`, "success");
          } else {
            this.toastService.showToast("Sincroniza\xE7\xE3o conclu\xEDda! Nenhum item pendente.", "success");
          }
        } else {
          const totalFailed = result.vehiclesFailed + result.journeysFailed;
          const totalSynced = result.vehiclesSynced + result.journeysSynced;
          let errorMessage = "";
          let errorDetails = "";
          if (totalSynced > 0 && totalFailed > 0) {
            errorMessage = `Sincroniza\xE7\xE3o parcial: ${totalSynced} itens sincronizados, ${totalFailed} falharam.`;
          } else if (totalFailed > 0) {
            errorMessage = `Erro na sincroniza\xE7\xE3o: ${totalFailed} itens falharam.`;
          } else {
            errorMessage = "Erro na sincroniza\xE7\xE3o.";
          }
          if (result.errors.length > 0) {
            errorDetails = this.formatErrorDetails(result.errors);
            errorMessage += ` ${errorDetails}`;
          }
          this.toastService.showToast(errorMessage, totalSynced > 0 ? "warning" : "danger");
          if (result.errors.length > 2) {
            setTimeout(() => {
              this.showErrorDetailsOption(result.errors);
            }, 2e3);
          }
        }
      }
      /**
       * Formata os detalhes dos erros para exibição
       */
      formatErrorDetails(errors) {
        if (errors.length === 0)
          return "";
        if (errors.length === 1) {
          return `Erro: ${this.simplifyErrorMessage(errors[0])}`;
        }
        if (errors.length <= 2) {
          return `Erros: ${errors.map((e) => this.simplifyErrorMessage(e)).join(", ")}`;
        }
        const mainCauses = this.getMainErrorCauses(errors);
        return `Principais erros: ${mainCauses}`;
      }
      /**
       * Simplifica mensagens de erro para o usuário
       */
      simplifyErrorMessage(error) {
        const errorLower = error.toLowerCase();
        if (errorLower.includes("network") || errorLower.includes("fetch") || errorLower.includes("connection")) {
          return "Problema de conex\xE3o";
        }
        if (errorLower.includes("500") || errorLower.includes("internal server")) {
          return "Erro no servidor";
        }
        if (errorLower.includes("401") || errorLower.includes("unauthorized") || errorLower.includes("forbidden")) {
          return "Erro de autoriza\xE7\xE3o";
        }
        if (errorLower.includes("400") || errorLower.includes("bad request") || errorLower.includes("validation")) {
          return "Dados inv\xE1lidos";
        }
        if (errorLower.includes("timeout")) {
          return "Tempo esgotado";
        }
        const parts = error.split(":");
        if (parts.length > 1) {
          return parts[parts.length - 1].trim().substring(0, 50);
        }
        return error.substring(0, 50);
      }
      /**
       * Identifica as principais causas dos erros
       */
      getMainErrorCauses(errors) {
        const causes = {
          network: 0,
          server: 0,
          auth: 0,
          data: 0,
          timeout: 0,
          other: 0
        };
        errors.forEach((error) => {
          const errorLower = error.toLowerCase();
          if (errorLower.includes("network") || errorLower.includes("fetch") || errorLower.includes("connection")) {
            causes.network++;
          } else if (errorLower.includes("500") || errorLower.includes("internal server")) {
            causes.server++;
          } else if (errorLower.includes("401") || errorLower.includes("unauthorized") || errorLower.includes("forbidden")) {
            causes.auth++;
          } else if (errorLower.includes("400") || errorLower.includes("bad request") || errorLower.includes("validation")) {
            causes.data++;
          } else if (errorLower.includes("timeout")) {
            causes.timeout++;
          } else {
            causes.other++;
          }
        });
        const mainCauses = [];
        if (causes.network > 0)
          mainCauses.push(`${causes.network} problemas de conex\xE3o`);
        if (causes.server > 0)
          mainCauses.push(`${causes.server} erros do servidor`);
        if (causes.auth > 0)
          mainCauses.push(`${causes.auth} erros de autoriza\xE7\xE3o`);
        if (causes.data > 0)
          mainCauses.push(`${causes.data} dados inv\xE1lidos`);
        if (causes.timeout > 0)
          mainCauses.push(`${causes.timeout} timeouts`);
        if (causes.other > 0)
          mainCauses.push(`${causes.other} outros erros`);
        return mainCauses.join(", ") || "Erros diversos";
      }
      /**
       * Synchronizes vehicles with detailed results
       */
      syncVehiclesDetailed(userId) {
        return __async(this, null, function* () {
          const result = {
            success: false,
            synced: 0,
            failed: 0,
            errors: []
          };
          try {
            console.log("Iniciando sincroniza\xE7\xE3o de ve\xEDculos...");
            const pendingVehicles = yield this.vehicleService.getPendingSyncVehicles(userId);
            const initialPendingCount = pendingVehicles.length;
            const success = yield this.vehicleService.syncVehicles(userId);
            const remainingPendingVehicles = yield this.vehicleService.getPendingSyncVehicles(userId);
            const remainingCount = remainingPendingVehicles.length;
            result.synced = initialPendingCount - remainingCount;
            result.failed = remainingCount;
            result.success = success && remainingCount === 0;
            if (success) {
              console.log(`Sincroniza\xE7\xE3o de ve\xEDculos conclu\xEDda: ${result.synced} sincronizados, ${result.failed} falharam`);
            } else {
              console.warn(`Sincroniza\xE7\xE3o de ve\xEDculos com erros: ${result.synced} sincronizados, ${result.failed} falharam`);
              if (result.failed > 0) {
                result.errors.push(`${result.failed} ve\xEDculos falharam na sincroniza\xE7\xE3o`);
              } else {
                result.errors.push("Erro geral na sincroniza\xE7\xE3o de ve\xEDculos");
              }
            }
          } catch (error) {
            console.error("Error syncing vehicles:", error);
            result.errors.push(`Erro na sincroniza\xE7\xE3o de ve\xEDculos: ${error}`);
          }
          return result;
        });
      }
      /**
       * Synchronizes journeys with detailed results
       */
      syncJourneysDetailed(userId) {
        return __async(this, null, function* () {
          const result = {
            success: false,
            synced: 0,
            failed: 0,
            errors: []
          };
          try {
            console.log("Iniciando sincroniza\xE7\xE3o de viagens...");
            const journeys = yield this.journeyService.getAllJourneysByUser(userId);
            const pendingJourneys = journeys.filter((j) => j.syncStatus === SyncStatus.PendingCreate || j.syncStatus === SyncStatus.PendingUpdate || j.syncStatus === SyncStatus.PendingDelete);
            if (pendingJourneys.length === 0) {
              console.log("Nenhuma viagem pendente para sincronizar");
              result.success = true;
              return result;
            }
            console.log(`${pendingJourneys.length} viagens pendentes para sincronizar`);
            for (const journey of pendingJourneys) {
              try {
                yield this.journeyService.sendJourneyToSync({
                  id: journey.id,
                  startDate: journey.startDate,
                  endDate: journey.endDate,
                  distance: journey.distance,
                  userId: journey.userId,
                  vehicleId: journey.vehicleId,
                  infosJourney: journey.infosJourney
                });
                const updatedJourney = __spreadProps(__spreadValues({}, journey), {
                  syncStatus: SyncStatus.Synced,
                  lastSyncDate: /* @__PURE__ */ new Date()
                });
                yield this.journeyService.updateJourneySync(updatedJourney);
                result.synced++;
                console.log(`Viagem sincronizada: ${journey.id}`);
              } catch (error) {
                result.failed++;
                const errorMsg = `Erro ao sincronizar viagem ${journey.id}: ${error}`;
                console.error(errorMsg);
                result.errors.push(errorMsg);
              }
            }
            console.log(`${result.synced}/${pendingJourneys.length} viagens sincronizadas com sucesso`);
            if (result.failed > 0) {
              console.warn(`${result.failed} viagens falharam na sincroniza\xE7\xE3o`);
            }
            result.success = result.failed === 0;
          } catch (error) {
            const errorMsg = `Erro geral na sincroniza\xE7\xE3o de viagens: ${error}`;
            console.error(errorMsg);
            result.errors.push(errorMsg);
          }
          return result;
        });
      }
      /**
       * Mostra opção para ver detalhes dos erros
       */
      showErrorDetailsOption(errors) {
        return __async(this, null, function* () {
          this.toastService.showToast(`${errors.length} erros encontrados. Verificando detalhes...`, "warning");
          setTimeout(() => {
            this.showErrorDetailsModal(errors);
          }, 1e3);
        });
      }
      /**
       * Mostra modal com detalhes completos dos erros
       */
      showErrorDetailsModal(errors) {
        return __async(this, null, function* () {
          const errorList = errors.map((error, index) => `${index + 1}. ${this.simplifyErrorMessage(error)}`).join("\n");
          const alert = yield this.alertController.create({
            header: "Detalhes dos Erros de Sincroniza\xE7\xE3o",
            message: `Foram encontrados ${errors.length} erros durante a sincroniza\xE7\xE3o:

${errorList}`,
            buttons: [
              {
                text: "Tentar Novamente",
                handler: () => {
                  this.syncAll();
                }
              },
              {
                text: "Fechar",
                role: "cancel"
              }
            ]
          });
          yield alert.present();
        });
      }
    };
    _SyncService.\u0275fac = function SyncService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _SyncService)(\u0275\u0275inject(NetworkService), \u0275\u0275inject(VehicleService), \u0275\u0275inject(JourneyStorageService), \u0275\u0275inject(SessionService), \u0275\u0275inject(ToastService), \u0275\u0275inject(AlertController));
    };
    _SyncService.\u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _SyncService, factory: _SyncService.\u0275fac, providedIn: "root" });
    SyncService = _SyncService;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SyncService, [{
        type: Injectable,
        args: [{
          providedIn: "root"
        }]
      }], () => [{ type: NetworkService }, { type: VehicleService }, { type: JourneyStorageService }, { type: SessionService }, { type: ToastService }, { type: AlertController }], null);
    })();
  }
});

// src/app/shared/components/sync-status/sync-status.component.ts
function SyncStatusComponent_ion_spinner_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "ion-spinner", 6);
  }
}
function SyncStatusComponent_ion_progress_bar_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "ion-progress-bar", 7);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275property("color", ctx_r0.getSyncStatusColor());
  }
}
var _SyncStatusComponent, SyncStatusComponent;
var init_sync_status_component = __esm({
  "src/app/shared/components/sync-status/sync-status.component.ts"() {
    "use strict";
    init_core();
    init_ionic_angular();
    init_common();
    init_core();
    init_sync_service();
    init_network_service();
    init_ionic_angular();
    init_common();
    _SyncStatusComponent = class _SyncStatusComponent {
      constructor(syncService, networkService) {
        this.syncService = syncService;
        this.networkService = networkService;
        this.isOnline = true;
        this.syncInProgress = false;
        this.pendingSyncCount = 0;
        this.lastSyncTime = null;
        this.subscriptions = [];
      }
      ngOnInit() {
        this.subscriptions.push(this.networkService.getOnlineStatus().subscribe((isOnline) => {
          this.isOnline = isOnline;
        }));
        this.subscriptions.push(this.syncService.syncInProgress$.subscribe((inProgress) => {
          this.syncInProgress = inProgress;
        }));
        this.subscriptions.push(this.syncService.pendingSyncCount$.subscribe((count) => {
          this.pendingSyncCount = count;
        }));
        this.subscriptions.push(this.syncService.lastSyncTime$.subscribe((time) => {
          this.lastSyncTime = time;
        }));
      }
      ngOnDestroy() {
        this.subscriptions.forEach((sub) => sub.unsubscribe());
      }
      /**
       * Returns a formatted string for the last sync time
       */
      getLastSyncTimeFormatted() {
        if (!this.lastSyncTime) {
          return "Nunca";
        }
        return this.lastSyncTime.toLocaleString("pt-BR", {
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
          hour: "2-digit",
          minute: "2-digit"
        });
      }
      /**
       * Returns the appropriate icon for the sync status
       */
      getSyncStatusIcon() {
        if (this.syncInProgress) {
          return "sync-circle";
        }
        if (this.pendingSyncCount > 0) {
          return "warning";
        }
        return "checkmark-circle-outline";
      }
      /**
       * Returns the appropriate color for the sync status
       */
      getSyncStatusColor() {
        if (this.syncInProgress) {
          return "primary";
        }
        if (this.pendingSyncCount > 0) {
          return "warning";
        }
        return "success";
      }
      /**
       * Returns the appropriate message for the sync status
       */
      getSyncStatusMessage() {
        if (this.syncInProgress) {
          return "Sincroniza\xE7\xE3o em andamento...";
        }
        if (this.pendingSyncCount > 0) {
          return `${this.pendingSyncCount} item(s) aguardando sincroniza\xE7\xE3o`;
        }
        return "Todos os dados est\xE3o sincronizados";
      }
    };
    _SyncStatusComponent.\u0275fac = function SyncStatusComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _SyncStatusComponent)(\u0275\u0275directiveInject(SyncService), \u0275\u0275directiveInject(NetworkService));
    };
    _SyncStatusComponent.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _SyncStatusComponent, selectors: [["app-sync-status"]], decls: 22, vars: 9, consts: [[3, "name", "color"], ["lines", "none"], ["slot", "start", 3, "name", "color"], ["name", "crescent", "slot", "end", 4, "ngIf"], ["type", "indeterminate", 3, "color", 4, "ngIf"], ["slot", "start", "name", "time-outline", "color", "medium"], ["name", "crescent", "slot", "end"], ["type", "indeterminate", 3, "color"]], template: function SyncStatusComponent_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-card")(1, "ion-card-header")(2, "ion-card-title");
        \u0275\u0275element(3, "ion-icon", 0);
        \u0275\u0275text(4, " Status de Sincroniza\xE7\xE3o ");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(5, "ion-card-content")(6, "ion-item", 1);
        \u0275\u0275element(7, "ion-icon", 2);
        \u0275\u0275elementStart(8, "ion-label")(9, "h2");
        \u0275\u0275text(10);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(11, "p");
        \u0275\u0275text(12);
        \u0275\u0275elementEnd()();
        \u0275\u0275template(13, SyncStatusComponent_ion_spinner_13_Template, 1, 0, "ion-spinner", 3);
        \u0275\u0275elementEnd();
        \u0275\u0275template(14, SyncStatusComponent_ion_progress_bar_14_Template, 1, 1, "ion-progress-bar", 4);
        \u0275\u0275elementStart(15, "ion-item", 1);
        \u0275\u0275element(16, "ion-icon", 5);
        \u0275\u0275elementStart(17, "ion-label")(18, "h2");
        \u0275\u0275text(19, "\xDAltima sincroniza\xE7\xE3o");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(20, "p");
        \u0275\u0275text(21);
        \u0275\u0275elementEnd()()()()();
      }
      if (rf & 2) {
        \u0275\u0275advance(3);
        \u0275\u0275property("name", ctx.getSyncStatusIcon())("color", ctx.getSyncStatusColor());
        \u0275\u0275advance(4);
        \u0275\u0275property("name", ctx.getSyncStatusIcon())("color", ctx.getSyncStatusColor());
        \u0275\u0275advance(3);
        \u0275\u0275textInterpolate(ctx.syncInProgress ? "Sincronizando..." : ctx.pendingSyncCount > 0 ? "Pendente" : "Sincronizado");
        \u0275\u0275advance(2);
        \u0275\u0275textInterpolate(ctx.getSyncStatusMessage());
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", ctx.syncInProgress);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", ctx.syncInProgress);
        \u0275\u0275advance(7);
        \u0275\u0275textInterpolate(ctx.getLastSyncTimeFormatted());
      }
    }, dependencies: [IonicModule, IonCard, IonCardContent, IonCardHeader, IonCardTitle, IonIcon, IonItem, IonLabel, IonProgressBar, IonSpinner, CommonModule, NgIf], styles: ["\n\nion-card[_ngcontent-%COMP%] {\n  margin: var(--app-spacing-md);\n}\nion-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n}\nion-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  margin-right: var(--app-spacing-sm);\n}\nion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%] {\n  --padding-start: 0;\n}\nion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n}\nion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  font-weight: 500;\n  margin-bottom: var(--app-spacing-xs);\n}\nion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: var(--ion-color-medium);\n}\nion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-progress-bar[_ngcontent-%COMP%] {\n  margin: var(--app-spacing-md) 0;\n}\n/*# sourceMappingURL=sync-status.component.css.map */"] });
    SyncStatusComponent = _SyncStatusComponent;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SyncStatusComponent, [{
        type: Component,
        args: [{ selector: "app-sync-status", standalone: true, imports: [IonicModule, CommonModule], template: `<ion-card>
  <ion-card-header>
    <ion-card-title>
      <ion-icon [name]="getSyncStatusIcon()" [color]="getSyncStatusColor()"></ion-icon>
      Status de Sincroniza\xE7\xE3o
    </ion-card-title>
  </ion-card-header>
  <ion-card-content>
    <ion-item lines="none">
      <ion-icon slot="start" [name]="getSyncStatusIcon()" [color]="getSyncStatusColor()"></ion-icon>
      <ion-label>
        <h2>{{ syncInProgress ? 'Sincronizando...' : (pendingSyncCount > 0 ? 'Pendente' : 'Sincronizado') }}</h2>
        <p>{{ getSyncStatusMessage() }}</p>
      </ion-label>
      <ion-spinner *ngIf="syncInProgress" name="crescent" slot="end"></ion-spinner>
    </ion-item>
    
    <ion-progress-bar *ngIf="syncInProgress" type="indeterminate" [color]="getSyncStatusColor()"></ion-progress-bar>
    
    <ion-item lines="none">
      <ion-icon slot="start" name="time-outline" color="medium"></ion-icon>
      <ion-label>
        <h2>\xDAltima sincroniza\xE7\xE3o</h2>
        <p>{{ getLastSyncTimeFormatted() }}</p>
      </ion-label>
    </ion-item>
  </ion-card-content>
</ion-card>
`, styles: ["/* src/app/shared/components/sync-status/sync-status.component.scss */\nion-card {\n  margin: var(--app-spacing-md);\n}\nion-card ion-card-header ion-card-title {\n  display: flex;\n  align-items: center;\n}\nion-card ion-card-header ion-card-title ion-icon {\n  margin-right: var(--app-spacing-sm);\n}\nion-card ion-card-content ion-item {\n  --padding-start: 0;\n}\nion-card ion-card-content ion-item ion-icon {\n  font-size: 1.5rem;\n}\nion-card ion-card-content ion-item h2 {\n  font-weight: 500;\n  margin-bottom: var(--app-spacing-xs);\n}\nion-card ion-card-content ion-item p {\n  color: var(--ion-color-medium);\n}\nion-card ion-card-content ion-progress-bar {\n  margin: var(--app-spacing-md) 0;\n}\n/*# sourceMappingURL=sync-status.component.css.map */\n"] }]
      }], () => [{ type: SyncService }, { type: NetworkService }], null);
    })();
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(SyncStatusComponent, { className: "SyncStatusComponent", filePath: "src/app/shared/components/sync-status/sync-status.component.ts", lineNumber: 15 });
    })();
  }
});

// src/app/pages/sync/sync.page.ts
function SyncPage_p_44_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1("", ctx_r0.pendingSyncCount, " item(s) aguardando sincroniza\xE7\xE3o");
  }
}
function SyncPage_p_45_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p");
    \u0275\u0275text(1, "Todos os dados est\xE3o sincronizados");
    \u0275\u0275elementEnd();
  }
}
function SyncPage_ion_spinner_54_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "ion-spinner", 14);
  }
}
var _SyncPage, SyncPage;
var init_sync_page = __esm({
  "src/app/pages/sync/sync.page.ts"() {
    init_core();
    init_ionic_angular();
    init_common();
    init_sync_status_component();
    init_router();
    init_core();
    init_sync_service();
    init_network_service();
    init_toast_service();
    init_session_service();
    init_ionic_angular();
    init_common();
    init_router();
    _SyncPage = class _SyncPage {
      constructor(syncService, networkService, toastService, sessionService) {
        this.syncService = syncService;
        this.networkService = networkService;
        this.toastService = toastService;
        this.sessionService = sessionService;
        this.isOnline = true;
        this.syncInProgress = false;
        this.pendingSyncCount = 0;
        this.lastSyncTime = null;
        this.username = "";
        this.subscriptions = [];
      }
      ngOnInit() {
        return __async(this, null, function* () {
          this.username = (yield this.sessionService.getUserName()) || "Usu\xE1rio";
          this.subscriptions.push(this.networkService.getOnlineStatus().subscribe((isOnline) => {
            this.isOnline = isOnline;
          }));
          this.subscriptions.push(this.syncService.syncInProgress$.subscribe((inProgress) => {
            this.syncInProgress = inProgress;
          }));
          this.subscriptions.push(this.syncService.pendingSyncCount$.subscribe((count) => {
            this.pendingSyncCount = count;
          }));
          this.subscriptions.push(this.syncService.lastSyncTime$.subscribe((time) => {
            this.lastSyncTime = time;
          }));
        });
      }
      ngOnDestroy() {
        this.subscriptions.forEach((sub) => sub.unsubscribe());
      }
      /**
       * Triggers a manual sync
       */
      syncAll() {
        return __async(this, null, function* () {
          if (!this.isOnline) {
            this.toastService.showToast("Sem conex\xE3o com a internet. N\xE3o \xE9 poss\xEDvel sincronizar.", "warning");
            return;
          }
          if (this.syncInProgress) {
            this.toastService.showToast("Sincroniza\xE7\xE3o j\xE1 em andamento.", "info");
            return;
          }
          yield this.syncService.syncAll();
        });
      }
      /**
       * Returns a formatted string for the last sync time
       */
      getLastSyncTimeFormatted() {
        if (!this.lastSyncTime) {
          return "Nunca";
        }
        return this.lastSyncTime.toLocaleString("pt-BR", {
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
          hour: "2-digit",
          minute: "2-digit"
        });
      }
    };
    _SyncPage.\u0275fac = function SyncPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _SyncPage)(\u0275\u0275directiveInject(SyncService), \u0275\u0275directiveInject(NetworkService), \u0275\u0275directiveInject(ToastService), \u0275\u0275directiveInject(SessionService));
    };
    _SyncPage.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _SyncPage, selectors: [["app-sync"]], decls: 59, vars: 17, consts: [[1, "ion-padding"], [1, "ion-text-center"], [3, "name", "color"], ["lines", "none"], ["slot", "start", 3, "name", "color"], ["name", "information-circle-outline"], ["slot", "start", "name", "time-outline", "color", "primary"], [4, "ngIf"], ["name", "options-outline"], ["expand", "block", 3, "click", "disabled", "color"], ["slot", "start", 3, "name"], ["name", "crescent", "slot", "start", 4, "ngIf"], ["expand", "block", "fill", "outline", "routerLink", "/tabs/home", 1, "ion-margin-top"], ["name", "home-outline", "slot", "start"], ["name", "crescent", "slot", "start"]], template: function SyncPage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header")(1, "ion-toolbar")(2, "ion-title");
        \u0275\u0275text(3, "Sincroniza\xE7\xE3o");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(4, "ion-content", 0)(5, "ion-card")(6, "ion-card-header")(7, "ion-card-title", 1);
        \u0275\u0275text(8);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(9, "ion-card-subtitle", 1);
        \u0275\u0275text(10, "Gerenciamento de Sincroniza\xE7\xE3o");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(11, "ion-card")(12, "ion-card-header")(13, "ion-card-title");
        \u0275\u0275element(14, "ion-icon", 2);
        \u0275\u0275text(15, " Status de Conex\xE3o ");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(16, "ion-card-content")(17, "ion-item", 3);
        \u0275\u0275element(18, "ion-icon", 4);
        \u0275\u0275elementStart(19, "ion-label")(20, "h2");
        \u0275\u0275text(21);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(22, "p");
        \u0275\u0275text(23);
        \u0275\u0275elementEnd()()()()();
        \u0275\u0275element(24, "app-sync-status");
        \u0275\u0275elementStart(25, "ion-card")(26, "ion-card-header")(27, "ion-card-title");
        \u0275\u0275element(28, "ion-icon", 5);
        \u0275\u0275text(29, " Informa\xE7\xF5es de Sincroniza\xE7\xE3o ");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(30, "ion-card-content")(31, "ion-list")(32, "ion-item");
        \u0275\u0275element(33, "ion-icon", 6);
        \u0275\u0275elementStart(34, "ion-label")(35, "h2");
        \u0275\u0275text(36, "\xDAltima sincroniza\xE7\xE3o");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(37, "p");
        \u0275\u0275text(38);
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(39, "ion-item");
        \u0275\u0275element(40, "ion-icon", 4);
        \u0275\u0275elementStart(41, "ion-label")(42, "h2");
        \u0275\u0275text(43, "Itens pendentes");
        \u0275\u0275elementEnd();
        \u0275\u0275template(44, SyncPage_p_44_Template, 2, 1, "p", 7)(45, SyncPage_p_45_Template, 2, 0, "p", 7);
        \u0275\u0275elementEnd()()()()();
        \u0275\u0275elementStart(46, "ion-card")(47, "ion-card-header")(48, "ion-card-title");
        \u0275\u0275element(49, "ion-icon", 8);
        \u0275\u0275text(50, " A\xE7\xF5es ");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(51, "ion-card-content")(52, "ion-button", 9);
        \u0275\u0275listener("click", function SyncPage_Template_ion_button_click_52_listener() {
          return ctx.syncAll();
        });
        \u0275\u0275element(53, "ion-icon", 10);
        \u0275\u0275template(54, SyncPage_ion_spinner_54_Template, 1, 0, "ion-spinner", 11);
        \u0275\u0275text(55);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(56, "ion-button", 12);
        \u0275\u0275element(57, "ion-icon", 13);
        \u0275\u0275text(58, " Voltar para Home ");
        \u0275\u0275elementEnd()()()();
      }
      if (rf & 2) {
        \u0275\u0275advance(8);
        \u0275\u0275textInterpolate1("Bem-vindo, ", ctx.username, "!");
        \u0275\u0275advance(6);
        \u0275\u0275property("name", ctx.isOnline ? "wifi-outline" : "wifi-off-outline")("color", ctx.isOnline ? "success" : "danger");
        \u0275\u0275advance(4);
        \u0275\u0275property("name", ctx.isOnline ? "cloud-done-outline" : "cloud-offline-outline")("color", ctx.isOnline ? "success" : "danger");
        \u0275\u0275advance(3);
        \u0275\u0275textInterpolate(ctx.isOnline ? "Conectado" : "Desconectado");
        \u0275\u0275advance(2);
        \u0275\u0275textInterpolate(ctx.isOnline ? "Voc\xEA est\xE1 conectado \xE0 internet." : "Voc\xEA est\xE1 offline. Conecte-se para sincronizar seus dados.");
        \u0275\u0275advance(15);
        \u0275\u0275textInterpolate(ctx.getLastSyncTimeFormatted());
        \u0275\u0275advance(2);
        \u0275\u0275property("name", ctx.pendingSyncCount > 0 ? "alert-circle-outline" : "checkmark-circle-outline")("color", ctx.pendingSyncCount > 0 ? "warning" : "success");
        \u0275\u0275advance(4);
        \u0275\u0275property("ngIf", ctx.pendingSyncCount > 0);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", ctx.pendingSyncCount === 0);
        \u0275\u0275advance(7);
        \u0275\u0275property("disabled", !ctx.isOnline || ctx.syncInProgress)("color", ctx.pendingSyncCount > 0 ? "warning" : "primary");
        \u0275\u0275advance();
        \u0275\u0275property("name", ctx.syncInProgress ? "sync-circle" : "sync");
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", ctx.syncInProgress);
        \u0275\u0275advance();
        \u0275\u0275textInterpolate1(" ", ctx.syncInProgress ? "Sincronizando..." : "Sincronizar Agora", " ");
      }
    }, dependencies: [IonicModule, IonButton, IonCard, IonCardContent, IonCardHeader, IonCardSubtitle, IonCardTitle, IonContent, IonHeader, IonIcon, IonItem, IonLabel, IonList, IonSpinner, IonTitle, IonToolbar, RouterLinkDelegateDirective, CommonModule, NgIf, SyncStatusComponent, RouterModule, RouterLink], styles: ["\n\nion-card[_ngcontent-%COMP%] {\n  border-radius: var(--app-border-radius-md, 8px);\n  box-shadow: var(--app-card-shadow, 0 2px 4px rgba(0, 0, 0, 0.1)) !important;\n  margin: var(--app-spacing-md, 16px) var(--app-spacing-md, 16px) var(--app-spacing-lg, 24px);\n}\nion-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%] {\n  padding: var(--app-spacing-md, 16px);\n}\nion-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%] {\n  font-size: 1.25rem;\n  font-weight: var(--app-heading-font-weight, 600);\n  display: flex;\n  align-items: center;\n}\nion-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  margin-right: 8px;\n  font-size: 1.5rem;\n}\nion-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-subtitle[_ngcontent-%COMP%] {\n  font-size: 0.9rem;\n  color: var(--ion-color-medium);\n  margin-top: var(--app-spacing-xs, 4px);\n}\nion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%] {\n  padding: var(--app-spacing-md, 16px);\n}\nion-item[_ngcontent-%COMP%] {\n  --padding-start: var(--app-spacing-md, 16px);\n  --padding-end: var(--app-spacing-md, 16px);\n  --inner-padding-end: 0;\n}\nion-item[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  font-weight: 500;\n  font-size: 1rem;\n}\nion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 0.9rem;\n  color: var(--ion-color-medium);\n}\nion-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n}\nion-button[_ngcontent-%COMP%] {\n  margin: var(--app-spacing-md, 16px) 0 0;\n  height: 48px;\n}\nion-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  margin-right: 8px;\n}\n.sync-status[_ngcontent-%COMP%] {\n  margin: var(--app-spacing-md, 16px) 0;\n}\n/*# sourceMappingURL=sync.page.css.map */"] });
    SyncPage = _SyncPage;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SyncPage, [{
        type: Component,
        args: [{ selector: "app-sync", standalone: true, imports: [IonicModule, CommonModule, SyncStatusComponent, RouterModule], template: `<ion-header>
  <ion-toolbar>
    <ion-title>Sincroniza\xE7\xE3o</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
  <ion-card>
    <ion-card-header>
      <ion-card-title class="ion-text-center">Bem-vindo, {{username}}!</ion-card-title>
      <ion-card-subtitle class="ion-text-center">Gerenciamento de Sincroniza\xE7\xE3o</ion-card-subtitle>
    </ion-card-header>
  </ion-card>

  <!-- Status de Conex\xE3o -->
  <ion-card>
    <ion-card-header>
      <ion-card-title>
        <ion-icon [name]="isOnline ? 'wifi-outline' : 'wifi-off-outline'" 
                  [color]="isOnline ? 'success' : 'danger'"></ion-icon>
        Status de Conex\xE3o
      </ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-item lines="none">
        <ion-icon slot="start" [name]="isOnline ? 'cloud-done-outline' : 'cloud-offline-outline'" 
                  [color]="isOnline ? 'success' : 'danger'"></ion-icon>
        <ion-label>
          <h2>{{ isOnline ? 'Conectado' : 'Desconectado' }}</h2>
          <p>{{ isOnline ? 'Voc\xEA est\xE1 conectado \xE0 internet.' : 'Voc\xEA est\xE1 offline. Conecte-se para sincronizar seus dados.' }}</p>
        </ion-label>
      </ion-item>
    </ion-card-content>
  </ion-card>

  <!-- Componente de Status de Sincroniza\xE7\xE3o -->
  <app-sync-status></app-sync-status>

  <!-- Informa\xE7\xF5es de Sincroniza\xE7\xE3o -->
  <ion-card>
    <ion-card-header>
      <ion-card-title>
        <ion-icon name="information-circle-outline"></ion-icon>
        Informa\xE7\xF5es de Sincroniza\xE7\xE3o
      </ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-list>
        <ion-item>
          <ion-icon slot="start" name="time-outline" color="primary"></ion-icon>
          <ion-label>
            <h2>\xDAltima sincroniza\xE7\xE3o</h2>
            <p>{{ getLastSyncTimeFormatted() }}</p>
          </ion-label>
        </ion-item>
        
        <ion-item>
          <ion-icon slot="start" [name]="pendingSyncCount > 0 ? 'alert-circle-outline' : 'checkmark-circle-outline'" 
                    [color]="pendingSyncCount > 0 ? 'warning' : 'success'"></ion-icon>
          <ion-label>
            <h2>Itens pendentes</h2>
            <p *ngIf="pendingSyncCount > 0">{{ pendingSyncCount }} item(s) aguardando sincroniza\xE7\xE3o</p>
            <p *ngIf="pendingSyncCount === 0">Todos os dados est\xE3o sincronizados</p>
          </ion-label>
        </ion-item>
      </ion-list>
    </ion-card-content>
  </ion-card>

  <!-- A\xE7\xF5es de Sincroniza\xE7\xE3o -->
  <ion-card>
    <ion-card-header>
      <ion-card-title>
        <ion-icon name="options-outline"></ion-icon>
        A\xE7\xF5es
      </ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-button expand="block" 
                  [disabled]="!isOnline || syncInProgress" 
                  (click)="syncAll()"
                  [color]="pendingSyncCount > 0 ? 'warning' : 'primary'">
        <ion-icon [name]="syncInProgress ? 'sync-circle' : 'sync'" slot="start"></ion-icon>
        <ion-spinner *ngIf="syncInProgress" name="crescent" slot="start"></ion-spinner>
        {{ syncInProgress ? 'Sincronizando...' : 'Sincronizar Agora' }}
      </ion-button>
      
      <ion-button expand="block" fill="outline" routerLink="/tabs/home" class="ion-margin-top">
        <ion-icon name="home-outline" slot="start"></ion-icon>
        Voltar para Home
      </ion-button>
    </ion-card-content>
  </ion-card>
</ion-content>
`, styles: ["/* src/app/pages/sync/sync.page.scss */\nion-card {\n  border-radius: var(--app-border-radius-md, 8px);\n  box-shadow: var(--app-card-shadow, 0 2px 4px rgba(0, 0, 0, 0.1)) !important;\n  margin: var(--app-spacing-md, 16px) var(--app-spacing-md, 16px) var(--app-spacing-lg, 24px);\n}\nion-card ion-card-header {\n  padding: var(--app-spacing-md, 16px);\n}\nion-card ion-card-header ion-card-title {\n  font-size: 1.25rem;\n  font-weight: var(--app-heading-font-weight, 600);\n  display: flex;\n  align-items: center;\n}\nion-card ion-card-header ion-card-title ion-icon {\n  margin-right: 8px;\n  font-size: 1.5rem;\n}\nion-card ion-card-header ion-card-subtitle {\n  font-size: 0.9rem;\n  color: var(--ion-color-medium);\n  margin-top: var(--app-spacing-xs, 4px);\n}\nion-card ion-card-content {\n  padding: var(--app-spacing-md, 16px);\n}\nion-item {\n  --padding-start: var(--app-spacing-md, 16px);\n  --padding-end: var(--app-spacing-md, 16px);\n  --inner-padding-end: 0;\n}\nion-item h2 {\n  font-weight: 500;\n  font-size: 1rem;\n}\nion-item p {\n  font-size: 0.9rem;\n  color: var(--ion-color-medium);\n}\nion-item ion-icon {\n  font-size: 1.5rem;\n}\nion-button {\n  margin: var(--app-spacing-md, 16px) 0 0;\n  height: 48px;\n}\nion-button ion-icon {\n  margin-right: 8px;\n}\n.sync-status {\n  margin: var(--app-spacing-md, 16px) 0;\n}\n/*# sourceMappingURL=sync.page.css.map */\n"] }]
      }], () => [{ type: SyncService }, { type: NetworkService }, { type: ToastService }, { type: SessionService }], null);
    })();
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(SyncPage, { className: "SyncPage", filePath: "src/app/pages/sync/sync.page.ts", lineNumber: 19 });
    })();
  }
});
init_sync_page();
export {
  SyncPage
};
//# sourceMappingURL=sync.page-F33PHRN2.js.map
