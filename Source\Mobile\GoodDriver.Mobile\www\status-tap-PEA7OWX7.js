import {
  findClosestIonContent,
  init_index8,
  scrollToTop
} from "./chunk-GPCNX67I.js";
import {
  init_client,
  readTask,
  writeTask
} from "./chunk-H2CU6QHT.js";
import {
  componentOnReady,
  init_helpers
} from "./chunk-DOEZTQY3.js";
import "./chunk-TD4SIQNM.js";
import {
  __async,
  __esm
} from "./chunk-4W6HR7MY.js";

// node_modules/@ionic/core/components/status-tap.js
var startStatusTap;
var init_status_tap = __esm({
  "node_modules/@ionic/core/components/status-tap.js"() {
    init_client();
    init_index8();
    init_helpers();
    startStatusTap = () => {
      const win = window;
      win.addEventListener("statusTap", () => {
        readTask(() => {
          const width = win.innerWidth;
          const height = win.innerHeight;
          const el = document.elementFromPoint(width / 2, height / 2);
          if (!el) {
            return;
          }
          const contentEl = findClosestIonContent(el);
          if (contentEl) {
            new Promise((resolve) => componentOnReady(contentEl, resolve)).then(() => {
              writeTask(() => __async(null, null, function* () {
                contentEl.style.setProperty("--overflow", "hidden");
                yield scrollToTop(contentEl, 300);
                contentEl.style.removeProperty("--overflow");
              }));
            });
          }
        });
      });
    };
  }
});
init_status_tap();
export {
  startStatusTap
};
/*! Bundled license information:

@ionic/core/components/status-tap.js:
  (*!
   * (C) Ionic http://ionicframework.com - MIT License
   *)
*/
//# sourceMappingURL=status-tap-PEA7OWX7.js.map
