{"version": 3, "sources": ["src/app/pages/login/login.page.scss"], "sourcesContent": [".login {\r\n  --background: #f8f8f8;\r\n  background: linear-gradient(135deg, rgba(46, 125, 50, 0.05) 0%, rgba(255, 255, 255, 1) 100%);\r\n\r\n  .login-header {\r\n    text-align: center;\r\n    padding: 40px 0 10px;\r\n\r\n    .logo-small {\r\n      width: 80px;\r\n      height: 80px;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    h1 {\r\n      font-size: 24px;\r\n      font-weight: 600;\r\n      color: var(--ion-color-primary);\r\n      margin: 0;\r\n    }\r\n  }\r\n\r\n  .login-container {\r\n    width: 90%;\r\n    max-width: 400px;\r\n    margin: 0 auto;\r\n    padding: 20px;\r\n    border-radius: var(--app-border-radius-lg);\r\n    background-color: white;\r\n    box-shadow: var(--app-card-shadow);\r\n\r\n    h2 {\r\n      margin-bottom: 24px;\r\n      color: var(--ion-color-primary);\r\n      font-weight: 500;\r\n      text-align: center;\r\n    }\r\n\r\n    .custom-input {\r\n      margin-bottom: 16px;\r\n      border-radius: var(--app-border-radius-md);\r\n      --background: var(--app-input-background);\r\n      --border-radius: var(--app-border-radius-md);\r\n      --padding-start: 12px;\r\n\r\n      ion-icon {\r\n        color: var(--ion-color-primary);\r\n        margin-right: 8px;\r\n      }\r\n    }\r\n\r\n    .login-btn {\r\n      margin-top: 24px;\r\n      --border-radius: var(--app-border-radius-md);\r\n      font-weight: 500;\r\n      height: 48px;\r\n      text-transform: none;\r\n      font-size: 16px;\r\n      box-shadow: 0 4px 8px rgba(46, 125, 50, 0.2);\r\n    }\r\n\r\n    .links {\r\n      margin-top: 24px;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      font-size: 0.9em;\r\n      padding: 0 8px;\r\n\r\n      a {\r\n        color: var(--ion-color-primary);\r\n        text-decoration: none;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        ion-icon {\r\n          margin-right: 4px;\r\n        }\r\n\r\n        &:hover {\r\n          text-decoration: underline;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,CAAA;AACE,gBAAA;AACA;IAAA;MAAA,MAAA;MAAA,KAAA,EAAA,EAAA,GAAA,EAAA,EAAA,EAAA,MAAA,EAAA;MAAA,IAAA,GAAA,EAAA,GAAA,EAAA,KAAA;;AAEA,CAJF,MAIE,CAAA;AACE,cAAA;AACA,WAAA,KAAA,EAAA;;AAEA,CARJ,MAQI,CAJF,aAIE,CAAA;AACE,SAAA;AACA,UAAA;AACA,iBAAA;;AAGF,CAdJ,MAcI,CAVF,aAUE;AACE,aAAA;AACA,eAAA;AACA,SAAA,IAAA;AACA,UAAA;;AAIJ,CAtBF,MAsBE,CAAA;AACE,SAAA;AACA,aAAA;AACA,UAAA,EAAA;AACA,WAAA;AACA,iBAAA,IAAA;AACA,oBAAA;AACA,cAAA,IAAA;;AAEA,CA/BJ,MA+BI,CATF,gBASE;AACE,iBAAA;AACA,SAAA,IAAA;AACA,eAAA;AACA,cAAA;;AAGF,CAtCJ,MAsCI,CAhBF,gBAgBE,CAAA;AACE,iBAAA;AACA,iBAAA,IAAA;AACA,gBAAA,IAAA;AACA,mBAAA,IAAA;AACA,mBAAA;;AAEA,CA7CN,MA6CM,CAvBJ,gBAuBI,CAPF,aAOE;AACE,SAAA,IAAA;AACA,gBAAA;;AAIJ,CAnDJ,MAmDI,CA7BF,gBA6BE,CAAA;AACE,cAAA;AACA,mBAAA,IAAA;AACA,eAAA;AACA,UAAA;AACA,kBAAA;AACA,aAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,EAAA,EAAA,GAAA,EAAA,EAAA,EAAA;;AAGF,CA7DJ,MA6DI,CAvCF,gBAuCE,CAAA;AACE,cAAA;AACA,WAAA;AACA,mBAAA;AACA,aAAA;AACA,WAAA,EAAA;;AAEA,CApEN,MAoEM,CA9CJ,gBA8CI,CAPF,MAOE;AACE,SAAA,IAAA;AACA,mBAAA;AACA,WAAA;AACA,eAAA;;AAEA,CA1ER,MA0EQ,CApDN,gBAoDM,CAbJ,MAaI,EAAA;AACE,gBAAA;;AAGF,CA9ER,MA8EQ,CAxDN,gBAwDM,CAjBJ,MAiBI,CAAA;AACE,mBAAA;;", "names": []}