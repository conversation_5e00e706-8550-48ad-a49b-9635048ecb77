{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-segment-content.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as Host } from './index-527b9e34.js';\nconst segmentContentCss = \":host{scroll-snap-align:center;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%;overflow-y:scroll;scrollbar-width:none;-ms-overflow-style:none;}:host::-webkit-scrollbar{display:none}\";\nconst IonSegmentContentStyle0 = segmentContentCss;\nconst SegmentContent = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n  }\n  render() {\n    return h(Host, {\n      key: '64b3965b14c749e77e9ce13b59f349d971e245c8'\n    }, h(\"slot\", {\n      key: '2d0bed34f9bc93f92e713cb51e42220f3cecd8f5'\n    }));\n  }\n};\nSegmentContent.style = IonSegmentContentStyle0;\nexport { SegmentContent as ion_segment_content };"], "mappings": ";;;;;;;;;;;AAAA,IAIM,mBACA,yBACA;AANN;AAAA;AAGA;AACA,IAAM,oBAAoB;AAC1B,IAAM,0BAA0B;AAChC,IAAM,iBAAiB,MAAM;AAAA,MAC3B,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAAA,MAChC;AAAA,MACA,SAAS;AACP,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,QACP,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,QACP,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AACA,mBAAe,QAAQ;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}