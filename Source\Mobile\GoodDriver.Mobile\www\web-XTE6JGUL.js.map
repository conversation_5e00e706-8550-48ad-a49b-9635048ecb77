{"version": 3, "sources": ["node_modules/@capacitor-community/sqlite/dist/esm/web.js"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\nexport class CapacitorSQLiteWeb extends WebPlugin {\n  constructor() {\n    super(...arguments);\n    this.jeepSqliteElement = null;\n    this.isWebStoreOpen = false;\n  }\n  async initWebStore() {\n    await customElements.whenDefined('jeep-sqlite');\n    this.jeepSqliteElement = document.querySelector('jeep-sqlite');\n    this.ensureJeepSqliteIsAvailable();\n    this.jeepSqliteElement.addEventListener('jeepSqliteImportProgress', event => {\n      this.notifyListeners('sqliteImportProgressEvent', event.detail);\n    });\n    this.jeepSqliteElement.addEventListener('jeepSqliteExportProgress', event => {\n      this.notifyListeners('sqliteExportProgressEvent', event.detail);\n    });\n    this.jeepSqliteElement.addEventListener('jeepSqliteHTTPRequestEnded', event => {\n      this.notifyListeners('sqliteHTTPRequestEndedEvent', event.detail);\n    });\n    this.jeepSqliteElement.addEventListener('jeepSqlitePickDatabaseEnded', event => {\n      this.notifyListeners('sqlitePickDatabaseEndedEvent', event.detail);\n    });\n    this.jeepSqliteElement.addEventListener('jeepSqliteSaveDatabaseToDisk', event => {\n      this.notifyListeners('sqliteSaveDatabaseToDiskEvent', event.detail);\n    });\n    if (!this.isWebStoreOpen) {\n      this.isWebStoreOpen = await this.jeepSqliteElement.isStoreOpen();\n    }\n    return;\n  }\n  async saveToStore(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      await this.jeepSqliteElement.saveToStore(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async getFromLocalDiskToStore(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      await this.jeepSqliteElement.getFromLocalDiskToStore(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async saveToLocalDisk(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      await this.jeepSqliteElement.saveToLocalDisk(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async echo(options) {\n    this.ensureJeepSqliteIsAvailable();\n    const echoResult = await this.jeepSqliteElement.echo(options);\n    return echoResult;\n  }\n  async createConnection(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      await this.jeepSqliteElement.createConnection(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async open(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      await this.jeepSqliteElement.open(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async closeConnection(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      await this.jeepSqliteElement.closeConnection(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async getVersion(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      const versionResult = await this.jeepSqliteElement.getVersion(options);\n      return versionResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async checkConnectionsConsistency(options) {\n    this.ensureJeepSqliteIsAvailable();\n    try {\n      const consistencyResult = await this.jeepSqliteElement.checkConnectionsConsistency(options);\n      return consistencyResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async close(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      await this.jeepSqliteElement.close(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async beginTransaction(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      const changes = await this.jeepSqliteElement.beginTransaction(options);\n      return changes;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async commitTransaction(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      const changes = await this.jeepSqliteElement.commitTransaction(options);\n      return changes;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async rollbackTransaction(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      const changes = await this.jeepSqliteElement.rollbackTransaction(options);\n      return changes;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async isTransactionActive(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      const result = await this.jeepSqliteElement.isTransactionActive(options);\n      return result;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async getTableList(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      const tableListResult = await this.jeepSqliteElement.getTableList(options);\n      return tableListResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async execute(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      const executeResult = await this.jeepSqliteElement.execute(options);\n      return executeResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async executeSet(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      const executeResult = await this.jeepSqliteElement.executeSet(options);\n      return executeResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async run(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      const runResult = await this.jeepSqliteElement.run(options);\n      return runResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async query(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      const queryResult = await this.jeepSqliteElement.query(options);\n      return queryResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async isDBExists(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      const dbExistsResult = await this.jeepSqliteElement.isDBExists(options);\n      return dbExistsResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async isDBOpen(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      const isDBOpenResult = await this.jeepSqliteElement.isDBOpen(options);\n      return isDBOpenResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async isDatabase(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      const isDatabaseResult = await this.jeepSqliteElement.isDatabase(options);\n      return isDatabaseResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async isTableExists(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      const tableExistsResult = await this.jeepSqliteElement.isTableExists(options);\n      return tableExistsResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async deleteDatabase(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      await this.jeepSqliteElement.deleteDatabase(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async isJsonValid(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      const isJsonValidResult = await this.jeepSqliteElement.isJsonValid(options);\n      return isJsonValidResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async importFromJson(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      const importFromJsonResult = await this.jeepSqliteElement.importFromJson(options);\n      return importFromJsonResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async exportToJson(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      const exportToJsonResult = await this.jeepSqliteElement.exportToJson(options);\n      return exportToJsonResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async createSyncTable(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      const createSyncTableResult = await this.jeepSqliteElement.createSyncTable(options);\n      return createSyncTableResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async setSyncDate(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      await this.jeepSqliteElement.setSyncDate(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async getSyncDate(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      const getSyncDateResult = await this.jeepSqliteElement.getSyncDate(options);\n      return getSyncDateResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async deleteExportedRows(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      await this.jeepSqliteElement.deleteExportedRows(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async addUpgradeStatement(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      await this.jeepSqliteElement.addUpgradeStatement(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async copyFromAssets(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      await this.jeepSqliteElement.copyFromAssets(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async getFromHTTPRequest(options) {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      await this.jeepSqliteElement.getFromHTTPRequest(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async getDatabaseList() {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      const databaseListResult = await this.jeepSqliteElement.getDatabaseList();\n      return databaseListResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  /**\n   * Checks if the `jeep-sqlite` element is present in the DOM.\n   * If it's not in the DOM, this method throws an Error.\n   *\n   * Attention: This will always fail, if the `intWebStore()` method wasn't called before.\n   */\n  ensureJeepSqliteIsAvailable() {\n    if (this.jeepSqliteElement === null) {\n      throw new Error(`The jeep-sqlite element is not present in the DOM! Please check the @capacitor-community/sqlite documentation for instructions regarding the web platform.`);\n    }\n  }\n  ensureWebstoreIsOpen() {\n    if (!this.isWebStoreOpen) {\n      /**\n       * if (!this.isWebStoreOpen)\n        this.isWebStoreOpen = await this.jeepSqliteElement.isStoreOpen();\n       */\n      throw new Error('WebStore is not open yet. You have to call \"initWebStore()\" first.');\n    }\n  }\n  ////////////////////////////////////\n  ////// UNIMPLEMENTED METHODS\n  ////////////////////////////////////\n  async getUrl() {\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async getMigratableDbList(options) {\n    console.log('getMigratableDbList', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async addSQLiteSuffix(options) {\n    console.log('addSQLiteSuffix', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async deleteOldDatabases(options) {\n    console.log('deleteOldDatabases', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async moveDatabasesAndAddSuffix(options) {\n    console.log('moveDatabasesAndAddSuffix', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async isSecretStored() {\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async setEncryptionSecret(options) {\n    console.log('setEncryptionSecret', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async changeEncryptionSecret(options) {\n    console.log('changeEncryptionSecret', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async clearEncryptionSecret() {\n    console.log('clearEncryptionSecret');\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async checkEncryptionSecret(options) {\n    console.log('checkEncryptionPassPhrase', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async getNCDatabasePath(options) {\n    console.log('getNCDatabasePath', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async createNCConnection(options) {\n    console.log('createNCConnection', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async closeNCConnection(options) {\n    console.log('closeNCConnection', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async isNCDatabase(options) {\n    console.log('isNCDatabase', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async isDatabaseEncrypted(options) {\n    console.log('isDatabaseEncrypted', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async isInConfigEncryption() {\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async isInConfigBiometricAuth() {\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async loadExtension(options) {\n    console.log('loadExtension', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async enableLoadExtension(options) {\n    console.log('enableLoadExtension', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n}\n"], "mappings": ";;;;;;;;;;AAAA,IACa;AADb;AAAA;AAAA;AACO,IAAM,qBAAN,cAAiC,UAAU;AAAA,MAChD,cAAc;AACZ,cAAM,GAAG,SAAS;AAClB,aAAK,oBAAoB;AACzB,aAAK,iBAAiB;AAAA,MACxB;AAAA,MACM,eAAe;AAAA;AACnB,gBAAM,eAAe,YAAY,aAAa;AAC9C,eAAK,oBAAoB,SAAS,cAAc,aAAa;AAC7D,eAAK,4BAA4B;AACjC,eAAK,kBAAkB,iBAAiB,4BAA4B,WAAS;AAC3E,iBAAK,gBAAgB,6BAA6B,MAAM,MAAM;AAAA,UAChE,CAAC;AACD,eAAK,kBAAkB,iBAAiB,4BAA4B,WAAS;AAC3E,iBAAK,gBAAgB,6BAA6B,MAAM,MAAM;AAAA,UAChE,CAAC;AACD,eAAK,kBAAkB,iBAAiB,8BAA8B,WAAS;AAC7E,iBAAK,gBAAgB,+BAA+B,MAAM,MAAM;AAAA,UAClE,CAAC;AACD,eAAK,kBAAkB,iBAAiB,+BAA+B,WAAS;AAC9E,iBAAK,gBAAgB,gCAAgC,MAAM,MAAM;AAAA,UACnE,CAAC;AACD,eAAK,kBAAkB,iBAAiB,gCAAgC,WAAS;AAC/E,iBAAK,gBAAgB,iCAAiC,MAAM,MAAM;AAAA,UACpE,CAAC;AACD,cAAI,CAAC,KAAK,gBAAgB;AACxB,iBAAK,iBAAiB,MAAM,KAAK,kBAAkB,YAAY;AAAA,UACjE;AACA;AAAA,QACF;AAAA;AAAA,MACM,YAAY,SAAS;AAAA;AACzB,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,KAAK,kBAAkB,YAAY,OAAO;AAChD;AAAA,UACF,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,wBAAwB,SAAS;AAAA;AACrC,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,KAAK,kBAAkB,wBAAwB,OAAO;AAC5D;AAAA,UACF,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,gBAAgB,SAAS;AAAA;AAC7B,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,KAAK,kBAAkB,gBAAgB,OAAO;AACpD;AAAA,UACF,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,KAAK,SAAS;AAAA;AAClB,eAAK,4BAA4B;AACjC,gBAAM,aAAa,MAAM,KAAK,kBAAkB,KAAK,OAAO;AAC5D,iBAAO;AAAA,QACT;AAAA;AAAA,MACM,iBAAiB,SAAS;AAAA;AAC9B,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,KAAK,kBAAkB,iBAAiB,OAAO;AACrD;AAAA,UACF,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,KAAK,SAAS;AAAA;AAClB,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,KAAK,kBAAkB,KAAK,OAAO;AACzC;AAAA,UACF,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,gBAAgB,SAAS;AAAA;AAC7B,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,KAAK,kBAAkB,gBAAgB,OAAO;AACpD;AAAA,UACF,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,WAAW,SAAS;AAAA;AACxB,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,gBAAgB,MAAM,KAAK,kBAAkB,WAAW,OAAO;AACrE,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,4BAA4B,SAAS;AAAA;AACzC,eAAK,4BAA4B;AACjC,cAAI;AACF,kBAAM,oBAAoB,MAAM,KAAK,kBAAkB,4BAA4B,OAAO;AAC1F,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,MAAM,SAAS;AAAA;AACnB,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,KAAK,kBAAkB,MAAM,OAAO;AAC1C;AAAA,UACF,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,iBAAiB,SAAS;AAAA;AAC9B,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,UAAU,MAAM,KAAK,kBAAkB,iBAAiB,OAAO;AACrE,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,kBAAkB,SAAS;AAAA;AAC/B,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,UAAU,MAAM,KAAK,kBAAkB,kBAAkB,OAAO;AACtE,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,oBAAoB,SAAS;AAAA;AACjC,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,UAAU,MAAM,KAAK,kBAAkB,oBAAoB,OAAO;AACxE,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,oBAAoB,SAAS;AAAA;AACjC,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,SAAS,MAAM,KAAK,kBAAkB,oBAAoB,OAAO;AACvE,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,aAAa,SAAS;AAAA;AAC1B,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,kBAAkB,MAAM,KAAK,kBAAkB,aAAa,OAAO;AACzE,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,QAAQ,SAAS;AAAA;AACrB,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,gBAAgB,MAAM,KAAK,kBAAkB,QAAQ,OAAO;AAClE,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,WAAW,SAAS;AAAA;AACxB,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,gBAAgB,MAAM,KAAK,kBAAkB,WAAW,OAAO;AACrE,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,IAAI,SAAS;AAAA;AACjB,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,YAAY,MAAM,KAAK,kBAAkB,IAAI,OAAO;AAC1D,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,MAAM,SAAS;AAAA;AACnB,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,cAAc,MAAM,KAAK,kBAAkB,MAAM,OAAO;AAC9D,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,WAAW,SAAS;AAAA;AACxB,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,iBAAiB,MAAM,KAAK,kBAAkB,WAAW,OAAO;AACtE,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,SAAS,SAAS;AAAA;AACtB,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,iBAAiB,MAAM,KAAK,kBAAkB,SAAS,OAAO;AACpE,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,WAAW,SAAS;AAAA;AACxB,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,mBAAmB,MAAM,KAAK,kBAAkB,WAAW,OAAO;AACxE,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,cAAc,SAAS;AAAA;AAC3B,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,oBAAoB,MAAM,KAAK,kBAAkB,cAAc,OAAO;AAC5E,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,eAAe,SAAS;AAAA;AAC5B,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,KAAK,kBAAkB,eAAe,OAAO;AACnD;AAAA,UACF,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,YAAY,SAAS;AAAA;AACzB,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,oBAAoB,MAAM,KAAK,kBAAkB,YAAY,OAAO;AAC1E,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,eAAe,SAAS;AAAA;AAC5B,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,uBAAuB,MAAM,KAAK,kBAAkB,eAAe,OAAO;AAChF,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,aAAa,SAAS;AAAA;AAC1B,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,qBAAqB,MAAM,KAAK,kBAAkB,aAAa,OAAO;AAC5E,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,gBAAgB,SAAS;AAAA;AAC7B,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,wBAAwB,MAAM,KAAK,kBAAkB,gBAAgB,OAAO;AAClF,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,YAAY,SAAS;AAAA;AACzB,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,KAAK,kBAAkB,YAAY,OAAO;AAChD;AAAA,UACF,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,YAAY,SAAS;AAAA;AACzB,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,oBAAoB,MAAM,KAAK,kBAAkB,YAAY,OAAO;AAC1E,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,mBAAmB,SAAS;AAAA;AAChC,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,KAAK,kBAAkB,mBAAmB,OAAO;AACvD;AAAA,UACF,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,oBAAoB,SAAS;AAAA;AACjC,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,KAAK,kBAAkB,oBAAoB,OAAO;AACxD;AAAA,UACF,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,eAAe,SAAS;AAAA;AAC5B,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,KAAK,kBAAkB,eAAe,OAAO;AACnD;AAAA,UACF,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,mBAAmB,SAAS;AAAA;AAChC,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,KAAK,kBAAkB,mBAAmB,OAAO;AACvD;AAAA,UACF,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA,MACM,kBAAkB;AAAA;AACtB,eAAK,4BAA4B;AACjC,eAAK,qBAAqB;AAC1B,cAAI;AACF,kBAAM,qBAAqB,MAAM,KAAK,kBAAkB,gBAAgB;AACxE,mBAAO;AAAA,UACT,SAAS,KAAK;AACZ,kBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;AAAA,UAC1B;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,8BAA8B;AAC5B,YAAI,KAAK,sBAAsB,MAAM;AACnC,gBAAM,IAAI,MAAM,4JAA4J;AAAA,QAC9K;AAAA,MACF;AAAA,MACA,uBAAuB;AACrB,YAAI,CAAC,KAAK,gBAAgB;AAKxB,gBAAM,IAAI,MAAM,oEAAoE;AAAA,QACtF;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAIM,SAAS;AAAA;AACb,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,MACM,oBAAoB,SAAS;AAAA;AACjC,kBAAQ,IAAI,uBAAuB,OAAO;AAC1C,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,MACM,gBAAgB,SAAS;AAAA;AAC7B,kBAAQ,IAAI,mBAAmB,OAAO;AACtC,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,MACM,mBAAmB,SAAS;AAAA;AAChC,kBAAQ,IAAI,sBAAsB,OAAO;AACzC,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,MACM,0BAA0B,SAAS;AAAA;AACvC,kBAAQ,IAAI,6BAA6B,OAAO;AAChD,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,MACM,iBAAiB;AAAA;AACrB,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,MACM,oBAAoB,SAAS;AAAA;AACjC,kBAAQ,IAAI,uBAAuB,OAAO;AAC1C,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,MACM,uBAAuB,SAAS;AAAA;AACpC,kBAAQ,IAAI,0BAA0B,OAAO;AAC7C,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,MACM,wBAAwB;AAAA;AAC5B,kBAAQ,IAAI,uBAAuB;AACnC,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,MACM,sBAAsB,SAAS;AAAA;AACnC,kBAAQ,IAAI,6BAA6B,OAAO;AAChD,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,MACM,kBAAkB,SAAS;AAAA;AAC/B,kBAAQ,IAAI,qBAAqB,OAAO;AACxC,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,MACM,mBAAmB,SAAS;AAAA;AAChC,kBAAQ,IAAI,sBAAsB,OAAO;AACzC,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,MACM,kBAAkB,SAAS;AAAA;AAC/B,kBAAQ,IAAI,qBAAqB,OAAO;AACxC,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,MACM,aAAa,SAAS;AAAA;AAC1B,kBAAQ,IAAI,gBAAgB,OAAO;AACnC,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,MACM,oBAAoB,SAAS;AAAA;AACjC,kBAAQ,IAAI,uBAAuB,OAAO;AAC1C,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,MACM,uBAAuB;AAAA;AAC3B,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,MACM,0BAA0B;AAAA;AAC9B,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,MACM,cAAc,SAAS;AAAA;AAC3B,kBAAQ,IAAI,iBAAiB,OAAO;AACpC,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,MACM,oBAAoB,SAAS;AAAA;AACjC,kBAAQ,IAAI,uBAAuB,OAAO;AAC1C,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,IACF;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}