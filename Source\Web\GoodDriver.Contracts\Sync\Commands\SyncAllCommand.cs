﻿using GoodDriver.Contracts.Vehicles.Commands;
using Rogerio.Cqrs.Commands;
using System.ComponentModel.DataAnnotations;

namespace GoodDriver.Contracts.Sync.Commands
{
    public class SyncAllCommand : ICommand
    {
        [Required]
        public string UserId { get; set; }
        
        public SyncUserCommand User { get; set; }
        
        public List<VehicleSyncCommand> Vehicles { get; set; } = new List<VehicleSyncCommand>();
        
        public List<SyncJourneyCommand> Journeys { get; set; } = new List<SyncJourneyCommand>();
        
        public DateTime SyncDate { get; set; } = DateTime.Now;
    }
}
