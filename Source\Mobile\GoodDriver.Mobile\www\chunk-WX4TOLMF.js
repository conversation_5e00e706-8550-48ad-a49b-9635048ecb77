import {
  __esm
} from "./chunk-4W6HR7MY.js";

// node_modules/@ionic/core/dist/esm/index-a5d50daf.js
var win, doc;
var init_index_a5d50daf = __esm({
  "node_modules/@ionic/core/dist/esm/index-a5d50daf.js"() {
    "use strict";
    win = typeof window !== "undefined" ? window : void 0;
    doc = typeof document !== "undefined" ? document : void 0;
  }
});

export {
  win,
  doc,
  init_index_a5d50daf
};
/*! Bundled license information:

@ionic/core/dist/esm/index-a5d50daf.js:
  (*!
   * (C) Ionic http://ionicframework.com - MIT License
   *)
*/
//# sourceMappingURL=chunk-WX4TOLMF.js.map
