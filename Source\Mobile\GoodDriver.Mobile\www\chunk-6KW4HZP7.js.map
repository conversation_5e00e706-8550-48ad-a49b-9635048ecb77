{"version": 3, "sources": ["node_modules/@capacitor/synapse/dist/synapse.mjs", "node_modules/@capacitor/geolocation/dist/esm/definitions.js", "node_modules/@capacitor/geolocation/dist/esm/index.js", "node_modules/@capacitor/motion/dist/esm/definitions.js", "node_modules/@capacitor/motion/dist/esm/index.js", "src/app/core/services/journeyinfo.service.ts", "src/app/core/services/geocoding.service.ts", "src/app/core/services/journey-storage.service.ts"], "sourcesContent": ["function s(t) {\n  t.CapacitorUtils.Synapse = new Proxy({}, {\n    get(e, n) {\n      return new Proxy({}, {\n        get(w, o) {\n          return (c, p, r) => {\n            const i = t.Capacitor.Plugins[n];\n            if (i === void 0) {\n              r(new Error(`Capacitor plugin ${n} not found`));\n              return;\n            }\n            if (typeof i[o] != \"function\") {\n              r(new Error(`Method ${o} not found in Capacitor plugin ${n}`));\n              return;\n            }\n            (async () => {\n              try {\n                const a = await i[o](c);\n                p(a);\n              } catch (a) {\n                r(a);\n              }\n            })();\n          };\n        }\n      });\n    }\n  });\n}\nfunction u(t) {\n  t.CapacitorUtils.Synapse = new Proxy({}, {\n    get(e, n) {\n      return t.cordova.plugins[n];\n    }\n  });\n}\nfunction f(t = !1) {\n  typeof window > \"u\" || (window.CapacitorUtils = window.CapacitorUtils || {}, window.Capacitor !== void 0 && !t ? s(window) : window.cordova !== void 0 && u(window));\n}\nexport { f as exposeSynapse };", "export {};\n", "import { registerPlugin } from '@capacitor/core';\nimport { exposeSynapse } from '@capacitor/synapse';\nconst Geolocation = registerPlugin('Geolocation', {\n  web: () => import('./web').then(m => new m.GeolocationWeb())\n});\nexposeSynapse();\nexport * from './definitions';\nexport { Geolocation };\n", "export {};\n", "import { registerPlugin } from '@capacitor/core';\nconst Motion = registerPlugin('Motion', {\n  android: () => import('./web').then(m => new m.MotionWeb()),\n  ios: () => import('./web').then(m => new m.MotionWeb()),\n  web: () => import('./web').then(m => new m.MotionWeb())\n});\nexport * from './definitions';\nexport { Motion };\n", "import { Injectable } from '@angular/core';\r\nimport { Geolocation } from '@capacitor/geolocation';  // Se estiver usando Capacitor\r\nimport { IDataStorage } from 'src/app/core/storage/data-storage.interface';  // Seu serviço de armazenamento\r\nimport { JourneyInfo } from 'src/app/core/models/journeyInfo.model'  // Seu modelo JourneyInfo\r\nimport { DataStorageService } from './data-storage-service';\r\nimport { Motion } from '@capacitor/motion';\r\nimport { BehaviorSubject } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class JourneyInfoService {\r\n  private journeyId: string = '';\r\n  private trackingInterval: any = null;\r\n  private trackingStartTime: number = 0;\r\n  private totalTimeAboveSpeed = 0;\r\n  private storage: IDataStorage;\r\n\r\n  // Para detecção de freada brusca baseada em velocidade\r\n  private previousSpeed: number = 0;\r\n  private previousSpeedTime: number = 0;\r\n\r\n  // Observable para o status de rastreamento\r\n  private trackingActiveSubject = new BehaviorSubject<boolean>(false);\r\n  public trackingActive$ = this.trackingActiveSubject.asObservable();\r\n\r\n  constructor(private dataStorageService: DataStorageService) {\r\n    // Inicializa o serviço de dados (supondo que você tenha um serviço de dados como IndexedDBStorageService)\r\n    this.storage = this.dataStorageService;\r\n    \r\n  }\r\n\r\n  // Função para iniciar o rastreamento\r\n  startTracking(journeyId: string) {\r\n    this.trackingStartTime = Date.now();\r\n    this.totalTimeAboveSpeed = 0;  // Reseta o tempo acima da velocidade\r\n    this.journeyId = journeyId;\r\n\r\n    // Reseta valores para detecção de freada brusca\r\n    this.previousSpeed = 0;\r\n    this.previousSpeedTime = 0;\r\n\r\n    // Atualiza o estado de rastreamento\r\n    this.trackingActiveSubject.next(true);\r\n\r\n    this.trackingInterval = setInterval(async () => {\r\n      try {\r\n        console.log('Executando ciclo de rastreamento...');\r\n\r\n        const location = await this.getCurrentLocation();\r\n        if (location) {\r\n          console.log('Localização obtida:', location);\r\n\r\n          // Verifica eventos de direção com tratamento de erro individual\r\n          try {\r\n            console.log('Verificando freada brusca...');\r\n            const hardBreakDetected = await this.checkHardBreak(location);\r\n            console.log('Resultado checkHardBreak:', hardBreakDetected);\r\n          } catch (error) {\r\n            console.error('Erro em checkHardBreak:', error);\r\n          }\r\n\r\n          try {\r\n            console.log('Verificando alta velocidade...');\r\n            const highVelocityDetected = await this.checkHighVelocity(location);\r\n            console.log('Resultado checkHighVelocity:', highVelocityDetected);\r\n          } catch (error) {\r\n            console.error('Erro em checkHighVelocity:', error);\r\n          }\r\n\r\n          // Salva a localização periodicamente\r\n          try {\r\n            console.log('Salvando localização...');\r\n            await this.saveLocation(location);\r\n            console.log('Localização salva com sucesso');\r\n          } catch (error) {\r\n            console.error('Erro ao salvar localização:', error);\r\n          }\r\n        } else {\r\n          console.log('Não foi possível obter localização');\r\n        }\r\n      } catch (error) {\r\n        console.error('Erro no ciclo de rastreamento:', error);\r\n      }\r\n    }, 10000);  // A cada 10 segundos\r\n\r\n    console.log('Rastreamento iniciado para viagem:', journeyId);\r\n  }\r\n\r\n  // Função para parar o rastreamento\r\n  stopTracking(journeyId: string) {\r\n    if (this.trackingInterval) {\r\n      clearInterval(this.trackingInterval);  // Limpa o intervalo\r\n      this.trackingInterval = null;\r\n\r\n      // Atualiza o estado de rastreamento\r\n      this.trackingActiveSubject.next(false);\r\n\r\n      console.log('Rastreamento parado para viagem:', journeyId);\r\n    }\r\n  }\r\n\r\n  // Função para obter a localização atual\r\n  private async getCurrentLocation(): Promise<JourneyInfo | null> {\r\n    try {\r\n      const coordinates = await Geolocation.getCurrentPosition({\r\n        enableHighAccuracy: true,\r\n        timeout: 15000,\r\n        maximumAge: 5000  // Cache de 5 segundos para evitar leituras muito frequentes\r\n      });\r\n\r\n      // Verificar se a precisão é aceitável\r\n      const accuracy = coordinates.coords.accuracy;\r\n      if (accuracy && accuracy > 50) {\r\n        console.log(`Precisão GPS baixa: ${accuracy}m, tentando novamente...`);\r\n        // Tentar uma segunda vez com configurações mais rigorosas\r\n        const retryCoordinates = await Geolocation.getCurrentPosition({\r\n          enableHighAccuracy: true,\r\n          timeout: 20000,\r\n          maximumAge: 0  // Força nova leitura\r\n        });\r\n\r\n        if (retryCoordinates.coords.accuracy && retryCoordinates.coords.accuracy <= 50) {\r\n          coordinates.coords = retryCoordinates.coords;\r\n        }\r\n      }\r\n\r\n      const location: JourneyInfo = {\r\n        id: this.generateUniqueId(),\r\n        journeyId: this.journeyId,\r\n        latitude: coordinates.coords.latitude,\r\n        longitude: coordinates.coords.longitude,\r\n        timestamp: new Date().toISOString(),\r\n        // Adicionar informações de precisão se disponíveis\r\n        accuracy: coordinates.coords.accuracy || undefined,\r\n        altitude: coordinates.coords.altitude || undefined,\r\n        speed: coordinates.coords.speed || undefined\r\n      };\r\n\r\n      console.log(`Localização obtida: ${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)} (precisão: ${accuracy}m)`);\r\n\r\n      return location;\r\n    } catch (error) {\r\n      console.error('Erro ao obter a localização:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // Função para obter a velocidade atual (em km/h)\r\n  private async getCurrentSpeed(): Promise<number> {\r\n    try {\r\n      const position = await Geolocation.getCurrentPosition();  // Usando Capacitor\r\n      const speed = position.coords.speed ? position.coords.speed * 3.6 : 0;  // Converte de m/s para km/h ou assume 0 se for null\r\n      return speed;  // Retorna a velocidade em km/h\r\n    } catch (error) {\r\n      console.error('Erro ao obter a velocidade:', error);\r\n      return 0;  // Se der erro, assume velocidade 0\r\n    }\r\n  }\r\n\r\n  // Função para salvar a localização no banco de dados\r\n  private async saveLocation(location: JourneyInfo): Promise<void> {\r\n    try {\r\n      // Use the correct table name 'journeyInfo' instead of 'location'\r\n      await this.storage.insert('journeyInfo', location);\r\n      console.log('Localização salva:', location);\r\n    } catch (error) {\r\n      console.error('Erro ao salvar a localização:', error);\r\n    }\r\n  }\r\n\r\n  // Função para gerar um ID único para cada localização (pode ser melhorada)\r\n  private generateUniqueId(): string {\r\n    return Math.random().toString(36).substring(2, 15);\r\n  }\r\n\r\n  async checkHardBreak(location: JourneyInfo): Promise<boolean> {\r\n    try {\r\n      // Método 1: Detecção baseada em velocidade (mais confiável)\r\n      const currentSpeed = await this.getCurrentSpeed();\r\n      const currentTime = Date.now();\r\n\r\n      if (this.previousSpeed > 0 && this.previousSpeedTime > 0) {\r\n        const speedDifference = this.previousSpeed - currentSpeed; // Diferença de velocidade\r\n        const timeDifference = (currentTime - this.previousSpeedTime) / 1000; // Diferença de tempo em segundos\r\n\r\n        if (timeDifference > 0) {\r\n          const deceleration = speedDifference / timeDifference; // Desaceleração em km/h por segundo\r\n\r\n          console.log(`Velocidade anterior: ${this.previousSpeed} km/h, Atual: ${currentSpeed} km/h`);\r\n          console.log(`Desaceleração: ${deceleration} km/h/s`);\r\n\r\n          // Se a desaceleração for maior que 15 km/h por segundo, considera freada brusca\r\n          if (deceleration > 15) {\r\n            console.log('Freada brusca detectada por velocidade!');\r\n            location.occurrenceType = 'HardBreak';\r\n            await this.saveLocation(location);\r\n\r\n            // Atualiza valores para próxima verificação\r\n            this.previousSpeed = currentSpeed;\r\n            this.previousSpeedTime = currentTime;\r\n\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Atualiza valores para próxima verificação\r\n      this.previousSpeed = currentSpeed;\r\n      this.previousSpeedTime = currentTime;\r\n\r\n      // Método 2: Fallback usando sensor de movimento (com timeout)\r\n      return await this.checkHardBreakWithMotionSensor(location);\r\n\r\n    } catch (error) {\r\n      console.error('Erro em checkHardBreak:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  private async checkHardBreakWithMotionSensor(location: JourneyInfo): Promise<boolean> {\r\n    return new Promise(async (resolve) => {\r\n      let isResolved = false;\r\n      let listenerHandle: any = null;\r\n\r\n      try {\r\n        // Verifica se o Motion sensor está disponível\r\n        const isMotionAvailable = await this.isMotionSensorAvailable();\r\n        if (!isMotionAvailable) {\r\n          console.log('Sensor de movimento não disponível');\r\n          resolve(false);\r\n          return;\r\n        }\r\n\r\n        listenerHandle = await Motion.addListener('accel', (event) => {\r\n          const { acceleration } = event;\r\n\r\n          if (acceleration?.x && acceleration.x < -10) {\r\n            console.log('Freada brusca detectada por sensor de movimento!');\r\n\r\n            location.occurrenceType = 'HardBreak';\r\n            this.saveLocation(location);\r\n\r\n            // Cancela o listener para não continuar ouvindo\r\n            if (listenerHandle) {\r\n              listenerHandle.remove();\r\n            }\r\n\r\n            if (!isResolved) {\r\n              isResolved = true;\r\n              resolve(true); // Responde que detectou a freada\r\n            }\r\n          }\r\n        });\r\n\r\n        // Timeout para resolver a Promise após um tempo limite\r\n        setTimeout(() => {\r\n          if (!isResolved) {\r\n            isResolved = true;\r\n            if (listenerHandle) {\r\n              listenerHandle.remove(); // Remove o listener\r\n            }\r\n            resolve(false); // Não detectou freada brusca\r\n          }\r\n        }, 1000); // Aguarda apenas 1 segundo para o sensor de movimento\r\n\r\n      } catch (error) {\r\n        console.error('Erro ao configurar listener de aceleração:', error);\r\n        if (!isResolved) {\r\n          isResolved = true;\r\n          resolve(false);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  private async isMotionSensorAvailable(): Promise<boolean> {\r\n    try {\r\n      // Tenta criar um listener temporário para verificar se o sensor está disponível\r\n      const testListener = await Motion.addListener('accel', () => {});\r\n      if (testListener) {\r\n        testListener.remove();\r\n        return true;\r\n      }\r\n      return false;\r\n    } catch (error) {\r\n      console.error('Erro ao verificar disponibilidade do sensor de movimento:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n\r\n  async checkHighVelocity(location: JourneyInfo): Promise<boolean> {\r\n    const speedKmH = await this.getCurrentSpeed();\r\n\r\n     if (speedKmH > 120) { // Se a velocidade for maior que 120 km/h\r\n          console.log('Alta velocidade detectada!');\r\n          location.occurrenceType = 'Acceleration';  // Define o tipo de ocorrência como alta velocidade\r\n          this.saveLocation(location);\r\n          return true; // Retorna verdadeiro se a velocidade for maior que 120 km/h\r\n     }\r\n        return false; // Retorna falso se a velocidade for menor ou igual a 120 km/h\r\n  }\r\n}\r\n", "import { Injectable } from '@angular/core';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class GeocodingService {\n\n  constructor() { }\n\n  /**\n   * Converte coordenadas em endereço usando múltiplas estratégias\n   */\n  async reverseGeocode(lat: number, lon: number): Promise<string> {\n    try {\n      console.log(`Iniciando reverse geocoding para: ${lat}, ${lon}`);\n\n      // Método 1: API BigDataCloud (suporta CORS)\n      let address = await this.tryBigDataCloudAPI(lat, lon);\n      if (address && address !== 'Endereço não encontrado') {\n        return address;\n      }\n\n      // Método 2: API OpenCage (se tiver chave)\n      address = await this.tryOpenCageAPI(lat, lon);\n      if (address && address !== 'Endereço não encontrado') {\n        return address;\n      }\n\n      // Método 3: Proxy CORS para Nominatim\n      address = await this.tryNominatimWithProxy(lat, lon);\n      if (address && address !== 'Endereço não encontrado') {\n        return address;\n      }\n\n      // Método 4: Fallback para coordenadas formatadas\n      return this.formatCoordinatesAsAddress(lat, lon);\n      \n    } catch (error) {\n      console.error('Erro geral no reverse geocoding:', error);\n      return this.formatCoordinatesAsAddress(lat, lon);\n    }\n  }\n\n  /**\n   * API BigDataCloud - Gratuita e suporta CORS\n   */\n  private async tryBigDataCloudAPI(lat: number, lon: number): Promise<string> {\n    try {\n      const url = `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lon}&localityLanguage=pt`;\n      \n      console.log('Tentando BigDataCloud API...');\n      \n      const response = await fetch(url, {\n        method: 'GET',\n        headers: {\n          'Accept': 'application/json',\n        }\n      });\n      \n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      \n      const data = await response.json();\n      \n      if (data) {\n        const addressParts = [];\n        \n        // Monta endereço hierárquico\n        if (data.locality) addressParts.push(data.locality);\n        if (data.city && data.city !== data.locality) addressParts.push(data.city);\n        if (data.principalSubdivision) addressParts.push(data.principalSubdivision);\n        if (data.countryName) addressParts.push(data.countryName);\n        \n        if (addressParts.length > 0) {\n          const address = addressParts.join(', ');\n          console.log('Endereço obtido via BigDataCloud:', address);\n          return address;\n        }\n      }\n      \n      return 'Endereço não encontrado';\n    } catch (error) {\n      console.error('Erro na BigDataCloud API:', error);\n      return 'Endereço não encontrado';\n    }\n  }\n\n  /**\n   * API OpenCage - Requer chave API (opcional)\n   */\n  private async tryOpenCageAPI(lat: number, lon: number): Promise<string> {\n    try {\n      // Substitua 'YOUR_API_KEY' por uma chave real se disponível\n      const apiKey = 'YOUR_API_KEY';\n      \n      if (!apiKey || apiKey === 'YOUR_API_KEY') {\n        console.log('OpenCage API key não configurada, pulando...');\n        return 'Endereço não encontrado';\n      }\n      \n      const url = `https://api.opencagedata.com/geocode/v1/json?q=${lat}+${lon}&key=${apiKey}&language=pt&pretty=1`;\n      \n      console.log('Tentando OpenCage API...');\n      \n      const response = await fetch(url);\n      \n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      \n      const data = await response.json();\n      \n      if (data && data.results && data.results.length > 0) {\n        const result = data.results[0];\n        const address = result.formatted;\n        console.log('Endereço obtido via OpenCage:', address);\n        return address;\n      }\n      \n      return 'Endereço não encontrado';\n    } catch (error) {\n      console.error('Erro na OpenCage API:', error);\n      return 'Endereço não encontrado';\n    }\n  }\n\n  /**\n   * Nominatim com proxy CORS\n   */\n  private async tryNominatimWithProxy(lat: number, lon: number): Promise<string> {\n    try {\n      const proxyUrl = 'https://api.allorigins.win/get?url=';\n      const targetUrl = encodeURIComponent(\n        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lon}&addressdetails=1&accept-language=pt-BR,pt,en`\n      );\n      const url = proxyUrl + targetUrl;\n      \n      console.log('Tentando Nominatim com proxy...');\n      \n      const response = await fetch(url, {\n        method: 'GET',\n        headers: {\n          'Accept': 'application/json',\n        }\n      });\n      \n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      \n      const proxyData = await response.json();\n      \n      if (proxyData && proxyData.contents) {\n        const data = JSON.parse(proxyData.contents);\n        \n        if (data && data.display_name) {\n          console.log('Endereço obtido via Nominatim (proxy):', data.display_name);\n          return data.display_name;\n        }\n      }\n      \n      return 'Endereço não encontrado';\n    } catch (error) {\n      console.error('Erro no Nominatim com proxy:', error);\n      return 'Endereço não encontrado';\n    }\n  }\n\n  /**\n   * Formata coordenadas como endereço quando todas as APIs falham\n   */\n  private formatCoordinatesAsAddress(lat: number, lon: number): string {\n    const latDirection = lat >= 0 ? 'N' : 'S';\n    const lonDirection = lon >= 0 ? 'L' : 'O';\n    \n    const latFormatted = Math.abs(lat).toFixed(6);\n    const lonFormatted = Math.abs(lon).toFixed(6);\n    \n    return `${latFormatted}°${latDirection}, ${lonFormatted}°${lonDirection}`;\n  }\n\n  /**\n   * Verifica se uma string é um endereço válido ou apenas coordenadas\n   */\n  isValidAddress(address: string): boolean {\n    // Se contém apenas números, pontos, vírgulas e direções cardeais, é coordenada\n    const coordinatePattern = /^[\\d\\.\\-°NSLO,\\s]+$/;\n    return !coordinatePattern.test(address);\n  }\n\n  /**\n   * Obtém endereço com cache simples para evitar requisições repetidas\n   */\n  private addressCache = new Map<string, string>();\n\n  async getReverseGeocodeWithCache(lat: number, lon: number): Promise<string> {\n    const key = `${lat.toFixed(6)},${lon.toFixed(6)}`;\n    \n    if (this.addressCache.has(key)) {\n      console.log('Endereço obtido do cache:', this.addressCache.get(key));\n      return this.addressCache.get(key)!;\n    }\n    \n    const address = await this.reverseGeocode(lat, lon);\n    this.addressCache.set(key, address);\n    \n    return address;\n  }\n\n  /**\n   * Limpa o cache de endereços\n   */\n  clearCache(): void {\n    this.addressCache.clear();\n    console.log('Cache de endereços limpo');\n  }\n}\n", "import { Injectable } from '@angular/core';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport { SessionService } from './session.service';\r\nimport { Journey } from '../models/journey.model';\r\nimport { DataStorageService } from './data-storage-service';\r\nimport { Platform } from '@ionic/angular'; // Para detecção da plataforma (dispositivo ou navegador)\r\nimport { JourneyInfo } from '../models/journeyInfo.model';\r\nimport { JourneyInfoService } from './journeyinfo.service';\r\nimport { VehicleService } from './vehicle.service';\r\nimport { NetworkService } from './network.service';\r\nimport { SyncStatus } from '../models/sync.model';\r\nimport { JourneySyncRequestDto } from '../dtos/journey/journeySyncRequestDto';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { ApiService } from './api.service';\r\nimport { GeocodingService } from './geocoding.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class JourneyStorageService\r\n{\r\n    constructor(\r\n      private sessionService: SessionService,\r\n      private dataStorageService: DataStorageService,\r\n      private platform: Platform,\r\n      private journeyInfoService: JourneyInfoService,\r\n      private vehicleService: VehicleService,\r\n      private networkService: NetworkService,\r\n      private http: HttpClient,\r\n      private apiService: ApiService,\r\n      private geocodingService: GeocodingService\r\n    ) {\r\n      this.isNative = this.platform.is('capacitor') || this.platform.is('cordova');\r\n    }\r\n\r\n  private readonly tableName = 'journeys';\r\n  private db: any; // Referência ao banco de dados (SQLite ou IndexedDB)\r\n  private isNative: boolean; // Variável para verificar se é dispositivo físico\r\n\r\n  async init()\r\n  {\r\n    await this.dataStorageService.init();\r\n  }\r\n\r\n  /**\r\n   * Starts a new journey if the user has at least one vehicle\r\n   * @returns The journey ID if successful, null if no vehicles exist\r\n   */\r\n  async startJourney(): Promise<string | null> {\r\n    const userId = await this.sessionService.getUserId();\r\n\r\n    // Check if the user has any vehicles\r\n    const hasVehicles = await this.vehicleService.hasVehicles(userId);\r\n    if (!hasVehicles) {\r\n      console.error('Cannot start journey: No vehicles registered for this user');\r\n      return null;\r\n    }\r\n\r\n    // Get the primary vehicle\r\n    const primaryVehicle = await this.vehicleService.getPrimaryVehicle(userId);\r\n    if (!primaryVehicle) {\r\n      console.error('Cannot start journey: No primary vehicle set');\r\n      return null;\r\n    }\r\n\r\n    const id = uuidv4();\r\n    const startDate = new Date().toISOString();\r\n\r\n    // Determine sync status based on network connectivity\r\n    const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingCreate : SyncStatus.PendingCreate;\r\n\r\n    // Create the journey with the primary vehicle ID\r\n    await this.dataStorageService.insert(this.tableName, {\r\n      id: id,\r\n      startDate: startDate,\r\n      userId: userId,\r\n      vehicleId: primaryVehicle.id,\r\n      syncStatus: syncStatus,\r\n      lastSyncDate: null\r\n    });\r\n\r\n    // Start tracking the journey\r\n    this.journeyInfoService.startTracking(id);\r\n    return id;\r\n  }\r\n\r\n  async endJourney(journeyId: string) {\r\n    this.journeyInfoService.stopTracking(journeyId);\r\n    const endDate = new Date().toISOString();\r\n\r\n    console.log(`Finalizando viagem ${journeyId}...`);\r\n\r\n    // Calcular a distância total da viagem com filtros de precisão\r\n    const result = await this.dataStorageService.select('journeyInfo', `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);\r\n\r\n    // Handle the result properly for both IndexedDB and SQLite\r\n    let infosJourney: JourneyInfo[] = [];\r\n    if (Array.isArray(result)) {\r\n      infosJourney = result;\r\n    } else if (result) {\r\n      try {\r\n        infosJourney = Array.isArray(result) ? result : [];\r\n      } catch (error) {\r\n        console.error('Error converting journey info to array:', error);\r\n      }\r\n    }\r\n\r\n    console.log(`Total de pontos coletados: ${infosJourney.length}`);\r\n\r\n    // Buscar endereços para pontos que não possuem endereço\r\n    await this.loadAndSaveAddressesForJourney(infosJourney);\r\n\r\n    let totalDistance = 0;\r\n\r\n    if (infosJourney && infosJourney.length > 1) {\r\n      // Aplicar filtros para melhorar a precisão\r\n      const filteredLocations = this.filterLocationsByAccuracy(infosJourney);\r\n      console.log(`Pontos após filtragem: ${filteredLocations.length}`);\r\n\r\n      // Calcular distância usando múltiplos métodos\r\n      totalDistance = this.calculateJourneyDistance(filteredLocations);\r\n\r\n      console.log(`Distância calculada: ${(totalDistance / 1000).toFixed(3)} km`);\r\n    } else {\r\n      console.log('Viagem sem pontos suficientes para calcular distância');\r\n    }\r\n\r\n    // Determine sync status based on network connectivity\r\n    const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingUpdate : SyncStatus.PendingUpdate;\r\n\r\n    // Update the journey with the calculated distance (convert to km)\r\n    await this.dataStorageService.update(\r\n      this.tableName,\r\n      {\r\n        endDate: endDate,\r\n        distance: totalDistance / 1000, // Convert meters to kilometers\r\n        syncStatus: syncStatus,\r\n        lastSyncDate: null\r\n      },\r\n      `id = '${journeyId}'`\r\n    );\r\n\r\n    console.log(`Viagem ${journeyId} finalizada com ${(totalDistance / 1000).toFixed(3)} km`);\r\n  }\r\n\r\n  async addLocation(journeyId: string, latitude: number, longitude: number) {\r\n    const id = uuidv4();\r\n    const timestamp = new Date().toISOString();\r\n\r\n    // Determine sync status based on network connectivity\r\n    const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingCreate : SyncStatus.PendingCreate;\r\n\r\n    // Use the correct table name 'journeyInfo' instead of 'location'\r\n    await this.dataStorageService.insert('journeyInfo', {\r\n      id: id,\r\n      journeyId: journeyId,\r\n      latitude: latitude,\r\n      longitude: longitude,\r\n      timestamp: timestamp,\r\n      syncStatus: syncStatus,\r\n      lastSyncDate: null\r\n    });\r\n  }\r\n\r\n  async closeConnection() {\r\n    // Fechar a conexão com o banco de dados se estiver usando SQLite\r\n    if (this.db) {\r\n      this.db.close();\r\n      this.db = null;\r\n     }\r\n  }\r\n\r\n  async getAllJourneys(): Promise<Journey[]>\r\n  {\r\n    let result = await this.dataStorageService.select(this.tableName, 'ORDER BY startDate DESC');\r\n\r\n    if(!result || result.length === 0)\r\n      return [];\r\n\r\n    for (let i = 0; i < result.length; i++) {\r\n      const journeyId = result[i].id;\r\n      // Use the correct table name 'journeyInfo' instead of 'location'\r\n      const infosJourney = await this.dataStorageService.select('journeyInfo', `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);\r\n\r\n      // Ensure we have an array of journey info objects\r\n      if (Array.isArray(infosJourney)) {\r\n        result[i].infos = infosJourney.filter((info: JourneyInfo) => info.journeyId === journeyId);\r\n      } else {\r\n        result[i].infos = [];\r\n      }\r\n    }\r\n\r\n    const journeys: Journey[] = result.map((data: any) => {\r\n      return {\r\n        id: data.id,\r\n        startDate: data.startDate,\r\n        endDate: data.endDate,\r\n        distance: data.distance,\r\n        userId: data.userId,\r\n        vehicleId: data.vehicleId,\r\n        infosJourney: data.infos,\r\n        syncStatus: data.syncStatus || SyncStatus.Synced,\r\n        lastSyncDate: data.lastSyncDate ? new Date(data.lastSyncDate) : undefined\r\n      };\r\n    });\r\n    return journeys;\r\n  }\r\n\r\n  async getAllJourneysByUser(userId: string): Promise<Journey[]>\r\n  {\r\n    // 1. Buscar dados do usuário no Local Storage\r\n    let result = await this.dataStorageService.select(this.tableName, ' ORDER BY startDate DESC');\r\n\r\n    // Filter results by userId\r\n    if (Array.isArray(result)) {\r\n      result = result.filter((data: any) => data.userId === userId);\r\n    } else {\r\n      return [];\r\n    }\r\n\r\n    if(!result || result.length === 0)\r\n      return [];\r\n\r\n    for (let i = 0; i < result.length; i++) {\r\n      const journeyId = result[i].id;\r\n      // Use the correct table name 'journeyInfo' instead of 'location'\r\n      const infosJourney = await this.dataStorageService.select('journeyInfo', `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);\r\n\r\n      // Ensure we have an array of journey info objects\r\n      if (Array.isArray(infosJourney)) {\r\n        result[i].infos = infosJourney.filter((info: JourneyInfo) => info.journeyId === journeyId);\r\n      } else {\r\n        result[i].infos = [];\r\n      }\r\n    }\r\n\r\n    const journeys: Journey[] = result.map((data: any) => {\r\n      return {\r\n        id: data.id,\r\n        startDate: data.startDate,\r\n        endDate: data.endDate,\r\n        distance: data.distance,\r\n        userId: data.userId,\r\n        vehicleId: data.vehicleId,\r\n        infosJourney: data.infos,\r\n        syncStatus: data.syncStatus || SyncStatus.Synced,\r\n        lastSyncDate: data.lastSyncDate ? new Date(data.lastSyncDate) : undefined\r\n      };\r\n    });\r\n    return journeys;\r\n  }\r\n\r\n  /**\r\n   * Filtra localizações por precisão e remove pontos muito próximos\r\n   */\r\n  private filterLocationsByAccuracy(locations: JourneyInfo[]): JourneyInfo[] {\r\n    if (locations.length <= 2) return locations;\r\n\r\n    const filtered: JourneyInfo[] = [];\r\n    const MIN_DISTANCE_THRESHOLD = 10; // Mínimo de 10 metros entre pontos\r\n    const MAX_SPEED_THRESHOLD = 200; // Máximo de 200 km/h (velocidade impossível para carros normais)\r\n\r\n    // Sempre incluir o primeiro ponto\r\n    filtered.push(locations[0]);\r\n\r\n    for (let i = 1; i < locations.length; i++) {\r\n      const current = locations[i];\r\n      const previous = filtered[filtered.length - 1];\r\n\r\n      // Calcular distância entre pontos\r\n      const distance = this.calculateDistance(\r\n        previous.latitude, previous.longitude,\r\n        current.latitude, current.longitude\r\n      );\r\n\r\n      // Calcular tempo entre pontos (em horas)\r\n      const timeDiff = (new Date(current.timestamp).getTime() - new Date(previous.timestamp).getTime()) / (1000 * 60 * 60);\r\n\r\n      // Calcular velocidade (km/h)\r\n      const speed = timeDiff > 0 ? (distance / 1000) / timeDiff : 0;\r\n\r\n      // Filtros de qualidade:\r\n      // 1. Distância mínima entre pontos (evita ruído GPS)\r\n      // 2. Velocidade máxima realística\r\n      // 3. Não incluir pontos com coordenadas inválidas\r\n      // 4. Precisão GPS aceitável (se disponível)\r\n      const hasGoodAccuracy = !current.accuracy || current.accuracy <= 30; // 30m de precisão máxima\r\n\r\n      if (distance >= MIN_DISTANCE_THRESHOLD &&\r\n          speed <= MAX_SPEED_THRESHOLD &&\r\n          this.isValidCoordinate(current.latitude, current.longitude) &&\r\n          hasGoodAccuracy) {\r\n        filtered.push(current);\r\n      } else {\r\n        const reason = [];\r\n        if (distance < MIN_DISTANCE_THRESHOLD) reason.push(`dist=${distance.toFixed(1)}m`);\r\n        if (speed > MAX_SPEED_THRESHOLD) reason.push(`speed=${speed.toFixed(1)}km/h`);\r\n        if (!this.isValidCoordinate(current.latitude, current.longitude)) reason.push('coords_invalid');\r\n        if (!hasGoodAccuracy) reason.push(`accuracy=${current.accuracy}m`);\r\n        console.log(`Ponto filtrado: ${reason.join(', ')}`);\r\n      }\r\n    }\r\n\r\n    // Sempre incluir o último ponto se não foi incluído\r\n    const lastOriginal = locations[locations.length - 1];\r\n    const lastFiltered = filtered[filtered.length - 1];\r\n    if (lastOriginal.timestamp !== lastFiltered.timestamp) {\r\n      filtered.push(lastOriginal);\r\n    }\r\n\r\n    return filtered;\r\n  }\r\n\r\n  /**\r\n   * Verifica se as coordenadas são válidas\r\n   */\r\n  private isValidCoordinate(lat: number, lon: number): boolean {\r\n    return lat >= -90 && lat <= 90 && lon >= -180 && lon <= 180 && lat !== 0 && lon !== 0;\r\n  }\r\n\r\n  /**\r\n   * Calcula a distância total da viagem usando múltiplos métodos\r\n   */\r\n  private calculateJourneyDistance(locations: JourneyInfo[]): number {\r\n    if (locations.length < 2) return 0;\r\n\r\n    // Método 1: Distância direta (linha reta do início ao fim)\r\n    const directDistance = this.calculateDistance(\r\n      locations[0].latitude, locations[0].longitude,\r\n      locations[locations.length - 1].latitude, locations[locations.length - 1].longitude\r\n    );\r\n\r\n    // Método 2: Distância cumulativa (soma de todos os segmentos)\r\n    let cumulativeDistance = 0;\r\n    for (let i = 0; i < locations.length - 1; i++) {\r\n      const segmentDistance = this.calculateDistance(\r\n        locations[i].latitude, locations[i].longitude,\r\n        locations[i + 1].latitude, locations[i + 1].longitude\r\n      );\r\n      cumulativeDistance += segmentDistance;\r\n    }\r\n\r\n    // Método 3: Distância suavizada (remove outliers)\r\n    const smoothedDistance = this.calculateSmoothedDistance(locations);\r\n\r\n    console.log(`Distâncias calculadas:`);\r\n    console.log(`- Direta: ${(directDistance / 1000).toFixed(3)} km`);\r\n    console.log(`- Cumulativa: ${(cumulativeDistance / 1000).toFixed(3)} km`);\r\n    console.log(`- Suavizada: ${(smoothedDistance / 1000).toFixed(3)} km`);\r\n\r\n    // Usar a distância suavizada como resultado final\r\n    // Se a diferença entre direta e cumulativa for muito grande, usar a direta\r\n    const ratio = cumulativeDistance / directDistance;\r\n\r\n    if (ratio > 3.0) {\r\n      console.log(`Ratio muito alto (${ratio.toFixed(2)}), usando distância direta`);\r\n      return directDistance;\r\n    } else {\r\n      console.log(`Usando distância suavizada`);\r\n      return smoothedDistance;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calcula distância suavizada removendo segmentos com velocidades irreais\r\n   */\r\n  private calculateSmoothedDistance(locations: JourneyInfo[]): number {\r\n    if (locations.length < 2) return 0;\r\n\r\n    let totalDistance = 0;\r\n    const MAX_SEGMENT_SPEED = 150; // km/h\r\n\r\n    for (let i = 0; i < locations.length - 1; i++) {\r\n      const current = locations[i];\r\n      const next = locations[i + 1];\r\n\r\n      const segmentDistance = this.calculateDistance(\r\n        current.latitude, current.longitude,\r\n        next.latitude, next.longitude\r\n      );\r\n\r\n      // Calcular tempo entre pontos (em horas)\r\n      const timeDiff = (new Date(next.timestamp).getTime() - new Date(current.timestamp).getTime()) / (1000 * 60 * 60);\r\n      const speed = timeDiff > 0 ? (segmentDistance / 1000) / timeDiff : 0;\r\n\r\n      // Incluir apenas segmentos com velocidade realística\r\n      if (speed <= MAX_SEGMENT_SPEED) {\r\n        totalDistance += segmentDistance;\r\n      } else {\r\n        console.log(`Segmento ignorado: ${speed.toFixed(1)} km/h`);\r\n      }\r\n    }\r\n\r\n    return totalDistance;\r\n  }\r\n\r\n  /**\r\n   * Calcula distância entre dois pontos usando fórmula de Haversine\r\n   */\r\n  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {\r\n    const toRad = (value: number) => (value * Math.PI) / 180;\r\n\r\n    const R = 6371000; // Raio da Terra em metros\r\n    const dLat = toRad(lat2 - lat1);\r\n    const dLon = toRad(lon2 - lon1);\r\n\r\n    const a =\r\n      Math.sin(dLat / 2) * Math.sin(dLat / 2) +\r\n      Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) *\r\n      Math.sin(dLon / 2) * Math.sin(dLon / 2);\r\n\r\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\r\n\r\n    return R * c; // Distância em metros\r\n  }\r\n\r\n  /**\r\n   * Carrega e salva endereços para pontos da viagem que não possuem endereço\r\n   */\r\n  private async loadAndSaveAddressesForJourney(infosJourney: JourneyInfo[]): Promise<void> {\r\n    if (!infosJourney || infosJourney.length === 0) return;\r\n\r\n    console.log('Carregando endereços para pontos da viagem...');\r\n\r\n    // Identificar pontos que precisam de endereço\r\n    const pointsNeedingAddress = infosJourney.filter(info => !info.address);\r\n\r\n    if (pointsNeedingAddress.length === 0) {\r\n      console.log('Todos os pontos já possuem endereço');\r\n      return;\r\n    }\r\n\r\n    console.log(`${pointsNeedingAddress.length} pontos precisam de endereço`);\r\n\r\n    // Priorizar início e fim da viagem\r\n    const priorityPoints: { info: JourneyInfo, index: number, priority: string }[] = [];\r\n    const regularPoints: JourneyInfo[] = [];\r\n\r\n    pointsNeedingAddress.forEach((info) => {\r\n      const actualIndex = infosJourney.indexOf(info);\r\n\r\n      if (actualIndex === 0) {\r\n        priorityPoints.push({ info, index: actualIndex, priority: 'start' });\r\n      } else if (actualIndex === infosJourney.length - 1 && infosJourney.length > 1) {\r\n        priorityPoints.push({ info, index: actualIndex, priority: 'end' });\r\n      } else {\r\n        regularPoints.push(info);\r\n      }\r\n    });\r\n\r\n    // Carregar endereços prioritários primeiro (início e fim)\r\n    if (priorityPoints.length > 0) {\r\n      console.log(`Carregando endereços prioritários (${priorityPoints.length} pontos)...`);\r\n\r\n      await Promise.all(\r\n        priorityPoints.map(async ({ info, priority }) => {\r\n          try {\r\n            console.log(`Carregando endereço para ${priority === 'start' ? 'início' : 'fim'} da viagem...`);\r\n            const address = await this.geocodingService.getReverseGeocodeWithCache(info.latitude, info.longitude);\r\n\r\n            // Atualizar o objeto na memória\r\n            info.address = address;\r\n\r\n            // Salvar no banco de dados\r\n            await this.updateJourneyInfoAddress(info.id, address);\r\n\r\n            console.log(`Endereço do ${priority === 'start' ? 'início' : 'fim'} salvo: ${address}`);\r\n          } catch (error) {\r\n            console.error(`Erro ao carregar endereço para ${priority}:`, error);\r\n            info.address = 'Endereço não disponível';\r\n            await this.updateJourneyInfoAddress(info.id, 'Endereço não disponível');\r\n          }\r\n        })\r\n      );\r\n\r\n      // Pequena pausa antes de carregar endereços intermediários\r\n      await new Promise(resolve => setTimeout(resolve, 500));\r\n    }\r\n\r\n    // Carregar endereços intermediários em lotes menores\r\n    if (regularPoints.length > 0) {\r\n      console.log(`Carregando endereços intermediários (${regularPoints.length} pontos)...`);\r\n\r\n      const batchSize = 3; // Lotes pequenos para não sobrecarregar a API\r\n\r\n      for (let i = 0; i < regularPoints.length; i += batchSize) {\r\n        const batch = regularPoints.slice(i, i + batchSize);\r\n\r\n        await Promise.all(\r\n          batch.map(async (info) => {\r\n            try {\r\n              const address = await this.geocodingService.getReverseGeocodeWithCache(info.latitude, info.longitude);\r\n\r\n              // Atualizar o objeto na memória\r\n              info.address = address;\r\n\r\n              // Salvar no banco de dados\r\n              await this.updateJourneyInfoAddress(info.id, address);\r\n\r\n              console.log(`Endereço intermediário salvo: ${address}`);\r\n            } catch (error) {\r\n              console.error('Erro ao carregar endereço intermediário:', error);\r\n              info.address = 'Endereço não disponível';\r\n              await this.updateJourneyInfoAddress(info.id, 'Endereço não disponível');\r\n            }\r\n          })\r\n        );\r\n\r\n        // Pausa entre lotes para ser respeitoso com a API\r\n        if (i + batchSize < regularPoints.length) {\r\n          await new Promise(resolve => setTimeout(resolve, 1000));\r\n        }\r\n      }\r\n    }\r\n\r\n    console.log('Carregamento de endereços concluído');\r\n  }\r\n\r\n  /**\r\n   * Atualiza apenas o campo address de um JourneyInfo\r\n   */\r\n  private async updateJourneyInfoAddress(journeyInfoId: string, address: string): Promise<void> {\r\n    try {\r\n      await this.dataStorageService.update(\r\n        'journeyInfo',\r\n        { address: address },\r\n        `id = '${journeyInfoId}'`\r\n      );\r\n    } catch (error) {\r\n      console.error(`Erro ao atualizar endereço para journeyInfo ${journeyInfoId}:`, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Updates the sync status of a journey\r\n   * @param journey The journey to update\r\n   * @returns True if the update was successful\r\n   */\r\n  async updateJourneySync(journey: Journey): Promise<boolean> {\r\n    try {\r\n      // Convert journey to database format\r\n      const journeyForDb = {\r\n        ...journey,\r\n        lastSyncDate: journey.lastSyncDate ? journey.lastSyncDate.toISOString() : null\r\n      };\r\n\r\n      // Remove infosJourney property as it's not stored in the journey table\r\n      if (journeyForDb.infosJourney) {\r\n        delete journeyForDb.infosJourney;\r\n      }\r\n\r\n      await this.dataStorageService.update(\r\n        this.tableName,\r\n        journeyForDb,\r\n        `id = '${journey.id}'`\r\n      );\r\n\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Error updating journey sync status:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gets journeys that need to be synchronized\r\n   * @param userId The user ID\r\n   * @returns Array of journeys that need to be synchronized\r\n   */\r\n  async getPendingSyncJourneys(userId: string): Promise<Journey[]> {\r\n    const journeys = await this.getAllJourneysByUser(userId);\r\n    return journeys.filter(j =>\r\n      j.syncStatus === SyncStatus.PendingCreate ||\r\n      j.syncStatus === SyncStatus.PendingUpdate ||\r\n      j.syncStatus === SyncStatus.PendingDelete\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Sends a journey to the server for synchronization\r\n   * @param journey The journey to send\r\n   * @returns True if the journey was sent successfully\r\n   */\r\n  async sendJourneyToSync(journey: JourneySyncRequestDto): Promise<boolean> {\r\n    try {\r\n      const url = this.apiService.getUrl('journey/SyncJourney');\r\n      await firstValueFrom(this.http.post(url, journey));\r\n      return true; // Se chegou até aqui, a requisição foi bem-sucedida\r\n    } catch (error) {\r\n      console.error('Error sending journey to sync:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,EAAE,GAAG;AACZ,IAAE,eAAe,UAAU,IAAI,MAAM,CAAC,GAAG;AAAA,IACvC,IAAI,GAAG,GAAG;AACR,aAAO,IAAI,MAAM,CAAC,GAAG;AAAA,QACnB,IAAI,GAAG,GAAG;AACR,iBAAO,CAAC,GAAG,GAAG,MAAM;AAClB,kBAAM,IAAI,EAAE,UAAU,QAAQ,CAAC;AAC/B,gBAAI,MAAM,QAAQ;AAChB,gBAAE,IAAI,MAAM,oBAAoB,CAAC,YAAY,CAAC;AAC9C;AAAA,YACF;AACA,gBAAI,OAAO,EAAE,CAAC,KAAK,YAAY;AAC7B,gBAAE,IAAI,MAAM,UAAU,CAAC,kCAAkC,CAAC,EAAE,CAAC;AAC7D;AAAA,YACF;AACA,aAAC,MAAY;AACX,kBAAI;AACF,sBAAM,IAAI,MAAM,EAAE,CAAC,EAAE,CAAC;AACtB,kBAAE,CAAC;AAAA,cACL,SAAS,GAAG;AACV,kBAAE,CAAC;AAAA,cACL;AAAA,YACF,IAAG;AAAA,UACL;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;AACA,SAAS,EAAE,GAAG;AACZ,IAAE,eAAe,UAAU,IAAI,MAAM,CAAC,GAAG;AAAA,IACvC,IAAI,GAAG,GAAG;AACR,aAAO,EAAE,QAAQ,QAAQ,CAAC;AAAA,IAC5B;AAAA,EACF,CAAC;AACH;AACA,SAAS,EAAE,IAAI,OAAI;AACjB,SAAO,SAAS,QAAQ,OAAO,iBAAiB,OAAO,kBAAkB,CAAC,GAAG,OAAO,cAAc,UAAU,CAAC,IAAI,EAAE,MAAM,IAAI,OAAO,YAAY,UAAU,EAAE,MAAM;AACpK;AAtCA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAEM;AAFN,IAAAA,YAAA;AAAA;AAAA;AAAA;AACA;AAKA;AAJA,IAAM,cAAc,eAAe,eAAe;AAAA,MAChD,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,eAAe,CAAC;AAAA,IAC7D,CAAC;AACD,MAAc;AAAA;AAAA;;;ACLd,IAAAC,oBAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IACM;AADN,IAAAC,YAAA;AAAA;AAAA;AAAA;AAMA,IAAAC;AALA,IAAM,SAAS,eAAe,UAAU;AAAA,MACtC,SAAS,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,UAAU,CAAC;AAAA,MAC1D,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,UAAU,CAAC;AAAA,MACtD,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,UAAU,CAAC;AAAA,IACxD,CAAC;AAAA;AAAA;;;ACLD,IAWa;AAXb;;;;AACA,IAAAC;AAIA,IAAAA;AACA;;;AAKM,IAAO,sBAAP,MAAO,oBAAkB;MAe7B,YAAoB,oBAAsC;AAAtC,aAAA,qBAAA;AAdZ,aAAA,YAAoB;AACpB,aAAA,mBAAwB;AACxB,aAAA,oBAA4B;AAC5B,aAAA,sBAAsB;AAItB,aAAA,gBAAwB;AACxB,aAAA,oBAA4B;AAG5B,aAAA,wBAAwB,IAAI,gBAAyB,KAAK;AAC3D,aAAA,kBAAkB,KAAK,sBAAsB,aAAY;AAI9D,aAAK,UAAU,KAAK;MAEtB;;MAGA,cAAc,WAAiB;AAC7B,aAAK,oBAAoB,KAAK,IAAG;AACjC,aAAK,sBAAsB;AAC3B,aAAK,YAAY;AAGjB,aAAK,gBAAgB;AACrB,aAAK,oBAAoB;AAGzB,aAAK,sBAAsB,KAAK,IAAI;AAEpC,aAAK,mBAAmB,YAAY,MAAW;AAC7C,cAAI;AACF,oBAAQ,IAAI,qCAAqC;AAEjD,kBAAM,WAAW,MAAM,KAAK,mBAAkB;AAC9C,gBAAI,UAAU;AACZ,sBAAQ,IAAI,6BAAuB,QAAQ;AAG3C,kBAAI;AACF,wBAAQ,IAAI,8BAA8B;AAC1C,sBAAM,oBAAoB,MAAM,KAAK,eAAe,QAAQ;AAC5D,wBAAQ,IAAI,6BAA6B,iBAAiB;cAC5D,SAAS,OAAO;AACd,wBAAQ,MAAM,2BAA2B,KAAK;cAChD;AAEA,kBAAI;AACF,wBAAQ,IAAI,gCAAgC;AAC5C,sBAAM,uBAAuB,MAAM,KAAK,kBAAkB,QAAQ;AAClE,wBAAQ,IAAI,gCAAgC,oBAAoB;cAClE,SAAS,OAAO;AACd,wBAAQ,MAAM,8BAA8B,KAAK;cACnD;AAGA,kBAAI;AACF,wBAAQ,IAAI,+BAAyB;AACrC,sBAAM,KAAK,aAAa,QAAQ;AAChC,wBAAQ,IAAI,qCAA+B;cAC7C,SAAS,OAAO;AACd,wBAAQ,MAAM,qCAA+B,KAAK;cACpD;YACF,OAAO;AACL,sBAAQ,IAAI,gDAAoC;YAClD;UACF,SAAS,OAAO;AACd,oBAAQ,MAAM,kCAAkC,KAAK;UACvD;QACF,IAAG,GAAK;AAER,gBAAQ,IAAI,sCAAsC,SAAS;MAC7D;;MAGA,aAAa,WAAiB;AAC5B,YAAI,KAAK,kBAAkB;AACzB,wBAAc,KAAK,gBAAgB;AACnC,eAAK,mBAAmB;AAGxB,eAAK,sBAAsB,KAAK,KAAK;AAErC,kBAAQ,IAAI,oCAAoC,SAAS;QAC3D;MACF;;MAGc,qBAAkB;;AAC9B,cAAI;AACF,kBAAM,cAAc,MAAM,YAAY,mBAAmB;cACvD,oBAAoB;cACpB,SAAS;cACT,YAAY;;aACb;AAGD,kBAAM,WAAW,YAAY,OAAO;AACpC,gBAAI,YAAY,WAAW,IAAI;AAC7B,sBAAQ,IAAI,0BAAuB,QAAQ,0BAA0B;AAErE,oBAAM,mBAAmB,MAAM,YAAY,mBAAmB;gBAC5D,oBAAoB;gBACpB,SAAS;gBACT,YAAY;;eACb;AAED,kBAAI,iBAAiB,OAAO,YAAY,iBAAiB,OAAO,YAAY,IAAI;AAC9E,4BAAY,SAAS,iBAAiB;cACxC;YACF;AAEA,kBAAM,WAAwB;cAC5B,IAAI,KAAK,iBAAgB;cACzB,WAAW,KAAK;cAChB,UAAU,YAAY,OAAO;cAC7B,WAAW,YAAY,OAAO;cAC9B,YAAW,oBAAI,KAAI,GAAG,YAAW;;cAEjC,UAAU,YAAY,OAAO,YAAY;cACzC,UAAU,YAAY,OAAO,YAAY;cACzC,OAAO,YAAY,OAAO,SAAS;;AAGrC,oBAAQ,IAAI,6BAAuB,SAAS,SAAS,QAAQ,CAAC,CAAC,KAAK,SAAS,UAAU,QAAQ,CAAC,CAAC,kBAAe,QAAQ,IAAI;AAE5H,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,sCAAgC,KAAK;AACnD,mBAAO;UACT;QACF;;;MAGc,kBAAe;;AAC3B,cAAI;AACF,kBAAM,WAAW,MAAM,YAAY,mBAAkB;AACrD,kBAAM,QAAQ,SAAS,OAAO,QAAQ,SAAS,OAAO,QAAQ,MAAM;AACpE,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,+BAA+B,KAAK;AAClD,mBAAO;UACT;QACF;;;MAGc,aAAa,UAAqB;;AAC9C,cAAI;AAEF,kBAAM,KAAK,QAAQ,OAAO,eAAe,QAAQ;AACjD,oBAAQ,IAAI,4BAAsB,QAAQ;UAC5C,SAAS,OAAO;AACd,oBAAQ,MAAM,uCAAiC,KAAK;UACtD;QACF;;;MAGQ,mBAAgB;AACtB,eAAO,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;MACnD;MAEM,eAAe,UAAqB;;AACxC,cAAI;AAEF,kBAAM,eAAe,MAAM,KAAK,gBAAe;AAC/C,kBAAM,cAAc,KAAK,IAAG;AAE5B,gBAAI,KAAK,gBAAgB,KAAK,KAAK,oBAAoB,GAAG;AACxD,oBAAM,kBAAkB,KAAK,gBAAgB;AAC7C,oBAAM,kBAAkB,cAAc,KAAK,qBAAqB;AAEhE,kBAAI,iBAAiB,GAAG;AACtB,sBAAM,eAAe,kBAAkB;AAEvC,wBAAQ,IAAI,wBAAwB,KAAK,aAAa,iBAAiB,YAAY,OAAO;AAC1F,wBAAQ,IAAI,wBAAkB,YAAY,SAAS;AAGnD,oBAAI,eAAe,IAAI;AACrB,0BAAQ,IAAI,yCAAyC;AACrD,2BAAS,iBAAiB;AAC1B,wBAAM,KAAK,aAAa,QAAQ;AAGhC,uBAAK,gBAAgB;AACrB,uBAAK,oBAAoB;AAEzB,yBAAO;gBACT;cACF;YACF;AAGA,iBAAK,gBAAgB;AACrB,iBAAK,oBAAoB;AAGzB,mBAAO,MAAM,KAAK,+BAA+B,QAAQ;UAE3D,SAAS,OAAO;AACd,oBAAQ,MAAM,2BAA2B,KAAK;AAC9C,mBAAO;UACT;QACF;;MAEc,+BAA+B,UAAqB;;AAChE,iBAAO,IAAI,QAAQ,CAAO,YAAW;AACnC,gBAAI,aAAa;AACjB,gBAAI,iBAAsB;AAE1B,gBAAI;AAEF,oBAAM,oBAAoB,MAAM,KAAK,wBAAuB;AAC5D,kBAAI,CAAC,mBAAmB;AACtB,wBAAQ,IAAI,0CAAoC;AAChD,wBAAQ,KAAK;AACb;cACF;AAEA,+BAAiB,MAAM,OAAO,YAAY,SAAS,CAAC,UAAS;AAC3D,sBAAM,EAAE,aAAY,IAAK;AAEzB,qBAAI,6CAAc,MAAK,aAAa,IAAI,KAAK;AAC3C,0BAAQ,IAAI,kDAAkD;AAE9D,2BAAS,iBAAiB;AAC1B,uBAAK,aAAa,QAAQ;AAG1B,sBAAI,gBAAgB;AAClB,mCAAe,OAAM;kBACvB;AAEA,sBAAI,CAAC,YAAY;AACf,iCAAa;AACb,4BAAQ,IAAI;kBACd;gBACF;cACF,CAAC;AAGD,yBAAW,MAAK;AACd,oBAAI,CAAC,YAAY;AACf,+BAAa;AACb,sBAAI,gBAAgB;AAClB,mCAAe,OAAM;kBACvB;AACA,0BAAQ,KAAK;gBACf;cACF,GAAG,GAAI;YAET,SAAS,OAAO;AACd,sBAAQ,MAAM,oDAA8C,KAAK;AACjE,kBAAI,CAAC,YAAY;AACf,6BAAa;AACb,wBAAQ,KAAK;cACf;YACF;UACF,EAAC;QACH;;MAEc,0BAAuB;;AACnC,cAAI;AAEF,kBAAM,eAAe,MAAM,OAAO,YAAY,SAAS,MAAK;YAAE,CAAC;AAC/D,gBAAI,cAAc;AAChB,2BAAa,OAAM;AACnB,qBAAO;YACT;AACA,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,6DAA6D,KAAK;AAChF,mBAAO;UACT;QACF;;MAGM,kBAAkB,UAAqB;;AAC3C,gBAAM,WAAW,MAAM,KAAK,gBAAe;AAE1C,cAAI,WAAW,KAAK;AACf,oBAAQ,IAAI,4BAA4B;AACxC,qBAAS,iBAAiB;AAC1B,iBAAK,aAAa,QAAQ;AAC1B,mBAAO;UACZ;AACG,iBAAO;QACb;;;;uCAnSW,qBAAkB,mBAAA,kBAAA,CAAA;IAAA;2FAAlB,qBAAkB,SAAlB,oBAAkB,WAAA,YAFjB,OAAM,CAAA;AAEd,IAAO,qBAAP;;0EAAO,oBAAkB,CAAA;cAH9B;eAAW;UACV,YAAY;SACb;;;;;;;ACVD,IAKa;AALb;;;;;AAKM,IAAO,oBAAP,MAAO,kBAAgB;MAE3B,cAAA;AA2LQ,aAAA,eAAe,oBAAI,IAAG;MA3Ld;;;;MAKV,eAAe,KAAa,KAAW;;AAC3C,cAAI;AACF,oBAAQ,IAAI,qCAAqC,GAAG,KAAK,GAAG,EAAE;AAG9D,gBAAI,UAAU,MAAM,KAAK,mBAAmB,KAAK,GAAG;AACpD,gBAAI,WAAW,YAAY,iCAA2B;AACpD,qBAAO;YACT;AAGA,sBAAU,MAAM,KAAK,eAAe,KAAK,GAAG;AAC5C,gBAAI,WAAW,YAAY,iCAA2B;AACpD,qBAAO;YACT;AAGA,sBAAU,MAAM,KAAK,sBAAsB,KAAK,GAAG;AACnD,gBAAI,WAAW,YAAY,iCAA2B;AACpD,qBAAO;YACT;AAGA,mBAAO,KAAK,2BAA2B,KAAK,GAAG;UAEjD,SAAS,OAAO;AACd,oBAAQ,MAAM,oCAAoC,KAAK;AACvD,mBAAO,KAAK,2BAA2B,KAAK,GAAG;UACjD;QACF;;;;;MAKc,mBAAmB,KAAa,KAAW;;AACvD,cAAI;AACF,kBAAM,MAAM,qEAAqE,GAAG,cAAc,GAAG;AAErG,oBAAQ,IAAI,8BAA8B;AAE1C,kBAAM,WAAW,MAAM,MAAM,KAAK;cAChC,QAAQ;cACR,SAAS;gBACP,UAAU;;aAEb;AAED,gBAAI,CAAC,SAAS,IAAI;AAChB,oBAAM,IAAI,MAAM,uBAAuB,SAAS,MAAM,EAAE;YAC1D;AAEA,kBAAM,OAAO,MAAM,SAAS,KAAI;AAEhC,gBAAI,MAAM;AACR,oBAAM,eAAe,CAAA;AAGrB,kBAAI,KAAK;AAAU,6BAAa,KAAK,KAAK,QAAQ;AAClD,kBAAI,KAAK,QAAQ,KAAK,SAAS,KAAK;AAAU,6BAAa,KAAK,KAAK,IAAI;AACzE,kBAAI,KAAK;AAAsB,6BAAa,KAAK,KAAK,oBAAoB;AAC1E,kBAAI,KAAK;AAAa,6BAAa,KAAK,KAAK,WAAW;AAExD,kBAAI,aAAa,SAAS,GAAG;AAC3B,sBAAM,UAAU,aAAa,KAAK,IAAI;AACtC,wBAAQ,IAAI,wCAAqC,OAAO;AACxD,uBAAO;cACT;YACF;AAEA,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,6BAA6B,KAAK;AAChD,mBAAO;UACT;QACF;;;;;MAKc,eAAe,KAAa,KAAW;;AACnD,cAAI;AAEF,kBAAM,SAAS;AAEf,gBAAI,CAAC,UAAU,WAAW,gBAAgB;AACxC,sBAAQ,IAAI,iDAA8C;AAC1D,qBAAO;YACT;AAEA,kBAAM,MAAM,kDAAkD,GAAG,IAAI,GAAG,QAAQ,MAAM;AAEtF,oBAAQ,IAAI,0BAA0B;AAEtC,kBAAM,WAAW,MAAM,MAAM,GAAG;AAEhC,gBAAI,CAAC,SAAS,IAAI;AAChB,oBAAM,IAAI,MAAM,uBAAuB,SAAS,MAAM,EAAE;YAC1D;AAEA,kBAAM,OAAO,MAAM,SAAS,KAAI;AAEhC,gBAAI,QAAQ,KAAK,WAAW,KAAK,QAAQ,SAAS,GAAG;AACnD,oBAAM,SAAS,KAAK,QAAQ,CAAC;AAC7B,oBAAM,UAAU,OAAO;AACvB,sBAAQ,IAAI,oCAAiC,OAAO;AACpD,qBAAO;YACT;AAEA,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,yBAAyB,KAAK;AAC5C,mBAAO;UACT;QACF;;;;;MAKc,sBAAsB,KAAa,KAAW;;AAC1D,cAAI;AACF,kBAAM,WAAW;AACjB,kBAAM,YAAY,mBAChB,+DAA+D,GAAG,QAAQ,GAAG,+CAA+C;AAE9H,kBAAM,MAAM,WAAW;AAEvB,oBAAQ,IAAI,iCAAiC;AAE7C,kBAAM,WAAW,MAAM,MAAM,KAAK;cAChC,QAAQ;cACR,SAAS;gBACP,UAAU;;aAEb;AAED,gBAAI,CAAC,SAAS,IAAI;AAChB,oBAAM,IAAI,MAAM,uBAAuB,SAAS,MAAM,EAAE;YAC1D;AAEA,kBAAM,YAAY,MAAM,SAAS,KAAI;AAErC,gBAAI,aAAa,UAAU,UAAU;AACnC,oBAAM,OAAO,KAAK,MAAM,UAAU,QAAQ;AAE1C,kBAAI,QAAQ,KAAK,cAAc;AAC7B,wBAAQ,IAAI,6CAA0C,KAAK,YAAY;AACvE,uBAAO,KAAK;cACd;YACF;AAEA,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,gCAAgC,KAAK;AACnD,mBAAO;UACT;QACF;;;;;MAKQ,2BAA2B,KAAa,KAAW;AACzD,cAAM,eAAe,OAAO,IAAI,MAAM;AACtC,cAAM,eAAe,OAAO,IAAI,MAAM;AAEtC,cAAM,eAAe,KAAK,IAAI,GAAG,EAAE,QAAQ,CAAC;AAC5C,cAAM,eAAe,KAAK,IAAI,GAAG,EAAE,QAAQ,CAAC;AAE5C,eAAO,GAAG,YAAY,OAAI,YAAY,KAAK,YAAY,OAAI,YAAY;MACzE;;;;MAKA,eAAe,SAAe;AAE5B,cAAM,oBAAoB;AAC1B,eAAO,CAAC,kBAAkB,KAAK,OAAO;MACxC;MAOM,2BAA2B,KAAa,KAAW;;AACvD,gBAAM,MAAM,GAAG,IAAI,QAAQ,CAAC,CAAC,IAAI,IAAI,QAAQ,CAAC,CAAC;AAE/C,cAAI,KAAK,aAAa,IAAI,GAAG,GAAG;AAC9B,oBAAQ,IAAI,gCAA6B,KAAK,aAAa,IAAI,GAAG,CAAC;AACnE,mBAAO,KAAK,aAAa,IAAI,GAAG;UAClC;AAEA,gBAAM,UAAU,MAAM,KAAK,eAAe,KAAK,GAAG;AAClD,eAAK,aAAa,IAAI,KAAK,OAAO;AAElC,iBAAO;QACT;;;;;MAKA,aAAU;AACR,aAAK,aAAa,MAAK;AACvB,gBAAQ,IAAI,6BAA0B;MACxC;;;uCAnNW,mBAAgB;IAAA;yFAAhB,mBAAgB,SAAhB,kBAAgB,WAAA,YAFf,OAAM,CAAA;AAEd,IAAO,mBAAP;;0EAAO,kBAAgB,CAAA;cAH5B;eAAW;UACV,YAAY;SACb;;;;;;;ACJD,IAoBa;AApBb;;;;AACA;AASA;AAEA;;;;;;;;;;;AAQM,IAAO,yBAAP,MAAO,uBAAqB;MAE9B,YACU,gBACA,oBACA,UACA,oBACA,gBACA,gBACA,MACA,YACA,kBAAkC;AARlC,aAAA,iBAAA;AACA,aAAA,qBAAA;AACA,aAAA,WAAA;AACA,aAAA,qBAAA;AACA,aAAA,iBAAA;AACA,aAAA,iBAAA;AACA,aAAA,OAAA;AACA,aAAA,aAAA;AACA,aAAA,mBAAA;AAKK,aAAA,YAAY;AAHzB,aAAK,WAAW,KAAK,SAAS,GAAG,WAAW,KAAK,KAAK,SAAS,GAAG,SAAS;MAC7E;MAMI,OAAI;;AAER,gBAAM,KAAK,mBAAmB,KAAI;QACpC;;;;;;MAMM,eAAY;;AAChB,gBAAM,SAAS,MAAM,KAAK,eAAe,UAAS;AAGlD,gBAAM,cAAc,MAAM,KAAK,eAAe,YAAY,MAAM;AAChE,cAAI,CAAC,aAAa;AAChB,oBAAQ,MAAM,4DAA4D;AAC1E,mBAAO;UACT;AAGA,gBAAM,iBAAiB,MAAM,KAAK,eAAe,kBAAkB,MAAM;AACzE,cAAI,CAAC,gBAAgB;AACnB,oBAAQ,MAAM,8CAA8C;AAC5D,mBAAO;UACT;AAEA,gBAAM,KAAK,WAAM;AACjB,gBAAM,aAAY,oBAAI,KAAI,GAAG,YAAW;AAGxC,gBAAM,aAAa,KAAK,eAAe,YAAW,IAAK,WAAW,gBAAgB,WAAW;AAG7F,gBAAM,KAAK,mBAAmB,OAAO,KAAK,WAAW;YACnD;YACA;YACA;YACA,WAAW,eAAe;YAC1B;YACA,cAAc;WACf;AAGD,eAAK,mBAAmB,cAAc,EAAE;AACxC,iBAAO;QACT;;MAEM,WAAW,WAAiB;;AAChC,eAAK,mBAAmB,aAAa,SAAS;AAC9C,gBAAM,WAAU,oBAAI,KAAI,GAAG,YAAW;AAEtC,kBAAQ,IAAI,sBAAsB,SAAS,KAAK;AAGhD,gBAAM,SAAS,MAAM,KAAK,mBAAmB,OAAO,eAAe,sBAAsB,SAAS,0BAA0B;AAG5H,cAAI,eAA8B,CAAA;AAClC,cAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,2BAAe;UACjB,WAAW,QAAQ;AACjB,gBAAI;AACF,6BAAe,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAA;YAClD,SAAS,OAAO;AACd,sBAAQ,MAAM,2CAA2C,KAAK;YAChE;UACF;AAEA,kBAAQ,IAAI,8BAA8B,aAAa,MAAM,EAAE;AAG/D,gBAAM,KAAK,+BAA+B,YAAY;AAEtD,cAAI,gBAAgB;AAEpB,cAAI,gBAAgB,aAAa,SAAS,GAAG;AAE3C,kBAAM,oBAAoB,KAAK,0BAA0B,YAAY;AACrE,oBAAQ,IAAI,6BAA0B,kBAAkB,MAAM,EAAE;AAGhE,4BAAgB,KAAK,yBAAyB,iBAAiB;AAE/D,oBAAQ,IAAI,4BAAyB,gBAAgB,KAAM,QAAQ,CAAC,CAAC,KAAK;UAC5E,OAAO;AACL,oBAAQ,IAAI,0DAAuD;UACrE;AAGA,gBAAM,aAAa,KAAK,eAAe,YAAW,IAAK,WAAW,gBAAgB,WAAW;AAG7F,gBAAM,KAAK,mBAAmB,OAC5B,KAAK,WACL;YACE;YACA,UAAU,gBAAgB;;YAC1B;YACA,cAAc;aAEhB,SAAS,SAAS,GAAG;AAGvB,kBAAQ,IAAI,UAAU,SAAS,oBAAoB,gBAAgB,KAAM,QAAQ,CAAC,CAAC,KAAK;QAC1F;;MAEM,YAAY,WAAmB,UAAkB,WAAiB;;AACtE,gBAAM,KAAK,WAAM;AACjB,gBAAM,aAAY,oBAAI,KAAI,GAAG,YAAW;AAGxC,gBAAM,aAAa,KAAK,eAAe,YAAW,IAAK,WAAW,gBAAgB,WAAW;AAG7F,gBAAM,KAAK,mBAAmB,OAAO,eAAe;YAClD;YACA;YACA;YACA;YACA;YACA;YACA,cAAc;WACf;QACH;;MAEM,kBAAe;;AAEnB,cAAI,KAAK,IAAI;AACX,iBAAK,GAAG,MAAK;AACb,iBAAK,KAAK;UACX;QACH;;MAEM,iBAAc;;AAElB,cAAI,SAAS,MAAM,KAAK,mBAAmB,OAAO,KAAK,WAAW,yBAAyB;AAE3F,cAAG,CAAC,UAAU,OAAO,WAAW;AAC9B,mBAAO,CAAA;AAET,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,kBAAM,YAAY,OAAO,CAAC,EAAE;AAE5B,kBAAM,eAAe,MAAM,KAAK,mBAAmB,OAAO,eAAe,sBAAsB,SAAS,0BAA0B;AAGlI,gBAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,qBAAO,CAAC,EAAE,QAAQ,aAAa,OAAO,CAAC,SAAsB,KAAK,cAAc,SAAS;YAC3F,OAAO;AACL,qBAAO,CAAC,EAAE,QAAQ,CAAA;YACpB;UACF;AAEA,gBAAM,WAAsB,OAAO,IAAI,CAAC,SAAa;AACnD,mBAAO;cACL,IAAI,KAAK;cACT,WAAW,KAAK;cAChB,SAAS,KAAK;cACd,UAAU,KAAK;cACf,QAAQ,KAAK;cACb,WAAW,KAAK;cAChB,cAAc,KAAK;cACnB,YAAY,KAAK,cAAc,WAAW;cAC1C,cAAc,KAAK,eAAe,IAAI,KAAK,KAAK,YAAY,IAAI;;UAEpE,CAAC;AACD,iBAAO;QACT;;MAEM,qBAAqB,QAAc;;AAGvC,cAAI,SAAS,MAAM,KAAK,mBAAmB,OAAO,KAAK,WAAW,0BAA0B;AAG5F,cAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,qBAAS,OAAO,OAAO,CAAC,SAAc,KAAK,WAAW,MAAM;UAC9D,OAAO;AACL,mBAAO,CAAA;UACT;AAEA,cAAG,CAAC,UAAU,OAAO,WAAW;AAC9B,mBAAO,CAAA;AAET,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,kBAAM,YAAY,OAAO,CAAC,EAAE;AAE5B,kBAAM,eAAe,MAAM,KAAK,mBAAmB,OAAO,eAAe,sBAAsB,SAAS,0BAA0B;AAGlI,gBAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,qBAAO,CAAC,EAAE,QAAQ,aAAa,OAAO,CAAC,SAAsB,KAAK,cAAc,SAAS;YAC3F,OAAO;AACL,qBAAO,CAAC,EAAE,QAAQ,CAAA;YACpB;UACF;AAEA,gBAAM,WAAsB,OAAO,IAAI,CAAC,SAAa;AACnD,mBAAO;cACL,IAAI,KAAK;cACT,WAAW,KAAK;cAChB,SAAS,KAAK;cACd,UAAU,KAAK;cACf,QAAQ,KAAK;cACb,WAAW,KAAK;cAChB,cAAc,KAAK;cACnB,YAAY,KAAK,cAAc,WAAW;cAC1C,cAAc,KAAK,eAAe,IAAI,KAAK,KAAK,YAAY,IAAI;;UAEpE,CAAC;AACD,iBAAO;QACT;;;;;MAKQ,0BAA0B,WAAwB;AACxD,YAAI,UAAU,UAAU;AAAG,iBAAO;AAElC,cAAM,WAA0B,CAAA;AAChC,cAAM,yBAAyB;AAC/B,cAAM,sBAAsB;AAG5B,iBAAS,KAAK,UAAU,CAAC,CAAC;AAE1B,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,gBAAM,UAAU,UAAU,CAAC;AAC3B,gBAAM,WAAW,SAAS,SAAS,SAAS,CAAC;AAG7C,gBAAM,WAAW,KAAK,kBACpB,SAAS,UAAU,SAAS,WAC5B,QAAQ,UAAU,QAAQ,SAAS;AAIrC,gBAAM,YAAY,IAAI,KAAK,QAAQ,SAAS,EAAE,QAAO,IAAK,IAAI,KAAK,SAAS,SAAS,EAAE,QAAO,MAAO,MAAO,KAAK;AAGjH,gBAAM,QAAQ,WAAW,IAAK,WAAW,MAAQ,WAAW;AAO5D,gBAAM,kBAAkB,CAAC,QAAQ,YAAY,QAAQ,YAAY;AAEjE,cAAI,YAAY,0BACZ,SAAS,uBACT,KAAK,kBAAkB,QAAQ,UAAU,QAAQ,SAAS,KAC1D,iBAAiB;AACnB,qBAAS,KAAK,OAAO;UACvB,OAAO;AACL,kBAAM,SAAS,CAAA;AACf,gBAAI,WAAW;AAAwB,qBAAO,KAAK,QAAQ,SAAS,QAAQ,CAAC,CAAC,GAAG;AACjF,gBAAI,QAAQ;AAAqB,qBAAO,KAAK,SAAS,MAAM,QAAQ,CAAC,CAAC,MAAM;AAC5E,gBAAI,CAAC,KAAK,kBAAkB,QAAQ,UAAU,QAAQ,SAAS;AAAG,qBAAO,KAAK,gBAAgB;AAC9F,gBAAI,CAAC;AAAiB,qBAAO,KAAK,YAAY,QAAQ,QAAQ,GAAG;AACjE,oBAAQ,IAAI,mBAAmB,OAAO,KAAK,IAAI,CAAC,EAAE;UACpD;QACF;AAGA,cAAM,eAAe,UAAU,UAAU,SAAS,CAAC;AACnD,cAAM,eAAe,SAAS,SAAS,SAAS,CAAC;AACjD,YAAI,aAAa,cAAc,aAAa,WAAW;AACrD,mBAAS,KAAK,YAAY;QAC5B;AAEA,eAAO;MACT;;;;MAKQ,kBAAkB,KAAa,KAAW;AAChD,eAAO,OAAO,OAAO,OAAO,MAAM,OAAO,QAAQ,OAAO,OAAO,QAAQ,KAAK,QAAQ;MACtF;;;;MAKQ,yBAAyB,WAAwB;AACvD,YAAI,UAAU,SAAS;AAAG,iBAAO;AAGjC,cAAM,iBAAiB,KAAK,kBAC1B,UAAU,CAAC,EAAE,UAAU,UAAU,CAAC,EAAE,WACpC,UAAU,UAAU,SAAS,CAAC,EAAE,UAAU,UAAU,UAAU,SAAS,CAAC,EAAE,SAAS;AAIrF,YAAI,qBAAqB;AACzB,iBAAS,IAAI,GAAG,IAAI,UAAU,SAAS,GAAG,KAAK;AAC7C,gBAAM,kBAAkB,KAAK,kBAC3B,UAAU,CAAC,EAAE,UAAU,UAAU,CAAC,EAAE,WACpC,UAAU,IAAI,CAAC,EAAE,UAAU,UAAU,IAAI,CAAC,EAAE,SAAS;AAEvD,gCAAsB;QACxB;AAGA,cAAM,mBAAmB,KAAK,0BAA0B,SAAS;AAEjE,gBAAQ,IAAI,2BAAwB;AACpC,gBAAQ,IAAI,cAAc,iBAAiB,KAAM,QAAQ,CAAC,CAAC,KAAK;AAChE,gBAAQ,IAAI,kBAAkB,qBAAqB,KAAM,QAAQ,CAAC,CAAC,KAAK;AACxE,gBAAQ,IAAI,iBAAiB,mBAAmB,KAAM,QAAQ,CAAC,CAAC,KAAK;AAIrE,cAAM,QAAQ,qBAAqB;AAEnC,YAAI,QAAQ,GAAK;AACf,kBAAQ,IAAI,qBAAqB,MAAM,QAAQ,CAAC,CAAC,+BAA4B;AAC7E,iBAAO;QACT,OAAO;AACL,kBAAQ,IAAI,+BAA4B;AACxC,iBAAO;QACT;MACF;;;;MAKQ,0BAA0B,WAAwB;AACxD,YAAI,UAAU,SAAS;AAAG,iBAAO;AAEjC,YAAI,gBAAgB;AACpB,cAAM,oBAAoB;AAE1B,iBAAS,IAAI,GAAG,IAAI,UAAU,SAAS,GAAG,KAAK;AAC7C,gBAAM,UAAU,UAAU,CAAC;AAC3B,gBAAM,OAAO,UAAU,IAAI,CAAC;AAE5B,gBAAM,kBAAkB,KAAK,kBAC3B,QAAQ,UAAU,QAAQ,WAC1B,KAAK,UAAU,KAAK,SAAS;AAI/B,gBAAM,YAAY,IAAI,KAAK,KAAK,SAAS,EAAE,QAAO,IAAK,IAAI,KAAK,QAAQ,SAAS,EAAE,QAAO,MAAO,MAAO,KAAK;AAC7G,gBAAM,QAAQ,WAAW,IAAK,kBAAkB,MAAQ,WAAW;AAGnE,cAAI,SAAS,mBAAmB;AAC9B,6BAAiB;UACnB,OAAO;AACL,oBAAQ,IAAI,sBAAsB,MAAM,QAAQ,CAAC,CAAC,OAAO;UAC3D;QACF;AAEA,eAAO;MACT;;;;MAKQ,kBAAkB,MAAc,MAAc,MAAc,MAAY;AAC9E,cAAM,QAAQ,CAAC,UAAmB,QAAQ,KAAK,KAAM;AAErD,cAAM,IAAI;AACV,cAAM,OAAO,MAAM,OAAO,IAAI;AAC9B,cAAM,OAAO,MAAM,OAAO,IAAI;AAE9B,cAAM,IACJ,KAAK,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC,IACtC,KAAK,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,IAAI,MAAM,IAAI,CAAC,IAC5C,KAAK,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC;AAExC,cAAM,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC;AAEvD,eAAO,IAAI;MACb;;;;MAKc,+BAA+B,cAA2B;;AACtE,cAAI,CAAC,gBAAgB,aAAa,WAAW;AAAG;AAEhD,kBAAQ,IAAI,kDAA+C;AAG3D,gBAAM,uBAAuB,aAAa,OAAO,UAAQ,CAAC,KAAK,OAAO;AAEtE,cAAI,qBAAqB,WAAW,GAAG;AACrC,oBAAQ,IAAI,2CAAqC;AACjD;UACF;AAEA,kBAAQ,IAAI,GAAG,qBAAqB,MAAM,iCAA8B;AAGxE,gBAAM,iBAA2E,CAAA;AACjF,gBAAM,gBAA+B,CAAA;AAErC,+BAAqB,QAAQ,CAAC,SAAQ;AACpC,kBAAM,cAAc,aAAa,QAAQ,IAAI;AAE7C,gBAAI,gBAAgB,GAAG;AACrB,6BAAe,KAAK,EAAE,MAAM,OAAO,aAAa,UAAU,QAAO,CAAE;YACrE,WAAW,gBAAgB,aAAa,SAAS,KAAK,aAAa,SAAS,GAAG;AAC7E,6BAAe,KAAK,EAAE,MAAM,OAAO,aAAa,UAAU,MAAK,CAAE;YACnE,OAAO;AACL,4BAAc,KAAK,IAAI;YACzB;UACF,CAAC;AAGD,cAAI,eAAe,SAAS,GAAG;AAC7B,oBAAQ,IAAI,4CAAsC,eAAe,MAAM,aAAa;AAEpF,kBAAM,QAAQ,IACZ,eAAe,IAAI,CAAO,OAAsB,eAAtB,KAAsB,WAAtB,EAAE,MAAM,SAAQ,GAAM;AAC9C,kBAAI;AACF,wBAAQ,IAAI,+BAA4B,aAAa,UAAU,cAAW,KAAK,eAAe;AAC9F,sBAAM,UAAU,MAAM,KAAK,iBAAiB,2BAA2B,KAAK,UAAU,KAAK,SAAS;AAGpG,qBAAK,UAAU;AAGf,sBAAM,KAAK,yBAAyB,KAAK,IAAI,OAAO;AAEpD,wBAAQ,IAAI,kBAAe,aAAa,UAAU,cAAW,KAAK,WAAW,OAAO,EAAE;cACxF,SAAS,OAAO;AACd,wBAAQ,MAAM,qCAAkC,QAAQ,KAAK,KAAK;AAClE,qBAAK,UAAU;AACf,sBAAM,KAAK,yBAAyB,KAAK,IAAI,kCAAyB;cACxE;YACF,EAAC,CAAC;AAIJ,kBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;UACvD;AAGA,cAAI,cAAc,SAAS,GAAG;AAC5B,oBAAQ,IAAI,8CAAwC,cAAc,MAAM,aAAa;AAErF,kBAAM,YAAY;AAElB,qBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK,WAAW;AACxD,oBAAM,QAAQ,cAAc,MAAM,GAAG,IAAI,SAAS;AAElD,oBAAM,QAAQ,IACZ,MAAM,IAAI,CAAO,SAAQ;AACvB,oBAAI;AACF,wBAAM,UAAU,MAAM,KAAK,iBAAiB,2BAA2B,KAAK,UAAU,KAAK,SAAS;AAGpG,uBAAK,UAAU;AAGf,wBAAM,KAAK,yBAAyB,KAAK,IAAI,OAAO;AAEpD,0BAAQ,IAAI,uCAAiC,OAAO,EAAE;gBACxD,SAAS,OAAO;AACd,0BAAQ,MAAM,kDAA4C,KAAK;AAC/D,uBAAK,UAAU;AACf,wBAAM,KAAK,yBAAyB,KAAK,IAAI,kCAAyB;gBACxE;cACF,EAAC,CAAC;AAIJ,kBAAI,IAAI,YAAY,cAAc,QAAQ;AACxC,sBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAI,CAAC;cACxD;YACF;UACF;AAEA,kBAAQ,IAAI,2CAAqC;QACnD;;;;;MAKc,yBAAyB,eAAuB,SAAe;;AAC3E,cAAI;AACF,kBAAM,KAAK,mBAAmB,OAC5B,eACA,EAAE,QAAgB,GAClB,SAAS,aAAa,GAAG;UAE7B,SAAS,OAAO;AACd,oBAAQ,MAAM,kDAA+C,aAAa,KAAK,KAAK;UACtF;QACF;;;;;;;MAOM,kBAAkB,SAAgB;;AACtC,cAAI;AAEF,kBAAM,eAAe,iCAChB,UADgB;cAEnB,cAAc,QAAQ,eAAe,QAAQ,aAAa,YAAW,IAAK;;AAI5E,gBAAI,aAAa,cAAc;AAC7B,qBAAO,aAAa;YACtB;AAEA,kBAAM,KAAK,mBAAmB,OAC5B,KAAK,WACL,cACA,SAAS,QAAQ,EAAE,GAAG;AAGxB,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,uCAAuC,KAAK;AAC1D,mBAAO;UACT;QACF;;;;;;;MAOM,uBAAuB,QAAc;;AACzC,gBAAM,WAAW,MAAM,KAAK,qBAAqB,MAAM;AACvD,iBAAO,SAAS,OAAO,OACrB,EAAE,eAAe,WAAW,iBAC5B,EAAE,eAAe,WAAW,iBAC5B,EAAE,eAAe,WAAW,aAAa;QAE7C;;;;;;;MAOM,kBAAkB,SAA8B;;AACpD,cAAI;AACF,kBAAM,MAAM,KAAK,WAAW,OAAO,qBAAqB;AACxD,kBAAM,eAAe,KAAK,KAAK,KAAK,KAAK,OAAO,CAAC;AACjD,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,kCAAkC,KAAK;AACrD,kBAAM;UACR;QACF;;;;uCA7jBW,wBAAqB,mBAAA,cAAA,GAAA,mBAAA,kBAAA,GAAA,mBAAA,QAAA,GAAA,mBAAA,kBAAA,GAAA,mBAAA,cAAA,GAAA,mBAAA,cAAA,GAAA,mBAAA,UAAA,GAAA,mBAAA,UAAA,GAAA,mBAAA,gBAAA,CAAA;IAAA;8FAArB,wBAAqB,SAArB,uBAAqB,WAAA,YAFpB,OAAM,CAAA;AAEd,IAAO,wBAAP;;0EAAO,uBAAqB,CAAA;cAHjC;eAAW;UACV,YAAY;SACb;;;;;", "names": ["init_esm", "init_definitions", "init_esm", "init_definitions", "init_esm"], "x_google_ignoreList": [0, 1, 2, 3, 4]}