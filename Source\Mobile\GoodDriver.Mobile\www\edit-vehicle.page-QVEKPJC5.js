import {
  BrandService,
  ModelService,
  init_brand_service,
  init_model_service
} from "./chunk-FNHWDMEZ.js";
import {
  ActivatedRoute,
  BooleanValueAccessorDirective,
  ChangeDetectorRef,
  CommonModule,
  Component,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  IonBackButton2 as IonBackButton,
  IonButton,
  IonButtons,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonCheckbox,
  IonContent,
  IonHeader,
  IonIcon,
  IonInput,
  IonItem,
  IonLabel,
  IonMaxValidator,
  IonMinValidator,
  IonNote,
  IonSelect,
  IonSelectOption,
  IonSpinner,
  IonText,
  IonTitle,
  IonToolbar,
  IonicModule,
  MaxLengthValidator,
  NetworkService,
  NgControlStatus,
  NgControlStatusGroup,
  NgForOf,
  NgIf,
  NumericValueAccessorDirective,
  ReactiveFormsModule,
  Router,
  SelectValueAccessorDirective,
  SessionService,
  SyncStatus,
  TextValueAccessorDirective,
  ToastService,
  Validators,
  VehicleService,
  ViewChild,
  init_common,
  init_core,
  init_forms,
  init_ionic_angular,
  init_network_service,
  init_router,
  init_session_service,
  init_sync_model,
  init_toast_service,
  init_vehicle_service,
  setClassMetadata,
  ɵNgNoValidate,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵqueryRefresh,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵviewQuery
} from "./chunk-2IV7NJLP.js";
import "./chunk-GJIVKQBF.js";
import "./chunk-WY354WYL.js";
import "./chunk-3TAP6XVY.js";
import "./chunk-BQEXAUCY.js";
import "./chunk-EUHNYIV3.js";
import "./chunk-AY6ZGCP5.js";
import "./chunk-I3UCYTIQ.js";
import "./chunk-4JI7VCBZ.js";
import "./chunk-YY7NCLAA.js";
import "./chunk-GUQ63TTB.js";
import "./chunk-CHFZ6R2P.js";
import "./chunk-XPKADKFH.js";
import "./chunk-V6QEVD67.js";
import "./chunk-WNPNB2PX.js";
import "./chunk-NRG74LSH.js";
import "./chunk-YSMEPS6E.js";
import "./chunk-NHEUXSZ2.js";
import "./chunk-WX4TOLMF.js";
import "./chunk-6SXGDLUH.js";
import "./chunk-NNIHVF7T.js";
import "./chunk-ZNALOMZJ.js";
import "./chunk-GHA2GP6C.js";
import "./chunk-H2CU6QHT.js";
import "./chunk-6PMAHZVS.js";
import "./chunk-DOEZTQY3.js";
import "./chunk-O4BCOVEV.js";
import "./chunk-TD4SIQNM.js";
import "./chunk-VU5YACRM.js";
import {
  __async,
  __esm
} from "./chunk-4W6HR7MY.js";

// src/app/pages/tabs/vehicles/edit-vehicle/edit-vehicle.page.ts
function EditVehiclePage_ion_content_6_ion_card_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-card", 25)(1, "ion-card-header")(2, "ion-card-title");
    \u0275\u0275element(3, "ion-icon", 26);
    \u0275\u0275text(4, " Aten\xE7\xE3o ");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(5, "ion-card-content")(6, "p");
    \u0275\u0275text(7, "Para editar um ve\xEDculo, \xE9 necess\xE1rio estar conectado \xE0 internet para obter as marcas e modelos atualizados.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "p");
    \u0275\u0275text(9, "Por favor, verifique sua conex\xE3o e tente novamente.");
    \u0275\u0275elementEnd()()();
  }
}
function EditVehiclePage_ion_content_6_ion_card_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-card", 27)(1, "ion-card-content");
    \u0275\u0275element(2, "ion-icon", 28);
    \u0275\u0275elementStart(3, "span");
    \u0275\u0275text(4, " \xC9 necess\xE1rio estar conectado \xE0 internet para obter as marcas e modelos de ve\xEDculos atualizados.");
    \u0275\u0275elementEnd()()();
  }
}
function EditVehiclePage_ion_content_6_ion_select_option_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-select-option", 29);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const brand_r3 = ctx.$implicit;
    \u0275\u0275property("value", brand_r3.id);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(brand_r3.name);
  }
}
function EditVehiclePage_ion_content_6_ion_select_option_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-select-option", 29);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const model_r4 = ctx.$implicit;
    \u0275\u0275property("value", model_r4.id);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(model_r4.name);
  }
}
function EditVehiclePage_ion_content_6_ion_note_28_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-note", 30);
    \u0275\u0275text(1, " Formato inv\xE1lido. Use o formato AAA-0000 ");
    \u0275\u0275elementEnd();
  }
}
function EditVehiclePage_ion_content_6_ion_spinner_38_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "ion-spinner", 31);
  }
}
function EditVehiclePage_ion_content_6_span_39_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "Salvar Altera\xE7\xF5es");
    \u0275\u0275elementEnd();
  }
}
function EditVehiclePage_ion_content_6_div_44_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 32)(1, "ion-text", 33)(2, "p", 34);
    \u0275\u0275element(3, "ion-icon", 26);
    \u0275\u0275text(4, " N\xE3o \xE9 poss\xEDvel salvar sem conex\xE3o com a internet ");
    \u0275\u0275elementEnd()()();
  }
}
function EditVehiclePage_ion_content_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "ion-content", 5);
    \u0275\u0275template(1, EditVehiclePage_ion_content_6_ion_card_1_Template, 10, 0, "ion-card", 6)(2, EditVehiclePage_ion_content_6_ion_card_2_Template, 5, 0, "ion-card", 7);
    \u0275\u0275elementStart(3, "form", 8);
    \u0275\u0275listener("ngSubmit", function EditVehiclePage_ion_content_6_Template_form_ngSubmit_3_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onSubmit());
    });
    \u0275\u0275elementStart(4, "ion-item")(5, "ion-label", 9);
    \u0275\u0275text(6, "Marca");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "ion-select", 10, 0);
    \u0275\u0275listener("ionChange", function EditVehiclePage_ion_content_6_Template_ion_select_ionChange_7_listener($event) {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onBrandChange($event.detail.value));
    });
    \u0275\u0275template(9, EditVehiclePage_ion_content_6_ion_select_option_9_Template, 2, 2, "ion-select-option", 11);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(10, "ion-item")(11, "ion-label", 9);
    \u0275\u0275text(12, "Modelo");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "ion-select", 12, 1);
    \u0275\u0275listener("ionChange", function EditVehiclePage_ion_content_6_Template_ion_select_ionChange_13_listener($event) {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onModelChange($event.detail.value));
    });
    \u0275\u0275template(15, EditVehiclePage_ion_content_6_ion_select_option_15_Template, 2, 2, "ion-select-option", 11);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(16, "ion-item")(17, "ion-label", 9);
    \u0275\u0275text(18, "Vers\xE3o");
    \u0275\u0275elementEnd();
    \u0275\u0275element(19, "ion-input", 13);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(20, "ion-item")(21, "ion-label", 9);
    \u0275\u0275text(22, "Ano");
    \u0275\u0275elementEnd();
    \u0275\u0275element(23, "ion-input", 14);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(24, "ion-item")(25, "ion-label", 9);
    \u0275\u0275text(26, "Placa");
    \u0275\u0275elementEnd();
    \u0275\u0275element(27, "ion-input", 15);
    \u0275\u0275template(28, EditVehiclePage_ion_content_6_ion_note_28_Template, 2, 0, "ion-note", 16);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(29, "ion-item")(30, "ion-label", 9);
    \u0275\u0275text(31, "N\xFAmero da Ap\xF3lice (opcional)");
    \u0275\u0275elementEnd();
    \u0275\u0275element(32, "ion-input", 17);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(33, "ion-item")(34, "ion-label");
    \u0275\u0275text(35, "Ve\xEDculo Principal");
    \u0275\u0275elementEnd();
    \u0275\u0275element(36, "ion-checkbox", 18);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(37, "ion-button", 19);
    \u0275\u0275listener("click", function EditVehiclePage_ion_content_6_Template_ion_button_click_37_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onSubmit());
    });
    \u0275\u0275template(38, EditVehiclePage_ion_content_6_ion_spinner_38_Template, 1, 0, "ion-spinner", 20)(39, EditVehiclePage_ion_content_6_span_39_Template, 2, 0, "span", 21);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(40, "ion-button", 22);
    \u0275\u0275listener("click", function EditVehiclePage_ion_content_6_Template_ion_button_click_40_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onCancel());
    });
    \u0275\u0275text(41, " Cancelar ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(42, "ion-button", 23);
    \u0275\u0275listener("click", function EditVehiclePage_ion_content_6_Template_ion_button_click_42_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.debugFormValues());
    });
    \u0275\u0275text(43, " Debug Form Values ");
    \u0275\u0275elementEnd();
    \u0275\u0275template(44, EditVehiclePage_ion_content_6_div_44_Template, 5, 0, "div", 24);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    let tmp_10_0;
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r1.isOnline);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.isOnline);
    \u0275\u0275advance();
    \u0275\u0275property("formGroup", ctx_r1.vehicleForm);
    \u0275\u0275advance(4);
    \u0275\u0275property("compareWith", ctx_r1.compareWith);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r1.brands);
    \u0275\u0275advance(4);
    \u0275\u0275property("compareWith", ctx_r1.compareWith);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r1.models);
    \u0275\u0275advance(13);
    \u0275\u0275property("ngIf", ((tmp_10_0 = ctx_r1.vehicleForm.get("plate")) == null ? null : tmp_10_0.hasError("pattern")) && ((tmp_10_0 = ctx_r1.vehicleForm.get("plate")) == null ? null : tmp_10_0.touched));
    \u0275\u0275advance(9);
    \u0275\u0275property("disabled", ctx_r1.vehicleForm.invalid || ctx_r1.isSubmitting || !ctx_r1.isOnline);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.isSubmitting);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r1.isSubmitting);
    \u0275\u0275advance();
    \u0275\u0275property("disabled", ctx_r1.isSubmitting);
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", !ctx_r1.isOnline);
  }
}
function EditVehiclePage_ion_content_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-content", 5)(1, "div", 35);
    \u0275\u0275element(2, "ion-spinner", 31);
    \u0275\u0275elementStart(3, "p");
    \u0275\u0275text(4, "Carregando dados do ve\xEDculo...");
    \u0275\u0275elementEnd()()();
  }
}
var _c0, _c1, _EditVehiclePage, EditVehiclePage;
var init_edit_vehicle_page = __esm({
  "src/app/pages/tabs/vehicles/edit-vehicle/edit-vehicle.page.ts"() {
    init_core();
    init_forms();
    init_sync_model();
    init_common();
    init_ionic_angular();
    init_core();
    init_forms();
    init_router();
    init_vehicle_service();
    init_toast_service();
    init_brand_service();
    init_model_service();
    init_session_service();
    init_network_service();
    init_ionic_angular();
    init_common();
    _c0 = ["brandSelect"];
    _c1 = ["modelSelect"];
    _EditVehiclePage = class _EditVehiclePage {
      constructor(fb, route, router, vehicleService, toastService, brandService, modelService, sessionService, networkService, cdr) {
        this.fb = fb;
        this.route = route;
        this.router = router;
        this.vehicleService = vehicleService;
        this.toastService = toastService;
        this.brandService = brandService;
        this.modelService = modelService;
        this.sessionService = sessionService;
        this.networkService = networkService;
        this.cdr = cdr;
        this.vehicleId = "";
        this.isLoading = true;
        this.isSubmitting = false;
        this.isOnline = true;
        this.brands = [];
        this.models = [];
        this.subscriptions = [];
        this.pendingVehicleData = null;
        this.compareWith = (o1, o2) => {
          return o1 && o2 ? o1 === o2 : o1 === o2;
        };
        this.vehicleForm = this.fb.group({
          brand: [null, Validators.required],
          model: [null, Validators.required],
          version: [null],
          year: [null, [Validators.required, Validators.min(1990), Validators.max(2026)]],
          plate: [null, [Validators.required]],
          policy: [null],
          isPrimary: [false]
        });
      }
      ngOnInit() {
        return __async(this, null, function* () {
          this.vehicleId = this.route.snapshot.paramMap.get("id") || "";
          this.checkNetworkStatus();
          this.subscriptions.push(this.networkService.getOnlineStatus().subscribe((isOnline) => {
            this.isOnline = isOnline;
            if (isOnline) {
              if (this.brands.length === 0) {
                this.loadBrandsAndVehicle();
              }
            }
          }));
          if (this.isOnline) {
            yield this.loadBrandsAndVehicle();
          } else if (this.vehicleId) {
            yield this.loadVehicle();
          }
          this.isLoading = false;
        });
      }
      loadBrandsAndVehicle() {
        return __async(this, null, function* () {
          try {
            yield this.loadBrands();
            if (this.vehicleId) {
              yield this.loadVehicle();
            }
          } catch (error) {
            console.error("Error loading brands and vehicle:", error);
          }
        });
      }
      ngOnDestroy() {
        this.subscriptions.forEach((sub) => sub.unsubscribe());
      }
      ngAfterViewInit() {
        console.log("AfterViewInit - ViewChild elements:", {
          brandSelect: this.brandSelect,
          modelSelect: this.modelSelect
        });
        if (this.pendingVehicleData) {
          console.log("Applying pending vehicle data:", this.pendingVehicleData);
          this.forceUpdateSelects(this.pendingVehicleData.brandId, this.pendingVehicleData.modelId);
          this.pendingVehicleData = null;
        }
      }
      ionViewDidEnter() {
        console.log("ionViewDidEnter - View fully loaded");
        if (this.pendingVehicleData) {
          console.log("Final attempt - Applying pending vehicle data:", this.pendingVehicleData);
          setTimeout(() => {
            this.forceUpdateSelects(this.pendingVehicleData.brandId, this.pendingVehicleData.modelId);
            this.pendingVehicleData = null;
          }, 500);
        }
      }
      /**
       * Check the current network status
       */
      checkNetworkStatus() {
        this.isOnline = this.networkService.isOnlineNow();
      }
      loadBrands() {
        return __async(this, null, function* () {
          try {
            if (!this.isOnline) {
              this.toastService.showToast("Sem conex\xE3o com a internet. N\xE3o \xE9 poss\xEDvel carregar as marcas de ve\xEDculos.", "warning");
              return;
            }
            console.log("Loading brands...");
            const data = yield this.brandService.listAll();
            this.brands = data;
            console.log("Brands loaded:", this.brands);
          } catch (err) {
            console.error("Error to load brands:", err);
            this.toastService.showToast("Erro ao carregar marcas de ve\xEDculos", "danger");
          }
        });
      }
      loadVehicle() {
        return __async(this, null, function* () {
          try {
            const vehicle = yield this.vehicleService.getById(this.vehicleId);
            if (vehicle) {
              console.log("Vehicle loaded:", vehicle);
              this.vehicleForm.patchValue({
                year: vehicle.year,
                plate: vehicle.plate,
                version: vehicle.version,
                policy: vehicle.policyNumber,
                isPrimary: vehicle.isPrimary
              });
              if (vehicle.brandId) {
                console.log("Setting brand:", vehicle.brandId);
                this.pendingVehicleData = {
                  brandId: vehicle.brandId.toString(),
                  modelId: vehicle.modelId.toString()
                };
                yield this.onBrandChange(vehicle.brandId.toString(), vehicle.modelId.toString());
                this.forceUpdateFormValues(vehicle.brandId.toString(), vehicle.modelId.toString());
                this.forceUpdateSelects(vehicle.brandId.toString(), vehicle.modelId.toString());
                setTimeout(() => this.debugFormValues(), 1500);
              }
            } else {
              this.toastService.showToast("Ve\xEDculo n\xE3o encontrado", "danger");
              this.router.navigate(["/tabs/vehicles"]);
            }
          } catch (error) {
            console.error("Error loading vehicle:", error);
            this.toastService.showToast("Erro ao carregar ve\xEDculo", "danger");
          }
        });
      }
      onBrandChange(brandId, modelIdToSelect) {
        return __async(this, null, function* () {
          try {
            if (!this.isOnline) {
              this.toastService.showToast("Sem conex\xE3o com a internet. N\xE3o \xE9 poss\xEDvel carregar os modelos de ve\xEDculos.", "warning");
              return;
            }
            console.log("Loading models for brand:", brandId);
            this.vehicleForm.patchValue({ model: null });
            const data = yield this.modelService.getByBranch({ brandId: parseInt(brandId) });
            this.models = data;
            console.log("Models loaded:", this.models);
            if (modelIdToSelect) {
              console.log("Setting model:", modelIdToSelect);
              setTimeout(() => {
                var _a;
                this.vehicleForm.patchValue({ model: modelIdToSelect });
                console.log("Model set in form:", (_a = this.vehicleForm.get("model")) == null ? void 0 : _a.value);
              }, 100);
            }
          } catch (err) {
            console.error("Error to load models:", err);
            this.toastService.showToast("Erro ao carregar modelos de ve\xEDculos", "danger");
          }
        });
      }
      // This method is called when the model selection changes
      onModelChange(_modelId) {
      }
      /**
       * Force update form values - useful when dealing with async loading
       */
      forceUpdateFormValues(brandId, modelId) {
        setTimeout(() => {
          console.log("Force updating form values - Brand:", brandId, "Model:", modelId);
          this.vehicleForm.patchValue({
            brand: brandId,
            model: modelId
          });
          this.vehicleForm.updateValueAndValidity();
          console.log("Form values after force update:", this.vehicleForm.value);
        }, 200);
        setTimeout(() => {
          console.log("Second attempt - Force updating form values");
          this.vehicleForm.patchValue({
            brand: brandId,
            model: modelId
          });
          this.vehicleForm.updateValueAndValidity();
          this.vehicleForm.markAsDirty();
          this.vehicleForm.markAsTouched();
          this.cdr.detectChanges();
        }, 500);
        setTimeout(() => {
          console.log("Third attempt - Force updating form values");
          this.vehicleForm.patchValue({
            brand: brandId,
            model: modelId
          });
          this.vehicleForm.updateValueAndValidity();
          this.cdr.detectChanges();
        }, 1e3);
      }
      onSubmit() {
        return __async(this, null, function* () {
          var _a;
          if (this.vehicleForm.invalid) {
            this.toastService.showToast("Por favor, preencha todos os campos obrigat\xF3rios corretamente");
            return;
          }
          if (!this.isOnline) {
            this.toastService.showToast("Sem conex\xE3o com a internet. N\xE3o \xE9 poss\xEDvel atualizar o ve\xEDculo.", "warning");
            return;
          }
          this.isSubmitting = true;
          try {
            const formValues = this.vehicleForm.value;
            const userId = (yield this.sessionService.getUserId()) || "";
            const selectedBrandId = formValues.brand;
            const selectedModelId = formValues.model;
            const selectedBrand = this.brands.find((brand) => brand.id === selectedBrandId);
            const selectedModel = this.models.find((model) => model.id === selectedModelId);
            if (!selectedBrand || !selectedModel) {
              this.toastService.showToast("Informa\xE7\xF5es de marca ou modelo incompletas. Verifique sua conex\xE3o.", "warning");
              return;
            }
            const updatedVehicleData = {
              id: this.vehicleId,
              userId,
              plate: formValues.plate,
              year: parseInt(formValues.year),
              brandId: parseInt(formValues.brand),
              brandName: (selectedBrand == null ? void 0 : selectedBrand.name) || "",
              modelId: parseInt(formValues.model),
              modelName: (selectedModel == null ? void 0 : selectedModel.name) || "",
              version: formValues.version,
              policyNumber: formValues.policy,
              isPrimary: formValues.isPrimary,
              updatedOn: /* @__PURE__ */ new Date(),
              syncStatus: SyncStatus.PendingUpdate,
              lastSyncDate: void 0
            };
            yield this.vehicleService.update(this.vehicleId, updatedVehicleData);
            this.toastService.showToast("Ve\xEDculo atualizado com sucesso!");
            this.router.navigate(["/tabs/vehicles"]);
          } catch (error) {
            console.error("Error updating vehicle:", error);
            this.toastService.showToast(((_a = error.error) == null ? void 0 : _a.message) || "Erro ao atualizar ve\xEDculo", "danger");
          } finally {
            this.isSubmitting = false;
          }
        });
      }
      onCancel() {
        this.router.navigate(["/tabs/vehicles"]);
      }
      /**
       * Force update ion-select components directly
       */
      forceUpdateSelects(brandId, modelId) {
        return __async(this, null, function* () {
          try {
            setTimeout(() => {
              console.log("Forcing select updates...");
              if (this.brandSelect) {
                console.log("Updating brand select to:", brandId);
                this.brandSelect.value = brandId;
                this.cdr.detectChanges();
              }
              if (this.modelSelect && modelId) {
                console.log("Updating model select to:", modelId);
                this.modelSelect.value = modelId;
                this.cdr.detectChanges();
              }
            }, 100);
          } catch (error) {
            console.error("Error forcing select updates:", error);
          }
        });
      }
      /**
       * Debug method to check form values
       */
      debugFormValues() {
        var _a, _b;
        console.log("=== FORM DEBUG ===");
        console.log("Form valid:", this.vehicleForm.valid);
        console.log("Form values:", this.vehicleForm.value);
        console.log("Brand control value:", (_a = this.vehicleForm.get("brand")) == null ? void 0 : _a.value);
        console.log("Model control value:", (_b = this.vehicleForm.get("model")) == null ? void 0 : _b.value);
        console.log("Available brands:", this.brands.map((b) => ({ id: b.id, name: b.name })));
        console.log("Available models:", this.models.map((m) => ({ id: m.id, name: m.name })));
        console.log("Brand select element:", this.brandSelect);
        console.log("Model select element:", this.modelSelect);
        console.log("==================");
      }
    };
    _EditVehiclePage.\u0275fac = function EditVehiclePage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _EditVehiclePage)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(ActivatedRoute), \u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(VehicleService), \u0275\u0275directiveInject(ToastService), \u0275\u0275directiveInject(BrandService), \u0275\u0275directiveInject(ModelService), \u0275\u0275directiveInject(SessionService), \u0275\u0275directiveInject(NetworkService), \u0275\u0275directiveInject(ChangeDetectorRef));
    };
    _EditVehiclePage.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _EditVehiclePage, selectors: [["app-edit-vehicle"]], viewQuery: function EditVehiclePage_Query(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275viewQuery(_c0, 5);
        \u0275\u0275viewQuery(_c1, 5);
      }
      if (rf & 2) {
        let _t;
        \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.brandSelect = _t.first);
        \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.modelSelect = _t.first);
      }
    }, decls: 8, vars: 2, consts: [["brandSelect", ""], ["modelSelect", ""], ["slot", "start"], ["defaultHref", "/tabs/vehicles"], ["class", "ion-padding", 4, "ngIf"], [1, "ion-padding"], ["color", "warning", 4, "ngIf"], ["color", "light", 4, "ngIf"], [3, "ngSubmit", "formGroup"], ["position", "stacked"], ["formControlName", "brand", "interface", "popover", "placeholder", "Selecione uma marca", 3, "ionChange", "compareWith"], [3, "value", 4, "ngFor", "ngForOf"], ["formControlName", "model", "interface", "popover", "placeholder", "Selecione um modelo", 3, "ionChange", "compareWith"], ["formControlName", "version", "placeholder", "Ex: 1.0 Flex"], ["formControlName", "year", "type", "number", "placeholder", "Ex: 2021", "min", "1900", "max", "2099"], ["formControlName", "plate", "maxlength", "8", "placeholder", "AAA-0000", "inputmode", "text"], ["slot", "error", 4, "ngIf"], ["formControlName", "policy", "type", "text"], ["formControlName", "isPrimary", "slot", "end"], ["expand", "block", 1, "ion-margin-top", 3, "click", "disabled"], ["name", "crescent", 4, "ngIf"], [4, "ngIf"], ["expand", "block", "color", "medium", 3, "click", "disabled"], ["expand", "block", "color", "tertiary", "fill", "outline", 3, "click"], ["class", "offline-message", 4, "ngIf"], ["color", "warning"], ["name", "wifi-outline"], ["color", "light"], ["name", "information-circle-outline", "color", "primary"], [3, "value"], ["slot", "error"], ["name", "crescent"], [1, "offline-message"], ["color", "danger"], [1, "ion-text-center"], [1, "loading-container"]], template: function EditVehiclePage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header")(1, "ion-toolbar")(2, "ion-title");
        \u0275\u0275text(3, "Editar Ve\xEDculo");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(4, "ion-buttons", 2);
        \u0275\u0275element(5, "ion-back-button", 3);
        \u0275\u0275elementEnd()()();
        \u0275\u0275template(6, EditVehiclePage_ion_content_6_Template, 45, 13, "ion-content", 4)(7, EditVehiclePage_ion_content_7_Template, 5, 0, "ion-content", 4);
      }
      if (rf & 2) {
        \u0275\u0275advance(6);
        \u0275\u0275property("ngIf", !ctx.isLoading);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", ctx.isLoading);
      }
    }, dependencies: [IonicModule, IonButton, IonButtons, IonCard, IonCardContent, IonCardHeader, IonCardTitle, IonCheckbox, IonContent, IonHeader, IonIcon, IonInput, IonItem, IonLabel, IonNote, IonSelect, IonSelectOption, IonSpinner, IonText, IonTitle, IonToolbar, BooleanValueAccessorDirective, NumericValueAccessorDirective, SelectValueAccessorDirective, TextValueAccessorDirective, IonBackButton, IonMinValidator, IonMaxValidator, ReactiveFormsModule, \u0275NgNoValidate, NgControlStatus, NgControlStatusGroup, MaxLengthValidator, FormGroupDirective, FormControlName, CommonModule, NgForOf, NgIf], styles: ["\n\nform[_ngcontent-%COMP%] {\n  padding: 16px;\n}\nion-list[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n}\n.offline-message[_ngcontent-%COMP%] {\n  margin-top: var(--app-spacing-md);\n  text-align: center;\n}\n.offline-message[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  margin-right: var(--app-spacing-xs);\n}\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 50vh;\n}\n.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%] {\n  margin-bottom: var(--app-spacing-md);\n}\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: var(--ion-color-medium);\n  font-size: 0.9rem;\n}\nion-card[_ngcontent-%COMP%] {\n  margin-bottom: var(--app-spacing-md);\n}\nion-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n}\nion-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  margin-right: var(--app-spacing-sm);\n}\nion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n}\nion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  margin-right: var(--app-spacing-sm);\n  font-size: 1.2rem;\n}\nion-item[_ngcontent-%COMP%] {\n  margin-bottom: var(--app-spacing-sm);\n}\nion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%] {\n  font-weight: 500;\n}\nion-item[_ngcontent-%COMP%]   ion-note[_ngcontent-%COMP%] {\n  font-size: 0.8rem;\n}\nion-button[_ngcontent-%COMP%] {\n  margin-top: var(--app-spacing-md);\n}\nion-button[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%] {\n  margin-right: var(--app-spacing-sm);\n}\n/*# sourceMappingURL=edit-vehicle.page.css.map */"] });
    EditVehiclePage = _EditVehiclePage;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(EditVehiclePage, [{
        type: Component,
        args: [{ selector: "app-edit-vehicle", standalone: true, imports: [IonicModule, ReactiveFormsModule, CommonModule], template: `<ion-header>\r
  <ion-toolbar>\r
    <ion-title>Editar Ve\xEDculo</ion-title>\r
    <ion-buttons slot="start">\r
      <ion-back-button defaultHref="/tabs/vehicles"></ion-back-button>\r
    </ion-buttons>\r
  </ion-toolbar>\r
</ion-header>\r
\r
<ion-content class="ion-padding" *ngIf="!isLoading">\r
  <!-- Alerta de conex\xE3o com a internet -->\r
  <ion-card color="warning" *ngIf="!isOnline">\r
    <ion-card-header>\r
      <ion-card-title>\r
        <ion-icon name="wifi-outline"></ion-icon>\r
        Aten\xE7\xE3o\r
      </ion-card-title>\r
    </ion-card-header>\r
    <ion-card-content>\r
      <p>Para editar um ve\xEDculo, \xE9 necess\xE1rio estar conectado \xE0 internet para obter as marcas e modelos atualizados.</p>\r
      <p>Por favor, verifique sua conex\xE3o e tente novamente.</p>\r
    </ion-card-content>\r
  </ion-card>\r
\r
  <!-- Alerta informativo -->\r
  <ion-card color="light" *ngIf="isOnline">\r
    <ion-card-content>\r
      <ion-icon name="information-circle-outline" color="primary"></ion-icon>\r
      <span> \xC9 necess\xE1rio estar conectado \xE0 internet para obter as marcas e modelos de ve\xEDculos atualizados.</span>\r
    </ion-card-content>\r
  </ion-card>\r
\r
  <form [formGroup]="vehicleForm" (ngSubmit)="onSubmit()">\r
    <!-- Marca -->\r
    <ion-item>\r
      <ion-label position="stacked">Marca</ion-label>\r
      <ion-select\r
        #brandSelect\r
        formControlName="brand"\r
        (ionChange)="onBrandChange($event.detail.value)"\r
        interface="popover"\r
        [compareWith]="compareWith"\r
        placeholder="Selecione uma marca">\r
        <ion-select-option *ngFor="let brand of brands" [value]="brand.id">{{ brand.name }}</ion-select-option>\r
      </ion-select>\r
    </ion-item>\r
\r
    <!-- Modelo -->\r
    <ion-item>\r
      <ion-label position="stacked">Modelo</ion-label>\r
      <ion-select\r
        #modelSelect\r
        formControlName="model"\r
        (ionChange)="onModelChange($event.detail.value)"\r
        interface="popover"\r
        [compareWith]="compareWith"\r
        placeholder="Selecione um modelo">\r
        <ion-select-option *ngFor="let model of models" [value]="model.id">{{ model.name }}</ion-select-option>\r
      </ion-select>\r
    </ion-item>\r
\r
    <!-- Vers\xE3o -->\r
    <ion-item>\r
      <ion-label position="stacked">Vers\xE3o</ion-label>\r
      <ion-input formControlName="version" placeholder="Ex: 1.0 Flex"></ion-input>\r
    </ion-item>\r
\r
    <!-- Ano -->\r
    <ion-item>\r
      <ion-label position="stacked">Ano</ion-label>\r
      <ion-input formControlName="year" type="number" placeholder="Ex: 2021" min="1900" max="2099"></ion-input>\r
    </ion-item>\r
\r
    <!-- Placa -->\r
    <ion-item>\r
      <ion-label position="stacked">Placa</ion-label>\r
      <ion-input formControlName="plate" maxlength="8" placeholder="AAA-0000" inputmode="text"></ion-input>\r
      <ion-note slot="error" *ngIf="vehicleForm.get('plate')?.hasError('pattern') && vehicleForm.get('plate')?.touched">\r
        Formato inv\xE1lido. Use o formato AAA-0000\r
      </ion-note>\r
    </ion-item>\r
\r
    <!-- Ap\xF3lice (opcional) -->\r
    <ion-item>\r
      <ion-label position="stacked">N\xFAmero da Ap\xF3lice (opcional)</ion-label>\r
      <ion-input formControlName="policy" type="text"></ion-input>\r
    </ion-item>\r
\r
    <!-- Ve\xEDculo Principal -->\r
    <ion-item>\r
      <ion-label>Ve\xEDculo Principal</ion-label>\r
      <ion-checkbox formControlName="isPrimary" slot="end"></ion-checkbox>\r
    </ion-item>\r
\r
    <!-- Bot\xE3o salvar -->\r
    <ion-button expand="block" class="ion-margin-top" (click)="onSubmit()" [disabled]="vehicleForm.invalid || isSubmitting || !isOnline">\r
      <ion-spinner name="crescent" *ngIf="isSubmitting"></ion-spinner>\r
      <span *ngIf="!isSubmitting">Salvar Altera\xE7\xF5es</span>\r
    </ion-button>\r
\r
    <!-- Bot\xE3o cancelar -->\r
    <ion-button expand="block" color="medium" (click)="onCancel()" [disabled]="isSubmitting">\r
      Cancelar\r
    </ion-button>\r
\r
    <!-- Debug button (temporary) -->\r
    <ion-button expand="block" color="tertiary" (click)="debugFormValues()" fill="outline">\r
      Debug Form Values\r
    </ion-button>\r
\r
    <!-- Mensagem de erro quando offline -->\r
    <div *ngIf="!isOnline" class="offline-message">\r
      <ion-text color="danger">\r
        <p class="ion-text-center">\r
          <ion-icon name="wifi-outline"></ion-icon>\r
          N\xE3o \xE9 poss\xEDvel salvar sem conex\xE3o com a internet\r
        </p>\r
      </ion-text>\r
    </div>\r
  </form>\r
</ion-content>\r
\r
<!-- Loading spinner -->\r
<ion-content *ngIf="isLoading" class="ion-padding">\r
  <div class="loading-container">\r
    <ion-spinner name="crescent"></ion-spinner>\r
    <p>Carregando dados do ve\xEDculo...</p>\r
  </div>\r
</ion-content>`, styles: ["/* src/app/pages/tabs/vehicles/edit-vehicle/edit-vehicle.page.scss */\nform {\n  padding: 16px;\n}\nion-list {\n  margin-bottom: 16px;\n}\n.offline-message {\n  margin-top: var(--app-spacing-md);\n  text-align: center;\n}\n.offline-message ion-icon {\n  margin-right: var(--app-spacing-xs);\n}\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 50vh;\n}\n.loading-container ion-spinner {\n  margin-bottom: var(--app-spacing-md);\n}\n.loading-container p {\n  color: var(--ion-color-medium);\n  font-size: 0.9rem;\n}\nion-card {\n  margin-bottom: var(--app-spacing-md);\n}\nion-card ion-card-title {\n  display: flex;\n  align-items: center;\n}\nion-card ion-card-title ion-icon {\n  margin-right: var(--app-spacing-sm);\n}\nion-card ion-card-content {\n  display: flex;\n  align-items: center;\n}\nion-card ion-card-content ion-icon {\n  margin-right: var(--app-spacing-sm);\n  font-size: 1.2rem;\n}\nion-item {\n  margin-bottom: var(--app-spacing-sm);\n}\nion-item ion-label {\n  font-weight: 500;\n}\nion-item ion-note {\n  font-size: 0.8rem;\n}\nion-button {\n  margin-top: var(--app-spacing-md);\n}\nion-button ion-spinner {\n  margin-right: var(--app-spacing-sm);\n}\n/*# sourceMappingURL=edit-vehicle.page.css.map */\n"] }]
      }], () => [{ type: FormBuilder }, { type: ActivatedRoute }, { type: Router }, { type: VehicleService }, { type: ToastService }, { type: BrandService }, { type: ModelService }, { type: SessionService }, { type: NetworkService }, { type: ChangeDetectorRef }], { brandSelect: [{
        type: ViewChild,
        args: ["brandSelect", { static: false }]
      }], modelSelect: [{
        type: ViewChild,
        args: ["modelSelect", { static: false }]
      }] });
    })();
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(EditVehiclePage, { className: "EditVehiclePage", filePath: "src/app/pages/tabs/vehicles/edit-vehicle/edit-vehicle.page.ts", lineNumber: 27 });
    })();
  }
});
init_edit_vehicle_page();
export {
  EditVehiclePage
};
//# sourceMappingURL=edit-vehicle.page-QVEKPJC5.js.map
