{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/button-active-90f1dbc4.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as writeTask } from './index-527b9e34.js';\nimport { h as hapticSelectionEnd, a as hapticSelectionStart, b as hapticSelectionChanged } from './haptic-ac164e4c.js';\nimport { createGesture } from './index-39782642.js';\nconst createButtonActiveGesture = (el, isButton) => {\n  let currentTouchedButton;\n  let initialTouchedButton;\n  const activateButtonAtPoint = (x, y, hapticFeedbackFn) => {\n    if (typeof document === 'undefined') {\n      return;\n    }\n    const target = document.elementFromPoint(x, y);\n    if (!target || !isButton(target) || target.disabled) {\n      clearActiveButton();\n      return;\n    }\n    if (target !== currentTouchedButton) {\n      clearActiveButton();\n      setActiveButton(target, hapticFeedbackFn);\n    }\n  };\n  const setActiveButton = (button, hapticFeedbackFn) => {\n    currentTouchedButton = button;\n    if (!initialTouchedButton) {\n      initialTouchedButton = currentTouchedButton;\n    }\n    const buttonToModify = currentTouchedButton;\n    writeTask(() => buttonToModify.classList.add('ion-activated'));\n    hapticFeedbackFn();\n  };\n  const clearActiveButton = (dispatchClick = false) => {\n    if (!currentTouchedButton) {\n      return;\n    }\n    const buttonToModify = currentTouchedButton;\n    writeTask(() => buttonToModify.classList.remove('ion-activated'));\n    /**\n     * Clicking on one button, but releasing on another button\n     * does not dispatch a click event in browsers, so we\n     * need to do it manually here. Some browsers will\n     * dispatch a click if clicking on one button, dragging over\n     * another button, and releasing on the original button. In that\n     * case, we need to make sure we do not cause a double click there.\n     */\n    if (dispatchClick && initialTouchedButton !== currentTouchedButton) {\n      currentTouchedButton.click();\n    }\n    currentTouchedButton = undefined;\n  };\n  return createGesture({\n    el,\n    gestureName: 'buttonActiveDrag',\n    threshold: 0,\n    onStart: ev => activateButtonAtPoint(ev.currentX, ev.currentY, hapticSelectionStart),\n    onMove: ev => activateButtonAtPoint(ev.currentX, ev.currentY, hapticSelectionChanged),\n    onEnd: () => {\n      clearActiveButton(true);\n      hapticSelectionEnd();\n      initialTouchedButton = undefined;\n    }\n  });\n};\nexport { createButtonActiveGesture as c };"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,IAMM;AANN;AAAA;AAAA;AAGA;AACA;AACA;AACA,IAAM,4BAA4B,CAAC,IAAI,aAAa;AAClD,UAAI;AACJ,UAAI;AACJ,YAAM,wBAAwB,CAAC,GAAG,GAAG,qBAAqB;AACxD,YAAI,OAAO,aAAa,aAAa;AACnC;AAAA,QACF;AACA,cAAM,SAAS,SAAS,iBAAiB,GAAG,CAAC;AAC7C,YAAI,CAAC,UAAU,CAAC,SAAS,MAAM,KAAK,OAAO,UAAU;AACnD,4BAAkB;AAClB;AAAA,QACF;AACA,YAAI,WAAW,sBAAsB;AACnC,4BAAkB;AAClB,0BAAgB,QAAQ,gBAAgB;AAAA,QAC1C;AAAA,MACF;AACA,YAAM,kBAAkB,CAAC,QAAQ,qBAAqB;AACpD,+BAAuB;AACvB,YAAI,CAAC,sBAAsB;AACzB,iCAAuB;AAAA,QACzB;AACA,cAAM,iBAAiB;AACvB,kBAAU,MAAM,eAAe,UAAU,IAAI,eAAe,CAAC;AAC7D,yBAAiB;AAAA,MACnB;AACA,YAAM,oBAAoB,CAAC,gBAAgB,UAAU;AACnD,YAAI,CAAC,sBAAsB;AACzB;AAAA,QACF;AACA,cAAM,iBAAiB;AACvB,kBAAU,MAAM,eAAe,UAAU,OAAO,eAAe,CAAC;AAShE,YAAI,iBAAiB,yBAAyB,sBAAsB;AAClE,+BAAqB,MAAM;AAAA,QAC7B;AACA,+BAAuB;AAAA,MACzB;AACA,aAAO,cAAc;AAAA,QACnB;AAAA,QACA,aAAa;AAAA,QACb,WAAW;AAAA,QACX,SAAS,QAAM,sBAAsB,GAAG,UAAU,GAAG,UAAU,oBAAoB;AAAA,QACnF,QAAQ,QAAM,sBAAsB,GAAG,UAAU,GAAG,UAAU,sBAAsB;AAAA,QACpF,OAAO,MAAM;AACX,4BAAkB,IAAI;AACtB,6BAAmB;AACnB,iCAAuB;AAAA,QACzB;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}