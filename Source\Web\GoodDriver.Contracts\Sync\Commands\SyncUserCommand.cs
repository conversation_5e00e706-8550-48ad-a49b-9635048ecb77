﻿﻿using Rogerio.Cqrs.Commands;
using System.ComponentModel.DataAnnotations;

namespace GoodDriver.Contracts.Sync.Commands
{
    public class SyncUserCommand : ICommand
    {
        [Required]
        public string UserId { get; set; }
        
        [Required]
        public string Name { get; set; }
        
        //[Required]
        //[EmailAddress]
        //public string Email { get; set; }
        
        //public string Phone { get; set; }
        
        //public string Document { get; set; }
        
        //public string BirthDate { get; set; }
        
        public string DeviceId { get; set; }
        
        public string DeviceModel { get; set; }
        
        public string DeviceOS { get; set; }
        
        public string AppVersion { get; set; }
        
        [Required]
        public string SyncStatus { get; set; }
        
        public DateTime CreatedOn { get; set; } = DateTime.Now;
        
        public DateTime? UpdatedOn { get; set; }
    }
}
