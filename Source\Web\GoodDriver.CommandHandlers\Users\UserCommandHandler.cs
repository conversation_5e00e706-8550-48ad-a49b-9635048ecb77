﻿using GoodDriver.Contracts.Users.Commands;
using GoodDriver.Contracts.Users.Requests;
using GoodDriver.Contracts.Users.Responses;
using GoodDriver.Domain.Users;
using Rogerio.Commom.Exceptions;
using Rogerio.Cqrs.Commands;
using Rogerio.Data;


namespace GoodDriver.CommandHandlers.Users
{
    public class UserCommandHandler : ICommandHandler<UserCreateCommand>,
                                      ICommandHandler<UserLogonRequest, UserLogonResponse>
    {
        private readonly IUserRepository repository;
        private readonly IUnitOfWorkFactory unitOfWorkFactory;
        private readonly IUserFactory userFactory;

        public UserCommandHandler(IUserRepository repository,
            IUnitOfWorkFactory unitOfWorkFactory, IUserFactory userFactory)
        {
            this.unitOfWorkFactory = unitOfWorkFactory ?? throw new ArgumentNullException("unitOfWorkFactory");
            this.repository = repository ?? throw new ArgumentNullException("repository");
            this.userFactory = userFactory ?? throw new ArgumentNullException("userFactory");
        }

        public async Task HandleAsync(UserCreateCommand command)
        {
            if (command == null)
                throw new ArgumentException(nameof(command), command?.GetType().FullName);

            using (var unitOfWork = unitOfWorkFactory.Get())
            {
                //var cpf = Common.Util.UnMaskDocument(command.CPF);
                var existingUserByEmail = await repository.GetAsyncBy(user => user.Email == command.Email);

                if (existingUserByEmail != null)
                {
                    throw new BusinessException("UserCreateCommand", "Já existe um usuário cadastrado com esse endereço de email.");
                }
                else
                {
                    var user = await this.userFactory.CreateAsync(command);
                    await repository.AddAsync(user);
                }
                unitOfWork.Complete();
            }
        }

        public async Task<UserLogonResponse> HandleAsync(UserLogonRequest request)
        {
            var user = await repository.GetAsyncBy(a => a.Email == request.Email);
            if (user == null) throw new BusinessException("User.NotFound", "Usuário não encontrado ou inexistente.");
            if (!user.Authenticate(request.Email, request.Password)) throw new BusinessException("User.Invalid", "Usuário e/ou Senha inválido(s).");

            //if (!user.CanLogOn())
            //    throw new BusinessException("User.NotActive", "Usuário não está ativo. Por favor, consulte sua caixa de E-mail para ativar sua conta.");

            await repository.UpdateAsync(user);

            var response = new UserLogonResponse(true, user.Id, user.Name, user.Email, user.ImageUrl);

            return response;
        }
    }
}
