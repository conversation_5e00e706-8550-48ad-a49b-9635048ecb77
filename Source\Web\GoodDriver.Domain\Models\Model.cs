﻿using GoodDriver.Domain.Brands;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Domain.Models
{
	public class Model 
	{
        public Model()
        {
			this.CreatedOn = DateTime.Now;
		}

        public Model(int id)
        {
            this.Id = id;
        }

        public Model(string name, Brand brand) : this()
		{
			this.Name = name;
			this.Brand = brand;
		}

		public int Id { get; private set; }

		public string Name { get; private set; }

        /// <summary>
		/// 
		/// </summary>
		public string ReferenceCode { get; private set; }
        public string ReferenceMonth { get; private set; }
        public string ShortName { get; private set; }


        public Brand Brand { get; private set; }

		public DateTime CreatedOn { get; set; }

		public DateTime UpdatedOn { get; set; }

		void Update(string name, Brand brand)
		{
			if(string.IsNullOrEmpty(name))
				throw new ArgumentNullException(nameof(name));
			if (brand == null)
				throw new ArgumentNullException(nameof(brand));
			this.Name = name;
			this.Brand = brand;

			this.UpdatedOn = DateTime.Now;
		}

	}
}
