﻿using GoodDriver.SetupDomain.Common;
using Rogerio.NHibernate;

namespace GoodDriver.SetupDomain.Stores
{
	public class CallContextUnitOfWorkStore : IUnitOfWorkStore
    {
        public void Store(string alias, IUnitOfWork unitOfWork)
        {
            if (unitOfWork == null)
            {
                throw new ArgumentNullException("unitOfWork");
            }

            if (string.IsNullOrEmpty(alias))
            {
                throw new ArgumentNullException("alias");
            }

            CallContext.SetData(alias, unitOfWork);
        }

        public void Remove(string alias)
        {
            if (string.IsNullOrEmpty(alias))
            {
                throw new ArgumentNullException("alias");
            }

            CallContext.SetData(alias, null);
        }

        public IUnitOfWork Get(string alias)
        {
            if (string.IsNullOrEmpty(alias))
            {
                throw new ArgumentNullException("alias");
            }

            return CallContext.GetData(alias) as IUnitOfWork;
        }
    }
}
