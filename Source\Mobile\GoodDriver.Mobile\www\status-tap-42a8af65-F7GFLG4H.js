import {
  findClosestIonContent,
  init_index_9a17db3d,
  scrollToTop
} from "./chunk-L6RBCVAP.js";
import {
  init_index_527b9e34,
  readTask,
  writeTask
} from "./chunk-V6QEVD67.js";
import {
  componentOnReady,
  init_helpers_d94bc8ad
} from "./chunk-WNPNB2PX.js";
import {
  init_index_cfd9c1f2
} from "./chunk-6SXGDLUH.js";
import {
  __async,
  __esm
} from "./chunk-4W6HR7MY.js";

// node_modules/@ionic/core/dist/esm/status-tap-42a8af65.js
var startStatusTap;
var init_status_tap_42a8af65 = __esm({
  "node_modules/@ionic/core/dist/esm/status-tap-42a8af65.js"() {
    init_index_527b9e34();
    init_index_9a17db3d();
    init_helpers_d94bc8ad();
    init_index_cfd9c1f2();
    startStatusTap = () => {
      const win = window;
      win.addEventListener("statusTap", () => {
        readTask(() => {
          const width = win.innerWidth;
          const height = win.innerHeight;
          const el = document.elementFromPoint(width / 2, height / 2);
          if (!el) {
            return;
          }
          const contentEl = findClosestIonContent(el);
          if (contentEl) {
            new Promise((resolve) => componentOnReady(contentEl, resolve)).then(() => {
              writeTask(() => __async(null, null, function* () {
                contentEl.style.setProperty("--overflow", "hidden");
                yield scrollToTop(contentEl, 300);
                contentEl.style.removeProperty("--overflow");
              }));
            });
          }
        });
      });
    };
  }
});
init_status_tap_42a8af65();
export {
  startStatusTap
};
/*! Bundled license information:

@ionic/core/dist/esm/status-tap-42a8af65.js:
  (*!
   * (C) Ionic http://ionicframework.com - MIT License
   *)
*/
//# sourceMappingURL=status-tap-42a8af65-F7GFLG4H.js.map
