import {
  CommonModule,
  Component,
  IonBadge,
  IonButton,
  IonContent,
  IonHeader,
  IonIcon,
  IonItem,
  IonItemOption,
  IonItemOptions,
  IonItemSliding,
  IonLabel,
  IonList,
  IonRefresher,
  IonRefresherContent,
  IonSpinner,
  IonText,
  IonTitle,
  IonToolbar,
  IonicModule,
  NgForOf,
  NgIf,
  RouterLink,
  RouterLinkDelegateDirective2 as RouterLinkDelegateDirective,
  RouterModule,
  SessionService,
  ToastService,
  VehicleService,
  VehicleStateService,
  init_common,
  init_core,
  init_ionic_angular,
  init_router,
  init_session_service,
  init_toast_service,
  init_vehicle_service,
  init_vehicle_state_service,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵpureFunction1,
  ɵɵreference,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtemplateRefExtractor,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-2TGB3XS3.js";
import "./chunk-GJIVKQBF.js";
import "./chunk-WY354WYL.js";
import "./chunk-3TAP6XVY.js";
import "./chunk-BQEXAUCY.js";
import "./chunk-EUHNYIV3.js";
import "./chunk-AY6ZGCP5.js";
import "./chunk-I3UCYTIQ.js";
import "./chunk-4JI7VCBZ.js";
import "./chunk-YY7NCLAA.js";
import "./chunk-GUQ63TTB.js";
import "./chunk-CHFZ6R2P.js";
import "./chunk-XPKADKFH.js";
import "./chunk-V6QEVD67.js";
import "./chunk-WNPNB2PX.js";
import "./chunk-NRG74LSH.js";
import "./chunk-YSMEPS6E.js";
import "./chunk-NHEUXSZ2.js";
import "./chunk-WX4TOLMF.js";
import "./chunk-6SXGDLUH.js";
import "./chunk-NNIHVF7T.js";
import "./chunk-ZNALOMZJ.js";
import "./chunk-GHA2GP6C.js";
import "./chunk-H2CU6QHT.js";
import "./chunk-6PMAHZVS.js";
import "./chunk-DOEZTQY3.js";
import "./chunk-O4BCOVEV.js";
import "./chunk-TD4SIQNM.js";
import "./chunk-VU5YACRM.js";
import {
  __async,
  __esm
} from "./chunk-4W6HR7MY.js";

// src/app/pages/tabs/vehicles/list-vehicles/list-vehicles.page.ts
function ListVehiclesPage_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 6);
    \u0275\u0275element(1, "ion-spinner");
    \u0275\u0275elementEnd();
  }
}
function ListVehiclesPage_ion_list_10_ion_item_sliding_1_ion_badge_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-badge", 13);
    \u0275\u0275text(1, "Principal");
    \u0275\u0275elementEnd();
  }
}
function ListVehiclesPage_ion_list_10_ion_item_sliding_1_ion_item_option_14_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "ion-item-option", 14);
    \u0275\u0275listener("click", function ListVehiclesPage_ion_list_10_ion_item_sliding_1_ion_item_option_14_Template_ion_item_option_click_0_listener() {
      \u0275\u0275restoreView(_r2);
      const vehicle_r3 = \u0275\u0275nextContext().$implicit;
      const ctx_r3 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r3.setPrimaryVehicle(vehicle_r3.id));
    });
    \u0275\u0275element(1, "ion-icon", 15);
    \u0275\u0275text(2, " Principal ");
    \u0275\u0275elementEnd();
  }
}
function ListVehiclesPage_ion_list_10_ion_item_sliding_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-item-sliding")(1, "ion-item", 8)(2, "ion-label")(3, "div", 9)(4, "h2");
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275template(6, ListVehiclesPage_ion_list_10_ion_item_sliding_1_ion_badge_6_Template, 2, 0, "ion-badge", 10);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "p")(8, "b");
    \u0275\u0275text(9);
    \u0275\u0275elementEnd();
    \u0275\u0275text(10);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "p");
    \u0275\u0275text(12);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(13, "ion-item-options", 11);
    \u0275\u0275template(14, ListVehiclesPage_ion_list_10_ion_item_sliding_1_ion_item_option_14_Template, 3, 0, "ion-item-option", 12);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const vehicle_r3 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(7, _c0, vehicle_r3.id));
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(vehicle_r3.plate);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", vehicle_r3.isPrimary);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(vehicle_r3.brandName || "Marca n\xE3o especificada");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" - ", vehicle_r3.modelName || "Modelo n\xE3o especificado", "");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("Ano: ", vehicle_r3.year, "");
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", !vehicle_r3.isPrimary);
  }
}
function ListVehiclesPage_ion_list_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-list");
    \u0275\u0275template(1, ListVehiclesPage_ion_list_10_ion_item_sliding_1_Template, 15, 9, "ion-item-sliding", 7);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r3 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r3.vehicles);
  }
}
function ListVehiclesPage_ng_template_11_ion_text_0_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-text", 17)(1, "p", 18);
    \u0275\u0275text(2, "Nenhum ve\xEDculo cadastrado.");
    \u0275\u0275elementEnd()();
  }
}
function ListVehiclesPage_ng_template_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, ListVehiclesPage_ng_template_11_ion_text_0_Template, 3, 0, "ion-text", 16);
  }
  if (rf & 2) {
    const ctx_r3 = \u0275\u0275nextContext();
    \u0275\u0275property("ngIf", !ctx_r3.isLoading);
  }
}
var _c0, _ListVehiclesPage, ListVehiclesPage;
var init_list_vehicles_page = __esm({
  "src/app/pages/tabs/vehicles/list-vehicles/list-vehicles.page.ts"() {
    init_core();
    init_ionic_angular();
    init_common();
    init_router();
    init_core();
    init_vehicle_service();
    init_session_service();
    init_toast_service();
    init_vehicle_state_service();
    init_ionic_angular();
    init_common();
    init_router();
    _c0 = (a0) => ["/vehicle-details", a0];
    _ListVehiclesPage = class _ListVehiclesPage {
      constructor(vehicleService, sessionService, toastService, vehicleStateService) {
        this.vehicleService = vehicleService;
        this.sessionService = sessionService;
        this.toastService = toastService;
        this.vehicleStateService = vehicleStateService;
        this.vehicles = [];
        this.isLoading = false;
        this.subscriptions = [];
      }
      ngOnInit() {
        this.loadVehicles();
        this.subscriptions.push(this.vehicleStateService.vehicles$.subscribe((vehicles) => {
          if (vehicles.length > 0) {
            this.vehicles = vehicles;
          }
        }));
      }
      ngOnDestroy() {
        this.subscriptions.forEach((sub) => sub.unsubscribe());
      }
      loadVehicles() {
        return __async(this, null, function* () {
          this.isLoading = true;
          const userId = (yield this.sessionService.getUserId()) || "";
          try {
            this.vehicles = yield this.vehicleService.listAllLocal(userId);
          } catch (error) {
            console.error("Error loading vehicles:", error);
            this.toastService.showToast("Erro ao carregar ve\xEDculos", "danger");
          } finally {
            this.isLoading = false;
          }
        });
      }
      /**
       * Sets a vehicle as the primary vehicle
       * @param vehicleId The ID of the vehicle to set as primary
       */
      setPrimaryVehicle(vehicleId) {
        return __async(this, null, function* () {
          const userId = (yield this.sessionService.getUserId()) || "";
          try {
            const success = yield this.vehicleService.setPrimaryVehicle(vehicleId, userId);
            if (success) {
              this.toastService.showToast("Ve\xEDculo definido como principal");
            } else {
              this.toastService.showToast("Erro ao definir ve\xEDculo como principal", "danger");
            }
          } catch (error) {
            console.error("Error setting primary vehicle:", error);
            this.toastService.showToast("Erro ao definir ve\xEDculo como principal", "danger");
          }
        });
      }
      doRefresh(event) {
        return __async(this, null, function* () {
          yield this.loadVehicles();
          event.target.complete();
        });
      }
    };
    _ListVehiclesPage.\u0275fac = function ListVehiclesPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ListVehiclesPage)(\u0275\u0275directiveInject(VehicleService), \u0275\u0275directiveInject(SessionService), \u0275\u0275directiveInject(ToastService), \u0275\u0275directiveInject(VehicleStateService));
    };
    _ListVehiclesPage.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ListVehiclesPage, selectors: [["app-list-vehicles"]], decls: 13, vars: 3, consts: [["noVehicles", ""], [1, "ion-padding"], ["slot", "fixed", 3, "ionRefresh"], ["expand", "block", "color", "primary", "routerLink", "/new-vehicle"], ["class", "ion-text-center ion-padding", 4, "ngIf"], [4, "ngIf", "ngIfElse"], [1, "ion-text-center", "ion-padding"], [4, "ngFor", "ngForOf"], ["detail", "true", 3, "routerLink"], [1, "vehicle-header"], ["color", "success", 4, "ngIf"], ["side", "end"], ["color", "primary", 3, "click", 4, "ngIf"], ["color", "success"], ["color", "primary", 3, "click"], ["slot", "icon-only", "name", "star"], ["color", "medium", 4, "ngIf"], ["color", "medium"], [1, "ion-text-center"]], template: function ListVehiclesPage_Template(rf, ctx) {
      if (rf & 1) {
        const _r1 = \u0275\u0275getCurrentView();
        \u0275\u0275elementStart(0, "ion-header")(1, "ion-toolbar")(2, "ion-title");
        \u0275\u0275text(3, "Ve\xEDculos");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(4, "ion-content", 1)(5, "ion-refresher", 2);
        \u0275\u0275listener("ionRefresh", function ListVehiclesPage_Template_ion_refresher_ionRefresh_5_listener($event) {
          \u0275\u0275restoreView(_r1);
          return \u0275\u0275resetView(ctx.doRefresh($event));
        });
        \u0275\u0275element(6, "ion-refresher-content");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(7, "ion-button", 3);
        \u0275\u0275text(8, " Cadastrar Novo Ve\xEDculo ");
        \u0275\u0275elementEnd();
        \u0275\u0275template(9, ListVehiclesPage_div_9_Template, 2, 0, "div", 4)(10, ListVehiclesPage_ion_list_10_Template, 2, 1, "ion-list", 5)(11, ListVehiclesPage_ng_template_11_Template, 1, 1, "ng-template", null, 0, \u0275\u0275templateRefExtractor);
        \u0275\u0275elementEnd();
      }
      if (rf & 2) {
        const noVehicles_r5 = \u0275\u0275reference(12);
        \u0275\u0275advance(9);
        \u0275\u0275property("ngIf", ctx.isLoading);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", (ctx.vehicles == null ? null : ctx.vehicles.length) && !ctx.isLoading)("ngIfElse", noVehicles_r5);
      }
    }, dependencies: [IonicModule, IonBadge, IonButton, IonContent, IonHeader, IonIcon, IonItem, IonItemOption, IonItemOptions, IonItemSliding, IonLabel, IonList, IonRefresher, IonRefresherContent, IonSpinner, IonText, IonTitle, IonToolbar, RouterLinkDelegateDirective, CommonModule, NgForOf, NgIf, RouterModule, RouterLink], styles: ["\n\n.vehicle-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 4px;\n}\n.vehicle-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  margin: 0;\n}\n.vehicle-header[_ngcontent-%COMP%]   ion-badge[_ngcontent-%COMP%] {\n  font-size: 12px;\n  padding: 4px 8px;\n}\nion-item-sliding[_ngcontent-%COMP%] {\n  margin-bottom: 8px;\n}\n/*# sourceMappingURL=list-vehicles.page.css.map */"] });
    ListVehiclesPage = _ListVehiclesPage;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ListVehiclesPage, [{
        type: Component,
        args: [{ selector: "app-list-vehicles", standalone: true, imports: [IonicModule, CommonModule, RouterModule], template: `<!-- src/app/pages/tabs/vehicles/list-vehicles/list-vehicles.page.html -->
<ion-header>
  <ion-toolbar>
    <ion-title>Ve\xEDculos</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
  <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <ion-button expand="block" color="primary" routerLink="/new-vehicle">
    Cadastrar Novo Ve\xEDculo
  </ion-button>

  <!-- Indicador de carregamento -->
  <div *ngIf="isLoading" class="ion-text-center ion-padding">
    <ion-spinner></ion-spinner>
  </div>

  <!-- Lista de ve\xEDculos-->
  <ion-list *ngIf="vehicles?.length && !isLoading; else noVehicles">
    <ion-item-sliding *ngFor="let vehicle of vehicles">
      <ion-item [routerLink]="['/vehicle-details', vehicle.id]" detail="true">
        <ion-label>
          <div class="vehicle-header">
            <h2>{{ vehicle.plate }}</h2>
            <ion-badge color="success" *ngIf="vehicle.isPrimary">Principal</ion-badge>
          </div>
          <p><b>{{ vehicle.brandName || 'Marca n\xE3o especificada' }}</b> - {{ vehicle.modelName || 'Modelo n\xE3o especificado' }}</p>
          <p>Ano: {{ vehicle.year }}</p>
        </ion-label>
      </ion-item>

      <ion-item-options side="end">
        <ion-item-option color="primary" *ngIf="!vehicle.isPrimary" (click)="setPrimaryVehicle(vehicle.id)">
          <ion-icon slot="icon-only" name="star"></ion-icon>
          Principal
        </ion-item-option>
      </ion-item-options>
    </ion-item-sliding>
  </ion-list>

  <!-- Mensagem caso n\xE3o existam ve\xEDculos -->
  <ng-template #noVehicles>
    <ion-text color="medium" *ngIf="!isLoading">
      <p class="ion-text-center">Nenhum ve\xEDculo cadastrado.</p>
    </ion-text>
  </ng-template>
</ion-content>`, styles: ["/* src/app/pages/tabs/vehicles/list-vehicles/list-vehicles.page.scss */\n.vehicle-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 4px;\n}\n.vehicle-header h2 {\n  margin: 0;\n}\n.vehicle-header ion-badge {\n  font-size: 12px;\n  padding: 4px 8px;\n}\nion-item-sliding {\n  margin-bottom: 8px;\n}\n/*# sourceMappingURL=list-vehicles.page.css.map */\n"] }]
      }], () => [{ type: VehicleService }, { type: SessionService }, { type: ToastService }, { type: VehicleStateService }], null);
    })();
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ListVehiclesPage, { className: "ListVehiclesPage", filePath: "src/app/pages/tabs/vehicles/list-vehicles/list-vehicles.page.ts", lineNumber: 19 });
    })();
  }
});
init_list_vehicles_page();
export {
  ListVehiclesPage
};
//# sourceMappingURL=list-vehicles.page-YTPD2FGE.js.map
