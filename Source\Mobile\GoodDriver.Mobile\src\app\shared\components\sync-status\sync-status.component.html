<ion-card>
  <ion-card-header>
    <ion-card-title>
      <ion-icon [name]="getSyncStatusIcon()" [color]="getSyncStatusColor()"></ion-icon>
      Status de Sincronização
    </ion-card-title>
  </ion-card-header>
  <ion-card-content>
    <ion-item lines="none">
      <ion-icon slot="start" [name]="getSyncStatusIcon()" [color]="getSyncStatusColor()"></ion-icon>
      <ion-label>
        <h2>{{ syncInProgress ? 'Sincronizando...' : (pendingSyncCount > 0 ? 'Pendente' : 'Sincronizado') }}</h2>
        <p>{{ getSyncStatusMessage() }}</p>
      </ion-label>
      <ion-spinner *ngIf="syncInProgress" name="crescent" slot="end"></ion-spinner>
    </ion-item>
    
    <ion-progress-bar *ngIf="syncInProgress" type="indeterminate" [color]="getSyncStatusColor()"></ion-progress-bar>
    
    <ion-item lines="none">
      <ion-icon slot="start" name="time-outline" color="medium"></ion-icon>
      <ion-label>
        <h2>Última sincronização</h2>
        <p>{{ getLastSyncTimeFormatted() }}</p>
      </ion-label>
    </ion-item>
  </ion-card-content>
</ion-card>
