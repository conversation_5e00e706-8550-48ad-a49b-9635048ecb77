﻿using GoodDriver.ReadModels.Brands.Result;
using GoodDriver.ReadModels.Models._2_Result;
using Rogerio.Cqrs.Requests;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.ReadModels.Models._1_Query
{
    public class ModelListQuery : IRequest<ModelListResult>
    {
        public ModelListQuery(int brandId)
        {
            this.BrandId = brandId;
        }

        public int BrandId { get; private set; }
    }
}
