﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Rogerio.Commom.Exceptions;
using Rogerio.Security.Domain;
using System.Security;
using System.Security.Claims;

namespace GoodDriver.API.Controllers
{
	public class BaseControllerGoodDriver : Rogerio.Mvc.ComumController
	{
		protected readonly ISecurityManager securityManager;

		public BaseControllerGoodDriver(ISecurityManager securityManager)
		{
			if (securityManager == null) throw new ArgumentNullException("securityManager");

			this.securityManager = securityManager;
		}

		public new CustomPrincipal User
		{
			get
			{
				return base.User as CustomPrincipal;
			}
		}

		//protected string GetUserId()
		//{
		//	if (this.User != null && !string.IsNullOrEmpty(this.User.Id))
		//		return this.User.Id;

		//	return string.Empty;
		//}

		//protected string GetCurrentCompanyId()
		//{
		//	if (this.User != null)
		//	{
		//		string companyId = this.User.GetValue("CompanyId");
		//		if (!string.IsNullOrEmpty(companyId))
		//			return companyId;
		//	}

		//	return string.Empty;
		//}

		//protected IList<UserCompanyModel> GetCompanies()
		//{
		//	if (this.User != null)
		//	{
		//		string companies = this.User.GetValue("Companies");
		//		if (!String.IsNullOrEmpty(companies))
		//			return JsonConvert.DeserializeObject<IList<UserCompanyModel>>(companies);
		//	}

		//	return new List<UserCompanyModel>();
		//}

		public override void OnActionExecuted(ActionExecutedContext context)
		{
			base.OnActionExecuted(context);

			if (context.Exception != null && context.Exception is HttpException)
			{
				var exception = context.Exception as HttpException;
				if (exception.StatusCode == System.Net.HttpStatusCode.Forbidden)
				{
					context.ExceptionHandled = true;
					context.Result = Forbid();
				}
			}
		}
	}
}
