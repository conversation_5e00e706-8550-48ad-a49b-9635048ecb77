{"version": 3, "sources": ["src/app/core/services/brand.service.ts", "src/app/core/services/model.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { ListBrandResponseDto } from '../dtos/brand/list-brand-responseDto';\r\nimport { Brand } from '../models/brand.model';\r\nimport { ApiService } from './api.service';\r\n\r\n\r\n@Injectable({\r\n    providedIn: 'root'\r\n  })\r\nexport class BrandService\r\n{\r\n    constructor(\r\n      private http: HttpClient,\r\n      private apiService: ApiService\r\n    ) {}\r\n\r\n    async listAll(): Promise<Brand[]> {\r\n        try {\r\n          const url = this.apiService.getUrl('brands');\r\n          const result = await this.http.get<ListBrandResponseDto[]>(url).toPromise();\r\n\r\n          if (!result) return [];\r\n\r\n          // Aqui você pode usar o map na resposta real (os dados que você recebe)\r\n          const brands: Brand[] = result.map((data: any) => {\r\n            return {\r\n              id: data.id,\r\n              name: data.name,\r\n              ico: data.ico\r\n            };\r\n          });\r\n\r\n          return brands;\r\n        } catch (error) {\r\n          console.error('Error fetching brands:', error);\r\n          return [];\r\n        }\r\n      }\r\n\r\n}", "import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { ListModelRequestDto } from '../dtos/model/list-model-requestDto';\r\nimport { ListModelResponseDto } from '../dtos/model/list-model-responseDto';\r\nimport { Model } from '../models/model.model';\r\nimport { ApiService } from './api.service';\r\n\r\n@Injectable({\r\n    providedIn: 'root'\r\n  })\r\nexport class ModelService\r\n{\r\n    constructor(\r\n      private http: HttpClient,\r\n      private apiService: ApiService\r\n    ) {}\r\n\r\n    async getByBranch(brandId: ListModelRequestDto): Promise<Model[]>\r\n    {\r\n        try {\r\n            console.log('ModelService.getByBranch called with:', brandId);\r\n            const url = this.apiService.getUrl('models', { brandId: brandId.brandId.toString() });\r\n            console.log('Generated URL:', url);\r\n            const result = await firstValueFrom(this.http.get<ListModelResponseDto[]>(url));\r\n\r\n            if (!result) return [];\r\n\r\n            const models: Model[] = result.map((data: any) => {\r\n                return {\r\n                    id: data.id,\r\n                    name: data.name,\r\n                    referenceCode: data.referenceCode,\r\n                    referenceMonth: data.referenceMonth,\r\n                    shortName: data.shortName,\r\n                    brandId: data.brandId,\r\n                };\r\n            });\r\n\r\n            return models;\r\n        }\r\n        catch (error) {\r\n            console.error('Error fetching models:', error);\r\n            return [];\r\n        }\r\n    }\r\n    }\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AACA,IASa;AATb;;;;;;;AASM,IAAO,gBAAP,MAAO,cAAY;MAErB,YACU,MACA,YAAsB;AADtB,aAAA,OAAA;AACA,aAAA,aAAA;MACP;MAEG,UAAO;;AACT,cAAI;AACF,kBAAM,MAAM,KAAK,WAAW,OAAO,QAAQ;AAC3C,kBAAM,SAAS,MAAM,KAAK,KAAK,IAA4B,GAAG,EAAE,UAAS;AAEzE,gBAAI,CAAC;AAAQ,qBAAO,CAAA;AAGpB,kBAAM,SAAkB,OAAO,IAAI,CAAC,SAAa;AAC/C,qBAAO;gBACL,IAAI,KAAK;gBACT,MAAM,KAAK;gBACX,KAAK,KAAK;;YAEd,CAAC;AAED,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,0BAA0B,KAAK;AAC7C,mBAAO,CAAA;UACT;QACF;;;;uCA5BO,eAAY,mBAAA,UAAA,GAAA,mBAAA,UAAA,CAAA;IAAA;qFAAZ,eAAY,SAAZ,cAAY,WAAA,YAFT,OAAM,CAAA;AAEhB,IAAO,eAAP;;0EAAO,cAAY,CAAA;cAHxB;eAAW;UACR,YAAY;SACb;;;;;;;ACRH,IAUa;AAVb;;;;AACA;;;;AASM,IAAO,gBAAP,MAAO,cAAY;MAErB,YACU,MACA,YAAsB;AADtB,aAAA,OAAA;AACA,aAAA,aAAA;MACP;MAEG,YAAY,SAA4B;;AAE1C,cAAI;AACA,oBAAQ,IAAI,yCAAyC,OAAO;AAC5D,kBAAM,MAAM,KAAK,WAAW,OAAO,UAAU,EAAE,SAAS,QAAQ,QAAQ,SAAQ,EAAE,CAAE;AACpF,oBAAQ,IAAI,kBAAkB,GAAG;AACjC,kBAAM,SAAS,MAAM,eAAe,KAAK,KAAK,IAA4B,GAAG,CAAC;AAE9E,gBAAI,CAAC;AAAQ,qBAAO,CAAA;AAEpB,kBAAM,SAAkB,OAAO,IAAI,CAAC,SAAa;AAC7C,qBAAO;gBACH,IAAI,KAAK;gBACT,MAAM,KAAK;gBACX,eAAe,KAAK;gBACpB,gBAAgB,KAAK;gBACrB,WAAW,KAAK;gBAChB,SAAS,KAAK;;YAEtB,CAAC;AAED,mBAAO;UACX,SACO,OAAO;AACV,oBAAQ,MAAM,0BAA0B,KAAK;AAC7C,mBAAO,CAAA;UACX;QACJ;;;;uCAlCS,eAAY,mBAAA,UAAA,GAAA,mBAAA,UAAA,CAAA;IAAA;qFAAZ,eAAY,SAAZ,cAAY,WAAA,YAFT,OAAM,CAAA;AAEhB,IAAO,eAAP;;0EAAO,cAAY,CAAA;cAHxB;eAAW;UACR,YAAY;SACb;;;;;", "names": []}