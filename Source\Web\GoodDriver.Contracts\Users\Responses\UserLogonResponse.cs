﻿using Rogerio.Cqrs.Commands;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Contracts.Users.Responses
{
    public class UserLogonResponse : ICommandResult
    {
        public UserLogonResponse()
        {
            
        }

        public UserLogonResponse(bool sucess, string message) : this()
        {
            this.Success = sucess;
            this.Message = message;
        }

        public UserLogonResponse(bool sucess, string userId, string name, string email, string imageUrl) : this()
        {
            this.Success= sucess;
            this.UserId = userId;
            this.UserName = name;
            this.UserEmail = email;
            this.UserImageUrl = imageUrl;
        }


        public bool Success { get; set; }

        public string Message { get; set; }

        public string UserId { get; set; }

        public string UserName { get; set; }

        public string UserEmail { get; set; }

        public string UserImageUrl { get; set; }
    }
}
