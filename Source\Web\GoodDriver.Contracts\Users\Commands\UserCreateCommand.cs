﻿using Rogerio.Cqrs.Commands;

namespace GoodDriver.Contracts.Users.Commands
{
	public class UserCreateCommand : ICommand
	{
        protected UserCreateCommand()
        {
            
        }

		public UserCreateCommand(string name, string phone,
			string email, string password, string passwordConfirm, bool informationResponsibilityChecked) : this()
		{
			this.Name = name;
			this.Password = password;
			this.PasswordConfirm = passwordConfirm;
			this.Phone = phone;
			this.Email = email;
			this.InformationResponsibilityChecked = informationResponsibilityChecked;
		}
		
		public string Name { get; set; }
		public string Email { get; set; }
		public string Phone { get; set; }
		public string Password { get; set; }
		public string PasswordConfirm { get; set; }
		public bool InformationResponsibilityChecked { get; set; }
	}
}
