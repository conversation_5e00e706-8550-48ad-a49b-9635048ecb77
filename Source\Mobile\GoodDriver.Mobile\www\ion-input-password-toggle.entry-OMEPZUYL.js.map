{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-input-password-toggle.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { p as printIonWarning } from './index-cfd9c1f2.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { x as eye, y as eyeOff } from './index-e2cf2ceb.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nconst iosInputPasswordToggleCss = \"\";\nconst IonInputPasswordToggleIosStyle0 = iosInputPasswordToggleCss;\nconst mdInputPasswordToggleCss = \"\";\nconst IonInputPasswordToggleMdStyle0 = mdInputPasswordToggleCss;\nconst InputPasswordToggle = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.togglePasswordVisibility = () => {\n      const {\n        inputElRef\n      } = this;\n      if (!inputElRef) {\n        return;\n      }\n      inputElRef.type = inputElRef.type === 'text' ? 'password' : 'text';\n    };\n    this.color = undefined;\n    this.showIcon = undefined;\n    this.hideIcon = undefined;\n    this.type = 'password';\n  }\n  /**\n   * Whenever the input type changes we need to re-run validation to ensure the password\n   * toggle is being used with the correct input type. If the application changes the type\n   * outside of this component we also need to re-render so the correct icon is shown.\n   */\n  onTypeChange(newValue) {\n    if (newValue !== 'text' && newValue !== 'password') {\n      printIonWarning(`[ion-input-password-toggle] - Only inputs of type \"text\" or \"password\" are supported. Input of type \"${newValue}\" is not compatible.`, this.el);\n      return;\n    }\n  }\n  connectedCallback() {\n    const {\n      el\n    } = this;\n    const inputElRef = this.inputElRef = el.closest('ion-input');\n    if (!inputElRef) {\n      printIonWarning('[ion-input-password-toggle] - No ancestor ion-input found. This component must be slotted inside of an ion-input.', el);\n      return;\n    }\n    /**\n     * Important: Set the type in connectedCallback because the default value\n     * of this.type may not always be accurate. Usually inputs have the \"password\" type\n     * but it is possible to have the input to initially have the \"text\" type. In that scenario\n     * the wrong icon will show briefly before switching to the correct icon. Setting the\n     * type here allows us to avoid that flicker.\n     */\n    this.type = inputElRef.type;\n  }\n  disconnectedCallback() {\n    this.inputElRef = null;\n  }\n  render() {\n    var _a, _b;\n    const {\n      color,\n      type\n    } = this;\n    const mode = getIonMode(this);\n    const showPasswordIcon = (_a = this.showIcon) !== null && _a !== void 0 ? _a : eye;\n    const hidePasswordIcon = (_b = this.hideIcon) !== null && _b !== void 0 ? _b : eyeOff;\n    const isPasswordVisible = type === 'text';\n    return h(Host, {\n      key: '1a28e078c83e74c72d8bb8189ece93ec2e3fa3d0',\n      class: createColorClasses(color, {\n        [mode]: true\n      })\n    }, h(\"ion-button\", {\n      key: '61591d11d97f3065240e51d8887f831339fd36cf',\n      mode: mode,\n      color: color,\n      fill: \"clear\",\n      shape: \"round\",\n      \"aria-checked\": isPasswordVisible ? 'true' : 'false',\n      \"aria-label\": isPasswordVisible ? 'Hide password' : 'Show password',\n      role: \"switch\",\n      type: \"button\",\n      onPointerDown: ev => {\n        /**\n         * This prevents mobile browsers from\n         * blurring the input when the password toggle\n         * button is activated.\n         */\n        ev.preventDefault();\n      },\n      onClick: this.togglePasswordVisibility\n    }, h(\"ion-icon\", {\n      key: '9d98e6675d5e2adea5c0f56ab67e98b6f57521dd',\n      slot: \"icon-only\",\n      \"aria-hidden\": \"true\",\n      icon: isPasswordVisible ? hidePasswordIcon : showPasswordIcon\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"type\": [\"onTypeChange\"]\n    };\n  }\n};\nInputPasswordToggle.style = {\n  ios: IonInputPasswordToggleIosStyle0,\n  md: IonInputPasswordToggleMdStyle0\n};\nexport { InputPasswordToggle as ion_input_password_toggle };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAQM,2BACA,iCACA,0BACA,gCACA;AAZN;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA,IAAM,4BAA4B;AAClC,IAAM,kCAAkC;AACxC,IAAM,2BAA2B;AACjC,IAAM,iCAAiC;AACvC,IAAM,sBAAsB,MAAM;AAAA,MAChC,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,2BAA2B,MAAM;AACpC,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,cAAI,CAAC,YAAY;AACf;AAAA,UACF;AACA,qBAAW,OAAO,WAAW,SAAS,SAAS,aAAa;AAAA,QAC9D;AACA,aAAK,QAAQ;AACb,aAAK,WAAW;AAChB,aAAK,WAAW;AAChB,aAAK,OAAO;AAAA,MACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,aAAa,UAAU;AACrB,YAAI,aAAa,UAAU,aAAa,YAAY;AAClD,0BAAgB,wGAAwG,QAAQ,wBAAwB,KAAK,EAAE;AAC/J;AAAA,QACF;AAAA,MACF;AAAA,MACA,oBAAoB;AAClB,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,cAAM,aAAa,KAAK,aAAa,GAAG,QAAQ,WAAW;AAC3D,YAAI,CAAC,YAAY;AACf,0BAAgB,qHAAqH,EAAE;AACvI;AAAA,QACF;AAQA,aAAK,OAAO,WAAW;AAAA,MACzB;AAAA,MACA,uBAAuB;AACrB,aAAK,aAAa;AAAA,MACpB;AAAA,MACA,SAAS;AACP,YAAI,IAAI;AACR,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM,oBAAoB,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,KAAK;AAC/E,cAAM,oBAAoB,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,KAAK;AAC/E,cAAM,oBAAoB,SAAS;AACnC,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,OAAO,mBAAmB,OAAO;AAAA,YAC/B,CAAC,IAAI,GAAG;AAAA,UACV,CAAC;AAAA,QACH,GAAG,EAAE,cAAc;AAAA,UACjB,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA,MAAM;AAAA,UACN,OAAO;AAAA,UACP,gBAAgB,oBAAoB,SAAS;AAAA,UAC7C,cAAc,oBAAoB,kBAAkB;AAAA,UACpD,MAAM;AAAA,UACN,MAAM;AAAA,UACN,eAAe,QAAM;AAMnB,eAAG,eAAe;AAAA,UACpB;AAAA,UACA,SAAS,KAAK;AAAA,QAChB,GAAG,EAAE,YAAY;AAAA,UACf,KAAK;AAAA,UACL,MAAM;AAAA,UACN,eAAe;AAAA,UACf,MAAM,oBAAoB,mBAAmB;AAAA,QAC/C,CAAC,CAAC,CAAC;AAAA,MACL;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,QAAQ,CAAC,cAAc;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AACA,wBAAoB,QAAQ;AAAA,MAC1B,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}