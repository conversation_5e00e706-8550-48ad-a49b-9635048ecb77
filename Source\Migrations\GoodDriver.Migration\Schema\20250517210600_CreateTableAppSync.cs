﻿﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Migration.Schema
{
	[FluentMigrator.Migration(20250517210600)]
	public class CreateTableAppSync : FluentMigrator.Migration
	{
		public override void Down()
		{
			if (Schema.Table("AppSyncJourneyInfo").Exists())
			{
				Delete.Table("AppSyncJourneyInfo");
			}
			
			if (Schema.Table("AppSyncJourney").Exists())
			{
				Delete.Table("AppSyncJourney");
			}
			
			if (Schema.Table("AppSyncVehicle").Exists())
			{
				Delete.Table("AppSyncVehicle");
			}
			
			if (Schema.Table("AppSyncUser").Exists())
			{
				Delete.Table("AppSyncUser");
			}
		}

		public override void Up()
		{
			// Create denormalized table for User data from the app
			if (!Schema.Table("AppSyncUser").Exists())
			{
				Create.Table("AppSyncUser")
					.WithColumn("Id").AsGuid().PrimaryKey().NotNullable()
					.WithColumn("Name").AsString(100).NotNullable()
					.WithColumn("Email").AsString(100).NotNullable()
					//.WithColumn("Phone").AsString(50).Nullable()
					//.WithColumn("Document").AsString(50).Nullable()
					//.WithColumn("BirthDate").AsString(50).Nullable()
					.WithColumn("DeviceId").AsString(100).Nullable()
					.WithColumn("DeviceModel").AsString(100).Nullable()
					.WithColumn("DeviceOS").AsString(50).Nullable()
					//.WithColumn("AppVersion").AsString(50).Nullable()
					.WithColumn("SyncStatus").AsString(50).NotNullable()
					.WithColumn("SyncDate").AsDateTime().NotNullable()
					.WithColumn("ProcessedStatus").AsString(50).NotNullable().WithDefaultValue("PENDING")
					.WithColumn("ProcessedDate").AsDateTime().Nullable()
					.WithColumn("ErrorMessage").AsString(500).Nullable()
					.WithColumn("CreatedOn").AsDateTime().NotNullable()
					.WithColumn("UpdatedOn").AsDateTime().Nullable();
			}
			
			// Create denormalized table for Vehicle data from the app
			if (!Schema.Table("AppSyncVehicle").Exists())
			{
				Create.Table("AppSyncVehicle")
					.WithColumn("Id").AsGuid().PrimaryKey().NotNullable()
					.WithColumn("UserId").AsGuid().NotNullable()
					.WithColumn("BrandId").AsInt32().NotNullable()
					.WithColumn("BrandName").AsString(100).NotNullable()
					.WithColumn("ModelId").AsInt32().NotNullable()
					.WithColumn("ModelName").AsString(100).NotNullable()
					.WithColumn("Year").AsInt32().NotNullable()
					.WithColumn("Plate").AsString(20).NotNullable()
					.WithColumn("Version").AsString(100).Nullable()
					.WithColumn("PolicyNumber").AsString(100).Nullable()
					.WithColumn("IsPrimary").AsBoolean().NotNullable()
					.WithColumn("SyncStatus").AsString(50).NotNullable()
					.WithColumn("SyncDate").AsDateTime().NotNullable()
					.WithColumn("ProcessedStatus").AsString(50).NotNullable().WithDefaultValue("PENDING")
					.WithColumn("ProcessedDate").AsDateTime().Nullable()
					.WithColumn("ErrorMessage").AsString(500).Nullable()
					.WithColumn("CreatedOn").AsDateTime().NotNullable()
					.WithColumn("UpdatedOn").AsDateTime().Nullable();
			}
			
			// Create denormalized table for Journey data from the app
			if (!Schema.Table("AppSyncJourney").Exists())
			{
				Create.Table("AppSyncJourney")
					.WithColumn("Id").AsGuid().PrimaryKey().NotNullable()
					.WithColumn("UserId").AsGuid().NotNullable()
					.WithColumn("VehicleId").AsGuid().NotNullable()
					.WithColumn("StartDate").AsDateTime().NotNullable()
					.WithColumn("EndDate").AsDateTime().Nullable()
					.WithColumn("Distance").AsDouble().Nullable()
					.WithColumn("DeviceId").AsString(100).Nullable()
					.WithColumn("SyncStatus").AsString(50).NotNullable()
					.WithColumn("SyncDate").AsDateTime().NotNullable()
					.WithColumn("ProcessedStatus").AsString(50).NotNullable().WithDefaultValue("PENDING")
					.WithColumn("ProcessedDate").AsDateTime().Nullable()
					.WithColumn("ErrorMessage").AsString(500).Nullable()
					.WithColumn("CreatedOn").AsDateTime().NotNullable()
					.WithColumn("UpdatedOn").AsDateTime().Nullable();
			}
			
			// Create denormalized table for JourneyInfo data from the app
			if (!Schema.Table("AppSyncJourneyInfo").Exists())
			{
				Create.Table("AppSyncJourneyInfo")
					.WithColumn("Id").AsGuid().PrimaryKey().NotNullable()
					.WithColumn("JourneyId").AsGuid().NotNullable()
					.WithColumn("Latitude").AsDouble().NotNullable()
					.WithColumn("Longitude").AsDouble().NotNullable()
					.WithColumn("Timestamp").AsDateTime().NotNullable()
					.WithColumn("Address").AsString(200).Nullable()
					.WithColumn("OccurrenceType").AsString(50).Nullable()
					.WithColumn("SyncStatus").AsString(50).NotNullable()
					.WithColumn("SyncDate").AsDateTime().NotNullable()
					.WithColumn("ProcessedStatus").AsString(50).NotNullable().WithDefaultValue("PENDING")
					.WithColumn("ProcessedDate").AsDateTime().Nullable()
					.WithColumn("ErrorMessage").AsString(500).Nullable()
					.WithColumn("CreatedOn").AsDateTime().NotNullable()
					.WithColumn("UpdatedOn").AsDateTime().Nullable();
			}
		}
	}
}
