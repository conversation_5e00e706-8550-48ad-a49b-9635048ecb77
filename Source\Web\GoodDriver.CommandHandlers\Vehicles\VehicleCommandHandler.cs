﻿using GoodDriver.Contracts.Users.Commands;
using GoodDriver.Contracts.Vehicles.Commands;
using GoodDriver.Domain.Users;
using GoodDriver.Domain.Vehicles;
using Rogerio.Commom;
using Rogerio.Commom.Exceptions;
using Rogerio.Cqrs.Commands;
using Rogerio.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.CommandHandlers.Vehicles
{
    public class VehicleCommandHandler : ICommandHandler<VehicleCreateCommand>
    {
        private readonly IUserRepository repository;
        private readonly IUnitOfWorkFactory unitOfWorkFactory;
        private readonly IUserFactory userFactory;

        public VehicleCommandHandler(IUserRepository repository,
            IUnitOfWorkFactory unitOfWorkFactory, IUserFactory userFactory)
        {
            this.unitOfWorkFactory = unitOfWorkFactory ?? throw new ArgumentNullException("unitOfWorkFactory");
            this.repository = repository ?? throw new ArgumentNullException("repository");
            this.userFactory = userFactory ?? throw new ArgumentNullException("userFactory");
        }

        public async Task HandleAsync(VehicleCreateCommand command)
        {
            if (command == null)
                throw new ArgumentException(nameof(command), command?.GetType().FullName);

            using (var unitOfWork = unitOfWorkFactory.Get())
            {
                var user = await repository.GetAsyncBy(user => user.Id == command.UserId);
                if(user == null)
                    throw new BusinessException("VehicleCreateCommand", "Usuário não encontrado.");
                if(EnsurePlateNotExistsAsync(command.Plate).Result)
                    throw new BusinessException("VehicleCreateCommand", "Já existe um veículo cadastrado com essa Placa.");

                user.AddVehicle(command.VehicleId, command.Plate, command.Year.ToString(), command.BrandId, command.ModelId);                
                await repository.UpdateAsync(user);
                
                unitOfWork.Complete();
            }
        }

        private async Task<bool> EnsurePlateNotExistsAsync(string plate)
        {
            var existsPlate = await repository.GetAsyncBy(u => u.Vehicles.Any(v => v.Plate == plate));
            return existsPlate != null;
        }
    }
}
