import {
  init_overlays_d99dcb0a,
  safeCall
} from "./chunk-4JI7VCBZ.js";
import {
  init_framework_delegate_56b467ad
} from "./chunk-GUQ63TTB.js";
import {
  getClassMap,
  init_theme_01f3f29c
} from "./chunk-CHFZ6R2P.js";
import {
  getIonMode,
  init_ionic_global_b26f573e
} from "./chunk-XPKADKFH.js";
import {
  Host,
  forceUpdate,
  getElement,
  h,
  init_index_527b9e34,
  registerInstance
} from "./chunk-V6QEVD67.js";
import {
  init_helpers_d94bc8ad
} from "./chunk-WNPNB2PX.js";
import {
  init_gesture_controller_314a54f6
} from "./chunk-YSMEPS6E.js";
import {
  init_hardware_back_button_a7eb8233
} from "./chunk-NHEUXSZ2.js";
import {
  init_index_a5d50daf
} from "./chunk-WX4TOLMF.js";
import {
  init_index_cfd9c1f2
} from "./chunk-6SXGDLUH.js";
import {
  __esm
} from "./chunk-4W6HR7MY.js";

// node_modules/@ionic/core/dist/esm/ion-select-modal.entry.js
var ionicSelectModalMdCss, IonSelectModalIonicStyle0, selectModalIosCss, IonSelectModalIosStyle0, selectModalMdCss, IonSelectModalMdStyle0, SelectModal;
var init_ion_select_modal_entry = __esm({
  "node_modules/@ionic/core/dist/esm/ion-select-modal.entry.js"() {
    init_index_527b9e34();
    init_ionic_global_b26f573e();
    init_overlays_d99dcb0a();
    init_theme_01f3f29c();
    init_index_cfd9c1f2();
    init_index_a5d50daf();
    init_helpers_d94bc8ad();
    init_hardware_back_button_a7eb8233();
    init_framework_delegate_56b467ad();
    init_gesture_controller_314a54f6();
    ionicSelectModalMdCss = ".sc-ion-select-modal-ionic-h{height:100%}ion-list.sc-ion-select-modal-ionic ion-radio.sc-ion-select-modal-ionic::part(container){display:none}ion-list.sc-ion-select-modal-ionic ion-radio.sc-ion-select-modal-ionic::part(label){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-item.sc-ion-select-modal-ionic{--inner-border-width:0}.item-radio-checked.sc-ion-select-modal-ionic{--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08);--background-focused:var(--ion-color-primary, #0054e9);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #0054e9);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-modal-ionic{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #0054e9)}";
    IonSelectModalIonicStyle0 = ionicSelectModalMdCss;
    selectModalIosCss = '.sc-ion-select-modal-ios-h{height:100%}ion-item.sc-ion-select-modal-ios{--inner-padding-end:0}ion-radio.sc-ion-select-modal-ios::after{bottom:0;position:absolute;width:calc(100% - 0.9375rem - 16px);border-width:0px 0px 0.55px 0px;border-style:solid;border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));content:""}ion-radio.sc-ion-select-modal-ios::after{inset-inline-start:calc(0.9375rem + 16px)}';
    IonSelectModalIosStyle0 = selectModalIosCss;
    selectModalMdCss = ".sc-ion-select-modal-md-h{height:100%}ion-list.sc-ion-select-modal-md ion-radio.sc-ion-select-modal-md::part(container){display:none}ion-list.sc-ion-select-modal-md ion-radio.sc-ion-select-modal-md::part(label){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-item.sc-ion-select-modal-md{--inner-border-width:0}.item-radio-checked.sc-ion-select-modal-md{--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08);--background-focused:var(--ion-color-primary, #0054e9);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #0054e9);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-modal-md{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #0054e9)}";
    IonSelectModalMdStyle0 = selectModalMdCss;
    SelectModal = class {
      constructor(hostRef) {
        registerInstance(this, hostRef);
        this.header = void 0;
        this.multiple = void 0;
        this.options = [];
      }
      closeModal() {
        const modal = this.el.closest("ion-modal");
        if (modal) {
          modal.dismiss();
        }
      }
      findOptionFromEvent(ev) {
        const {
          options
        } = this;
        return options.find((o) => o.value === ev.target.value);
      }
      getValues(ev) {
        const {
          multiple,
          options
        } = this;
        if (multiple) {
          return options.filter((o) => o.checked).map((o) => o.value);
        }
        const option = ev ? this.findOptionFromEvent(ev) : null;
        return option ? option.value : void 0;
      }
      callOptionHandler(ev) {
        const option = this.findOptionFromEvent(ev);
        const values = this.getValues(ev);
        if (option === null || option === void 0 ? void 0 : option.handler) {
          safeCall(option.handler, values);
        }
      }
      setChecked(ev) {
        const {
          multiple
        } = this;
        const option = this.findOptionFromEvent(ev);
        if (multiple && option) {
          option.checked = ev.detail.checked;
        }
      }
      renderRadioOptions() {
        const checked = this.options.filter((o) => o.checked).map((o) => o.value)[0];
        return h("ion-radio-group", {
          value: checked,
          onIonChange: (ev) => this.callOptionHandler(ev)
        }, this.options.map((option) => h("ion-item", {
          lines: "none",
          class: Object.assign({
            // TODO FW-4784
            "item-radio-checked": option.value === checked
          }, getClassMap(option.cssClass))
        }, h("ion-radio", {
          value: option.value,
          disabled: option.disabled,
          justify: "start",
          labelPlacement: "end",
          onClick: () => this.closeModal(),
          onKeyUp: (ev) => {
            if (ev.key === " ") {
              this.closeModal();
            }
          }
        }, option.text))));
      }
      renderCheckboxOptions() {
        return this.options.map((option) => h("ion-item", {
          class: Object.assign({
            // TODO FW-4784
            "item-checkbox-checked": option.checked
          }, getClassMap(option.cssClass))
        }, h("ion-checkbox", {
          value: option.value,
          disabled: option.disabled,
          checked: option.checked,
          justify: "start",
          labelPlacement: "end",
          onIonChange: (ev) => {
            this.setChecked(ev);
            this.callOptionHandler(ev);
            forceUpdate(this);
          }
        }, option.text)));
      }
      render() {
        return h(Host, {
          key: "885198a9f21884e3bfb9bf0af53e0ee3ae37b231",
          class: getIonMode(this)
        }, h("ion-header", {
          key: "d8b63726869747ac711e4fda78a50ce46f72970c"
        }, h("ion-toolbar", {
          key: "9ab2a4c1480dd74eeae38d7b580a2e87fb71270e"
        }, this.header !== void 0 && h("ion-title", {
          key: "87a7034385ef57f55cefdd0371dbb66a64827290"
        }, this.header), h("ion-buttons", {
          key: "0a35424ea13ca002abc9a43b6138730254f187d0",
          slot: "end"
        }, h("ion-button", {
          key: "238bf40b47128d9aa995d14d9ff9ebcae4f79492",
          onClick: () => this.closeModal()
        }, "Close")))), h("ion-content", {
          key: "4a256f3381f8cabbc7194337b8ae4aa1c3ab1066"
        }, h("ion-list", {
          key: "acd38fc52024632176467ed6a84106a454021544"
        }, this.multiple === true ? this.renderCheckboxOptions() : this.renderRadioOptions())));
      }
      get el() {
        return getElement(this);
      }
    };
    SelectModal.style = {
      ionic: IonSelectModalIonicStyle0,
      ios: IonSelectModalIosStyle0,
      md: IonSelectModalMdStyle0
    };
  }
});
init_ion_select_modal_entry();
export {
  SelectModal as ion_select_modal
};
/*! Bundled license information:

@ionic/core/dist/esm/ion-select-modal.entry.js:
  (*!
   * (C) Ionic http://ionicframework.com - MIT License
   *)
*/
//# sourceMappingURL=ion-select-modal.entry-NMEOA6BI.js.map
