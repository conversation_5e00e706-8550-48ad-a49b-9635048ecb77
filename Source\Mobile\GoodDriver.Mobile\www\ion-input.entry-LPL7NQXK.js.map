{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-input.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, i as forceUpdate, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { c as createNotchController } from './notch-controller-1a1f7183.js';\nimport { e as debounceEvent, i as inheritAriaAttributes, h as inheritAttributes, c as componentOnReady } from './helpers-d94bc8ad.js';\nimport { c as createSlotMutationController, g as getCounterText } from './input.utils-40504d6d.js';\nimport { h as hostContext, c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as closeCircle, d as closeSharp } from './index-e2cf2ceb.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport './index-a5d50daf.js';\nimport './index-cfd9c1f2.js';\nconst inputIosCss = \".sc-ion-input-ios-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--background:transparent;--color:initial;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;padding:0 !important;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2}ion-item[slot=start].sc-ion-input-ios-h,ion-item [slot=start].sc-ion-input-ios-h,ion-item[slot=end].sc-ion-input-ios-h,ion-item [slot=end].sc-ion-input-ios-h{width:auto}.ion-color.sc-ion-input-ios-h{--highlight-color-focused:var(--ion-color-base)}.input-label-placement-floating.sc-ion-input-ios-h,.input-label-placement-stacked.sc-ion-input-ios-h{min-height:56px}.native-input.sc-ion-input-ios{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:inline-block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;height:100%;max-height:100%;border:0;outline:none;background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:1}.native-input.sc-ion-input-ios::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios:-webkit-autofill{background-color:transparent}.native-input.sc-ion-input-ios:invalid{-webkit-box-shadow:none;box-shadow:none}.native-input.sc-ion-input-ios::-ms-clear{display:none}.cloned-input.sc-ion-input-ios{top:0;bottom:0;position:absolute;pointer-events:none}.cloned-input.sc-ion-input-ios{inset-inline-start:0}.cloned-input.sc-ion-input-ios:disabled{opacity:1}.input-clear-icon.sc-ion-input-ios{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;background-position:center;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:30px;height:30px;border:0;outline:none;background-color:transparent;background-repeat:no-repeat;color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));visibility:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}.in-item-color.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{color:inherit}.input-clear-icon.sc-ion-input-ios:focus{opacity:0.5}.has-value.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{visibility:visible}.input-wrapper.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:stretch;align-items:stretch;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-input-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;width:100%}.ion-touched.ion-invalid.sc-ion-input-ios-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-ios-h{--highlight-color:var(--highlight-color-valid)}.input-bottom.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}.has-focus.ion-valid.sc-ion-input-ios-h,.ion-touched.ion-invalid.sc-ion-input-ios-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios{display:none;color:var(--highlight-color-invalid)}.input-bottom.sc-ion-input-ios .helper-text.sc-ion-input-ios{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}.ion-touched.ion-invalid.sc-ion-input-ios-h .input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios{display:block}.ion-touched.ion-invalid.sc-ion-input-ios-h .input-bottom.sc-ion-input-ios .helper-text.sc-ion-input-ios{display:none}.input-bottom.sc-ion-input-ios .counter.sc-ion-input-ios{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.has-focus.sc-ion-input-ios-h input.sc-ion-input-ios{caret-color:var(--highlight-color)}.label-text-wrapper.sc-ion-input-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-input-ios,.sc-ion-input-ios-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-input-ios,.input-outline-notch-hidden.sc-ion-input-ios{display:none}.input-wrapper.sc-ion-input-ios input.sc-ion-input-ios{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.input-label-placement-start.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:row;flex-direction:row}.input-label-placement-start.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-end.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.input-label-placement-end.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-ios-h .label-text.sc-ion-input-ios{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.input-label-placement-stacked.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:left top;transform-origin:left top;max-width:100%;z-index:2}[dir=rtl].sc-ion-input-ios-h -no-combinator.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h -no-combinator.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].sc-ion-input-ios-h -no-combinator.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h -no-combinator.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-label-placement-stacked.sc-ion-input-ios-h:dir(rtl) .label-text-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h:dir(rtl) .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:right top;transform-origin:right top}}.input-label-placement-stacked.sc-ion-input-ios-h input.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0}.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{opacity:0}.has-focus.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios,.has-value.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{opacity:1}.label-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.sc-ion-input-ios-s>[slot=start]:last-of-type{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-input-ios-s>[slot=end]:first-of-type{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.sc-ion-input-ios-h[disabled].sc-ion-input-ios-s>ion-input-password-toggle,.sc-ion-input-ios-h[disabled] .sc-ion-input-ios-s>ion-input-password-toggle,.sc-ion-input-ios-h[readonly].sc-ion-input-ios-s>ion-input-password-toggle,.sc-ion-input-ios-h[readonly] .sc-ion-input-ios-s>ion-input-password-toggle{display:none}.sc-ion-input-ios-h{--border-width:0.55px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--highlight-height:0px;font-size:inherit}.input-clear-icon.sc-ion-input-ios ion-icon.sc-ion-input-ios{width:18px;height:18px}.input-disabled.sc-ion-input-ios-h{opacity:0.3}.sc-ion-input-ios-s>ion-button[slot=start].button-has-icon-only,.sc-ion-input-ios-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\nconst IonInputIosStyle0 = inputIosCss;\nconst inputMdCss = \".sc-ion-input-md-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--background:transparent;--color:initial;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;padding:0 !important;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2}ion-item[slot=start].sc-ion-input-md-h,ion-item [slot=start].sc-ion-input-md-h,ion-item[slot=end].sc-ion-input-md-h,ion-item [slot=end].sc-ion-input-md-h{width:auto}.ion-color.sc-ion-input-md-h{--highlight-color-focused:var(--ion-color-base)}.input-label-placement-floating.sc-ion-input-md-h,.input-label-placement-stacked.sc-ion-input-md-h{min-height:56px}.native-input.sc-ion-input-md{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:inline-block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;height:100%;max-height:100%;border:0;outline:none;background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:1}.native-input.sc-ion-input-md::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md:-webkit-autofill{background-color:transparent}.native-input.sc-ion-input-md:invalid{-webkit-box-shadow:none;box-shadow:none}.native-input.sc-ion-input-md::-ms-clear{display:none}.cloned-input.sc-ion-input-md{top:0;bottom:0;position:absolute;pointer-events:none}.cloned-input.sc-ion-input-md{inset-inline-start:0}.cloned-input.sc-ion-input-md:disabled{opacity:1}.input-clear-icon.sc-ion-input-md{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;background-position:center;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:30px;height:30px;border:0;outline:none;background-color:transparent;background-repeat:no-repeat;color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));visibility:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}.in-item-color.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{color:inherit}.input-clear-icon.sc-ion-input-md:focus{opacity:0.5}.has-value.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{visibility:visible}.input-wrapper.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:stretch;align-items:stretch;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-input-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;width:100%}.ion-touched.ion-invalid.sc-ion-input-md-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-md-h{--highlight-color:var(--highlight-color-valid)}.input-bottom.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}.has-focus.ion-valid.sc-ion-input-md-h,.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-md .error-text.sc-ion-input-md{display:none;color:var(--highlight-color-invalid)}.input-bottom.sc-ion-input-md .helper-text.sc-ion-input-md{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}.ion-touched.ion-invalid.sc-ion-input-md-h .input-bottom.sc-ion-input-md .error-text.sc-ion-input-md{display:block}.ion-touched.ion-invalid.sc-ion-input-md-h .input-bottom.sc-ion-input-md .helper-text.sc-ion-input-md{display:none}.input-bottom.sc-ion-input-md .counter.sc-ion-input-md{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.has-focus.sc-ion-input-md-h input.sc-ion-input-md{caret-color:var(--highlight-color)}.label-text-wrapper.sc-ion-input-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-input-md,.sc-ion-input-md-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-input-md,.input-outline-notch-hidden.sc-ion-input-md{display:none}.input-wrapper.sc-ion-input-md input.sc-ion-input-md{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.input-label-placement-start.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:row;flex-direction:row}.input-label-placement-start.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-end.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.input-label-placement-end.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-md-h .label-text.sc-ion-input-md{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.input-label-placement-stacked.sc-ion-input-md-h .input-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:left top;transform-origin:left top;max-width:100%;z-index:2}[dir=rtl].sc-ion-input-md-h -no-combinator.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].sc-ion-input-md-h -no-combinator.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-label-placement-stacked.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}}.input-label-placement-stacked.sc-ion-input-md-h input.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0}.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{opacity:0}.has-focus.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md,.has-value.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{opacity:1}.label-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.sc-ion-input-md-s>[slot=start]:last-of-type{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-input-md-s>[slot=end]:first-of-type{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.sc-ion-input-md-h[disabled].sc-ion-input-md-s>ion-input-password-toggle,.sc-ion-input-md-h[disabled] .sc-ion-input-md-s>ion-input-password-toggle,.sc-ion-input-md-h[readonly].sc-ion-input-md-s>ion-input-password-toggle,.sc-ion-input-md-h[readonly] .sc-ion-input-md-s>ion-input-password-toggle{display:none}.input-fill-solid.sc-ion-input-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-500, var(--ion-background-color-step-500, gray));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-bottom:var(--border-width) var(--border-style) var(--border-color)}.has-focus.input-fill-solid.ion-valid.sc-ion-input-md-h,.input-fill-solid.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-fill-solid.sc-ion-input-md-h .input-bottom.sc-ion-input-md{border-top:none}@media (any-hover: hover){.input-fill-solid.sc-ion-input-md-h:hover{--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}.input-fill-solid.has-focus.sc-ion-input-md-h{--background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0px;border-end-start-radius:0px}.label-floating.input-fill-solid.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{max-width:calc(100% / 0.75)}.input-fill-outline.sc-ion-input-md-h{--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.input-fill-outline.input-shape-round.sc-ion-input-md-h{--border-radius:28px;--padding-start:32px;--padding-end:32px}.has-focus.input-fill-outline.ion-valid.sc-ion-input-md-h,.input-fill-outline.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}@media (any-hover: hover){.input-fill-outline.sc-ion-input-md-h:hover{--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}.input-fill-outline.has-focus.sc-ion-input-md-h{--border-width:var(--highlight-height);--border-color:var(--highlight-color)}.input-fill-outline.sc-ion-input-md-h .input-bottom.sc-ion-input-md{border-top:none}.input-fill-outline.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-bottom:none}.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:left top;transform-origin:left top;position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}}.input-fill-outline.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{position:relative}.label-floating.input-fill-outline.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h input.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}.input-fill-outline.sc-ion-input-md-h .input-outline-container.sc-ion-input-md{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{pointer-events:none}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color)}.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md{max-width:calc(100% - var(--padding-start) - var(--padding-end))}.input-fill-outline.sc-ion-input-md-h .notch-spacer.sc-ion-input-md{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none;-webkit-box-sizing:content-box;box-sizing:content-box}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md{border-start-start-radius:var(--border-radius);border-start-end-radius:0px;border-end-end-radius:0px;border-end-start-radius:var(--border-radius);-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color);width:calc(var(--padding-start) - 4px)}.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color);border-start-start-radius:0px;border-start-end-radius:var(--border-radius);border-end-end-radius:var(--border-radius);border-end-start-radius:0px;-ms-flex-positive:1;flex-grow:1}.label-floating.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md{border-top:none}.sc-ion-input-md-h{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--highlight-height:2px;font-size:inherit}.input-clear-icon.sc-ion-input-md ion-icon.sc-ion-input-md{width:22px;height:22px}.input-disabled.sc-ion-input-md-h{opacity:0.38}.has-focus.ion-valid.sc-ion-input-md-h,.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-md .counter.sc-ion-input-md{letter-spacing:0.0333333333em}.input-label-placement-floating.has-focus.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-stacked.has-focus.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{color:var(--highlight-color)}.has-focus.input-label-placement-floating.ion-valid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.ion-touched.ion-invalid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.has-focus.input-label-placement-stacked.ion-valid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-stacked.ion-touched.ion-invalid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{color:var(--highlight-color)}.input-highlight.sc-ion-input-md{bottom:-1px;position:absolute;width:100%;height:var(--highlight-height);-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}.input-highlight.sc-ion-input-md{inset-inline-start:0}.has-focus.sc-ion-input-md-h .input-highlight.sc-ion-input-md{-webkit-transform:scale(1);transform:scale(1)}.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{bottom:0}.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{inset-inline-start:0}.input-shape-round.sc-ion-input-md-h{--border-radius:16px}.sc-ion-input-md-s>ion-button[slot=start].button-has-icon-only,.sc-ion-input-md-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\nconst IonInputMdStyle0 = inputMdCss;\nconst Input = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionInput = createEvent(this, \"ionInput\", 7);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.inputId = `ion-input-${inputIds++}`;\n    this.helperTextId = `${this.inputId}-helper-text`;\n    this.errorTextId = `${this.inputId}-error-text`;\n    this.inheritedAttributes = {};\n    this.isComposing = false;\n    /**\n     * `true` if the input was cleared as a result of the user typing\n     * with `clearOnEdit` enabled.\n     *\n     * Resets when the input loses focus.\n     */\n    this.didInputClearOnEdit = false;\n    this.onInput = ev => {\n      const input = ev.target;\n      if (input) {\n        this.value = input.value || '';\n      }\n      this.emitInputChange(ev);\n    };\n    this.onChange = ev => {\n      this.emitValueChange(ev);\n    };\n    this.onBlur = ev => {\n      this.hasFocus = false;\n      if (this.focusedValue !== this.value) {\n        /**\n         * Emits the `ionChange` event when the input value\n         * is different than the value when the input was focused.\n         */\n        this.emitValueChange(ev);\n      }\n      this.didInputClearOnEdit = false;\n      this.ionBlur.emit(ev);\n    };\n    this.onFocus = ev => {\n      this.hasFocus = true;\n      this.focusedValue = this.value;\n      this.ionFocus.emit(ev);\n    };\n    this.onKeydown = ev => {\n      this.checkClearOnEdit(ev);\n    };\n    this.onCompositionStart = () => {\n      this.isComposing = true;\n    };\n    this.onCompositionEnd = () => {\n      this.isComposing = false;\n    };\n    this.clearTextInput = ev => {\n      if (this.clearInput && !this.readonly && !this.disabled && ev) {\n        ev.preventDefault();\n        ev.stopPropagation();\n        // Attempt to focus input again after pressing clear button\n        this.setFocus();\n      }\n      this.value = '';\n      this.emitInputChange(ev);\n    };\n    /**\n     * Stops propagation when the label is clicked,\n     * otherwise, two clicks will be triggered.\n     */\n    this.onLabelClick = ev => {\n      // Only stop propagation if the click was directly on the label\n      // and not on the input or other child elements\n      if (ev.target === ev.currentTarget) {\n        ev.stopPropagation();\n      }\n    };\n    this.hasFocus = false;\n    this.color = undefined;\n    this.autocapitalize = 'off';\n    this.autocomplete = 'off';\n    this.autocorrect = 'off';\n    this.autofocus = false;\n    this.clearInput = false;\n    this.clearInputIcon = undefined;\n    this.clearOnEdit = undefined;\n    this.counter = false;\n    this.counterFormatter = undefined;\n    this.debounce = undefined;\n    this.disabled = false;\n    this.enterkeyhint = undefined;\n    this.errorText = undefined;\n    this.fill = undefined;\n    this.inputmode = undefined;\n    this.helperText = undefined;\n    this.label = undefined;\n    this.labelPlacement = 'start';\n    this.max = undefined;\n    this.maxlength = undefined;\n    this.min = undefined;\n    this.minlength = undefined;\n    this.multiple = undefined;\n    this.name = this.inputId;\n    this.pattern = undefined;\n    this.placeholder = undefined;\n    this.readonly = false;\n    this.required = false;\n    this.shape = undefined;\n    this.spellcheck = false;\n    this.step = undefined;\n    this.type = 'text';\n    this.value = '';\n  }\n  debounceChanged() {\n    const {\n      ionInput,\n      debounce,\n      originalIonInput\n    } = this;\n    /**\n     * If debounce is undefined, we have to manually revert the ionInput emitter in case\n     * debounce used to be set to a number. Otherwise, the event would stay debounced.\n     */\n    this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n  }\n  /**\n   * Whenever the type on the input changes we need\n   * to update the internal type prop on the password\n   * toggle so that that correct icon is shown.\n   */\n  onTypeChange() {\n    const passwordToggle = this.el.querySelector('ion-input-password-toggle');\n    if (passwordToggle) {\n      passwordToggle.type = this.type;\n    }\n  }\n  /**\n   * Update the native input element when the value changes\n   */\n  valueChanged() {\n    const nativeInput = this.nativeInput;\n    const value = this.getValue();\n    if (nativeInput && nativeInput.value !== value && !this.isComposing) {\n      /**\n       * Assigning the native input's value on attribute\n       * value change, allows `ionInput` implementations\n       * to override the control's value.\n       *\n       * Used for patterns such as input trimming (removing whitespace),\n       * or input masking.\n       */\n      nativeInput.value = value;\n    }\n  }\n  /**\n   * dir is a globally enumerated attribute.\n   * As a result, creating these as properties\n   * can have unintended side effects. Instead, we\n   * listen for attribute changes and inherit them\n   * to the inner `<input>` element.\n   */\n  onDirChanged(newValue) {\n    this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), {\n      dir: newValue\n    });\n    forceUpdate(this);\n  }\n  /**\n   * This prevents the native input from emitting the click event.\n   * Instead, the click event from the ion-input is emitted.\n   */\n  onClickCapture(ev) {\n    const nativeInput = this.nativeInput;\n    if (nativeInput && ev.target === nativeInput) {\n      ev.stopPropagation();\n      this.el.click();\n    }\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = Object.assign(Object.assign({}, inheritAriaAttributes(this.el)), inheritAttributes(this.el, ['tabindex', 'title', 'data-form-type', 'dir']));\n  }\n  connectedCallback() {\n    const {\n      el\n    } = this;\n    this.slotMutationController = createSlotMutationController(el, ['label', 'start', 'end'], () => forceUpdate(this));\n    this.notchController = createNotchController(el, () => this.notchSpacerEl, () => this.labelSlot);\n    this.debounceChanged();\n    {\n      document.dispatchEvent(new CustomEvent('ionInputDidLoad', {\n        detail: this.el\n      }));\n    }\n  }\n  componentDidLoad() {\n    this.originalIonInput = this.ionInput;\n    /**\n     * Set the type on the password toggle in the event that this input's\n     * type was set async and does not match the default type for the password toggle.\n     * This can happen when the type is bound using a JS framework binding syntax\n     * such as [type] in Angular.\n     */\n    this.onTypeChange();\n    this.debounceChanged();\n  }\n  componentDidRender() {\n    var _a;\n    (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n  }\n  disconnectedCallback() {\n    {\n      document.dispatchEvent(new CustomEvent('ionInputDidUnload', {\n        detail: this.el\n      }));\n    }\n    if (this.slotMutationController) {\n      this.slotMutationController.destroy();\n      this.slotMutationController = undefined;\n    }\n    if (this.notchController) {\n      this.notchController.destroy();\n      this.notchController = undefined;\n    }\n  }\n  /**\n   * Sets focus on the native `input` in `ion-input`. Use this method instead of the global\n   * `input.focus()`.\n   *\n   * Developers who wish to focus an input when a page enters\n   * should call `setFocus()` in the `ionViewDidEnter()` lifecycle method.\n   *\n   * Developers who wish to focus an input when an overlay is presented\n   * should call `setFocus` after `didPresent` has resolved.\n   *\n   * See [managing focus](/docs/developing/managing-focus) for more information.\n   */\n  async setFocus() {\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  }\n  /**\n   * Returns the native `<input>` element used under the hood.\n   */\n  async getInputElement() {\n    /**\n     * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n     * nativeInput won't be defined yet with the custom elements build, so wait for it to load in.\n     */\n    if (!this.nativeInput) {\n      await new Promise(resolve => componentOnReady(this.el, resolve));\n    }\n    return Promise.resolve(this.nativeInput);\n  }\n  /**\n   * Emits an `ionChange` event.\n   *\n   * This API should be called for user committed changes.\n   * This API should not be used for external value changes.\n   */\n  emitValueChange(event) {\n    const {\n      value\n    } = this;\n    // Checks for both null and undefined values\n    const newValue = value == null ? value : value.toString();\n    // Emitting a value change should update the internal state for tracking the focused value\n    this.focusedValue = newValue;\n    this.ionChange.emit({\n      value: newValue,\n      event\n    });\n  }\n  /**\n   * Emits an `ionInput` event.\n   */\n  emitInputChange(event) {\n    const {\n      value\n    } = this;\n    // Checks for both null and undefined values\n    const newValue = value == null ? value : value.toString();\n    this.ionInput.emit({\n      value: newValue,\n      event\n    });\n  }\n  shouldClearOnEdit() {\n    const {\n      type,\n      clearOnEdit\n    } = this;\n    return clearOnEdit === undefined ? type === 'password' : clearOnEdit;\n  }\n  getValue() {\n    return typeof this.value === 'number' ? this.value.toString() : (this.value || '').toString();\n  }\n  checkClearOnEdit(ev) {\n    if (!this.shouldClearOnEdit()) {\n      return;\n    }\n    /**\n     * The following keys do not modify the\n     * contents of the input. As a result, pressing\n     * them should not edit the input.\n     *\n     * We can't check to see if the value of the input\n     * was changed because we call checkClearOnEdit\n     * in a keydown listener, and the key has not yet\n     * been added to the input.\n     */\n    const IGNORED_KEYS = ['Enter', 'Tab', 'Shift', 'Meta', 'Alt', 'Control'];\n    const pressedIgnoredKey = IGNORED_KEYS.includes(ev.key);\n    /**\n     * Clear the input if the control has not been previously cleared during focus.\n     * Do not clear if the user hitting enter to submit a form.\n     */\n    if (!this.didInputClearOnEdit && this.hasValue() && !pressedIgnoredKey) {\n      this.value = '';\n      this.emitInputChange(ev);\n    }\n    /**\n     * Pressing an IGNORED_KEYS first and\n     * then an allowed key will cause the input to not\n     * be cleared.\n     */\n    if (!pressedIgnoredKey) {\n      this.didInputClearOnEdit = true;\n    }\n  }\n  hasValue() {\n    return this.getValue().length > 0;\n  }\n  /**\n   * Renders the helper text or error text values\n   */\n  renderHintText() {\n    const {\n      helperText,\n      errorText,\n      helperTextId,\n      errorTextId\n    } = this;\n    return [h(\"div\", {\n      id: helperTextId,\n      class: \"helper-text\"\n    }, helperText), h(\"div\", {\n      id: errorTextId,\n      class: \"error-text\"\n    }, errorText)];\n  }\n  getHintTextID() {\n    const {\n      el,\n      helperText,\n      errorText,\n      helperTextId,\n      errorTextId\n    } = this;\n    if (el.classList.contains('ion-touched') && el.classList.contains('ion-invalid') && errorText) {\n      return errorTextId;\n    }\n    if (helperText) {\n      return helperTextId;\n    }\n    return undefined;\n  }\n  renderCounter() {\n    const {\n      counter,\n      maxlength,\n      counterFormatter,\n      value\n    } = this;\n    if (counter !== true || maxlength === undefined) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"counter\"\n    }, getCounterText(value, maxlength, counterFormatter));\n  }\n  /**\n   * Responsible for rendering helper text,\n   * error text, and counter. This element should only\n   * be rendered if hint text is set or counter is enabled.\n   */\n  renderBottomContent() {\n    const {\n      counter,\n      helperText,\n      errorText,\n      maxlength\n    } = this;\n    /**\n     * undefined and empty string values should\n     * be treated as not having helper/error text.\n     */\n    const hasHintText = !!helperText || !!errorText;\n    const hasCounter = counter === true && maxlength !== undefined;\n    if (!hasHintText && !hasCounter) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"input-bottom\"\n    }, this.renderHintText(), this.renderCounter());\n  }\n  renderLabel() {\n    const {\n      label\n    } = this;\n    return h(\"div\", {\n      class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': !this.hasLabel\n      }\n    }, label === undefined ? h(\"slot\", {\n      name: \"label\"\n    }) : h(\"div\", {\n      class: \"label-text\"\n    }, label));\n  }\n  /**\n   * Gets any content passed into the `label` slot,\n   * not the <slot> definition.\n   */\n  get labelSlot() {\n    return this.el.querySelector('[slot=\"label\"]');\n  }\n  /**\n   * Returns `true` if label content is provided\n   * either by a prop or a content. If you want\n   * to get the plaintext value of the label use\n   * the `labelText` getter instead.\n   */\n  get hasLabel() {\n    return this.label !== undefined || this.labelSlot !== null;\n  }\n  /**\n   * Renders the border container\n   * when fill=\"outline\".\n   */\n  renderLabelContainer() {\n    const mode = getIonMode(this);\n    const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n    if (hasOutlineFill) {\n      /**\n       * The outline fill has a special outline\n       * that appears around the input and the label.\n       * Certain stacked and floating label placements cause the\n       * label to translate up and create a \"cut out\"\n       * inside of that border by using the notch-spacer element.\n       */\n      return [h(\"div\", {\n        class: \"input-outline-container\"\n      }, h(\"div\", {\n        class: \"input-outline-start\"\n      }), h(\"div\", {\n        class: {\n          'input-outline-notch': true,\n          'input-outline-notch-hidden': !this.hasLabel\n        }\n      }, h(\"div\", {\n        class: \"notch-spacer\",\n        \"aria-hidden\": \"true\",\n        ref: el => this.notchSpacerEl = el\n      }, this.label)), h(\"div\", {\n        class: \"input-outline-end\"\n      })), this.renderLabel()];\n    }\n    /**\n     * If not using the outline style,\n     * we can render just the label.\n     */\n    return this.renderLabel();\n  }\n  render() {\n    const {\n      disabled,\n      fill,\n      readonly,\n      shape,\n      inputId,\n      labelPlacement,\n      el,\n      hasFocus,\n      clearInputIcon\n    } = this;\n    const mode = getIonMode(this);\n    const value = this.getValue();\n    const inItem = hostContext('ion-item', this.el);\n    const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n    const defaultClearIcon = mode === 'ios' ? closeCircle : closeSharp;\n    const clearIconData = clearInputIcon !== null && clearInputIcon !== void 0 ? clearInputIcon : defaultClearIcon;\n    const hasValue = this.hasValue();\n    const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n    /**\n     * If the label is stacked, it should always sit above the input.\n     * For floating labels, the label should move above the input if\n     * the input has a value, is focused, or has anything in either\n     * the start or end slot.\n     *\n     * If there is content in the start slot, the label would overlap\n     * it if not forced to float. This is also applied to the end slot\n     * because with the default or solid fills, the input is not\n     * vertically centered in the container, but the label is. This\n     * causes the slots and label to appear vertically offset from each\n     * other when the label isn't floating above the input. This doesn't\n     * apply to the outline fill, but this was not accounted for to keep\n     * things consistent.\n     *\n     * TODO(FW-5592): Remove hasStartEndSlots condition\n     */\n    const labelShouldFloat = labelPlacement === 'stacked' || labelPlacement === 'floating' && (hasValue || hasFocus || hasStartEndSlots);\n    return h(Host, {\n      key: '41b2526627e7d2773a80f011b123284203a71ca0',\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'has-value': hasValue,\n        'has-focus': hasFocus,\n        'label-floating': labelShouldFloat,\n        [`input-fill-${fill}`]: fill !== undefined,\n        [`input-shape-${shape}`]: shape !== undefined,\n        [`input-label-placement-${labelPlacement}`]: true,\n        'in-item': inItem,\n        'in-item-color': hostContext('ion-item.ion-color', this.el),\n        'input-disabled': disabled\n      })\n    }, h(\"label\", {\n      key: '9ab078363e32528102b441ad1791d83f86fdcbdc',\n      class: \"input-wrapper\",\n      htmlFor: inputId,\n      onClick: this.onLabelClick\n    }, this.renderLabelContainer(), h(\"div\", {\n      key: 'e34b594980ec62e4c618e827fadf7669a39ad0d8',\n      class: \"native-wrapper\",\n      onClick: this.onLabelClick\n    }, h(\"slot\", {\n      key: '12dc04ead5502e9e5736240e918bf9331bf7b5d9',\n      name: \"start\"\n    }), h(\"input\", Object.assign({\n      key: 'df356eb4ced23109b2c0242f36dc043aba8782d6',\n      class: \"native-input\",\n      ref: input => this.nativeInput = input,\n      id: inputId,\n      disabled: disabled,\n      autoCapitalize: this.autocapitalize,\n      autoComplete: this.autocomplete,\n      autoCorrect: this.autocorrect,\n      autoFocus: this.autofocus,\n      enterKeyHint: this.enterkeyhint,\n      inputMode: this.inputmode,\n      min: this.min,\n      max: this.max,\n      minLength: this.minlength,\n      maxLength: this.maxlength,\n      multiple: this.multiple,\n      name: this.name,\n      pattern: this.pattern,\n      placeholder: this.placeholder || '',\n      readOnly: readonly,\n      required: this.required,\n      spellcheck: this.spellcheck,\n      step: this.step,\n      type: this.type,\n      value: value,\n      onInput: this.onInput,\n      onChange: this.onChange,\n      onBlur: this.onBlur,\n      onFocus: this.onFocus,\n      onKeyDown: this.onKeydown,\n      onCompositionstart: this.onCompositionStart,\n      onCompositionend: this.onCompositionEnd,\n      \"aria-describedby\": this.getHintTextID(),\n      \"aria-invalid\": this.getHintTextID() === this.errorTextId\n    }, this.inheritedAttributes)), this.clearInput && !readonly && !disabled && h(\"button\", {\n      key: 'aa7cb47ac287140a68c5cb0ee9359abaa611e21b',\n      \"aria-label\": \"reset\",\n      type: \"button\",\n      class: \"input-clear-icon\",\n      onPointerDown: ev => {\n        /**\n         * This prevents mobile browsers from\n         * blurring the input when the clear\n         * button is activated.\n         */\n        ev.preventDefault();\n      },\n      onFocusin: ev => {\n        /**\n         * Prevent the focusin event from bubbling otherwise it will cause the focusin\n         * event listener in scroll assist to fire. When this fires, focus will be moved\n         * back to the input even if the clear button was never tapped. This poses issues\n         * for screen readers as it means users would be unable to swipe past the clear button.\n         */\n        ev.stopPropagation();\n      },\n      onClick: this.clearTextInput\n    }, h(\"ion-icon\", {\n      key: 'cd03e46b97299d9db5cedf81944ae9bbe72bacdc',\n      \"aria-hidden\": \"true\",\n      icon: clearIconData\n    })), h(\"slot\", {\n      key: 'de36b79a89c4b413beba22e8a74c53dbf57a84ab',\n      name: \"end\"\n    })), shouldRenderHighlight && h(\"div\", {\n      key: 'f088509073845bf767ea7ccfde1e917e1cf93cc1',\n      class: \"input-highlight\"\n    })), this.renderBottomContent());\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"debounce\": [\"debounceChanged\"],\n      \"type\": [\"onTypeChange\"],\n      \"value\": [\"valueChanged\"],\n      \"dir\": [\"onDirChanged\"]\n    };\n  }\n};\nlet inputIds = 0;\nInput.style = {\n  ios: IonInputIosStyle0,\n  md: IonInputMdStyle0\n};\nexport { Input as ion_input };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAYM,aACA,mBACA,YACA,kBACA,OA4mBF;AA5nBJ;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAM,cAAc;AACpB,IAAM,oBAAoB;AAC1B,IAAM,aAAa;AACnB,IAAM,mBAAmB;AACzB,IAAM,QAAQ,MAAM;AAAA,MAClB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,aAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,aAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,aAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,aAAK,UAAU,aAAa,UAAU;AACtC,aAAK,eAAe,GAAG,KAAK,OAAO;AACnC,aAAK,cAAc,GAAG,KAAK,OAAO;AAClC,aAAK,sBAAsB,CAAC;AAC5B,aAAK,cAAc;AAOnB,aAAK,sBAAsB;AAC3B,aAAK,UAAU,QAAM;AACnB,gBAAM,QAAQ,GAAG;AACjB,cAAI,OAAO;AACT,iBAAK,QAAQ,MAAM,SAAS;AAAA,UAC9B;AACA,eAAK,gBAAgB,EAAE;AAAA,QACzB;AACA,aAAK,WAAW,QAAM;AACpB,eAAK,gBAAgB,EAAE;AAAA,QACzB;AACA,aAAK,SAAS,QAAM;AAClB,eAAK,WAAW;AAChB,cAAI,KAAK,iBAAiB,KAAK,OAAO;AAKpC,iBAAK,gBAAgB,EAAE;AAAA,UACzB;AACA,eAAK,sBAAsB;AAC3B,eAAK,QAAQ,KAAK,EAAE;AAAA,QACtB;AACA,aAAK,UAAU,QAAM;AACnB,eAAK,WAAW;AAChB,eAAK,eAAe,KAAK;AACzB,eAAK,SAAS,KAAK,EAAE;AAAA,QACvB;AACA,aAAK,YAAY,QAAM;AACrB,eAAK,iBAAiB,EAAE;AAAA,QAC1B;AACA,aAAK,qBAAqB,MAAM;AAC9B,eAAK,cAAc;AAAA,QACrB;AACA,aAAK,mBAAmB,MAAM;AAC5B,eAAK,cAAc;AAAA,QACrB;AACA,aAAK,iBAAiB,QAAM;AAC1B,cAAI,KAAK,cAAc,CAAC,KAAK,YAAY,CAAC,KAAK,YAAY,IAAI;AAC7D,eAAG,eAAe;AAClB,eAAG,gBAAgB;AAEnB,iBAAK,SAAS;AAAA,UAChB;AACA,eAAK,QAAQ;AACb,eAAK,gBAAgB,EAAE;AAAA,QACzB;AAKA,aAAK,eAAe,QAAM;AAGxB,cAAI,GAAG,WAAW,GAAG,eAAe;AAClC,eAAG,gBAAgB;AAAA,UACrB;AAAA,QACF;AACA,aAAK,WAAW;AAChB,aAAK,QAAQ;AACb,aAAK,iBAAiB;AACtB,aAAK,eAAe;AACpB,aAAK,cAAc;AACnB,aAAK,YAAY;AACjB,aAAK,aAAa;AAClB,aAAK,iBAAiB;AACtB,aAAK,cAAc;AACnB,aAAK,UAAU;AACf,aAAK,mBAAmB;AACxB,aAAK,WAAW;AAChB,aAAK,WAAW;AAChB,aAAK,eAAe;AACpB,aAAK,YAAY;AACjB,aAAK,OAAO;AACZ,aAAK,YAAY;AACjB,aAAK,aAAa;AAClB,aAAK,QAAQ;AACb,aAAK,iBAAiB;AACtB,aAAK,MAAM;AACX,aAAK,YAAY;AACjB,aAAK,MAAM;AACX,aAAK,YAAY;AACjB,aAAK,WAAW;AAChB,aAAK,OAAO,KAAK;AACjB,aAAK,UAAU;AACf,aAAK,cAAc;AACnB,aAAK,WAAW;AAChB,aAAK,WAAW;AAChB,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,OAAO;AACZ,aAAK,OAAO;AACZ,aAAK,QAAQ;AAAA,MACf;AAAA,MACA,kBAAkB;AAChB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AAKJ,aAAK,WAAW,aAAa,SAAY,qBAAqB,QAAQ,qBAAqB,SAAS,mBAAmB,WAAW,cAAc,UAAU,QAAQ;AAAA,MACpK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,eAAe;AACb,cAAM,iBAAiB,KAAK,GAAG,cAAc,2BAA2B;AACxE,YAAI,gBAAgB;AAClB,yBAAe,OAAO,KAAK;AAAA,QAC7B;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAIA,eAAe;AACb,cAAM,cAAc,KAAK;AACzB,cAAM,QAAQ,KAAK,SAAS;AAC5B,YAAI,eAAe,YAAY,UAAU,SAAS,CAAC,KAAK,aAAa;AASnE,sBAAY,QAAQ;AAAA,QACtB;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,aAAa,UAAU;AACrB,aAAK,sBAAsB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,mBAAmB,GAAG;AAAA,UACpF,KAAK;AAAA,QACP,CAAC;AACD,oBAAY,IAAI;AAAA,MAClB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,eAAe,IAAI;AACjB,cAAM,cAAc,KAAK;AACzB,YAAI,eAAe,GAAG,WAAW,aAAa;AAC5C,aAAG,gBAAgB;AACnB,eAAK,GAAG,MAAM;AAAA,QAChB;AAAA,MACF;AAAA,MACA,oBAAoB;AAClB,aAAK,sBAAsB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,sBAAsB,KAAK,EAAE,CAAC,GAAG,kBAAkB,KAAK,IAAI,CAAC,YAAY,SAAS,kBAAkB,KAAK,CAAC,CAAC;AAAA,MACxK;AAAA,MACA,oBAAoB;AAClB,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,aAAK,yBAAyB,6BAA6B,IAAI,CAAC,SAAS,SAAS,KAAK,GAAG,MAAM,YAAY,IAAI,CAAC;AACjH,aAAK,kBAAkB,sBAAsB,IAAI,MAAM,KAAK,eAAe,MAAM,KAAK,SAAS;AAC/F,aAAK,gBAAgB;AACrB;AACE,mBAAS,cAAc,IAAI,YAAY,mBAAmB;AAAA,YACxD,QAAQ,KAAK;AAAA,UACf,CAAC,CAAC;AAAA,QACJ;AAAA,MACF;AAAA,MACA,mBAAmB;AACjB,aAAK,mBAAmB,KAAK;AAO7B,aAAK,aAAa;AAClB,aAAK,gBAAgB;AAAA,MACvB;AAAA,MACA,qBAAqB;AACnB,YAAI;AACJ,SAAC,KAAK,KAAK,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,oBAAoB;AAAA,MAC1F;AAAA,MACA,uBAAuB;AACrB;AACE,mBAAS,cAAc,IAAI,YAAY,qBAAqB;AAAA,YAC1D,QAAQ,KAAK;AAAA,UACf,CAAC,CAAC;AAAA,QACJ;AACA,YAAI,KAAK,wBAAwB;AAC/B,eAAK,uBAAuB,QAAQ;AACpC,eAAK,yBAAyB;AAAA,QAChC;AACA,YAAI,KAAK,iBAAiB;AACxB,eAAK,gBAAgB,QAAQ;AAC7B,eAAK,kBAAkB;AAAA,QACzB;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaM,WAAW;AAAA;AACf,cAAI,KAAK,aAAa;AACpB,iBAAK,YAAY,MAAM;AAAA,UACzB;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA,MAIM,kBAAkB;AAAA;AAKtB,cAAI,CAAC,KAAK,aAAa;AACrB,kBAAM,IAAI,QAAQ,aAAW,iBAAiB,KAAK,IAAI,OAAO,CAAC;AAAA,UACjE;AACA,iBAAO,QAAQ,QAAQ,KAAK,WAAW;AAAA,QACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,gBAAgB,OAAO;AACrB,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AAEJ,cAAM,WAAW,SAAS,OAAO,QAAQ,MAAM,SAAS;AAExD,aAAK,eAAe;AACpB,aAAK,UAAU,KAAK;AAAA,UAClB,OAAO;AAAA,UACP;AAAA,QACF,CAAC;AAAA,MACH;AAAA;AAAA;AAAA;AAAA,MAIA,gBAAgB,OAAO;AACrB,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AAEJ,cAAM,WAAW,SAAS,OAAO,QAAQ,MAAM,SAAS;AACxD,aAAK,SAAS,KAAK;AAAA,UACjB,OAAO;AAAA,UACP;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,oBAAoB;AAClB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,eAAO,gBAAgB,SAAY,SAAS,aAAa;AAAA,MAC3D;AAAA,MACA,WAAW;AACT,eAAO,OAAO,KAAK,UAAU,WAAW,KAAK,MAAM,SAAS,KAAK,KAAK,SAAS,IAAI,SAAS;AAAA,MAC9F;AAAA,MACA,iBAAiB,IAAI;AACnB,YAAI,CAAC,KAAK,kBAAkB,GAAG;AAC7B;AAAA,QACF;AAWA,cAAM,eAAe,CAAC,SAAS,OAAO,SAAS,QAAQ,OAAO,SAAS;AACvE,cAAM,oBAAoB,aAAa,SAAS,GAAG,GAAG;AAKtD,YAAI,CAAC,KAAK,uBAAuB,KAAK,SAAS,KAAK,CAAC,mBAAmB;AACtE,eAAK,QAAQ;AACb,eAAK,gBAAgB,EAAE;AAAA,QACzB;AAMA,YAAI,CAAC,mBAAmB;AACtB,eAAK,sBAAsB;AAAA,QAC7B;AAAA,MACF;AAAA,MACA,WAAW;AACT,eAAO,KAAK,SAAS,EAAE,SAAS;AAAA,MAClC;AAAA;AAAA;AAAA;AAAA,MAIA,iBAAiB;AACf,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,eAAO,CAAC,EAAE,OAAO;AAAA,UACf,IAAI;AAAA,UACJ,OAAO;AAAA,QACT,GAAG,UAAU,GAAG,EAAE,OAAO;AAAA,UACvB,IAAI;AAAA,UACJ,OAAO;AAAA,QACT,GAAG,SAAS,CAAC;AAAA,MACf;AAAA,MACA,gBAAgB;AACd,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,GAAG,UAAU,SAAS,aAAa,KAAK,GAAG,UAAU,SAAS,aAAa,KAAK,WAAW;AAC7F,iBAAO;AAAA,QACT;AACA,YAAI,YAAY;AACd,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AACd,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,YAAY,QAAQ,cAAc,QAAW;AAC/C;AAAA,QACF;AACA,eAAO,EAAE,OAAO;AAAA,UACd,OAAO;AAAA,QACT,GAAG,eAAe,OAAO,WAAW,gBAAgB,CAAC;AAAA,MACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,sBAAsB;AACpB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AAKJ,cAAM,cAAc,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,cAAM,aAAa,YAAY,QAAQ,cAAc;AACrD,YAAI,CAAC,eAAe,CAAC,YAAY;AAC/B;AAAA,QACF;AACA,eAAO,EAAE,OAAO;AAAA,UACd,OAAO;AAAA,QACT,GAAG,KAAK,eAAe,GAAG,KAAK,cAAc,CAAC;AAAA,MAChD;AAAA,MACA,cAAc;AACZ,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,eAAO,EAAE,OAAO;AAAA,UACd,OAAO;AAAA,YACL,sBAAsB;AAAA,YACtB,6BAA6B,CAAC,KAAK;AAAA,UACrC;AAAA,QACF,GAAG,UAAU,SAAY,EAAE,QAAQ;AAAA,UACjC,MAAM;AAAA,QACR,CAAC,IAAI,EAAE,OAAO;AAAA,UACZ,OAAO;AAAA,QACT,GAAG,KAAK,CAAC;AAAA,MACX;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,IAAI,YAAY;AACd,eAAO,KAAK,GAAG,cAAc,gBAAgB;AAAA,MAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,IAAI,WAAW;AACb,eAAO,KAAK,UAAU,UAAa,KAAK,cAAc;AAAA,MACxD;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,uBAAuB;AACrB,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM,iBAAiB,SAAS,QAAQ,KAAK,SAAS;AACtD,YAAI,gBAAgB;AAQlB,iBAAO,CAAC,EAAE,OAAO;AAAA,YACf,OAAO;AAAA,UACT,GAAG,EAAE,OAAO;AAAA,YACV,OAAO;AAAA,UACT,CAAC,GAAG,EAAE,OAAO;AAAA,YACX,OAAO;AAAA,cACL,uBAAuB;AAAA,cACvB,8BAA8B,CAAC,KAAK;AAAA,YACtC;AAAA,UACF,GAAG,EAAE,OAAO;AAAA,YACV,OAAO;AAAA,YACP,eAAe;AAAA,YACf,KAAK,QAAM,KAAK,gBAAgB;AAAA,UAClC,GAAG,KAAK,KAAK,CAAC,GAAG,EAAE,OAAO;AAAA,YACxB,OAAO;AAAA,UACT,CAAC,CAAC,GAAG,KAAK,YAAY,CAAC;AAAA,QACzB;AAKA,eAAO,KAAK,YAAY;AAAA,MAC1B;AAAA,MACA,SAAS;AACP,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM,QAAQ,KAAK,SAAS;AAC5B,cAAM,SAAS,YAAY,YAAY,KAAK,EAAE;AAC9C,cAAM,wBAAwB,SAAS,QAAQ,SAAS,aAAa,CAAC;AACtE,cAAM,mBAAmB,SAAS,QAAQ,cAAc;AACxD,cAAM,gBAAgB,mBAAmB,QAAQ,mBAAmB,SAAS,iBAAiB;AAC9F,cAAM,WAAW,KAAK,SAAS;AAC/B,cAAM,mBAAmB,GAAG,cAAc,8BAA8B,MAAM;AAkB9E,cAAM,mBAAmB,mBAAmB,aAAa,mBAAmB,eAAe,YAAY,YAAY;AACnH,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,OAAO,mBAAmB,KAAK,OAAO;AAAA,YACpC,CAAC,IAAI,GAAG;AAAA,YACR,aAAa;AAAA,YACb,aAAa;AAAA,YACb,kBAAkB;AAAA,YAClB,CAAC,cAAc,IAAI,EAAE,GAAG,SAAS;AAAA,YACjC,CAAC,eAAe,KAAK,EAAE,GAAG,UAAU;AAAA,YACpC,CAAC,yBAAyB,cAAc,EAAE,GAAG;AAAA,YAC7C,WAAW;AAAA,YACX,iBAAiB,YAAY,sBAAsB,KAAK,EAAE;AAAA,YAC1D,kBAAkB;AAAA,UACpB,CAAC;AAAA,QACH,GAAG,EAAE,SAAS;AAAA,UACZ,KAAK;AAAA,UACL,OAAO;AAAA,UACP,SAAS;AAAA,UACT,SAAS,KAAK;AAAA,QAChB,GAAG,KAAK,qBAAqB,GAAG,EAAE,OAAO;AAAA,UACvC,KAAK;AAAA,UACL,OAAO;AAAA,UACP,SAAS,KAAK;AAAA,QAChB,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,UACL,MAAM;AAAA,QACR,CAAC,GAAG,EAAE,SAAS,OAAO,OAAO;AAAA,UAC3B,KAAK;AAAA,UACL,OAAO;AAAA,UACP,KAAK,WAAS,KAAK,cAAc;AAAA,UACjC,IAAI;AAAA,UACJ;AAAA,UACA,gBAAgB,KAAK;AAAA,UACrB,cAAc,KAAK;AAAA,UACnB,aAAa,KAAK;AAAA,UAClB,WAAW,KAAK;AAAA,UAChB,cAAc,KAAK;AAAA,UACnB,WAAW,KAAK;AAAA,UAChB,KAAK,KAAK;AAAA,UACV,KAAK,KAAK;AAAA,UACV,WAAW,KAAK;AAAA,UAChB,WAAW,KAAK;AAAA,UAChB,UAAU,KAAK;AAAA,UACf,MAAM,KAAK;AAAA,UACX,SAAS,KAAK;AAAA,UACd,aAAa,KAAK,eAAe;AAAA,UACjC,UAAU;AAAA,UACV,UAAU,KAAK;AAAA,UACf,YAAY,KAAK;AAAA,UACjB,MAAM,KAAK;AAAA,UACX,MAAM,KAAK;AAAA,UACX;AAAA,UACA,SAAS,KAAK;AAAA,UACd,UAAU,KAAK;AAAA,UACf,QAAQ,KAAK;AAAA,UACb,SAAS,KAAK;AAAA,UACd,WAAW,KAAK;AAAA,UAChB,oBAAoB,KAAK;AAAA,UACzB,kBAAkB,KAAK;AAAA,UACvB,oBAAoB,KAAK,cAAc;AAAA,UACvC,gBAAgB,KAAK,cAAc,MAAM,KAAK;AAAA,QAChD,GAAG,KAAK,mBAAmB,CAAC,GAAG,KAAK,cAAc,CAAC,YAAY,CAAC,YAAY,EAAE,UAAU;AAAA,UACtF,KAAK;AAAA,UACL,cAAc;AAAA,UACd,MAAM;AAAA,UACN,OAAO;AAAA,UACP,eAAe,QAAM;AAMnB,eAAG,eAAe;AAAA,UACpB;AAAA,UACA,WAAW,QAAM;AAOf,eAAG,gBAAgB;AAAA,UACrB;AAAA,UACA,SAAS,KAAK;AAAA,QAChB,GAAG,EAAE,YAAY;AAAA,UACf,KAAK;AAAA,UACL,eAAe;AAAA,UACf,MAAM;AAAA,QACR,CAAC,CAAC,GAAG,EAAE,QAAQ;AAAA,UACb,KAAK;AAAA,UACL,MAAM;AAAA,QACR,CAAC,CAAC,GAAG,yBAAyB,EAAE,OAAO;AAAA,UACrC,KAAK;AAAA,UACL,OAAO;AAAA,QACT,CAAC,CAAC,GAAG,KAAK,oBAAoB,CAAC;AAAA,MACjC;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,YAAY,CAAC,iBAAiB;AAAA,UAC9B,QAAQ,CAAC,cAAc;AAAA,UACvB,SAAS,CAAC,cAAc;AAAA,UACxB,OAAO,CAAC,cAAc;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,IAAI,WAAW;AACf,UAAM,QAAQ;AAAA,MACZ,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}