// Mixins
// --------------------------------------------------
// Utility mixins for better code reuse

// Flexbox mixins
// --------------------------------------------------

// Flex container
@mixin flex-container($direction: row, $justify: flex-start, $align: stretch, $wrap: nowrap) {
  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;
  flex-wrap: $wrap;
}

// Flex center (both horizontally and vertically)
@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

// Typography mixins
// --------------------------------------------------

// Base typography
@mixin typography-base {
  font-family: var(--app-font-family);
  color: var(--app-text-color);
  line-height: 1.5;
}

// Heading styles
@mixin heading($size, $weight: var(--app-heading-font-weight), $margin-bottom: var(--app-spacing-md)) {
  font-size: $size;
  font-weight: $weight;
  margin-bottom: $margin-bottom;
  line-height: 1.2;
}

// Card mixins
// --------------------------------------------------

// Basic card style
@mixin card-base {
  background-color: var(--app-background-color);
  border-radius: var(--app-border-radius-md);
  box-shadow: var(--app-card-shadow);
  padding: var(--app-spacing-md);
  margin-bottom: var(--app-spacing-md);
}

// Elevated card with stronger shadow
@mixin card-elevated {
  @include card-base;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

// Form mixins
// --------------------------------------------------

// Form group
@mixin form-group($margin-bottom: var(--app-spacing-md)) {
  margin-bottom: $margin-bottom;
}

// Form input
@mixin form-input {
  background-color: var(--app-input-background);
  border: 1px solid var(--app-border-color);
  border-radius: var(--app-border-radius-sm);
  padding: var(--app-spacing-sm) var(--app-spacing-md);
  width: 100%;
  transition: border-color var(--app-transition-fast);
  
  &:focus {
    border-color: var(--ion-color-primary);
    outline: none;
  }
}

// Button mixins
// --------------------------------------------------

// Base button
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--app-spacing-sm) var(--app-spacing-lg);
  border-radius: var(--app-border-radius-md);
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all var(--app-transition-fast);
  text-decoration: none;
}

// Primary button
@mixin button-primary {
  @include button-base;
  background-color: var(--ion-color-primary);
  color: var(--ion-color-primary-contrast);
  
  &:hover, &:focus {
    background-color: var(--ion-color-primary-shade);
  }
}

// Secondary button
@mixin button-secondary {
  @include button-base;
  background-color: var(--ion-color-secondary);
  color: var(--ion-color-secondary-contrast);
  
  &:hover, &:focus {
    background-color: var(--ion-color-secondary-shade);
  }
}

// Outline button
@mixin button-outline($color: var(--ion-color-primary)) {
  @include button-base;
  background-color: transparent;
  color: $color;
  border: 1px solid $color;
  
  &:hover, &:focus {
    background-color: rgba($color, 0.1);
  }
}

// Layout mixins
// --------------------------------------------------

// Container with max width and centered
@mixin container($max-width: 1200px, $padding: var(--app-spacing-md)) {
  width: 100%;
  max-width: $max-width;
  margin-left: auto;
  margin-right: auto;
  padding-left: $padding;
  padding-right: $padding;
}

// Responsive breakpoints
// --------------------------------------------------

// Media query for small devices
@mixin for-phone-only {
  @media (max-width: 599px) { @content; }
}

// Media query for tablets
@mixin for-tablet-portrait-up {
  @media (min-width: 600px) { @content; }
}

// Media query for tablets landscape
@mixin for-tablet-landscape-up {
  @media (min-width: 900px) { @content; }
}

// Media query for desktop
@mixin for-desktop-up {
  @media (min-width: 1200px) { @content; }
}

// Media query for large desktop
@mixin for-big-desktop-up {
  @media (min-width: 1800px) { @content; }
}

// Animation mixins
// --------------------------------------------------

// Fade in animation
@mixin fade-in($duration: 0.3s, $delay: 0s) {
  animation: fadeIn $duration ease $delay forwards;
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
}

// Slide in from bottom
@mixin slide-in-bottom($duration: 0.3s, $delay: 0s, $distance: 20px) {
  animation: slideInBottom $duration ease $delay forwards;
  
  @keyframes slideInBottom {
    from { 
      opacity: 0;
      transform: translateY($distance);
    }
    to { 
      opacity: 1;
      transform: translateY(0);
    }
  }
}
