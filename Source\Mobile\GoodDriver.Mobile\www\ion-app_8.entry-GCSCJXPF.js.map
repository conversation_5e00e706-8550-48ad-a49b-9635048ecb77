{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-app_8.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as Host, f as getElement, c as createEvent, d as readTask, i as forceUpdate, w as writeTask } from './index-527b9e34.js';\nimport { shouldUseCloseWatcher } from './hardware-back-button-a7eb8233.js';\nimport { c as config, p as printIonWarning, d as printIonError } from './index-cfd9c1f2.js';\nimport { b as getIonMode, a as isPlatform } from './ionic-global-b26f573e.js';\nimport { i as inheritAriaAttributes, k as hasLazyBuild, c as componentOnReady, j as clamp, s as shallowEqualStringMap } from './helpers-d94bc8ad.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { a as findIonContent, p as printIonContentErrorMsg, g as getScrollElement } from './index-9a17db3d.js';\nimport { c as createKeyboardController } from './keyboard-controller-ec5c2bfa.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-fe2083dc.js';\nimport { a as attachComponent, d as detachComponent } from './framework-delegate-56b467ad.js';\nimport { c as createLockController } from './lock-controller-316928be.js';\nimport { t as transition } from './index-68c0d151.js';\nimport './index-a5d50daf.js';\nimport './keyboard-73175e24.js';\nimport './capacitor-59395cbd.js';\nconst appCss = \"html.plt-mobile ion-app{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}html.plt-mobile ion-app [contenteditable]{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}ion-app.force-statusbar-padding{--ion-safe-area-top:20px}\";\nconst IonAppStyle0 = appCss;\nconst App = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n  }\n  componentDidLoad() {\n    {\n      rIC(async () => {\n        const isHybrid = isPlatform(window, 'hybrid');\n        if (!config.getBoolean('_testing')) {\n          import('./index-be190feb.js').then(module => module.startTapClick(config));\n        }\n        if (config.getBoolean('statusTap', isHybrid)) {\n          import('./status-tap-42a8af65.js').then(module => module.startStatusTap());\n        }\n        if (config.getBoolean('inputShims', needInputShims())) {\n          /**\n           * needInputShims() ensures that only iOS and Android\n           * platforms proceed into this block.\n           */\n          const platform = isPlatform(window, 'ios') ? 'ios' : 'android';\n          import('./input-shims-279903e2.js').then(module => module.startInputShims(config, platform));\n        }\n        const hardwareBackButtonModule = await import('./hardware-back-button-a7eb8233.js');\n        const supportsHardwareBackButtonEvents = isHybrid || shouldUseCloseWatcher();\n        if (config.getBoolean('hardwareBackButton', supportsHardwareBackButtonEvents)) {\n          hardwareBackButtonModule.startHardwareBackButton();\n        } else {\n          /**\n           * If an app sets hardwareBackButton: false and experimentalCloseWatcher: true\n           * then the close watcher will not be used.\n           */\n          if (shouldUseCloseWatcher()) {\n            printIonWarning('[ion-app] - experimentalCloseWatcher was set to `true`, but hardwareBackButton was set to `false`. Both config options must be `true` for the Close Watcher API to be used.');\n          }\n          hardwareBackButtonModule.blockHardwareBackButton();\n        }\n        if (typeof window !== 'undefined') {\n          import('./keyboard-52278bd7.js').then(module => module.startKeyboardAssist(window));\n        }\n        import('./focus-visible-dd40d69f.js').then(module => this.focusVisible = module.startFocusVisible());\n      });\n    }\n  }\n  /**\n   * Used to set focus on an element that uses `ion-focusable`.\n   * Do not use this if focusing the element as a result of a keyboard\n   * event as the focus utility should handle this for us. This method\n   * should be used when we want to programmatically focus an element as\n   * a result of another user action. (Ex: We focus the first element\n   * inside of a popover when the user presents it, but the popover is not always\n   * presented as a result of keyboard action.)\n   */\n  async setFocus(elements) {\n    if (this.focusVisible) {\n      this.focusVisible.setFocus(elements);\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '03aa892f986330078d112b1e8b010df98fa7e39e',\n      class: {\n        [mode]: true,\n        'ion-page': true,\n        'force-statusbar-padding': config.getBoolean('_forceStatusbarPadding')\n      }\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nconst needInputShims = () => {\n  /**\n   * iOS always needs input shims\n   */\n  const needsShimsIOS = isPlatform(window, 'ios') && isPlatform(window, 'mobile');\n  if (needsShimsIOS) {\n    return true;\n  }\n  /**\n   * Android only needs input shims when running\n   * in the browser and only if the browser is using the\n   * new Chrome 108+ resize behavior: https://developer.chrome.com/blog/viewport-resize-behavior/\n   */\n  const isAndroidMobileWeb = isPlatform(window, 'android') && isPlatform(window, 'mobileweb');\n  if (isAndroidMobileWeb) {\n    return true;\n  }\n  return false;\n};\nconst rIC = callback => {\n  if ('requestIdleCallback' in window) {\n    window.requestIdleCallback(callback);\n  } else {\n    setTimeout(callback, 32);\n  }\n};\nApp.style = IonAppStyle0;\nconst buttonsIosCss = \".sc-ion-buttons-ios-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-ios-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-ios-s ion-button{--padding-top:3px;--padding-bottom:3px;--padding-start:5px;--padding-end:5px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;min-height:32px}.sc-ion-buttons-ios-s .button-has-icon-only{--padding-top:0;--padding-bottom:0}.sc-ion-buttons-ios-s ion-button:not(.button-round){--border-radius:4px}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button{--color:initial;--border-color:initial;--background-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-solid,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-solid{--background:var(--ion-color-contrast);--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12;--background-hover:var(--ion-color-base);--background-hover-opacity:0.45;--color:var(--ion-color-base);--color-focused:var(--ion-color-base)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-clear,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-clear{--color-activated:var(--ion-color-contrast);--color-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-outline,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-outline{--color-activated:var(--ion-color-base);--color-focused:var(--ion-color-contrast);--background-activated:var(--ion-color-contrast)}.sc-ion-buttons-ios-s .button-clear,.sc-ion-buttons-ios-s .button-outline{--background-activated:transparent;--background-focused:currentColor;--background-hover:transparent}.sc-ion-buttons-ios-s .button-solid:not(.ion-color){--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12}.sc-ion-buttons-ios-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:1.41em;line-height:0.67}.sc-ion-buttons-ios-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-start:0.4em;margin-inline-start:0.4em;font-size:1.41em;line-height:0.67}.sc-ion-buttons-ios-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.65em;line-height:0.67}\";\nconst IonButtonsIosStyle0 = buttonsIosCss;\nconst buttonsMdCss = \".sc-ion-buttons-md-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-md-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-md-s ion-button{--padding-top:3px;--padding-bottom:3px;--padding-start:8px;--padding-end:8px;--box-shadow:none;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;min-height:32px}.sc-ion-buttons-md-s .button-has-icon-only{--padding-top:0;--padding-bottom:0}.sc-ion-buttons-md-s ion-button:not(.button-round){--border-radius:2px}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button{--color:initial;--color-focused:var(--ion-color-contrast);--color-hover:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-contrast);--background-hover:var(--ion-color-contrast)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-solid,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-solid{--background:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-shade);--background-hover:var(--ion-color-base);--color:var(--ion-color-base);--color-focused:var(--ion-color-base);--color-hover:var(--ion-color-base)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-outline,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-outline{--border-color:var(--ion-color-contrast)}.sc-ion-buttons-md-s .button-has-icon-only.button-clear{--padding-top:12px;--padding-end:12px;--padding-bottom:12px;--padding-start:12px;--border-radius:50%;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:3rem;height:3rem}.sc-ion-buttons-md-s .button{--background-hover:currentColor}.sc-ion-buttons-md-s .button-solid{--color:var(--ion-toolbar-background, var(--ion-background-color, #fff));--background:var(--ion-toolbar-color, var(--ion-text-color, #424242));--background-activated:transparent;--background-focused:currentColor}.sc-ion-buttons-md-s .button-outline{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor;--border-color:currentColor}.sc-ion-buttons-md-s .button-clear{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor}.sc-ion-buttons-md-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:1.4em}.sc-ion-buttons-md-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-start:0.4em;margin-inline-start:0.4em;font-size:1.4em}.sc-ion-buttons-md-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.8em}\";\nconst IonButtonsMdStyle0 = buttonsMdCss;\nconst Buttons = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.collapse = false;\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '58c1fc5eb867d0731c63549b1ccb3ec3bbbe6e1b',\n      class: {\n        [mode]: true,\n        ['buttons-collapse']: this.collapse\n      }\n    }, h(\"slot\", {\n      key: '0c8f95b9840c8fa0c4e50be84c5159620a3eb5c8'\n    }));\n  }\n};\nButtons.style = {\n  ios: IonButtonsIosStyle0,\n  md: IonButtonsMdStyle0\n};\nconst contentCss = \":host{--background:var(--ion-background-color, #fff);--color:var(--ion-text-color, #000);--padding-top:0px;--padding-bottom:0px;--padding-start:0px;--padding-end:0px;--keyboard-offset:0px;--offset-top:0px;--offset-bottom:0px;--overflow:auto;display:block;position:relative;-ms-flex:1;flex:1;width:100%;height:100%;margin:0 !important;padding:0 !important;font-family:var(--ion-font-family, inherit);contain:size style}:host(.ion-color) .inner-scroll{background:var(--ion-color-base);color:var(--ion-color-contrast)}#background-content{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);position:absolute;background:var(--background)}.inner-scroll{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:calc(var(--padding-top) + var(--offset-top));padding-bottom:calc(var(--padding-bottom) + var(--keyboard-offset) + var(--offset-bottom));position:absolute;color:var(--color);-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;-ms-touch-action:pan-x pan-y pinch-zoom;touch-action:pan-x pan-y pinch-zoom}.scroll-y,.scroll-x{-webkit-overflow-scrolling:touch;z-index:0;will-change:scroll-position}.scroll-y{overflow-y:var(--overflow);overscroll-behavior-y:contain}.scroll-x{overflow-x:var(--overflow);overscroll-behavior-x:contain}.overscroll::before,.overscroll::after{position:absolute;width:1px;height:1px;content:\\\"\\\"}.overscroll::before{bottom:-1px}.overscroll::after{top:-1px}:host(.content-sizing){display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;min-height:0;contain:none}:host(.content-sizing) .inner-scroll{position:relative;top:0;bottom:0;margin-top:calc(var(--offset-top) * -1);margin-bottom:calc(var(--offset-bottom) * -1)}.transition-effect{display:none;position:absolute;width:100%;height:100vh;opacity:0;pointer-events:none}:host(.content-ltr) .transition-effect{left:-100%;}:host(.content-rtl) .transition-effect{right:-100%;}.transition-cover{position:absolute;right:0;width:100%;height:100%;background:black;opacity:0.1}.transition-shadow{display:block;position:absolute;width:100%;height:100%;-webkit-box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03);box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03)}:host(.content-ltr) .transition-shadow{right:0;}:host(.content-rtl) .transition-shadow{left:0;-webkit-transform:scaleX(-1);transform:scaleX(-1)}::slotted([slot=fixed]){position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0)}\";\nconst IonContentStyle0 = contentCss;\nconst Content = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionScrollStart = createEvent(this, \"ionScrollStart\", 7);\n    this.ionScroll = createEvent(this, \"ionScroll\", 7);\n    this.ionScrollEnd = createEvent(this, \"ionScrollEnd\", 7);\n    this.watchDog = null;\n    this.isScrolling = false;\n    this.lastScroll = 0;\n    this.queued = false;\n    this.cTop = -1;\n    this.cBottom = -1;\n    this.isMainContent = true;\n    this.resizeTimeout = null;\n    this.inheritedAttributes = {};\n    this.tabsElement = null;\n    // Detail is used in a hot loop in the scroll event, by allocating it here\n    // V8 will be able to inline any read/write to it since it's a monomorphic class.\n    // https://mrale.ph/blog/2015/01/11/whats-up-with-monomorphism.html\n    this.detail = {\n      scrollTop: 0,\n      scrollLeft: 0,\n      type: 'scroll',\n      event: undefined,\n      startX: 0,\n      startY: 0,\n      startTime: 0,\n      currentX: 0,\n      currentY: 0,\n      velocityX: 0,\n      velocityY: 0,\n      deltaX: 0,\n      deltaY: 0,\n      currentTime: 0,\n      data: undefined,\n      isScrolling: true\n    };\n    this.color = undefined;\n    this.fullscreen = false;\n    this.fixedSlotPlacement = 'after';\n    this.forceOverscroll = undefined;\n    this.scrollX = false;\n    this.scrollY = true;\n    this.scrollEvents = false;\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n  }\n  connectedCallback() {\n    this.isMainContent = this.el.closest('ion-menu, ion-popover, ion-modal') === null;\n    /**\n     * The fullscreen content offsets need to be\n     * computed after the tab bar has loaded. Since\n     * lazy evaluation means components are not hydrated\n     * at the same time, we need to wait for the ionTabBarLoaded\n     * event to fire. This does not impact dist-custom-elements\n     * because there is no hydration there.\n     */\n    if (hasLazyBuild(this.el)) {\n      /**\n       * We need to cache the reference to the tabs.\n       * If just the content is unmounted then we won't\n       * be able to query for the closest tabs on disconnectedCallback\n       * since the content has been removed from the DOM tree.\n       */\n      const closestTabs = this.tabsElement = this.el.closest('ion-tabs');\n      if (closestTabs !== null) {\n        /**\n         * When adding and removing the event listener\n         * we need to make sure we pass the same function reference\n         * otherwise the event listener will not be removed properly.\n         * We can't only pass `this.resize` because \"this\" in the function\n         * context becomes a reference to IonTabs instead of IonContent.\n         *\n         * Additionally, we listen for ionTabBarLoaded on the IonTabs\n         * instance rather than the IonTabBar instance. It's possible for\n         * a tab bar to be conditionally rendered/mounted. Since ionTabBarLoaded\n         * bubbles, we can catch any instances of child tab bars loading by listening\n         * on IonTabs.\n         */\n        this.tabsLoadCallback = () => this.resize();\n        closestTabs.addEventListener('ionTabBarLoaded', this.tabsLoadCallback);\n      }\n    }\n  }\n  disconnectedCallback() {\n    this.onScrollEnd();\n    if (hasLazyBuild(this.el)) {\n      /**\n       * The event listener and tabs caches need to\n       * be cleared otherwise this will create a memory\n       * leak where the IonTabs instance can never be\n       * garbage collected.\n       */\n      const {\n        tabsElement,\n        tabsLoadCallback\n      } = this;\n      if (tabsElement !== null && tabsLoadCallback !== undefined) {\n        tabsElement.removeEventListener('ionTabBarLoaded', tabsLoadCallback);\n      }\n      this.tabsElement = null;\n      this.tabsLoadCallback = undefined;\n    }\n  }\n  /**\n   * Rotating certain devices can update\n   * the safe area insets. As a result,\n   * the fullscreen feature on ion-content\n   * needs to be recalculated.\n   *\n   * We listen for \"resize\" because we\n   * do not care what the orientation of\n   * the device is. Other APIs\n   * such as ScreenOrientation or\n   * the deviceorientation event must have\n   * permission from the user first whereas\n   * the \"resize\" event does not.\n   *\n   * We also throttle the callback to minimize\n   * thrashing when quickly resizing a window.\n   */\n  onResize() {\n    if (this.resizeTimeout) {\n      clearTimeout(this.resizeTimeout);\n      this.resizeTimeout = null;\n    }\n    this.resizeTimeout = setTimeout(() => {\n      /**\n       * Resize should only happen\n       * if the content is visible.\n       * When the content is hidden\n       * then offsetParent will be null.\n       */\n      if (this.el.offsetParent === null) {\n        return;\n      }\n      this.resize();\n    }, 100);\n  }\n  shouldForceOverscroll() {\n    const {\n      forceOverscroll\n    } = this;\n    const mode = getIonMode(this);\n    return forceOverscroll === undefined ? mode === 'ios' && isPlatform('ios') : forceOverscroll;\n  }\n  resize() {\n    /**\n     * Only force update if the component is rendered in a browser context.\n     * Using `forceUpdate` in a server context with pre-rendering can lead to an infinite loop.\n     * The `hydrateDocument` function in `@stencil/core` will render the `ion-content`, but\n     * `forceUpdate` will trigger another render, locking up the server.\n     *\n     * TODO: Remove if STENCIL-834 determines Stencil will account for this.\n     */\n    {\n      if (this.fullscreen) {\n        readTask(() => this.readDimensions());\n      } else if (this.cTop !== 0 || this.cBottom !== 0) {\n        this.cTop = this.cBottom = 0;\n        forceUpdate(this);\n      }\n    }\n  }\n  readDimensions() {\n    const page = getPageElement(this.el);\n    const top = Math.max(this.el.offsetTop, 0);\n    const bottom = Math.max(page.offsetHeight - top - this.el.offsetHeight, 0);\n    const dirty = top !== this.cTop || bottom !== this.cBottom;\n    if (dirty) {\n      this.cTop = top;\n      this.cBottom = bottom;\n      forceUpdate(this);\n    }\n  }\n  onScroll(ev) {\n    const timeStamp = Date.now();\n    const shouldStart = !this.isScrolling;\n    this.lastScroll = timeStamp;\n    if (shouldStart) {\n      this.onScrollStart();\n    }\n    if (!this.queued && this.scrollEvents) {\n      this.queued = true;\n      readTask(ts => {\n        this.queued = false;\n        this.detail.event = ev;\n        updateScrollDetail(this.detail, this.scrollEl, ts, shouldStart);\n        this.ionScroll.emit(this.detail);\n      });\n    }\n  }\n  /**\n   * Get the element where the actual scrolling takes place.\n   * This element can be used to subscribe to `scroll` events or manually modify\n   * `scrollTop`. However, it's recommended to use the API provided by `ion-content`:\n   *\n   * i.e. Using `ionScroll`, `ionScrollStart`, `ionScrollEnd` for scrolling events\n   * and `scrollToPoint()` to scroll the content into a certain point.\n   */\n  async getScrollElement() {\n    /**\n     * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n     * scrollEl won't be defined yet with the custom elements build, so wait for it to load in.\n     */\n    if (!this.scrollEl) {\n      await new Promise(resolve => componentOnReady(this.el, resolve));\n    }\n    return Promise.resolve(this.scrollEl);\n  }\n  /**\n   * Returns the background content element.\n   * @internal\n   */\n  async getBackgroundElement() {\n    if (!this.backgroundContentEl) {\n      await new Promise(resolve => componentOnReady(this.el, resolve));\n    }\n    return Promise.resolve(this.backgroundContentEl);\n  }\n  /**\n   * Scroll to the top of the component.\n   *\n   * @param duration The amount of time to take scrolling to the top. Defaults to `0`.\n   */\n  scrollToTop(duration = 0) {\n    return this.scrollToPoint(undefined, 0, duration);\n  }\n  /**\n   * Scroll to the bottom of the component.\n   *\n   * @param duration The amount of time to take scrolling to the bottom. Defaults to `0`.\n   */\n  async scrollToBottom(duration = 0) {\n    const scrollEl = await this.getScrollElement();\n    const y = scrollEl.scrollHeight - scrollEl.clientHeight;\n    return this.scrollToPoint(undefined, y, duration);\n  }\n  /**\n   * Scroll by a specified X/Y distance in the component.\n   *\n   * @param x The amount to scroll by on the horizontal axis.\n   * @param y The amount to scroll by on the vertical axis.\n   * @param duration The amount of time to take scrolling by that amount.\n   */\n  async scrollByPoint(x, y, duration) {\n    const scrollEl = await this.getScrollElement();\n    return this.scrollToPoint(x + scrollEl.scrollLeft, y + scrollEl.scrollTop, duration);\n  }\n  /**\n   * Scroll to a specified X/Y location in the component.\n   *\n   * @param x The point to scroll to on the horizontal axis.\n   * @param y The point to scroll to on the vertical axis.\n   * @param duration The amount of time to take scrolling to that point. Defaults to `0`.\n   */\n  async scrollToPoint(x, y, duration = 0) {\n    const el = await this.getScrollElement();\n    if (duration < 32) {\n      if (y != null) {\n        el.scrollTop = y;\n      }\n      if (x != null) {\n        el.scrollLeft = x;\n      }\n      return;\n    }\n    let resolve;\n    let startTime = 0;\n    const promise = new Promise(r => resolve = r);\n    const fromY = el.scrollTop;\n    const fromX = el.scrollLeft;\n    const deltaY = y != null ? y - fromY : 0;\n    const deltaX = x != null ? x - fromX : 0;\n    // scroll loop\n    const step = timeStamp => {\n      const linearTime = Math.min(1, (timeStamp - startTime) / duration) - 1;\n      const easedT = Math.pow(linearTime, 3) + 1;\n      if (deltaY !== 0) {\n        el.scrollTop = Math.floor(easedT * deltaY + fromY);\n      }\n      if (deltaX !== 0) {\n        el.scrollLeft = Math.floor(easedT * deltaX + fromX);\n      }\n      if (easedT < 1) {\n        // do not use DomController here\n        // must use nativeRaf in order to fire in the next frame\n        requestAnimationFrame(step);\n      } else {\n        resolve();\n      }\n    };\n    // chill out for a frame first\n    requestAnimationFrame(ts => {\n      startTime = ts;\n      step(ts);\n    });\n    return promise;\n  }\n  onScrollStart() {\n    this.isScrolling = true;\n    this.ionScrollStart.emit({\n      isScrolling: true\n    });\n    if (this.watchDog) {\n      clearInterval(this.watchDog);\n    }\n    // watchdog\n    this.watchDog = setInterval(() => {\n      if (this.lastScroll < Date.now() - 120) {\n        this.onScrollEnd();\n      }\n    }, 100);\n  }\n  onScrollEnd() {\n    if (this.watchDog) clearInterval(this.watchDog);\n    this.watchDog = null;\n    if (this.isScrolling) {\n      this.isScrolling = false;\n      this.ionScrollEnd.emit({\n        isScrolling: false\n      });\n    }\n  }\n  render() {\n    const {\n      fixedSlotPlacement,\n      inheritedAttributes,\n      isMainContent,\n      scrollX,\n      scrollY,\n      el\n    } = this;\n    const rtl = isRTL(el) ? 'rtl' : 'ltr';\n    const mode = getIonMode(this);\n    const forceOverscroll = this.shouldForceOverscroll();\n    const transitionShadow = mode === 'ios';\n    this.resize();\n    return h(Host, Object.assign({\n      key: 'f2a24aa66dbf5c76f9d4b06f708eb73cadc239df',\n      role: isMainContent ? 'main' : undefined,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'content-sizing': hostContext('ion-popover', this.el),\n        overscroll: forceOverscroll,\n        [`content-${rtl}`]: true\n      }),\n      style: {\n        '--offset-top': `${this.cTop}px`,\n        '--offset-bottom': `${this.cBottom}px`\n      }\n    }, inheritedAttributes), h(\"div\", {\n      key: '6480ca7648b278abb36477b3838bccbcd4995e2a',\n      ref: el => this.backgroundContentEl = el,\n      id: \"background-content\",\n      part: \"background\"\n    }), fixedSlotPlacement === 'before' ? h(\"slot\", {\n      name: \"fixed\"\n    }) : null, h(\"div\", {\n      key: '29a23b663f5f0215bb000820c01e1814c0d55985',\n      class: {\n        'inner-scroll': true,\n        'scroll-x': scrollX,\n        'scroll-y': scrollY,\n        overscroll: (scrollX || scrollY) && forceOverscroll\n      },\n      ref: scrollEl => this.scrollEl = scrollEl,\n      onScroll: this.scrollEvents ? ev => this.onScroll(ev) : undefined,\n      part: \"scroll\"\n    }, h(\"slot\", {\n      key: '0fe1bd05609a4b88ae2ce9addf5d5dc5dc1806f0'\n    })), transitionShadow ? h(\"div\", {\n      class: \"transition-effect\"\n    }, h(\"div\", {\n      class: \"transition-cover\"\n    }), h(\"div\", {\n      class: \"transition-shadow\"\n    })) : null, fixedSlotPlacement === 'after' ? h(\"slot\", {\n      name: \"fixed\"\n    }) : null);\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nconst getParentElement = el => {\n  var _a;\n  if (el.parentElement) {\n    // normal element with a parent element\n    return el.parentElement;\n  }\n  if ((_a = el.parentNode) === null || _a === void 0 ? void 0 : _a.host) {\n    // shadow dom's document fragment\n    return el.parentNode.host;\n  }\n  return null;\n};\nconst getPageElement = el => {\n  const tabs = el.closest('ion-tabs');\n  if (tabs) {\n    return tabs;\n  }\n  /**\n   * If we're in a popover, we need to use its wrapper so we can account for space\n   * between the popover and the edges of the screen. But if the popover contains\n   * its own page element, we should use that instead.\n   */\n  const page = el.closest('ion-app, ion-page, .ion-page, page-inner, .popover-content');\n  if (page) {\n    return page;\n  }\n  return getParentElement(el);\n};\n// ******** DOM READ ****************\nconst updateScrollDetail = (detail, el, timestamp, shouldStart) => {\n  const prevX = detail.currentX;\n  const prevY = detail.currentY;\n  const prevT = detail.currentTime;\n  const currentX = el.scrollLeft;\n  const currentY = el.scrollTop;\n  const timeDelta = timestamp - prevT;\n  if (shouldStart) {\n    // remember the start positions\n    detail.startTime = timestamp;\n    detail.startX = currentX;\n    detail.startY = currentY;\n    detail.velocityX = detail.velocityY = 0;\n  }\n  detail.currentTime = timestamp;\n  detail.currentX = detail.scrollLeft = currentX;\n  detail.currentY = detail.scrollTop = currentY;\n  detail.deltaX = currentX - detail.startX;\n  detail.deltaY = currentY - detail.startY;\n  if (timeDelta > 0 && timeDelta < 100) {\n    const velocityX = (currentX - prevX) / timeDelta;\n    const velocityY = (currentY - prevY) / timeDelta;\n    detail.velocityX = velocityX * 0.7 + detail.velocityX * 0.3;\n    detail.velocityY = velocityY * 0.7 + detail.velocityY * 0.3;\n  }\n};\nContent.style = IonContentStyle0;\nconst handleFooterFade = (scrollEl, baseEl) => {\n  readTask(() => {\n    const scrollTop = scrollEl.scrollTop;\n    const maxScroll = scrollEl.scrollHeight - scrollEl.clientHeight;\n    /**\n     * Toolbar background will fade\n     * out over fadeDuration in pixels.\n     */\n    const fadeDuration = 10;\n    /**\n     * Begin fading out maxScroll - 30px\n     * from the bottom of the content.\n     * Also determine how close we are\n     * to starting the fade. If we are\n     * before the starting point, the\n     * scale value will get clamped to 0.\n     * If we are after the maxScroll (rubber\n     * band scrolling), the scale value will\n     * get clamped to 1.\n     */\n    const fadeStart = maxScroll - fadeDuration;\n    const distanceToStart = scrollTop - fadeStart;\n    const scale = clamp(0, 1 - distanceToStart / fadeDuration, 1);\n    writeTask(() => {\n      baseEl.style.setProperty('--opacity-scale', scale.toString());\n    });\n  });\n};\nconst footerIosCss = \"ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-ios ion-toolbar:first-of-type{--border-width:0.55px 0 0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.footer-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.footer-translucent-ios ion-toolbar{--opacity:.8}}.footer-ios.ion-no-border ion-toolbar:first-of-type{--border-width:0}.footer-collapse-fade ion-toolbar{--opacity-scale:inherit}\";\nconst IonFooterIosStyle0 = footerIosCss;\nconst footerMdCss = \"ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.footer-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}\";\nconst IonFooterMdStyle0 = footerMdCss;\nconst Footer = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.keyboardCtrl = null;\n    this.checkCollapsibleFooter = () => {\n      const mode = getIonMode(this);\n      if (mode !== 'ios') {\n        return;\n      }\n      const {\n        collapse\n      } = this;\n      const hasFade = collapse === 'fade';\n      this.destroyCollapsibleFooter();\n      if (hasFade) {\n        const pageEl = this.el.closest('ion-app,ion-page,.ion-page,page-inner');\n        const contentEl = pageEl ? findIonContent(pageEl) : null;\n        if (!contentEl) {\n          printIonContentErrorMsg(this.el);\n          return;\n        }\n        this.setupFadeFooter(contentEl);\n      }\n    };\n    this.setupFadeFooter = async contentEl => {\n      const scrollEl = this.scrollEl = await getScrollElement(contentEl);\n      /**\n       * Handle fading of toolbars on scroll\n       */\n      this.contentScrollCallback = () => {\n        handleFooterFade(scrollEl, this.el);\n      };\n      scrollEl.addEventListener('scroll', this.contentScrollCallback);\n      handleFooterFade(scrollEl, this.el);\n    };\n    this.keyboardVisible = false;\n    this.collapse = undefined;\n    this.translucent = false;\n  }\n  componentDidLoad() {\n    this.checkCollapsibleFooter();\n  }\n  componentDidUpdate() {\n    this.checkCollapsibleFooter();\n  }\n  async connectedCallback() {\n    this.keyboardCtrl = await createKeyboardController(async (keyboardOpen, waitForResize) => {\n      /**\n       * If the keyboard is hiding, then we need to wait\n       * for the webview to resize. Otherwise, the footer\n       * will flicker before the webview resizes.\n       */\n      if (keyboardOpen === false && waitForResize !== undefined) {\n        await waitForResize;\n      }\n      this.keyboardVisible = keyboardOpen; // trigger re-render by updating state\n    });\n  }\n  disconnectedCallback() {\n    if (this.keyboardCtrl) {\n      this.keyboardCtrl.destroy();\n    }\n  }\n  destroyCollapsibleFooter() {\n    if (this.scrollEl && this.contentScrollCallback) {\n      this.scrollEl.removeEventListener('scroll', this.contentScrollCallback);\n      this.contentScrollCallback = undefined;\n    }\n  }\n  render() {\n    const {\n      translucent,\n      collapse\n    } = this;\n    const mode = getIonMode(this);\n    const tabs = this.el.closest('ion-tabs');\n    const tabBar = tabs === null || tabs === void 0 ? void 0 : tabs.querySelector(':scope > ion-tab-bar');\n    return h(Host, {\n      key: 'ddc228f1a1e7fa4f707dccf74db2490ca3241137',\n      role: \"contentinfo\",\n      class: {\n        [mode]: true,\n        // Used internally for styling\n        [`footer-${mode}`]: true,\n        [`footer-translucent`]: translucent,\n        [`footer-translucent-${mode}`]: translucent,\n        ['footer-toolbar-padding']: !this.keyboardVisible && (!tabBar || tabBar.slot !== 'bottom'),\n        [`footer-collapse-${collapse}`]: collapse !== undefined\n      }\n    }, mode === 'ios' && translucent && h(\"div\", {\n      key: 'e16ed4963ff94e06de77eb8038201820af73937c',\n      class: \"footer-background\"\n    }), h(\"slot\", {\n      key: 'f186934febf85d37133d9351a96c1a64b0a4b203'\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nFooter.style = {\n  ios: IonFooterIosStyle0,\n  md: IonFooterMdStyle0\n};\nconst TRANSITION = 'all 0.2s ease-in-out';\nconst cloneElement = tagName => {\n  const getCachedEl = document.querySelector(`${tagName}.ion-cloned-element`);\n  if (getCachedEl !== null) {\n    return getCachedEl;\n  }\n  const clonedEl = document.createElement(tagName);\n  clonedEl.classList.add('ion-cloned-element');\n  clonedEl.style.setProperty('display', 'none');\n  document.body.appendChild(clonedEl);\n  return clonedEl;\n};\nconst createHeaderIndex = headerEl => {\n  if (!headerEl) {\n    return;\n  }\n  const toolbars = headerEl.querySelectorAll('ion-toolbar');\n  return {\n    el: headerEl,\n    toolbars: Array.from(toolbars).map(toolbar => {\n      const ionTitleEl = toolbar.querySelector('ion-title');\n      return {\n        el: toolbar,\n        background: toolbar.shadowRoot.querySelector('.toolbar-background'),\n        ionTitleEl,\n        innerTitleEl: ionTitleEl ? ionTitleEl.shadowRoot.querySelector('.toolbar-title') : null,\n        ionButtonsEl: Array.from(toolbar.querySelectorAll('ion-buttons'))\n      };\n    })\n  };\n};\nconst handleContentScroll = (scrollEl, scrollHeaderIndex, contentEl) => {\n  readTask(() => {\n    const scrollTop = scrollEl.scrollTop;\n    const scale = clamp(1, 1 + -scrollTop / 500, 1.1);\n    // Native refresher should not cause titles to scale\n    const nativeRefresher = contentEl.querySelector('ion-refresher.refresher-native');\n    if (nativeRefresher === null) {\n      writeTask(() => {\n        scaleLargeTitles(scrollHeaderIndex.toolbars, scale);\n      });\n    }\n  });\n};\nconst setToolbarBackgroundOpacity = (headerEl, opacity) => {\n  /**\n   * Fading in the backdrop opacity\n   * should happen after the large title\n   * has collapsed, so it is handled\n   * by handleHeaderFade()\n   */\n  if (headerEl.collapse === 'fade') {\n    return;\n  }\n  if (opacity === undefined) {\n    headerEl.style.removeProperty('--opacity-scale');\n  } else {\n    headerEl.style.setProperty('--opacity-scale', opacity.toString());\n  }\n};\nconst handleToolbarBorderIntersection = (ev, mainHeaderIndex, scrollTop) => {\n  if (!ev[0].isIntersecting) {\n    return;\n  }\n  /**\n   * There is a bug in Safari where overflow scrolling on a non-body element\n   * does not always reset the scrollTop position to 0 when letting go. It will\n   * set to 1 once the rubber band effect has ended. This causes the background to\n   * appear slightly on certain app setups.\n   *\n   * Additionally, we check if user is rubber banding (scrolling is negative)\n   * as this can mean they are using pull to refresh. Once the refresher starts,\n   * the content is transformed which can cause the intersection observer to erroneously\n   * fire here as well.\n   */\n  const scale = ev[0].intersectionRatio > 0.9 || scrollTop <= 0 ? 0 : (1 - ev[0].intersectionRatio) * 100 / 75;\n  setToolbarBackgroundOpacity(mainHeaderIndex.el, scale === 1 ? undefined : scale);\n};\n/**\n * If toolbars are intersecting, hide the scrollable toolbar content\n * and show the primary toolbar content. If the toolbars are not intersecting,\n * hide the primary toolbar content and show the scrollable toolbar content\n */\nconst handleToolbarIntersection = (ev,\n// TODO(FW-2832): type (IntersectionObserverEntry[] triggers errors which should be sorted)\nmainHeaderIndex, scrollHeaderIndex, scrollEl) => {\n  writeTask(() => {\n    const scrollTop = scrollEl.scrollTop;\n    handleToolbarBorderIntersection(ev, mainHeaderIndex, scrollTop);\n    const event = ev[0];\n    const intersection = event.intersectionRect;\n    const intersectionArea = intersection.width * intersection.height;\n    const rootArea = event.rootBounds.width * event.rootBounds.height;\n    const isPageHidden = intersectionArea === 0 && rootArea === 0;\n    const leftDiff = Math.abs(intersection.left - event.boundingClientRect.left);\n    const rightDiff = Math.abs(intersection.right - event.boundingClientRect.right);\n    const isPageTransitioning = intersectionArea > 0 && (leftDiff >= 5 || rightDiff >= 5);\n    if (isPageHidden || isPageTransitioning) {\n      return;\n    }\n    if (event.isIntersecting) {\n      setHeaderActive(mainHeaderIndex, false);\n      setHeaderActive(scrollHeaderIndex);\n    } else {\n      /**\n       * There is a bug with IntersectionObserver on Safari\n       * where `event.isIntersecting === false` when cancelling\n       * a swipe to go back gesture. Checking the intersection\n       * x, y, width, and height provides a workaround. This bug\n       * does not happen when using Safari + Web Animations,\n       * only Safari + CSS Animations.\n       */\n      const hasValidIntersection = intersection.x === 0 && intersection.y === 0 || intersection.width !== 0 && intersection.height !== 0;\n      if (hasValidIntersection && scrollTop > 0) {\n        setHeaderActive(mainHeaderIndex);\n        setHeaderActive(scrollHeaderIndex, false);\n        setToolbarBackgroundOpacity(mainHeaderIndex.el);\n      }\n    }\n  });\n};\nconst setHeaderActive = (headerIndex, active = true) => {\n  const headerEl = headerIndex.el;\n  const toolbars = headerIndex.toolbars;\n  const ionTitles = toolbars.map(toolbar => toolbar.ionTitleEl);\n  if (active) {\n    headerEl.classList.remove('header-collapse-condense-inactive');\n    ionTitles.forEach(ionTitle => {\n      if (ionTitle) {\n        ionTitle.removeAttribute('aria-hidden');\n      }\n    });\n  } else {\n    headerEl.classList.add('header-collapse-condense-inactive');\n    /**\n     * The small title should only be accessed by screen readers\n     * when the large title collapses into the small title due\n     * to scrolling.\n     *\n     * Originally, the header was given `aria-hidden=\"true\"`\n     * but this caused issues with screen readers not being\n     * able to access any focusable elements within the header.\n     */\n    ionTitles.forEach(ionTitle => {\n      if (ionTitle) {\n        ionTitle.setAttribute('aria-hidden', 'true');\n      }\n    });\n  }\n};\nconst scaleLargeTitles = (toolbars = [], scale = 1, transition = false) => {\n  toolbars.forEach(toolbar => {\n    const ionTitle = toolbar.ionTitleEl;\n    const titleDiv = toolbar.innerTitleEl;\n    if (!ionTitle || ionTitle.size !== 'large') {\n      return;\n    }\n    titleDiv.style.transition = transition ? TRANSITION : '';\n    titleDiv.style.transform = `scale3d(${scale}, ${scale}, 1)`;\n  });\n};\nconst handleHeaderFade = (scrollEl, baseEl, condenseHeader) => {\n  readTask(() => {\n    const scrollTop = scrollEl.scrollTop;\n    const baseElHeight = baseEl.clientHeight;\n    const fadeStart = condenseHeader ? condenseHeader.clientHeight : 0;\n    /**\n     * If we are using fade header with a condense\n     * header, then the toolbar backgrounds should\n     * not begin to fade in until the condense\n     * header has fully collapsed.\n     *\n     * Additionally, the main content should not\n     * overflow out of the container until the\n     * condense header has fully collapsed. When\n     * using just the condense header the content\n     * should overflow out of the container.\n     */\n    if (condenseHeader !== null && scrollTop < fadeStart) {\n      baseEl.style.setProperty('--opacity-scale', '0');\n      scrollEl.style.setProperty('clip-path', `inset(${baseElHeight}px 0px 0px 0px)`);\n      return;\n    }\n    const distanceToStart = scrollTop - fadeStart;\n    const fadeDuration = 10;\n    const scale = clamp(0, distanceToStart / fadeDuration, 1);\n    writeTask(() => {\n      scrollEl.style.removeProperty('clip-path');\n      baseEl.style.setProperty('--opacity-scale', scale.toString());\n    });\n  });\n};\nconst headerIosCss = \"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-ios ion-toolbar:last-of-type{--border-width:0 0 0.55px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.header-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.header-translucent-ios ion-toolbar{--opacity:.8}.header-collapse-condense-inactive .header-background{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}}.header-ios.ion-no-border ion-toolbar:last-of-type{--border-width:0}.header-collapse-fade ion-toolbar{--opacity-scale:inherit}.header-collapse-condense{z-index:9}.header-collapse-condense ion-toolbar{position:-webkit-sticky;position:sticky;top:0}.header-collapse-condense ion-toolbar:first-of-type{padding-top:0px;z-index:1}.header-collapse-condense ion-toolbar{--background:var(--ion-background-color, #fff);z-index:0}.header-collapse-condense ion-toolbar:last-of-type{--border-width:0px}.header-collapse-condense ion-toolbar ion-searchbar{padding-top:0px;padding-bottom:13px}.header-collapse-main{--opacity-scale:1}.header-collapse-main ion-toolbar{--opacity-scale:inherit}.header-collapse-main ion-toolbar.in-toolbar ion-title,.header-collapse-main ion-toolbar.in-toolbar ion-buttons{-webkit-transition:all 0.2s ease-in-out;transition:all 0.2s ease-in-out}.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-buttons.buttons-collapse{opacity:0;pointer-events:none}.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-buttons.buttons-collapse{visibility:hidden}ion-header.header-ios:not(.header-collapse-main):has(~ion-content ion-header.header-ios[collapse=condense],~ion-content ion-header.header-ios.header-collapse-condense){opacity:0}\";\nconst IonHeaderIosStyle0 = headerIosCss;\nconst headerMdCss = \"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.header-collapse-condense{display:none}.header-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}\";\nconst IonHeaderMdStyle0 = headerMdCss;\nconst Header = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.inheritedAttributes = {};\n    this.setupFadeHeader = async (contentEl, condenseHeader) => {\n      const scrollEl = this.scrollEl = await getScrollElement(contentEl);\n      /**\n       * Handle fading of toolbars on scroll\n       */\n      this.contentScrollCallback = () => {\n        handleHeaderFade(this.scrollEl, this.el, condenseHeader);\n      };\n      scrollEl.addEventListener('scroll', this.contentScrollCallback);\n      handleHeaderFade(this.scrollEl, this.el, condenseHeader);\n    };\n    this.collapse = undefined;\n    this.translucent = false;\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n  }\n  componentDidLoad() {\n    this.checkCollapsibleHeader();\n  }\n  componentDidUpdate() {\n    this.checkCollapsibleHeader();\n  }\n  disconnectedCallback() {\n    this.destroyCollapsibleHeader();\n  }\n  async checkCollapsibleHeader() {\n    const mode = getIonMode(this);\n    if (mode !== 'ios') {\n      return;\n    }\n    const {\n      collapse\n    } = this;\n    const hasCondense = collapse === 'condense';\n    const hasFade = collapse === 'fade';\n    this.destroyCollapsibleHeader();\n    if (hasCondense) {\n      const pageEl = this.el.closest('ion-app,ion-page,.ion-page,page-inner');\n      const contentEl = pageEl ? findIonContent(pageEl) : null;\n      // Cloned elements are always needed in iOS transition\n      writeTask(() => {\n        const title = cloneElement('ion-title');\n        title.size = 'large';\n        cloneElement('ion-back-button');\n      });\n      await this.setupCondenseHeader(contentEl, pageEl);\n    } else if (hasFade) {\n      const pageEl = this.el.closest('ion-app,ion-page,.ion-page,page-inner');\n      const contentEl = pageEl ? findIonContent(pageEl) : null;\n      if (!contentEl) {\n        printIonContentErrorMsg(this.el);\n        return;\n      }\n      const condenseHeader = contentEl.querySelector('ion-header[collapse=\"condense\"]');\n      await this.setupFadeHeader(contentEl, condenseHeader);\n    }\n  }\n  destroyCollapsibleHeader() {\n    if (this.intersectionObserver) {\n      this.intersectionObserver.disconnect();\n      this.intersectionObserver = undefined;\n    }\n    if (this.scrollEl && this.contentScrollCallback) {\n      this.scrollEl.removeEventListener('scroll', this.contentScrollCallback);\n      this.contentScrollCallback = undefined;\n    }\n    if (this.collapsibleMainHeader) {\n      this.collapsibleMainHeader.classList.remove('header-collapse-main');\n      this.collapsibleMainHeader = undefined;\n    }\n  }\n  async setupCondenseHeader(contentEl, pageEl) {\n    if (!contentEl || !pageEl) {\n      printIonContentErrorMsg(this.el);\n      return;\n    }\n    if (typeof IntersectionObserver === 'undefined') {\n      return;\n    }\n    this.scrollEl = await getScrollElement(contentEl);\n    const headers = pageEl.querySelectorAll('ion-header');\n    this.collapsibleMainHeader = Array.from(headers).find(header => header.collapse !== 'condense');\n    if (!this.collapsibleMainHeader) {\n      return;\n    }\n    const mainHeaderIndex = createHeaderIndex(this.collapsibleMainHeader);\n    const scrollHeaderIndex = createHeaderIndex(this.el);\n    if (!mainHeaderIndex || !scrollHeaderIndex) {\n      return;\n    }\n    setHeaderActive(mainHeaderIndex, false);\n    setToolbarBackgroundOpacity(mainHeaderIndex.el, 0);\n    /**\n     * Handle interaction between toolbar collapse and\n     * showing/hiding content in the primary ion-header\n     * as well as progressively showing/hiding the main header\n     * border as the top-most toolbar collapses or expands.\n     */\n    const toolbarIntersection = ev => {\n      handleToolbarIntersection(ev, mainHeaderIndex, scrollHeaderIndex, this.scrollEl);\n    };\n    this.intersectionObserver = new IntersectionObserver(toolbarIntersection, {\n      root: contentEl,\n      threshold: [0.25, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1]\n    });\n    this.intersectionObserver.observe(scrollHeaderIndex.toolbars[scrollHeaderIndex.toolbars.length - 1].el);\n    /**\n     * Handle scaling of large iOS titles and\n     * showing/hiding border on last toolbar\n     * in primary header\n     */\n    this.contentScrollCallback = () => {\n      handleContentScroll(this.scrollEl, scrollHeaderIndex, contentEl);\n    };\n    this.scrollEl.addEventListener('scroll', this.contentScrollCallback);\n    writeTask(() => {\n      if (this.collapsibleMainHeader !== undefined) {\n        this.collapsibleMainHeader.classList.add('header-collapse-main');\n      }\n    });\n  }\n  render() {\n    const {\n      translucent,\n      inheritedAttributes\n    } = this;\n    const mode = getIonMode(this);\n    const collapse = this.collapse || 'none';\n    // banner role must be at top level, so remove role if inside a menu\n    const roleType = hostContext('ion-menu', this.el) ? 'none' : 'banner';\n    return h(Host, Object.assign({\n      key: 'b6cc27f0b08afc9fcc889683525da765d80ba672',\n      role: roleType,\n      class: {\n        [mode]: true,\n        // Used internally for styling\n        [`header-${mode}`]: true,\n        [`header-translucent`]: this.translucent,\n        [`header-collapse-${collapse}`]: true,\n        [`header-translucent-${mode}`]: this.translucent\n      }\n    }, inheritedAttributes), mode === 'ios' && translucent && h(\"div\", {\n      key: '395766d4dcee3398bc91960db21f922095292f14',\n      class: \"header-background\"\n    }), h(\"slot\", {\n      key: '09a67ece27b258ff1248805d43d92a49b2c6859a'\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nHeader.style = {\n  ios: IonHeaderIosStyle0,\n  md: IonHeaderMdStyle0\n};\nconst routerOutletCss = \":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}\";\nconst IonRouterOutletStyle0 = routerOutletCss;\nconst RouterOutlet = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionNavWillLoad = createEvent(this, \"ionNavWillLoad\", 7);\n    this.ionNavWillChange = createEvent(this, \"ionNavWillChange\", 3);\n    this.ionNavDidChange = createEvent(this, \"ionNavDidChange\", 3);\n    this.lockController = createLockController();\n    this.gestureOrAnimationInProgress = false;\n    this.mode = getIonMode(this);\n    this.delegate = undefined;\n    this.animated = true;\n    this.animation = undefined;\n    this.swipeHandler = undefined;\n  }\n  swipeHandlerChanged() {\n    if (this.gesture) {\n      this.gesture.enable(this.swipeHandler !== undefined);\n    }\n  }\n  async connectedCallback() {\n    const onStart = () => {\n      this.gestureOrAnimationInProgress = true;\n      if (this.swipeHandler) {\n        this.swipeHandler.onStart();\n      }\n    };\n    this.gesture = (await import('./swipe-back-0184f6b3.js')).createSwipeBackGesture(this.el, () => !this.gestureOrAnimationInProgress && !!this.swipeHandler && this.swipeHandler.canStart(), () => onStart(), step => {\n      var _a;\n      return (_a = this.ani) === null || _a === void 0 ? void 0 : _a.progressStep(step);\n    }, (shouldComplete, step, dur) => {\n      if (this.ani) {\n        this.ani.onFinish(() => {\n          this.gestureOrAnimationInProgress = false;\n          if (this.swipeHandler) {\n            this.swipeHandler.onEnd(shouldComplete);\n          }\n        }, {\n          oneTimeCallback: true\n        });\n        // Account for rounding errors in JS\n        let newStepValue = shouldComplete ? -0.001 : 0.001;\n        /**\n         * Animation will be reversed here, so need to\n         * reverse the easing curve as well\n         *\n         * Additionally, we need to account for the time relative\n         * to the new easing curve, as `stepValue` is going to be given\n         * in terms of a linear curve.\n         */\n        if (!shouldComplete) {\n          this.ani.easing('cubic-bezier(1, 0, 0.68, 0.28)');\n          newStepValue += getTimeGivenProgression([0, 0], [1, 0], [0.68, 0.28], [1, 1], step)[0];\n        } else {\n          newStepValue += getTimeGivenProgression([0, 0], [0.32, 0.72], [0, 1], [1, 1], step)[0];\n        }\n        this.ani.progressEnd(shouldComplete ? 1 : 0, newStepValue, dur);\n      } else {\n        this.gestureOrAnimationInProgress = false;\n      }\n    });\n    this.swipeHandlerChanged();\n  }\n  componentWillLoad() {\n    this.ionNavWillLoad.emit();\n  }\n  disconnectedCallback() {\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n  }\n  /** @internal */\n  async commit(enteringEl, leavingEl, opts) {\n    const unlock = await this.lockController.lock();\n    let changed = false;\n    try {\n      changed = await this.transition(enteringEl, leavingEl, opts);\n    } catch (e) {\n      printIonError('[ion-router-outlet] - Exception in commit:', e);\n    }\n    unlock();\n    return changed;\n  }\n  /** @internal */\n  async setRouteId(id, params, direction, animation) {\n    const changed = await this.setRoot(id, params, {\n      duration: direction === 'root' ? 0 : undefined,\n      direction: direction === 'back' ? 'back' : 'forward',\n      animationBuilder: animation\n    });\n    return {\n      changed,\n      element: this.activeEl\n    };\n  }\n  /** @internal */\n  async getRouteId() {\n    const active = this.activeEl;\n    return active ? {\n      id: active.tagName,\n      element: active,\n      params: this.activeParams\n    } : undefined;\n  }\n  async setRoot(component, params, opts) {\n    if (this.activeComponent === component && shallowEqualStringMap(params, this.activeParams)) {\n      return false;\n    }\n    // attach entering view to DOM\n    const leavingEl = this.activeEl;\n    const enteringEl = await attachComponent(this.delegate, this.el, component, ['ion-page', 'ion-page-invisible'], params);\n    this.activeComponent = component;\n    this.activeEl = enteringEl;\n    this.activeParams = params;\n    // commit animation\n    await this.commit(enteringEl, leavingEl, opts);\n    await detachComponent(this.delegate, leavingEl);\n    return true;\n  }\n  async transition(enteringEl, leavingEl, opts = {}) {\n    if (leavingEl === enteringEl) {\n      return false;\n    }\n    // emit nav will change event\n    this.ionNavWillChange.emit();\n    const {\n      el,\n      mode\n    } = this;\n    const animated = this.animated && config.getBoolean('animated', true);\n    const animationBuilder = opts.animationBuilder || this.animation || config.get('navAnimation');\n    await transition(Object.assign(Object.assign({\n      mode,\n      animated,\n      enteringEl,\n      leavingEl,\n      baseEl: el,\n      /**\n       * We need to wait for all Stencil components\n       * to be ready only when using the lazy\n       * loaded bundle.\n       */\n      deepWait: hasLazyBuild(el),\n      progressCallback: opts.progressAnimation ? ani => {\n        /**\n         * Because this progress callback is called asynchronously\n         * it is possible for the gesture to start and end before\n         * the animation is ever set. In that scenario, we should\n         * immediately call progressEnd so that the transition promise\n         * resolves and the gesture does not get locked up.\n         */\n        if (ani !== undefined && !this.gestureOrAnimationInProgress) {\n          this.gestureOrAnimationInProgress = true;\n          ani.onFinish(() => {\n            this.gestureOrAnimationInProgress = false;\n            if (this.swipeHandler) {\n              this.swipeHandler.onEnd(false);\n            }\n          }, {\n            oneTimeCallback: true\n          });\n          /**\n           * Playing animation to beginning\n           * with a duration of 0 prevents\n           * any flickering when the animation\n           * is later cleaned up.\n           */\n          ani.progressEnd(0, 0, 0);\n        } else {\n          this.ani = ani;\n        }\n      } : undefined\n    }, opts), {\n      animationBuilder\n    }));\n    // emit nav changed event\n    this.ionNavDidChange.emit();\n    return true;\n  }\n  render() {\n    return h(\"slot\", {\n      key: 'e34e02b5154172c8d5cdd187b6ea58119b6946eb'\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"swipeHandler\": [\"swipeHandlerChanged\"]\n    };\n  }\n};\nRouterOutlet.style = IonRouterOutletStyle0;\nconst titleIosCss = \":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{top:0;-webkit-padding-start:90px;padding-inline-start:90px;-webkit-padding-end:90px;padding-inline-end:90px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);position:absolute;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0);font-size:min(1.0625rem, 20.4px);font-weight:600;text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host{inset-inline-start:0}:host(.title-small){-webkit-padding-start:9px;padding-inline-start:9px;-webkit-padding-end:9px;padding-inline-end:9px;padding-top:6px;padding-bottom:16px;position:relative;font-size:min(0.8125rem, 23.4px);font-weight:normal}:host(.title-large){-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:2px;padding-bottom:4px;-webkit-transform-origin:left center;transform-origin:left center;position:static;-ms-flex-align:end;align-items:flex-end;min-width:100%;font-size:min(2.125rem, 61.2px);font-weight:700;text-align:start}:host(.title-large.title-rtl){-webkit-transform-origin:right center;transform-origin:right center}:host(.title-large.ion-cloned-element){--color:var(--ion-text-color, #000);font-family:var(--ion-font-family)}:host(.title-large) .toolbar-title{-webkit-transform-origin:inherit;transform-origin:inherit;width:auto}:host-context([dir=rtl]):host(.title-large) .toolbar-title,:host-context([dir=rtl]).title-large .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}@supports selector(:dir(rtl)){:host(.title-large:dir(rtl)) .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}}\";\nconst IonTitleIosStyle0 = titleIosCss;\nconst titleMdCss = \":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;font-size:1.25rem;font-weight:500;letter-spacing:0.0125em}:host(.title-small){width:100%;height:100%;font-size:0.9375rem;font-weight:normal}\";\nconst IonTitleMdStyle0 = titleMdCss;\nconst ToolbarTitle = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.color = undefined;\n    this.size = undefined;\n  }\n  sizeChanged() {\n    this.emitStyle();\n  }\n  connectedCallback() {\n    this.emitStyle();\n  }\n  emitStyle() {\n    const size = this.getSize();\n    this.ionStyle.emit({\n      [`title-${size}`]: true\n    });\n  }\n  getSize() {\n    return this.size !== undefined ? this.size : 'default';\n  }\n  render() {\n    const mode = getIonMode(this);\n    const size = this.getSize();\n    return h(Host, {\n      key: '3f7b19c99961dbb86c0a925218332528b22e6880',\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        [`title-${size}`]: true,\n        'title-rtl': document.dir === 'rtl'\n      })\n    }, h(\"div\", {\n      key: '12054fbdd60e40a15875e442c20143766fc34fc3',\n      class: \"toolbar-title\"\n    }, h(\"slot\", {\n      key: '9f14fb14a67d4bd1e4536a4d64a637fbe5a151c7'\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"size\": [\"sizeChanged\"]\n    };\n  }\n};\nToolbarTitle.style = {\n  ios: IonTitleIosStyle0,\n  md: IonTitleMdStyle0\n};\nconst toolbarIosCss = \":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-color-step-50, var(--ion-background-color-step-50, #f7f7f7)));--color:var(--ion-toolbar-color, var(--ion-text-color, #000));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.2)))));--padding-top:3px;--padding-bottom:3px;--padding-start:4px;--padding-end:4px;--min-height:44px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:4;order:4;min-width:0}:host(.toolbar-segment) .toolbar-content{display:-ms-inline-flexbox;display:inline-flex}:host(.toolbar-searchbar) .toolbar-container{padding-top:0;padding-bottom:0}:host(.toolbar-searchbar) ::slotted(*){-ms-flex-item-align:start;align-self:start}:host(.toolbar-searchbar) ::slotted(ion-chip){margin-top:3px}::slotted(ion-buttons){min-height:38px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:3;order:3}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}:host(.toolbar-title-large) .toolbar-container{-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:start;align-items:flex-start}:host(.toolbar-title-large) .toolbar-content ion-title{-ms-flex:1;flex:1;-ms-flex-order:8;order:8;min-width:100%}\";\nconst IonToolbarIosStyle0 = toolbarIosCss;\nconst toolbarMdCss = \":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-background-color, #fff));--color:var(--ion-toolbar-color, var(--ion-text-color, #424242));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, #c1c4cd))));--padding-top:0;--padding-bottom:0;--padding-start:0;--padding-end:0;--min-height:56px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:3;order:3;min-width:0;max-width:100%}::slotted(.buttons-first-slot){-webkit-margin-start:4px;margin-inline-start:4px}::slotted(.buttons-last-slot){-webkit-margin-end:4px;margin-inline-end:4px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:4;order:4}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}\";\nconst IonToolbarMdStyle0 = toolbarMdCss;\nconst Toolbar = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.childrenStyles = new Map();\n    this.color = undefined;\n  }\n  componentWillLoad() {\n    const buttons = Array.from(this.el.querySelectorAll('ion-buttons'));\n    const firstButtons = buttons.find(button => {\n      return button.slot === 'start';\n    });\n    if (firstButtons) {\n      firstButtons.classList.add('buttons-first-slot');\n    }\n    const buttonsReversed = buttons.reverse();\n    const lastButtons = buttonsReversed.find(button => button.slot === 'end') || buttonsReversed.find(button => button.slot === 'primary') || buttonsReversed.find(button => button.slot === 'secondary');\n    if (lastButtons) {\n      lastButtons.classList.add('buttons-last-slot');\n    }\n  }\n  childrenStyle(ev) {\n    ev.stopPropagation();\n    const tagName = ev.target.tagName;\n    const updatedStyles = ev.detail;\n    const newStyles = {};\n    const childStyles = this.childrenStyles.get(tagName) || {};\n    let hasStyleChange = false;\n    Object.keys(updatedStyles).forEach(key => {\n      const childKey = `toolbar-${key}`;\n      const newValue = updatedStyles[key];\n      if (newValue !== childStyles[childKey]) {\n        hasStyleChange = true;\n      }\n      if (newValue) {\n        newStyles[childKey] = true;\n      }\n    });\n    if (hasStyleChange) {\n      this.childrenStyles.set(tagName, newStyles);\n      forceUpdate(this);\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    const childStyles = {};\n    this.childrenStyles.forEach(value => {\n      Object.assign(childStyles, value);\n    });\n    return h(Host, {\n      key: '402afe7ce0c97883cedd0e48a5a0492a9bfe76ae',\n      class: Object.assign(Object.assign({}, childStyles), createColorClasses(this.color, {\n        [mode]: true,\n        'in-toolbar': hostContext('ion-toolbar', this.el)\n      }))\n    }, h(\"div\", {\n      key: '2465a6dc8d507ec650538378d1be2abd399c58ad',\n      class: \"toolbar-background\",\n      part: \"background\"\n    }), h(\"div\", {\n      key: '6075096afd12303b961e4fe9ad345ef2887573af',\n      class: \"toolbar-container\",\n      part: \"container\"\n    }, h(\"slot\", {\n      key: '8b7eec1148cfeb339d87cdf9273f2104703e7601',\n      name: \"start\"\n    }), h(\"slot\", {\n      key: 'b102d3926cade24faf78b7af78ad5e192c4c0308',\n      name: \"secondary\"\n    }), h(\"div\", {\n      key: 'c6ab2e978328324c6f9e7892024cbcd8b8987067',\n      class: \"toolbar-content\",\n      part: \"content\"\n    }, h(\"slot\", {\n      key: '86f8952c4355a9df5b4bbb95e9d0cafefd272d5b'\n    })), h(\"slot\", {\n      key: '501e43431da6b9dd35b47b79222f948d445f7a78',\n      name: \"primary\"\n    }), h(\"slot\", {\n      key: '84bf1a15a5e52e8e94df9f479c4ce18004f5ab57',\n      name: \"end\"\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nToolbar.style = {\n  ios: IonToolbarIosStyle0,\n  md: IonToolbarMdStyle0\n};\nexport { App as ion_app, Buttons as ion_buttons, Content as ion_content, Footer as ion_footer, Header as ion_header, RouterOutlet as ion_router_outlet, ToolbarTitle as ion_title, Toolbar as ion_toolbar };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAmBM,QACA,cACA,KAwEA,gBAmBA,KAQA,eACA,qBACA,cACA,oBACA,SAsBA,YACA,kBACA,SAkYA,kBAYA,gBAiBA,oBA2BA,kBA4BA,cACA,oBACA,aACA,mBACA,QAwGA,YACA,cAWA,mBAmBA,qBAaA,6BAgBA,iCAuBA,2BAsCA,iBA6BA,kBAWA,kBA+BA,cACA,oBACA,aACA,mBACA,QAiKA,iBACA,uBACA,cAkMA,aACA,mBACA,YACA,kBACA,cAoDA,eACA,qBACA,cACA,oBACA;AA3zCN;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAM,SAAS;AACf,IAAM,eAAe;AACrB,IAAM,MAAM,MAAM;AAAA,MAChB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAAA,MAChC;AAAA,MACA,mBAAmB;AACjB;AACE,cAAI,MAAY;AACd,kBAAM,WAAW,WAAW,QAAQ,QAAQ;AAC5C,gBAAI,CAAC,OAAO,WAAW,UAAU,GAAG;AAClC,qBAAO,8BAAqB,EAAE,KAAK,YAAU,OAAO,cAAc,MAAM,CAAC;AAAA,YAC3E;AACA,gBAAI,OAAO,WAAW,aAAa,QAAQ,GAAG;AAC5C,qBAAO,mCAA0B,EAAE,KAAK,YAAU,OAAO,eAAe,CAAC;AAAA,YAC3E;AACA,gBAAI,OAAO,WAAW,cAAc,eAAe,CAAC,GAAG;AAKrD,oBAAM,WAAW,WAAW,QAAQ,KAAK,IAAI,QAAQ;AACrD,qBAAO,oCAA2B,EAAE,KAAK,YAAU,OAAO,gBAAgB,QAAQ,QAAQ,CAAC;AAAA,YAC7F;AACA,kBAAM,2BAA2B,MAAM,OAAO,6CAAoC;AAClF,kBAAM,mCAAmC,YAAY,sBAAsB;AAC3E,gBAAI,OAAO,WAAW,sBAAsB,gCAAgC,GAAG;AAC7E,uCAAyB,wBAAwB;AAAA,YACnD,OAAO;AAKL,kBAAI,sBAAsB,GAAG;AAC3B,gCAAgB,6KAA6K;AAAA,cAC/L;AACA,uCAAyB,wBAAwB;AAAA,YACnD;AACA,gBAAI,OAAO,WAAW,aAAa;AACjC,qBAAO,iCAAwB,EAAE,KAAK,YAAU,OAAO,oBAAoB,MAAM,CAAC;AAAA,YACpF;AACA,mBAAO,sCAA6B,EAAE,KAAK,YAAU,KAAK,eAAe,OAAO,kBAAkB,CAAC;AAAA,UACrG,EAAC;AAAA,QACH;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUM,SAAS,UAAU;AAAA;AACvB,cAAI,KAAK,cAAc;AACrB,iBAAK,aAAa,SAAS,QAAQ;AAAA,UACrC;AAAA,QACF;AAAA;AAAA,MACA,SAAS;AACP,cAAM,OAAO,WAAW,IAAI;AAC5B,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,OAAO;AAAA,YACL,CAAC,IAAI,GAAG;AAAA,YACR,YAAY;AAAA,YACZ,2BAA2B,OAAO,WAAW,wBAAwB;AAAA,UACvE;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,IACF;AACA,IAAM,iBAAiB,MAAM;AAI3B,YAAM,gBAAgB,WAAW,QAAQ,KAAK,KAAK,WAAW,QAAQ,QAAQ;AAC9E,UAAI,eAAe;AACjB,eAAO;AAAA,MACT;AAMA,YAAM,qBAAqB,WAAW,QAAQ,SAAS,KAAK,WAAW,QAAQ,WAAW;AAC1F,UAAI,oBAAoB;AACtB,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,IAAM,MAAM,cAAY;AACtB,UAAI,yBAAyB,QAAQ;AACnC,eAAO,oBAAoB,QAAQ;AAAA,MACrC,OAAO;AACL,mBAAW,UAAU,EAAE;AAAA,MACzB;AAAA,IACF;AACA,QAAI,QAAQ;AACZ,IAAM,gBAAgB;AACtB,IAAM,sBAAsB;AAC5B,IAAM,eAAe;AACrB,IAAM,qBAAqB;AAC3B,IAAM,UAAU,MAAM;AAAA,MACpB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,WAAW;AAAA,MAClB;AAAA,MACA,SAAS;AACP,cAAM,OAAO,WAAW,IAAI;AAC5B,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,OAAO;AAAA,YACL,CAAC,IAAI,GAAG;AAAA,YACR,CAAC,kBAAkB,GAAG,KAAK;AAAA,UAC7B;AAAA,QACF,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,QACP,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AACA,YAAQ,QAAQ;AAAA,MACd,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AACA,IAAM,aAAa;AACnB,IAAM,mBAAmB;AACzB,IAAM,UAAU,MAAM;AAAA,MACpB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,aAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,aAAK,eAAe,YAAY,MAAM,gBAAgB,CAAC;AACvD,aAAK,WAAW;AAChB,aAAK,cAAc;AACnB,aAAK,aAAa;AAClB,aAAK,SAAS;AACd,aAAK,OAAO;AACZ,aAAK,UAAU;AACf,aAAK,gBAAgB;AACrB,aAAK,gBAAgB;AACrB,aAAK,sBAAsB,CAAC;AAC5B,aAAK,cAAc;AAInB,aAAK,SAAS;AAAA,UACZ,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,WAAW;AAAA,UACX,UAAU;AAAA,UACV,UAAU;AAAA,UACV,WAAW;AAAA,UACX,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,MAAM;AAAA,UACN,aAAa;AAAA,QACf;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,qBAAqB;AAC1B,aAAK,kBAAkB;AACvB,aAAK,UAAU;AACf,aAAK,UAAU;AACf,aAAK,eAAe;AAAA,MACtB;AAAA,MACA,oBAAoB;AAClB,aAAK,sBAAsB,sBAAsB,KAAK,EAAE;AAAA,MAC1D;AAAA,MACA,oBAAoB;AAClB,aAAK,gBAAgB,KAAK,GAAG,QAAQ,kCAAkC,MAAM;AAS7E,YAAI,aAAa,KAAK,EAAE,GAAG;AAOzB,gBAAM,cAAc,KAAK,cAAc,KAAK,GAAG,QAAQ,UAAU;AACjE,cAAI,gBAAgB,MAAM;AAcxB,iBAAK,mBAAmB,MAAM,KAAK,OAAO;AAC1C,wBAAY,iBAAiB,mBAAmB,KAAK,gBAAgB;AAAA,UACvE;AAAA,QACF;AAAA,MACF;AAAA,MACA,uBAAuB;AACrB,aAAK,YAAY;AACjB,YAAI,aAAa,KAAK,EAAE,GAAG;AAOzB,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,gBAAgB,QAAQ,qBAAqB,QAAW;AAC1D,wBAAY,oBAAoB,mBAAmB,gBAAgB;AAAA,UACrE;AACA,eAAK,cAAc;AACnB,eAAK,mBAAmB;AAAA,QAC1B;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkBA,WAAW;AACT,YAAI,KAAK,eAAe;AACtB,uBAAa,KAAK,aAAa;AAC/B,eAAK,gBAAgB;AAAA,QACvB;AACA,aAAK,gBAAgB,WAAW,MAAM;AAOpC,cAAI,KAAK,GAAG,iBAAiB,MAAM;AACjC;AAAA,UACF;AACA,eAAK,OAAO;AAAA,QACd,GAAG,GAAG;AAAA,MACR;AAAA,MACA,wBAAwB;AACtB,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,cAAM,OAAO,WAAW,IAAI;AAC5B,eAAO,oBAAoB,SAAY,SAAS,SAAS,WAAW,KAAK,IAAI;AAAA,MAC/E;AAAA,MACA,SAAS;AASP;AACE,cAAI,KAAK,YAAY;AACnB,qBAAS,MAAM,KAAK,eAAe,CAAC;AAAA,UACtC,WAAW,KAAK,SAAS,KAAK,KAAK,YAAY,GAAG;AAChD,iBAAK,OAAO,KAAK,UAAU;AAC3B,wBAAY,IAAI;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AAAA,MACA,iBAAiB;AACf,cAAM,OAAO,eAAe,KAAK,EAAE;AACnC,cAAM,MAAM,KAAK,IAAI,KAAK,GAAG,WAAW,CAAC;AACzC,cAAM,SAAS,KAAK,IAAI,KAAK,eAAe,MAAM,KAAK,GAAG,cAAc,CAAC;AACzE,cAAM,QAAQ,QAAQ,KAAK,QAAQ,WAAW,KAAK;AACnD,YAAI,OAAO;AACT,eAAK,OAAO;AACZ,eAAK,UAAU;AACf,sBAAY,IAAI;AAAA,QAClB;AAAA,MACF;AAAA,MACA,SAAS,IAAI;AACX,cAAM,YAAY,KAAK,IAAI;AAC3B,cAAM,cAAc,CAAC,KAAK;AAC1B,aAAK,aAAa;AAClB,YAAI,aAAa;AACf,eAAK,cAAc;AAAA,QACrB;AACA,YAAI,CAAC,KAAK,UAAU,KAAK,cAAc;AACrC,eAAK,SAAS;AACd,mBAAS,QAAM;AACb,iBAAK,SAAS;AACd,iBAAK,OAAO,QAAQ;AACpB,+BAAmB,KAAK,QAAQ,KAAK,UAAU,IAAI,WAAW;AAC9D,iBAAK,UAAU,KAAK,KAAK,MAAM;AAAA,UACjC,CAAC;AAAA,QACH;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASM,mBAAmB;AAAA;AAKvB,cAAI,CAAC,KAAK,UAAU;AAClB,kBAAM,IAAI,QAAQ,aAAW,iBAAiB,KAAK,IAAI,OAAO,CAAC;AAAA,UACjE;AACA,iBAAO,QAAQ,QAAQ,KAAK,QAAQ;AAAA,QACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAKM,uBAAuB;AAAA;AAC3B,cAAI,CAAC,KAAK,qBAAqB;AAC7B,kBAAM,IAAI,QAAQ,aAAW,iBAAiB,KAAK,IAAI,OAAO,CAAC;AAAA,UACjE;AACA,iBAAO,QAAQ,QAAQ,KAAK,mBAAmB;AAAA,QACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,YAAY,WAAW,GAAG;AACxB,eAAO,KAAK,cAAc,QAAW,GAAG,QAAQ;AAAA,MAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMM,eAAe,WAAW,GAAG;AAAA;AACjC,gBAAM,WAAW,MAAM,KAAK,iBAAiB;AAC7C,gBAAM,IAAI,SAAS,eAAe,SAAS;AAC3C,iBAAO,KAAK,cAAc,QAAW,GAAG,QAAQ;AAAA,QAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQM,cAAc,GAAG,GAAG,UAAU;AAAA;AAClC,gBAAM,WAAW,MAAM,KAAK,iBAAiB;AAC7C,iBAAO,KAAK,cAAc,IAAI,SAAS,YAAY,IAAI,SAAS,WAAW,QAAQ;AAAA,QACrF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQM,cAAc,GAAG,GAAG,WAAW,GAAG;AAAA;AACtC,gBAAM,KAAK,MAAM,KAAK,iBAAiB;AACvC,cAAI,WAAW,IAAI;AACjB,gBAAI,KAAK,MAAM;AACb,iBAAG,YAAY;AAAA,YACjB;AACA,gBAAI,KAAK,MAAM;AACb,iBAAG,aAAa;AAAA,YAClB;AACA;AAAA,UACF;AACA,cAAI;AACJ,cAAI,YAAY;AAChB,gBAAM,UAAU,IAAI,QAAQ,OAAK,UAAU,CAAC;AAC5C,gBAAM,QAAQ,GAAG;AACjB,gBAAM,QAAQ,GAAG;AACjB,gBAAM,SAAS,KAAK,OAAO,IAAI,QAAQ;AACvC,gBAAM,SAAS,KAAK,OAAO,IAAI,QAAQ;AAEvC,gBAAM,OAAO,eAAa;AACxB,kBAAM,aAAa,KAAK,IAAI,IAAI,YAAY,aAAa,QAAQ,IAAI;AACrE,kBAAM,SAAS,KAAK,IAAI,YAAY,CAAC,IAAI;AACzC,gBAAI,WAAW,GAAG;AAChB,iBAAG,YAAY,KAAK,MAAM,SAAS,SAAS,KAAK;AAAA,YACnD;AACA,gBAAI,WAAW,GAAG;AAChB,iBAAG,aAAa,KAAK,MAAM,SAAS,SAAS,KAAK;AAAA,YACpD;AACA,gBAAI,SAAS,GAAG;AAGd,oCAAsB,IAAI;AAAA,YAC5B,OAAO;AACL,sBAAQ;AAAA,YACV;AAAA,UACF;AAEA,gCAAsB,QAAM;AAC1B,wBAAY;AACZ,iBAAK,EAAE;AAAA,UACT,CAAC;AACD,iBAAO;AAAA,QACT;AAAA;AAAA,MACA,gBAAgB;AACd,aAAK,cAAc;AACnB,aAAK,eAAe,KAAK;AAAA,UACvB,aAAa;AAAA,QACf,CAAC;AACD,YAAI,KAAK,UAAU;AACjB,wBAAc,KAAK,QAAQ;AAAA,QAC7B;AAEA,aAAK,WAAW,YAAY,MAAM;AAChC,cAAI,KAAK,aAAa,KAAK,IAAI,IAAI,KAAK;AACtC,iBAAK,YAAY;AAAA,UACnB;AAAA,QACF,GAAG,GAAG;AAAA,MACR;AAAA,MACA,cAAc;AACZ,YAAI,KAAK,SAAU,eAAc,KAAK,QAAQ;AAC9C,aAAK,WAAW;AAChB,YAAI,KAAK,aAAa;AACpB,eAAK,cAAc;AACnB,eAAK,aAAa,KAAK;AAAA,YACrB,aAAa;AAAA,UACf,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,SAAS;AACP,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,MAAM,MAAM,EAAE,IAAI,QAAQ;AAChC,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM,kBAAkB,KAAK,sBAAsB;AACnD,cAAM,mBAAmB,SAAS;AAClC,aAAK,OAAO;AACZ,eAAO,EAAE,MAAM,OAAO,OAAO;AAAA,UAC3B,KAAK;AAAA,UACL,MAAM,gBAAgB,SAAS;AAAA,UAC/B,OAAO,mBAAmB,KAAK,OAAO;AAAA,YACpC,CAAC,IAAI,GAAG;AAAA,YACR,kBAAkB,YAAY,eAAe,KAAK,EAAE;AAAA,YACpD,YAAY;AAAA,YACZ,CAAC,WAAW,GAAG,EAAE,GAAG;AAAA,UACtB,CAAC;AAAA,UACD,OAAO;AAAA,YACL,gBAAgB,GAAG,KAAK,IAAI;AAAA,YAC5B,mBAAmB,GAAG,KAAK,OAAO;AAAA,UACpC;AAAA,QACF,GAAG,mBAAmB,GAAG,EAAE,OAAO;AAAA,UAChC,KAAK;AAAA,UACL,KAAK,CAAAA,QAAM,KAAK,sBAAsBA;AAAA,UACtC,IAAI;AAAA,UACJ,MAAM;AAAA,QACR,CAAC,GAAG,uBAAuB,WAAW,EAAE,QAAQ;AAAA,UAC9C,MAAM;AAAA,QACR,CAAC,IAAI,MAAM,EAAE,OAAO;AAAA,UAClB,KAAK;AAAA,UACL,OAAO;AAAA,YACL,gBAAgB;AAAA,YAChB,YAAY;AAAA,YACZ,YAAY;AAAA,YACZ,aAAa,WAAW,YAAY;AAAA,UACtC;AAAA,UACA,KAAK,cAAY,KAAK,WAAW;AAAA,UACjC,UAAU,KAAK,eAAe,QAAM,KAAK,SAAS,EAAE,IAAI;AAAA,UACxD,MAAM;AAAA,QACR,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,QACP,CAAC,CAAC,GAAG,mBAAmB,EAAE,OAAO;AAAA,UAC/B,OAAO;AAAA,QACT,GAAG,EAAE,OAAO;AAAA,UACV,OAAO;AAAA,QACT,CAAC,GAAG,EAAE,OAAO;AAAA,UACX,OAAO;AAAA,QACT,CAAC,CAAC,IAAI,MAAM,uBAAuB,UAAU,EAAE,QAAQ;AAAA,UACrD,MAAM;AAAA,QACR,CAAC,IAAI,IAAI;AAAA,MACX;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,IACF;AACA,IAAM,mBAAmB,QAAM;AAC7B,UAAI;AACJ,UAAI,GAAG,eAAe;AAEpB,eAAO,GAAG;AAAA,MACZ;AACA,WAAK,KAAK,GAAG,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAErE,eAAO,GAAG,WAAW;AAAA,MACvB;AACA,aAAO;AAAA,IACT;AACA,IAAM,iBAAiB,QAAM;AAC3B,YAAM,OAAO,GAAG,QAAQ,UAAU;AAClC,UAAI,MAAM;AACR,eAAO;AAAA,MACT;AAMA,YAAM,OAAO,GAAG,QAAQ,4DAA4D;AACpF,UAAI,MAAM;AACR,eAAO;AAAA,MACT;AACA,aAAO,iBAAiB,EAAE;AAAA,IAC5B;AAEA,IAAM,qBAAqB,CAAC,QAAQ,IAAI,WAAW,gBAAgB;AACjE,YAAM,QAAQ,OAAO;AACrB,YAAM,QAAQ,OAAO;AACrB,YAAM,QAAQ,OAAO;AACrB,YAAM,WAAW,GAAG;AACpB,YAAM,WAAW,GAAG;AACpB,YAAM,YAAY,YAAY;AAC9B,UAAI,aAAa;AAEf,eAAO,YAAY;AACnB,eAAO,SAAS;AAChB,eAAO,SAAS;AAChB,eAAO,YAAY,OAAO,YAAY;AAAA,MACxC;AACA,aAAO,cAAc;AACrB,aAAO,WAAW,OAAO,aAAa;AACtC,aAAO,WAAW,OAAO,YAAY;AACrC,aAAO,SAAS,WAAW,OAAO;AAClC,aAAO,SAAS,WAAW,OAAO;AAClC,UAAI,YAAY,KAAK,YAAY,KAAK;AACpC,cAAM,aAAa,WAAW,SAAS;AACvC,cAAM,aAAa,WAAW,SAAS;AACvC,eAAO,YAAY,YAAY,MAAM,OAAO,YAAY;AACxD,eAAO,YAAY,YAAY,MAAM,OAAO,YAAY;AAAA,MAC1D;AAAA,IACF;AACA,YAAQ,QAAQ;AAChB,IAAM,mBAAmB,CAAC,UAAU,WAAW;AAC7C,eAAS,MAAM;AACb,cAAM,YAAY,SAAS;AAC3B,cAAM,YAAY,SAAS,eAAe,SAAS;AAKnD,cAAM,eAAe;AAYrB,cAAM,YAAY,YAAY;AAC9B,cAAM,kBAAkB,YAAY;AACpC,cAAM,QAAQ,MAAM,GAAG,IAAI,kBAAkB,cAAc,CAAC;AAC5D,kBAAU,MAAM;AACd,iBAAO,MAAM,YAAY,mBAAmB,MAAM,SAAS,CAAC;AAAA,QAC9D,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,IAAM,eAAe;AACrB,IAAM,qBAAqB;AAC3B,IAAM,cAAc;AACpB,IAAM,oBAAoB;AAC1B,IAAM,SAAS,MAAM;AAAA,MACnB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,eAAe;AACpB,aAAK,yBAAyB,MAAM;AAClC,gBAAM,OAAO,WAAW,IAAI;AAC5B,cAAI,SAAS,OAAO;AAClB;AAAA,UACF;AACA,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,gBAAM,UAAU,aAAa;AAC7B,eAAK,yBAAyB;AAC9B,cAAI,SAAS;AACX,kBAAM,SAAS,KAAK,GAAG,QAAQ,uCAAuC;AACtE,kBAAM,YAAY,SAAS,eAAe,MAAM,IAAI;AACpD,gBAAI,CAAC,WAAW;AACd,sCAAwB,KAAK,EAAE;AAC/B;AAAA,YACF;AACA,iBAAK,gBAAgB,SAAS;AAAA,UAChC;AAAA,QACF;AACA,aAAK,kBAAkB,CAAM,cAAa;AACxC,gBAAM,WAAW,KAAK,WAAW,MAAM,iBAAiB,SAAS;AAIjE,eAAK,wBAAwB,MAAM;AACjC,6BAAiB,UAAU,KAAK,EAAE;AAAA,UACpC;AACA,mBAAS,iBAAiB,UAAU,KAAK,qBAAqB;AAC9D,2BAAiB,UAAU,KAAK,EAAE;AAAA,QACpC;AACA,aAAK,kBAAkB;AACvB,aAAK,WAAW;AAChB,aAAK,cAAc;AAAA,MACrB;AAAA,MACA,mBAAmB;AACjB,aAAK,uBAAuB;AAAA,MAC9B;AAAA,MACA,qBAAqB;AACnB,aAAK,uBAAuB;AAAA,MAC9B;AAAA,MACM,oBAAoB;AAAA;AACxB,eAAK,eAAe,MAAM,yBAAyB,CAAO,cAAc,kBAAkB;AAMxF,gBAAI,iBAAiB,SAAS,kBAAkB,QAAW;AACzD,oBAAM;AAAA,YACR;AACA,iBAAK,kBAAkB;AAAA,UACzB,EAAC;AAAA,QACH;AAAA;AAAA,MACA,uBAAuB;AACrB,YAAI,KAAK,cAAc;AACrB,eAAK,aAAa,QAAQ;AAAA,QAC5B;AAAA,MACF;AAAA,MACA,2BAA2B;AACzB,YAAI,KAAK,YAAY,KAAK,uBAAuB;AAC/C,eAAK,SAAS,oBAAoB,UAAU,KAAK,qBAAqB;AACtE,eAAK,wBAAwB;AAAA,QAC/B;AAAA,MACF;AAAA,MACA,SAAS;AACP,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM,OAAO,KAAK,GAAG,QAAQ,UAAU;AACvC,cAAM,SAAS,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,cAAc,sBAAsB;AACpG,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,YACL,CAAC,IAAI,GAAG;AAAA;AAAA,YAER,CAAC,UAAU,IAAI,EAAE,GAAG;AAAA,YACpB,CAAC,oBAAoB,GAAG;AAAA,YACxB,CAAC,sBAAsB,IAAI,EAAE,GAAG;AAAA,YAChC,CAAC,wBAAwB,GAAG,CAAC,KAAK,oBAAoB,CAAC,UAAU,OAAO,SAAS;AAAA,YACjF,CAAC,mBAAmB,QAAQ,EAAE,GAAG,aAAa;AAAA,UAChD;AAAA,QACF,GAAG,SAAS,SAAS,eAAe,EAAE,OAAO;AAAA,UAC3C,KAAK;AAAA,UACL,OAAO;AAAA,QACT,CAAC,GAAG,EAAE,QAAQ;AAAA,UACZ,KAAK;AAAA,QACP,CAAC,CAAC;AAAA,MACJ;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,IACF;AACA,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AACA,IAAM,aAAa;AACnB,IAAM,eAAe,aAAW;AAC9B,YAAM,cAAc,SAAS,cAAc,GAAG,OAAO,qBAAqB;AAC1E,UAAI,gBAAgB,MAAM;AACxB,eAAO;AAAA,MACT;AACA,YAAM,WAAW,SAAS,cAAc,OAAO;AAC/C,eAAS,UAAU,IAAI,oBAAoB;AAC3C,eAAS,MAAM,YAAY,WAAW,MAAM;AAC5C,eAAS,KAAK,YAAY,QAAQ;AAClC,aAAO;AAAA,IACT;AACA,IAAM,oBAAoB,cAAY;AACpC,UAAI,CAAC,UAAU;AACb;AAAA,MACF;AACA,YAAM,WAAW,SAAS,iBAAiB,aAAa;AACxD,aAAO;AAAA,QACL,IAAI;AAAA,QACJ,UAAU,MAAM,KAAK,QAAQ,EAAE,IAAI,aAAW;AAC5C,gBAAM,aAAa,QAAQ,cAAc,WAAW;AACpD,iBAAO;AAAA,YACL,IAAI;AAAA,YACJ,YAAY,QAAQ,WAAW,cAAc,qBAAqB;AAAA,YAClE;AAAA,YACA,cAAc,aAAa,WAAW,WAAW,cAAc,gBAAgB,IAAI;AAAA,YACnF,cAAc,MAAM,KAAK,QAAQ,iBAAiB,aAAa,CAAC;AAAA,UAClE;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,IAAM,sBAAsB,CAAC,UAAU,mBAAmB,cAAc;AACtE,eAAS,MAAM;AACb,cAAM,YAAY,SAAS;AAC3B,cAAM,QAAQ,MAAM,GAAG,IAAI,CAAC,YAAY,KAAK,GAAG;AAEhD,cAAM,kBAAkB,UAAU,cAAc,gCAAgC;AAChF,YAAI,oBAAoB,MAAM;AAC5B,oBAAU,MAAM;AACd,6BAAiB,kBAAkB,UAAU,KAAK;AAAA,UACpD,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AACA,IAAM,8BAA8B,CAAC,UAAU,YAAY;AAOzD,UAAI,SAAS,aAAa,QAAQ;AAChC;AAAA,MACF;AACA,UAAI,YAAY,QAAW;AACzB,iBAAS,MAAM,eAAe,iBAAiB;AAAA,MACjD,OAAO;AACL,iBAAS,MAAM,YAAY,mBAAmB,QAAQ,SAAS,CAAC;AAAA,MAClE;AAAA,IACF;AACA,IAAM,kCAAkC,CAAC,IAAI,iBAAiB,cAAc;AAC1E,UAAI,CAAC,GAAG,CAAC,EAAE,gBAAgB;AACzB;AAAA,MACF;AAYA,YAAM,QAAQ,GAAG,CAAC,EAAE,oBAAoB,OAAO,aAAa,IAAI,KAAK,IAAI,GAAG,CAAC,EAAE,qBAAqB,MAAM;AAC1G,kCAA4B,gBAAgB,IAAI,UAAU,IAAI,SAAY,KAAK;AAAA,IACjF;AAMA,IAAM,4BAA4B,CAAC,IAEnC,iBAAiB,mBAAmB,aAAa;AAC/C,gBAAU,MAAM;AACd,cAAM,YAAY,SAAS;AAC3B,wCAAgC,IAAI,iBAAiB,SAAS;AAC9D,cAAM,QAAQ,GAAG,CAAC;AAClB,cAAM,eAAe,MAAM;AAC3B,cAAM,mBAAmB,aAAa,QAAQ,aAAa;AAC3D,cAAM,WAAW,MAAM,WAAW,QAAQ,MAAM,WAAW;AAC3D,cAAM,eAAe,qBAAqB,KAAK,aAAa;AAC5D,cAAM,WAAW,KAAK,IAAI,aAAa,OAAO,MAAM,mBAAmB,IAAI;AAC3E,cAAM,YAAY,KAAK,IAAI,aAAa,QAAQ,MAAM,mBAAmB,KAAK;AAC9E,cAAM,sBAAsB,mBAAmB,MAAM,YAAY,KAAK,aAAa;AACnF,YAAI,gBAAgB,qBAAqB;AACvC;AAAA,QACF;AACA,YAAI,MAAM,gBAAgB;AACxB,0BAAgB,iBAAiB,KAAK;AACtC,0BAAgB,iBAAiB;AAAA,QACnC,OAAO;AASL,gBAAM,uBAAuB,aAAa,MAAM,KAAK,aAAa,MAAM,KAAK,aAAa,UAAU,KAAK,aAAa,WAAW;AACjI,cAAI,wBAAwB,YAAY,GAAG;AACzC,4BAAgB,eAAe;AAC/B,4BAAgB,mBAAmB,KAAK;AACxC,wCAA4B,gBAAgB,EAAE;AAAA,UAChD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,IAAM,kBAAkB,CAAC,aAAa,SAAS,SAAS;AACtD,YAAM,WAAW,YAAY;AAC7B,YAAM,WAAW,YAAY;AAC7B,YAAM,YAAY,SAAS,IAAI,aAAW,QAAQ,UAAU;AAC5D,UAAI,QAAQ;AACV,iBAAS,UAAU,OAAO,mCAAmC;AAC7D,kBAAU,QAAQ,cAAY;AAC5B,cAAI,UAAU;AACZ,qBAAS,gBAAgB,aAAa;AAAA,UACxC;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,iBAAS,UAAU,IAAI,mCAAmC;AAU1D,kBAAU,QAAQ,cAAY;AAC5B,cAAI,UAAU;AACZ,qBAAS,aAAa,eAAe,MAAM;AAAA,UAC7C;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,IAAM,mBAAmB,CAAC,WAAW,CAAC,GAAG,QAAQ,GAAGC,cAAa,UAAU;AACzE,eAAS,QAAQ,aAAW;AAC1B,cAAM,WAAW,QAAQ;AACzB,cAAM,WAAW,QAAQ;AACzB,YAAI,CAAC,YAAY,SAAS,SAAS,SAAS;AAC1C;AAAA,QACF;AACA,iBAAS,MAAM,aAAaA,cAAa,aAAa;AACtD,iBAAS,MAAM,YAAY,WAAW,KAAK,KAAK,KAAK;AAAA,MACvD,CAAC;AAAA,IACH;AACA,IAAM,mBAAmB,CAAC,UAAU,QAAQ,mBAAmB;AAC7D,eAAS,MAAM;AACb,cAAM,YAAY,SAAS;AAC3B,cAAM,eAAe,OAAO;AAC5B,cAAM,YAAY,iBAAiB,eAAe,eAAe;AAajE,YAAI,mBAAmB,QAAQ,YAAY,WAAW;AACpD,iBAAO,MAAM,YAAY,mBAAmB,GAAG;AAC/C,mBAAS,MAAM,YAAY,aAAa,SAAS,YAAY,iBAAiB;AAC9E;AAAA,QACF;AACA,cAAM,kBAAkB,YAAY;AACpC,cAAM,eAAe;AACrB,cAAM,QAAQ,MAAM,GAAG,kBAAkB,cAAc,CAAC;AACxD,kBAAU,MAAM;AACd,mBAAS,MAAM,eAAe,WAAW;AACzC,iBAAO,MAAM,YAAY,mBAAmB,MAAM,SAAS,CAAC;AAAA,QAC9D,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,IAAM,eAAe;AACrB,IAAM,qBAAqB;AAC3B,IAAM,cAAc;AACpB,IAAM,oBAAoB;AAC1B,IAAM,SAAS,MAAM;AAAA,MACnB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,sBAAsB,CAAC;AAC5B,aAAK,kBAAkB,CAAO,WAAW,mBAAmB;AAC1D,gBAAM,WAAW,KAAK,WAAW,MAAM,iBAAiB,SAAS;AAIjE,eAAK,wBAAwB,MAAM;AACjC,6BAAiB,KAAK,UAAU,KAAK,IAAI,cAAc;AAAA,UACzD;AACA,mBAAS,iBAAiB,UAAU,KAAK,qBAAqB;AAC9D,2BAAiB,KAAK,UAAU,KAAK,IAAI,cAAc;AAAA,QACzD;AACA,aAAK,WAAW;AAChB,aAAK,cAAc;AAAA,MACrB;AAAA,MACA,oBAAoB;AAClB,aAAK,sBAAsB,sBAAsB,KAAK,EAAE;AAAA,MAC1D;AAAA,MACA,mBAAmB;AACjB,aAAK,uBAAuB;AAAA,MAC9B;AAAA,MACA,qBAAqB;AACnB,aAAK,uBAAuB;AAAA,MAC9B;AAAA,MACA,uBAAuB;AACrB,aAAK,yBAAyB;AAAA,MAChC;AAAA,MACM,yBAAyB;AAAA;AAC7B,gBAAM,OAAO,WAAW,IAAI;AAC5B,cAAI,SAAS,OAAO;AAClB;AAAA,UACF;AACA,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,gBAAM,cAAc,aAAa;AACjC,gBAAM,UAAU,aAAa;AAC7B,eAAK,yBAAyB;AAC9B,cAAI,aAAa;AACf,kBAAM,SAAS,KAAK,GAAG,QAAQ,uCAAuC;AACtE,kBAAM,YAAY,SAAS,eAAe,MAAM,IAAI;AAEpD,sBAAU,MAAM;AACd,oBAAM,QAAQ,aAAa,WAAW;AACtC,oBAAM,OAAO;AACb,2BAAa,iBAAiB;AAAA,YAChC,CAAC;AACD,kBAAM,KAAK,oBAAoB,WAAW,MAAM;AAAA,UAClD,WAAW,SAAS;AAClB,kBAAM,SAAS,KAAK,GAAG,QAAQ,uCAAuC;AACtE,kBAAM,YAAY,SAAS,eAAe,MAAM,IAAI;AACpD,gBAAI,CAAC,WAAW;AACd,sCAAwB,KAAK,EAAE;AAC/B;AAAA,YACF;AACA,kBAAM,iBAAiB,UAAU,cAAc,iCAAiC;AAChF,kBAAM,KAAK,gBAAgB,WAAW,cAAc;AAAA,UACtD;AAAA,QACF;AAAA;AAAA,MACA,2BAA2B;AACzB,YAAI,KAAK,sBAAsB;AAC7B,eAAK,qBAAqB,WAAW;AACrC,eAAK,uBAAuB;AAAA,QAC9B;AACA,YAAI,KAAK,YAAY,KAAK,uBAAuB;AAC/C,eAAK,SAAS,oBAAoB,UAAU,KAAK,qBAAqB;AACtE,eAAK,wBAAwB;AAAA,QAC/B;AACA,YAAI,KAAK,uBAAuB;AAC9B,eAAK,sBAAsB,UAAU,OAAO,sBAAsB;AAClE,eAAK,wBAAwB;AAAA,QAC/B;AAAA,MACF;AAAA,MACM,oBAAoB,WAAW,QAAQ;AAAA;AAC3C,cAAI,CAAC,aAAa,CAAC,QAAQ;AACzB,oCAAwB,KAAK,EAAE;AAC/B;AAAA,UACF;AACA,cAAI,OAAO,yBAAyB,aAAa;AAC/C;AAAA,UACF;AACA,eAAK,WAAW,MAAM,iBAAiB,SAAS;AAChD,gBAAM,UAAU,OAAO,iBAAiB,YAAY;AACpD,eAAK,wBAAwB,MAAM,KAAK,OAAO,EAAE,KAAK,YAAU,OAAO,aAAa,UAAU;AAC9F,cAAI,CAAC,KAAK,uBAAuB;AAC/B;AAAA,UACF;AACA,gBAAM,kBAAkB,kBAAkB,KAAK,qBAAqB;AACpE,gBAAM,oBAAoB,kBAAkB,KAAK,EAAE;AACnD,cAAI,CAAC,mBAAmB,CAAC,mBAAmB;AAC1C;AAAA,UACF;AACA,0BAAgB,iBAAiB,KAAK;AACtC,sCAA4B,gBAAgB,IAAI,CAAC;AAOjD,gBAAM,sBAAsB,QAAM;AAChC,sCAA0B,IAAI,iBAAiB,mBAAmB,KAAK,QAAQ;AAAA,UACjF;AACA,eAAK,uBAAuB,IAAI,qBAAqB,qBAAqB;AAAA,YACxE,MAAM;AAAA,YACN,WAAW,CAAC,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,CAAC;AAAA,UACxD,CAAC;AACD,eAAK,qBAAqB,QAAQ,kBAAkB,SAAS,kBAAkB,SAAS,SAAS,CAAC,EAAE,EAAE;AAMtG,eAAK,wBAAwB,MAAM;AACjC,gCAAoB,KAAK,UAAU,mBAAmB,SAAS;AAAA,UACjE;AACA,eAAK,SAAS,iBAAiB,UAAU,KAAK,qBAAqB;AACnE,oBAAU,MAAM;AACd,gBAAI,KAAK,0BAA0B,QAAW;AAC5C,mBAAK,sBAAsB,UAAU,IAAI,sBAAsB;AAAA,YACjE;AAAA,UACF,CAAC;AAAA,QACH;AAAA;AAAA,MACA,SAAS;AACP,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM,WAAW,KAAK,YAAY;AAElC,cAAM,WAAW,YAAY,YAAY,KAAK,EAAE,IAAI,SAAS;AAC7D,eAAO,EAAE,MAAM,OAAO,OAAO;AAAA,UAC3B,KAAK;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,YACL,CAAC,IAAI,GAAG;AAAA;AAAA,YAER,CAAC,UAAU,IAAI,EAAE,GAAG;AAAA,YACpB,CAAC,oBAAoB,GAAG,KAAK;AAAA,YAC7B,CAAC,mBAAmB,QAAQ,EAAE,GAAG;AAAA,YACjC,CAAC,sBAAsB,IAAI,EAAE,GAAG,KAAK;AAAA,UACvC;AAAA,QACF,GAAG,mBAAmB,GAAG,SAAS,SAAS,eAAe,EAAE,OAAO;AAAA,UACjE,KAAK;AAAA,UACL,OAAO;AAAA,QACT,CAAC,GAAG,EAAE,QAAQ;AAAA,UACZ,KAAK;AAAA,QACP,CAAC,CAAC;AAAA,MACJ;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,IACF;AACA,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AACA,IAAM,kBAAkB;AACxB,IAAM,wBAAwB;AAC9B,IAAM,eAAe,MAAM;AAAA,MACzB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,aAAK,mBAAmB,YAAY,MAAM,oBAAoB,CAAC;AAC/D,aAAK,kBAAkB,YAAY,MAAM,mBAAmB,CAAC;AAC7D,aAAK,iBAAiB,qBAAqB;AAC3C,aAAK,+BAA+B;AACpC,aAAK,OAAO,WAAW,IAAI;AAC3B,aAAK,WAAW;AAChB,aAAK,WAAW;AAChB,aAAK,YAAY;AACjB,aAAK,eAAe;AAAA,MACtB;AAAA,MACA,sBAAsB;AACpB,YAAI,KAAK,SAAS;AAChB,eAAK,QAAQ,OAAO,KAAK,iBAAiB,MAAS;AAAA,QACrD;AAAA,MACF;AAAA,MACM,oBAAoB;AAAA;AACxB,gBAAM,UAAU,MAAM;AACpB,iBAAK,+BAA+B;AACpC,gBAAI,KAAK,cAAc;AACrB,mBAAK,aAAa,QAAQ;AAAA,YAC5B;AAAA,UACF;AACA,eAAK,WAAW,MAAM,OAAO,mCAA0B,GAAG,uBAAuB,KAAK,IAAI,MAAM,CAAC,KAAK,gCAAgC,CAAC,CAAC,KAAK,gBAAgB,KAAK,aAAa,SAAS,GAAG,MAAM,QAAQ,GAAG,UAAQ;AAClN,gBAAI;AACJ,oBAAQ,KAAK,KAAK,SAAS,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,IAAI;AAAA,UAClF,GAAG,CAAC,gBAAgB,MAAM,QAAQ;AAChC,gBAAI,KAAK,KAAK;AACZ,mBAAK,IAAI,SAAS,MAAM;AACtB,qBAAK,+BAA+B;AACpC,oBAAI,KAAK,cAAc;AACrB,uBAAK,aAAa,MAAM,cAAc;AAAA,gBACxC;AAAA,cACF,GAAG;AAAA,gBACD,iBAAiB;AAAA,cACnB,CAAC;AAED,kBAAI,eAAe,iBAAiB,QAAS;AAS7C,kBAAI,CAAC,gBAAgB;AACnB,qBAAK,IAAI,OAAO,gCAAgC;AAChD,gCAAgB,wBAAwB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC;AAAA,cACvF,OAAO;AACL,gCAAgB,wBAAwB,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC;AAAA,cACvF;AACA,mBAAK,IAAI,YAAY,iBAAiB,IAAI,GAAG,cAAc,GAAG;AAAA,YAChE,OAAO;AACL,mBAAK,+BAA+B;AAAA,YACtC;AAAA,UACF,CAAC;AACD,eAAK,oBAAoB;AAAA,QAC3B;AAAA;AAAA,MACA,oBAAoB;AAClB,aAAK,eAAe,KAAK;AAAA,MAC3B;AAAA,MACA,uBAAuB;AACrB,YAAI,KAAK,SAAS;AAChB,eAAK,QAAQ,QAAQ;AACrB,eAAK,UAAU;AAAA,QACjB;AAAA,MACF;AAAA;AAAA,MAEM,OAAO,YAAY,WAAW,MAAM;AAAA;AACxC,gBAAM,SAAS,MAAM,KAAK,eAAe,KAAK;AAC9C,cAAI,UAAU;AACd,cAAI;AACF,sBAAU,MAAM,KAAK,WAAW,YAAY,WAAW,IAAI;AAAA,UAC7D,SAAS,GAAG;AACV,0BAAc,8CAA8C,CAAC;AAAA,UAC/D;AACA,iBAAO;AACP,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA,MAEM,WAAW,IAAI,QAAQ,WAAW,WAAW;AAAA;AACjD,gBAAM,UAAU,MAAM,KAAK,QAAQ,IAAI,QAAQ;AAAA,YAC7C,UAAU,cAAc,SAAS,IAAI;AAAA,YACrC,WAAW,cAAc,SAAS,SAAS;AAAA,YAC3C,kBAAkB;AAAA,UACpB,CAAC;AACD,iBAAO;AAAA,YACL;AAAA,YACA,SAAS,KAAK;AAAA,UAChB;AAAA,QACF;AAAA;AAAA;AAAA,MAEM,aAAa;AAAA;AACjB,gBAAM,SAAS,KAAK;AACpB,iBAAO,SAAS;AAAA,YACd,IAAI,OAAO;AAAA,YACX,SAAS;AAAA,YACT,QAAQ,KAAK;AAAA,UACf,IAAI;AAAA,QACN;AAAA;AAAA,MACM,QAAQ,WAAW,QAAQ,MAAM;AAAA;AACrC,cAAI,KAAK,oBAAoB,aAAa,sBAAsB,QAAQ,KAAK,YAAY,GAAG;AAC1F,mBAAO;AAAA,UACT;AAEA,gBAAM,YAAY,KAAK;AACvB,gBAAM,aAAa,MAAM,gBAAgB,KAAK,UAAU,KAAK,IAAI,WAAW,CAAC,YAAY,oBAAoB,GAAG,MAAM;AACtH,eAAK,kBAAkB;AACvB,eAAK,WAAW;AAChB,eAAK,eAAe;AAEpB,gBAAM,KAAK,OAAO,YAAY,WAAW,IAAI;AAC7C,gBAAM,gBAAgB,KAAK,UAAU,SAAS;AAC9C,iBAAO;AAAA,QACT;AAAA;AAAA,MACM,WAAW,IAAY,IAAsB;AAAA,mDAAlC,YAAY,WAAW,OAAO,CAAC,GAAG;AACjD,cAAI,cAAc,YAAY;AAC5B,mBAAO;AAAA,UACT;AAEA,eAAK,iBAAiB,KAAK;AAC3B,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM,WAAW,KAAK,YAAY,OAAO,WAAW,YAAY,IAAI;AACpE,gBAAM,mBAAmB,KAAK,oBAAoB,KAAK,aAAa,OAAO,IAAI,cAAc;AAC7F,gBAAM,WAAW,OAAO,OAAO,OAAO,OAAO;AAAA,YAC3C;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAMR,UAAU,aAAa,EAAE;AAAA,YACzB,kBAAkB,KAAK,oBAAoB,SAAO;AAQhD,kBAAI,QAAQ,UAAa,CAAC,KAAK,8BAA8B;AAC3D,qBAAK,+BAA+B;AACpC,oBAAI,SAAS,MAAM;AACjB,uBAAK,+BAA+B;AACpC,sBAAI,KAAK,cAAc;AACrB,yBAAK,aAAa,MAAM,KAAK;AAAA,kBAC/B;AAAA,gBACF,GAAG;AAAA,kBACD,iBAAiB;AAAA,gBACnB,CAAC;AAOD,oBAAI,YAAY,GAAG,GAAG,CAAC;AAAA,cACzB,OAAO;AACL,qBAAK,MAAM;AAAA,cACb;AAAA,YACF,IAAI;AAAA,UACN,GAAG,IAAI,GAAG;AAAA,YACR;AAAA,UACF,CAAC,CAAC;AAEF,eAAK,gBAAgB,KAAK;AAC1B,iBAAO;AAAA,QACT;AAAA;AAAA,MACA,SAAS;AACP,eAAO,EAAE,QAAQ;AAAA,UACf,KAAK;AAAA,QACP,CAAC;AAAA,MACH;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,gBAAgB,CAAC,qBAAqB;AAAA,QACxC;AAAA,MACF;AAAA,IACF;AACA,iBAAa,QAAQ;AACrB,IAAM,cAAc;AACpB,IAAM,oBAAoB;AAC1B,IAAM,aAAa;AACnB,IAAM,mBAAmB;AACzB,IAAM,eAAe,MAAM;AAAA,MACzB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,aAAK,QAAQ;AACb,aAAK,OAAO;AAAA,MACd;AAAA,MACA,cAAc;AACZ,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,oBAAoB;AAClB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,YAAY;AACV,cAAM,OAAO,KAAK,QAAQ;AAC1B,aAAK,SAAS,KAAK;AAAA,UACjB,CAAC,SAAS,IAAI,EAAE,GAAG;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,MACA,UAAU;AACR,eAAO,KAAK,SAAS,SAAY,KAAK,OAAO;AAAA,MAC/C;AAAA,MACA,SAAS;AACP,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM,OAAO,KAAK,QAAQ;AAC1B,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,OAAO,mBAAmB,KAAK,OAAO;AAAA,YACpC,CAAC,IAAI,GAAG;AAAA,YACR,CAAC,SAAS,IAAI,EAAE,GAAG;AAAA,YACnB,aAAa,SAAS,QAAQ;AAAA,UAChC,CAAC;AAAA,QACH,GAAG,EAAE,OAAO;AAAA,UACV,KAAK;AAAA,UACL,OAAO;AAAA,QACT,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,QACP,CAAC,CAAC,CAAC;AAAA,MACL;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,QAAQ,CAAC,aAAa;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,iBAAa,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AACA,IAAM,gBAAgB;AACtB,IAAM,sBAAsB;AAC5B,IAAM,eAAe;AACrB,IAAM,qBAAqB;AAC3B,IAAM,UAAU,MAAM;AAAA,MACpB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,iBAAiB,oBAAI,IAAI;AAC9B,aAAK,QAAQ;AAAA,MACf;AAAA,MACA,oBAAoB;AAClB,cAAM,UAAU,MAAM,KAAK,KAAK,GAAG,iBAAiB,aAAa,CAAC;AAClE,cAAM,eAAe,QAAQ,KAAK,YAAU;AAC1C,iBAAO,OAAO,SAAS;AAAA,QACzB,CAAC;AACD,YAAI,cAAc;AAChB,uBAAa,UAAU,IAAI,oBAAoB;AAAA,QACjD;AACA,cAAM,kBAAkB,QAAQ,QAAQ;AACxC,cAAM,cAAc,gBAAgB,KAAK,YAAU,OAAO,SAAS,KAAK,KAAK,gBAAgB,KAAK,YAAU,OAAO,SAAS,SAAS,KAAK,gBAAgB,KAAK,YAAU,OAAO,SAAS,WAAW;AACpM,YAAI,aAAa;AACf,sBAAY,UAAU,IAAI,mBAAmB;AAAA,QAC/C;AAAA,MACF;AAAA,MACA,cAAc,IAAI;AAChB,WAAG,gBAAgB;AACnB,cAAM,UAAU,GAAG,OAAO;AAC1B,cAAM,gBAAgB,GAAG;AACzB,cAAM,YAAY,CAAC;AACnB,cAAM,cAAc,KAAK,eAAe,IAAI,OAAO,KAAK,CAAC;AACzD,YAAI,iBAAiB;AACrB,eAAO,KAAK,aAAa,EAAE,QAAQ,SAAO;AACxC,gBAAM,WAAW,WAAW,GAAG;AAC/B,gBAAM,WAAW,cAAc,GAAG;AAClC,cAAI,aAAa,YAAY,QAAQ,GAAG;AACtC,6BAAiB;AAAA,UACnB;AACA,cAAI,UAAU;AACZ,sBAAU,QAAQ,IAAI;AAAA,UACxB;AAAA,QACF,CAAC;AACD,YAAI,gBAAgB;AAClB,eAAK,eAAe,IAAI,SAAS,SAAS;AAC1C,sBAAY,IAAI;AAAA,QAClB;AAAA,MACF;AAAA,MACA,SAAS;AACP,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM,cAAc,CAAC;AACrB,aAAK,eAAe,QAAQ,WAAS;AACnC,iBAAO,OAAO,aAAa,KAAK;AAAA,QAClC,CAAC;AACD,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,WAAW,GAAG,mBAAmB,KAAK,OAAO;AAAA,YAClF,CAAC,IAAI,GAAG;AAAA,YACR,cAAc,YAAY,eAAe,KAAK,EAAE;AAAA,UAClD,CAAC,CAAC;AAAA,QACJ,GAAG,EAAE,OAAO;AAAA,UACV,KAAK;AAAA,UACL,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC,GAAG,EAAE,OAAO;AAAA,UACX,KAAK;AAAA,UACL,OAAO;AAAA,UACP,MAAM;AAAA,QACR,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,UACL,MAAM;AAAA,QACR,CAAC,GAAG,EAAE,QAAQ;AAAA,UACZ,KAAK;AAAA,UACL,MAAM;AAAA,QACR,CAAC,GAAG,EAAE,OAAO;AAAA,UACX,KAAK;AAAA,UACL,OAAO;AAAA,UACP,MAAM;AAAA,QACR,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,QACP,CAAC,CAAC,GAAG,EAAE,QAAQ;AAAA,UACb,KAAK;AAAA,UACL,MAAM;AAAA,QACR,CAAC,GAAG,EAAE,QAAQ;AAAA,UACZ,KAAK;AAAA,UACL,MAAM;AAAA,QACR,CAAC,CAAC,CAAC;AAAA,MACL;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,IACF;AACA,YAAQ,QAAQ;AAAA,MACd,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AAAA;AAAA;", "names": ["el", "transition"], "x_google_ignoreList": [0]}