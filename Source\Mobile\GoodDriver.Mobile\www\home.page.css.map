{"version": 3, "sources": ["src/app/pages/tabs/home/<USER>"], "sourcesContent": ["/* src/app/home/<USER>/\n.welcome-container {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  height: 100%;\n}\n\nion-card {\n  margin: 0 auto;\n  max-width: 500px;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n}\n\nion-list {\n  margin-top: 16px;\n  margin-bottom: 16px;\n}\n\nion-item {\n  --border-color: rgba(0, 0, 0, 0.1);\n  cursor: pointer;\n}\n\nion-item:hover {\n  --background: rgba(0, 0, 0, 0.05);\n}"], "mappings": ";AACA,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,UAAA;;AAGF;AACE,UAAA,EAAA;AACA,aAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF;AACE,cAAA;AACA,iBAAA;;AAGF;AACE,kBAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,UAAA;;AAGF,QAAA;AACE,gBAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;", "names": []}