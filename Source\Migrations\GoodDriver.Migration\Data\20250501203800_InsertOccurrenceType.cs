﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Migration.Data
{
    [FluentMigrator.Migration(20250501203800)]
    public class InsertOccurrenceType : FluentMigrator.Migration
    {
        public override void Down()
        {
            Execute.Sql("DELETE FROM Occurrence");
        }

        public override void Up()
        {
            string sql = @$"
                INSERT INTO Occurrence (Id, Name, OccurrenceType, DiscountScore, CreatedOn, Message)
                VALUES
                    (1, 'Freada brusca', 'HardBreak', 30, Getdate(), 'Sempre que efetua uma freada brusca, você coloca em risco a sua segurança e a dos outros.'),
                    (2, 'Falar ao Telefone', 'PhoneUse', 40, GETDATE(), 'Mantenha o foco na sua direção. Evite mexer no celular durante a viagem.'),
                    (3, 'Aceleração', 'Acceleration', 30, GETDATE(), 'Alta velocidade ao volante aumenta drasticamente o risco de acidentes graves. Dirija com cautela.'),
                    (4, 'Col<PERSON>ão', 'Collision', 70, GETDATE(), 'Colisão pode ser fatal. Mantenha distância segura e atenção total ao volante.')
";
            Execute.Sql(sql);
        }
    }
}
