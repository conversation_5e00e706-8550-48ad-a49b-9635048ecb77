{"name": "@capacitor/device", "version": "7.0.1", "description": "The Device API exposes internal information about the device, such as the model and operating system version, along with user information such as unique ids.", "main": "dist/plugin.cjs.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "unpkg": "dist/plugin.js", "files": ["android/src/main/", "android/build.gradle", "dist/", "ios/Sources", "ios/Tests", "Package.swift", "CapacitorDevice.podspec"], "author": "Ionic <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ionic-team/capacitor-plugins.git"}, "bugs": {"url": "https://github.com/ionic-team/capacitor-plugins/issues"}, "keywords": ["capacitor", "plugin", "native"], "scripts": {"test": "uvu -r esm -r ts-node/register src/__tests__", "verify": "npm run verify:ios && npm run verify:android && npm run verify:web", "verify:ios": "xcodebuild build -scheme CapacitorDevice -destination generic/platform=iOS", "verify:android": "cd android && ./gradlew clean build test && cd ..", "verify:web": "npm run build && npm test", "lint": "npm run eslint && npm run prettier -- --check && npm run swiftlint -- lint", "fmt": "npm run eslint -- --fix && npm run prettier -- --write && npm run swiftlint -- --fix --format", "eslint": "eslint . --ext ts", "prettier": "prettier \"**/*.{css,html,ts,js,java}\"", "swiftlint": "node-swiftlint", "docgen": "docgen --api DevicePlugin --output-readme README.md --output-json dist/docs.json", "build": "npm run clean && npm run docgen && tsc && rollup -c rollup.config.mjs", "clean": "rimraf ./dist", "watch": "tsc --watch", "prepublishOnly": "npm run build", "publish:cocoapod": "pod trunk push ./CapacitorDevice.podspec --allow-warnings"}, "devDependencies": {"@capacitor/android": "next", "@capacitor/core": "next", "@capacitor/docgen": "0.2.2", "@capacitor/ios": "next", "@ionic/eslint-config": "^0.4.0", "@ionic/prettier-config": "~1.0.1", "@ionic/swiftlint-config": "^1.1.2", "eslint": "^8.57.0", "esm": "^3.2.25", "prettier": "~2.3.0", "prettier-plugin-java": "~1.0.2", "rimraf": "^6.0.1", "rollup": "^4.26.0", "swiftlint": "^1.0.1", "ts-node": "^9.1.1", "typescript": "~4.1.5", "uvu": "^0.5.1"}, "peerDependencies": {"@capacitor/core": ">=7.0.0"}, "prettier": "@ionic/prettier-config", "swiftlint": "@ionic/swiftlint-config", "eslintConfig": {"extends": "@ionic/eslint-config/recommended"}, "capacitor": {"ios": {"src": "ios"}, "android": {"src": "android"}}, "publishConfig": {"access": "public"}, "gitHead": "927f549a995118acd8e5735835300c4c3cbf3de7"}