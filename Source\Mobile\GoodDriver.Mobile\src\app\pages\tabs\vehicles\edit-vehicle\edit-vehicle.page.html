<ion-header>
  <ion-toolbar>
    <ion-title>Editar Veículo</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content *ngIf="!isLoading">
  <form [formGroup]="vehicleForm" (ngSubmit)="onSubmit()">
    <ion-list>      
      <ion-item>
        <ion-label position="stacked">Marca</ion-label>
        <ion-input formControlName="brandName"></ion-input>
      </ion-item>
      <ion-item>
        <ion-label position="stacked">Modelo</ion-label>
        <ion-input formControlName="modelName"></ion-input>
      </ion-item>
      <ion-item>
        <ion-label position="stacked">Placa</ion-label>
        <ion-input formControlName="plate"></ion-input>
      </ion-item>
      <ion-item>
        <ion-label position="stacked">Ano</ion-label>
        <ion-input type="number" formControlName="year"></ion-input>
      </ion-item>
      <ion-item>
        <ion-label position="stacked">V<PERSON><PERSON></ion-label>
        <ion-input formControlName="version"></ion-input>
      </ion-item>
      <ion-item>
        <ion-label position="stacked">Númer<PERSON> da Apólice</ion-label>
        <ion-input formControlName="policyNumber"></ion-input>
      </ion-item>
      <ion-item>
        <ion-label position="stacked">Principal?</ion-label>
        <ion-checkbox formControlName="isPrimary"></ion-checkbox>
      </ion-item>
    </ion-list>
    <ion-button expand="block" type="submit" [disabled]="vehicleForm.invalid">Salvar</ion-button>
    <ion-button expand="block" color="medium" (click)="onCancel()">Cancelar</ion-button>
  </form>
</ion-content>
<ion-spinner *ngIf="isLoading"></ion-spinner> 