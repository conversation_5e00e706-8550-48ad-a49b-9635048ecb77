<ion-header>
  <ion-toolbar>
    <ion-title>Editar Veículo</ion-title>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/tabs/vehicles"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding" *ngIf="!isLoading">
  <!-- Alerta de conexão com a internet -->
  <ion-card color="warning" *ngIf="!isOnline">
    <ion-card-header>
      <ion-card-title>
        <ion-icon name="wifi-outline"></ion-icon>
        Atenção
      </ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <p>Para editar um veículo, é necessário estar conectado à internet para obter as marcas e modelos atualizados.</p>
      <p>Por favor, verifique sua conexão e tente novamente.</p>
    </ion-card-content>
  </ion-card>

  <!-- Alerta informativo -->
  <ion-card color="light" *ngIf="isOnline">
    <ion-card-content>
      <ion-icon name="information-circle-outline" color="primary"></ion-icon>
      <span> É necessário estar conectado à internet para obter as marcas e modelos de veículos atualizados.</span>
    </ion-card-content>
  </ion-card>

  <form [formGroup]="vehicleForm" (ngSubmit)="onSubmit()">
    <!-- Marca -->
    <ion-item>
      <ion-label position="stacked">Marca</ion-label>
      <ion-select formControlName="brand" (ionChange)="onBrandChange($event.detail.value)" interface="popover" [compareWith]="compareWith">
        <ion-select-option *ngFor="let brand of brands" [value]="brand.id">{{ brand.name }}</ion-select-option>
      </ion-select>
    </ion-item>

    <!-- Modelo -->
    <ion-item>
      <ion-label position="stacked">Modelo</ion-label>
      <ion-select formControlName="model" (ionChange)="onModelChange($event.detail.value)" interface="popover" [compareWith]="compareWith">
        <ion-select-option *ngFor="let model of models" [value]="model.id">{{ model.name }}</ion-select-option>
      </ion-select>
    </ion-item>

    <!-- Versão -->
    <ion-item>
      <ion-label position="stacked">Versão</ion-label>
      <ion-input formControlName="version" placeholder="Ex: 1.0 Flex"></ion-input>
    </ion-item>

    <!-- Ano -->
    <ion-item>
      <ion-label position="stacked">Ano</ion-label>
      <ion-input formControlName="year" type="number" placeholder="Ex: 2021" min="1900" max="2099"></ion-input>
    </ion-item>

    <!-- Placa -->
    <ion-item>
      <ion-label position="stacked">Placa</ion-label>
      <ion-input formControlName="plate" maxlength="8" placeholder="AAA-0000" inputmode="text"></ion-input>
      <ion-note slot="error" *ngIf="vehicleForm.get('plate')?.hasError('pattern') && vehicleForm.get('plate')?.touched">
        Formato inválido. Use o formato AAA-0000
      </ion-note>
    </ion-item>

    <!-- Apólice (opcional) -->
    <ion-item>
      <ion-label position="stacked">Número da Apólice (opcional)</ion-label>
      <ion-input formControlName="policy" type="text"></ion-input>
    </ion-item>

    <!-- Veículo Principal -->
    <ion-item>
      <ion-label>Veículo Principal</ion-label>
      <ion-checkbox formControlName="isPrimary" slot="end"></ion-checkbox>
    </ion-item>

    <!-- Botão salvar -->
    <ion-button expand="block" class="ion-margin-top" (click)="onSubmit()" [disabled]="vehicleForm.invalid || isSubmitting || !isOnline">
      <ion-spinner name="crescent" *ngIf="isSubmitting"></ion-spinner>
      <span *ngIf="!isSubmitting">Salvar Alterações</span>
    </ion-button>

    <!-- Botão cancelar -->
    <ion-button expand="block" color="medium" (click)="onCancel()" [disabled]="isSubmitting">
      Cancelar
    </ion-button>

    <!-- Debug button (temporary) -->
    <ion-button expand="block" color="tertiary" (click)="debugFormValues()" fill="outline">
      Debug Form Values
    </ion-button>

    <!-- Mensagem de erro quando offline -->
    <div *ngIf="!isOnline" class="offline-message">
      <ion-text color="danger">
        <p class="ion-text-center">
          <ion-icon name="wifi-outline"></ion-icon>
          Não é possível salvar sem conexão com a internet
        </p>
      </ion-text>
    </div>
  </form>
</ion-content>

<!-- Loading spinner -->
<ion-content *ngIf="isLoading" class="ion-padding">
  <div class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Carregando dados do veículo...</p>
  </div>
</ion-content>