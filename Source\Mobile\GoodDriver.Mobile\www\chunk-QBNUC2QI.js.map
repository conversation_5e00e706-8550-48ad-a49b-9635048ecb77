{"version": 3, "sources": ["node_modules/@capacitor/synapse/dist/synapse.mjs", "node_modules/@capacitor/geolocation/dist/esm/definitions.js", "node_modules/@capacitor/geolocation/dist/esm/index.js", "node_modules/@capacitor/motion/dist/esm/definitions.js", "node_modules/@capacitor/motion/dist/esm/index.js", "src/app/core/services/journeyinfo.service.ts", "src/app/core/services/journey-storage.service.ts"], "sourcesContent": ["function s(t) {\n  t.CapacitorUtils.Synapse = new Proxy({}, {\n    get(e, n) {\n      return new Proxy({}, {\n        get(w, o) {\n          return (c, p, r) => {\n            const i = t.Capacitor.Plugins[n];\n            if (i === void 0) {\n              r(new Error(`Capacitor plugin ${n} not found`));\n              return;\n            }\n            if (typeof i[o] != \"function\") {\n              r(new Error(`Method ${o} not found in Capacitor plugin ${n}`));\n              return;\n            }\n            (async () => {\n              try {\n                const a = await i[o](c);\n                p(a);\n              } catch (a) {\n                r(a);\n              }\n            })();\n          };\n        }\n      });\n    }\n  });\n}\nfunction u(t) {\n  t.CapacitorUtils.Synapse = new Proxy({}, {\n    get(e, n) {\n      return t.cordova.plugins[n];\n    }\n  });\n}\nfunction f(t = !1) {\n  typeof window > \"u\" || (window.CapacitorUtils = window.CapacitorUtils || {}, window.Capacitor !== void 0 && !t ? s(window) : window.cordova !== void 0 && u(window));\n}\nexport { f as exposeSynapse };", "export {};\n", "import { registerPlugin } from '@capacitor/core';\nimport { exposeSynapse } from '@capacitor/synapse';\nconst Geolocation = registerPlugin('Geolocation', {\n  web: () => import('./web').then(m => new m.GeolocationWeb())\n});\nexposeSynapse();\nexport * from './definitions';\nexport { Geolocation };\n", "export {};\n", "import { registerPlugin } from '@capacitor/core';\nconst Motion = registerPlugin('Motion', {\n  android: () => import('./web').then(m => new m.MotionWeb()),\n  ios: () => import('./web').then(m => new m.MotionWeb()),\n  web: () => import('./web').then(m => new m.MotionWeb())\n});\nexport * from './definitions';\nexport { Motion };\n", "import { Injectable } from '@angular/core';\r\nimport { Geolocation } from '@capacitor/geolocation';  // Se estiver usando Capacitor\r\nimport { IDataStorage } from 'src/app/core/storage/data-storage.interface';  // Seu serviço de armazenamento\r\nimport { JourneyInfo } from 'src/app/core/models/journeyInfo.model'  // Seu modelo JourneyInfo\r\nimport { DataStorageService } from './data-storage-service';\r\nimport { Motion } from '@capacitor/motion';\r\nimport { BehaviorSubject } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class JourneyInfoService {\r\n  private journeyId: string = '';\r\n  private trackingInterval: any = null;\r\n  private trackingStartTime: number = 0;\r\n  private totalTimeAboveSpeed = 0;\r\n  private storage: IDataStorage;\r\n\r\n  // Observable para o status de rastreamento\r\n  private trackingActiveSubject = new BehaviorSubject<boolean>(false);\r\n  public trackingActive$ = this.trackingActiveSubject.asObservable();\r\n\r\n  constructor(private dataStorageService: DataStorageService) {\r\n    // Inicializa o serviço de dados (supondo que você tenha um serviço de dados como IndexedDBStorageService)\r\n    this.storage = this.dataStorageService;\r\n    \r\n  }\r\n\r\n  // Função para iniciar o rastreamento\r\n  startTracking(journeyId: string) {\r\n    this.trackingStartTime = Date.now();\r\n    this.totalTimeAboveSpeed = 0;  // Reseta o tempo acima da velocidade\r\n    this.journeyId = journeyId;\r\n\r\n    // Atualiza o estado de rastreamento\r\n    this.trackingActiveSubject.next(true);\r\n\r\n    this.trackingInterval = setInterval(async () => {\r\n      const location = await this.getCurrentLocation();\r\n      if (location) {\r\n        // Verifica eventos de direção\r\n        await this.checkHardBreak(location);  // Verifica se houve uma freada brusca\r\n        await this.checkHighVelocity(location);  // Verifica se houve alta velocidade\r\n\r\n        // Salva a localização periodicamente\r\n        this.saveLocation(location);\r\n      }\r\n    }, 10000);  // A cada 10 segundos\r\n\r\n    console.log('Rastreamento iniciado para viagem:', journeyId);\r\n  }\r\n\r\n  // Função para parar o rastreamento\r\n  stopTracking(journeyId: string) {\r\n    if (this.trackingInterval) {\r\n      clearInterval(this.trackingInterval);  // Limpa o intervalo\r\n      this.trackingInterval = null;\r\n\r\n      // Atualiza o estado de rastreamento\r\n      this.trackingActiveSubject.next(false);\r\n\r\n      console.log('Rastreamento parado para viagem:', journeyId);\r\n    }\r\n  }\r\n\r\n  // Função para obter a localização atual\r\n  private async getCurrentLocation(): Promise<JourneyInfo | null> {\r\n    try {\r\n      const coordinates = await Geolocation.getCurrentPosition(\r\n        { enableHighAccuracy: true,\r\n          timeout: 10000\r\n         }\r\n      );  // Usando Capacitor\r\n      const location: JourneyInfo = {\r\n        id: this.generateUniqueId(),\r\n        journeyId: this.journeyId,\r\n        latitude: coordinates.coords.latitude,\r\n        longitude: coordinates.coords.longitude,\r\n        timestamp: new Date().toISOString(),  // Marca o horário atual\r\n      };\r\n      return location;\r\n    } catch (error) {\r\n      console.error('Erro ao obter a localização:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // Função para obter a velocidade atual (em km/h)\r\n  private async getCurrentSpeed(): Promise<number> {\r\n    try {\r\n      const position = await Geolocation.getCurrentPosition();  // Usando Capacitor\r\n      const speed = position.coords.speed ? position.coords.speed * 3.6 : 0;  // Converte de m/s para km/h ou assume 0 se for null\r\n      return speed;  // Retorna a velocidade em km/h\r\n    } catch (error) {\r\n      console.error('Erro ao obter a velocidade:', error);\r\n      return 0;  // Se der erro, assume velocidade 0\r\n    }\r\n  }\r\n\r\n  // Função para salvar a localização no banco de dados\r\n  private async saveLocation(location: JourneyInfo): Promise<void> {\r\n    try {\r\n      // Use the correct table name 'journeyInfo' instead of 'location'\r\n      await this.storage.insert('journeyInfo', location);\r\n      console.log('Localização salva:', location);\r\n    } catch (error) {\r\n      console.error('Erro ao salvar a localização:', error);\r\n    }\r\n  }\r\n\r\n  // Função para gerar um ID único para cada localização (pode ser melhorada)\r\n  private generateUniqueId(): string {\r\n    return Math.random().toString(36).substring(2, 15);\r\n  }\r\n\r\n  checkHardBreak(location: JourneyInfo): Promise<boolean> {\r\n    return new Promise((resolve) => {\r\n      const listener = Motion.addListener('accel', (event) => {\r\n        const { acceleration } = event;\r\n\r\n        if (acceleration?.x && acceleration.x < -10) {\r\n          console.log('Freada brusca detectada!');\r\n\r\n          location.occurrenceType = 'HardBreak';\r\n          this.saveLocation(location);\r\n\r\n          // // Cancela o listener para não continuar ouvindo\r\n          // listener.remove();\r\n\r\n          resolve(true); // Responde que detectou a freada\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n\r\n  async checkHighVelocity(location: JourneyInfo): Promise<boolean> {\r\n    const speedKmH = await this.getCurrentSpeed();\r\n\r\n     if (speedKmH > 120) { // Se a velocidade for maior que 120 km/h\r\n          console.log('Alta velocidade detectada!');\r\n          location.occurrenceType = 'Acceleration';  // Define o tipo de ocorrência como alta velocidade\r\n          this.saveLocation(location);\r\n          return true; // Retorna verdadeiro se a velocidade for maior que 120 km/h\r\n     }\r\n        return false; // Retorna falso se a velocidade for menor ou igual a 120 km/h\r\n  }\r\n}\r\n", "import { Injectable } from '@angular/core';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport { SessionService } from './session.service';\r\nimport { Journey } from '../models/journey.model';\r\nimport { DataStorageService } from './data-storage-service';\r\nimport { Platform } from '@ionic/angular'; // Para detecção da plataforma (dispositivo ou navegador)\r\nimport { JourneyInfo } from '../models/journeyInfo.model';\r\nimport { JourneyInfoService } from './journeyinfo.service';\r\nimport { VehicleService } from './vehicle.service';\r\nimport { NetworkService } from './network.service';\r\nimport { SyncStatus } from '../models/sync.model';\r\nimport { JourneySyncRequestDto } from '../dtos/journey/journeySyncRequestDto';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { ApiService } from './api.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class JourneyStorageService\r\n{\r\n    constructor(\r\n      private sessionService: SessionService,\r\n      private dataStorageService: DataStorageService,\r\n      private platform: Platform,\r\n      private journeyInfoService: JourneyInfoService,\r\n      private vehicleService: VehicleService,\r\n      private networkService: NetworkService,\r\n      private http: HttpClient,\r\n      private apiService: ApiService\r\n    ) {\r\n      this.isNative = this.platform.is('capacitor') || this.platform.is('cordova');\r\n    }\r\n\r\n  private readonly tableName = 'journeys';\r\n  private db: any; // Referência ao banco de dados (SQLite ou IndexedDB)\r\n  private isNative: boolean; // Variável para verificar se é dispositivo físico\r\n\r\n  async init()\r\n  {\r\n    await this.dataStorageService.init();\r\n  }\r\n\r\n  /**\r\n   * Starts a new journey if the user has at least one vehicle\r\n   * @returns The journey ID if successful, null if no vehicles exist\r\n   */\r\n  async startJourney(): Promise<string | null> {\r\n    const userId = await this.sessionService.getUserId();\r\n\r\n    // Check if the user has any vehicles\r\n    const hasVehicles = await this.vehicleService.hasVehicles(userId);\r\n    if (!hasVehicles) {\r\n      console.error('Cannot start journey: No vehicles registered for this user');\r\n      return null;\r\n    }\r\n\r\n    // Get the primary vehicle\r\n    const primaryVehicle = await this.vehicleService.getPrimaryVehicle(userId);\r\n    if (!primaryVehicle) {\r\n      console.error('Cannot start journey: No primary vehicle set');\r\n      return null;\r\n    }\r\n\r\n    const id = uuidv4();\r\n    const startDate = new Date().toISOString();\r\n\r\n    // Determine sync status based on network connectivity\r\n    const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingCreate : SyncStatus.PendingCreate;\r\n\r\n    // Create the journey with the primary vehicle ID\r\n    await this.dataStorageService.insert(this.tableName, {\r\n      id: id,\r\n      startDate: startDate,\r\n      userId: userId,\r\n      vehicleId: primaryVehicle.id,\r\n      syncStatus: syncStatus,\r\n      lastSyncDate: null\r\n    });\r\n\r\n    // Start tracking the journey\r\n    this.journeyInfoService.startTracking(id);\r\n    return id;\r\n  }\r\n\r\n  async endJourney(journeyId: string) {\r\n    this.journeyInfoService.stopTracking(journeyId);\r\n    const endDate = new Date().toISOString();\r\n\r\n    // Calcular a distância total da viagem\r\n    // Use the correct table name 'journeyInfo' instead of 'location'\r\n    const result = await this.dataStorageService.select('journeyInfo', `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);\r\n\r\n    // Handle the result properly for both IndexedDB and SQLite\r\n    let infosJourney: JourneyInfo[] = [];\r\n    if (Array.isArray(result)) {\r\n      infosJourney = result;\r\n    } else if (result) {\r\n      // If result is not an array but exists, try to convert it to an array\r\n      try {\r\n        infosJourney = Array.isArray(result) ? result : [];\r\n      } catch (error) {\r\n        console.error('Error converting journey info to array:', error);\r\n      }\r\n    }\r\n\r\n    let totalDistance = 0;\r\n\r\n    if (infosJourney && infosJourney.length > 1) {\r\n      for (let i = 0; i < infosJourney.length - 1; i++) {\r\n        const start = infosJourney[i];\r\n        const end = infosJourney[i + 1];\r\n        totalDistance += this.calculateDistance(\r\n          start.latitude, start.longitude,\r\n          end.latitude, end.longitude\r\n        );\r\n      }\r\n    }\r\n\r\n    // Determine sync status based on network connectivity\r\n    const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingUpdate : SyncStatus.PendingUpdate;\r\n\r\n    // Update the journey with the calculated distance\r\n    await this.dataStorageService.update(\r\n      this.tableName,\r\n      {\r\n        endDate: endDate,\r\n        distance: totalDistance,\r\n        syncStatus: syncStatus,\r\n        lastSyncDate: null\r\n      },\r\n      `id = '${journeyId}'`\r\n    );\r\n  }\r\n\r\n  async addLocation(journeyId: string, latitude: number, longitude: number) {\r\n    const id = uuidv4();\r\n    const timestamp = new Date().toISOString();\r\n\r\n    // Determine sync status based on network connectivity\r\n    const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingCreate : SyncStatus.PendingCreate;\r\n\r\n    // Use the correct table name 'journeyInfo' instead of 'location'\r\n    await this.dataStorageService.insert('journeyInfo', {\r\n      id: id,\r\n      journeyId: journeyId,\r\n      latitude: latitude,\r\n      longitude: longitude,\r\n      timestamp: timestamp,\r\n      syncStatus: syncStatus,\r\n      lastSyncDate: null\r\n    });\r\n  }\r\n\r\n  async closeConnection() {\r\n    // Fechar a conexão com o banco de dados se estiver usando SQLite\r\n    if (this.db) {\r\n      this.db.close();\r\n      this.db = null;\r\n     }\r\n  }\r\n\r\n  async getAllJourneys(): Promise<Journey[]>\r\n  {\r\n    let result = await this.dataStorageService.select(this.tableName, 'ORDER BY startDate DESC');\r\n\r\n    if(!result || result.length === 0)\r\n      return [];\r\n\r\n    for (let i = 0; i < result.length; i++) {\r\n      const journeyId = result[i].id;\r\n      // Use the correct table name 'journeyInfo' instead of 'location'\r\n      const infosJourney = await this.dataStorageService.select('journeyInfo', `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);\r\n\r\n      // Ensure we have an array of journey info objects\r\n      if (Array.isArray(infosJourney)) {\r\n        result[i].infos = infosJourney.filter((info: JourneyInfo) => info.journeyId === journeyId);\r\n      } else {\r\n        result[i].infos = [];\r\n      }\r\n    }\r\n\r\n    const journeys: Journey[] = result.map((data: any) => {\r\n      return {\r\n        id: data.id,\r\n        startDate: data.startDate,\r\n        endDate: data.endDate,\r\n        distance: data.distance,\r\n        userId: data.userId,\r\n        vehicleId: data.vehicleId,\r\n        infosJourney: data.infos,\r\n        syncStatus: data.syncStatus || SyncStatus.Synced,\r\n        lastSyncDate: data.lastSyncDate ? new Date(data.lastSyncDate) : undefined\r\n      };\r\n    });\r\n    return journeys;\r\n  }\r\n\r\n  async getAllJourneysByUser(userId: string): Promise<Journey[]>\r\n  {\r\n    // 1. Buscar dados do usuário no Local Storage\r\n    let result = await this.dataStorageService.select(this.tableName, ' ORDER BY startDate DESC');\r\n\r\n    // Filter results by userId\r\n    if (Array.isArray(result)) {\r\n      result = result.filter((data: any) => data.userId === userId);\r\n    } else {\r\n      return [];\r\n    }\r\n\r\n    if(!result || result.length === 0)\r\n      return [];\r\n\r\n    for (let i = 0; i < result.length; i++) {\r\n      const journeyId = result[i].id;\r\n      // Use the correct table name 'journeyInfo' instead of 'location'\r\n      const infosJourney = await this.dataStorageService.select('journeyInfo', `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);\r\n\r\n      // Ensure we have an array of journey info objects\r\n      if (Array.isArray(infosJourney)) {\r\n        result[i].infos = infosJourney.filter((info: JourneyInfo) => info.journeyId === journeyId);\r\n      } else {\r\n        result[i].infos = [];\r\n      }\r\n    }\r\n\r\n    const journeys: Journey[] = result.map((data: any) => {\r\n      return {\r\n        id: data.id,\r\n        startDate: data.startDate,\r\n        endDate: data.endDate,\r\n        distance: data.distance,\r\n        userId: data.userId,\r\n        vehicleId: data.vehicleId,\r\n        infosJourney: data.infos,\r\n        syncStatus: data.syncStatus || SyncStatus.Synced,\r\n        lastSyncDate: data.lastSyncDate ? new Date(data.lastSyncDate) : undefined\r\n      };\r\n    });\r\n    return journeys;\r\n  }\r\n\r\n  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {\r\n    const toRad = (value: number) => (value * Math.PI) / 180;\r\n\r\n    const R = 6371000; // Raio da Terra em metros\r\n    const dLat = toRad(lat2 - lat1);\r\n    const dLon = toRad(lon2 - lon1);\r\n\r\n    const a =\r\n      Math.sin(dLat / 2) * Math.sin(dLat / 2) +\r\n      Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) *\r\n      Math.sin(dLon / 2) * Math.sin(dLon / 2);\r\n\r\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\r\n\r\n    return R * c; // Distância em metros\r\n  }\r\n\r\n  /**\r\n   * Updates the sync status of a journey\r\n   * @param journey The journey to update\r\n   * @returns True if the update was successful\r\n   */\r\n  async updateJourneySync(journey: Journey): Promise<boolean> {\r\n    try {\r\n      // Convert journey to database format\r\n      const journeyForDb = {\r\n        ...journey,\r\n        lastSyncDate: journey.lastSyncDate ? journey.lastSyncDate.toISOString() : null\r\n      };\r\n\r\n      // Remove infosJourney property as it's not stored in the journey table\r\n      if (journeyForDb.infosJourney) {\r\n        delete journeyForDb.infosJourney;\r\n      }\r\n\r\n      await this.dataStorageService.update(\r\n        this.tableName,\r\n        journeyForDb,\r\n        `id = '${journey.id}'`\r\n      );\r\n\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Error updating journey sync status:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gets journeys that need to be synchronized\r\n   * @param userId The user ID\r\n   * @returns Array of journeys that need to be synchronized\r\n   */\r\n  async getPendingSyncJourneys(userId: string): Promise<Journey[]> {\r\n    const journeys = await this.getAllJourneysByUser(userId);\r\n    return journeys.filter(j =>\r\n      j.syncStatus === SyncStatus.PendingCreate ||\r\n      j.syncStatus === SyncStatus.PendingUpdate ||\r\n      j.syncStatus === SyncStatus.PendingDelete\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Sends a journey to the server for synchronization\r\n   * @param journey The journey to send\r\n   * @returns True if the journey was sent successfully\r\n   */\r\n  async sendJourneyToSync(journey: JourneySyncRequestDto): Promise<boolean> {\r\n    try {\r\n      const url = this.apiService.getUrl('journey/SyncJourney');\r\n      const result = await this.http.post(url, journey).toPromise();\r\n      return true; // Se chegou até aqui, a requisição foi bem-sucedida\r\n    } catch (error) {\r\n      console.error('Error sending journey to sync:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,EAAE,GAAG;AACZ,IAAE,eAAe,UAAU,IAAI,MAAM,CAAC,GAAG;AAAA,IACvC,IAAI,GAAG,GAAG;AACR,aAAO,IAAI,MAAM,CAAC,GAAG;AAAA,QACnB,IAAI,GAAG,GAAG;AACR,iBAAO,CAAC,GAAG,GAAG,MAAM;AAClB,kBAAM,IAAI,EAAE,UAAU,QAAQ,CAAC;AAC/B,gBAAI,MAAM,QAAQ;AAChB,gBAAE,IAAI,MAAM,oBAAoB,CAAC,YAAY,CAAC;AAC9C;AAAA,YACF;AACA,gBAAI,OAAO,EAAE,CAAC,KAAK,YAAY;AAC7B,gBAAE,IAAI,MAAM,UAAU,CAAC,kCAAkC,CAAC,EAAE,CAAC;AAC7D;AAAA,YACF;AACA,aAAC,MAAY;AACX,kBAAI;AACF,sBAAM,IAAI,MAAM,EAAE,CAAC,EAAE,CAAC;AACtB,kBAAE,CAAC;AAAA,cACL,SAAS,GAAG;AACV,kBAAE,CAAC;AAAA,cACL;AAAA,YACF,IAAG;AAAA,UACL;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;AACA,SAAS,EAAE,GAAG;AACZ,IAAE,eAAe,UAAU,IAAI,MAAM,CAAC,GAAG;AAAA,IACvC,IAAI,GAAG,GAAG;AACR,aAAO,EAAE,QAAQ,QAAQ,CAAC;AAAA,IAC5B;AAAA,EACF,CAAC;AACH;AACA,SAAS,EAAE,IAAI,OAAI;AACjB,SAAO,SAAS,QAAQ,OAAO,iBAAiB,OAAO,kBAAkB,CAAC,GAAG,OAAO,cAAc,UAAU,CAAC,IAAI,EAAE,MAAM,IAAI,OAAO,YAAY,UAAU,EAAE,MAAM;AACpK;AAtCA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAEM;AAFN,IAAAA,YAAA;AAAA;AAAA;AAAA;AACA;AAKA;AAJA,IAAM,cAAc,eAAe,eAAe;AAAA,MAChD,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,eAAe,CAAC;AAAA,IAC7D,CAAC;AACD,MAAc;AAAA;AAAA;;;ACLd,IAAAC,oBAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IACM;AADN,IAAAC,YAAA;AAAA;AAAA;AAAA;AAMA,IAAAC;AALA,IAAM,SAAS,eAAe,UAAU;AAAA,MACtC,SAAS,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,UAAU,CAAC;AAAA,MAC1D,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,UAAU,CAAC;AAAA,MACtD,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,UAAU,CAAC;AAAA,IACxD,CAAC;AAAA;AAAA;;;ACLD,IAWa;AAXb;;;;AACA,IAAAC;AAIA,IAAAA;AACA;;;AAKM,IAAO,sBAAP,MAAO,oBAAkB;MAW7B,YAAoB,oBAAsC;AAAtC,aAAA,qBAAA;AAVZ,aAAA,YAAoB;AACpB,aAAA,mBAAwB;AACxB,aAAA,oBAA4B;AAC5B,aAAA,sBAAsB;AAItB,aAAA,wBAAwB,IAAI,gBAAyB,KAAK;AAC3D,aAAA,kBAAkB,KAAK,sBAAsB,aAAY;AAI9D,aAAK,UAAU,KAAK;MAEtB;;MAGA,cAAc,WAAiB;AAC7B,aAAK,oBAAoB,KAAK,IAAG;AACjC,aAAK,sBAAsB;AAC3B,aAAK,YAAY;AAGjB,aAAK,sBAAsB,KAAK,IAAI;AAEpC,aAAK,mBAAmB,YAAY,MAAW;AAC7C,gBAAM,WAAW,MAAM,KAAK,mBAAkB;AAC9C,cAAI,UAAU;AAEZ,kBAAM,KAAK,eAAe,QAAQ;AAClC,kBAAM,KAAK,kBAAkB,QAAQ;AAGrC,iBAAK,aAAa,QAAQ;UAC5B;QACF,IAAG,GAAK;AAER,gBAAQ,IAAI,sCAAsC,SAAS;MAC7D;;MAGA,aAAa,WAAiB;AAC5B,YAAI,KAAK,kBAAkB;AACzB,wBAAc,KAAK,gBAAgB;AACnC,eAAK,mBAAmB;AAGxB,eAAK,sBAAsB,KAAK,KAAK;AAErC,kBAAQ,IAAI,oCAAoC,SAAS;QAC3D;MACF;;MAGc,qBAAkB;;AAC9B,cAAI;AACF,kBAAM,cAAc,MAAM,YAAY,mBACpC;cAAE,oBAAoB;cACpB,SAAS;aACT;AAEJ,kBAAM,WAAwB;cAC5B,IAAI,KAAK,iBAAgB;cACzB,WAAW,KAAK;cAChB,UAAU,YAAY,OAAO;cAC7B,WAAW,YAAY,OAAO;cAC9B,YAAW,oBAAI,KAAI,GAAG,YAAW;;;AAEnC,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,sCAAgC,KAAK;AACnD,mBAAO;UACT;QACF;;;MAGc,kBAAe;;AAC3B,cAAI;AACF,kBAAM,WAAW,MAAM,YAAY,mBAAkB;AACrD,kBAAM,QAAQ,SAAS,OAAO,QAAQ,SAAS,OAAO,QAAQ,MAAM;AACpE,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,+BAA+B,KAAK;AAClD,mBAAO;UACT;QACF;;;MAGc,aAAa,UAAqB;;AAC9C,cAAI;AAEF,kBAAM,KAAK,QAAQ,OAAO,eAAe,QAAQ;AACjD,oBAAQ,IAAI,4BAAsB,QAAQ;UAC5C,SAAS,OAAO;AACd,oBAAQ,MAAM,uCAAiC,KAAK;UACtD;QACF;;;MAGQ,mBAAgB;AACtB,eAAO,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;MACnD;MAEA,eAAe,UAAqB;AAClC,eAAO,IAAI,QAAQ,CAAC,YAAW;AAC7B,gBAAM,WAAW,OAAO,YAAY,SAAS,CAAC,UAAS;AACrD,kBAAM,EAAE,aAAY,IAAK;AAEzB,iBAAI,6CAAc,MAAK,aAAa,IAAI,KAAK;AAC3C,sBAAQ,IAAI,0BAA0B;AAEtC,uBAAS,iBAAiB;AAC1B,mBAAK,aAAa,QAAQ;AAK1B,sBAAQ,IAAI;YACd;UACF,CAAC;QACH,CAAC;MACH;MAGM,kBAAkB,UAAqB;;AAC3C,gBAAM,WAAW,MAAM,KAAK,gBAAe;AAE1C,cAAI,WAAW,KAAK;AACf,oBAAQ,IAAI,4BAA4B;AACxC,qBAAS,iBAAiB;AAC1B,iBAAK,aAAa,QAAQ;AAC1B,mBAAO;UACZ;AACG,iBAAO;QACb;;;;uCAvIW,qBAAkB,mBAAA,kBAAA,CAAA;IAAA;2FAAlB,qBAAkB,SAAlB,oBAAkB,WAAA,YAFjB,OAAM,CAAA;AAEd,IAAO,qBAAP;;0EAAO,oBAAkB,CAAA;cAH9B;eAAW;UACV,YAAY;SACb;;;;;;;ACVD,IAmBa;AAnBb;;;;AACA;AASA;;;;;;;;;;AASM,IAAO,yBAAP,MAAO,uBAAqB;MAE9B,YACU,gBACA,oBACA,UACA,oBACA,gBACA,gBACA,MACA,YAAsB;AAPtB,aAAA,iBAAA;AACA,aAAA,qBAAA;AACA,aAAA,WAAA;AACA,aAAA,qBAAA;AACA,aAAA,iBAAA;AACA,aAAA,iBAAA;AACA,aAAA,OAAA;AACA,aAAA,aAAA;AAKK,aAAA,YAAY;AAHzB,aAAK,WAAW,KAAK,SAAS,GAAG,WAAW,KAAK,KAAK,SAAS,GAAG,SAAS;MAC7E;MAMI,OAAI;;AAER,gBAAM,KAAK,mBAAmB,KAAI;QACpC;;;;;;MAMM,eAAY;;AAChB,gBAAM,SAAS,MAAM,KAAK,eAAe,UAAS;AAGlD,gBAAM,cAAc,MAAM,KAAK,eAAe,YAAY,MAAM;AAChE,cAAI,CAAC,aAAa;AAChB,oBAAQ,MAAM,4DAA4D;AAC1E,mBAAO;UACT;AAGA,gBAAM,iBAAiB,MAAM,KAAK,eAAe,kBAAkB,MAAM;AACzE,cAAI,CAAC,gBAAgB;AACnB,oBAAQ,MAAM,8CAA8C;AAC5D,mBAAO;UACT;AAEA,gBAAM,KAAK,WAAM;AACjB,gBAAM,aAAY,oBAAI,KAAI,GAAG,YAAW;AAGxC,gBAAM,aAAa,KAAK,eAAe,YAAW,IAAK,WAAW,gBAAgB,WAAW;AAG7F,gBAAM,KAAK,mBAAmB,OAAO,KAAK,WAAW;YACnD;YACA;YACA;YACA,WAAW,eAAe;YAC1B;YACA,cAAc;WACf;AAGD,eAAK,mBAAmB,cAAc,EAAE;AACxC,iBAAO;QACT;;MAEM,WAAW,WAAiB;;AAChC,eAAK,mBAAmB,aAAa,SAAS;AAC9C,gBAAM,WAAU,oBAAI,KAAI,GAAG,YAAW;AAItC,gBAAM,SAAS,MAAM,KAAK,mBAAmB,OAAO,eAAe,sBAAsB,SAAS,0BAA0B;AAG5H,cAAI,eAA8B,CAAA;AAClC,cAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,2BAAe;UACjB,WAAW,QAAQ;AAEjB,gBAAI;AACF,6BAAe,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAA;YAClD,SAAS,OAAO;AACd,sBAAQ,MAAM,2CAA2C,KAAK;YAChE;UACF;AAEA,cAAI,gBAAgB;AAEpB,cAAI,gBAAgB,aAAa,SAAS,GAAG;AAC3C,qBAAS,IAAI,GAAG,IAAI,aAAa,SAAS,GAAG,KAAK;AAChD,oBAAM,QAAQ,aAAa,CAAC;AAC5B,oBAAM,MAAM,aAAa,IAAI,CAAC;AAC9B,+BAAiB,KAAK,kBACpB,MAAM,UAAU,MAAM,WACtB,IAAI,UAAU,IAAI,SAAS;YAE/B;UACF;AAGA,gBAAM,aAAa,KAAK,eAAe,YAAW,IAAK,WAAW,gBAAgB,WAAW;AAG7F,gBAAM,KAAK,mBAAmB,OAC5B,KAAK,WACL;YACE;YACA,UAAU;YACV;YACA,cAAc;aAEhB,SAAS,SAAS,GAAG;QAEzB;;MAEM,YAAY,WAAmB,UAAkB,WAAiB;;AACtE,gBAAM,KAAK,WAAM;AACjB,gBAAM,aAAY,oBAAI,KAAI,GAAG,YAAW;AAGxC,gBAAM,aAAa,KAAK,eAAe,YAAW,IAAK,WAAW,gBAAgB,WAAW;AAG7F,gBAAM,KAAK,mBAAmB,OAAO,eAAe;YAClD;YACA;YACA;YACA;YACA;YACA;YACA,cAAc;WACf;QACH;;MAEM,kBAAe;;AAEnB,cAAI,KAAK,IAAI;AACX,iBAAK,GAAG,MAAK;AACb,iBAAK,KAAK;UACX;QACH;;MAEM,iBAAc;;AAElB,cAAI,SAAS,MAAM,KAAK,mBAAmB,OAAO,KAAK,WAAW,yBAAyB;AAE3F,cAAG,CAAC,UAAU,OAAO,WAAW;AAC9B,mBAAO,CAAA;AAET,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,kBAAM,YAAY,OAAO,CAAC,EAAE;AAE5B,kBAAM,eAAe,MAAM,KAAK,mBAAmB,OAAO,eAAe,sBAAsB,SAAS,0BAA0B;AAGlI,gBAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,qBAAO,CAAC,EAAE,QAAQ,aAAa,OAAO,CAAC,SAAsB,KAAK,cAAc,SAAS;YAC3F,OAAO;AACL,qBAAO,CAAC,EAAE,QAAQ,CAAA;YACpB;UACF;AAEA,gBAAM,WAAsB,OAAO,IAAI,CAAC,SAAa;AACnD,mBAAO;cACL,IAAI,KAAK;cACT,WAAW,KAAK;cAChB,SAAS,KAAK;cACd,UAAU,KAAK;cACf,QAAQ,KAAK;cACb,WAAW,KAAK;cAChB,cAAc,KAAK;cACnB,YAAY,KAAK,cAAc,WAAW;cAC1C,cAAc,KAAK,eAAe,IAAI,KAAK,KAAK,YAAY,IAAI;;UAEpE,CAAC;AACD,iBAAO;QACT;;MAEM,qBAAqB,QAAc;;AAGvC,cAAI,SAAS,MAAM,KAAK,mBAAmB,OAAO,KAAK,WAAW,0BAA0B;AAG5F,cAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,qBAAS,OAAO,OAAO,CAAC,SAAc,KAAK,WAAW,MAAM;UAC9D,OAAO;AACL,mBAAO,CAAA;UACT;AAEA,cAAG,CAAC,UAAU,OAAO,WAAW;AAC9B,mBAAO,CAAA;AAET,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,kBAAM,YAAY,OAAO,CAAC,EAAE;AAE5B,kBAAM,eAAe,MAAM,KAAK,mBAAmB,OAAO,eAAe,sBAAsB,SAAS,0BAA0B;AAGlI,gBAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,qBAAO,CAAC,EAAE,QAAQ,aAAa,OAAO,CAAC,SAAsB,KAAK,cAAc,SAAS;YAC3F,OAAO;AACL,qBAAO,CAAC,EAAE,QAAQ,CAAA;YACpB;UACF;AAEA,gBAAM,WAAsB,OAAO,IAAI,CAAC,SAAa;AACnD,mBAAO;cACL,IAAI,KAAK;cACT,WAAW,KAAK;cAChB,SAAS,KAAK;cACd,UAAU,KAAK;cACf,QAAQ,KAAK;cACb,WAAW,KAAK;cAChB,cAAc,KAAK;cACnB,YAAY,KAAK,cAAc,WAAW;cAC1C,cAAc,KAAK,eAAe,IAAI,KAAK,KAAK,YAAY,IAAI;;UAEpE,CAAC;AACD,iBAAO;QACT;;MAEQ,kBAAkB,MAAc,MAAc,MAAc,MAAY;AAC9E,cAAM,QAAQ,CAAC,UAAmB,QAAQ,KAAK,KAAM;AAErD,cAAM,IAAI;AACV,cAAM,OAAO,MAAM,OAAO,IAAI;AAC9B,cAAM,OAAO,MAAM,OAAO,IAAI;AAE9B,cAAM,IACJ,KAAK,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC,IACtC,KAAK,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,IAAI,MAAM,IAAI,CAAC,IAC5C,KAAK,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC;AAExC,cAAM,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC;AAEvD,eAAO,IAAI;MACb;;;;;;MAOM,kBAAkB,SAAgB;;AACtC,cAAI;AAEF,kBAAM,eAAe,iCAChB,UADgB;cAEnB,cAAc,QAAQ,eAAe,QAAQ,aAAa,YAAW,IAAK;;AAI5E,gBAAI,aAAa,cAAc;AAC7B,qBAAO,aAAa;YACtB;AAEA,kBAAM,KAAK,mBAAmB,OAC5B,KAAK,WACL,cACA,SAAS,QAAQ,EAAE,GAAG;AAGxB,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,uCAAuC,KAAK;AAC1D,mBAAO;UACT;QACF;;;;;;;MAOM,uBAAuB,QAAc;;AACzC,gBAAM,WAAW,MAAM,KAAK,qBAAqB,MAAM;AACvD,iBAAO,SAAS,OAAO,OACrB,EAAE,eAAe,WAAW,iBAC5B,EAAE,eAAe,WAAW,iBAC5B,EAAE,eAAe,WAAW,aAAa;QAE7C;;;;;;;MAOM,kBAAkB,SAA8B;;AACpD,cAAI;AACF,kBAAM,MAAM,KAAK,WAAW,OAAO,qBAAqB;AACxD,kBAAM,SAAS,MAAM,KAAK,KAAK,KAAK,KAAK,OAAO,EAAE,UAAS;AAC3D,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,kCAAkC,KAAK;AACrD,kBAAM;UACR;QACF;;;;uCA3SW,wBAAqB,mBAAA,cAAA,GAAA,mBAAA,kBAAA,GAAA,mBAAA,QAAA,GAAA,mBAAA,kBAAA,GAAA,mBAAA,cAAA,GAAA,mBAAA,cAAA,GAAA,mBAAA,UAAA,GAAA,mBAAA,UAAA,CAAA;IAAA;8FAArB,wBAAqB,SAArB,uBAAqB,WAAA,YAFpB,OAAM,CAAA;AAEd,IAAO,wBAAP;;0EAAO,uBAAqB,CAAA;cAHjC;eAAW;UACV,YAAY;SACb;;;;;", "names": ["init_esm", "init_definitions", "init_esm", "init_definitions", "init_esm"], "x_google_ignoreList": [0, 1, 2, 3, 4]}