import { Injectable } from '@angular/core';
import { Geolocation } from '@capacitor/geolocation';  // Se estiver usando Capacitor
import { IDataStorage } from 'src/app/core/storage/data-storage.interface';  // Seu serviço de armazenamento
import { JourneyInfo } from 'src/app/core/models/journeyInfo.model'  // Seu modelo JourneyInfo
import { DataStorageService } from './data-storage-service';
import { Motion } from '@capacitor/motion';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class JourneyInfoService {
  private journeyId: string = '';
  private trackingInterval: any = null;
  private trackingStartTime: number = 0;
  private totalTimeAboveSpeed = 0;
  private storage: IDataStorage;

  // Observable para o status de rastreamento
  private trackingActiveSubject = new BehaviorSubject<boolean>(false);
  public trackingActive$ = this.trackingActiveSubject.asObservable();

  constructor(private dataStorageService: DataStorageService) {
    // Inicializa o serviço de dados (supondo que você tenha um serviço de dados como IndexedDBStorageService)
    this.storage = this.dataStorageService;
    
  }

  // Função para iniciar o rastreamento
  startTracking(journeyId: string) {
    this.trackingStartTime = Date.now();
    this.totalTimeAboveSpeed = 0;  // Reseta o tempo acima da velocidade
    this.journeyId = journeyId;

    // Atualiza o estado de rastreamento
    this.trackingActiveSubject.next(true);

    this.trackingInterval = setInterval(async () => {
      const location = await this.getCurrentLocation();
      if (location) {
        // Verifica eventos de direção
        await this.checkHardBreak(location);  // Verifica se houve uma freada brusca
        await this.checkHighVelocity(location);  // Verifica se houve alta velocidade

        // Salva a localização periodicamente
        this.saveLocation(location);
      }
    }, 10000);  // A cada 10 segundos

    console.log('Rastreamento iniciado para viagem:', journeyId);
  }

  // Função para parar o rastreamento
  stopTracking(journeyId: string) {
    if (this.trackingInterval) {
      clearInterval(this.trackingInterval);  // Limpa o intervalo
      this.trackingInterval = null;

      // Atualiza o estado de rastreamento
      this.trackingActiveSubject.next(false);

      console.log('Rastreamento parado para viagem:', journeyId);
    }
  }

  // Função para obter a localização atual
  private async getCurrentLocation(): Promise<JourneyInfo | null> {
    try {
      const coordinates = await Geolocation.getCurrentPosition(
        { enableHighAccuracy: true,
          timeout: 10000
         }
      );  // Usando Capacitor
      const location: JourneyInfo = {
        id: this.generateUniqueId(),
        journeyId: this.journeyId,
        latitude: coordinates.coords.latitude,
        longitude: coordinates.coords.longitude,
        timestamp: new Date().toISOString(),  // Marca o horário atual
      };
      return location;
    } catch (error) {
      console.error('Erro ao obter a localização:', error);
      return null;
    }
  }

  // Função para obter a velocidade atual (em km/h)
  private async getCurrentSpeed(): Promise<number> {
    try {
      const position = await Geolocation.getCurrentPosition();  // Usando Capacitor
      const speed = position.coords.speed ? position.coords.speed * 3.6 : 0;  // Converte de m/s para km/h ou assume 0 se for null
      return speed;  // Retorna a velocidade em km/h
    } catch (error) {
      console.error('Erro ao obter a velocidade:', error);
      return 0;  // Se der erro, assume velocidade 0
    }
  }

  // Função para salvar a localização no banco de dados
  private async saveLocation(location: JourneyInfo): Promise<void> {
    try {
      // Use the correct table name 'journeyInfo' instead of 'location'
      await this.storage.insert('journeyInfo', location);
      console.log('Localização salva:', location);
    } catch (error) {
      console.error('Erro ao salvar a localização:', error);
    }
  }

  // Função para gerar um ID único para cada localização (pode ser melhorada)
  private generateUniqueId(): string {
    return Math.random().toString(36).substring(2, 15);
  }

  checkHardBreak(location: JourneyInfo): Promise<boolean> {
    return new Promise((resolve) => {
      const listener = Motion.addListener('accel', (event) => {
        const { acceleration } = event;

        if (acceleration?.x && acceleration.x < -10) {
          console.log('Freada brusca detectada!');

          location.occurrenceType = 'HardBreak';
          this.saveLocation(location);

          // // Cancela o listener para não continuar ouvindo
          // listener.remove();

          resolve(true); // Responde que detectou a freada
        }
      });
    });
  }


  async checkHighVelocity(location: JourneyInfo): Promise<boolean> {
    const speedKmH = await this.getCurrentSpeed();

     if (speedKmH > 120) { // Se a velocidade for maior que 120 km/h
          console.log('Alta velocidade detectada!');
          location.occurrenceType = 'Acceleration';  // Define o tipo de ocorrência como alta velocidade
          this.saveLocation(location);
          return true; // Retorna verdadeiro se a velocidade for maior que 120 km/h
     }
        return false; // Retorna falso se a velocidade for menor ou igual a 120 km/h
  }
}
