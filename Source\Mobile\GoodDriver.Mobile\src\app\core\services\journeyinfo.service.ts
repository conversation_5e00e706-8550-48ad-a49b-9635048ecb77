import { Injectable } from '@angular/core';
import { Geolocation } from '@capacitor/geolocation';  // Se estiver usando Capacitor
import { IDataStorage } from 'src/app/core/storage/data-storage.interface';  // Seu serviço de armazenamento
import { JourneyInfo } from 'src/app/core/models/journeyInfo.model'  // Seu modelo JourneyInfo
import { DataStorageService } from './data-storage-service';
import { Motion } from '@capacitor/motion';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class JourneyInfoService {
  private journeyId: string = '';
  private trackingInterval: any = null;
  private trackingStartTime: number = 0;
  private totalTimeAboveSpeed = 0;
  private storage: IDataStorage;

  // Para detecção de freada brusca baseada em velocidade
  private previousSpeed: number = 0;
  private previousSpeedTime: number = 0;

  // Observable para o status de rastreamento
  private trackingActiveSubject = new BehaviorSubject<boolean>(false);
  public trackingActive$ = this.trackingActiveSubject.asObservable();

  constructor(private dataStorageService: DataStorageService) {
    // Inicializa o serviço de dados (supondo que você tenha um serviço de dados como IndexedDBStorageService)
    this.storage = this.dataStorageService;
    
  }

  // Função para iniciar o rastreamento
  startTracking(journeyId: string) {
    this.trackingStartTime = Date.now();
    this.totalTimeAboveSpeed = 0;  // Reseta o tempo acima da velocidade
    this.journeyId = journeyId;

    // Reseta valores para detecção de freada brusca
    this.previousSpeed = 0;
    this.previousSpeedTime = 0;

    // Atualiza o estado de rastreamento
    this.trackingActiveSubject.next(true);

    this.trackingInterval = setInterval(async () => {
      try {
        console.log('Executando ciclo de rastreamento...');

        const location = await this.getCurrentLocation();
        if (location) {
          console.log('Localização obtida:', location);

          // Verifica eventos de direção com tratamento de erro individual
          try {
            console.log('Verificando freada brusca...');
            const hardBreakDetected = await this.checkHardBreak(location);
            console.log('Resultado checkHardBreak:', hardBreakDetected);
          } catch (error) {
            console.error('Erro em checkHardBreak:', error);
          }

          try {
            console.log('Verificando alta velocidade...');
            const highVelocityDetected = await this.checkHighVelocity(location);
            console.log('Resultado checkHighVelocity:', highVelocityDetected);
          } catch (error) {
            console.error('Erro em checkHighVelocity:', error);
          }

          // Salva a localização periodicamente
          try {
            console.log('Salvando localização...');
            await this.saveLocation(location);
            console.log('Localização salva com sucesso');
          } catch (error) {
            console.error('Erro ao salvar localização:', error);
          }
        } else {
          console.log('Não foi possível obter localização');
        }
      } catch (error) {
        console.error('Erro no ciclo de rastreamento:', error);
      }
    }, 10000);  // A cada 10 segundos

    console.log('Rastreamento iniciado para viagem:', journeyId);
  }

  // Função para parar o rastreamento
  stopTracking(journeyId: string) {
    if (this.trackingInterval) {
      clearInterval(this.trackingInterval);  // Limpa o intervalo
      this.trackingInterval = null;

      // Atualiza o estado de rastreamento
      this.trackingActiveSubject.next(false);

      console.log('Rastreamento parado para viagem:', journeyId);
    }
  }

  // Função para obter a localização atual
  private async getCurrentLocation(): Promise<JourneyInfo | null> {
    try {
      const coordinates = await Geolocation.getCurrentPosition({
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 5000  // Cache de 5 segundos para evitar leituras muito frequentes
      });

      // Verificar se a precisão é aceitável
      const accuracy = coordinates.coords.accuracy;
      if (accuracy && accuracy > 50) {
        console.log(`Precisão GPS baixa: ${accuracy}m, tentando novamente...`);
        // Tentar uma segunda vez com configurações mais rigorosas
        const retryCoordinates = await Geolocation.getCurrentPosition({
          enableHighAccuracy: true,
          timeout: 20000,
          maximumAge: 0  // Força nova leitura
        });

        if (retryCoordinates.coords.accuracy && retryCoordinates.coords.accuracy <= 50) {
          coordinates.coords = retryCoordinates.coords;
        }
      }

      const location: JourneyInfo = {
        id: this.generateUniqueId(),
        journeyId: this.journeyId,
        latitude: coordinates.coords.latitude,
        longitude: coordinates.coords.longitude,
        timestamp: new Date().toISOString(),
        // Adicionar informações de precisão se disponíveis
        accuracy: coordinates.coords.accuracy || undefined,
        altitude: coordinates.coords.altitude || undefined,
        speed: coordinates.coords.speed || undefined
      };

      console.log(`Localização obtida: ${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)} (precisão: ${accuracy}m)`);

      return location;
    } catch (error) {
      console.error('Erro ao obter a localização:', error);
      return null;
    }
  }

  // Função para obter a velocidade atual (em km/h)
  private async getCurrentSpeed(): Promise<number> {
    try {
      const position = await Geolocation.getCurrentPosition();  // Usando Capacitor
      const speed = position.coords.speed ? position.coords.speed * 3.6 : 0;  // Converte de m/s para km/h ou assume 0 se for null
      return speed;  // Retorna a velocidade em km/h
    } catch (error) {
      console.error('Erro ao obter a velocidade:', error);
      return 0;  // Se der erro, assume velocidade 0
    }
  }

  // Função para salvar a localização no banco de dados
  private async saveLocation(location: JourneyInfo): Promise<void> {
    try {
      // Use the correct table name 'journeyInfo' instead of 'location'
      await this.storage.insert('journeyInfo', location);
      console.log('Localização salva:', location);
    } catch (error) {
      console.error('Erro ao salvar a localização:', error);
    }
  }

  // Função para gerar um ID único para cada localização (pode ser melhorada)
  private generateUniqueId(): string {
    return Math.random().toString(36).substring(2, 15);
  }

  async checkHardBreak(location: JourneyInfo): Promise<boolean> {
    try {
      // Método 1: Detecção baseada em velocidade (mais confiável)
      const currentSpeed = await this.getCurrentSpeed();
      const currentTime = Date.now();

      if (this.previousSpeed > 0 && this.previousSpeedTime > 0) {
        const speedDifference = this.previousSpeed - currentSpeed; // Diferença de velocidade
        const timeDifference = (currentTime - this.previousSpeedTime) / 1000; // Diferença de tempo em segundos

        if (timeDifference > 0) {
          const deceleration = speedDifference / timeDifference; // Desaceleração em km/h por segundo

          console.log(`Velocidade anterior: ${this.previousSpeed} km/h, Atual: ${currentSpeed} km/h`);
          console.log(`Desaceleração: ${deceleration} km/h/s`);

          // Se a desaceleração for maior que 15 km/h por segundo, considera freada brusca
          if (deceleration > 15) {
            console.log('Freada brusca detectada por velocidade!');
            location.occurrenceType = 'HardBreak';
            await this.saveLocation(location);

            // Atualiza valores para próxima verificação
            this.previousSpeed = currentSpeed;
            this.previousSpeedTime = currentTime;

            return true;
          }
        }
      }

      // Atualiza valores para próxima verificação
      this.previousSpeed = currentSpeed;
      this.previousSpeedTime = currentTime;

      // Método 2: Fallback usando sensor de movimento (com timeout)
      return await this.checkHardBreakWithMotionSensor(location);

    } catch (error) {
      console.error('Erro em checkHardBreak:', error);
      return false;
    }
  }

  private async checkHardBreakWithMotionSensor(location: JourneyInfo): Promise<boolean> {
    return new Promise(async (resolve) => {
      let isResolved = false;
      let listenerHandle: any = null;

      try {
        // Verifica se o Motion sensor está disponível
        const isMotionAvailable = await this.isMotionSensorAvailable();
        if (!isMotionAvailable) {
          console.log('Sensor de movimento não disponível');
          resolve(false);
          return;
        }

        listenerHandle = await Motion.addListener('accel', (event) => {
          const { acceleration } = event;

          if (acceleration?.x && acceleration.x < -10) {
            console.log('Freada brusca detectada por sensor de movimento!');

            location.occurrenceType = 'HardBreak';
            this.saveLocation(location);

            // Cancela o listener para não continuar ouvindo
            if (listenerHandle) {
              listenerHandle.remove();
            }

            if (!isResolved) {
              isResolved = true;
              resolve(true); // Responde que detectou a freada
            }
          }
        });

        // Timeout para resolver a Promise após um tempo limite
        setTimeout(() => {
          if (!isResolved) {
            isResolved = true;
            if (listenerHandle) {
              listenerHandle.remove(); // Remove o listener
            }
            resolve(false); // Não detectou freada brusca
          }
        }, 1000); // Aguarda apenas 1 segundo para o sensor de movimento

      } catch (error) {
        console.error('Erro ao configurar listener de aceleração:', error);
        if (!isResolved) {
          isResolved = true;
          resolve(false);
        }
      }
    });
  }

  private async isMotionSensorAvailable(): Promise<boolean> {
    try {
      // Tenta criar um listener temporário para verificar se o sensor está disponível
      const testListener = await Motion.addListener('accel', () => {});
      if (testListener) {
        testListener.remove();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Erro ao verificar disponibilidade do sensor de movimento:', error);
      return false;
    }
  }


  async checkHighVelocity(location: JourneyInfo): Promise<boolean> {
    const speedKmH = await this.getCurrentSpeed();

     if (speedKmH > 120) { // Se a velocidade for maior que 120 km/h
          console.log('Alta velocidade detectada!');
          location.occurrenceType = 'Acceleration';  // Define o tipo de ocorrência como alta velocidade
          this.saveLocation(location);
          return true; // Retorna verdadeiro se a velocidade for maior que 120 km/h
     }
        return false; // Retorna falso se a velocidade for menor ou igual a 120 km/h
  }
}
