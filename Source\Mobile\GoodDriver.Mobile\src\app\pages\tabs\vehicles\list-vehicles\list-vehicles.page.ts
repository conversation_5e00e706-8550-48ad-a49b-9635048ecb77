import { Component, OnInit, OnDestroy } from '@angular/core';
import { VehicleService } from 'src/app/core/services/vehicle.service';
import { Vehicle } from 'src/app/core/models/vehicle.model';
import { IonicModule, RefresherCustomEvent } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { SessionService } from 'src/app/core/services/session.service';
import { ToastService } from 'src/app/core/services/toast.service';
import { VehicleStateService } from 'src/app/core/services/vehicle-state.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-list-vehicles',
  templateUrl: './list-vehicles.page.html',
  styleUrls: ['./list-vehicles.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, RouterModule]
})
export class ListVehiclesPage implements OnInit, OnDestroy {
  vehicles: Vehicle[] = [];
  isLoading = false;

  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    private vehicleService: VehicleService,
    private sessionService: SessionService,
    private toastService: ToastService,
    private vehicleStateService: VehicleStateService
  ) {}

  ngOnInit() {
    this.loadVehicles();

    // Subscribe to vehicles list changes
    this.subscriptions.push(
      this.vehicleStateService.vehicles$.subscribe(vehicles => {
        if (vehicles.length > 0) {
          this.vehicles = vehicles;
        }
      })
    );
  }

  ngOnDestroy() {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  async loadVehicles() {
    this.isLoading = true;
    const userId = await this.sessionService.getUserId() || '';
    try {
      this.vehicles = await this.vehicleService.listAllLocal(userId);
    } catch (error) {
      console.error('Error loading vehicles:', error);
      this.toastService.showToast('Erro ao carregar veículos', 'danger');
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Sets a vehicle as the primary vehicle
   * @param vehicleId The ID of the vehicle to set as primary
   */
  async setPrimaryVehicle(vehicleId: string) {
    const userId = await this.sessionService.getUserId() || '';
    try {
      const success = await this.vehicleService.setPrimaryVehicle(vehicleId, userId);
      if (success) {
        this.toastService.showToast('Veículo definido como principal');
        // No need to reload vehicles manually as we're now subscribed to the state service
        // The VehicleService will update the VehicleStateService,
        // which will notify all subscribers about the change
      } else {
        this.toastService.showToast('Erro ao definir veículo como principal', 'danger');
      }
    } catch (error) {
      console.error('Error setting primary vehicle:', error);
      this.toastService.showToast('Erro ao definir veículo como principal', 'danger');
    }
  }

  async doRefresh(event: RefresherCustomEvent) {
    await this.loadVehicles();
    event.target.complete();
  }
}