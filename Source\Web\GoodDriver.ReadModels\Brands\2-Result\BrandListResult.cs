﻿using Rogerio.Cqrs.Requests;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.ReadModels.Brands.Result
{
    public class BrandListResult : List<BrandListResult.BrandListItemResult> , IRequestResult
    {
        public BrandListResult()
        {
            
        }

        public BrandListResult(IList<BrandListItemResult> brands)
        {
            if(brands != null && brands.Count > 0)
            {
                this.AddRange(brands);
            }
        }

        public class BrandListItemResult
        {
            public int Id { get; private set; }

            public string Name { get; private set; }

            public string Ico { get; private set; }
        }        
    }
}
