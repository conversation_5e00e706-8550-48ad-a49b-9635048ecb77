import { Component, OnInit } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'tabs',
    templateUrl: 'tabs.page.html',
    //styleUrls: ['tabs.page.scss'],
    imports: [
      IonicModule, 
      CommonModule,
      RouterModule
    ]
  })

export class TabsPage implements OnInit 
{
    constructor(private router: Router) {
    }
    ngOnInit(): void {
        
    }

    logout() {
        console.log('Logout realizado');
        this.router.navigate(['/login']);
    }
}