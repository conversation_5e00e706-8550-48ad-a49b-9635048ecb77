{"version": 3, "sources": ["src/app/pages/tabs/journeys/journey-details/journey-details.page.ts", "src/app/pages/tabs/journeys/journey-details/journey-details.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute } from '@angular/router';\nimport { Journey } from 'src/app/core/models/journey.model';\nimport { JourneyInfo } from 'src/app/core/models/journeyInfo.model';\nimport { DataStorageService } from 'src/app/core/services/data-storage-service';\nimport { IonHeader, IonCardContent } from \"@ionic/angular/standalone\";\nimport { IonicModule } from '@ionic/angular';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-journey-details',\n  standalone: true,\n  templateUrl: './journey-details.page.html',\n  styleUrls: [ './journey-details.page.scss'],\n  imports: [\n    IonicModule,\n    CommonModule,\n  ]\n})\nexport class JourneyDetailsPage implements OnInit {\n\n  journey!: Journey;\n\n  constructor(\n    private route: ActivatedRoute,\n    private dataStorageService: DataStorageService\n  ) {}\n\n  async ngOnInit() {\n    const journeyId = this.route.snapshot.paramMap.get('id');\n    const journeyStr = this.route.snapshot.queryParamMap.get('data');\n\n    if (journeyStr) {\n      this.journey = JSON.parse(journeyStr);\n      this.journey.infosJourney?.map(async info => {\n        info.address = await this.reverseGeocode(info.latitude, info.longitude);\n        return info;\n      });        \n    }\n  }\n\n  async reverseGeocode(lat: number, lon: number): Promise<string> {\n    const url = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lon}`;\n    const response = await fetch(url);\n    const data = await response.json();\n    return data.display_name || 'Endereço não encontrado';\n  }\n}\n", "<ion-header [translucent]=\"true\">\n  <ion-toolbar color=\"primary\">\n    <ion-title>Detalhes da Viagem</ion-title>\n    <ion-buttons slot=\"start\">\n      <ion-back-button defaultHref=\"/tabs/journeys\"></ion-back-button>\n    </ion-buttons>\n  </ion-toolbar>\n</ion-header>\n\n<ion-content class=\"ion-padding\">\n\n  <ion-card *ngIf=\"journey\">\n    <ion-card-header>\n      <ion-card-title>Resumo</ion-card-title>\n    </ion-card-header>\n    <ion-card-content>\n      <p><strong>Início:</strong> {{ journey.startDate | date:'short' }}</p>\n      <p><strong>Fim:</strong> {{ journey.endDate ? (journey.endDate | date:'short') : 'Em andamento' }}</p>\n      <p><strong>Distância:</strong> {{ journey.distance | number:'1.2-2' }} km</p>\n    </ion-card-content>\n  </ion-card>\n\n  <ion-list>\n    <ion-list-header>\n      Localizações Registradas\n    </ion-list-header>\n\n    <div *ngIf=\"journey.infosJourney?.length === 0\" class=\"ion-text-center ion-padding\">\n      <p>Nenhuma localização registrada para esta viagem.</p>\n    </div>\n\n    <ion-item *ngFor=\"let info of journey.infosJourney\">\n      <ion-label>\n        <h3>{{ info.timestamp | date:'short' }}</h3>\n        <p>Latitude: {{ info.latitude }}, Longitude: {{ info.longitude }}</p>\n        <p><strong>Endereço:</strong> {{ info.address }}</p>\n      </ion-label>\n    </ion-item>\n  </ion-list>\n\n</ion-content>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACWE,IAAA,yBAAA,GAAA,UAAA,EAA0B,GAAA,iBAAA,EACP,GAAA,gBAAA;AACC,IAAA,iBAAA,GAAA,QAAA;AAAM,IAAA,uBAAA,EAAiB;AAEzC,IAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,GAAA,EACb,GAAA,QAAA;AAAQ,IAAA,iBAAA,GAAA,YAAA;AAAO,IAAA,uBAAA;AAAU,IAAA,iBAAA,CAAA;;AAAsC,IAAA,uBAAA;AAClE,IAAA,yBAAA,IAAA,GAAA,EAAG,IAAA,QAAA;AAAQ,IAAA,iBAAA,IAAA,MAAA;AAAI,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;;AAAyE,IAAA,uBAAA;AAClG,IAAA,yBAAA,IAAA,GAAA,EAAG,IAAA,QAAA;AAAQ,IAAA,iBAAA,IAAA,eAAA;AAAU,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;;AAA0C,IAAA,uBAAA,EAAI,EAC5D;;;;AAHW,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,sBAAA,GAAA,GAAA,OAAA,QAAA,WAAA,OAAA,GAAA,EAAA;AACH,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,QAAA,UAAA,sBAAA,IAAA,GAAA,OAAA,QAAA,SAAA,OAAA,IAAA,gBAAA,EAAA;AACM,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,sBAAA,IAAA,GAAA,OAAA,QAAA,UAAA,OAAA,GAAA,KAAA;;;;;AASjC,IAAA,yBAAA,GAAA,OAAA,CAAA,EAAoF,GAAA,GAAA;AAC/E,IAAA,iBAAA,GAAA,wDAAA;AAAgD,IAAA,uBAAA,EAAI;;;;;AAGzD,IAAA,yBAAA,GAAA,UAAA,EAAoD,GAAA,WAAA,EACvC,GAAA,IAAA;AACL,IAAA,iBAAA,CAAA;;AAAmC,IAAA,uBAAA;AACvC,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,CAAA;AAA8D,IAAA,uBAAA;AACjE,IAAA,yBAAA,GAAA,GAAA,EAAG,GAAA,QAAA;AAAQ,IAAA,iBAAA,GAAA,cAAA;AAAS,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;AAAkB,IAAA,uBAAA,EAAI,EAC1C;;;;AAHN,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,sBAAA,GAAA,GAAA,QAAA,WAAA,OAAA,CAAA;AACD,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,cAAA,QAAA,UAAA,iBAAA,QAAA,WAAA,EAAA;AAC2B,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,QAAA,SAAA,EAAA;;;ADnCtC,IAmBa;AAnBb;;;AAMA;AACA;;;;;;AAYM,IAAO,sBAAP,MAAO,oBAAkB;MAI7B,YACU,OACA,oBAAsC;AADtC,aAAA,QAAA;AACA,aAAA,qBAAA;MACP;MAEG,WAAQ;;AA5BhB;AA6BI,gBAAM,YAAY,KAAK,MAAM,SAAS,SAAS,IAAI,IAAI;AACvD,gBAAM,aAAa,KAAK,MAAM,SAAS,cAAc,IAAI,MAAM;AAE/D,cAAI,YAAY;AACd,iBAAK,UAAU,KAAK,MAAM,UAAU;AACpC,uBAAK,QAAQ,iBAAb,mBAA2B,IAAI,CAAM,SAAO;AAC1C,mBAAK,UAAU,MAAM,KAAK,eAAe,KAAK,UAAU,KAAK,SAAS;AACtE,qBAAO;YACT;UACF;QACF;;MAEM,eAAe,KAAa,KAAW;;AAC3C,gBAAM,MAAM,+DAA+D,GAAG,QAAQ,GAAG;AACzF,gBAAM,WAAW,MAAM,MAAM,GAAG;AAChC,gBAAM,OAAO,MAAM,SAAS,KAAI;AAChC,iBAAO,KAAK,gBAAgB;QAC9B;;;;uCA3BW,qBAAkB,4BAAA,cAAA,GAAA,4BAAA,kBAAA,CAAA;IAAA;wFAAlB,qBAAkB,WAAA,CAAA,CAAA,qBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,QAAA,OAAA,GAAA,CAAA,eAAA,gBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,SAAA,+BAAA,GAAA,MAAA,GAAA,CAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,mBAAA,aAAA,CAAA,GAAA,UAAA,SAAA,4BAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;ACnB/B,QAAA,yBAAA,GAAA,cAAA,CAAA,EAAiC,GAAA,eAAA,CAAA,EACF,GAAA,WAAA;AAChB,QAAA,iBAAA,GAAA,oBAAA;AAAkB,QAAA,uBAAA;AAC7B,QAAA,yBAAA,GAAA,eAAA,CAAA;AACE,QAAA,oBAAA,GAAA,mBAAA,CAAA;AACF,QAAA,uBAAA,EAAc,EACF;AAGhB,QAAA,yBAAA,GAAA,eAAA,CAAA;AAEE,QAAA,qBAAA,GAAA,wCAAA,IAAA,IAAA,YAAA,CAAA;AAWA,QAAA,yBAAA,GAAA,UAAA,EAAU,GAAA,iBAAA;AAEN,QAAA,iBAAA,IAAA,kCAAA;AACF,QAAA,uBAAA;AAEA,QAAA,qBAAA,IAAA,oCAAA,GAAA,GAAA,OAAA,CAAA,EAAoF,IAAA,yCAAA,IAAA,GAAA,YAAA,CAAA;AAWtF,QAAA,uBAAA,EAAW;;;AAtCD,QAAA,qBAAA,eAAA,IAAA;AAWC,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,OAAA;AAgBH,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,SAAA,IAAA,QAAA,gBAAA,OAAA,OAAA,IAAA,QAAA,aAAA,YAAA,CAAA;AAIqB,QAAA,oBAAA;AAAA,QAAA,qBAAA,WAAA,IAAA,QAAA,YAAA;;sBDhB3B,aAAW,YAAA,SAAA,gBAAA,eAAA,cAAA,YAAA,WAAA,SAAA,UAAA,SAAA,eAAA,UAAA,YAAA,eACX,cAAY,SAAA,MAAA,aAAA,QAAA,GAAA,eAAA,EAAA,CAAA;AAGV,IAAO,qBAAP;;0EAAO,oBAAkB,CAAA;cAV9B;2BACW,uBAAqB,YACnB,MAAI,SAGP;UACP;UACA;WACD,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA,CAAA;;;;iFAEU,oBAAkB,EAAA,WAAA,sBAAA,UAAA,uEAAA,YAAA,GAAA,CAAA;IAAA,GAAA;;;", "names": []}