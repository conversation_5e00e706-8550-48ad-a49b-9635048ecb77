﻿using GoodDriver.API.Controllers.Users;
using GoodDriver.Contracts.Sync.Commands;
using GoodDriver.Contracts.Vehicles.Commands;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Rogerio.Commom.Exceptions;
using Rogerio.Cqrs.Commands;
using Rogerio.Cqrs.Requests;
using Rogerio.Security;
using Rogerio.Security.Domain;

namespace GoodDriver.API.Controllers.Sync
{
    [ApiController]
    [Route("api/sync")]
    public class SyncController : BaseControllerGoodDriver
    {
        private readonly ICommandBus commandBus;
        private readonly IConfiguration _config;
        private readonly IRequestBus requestBus;

        public SyncController(
            IConfiguration config, 
            ILogger<SyncController> logger, 
            ISecurityManager securityManager, 
            ICommandBus commandBus, 
            IRequestBus requestBus) 
            : base(securityManager)
        {
            _config = config;
            this.commandBus = commandBus;
            this.requestBus = requestBus;
        }

        /// <summary>
        /// Synchronizes user data from the mobile app
        /// </summary>
        [HttpPost("user")]
        [Authorize]
        public async Task<IActionResult> SyncUser([FromBody] SyncUserCommand command)
        {
            if (!ModelState.IsValid)
                return BadRequest(new { Success = false, Message = "Invalid data for user synchronization." });

            try
            {
                // Set the user ID from the authenticated user if not provided
                if (string.IsNullOrEmpty(command.UserId))
                {
                    command.UserId = User.Id;
                }

                await commandBus.SendAsync(command);
                return Ok(new { Success = true, Message = "User data synchronized successfully." });
            }
            catch (BusinessException ex)
            {
                return BadRequest(new { message = ex.Message, errorCode = ex.Key });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// Synchronizes vehicle data from the mobile app
        /// </summary>
        [HttpPost("vehicle")]
        [Authorize]
        public async Task<IActionResult> SyncVehicle([FromBody] VehicleSyncCommand command)
        {
            if (!ModelState.IsValid)
                return BadRequest(new { Success = false, Message = "Invalid data for vehicle synchronization." });

            try
            {
                // Set the user ID from the authenticated user if not provided
                if (string.IsNullOrEmpty(command.UserId))
                {
                    command.UserId = User.Id;
                }

                await commandBus.SendAsync(command);
                return Ok(new { Success = true, Message = "Vehicle data synchronized successfully." });
            }
            catch (BusinessException ex)
            {
                return BadRequest(new { message = ex.Message, errorCode = ex.Key });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// Synchronizes journey data from the mobile app
        /// </summary>
        [HttpPost("journey")]
        [Authorize]
        public async Task<IActionResult> SyncJourney([FromBody] SyncJourneyCommand command)
        {
            if (!ModelState.IsValid)
                return BadRequest(new { Success = false, Message = "Invalid data for journey synchronization." });

            try
            {
                // Set the user ID from the authenticated user if not provided
                if (string.IsNullOrEmpty(command.UserId))
                {
                    command.UserId = User.Id;
                }

                await commandBus.SendAsync(command);
                return Ok(new { Success = true, Message = "Journey data synchronized successfully." });
            }
            catch (BusinessException ex)
            {
                return BadRequest(new { message = ex.Message, errorCode = ex.Key });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// Synchronizes all data from the mobile app in a single request
        /// </summary>
        [HttpPost("all")]
        [Authorize]
        public async Task<IActionResult> SyncAll([FromBody] SyncAllCommand command)
        {
            if (!ModelState.IsValid)
                return BadRequest(new { Success = false, Message = "Invalid data for synchronization." });

            try
            {
                // Set the user ID from the authenticated user if not provided
                if (string.IsNullOrEmpty(command.UserId))
                {
                    command.UserId = User.Id;
                }

                await commandBus.SendAsync(command);
                return Ok(new { Success = true, Message = "All data synchronized successfully." });
            }
            catch (BusinessException ex)
            {
                return BadRequest(new { message = ex.Message, errorCode = ex.Key });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
