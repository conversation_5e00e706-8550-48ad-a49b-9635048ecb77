﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Common.Validation
{
    public class StrongPasswordValidatorAttribute : RegularExpValidatorAttribute, IRuleSetAttribute
    {
        public int RequiredLength { get; private set; } = 6;


        public bool RequireNonLetterOrDigit { get; private set; }

        public bool RequireDigit { get; private set; } = true;


        public bool RequireLowercase { get; private set; }

        public bool RequireUppercase { get; private set; } = true;


        public StrongPasswordValidatorAttribute()
            : base(GenerateParttern(6, requireNonLetterOrDigit: false, requireDigit: true, requireLowercase: false, requireUppercase: true))
        {
            base.Prefix = "StrongPasswordRequired";
        }

        public StrongPasswordValidatorAttribute(params string[] rulesSet)
            : base(GenerateParttern(6, requireNonLetterOrDigit: false, requireDigit: true, requireLowercase: false, requireUppercase: true), rulesSet)
        {
            base.Prefix = "StrongPasswordRequired";
        }

        public StrongPasswordValidatorAttribute(int requiredLength, bool requireNonLetterOrDigit, bool requireDigit, bool requireLowercase, bool requireUppercase, params string[] rulesSet)
            : base(GenerateParttern(requiredLength, requireNonLetterOrDigit, requireDigit, requireLowercase, requireUppercase), rulesSet)
        {
            RequiredLength = requiredLength;
            RequireNonLetterOrDigit = requireNonLetterOrDigit;
            RequireDigit = requireDigit;
            RequireLowercase = requireLowercase;
            RequireUppercase = requireUppercase;
        }

        private static string GenerateParttern(int requiredLength, bool requireNonLetterOrDigit, bool requireDigit, bool requireLowercase, bool requireUppercase)
        {
            string text = "^";
            if (requireLowercase)
            {
                text += "(?=(.*[a-z]){1,})";
            }

            if (requireUppercase)
            {
                text += "(?=(.*[A-Z]){1,})";
            }

            if (requireDigit)
            {
                text += "(?=(.*[0-9]){1,})";
            }

            if (requireNonLetterOrDigit)
            {
                text += "(?=(.*[!@#$%^&*()\\-__+.]){1,})";
            }

            return text + ".{" + requiredLength + ",}$";
        }
    }
}
