﻿using GoodDriver.API.Helpers;
using GoodDriver.Contracts.Users.Commands;
using GoodDriver.Contracts.Users.Requests;
using GoodDriver.Contracts.Users.Responses;
using Microsoft.AspNetCore.Identity.Data;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Rogerio.Commom.Exceptions;
using Rogerio.Cqrs.Commands;
using Rogerio.Security;
using Rogerio.Security.Domain;

namespace GoodDriver.API.Controllers.Users
{
    [ApiController]
    [Route("api/user")]
    public class UserController : BaseControllerGoodDriver
	{
		private readonly ICommandBus commandBus;
		private readonly IConfiguration _config;
        private readonly IOptions<SecurityOptions> secutiryOption;

        public UserController(IConfiguration config, ILogger<UserController> logger, ISecurityManager securityManager, ICommandBus _commandBus, IOptions<SecurityOptions> secutiryOption)
			: base(securityManager)
		{
			_config = config;
			commandBus = _commandBus;
            this.secutiryOption = secutiryOption;
        }

        /// <summary>
        /// Cria um novo usuário no sistema.
        /// </summary>
        [HttpPost("create")]
		//[Authorize(Roles = "admin")] // Apenas administradores podem criar usuários
		public async Task<IActionResult> CreateUser([FromBody] UserCreateCommand createcommand)
		{
			if (!ModelState.IsValid)
				return new JsonResult(new { Success = false, Message = "Dados inválidos para criação do usuário." });

			try
			{
				await commandBus.SendAsync(createcommand);
				return Ok(new { Success = true, Message = "Usuário criado com sucesso, consulte sua caixa de e-mail para ativar sua conta."});
			}
            catch (BusinessException ex)
            {
                return BadRequest(new { message = ex.Message, errorCode = ex.Key });
            }
            catch (Exception ex)
			{
                return BadRequest(new { message = ex.Message });
            }

		}

		[HttpPost("authentication")]
		public async Task<IActionResult> AuthenticationAsync([FromBody] LoginRequest login)
        {
            try
			{
                var commandResponse = await commandBus.SendAsync<UserLogonRequest, UserLogonResponse>(new UserLogonRequest(login.Email, login.Password));

                if (!commandResponse.Success)
                {
                    return BadRequest(new { message = commandResponse.Message });
                }

                var principal = PrincipalFactory.Create(commandResponse);

                string token = securityManager.GetToken(principal);

				var response = new
				{
                    Token = token,
                    UserId = commandResponse.UserId,
                    UserName = commandResponse.UserName,
                    UserEmail = commandResponse.UserEmail,
                    CookieName = secutiryOption.Value?.Cookie?.Name,
                    CookieDomain = secutiryOption.Value?.Cookie?.Domain
                };

                return Ok(response);
            }
            catch (BusinessException ex)
            {
                if (ex.Key.Equals("User.NotFound") || ex.Key.Equals("User.Invalid"))
                {
                    return StatusCode(statusCode: 401, value: new { message = ex.Message, errorCode = ex.Key });
                }

                return StatusCode(statusCode: 400, value: new { message = ex.Message, errorCode = ex.Key });
            }
            catch (Exception exception)
            {
                return BadRequest(new { message = exception.Message });
            }
        }
	}
}
