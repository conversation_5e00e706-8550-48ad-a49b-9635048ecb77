{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-img.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { h as inheritAttributes } from './helpers-d94bc8ad.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport './index-cfd9c1f2.js';\nconst imgCss = \":host{display:block;-o-object-fit:contain;object-fit:contain}img{display:block;width:100%;height:100%;-o-object-fit:inherit;object-fit:inherit;-o-object-position:inherit;object-position:inherit}\";\nconst IonImgStyle0 = imgCss;\nconst Img = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionImgWillLoad = createEvent(this, \"ionImgWillLoad\", 7);\n    this.ionImgDidLoad = createEvent(this, \"ionImgDidLoad\", 7);\n    this.ionError = createEvent(this, \"ionError\", 7);\n    this.inheritedAttributes = {};\n    this.onLoad = () => {\n      this.ionImgDidLoad.emit();\n    };\n    this.onError = () => {\n      this.ionError.emit();\n    };\n    this.loadSrc = undefined;\n    this.loadError = undefined;\n    this.alt = undefined;\n    this.src = undefined;\n  }\n  srcChanged() {\n    this.addIO();\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAttributes(this.el, ['draggable']);\n  }\n  componentDidLoad() {\n    this.addIO();\n  }\n  addIO() {\n    if (this.src === undefined) {\n      return;\n    }\n    if (typeof window !== 'undefined' && 'IntersectionObserver' in window && 'IntersectionObserverEntry' in window && 'isIntersecting' in window.IntersectionObserverEntry.prototype) {\n      this.removeIO();\n      this.io = new IntersectionObserver(data => {\n        /**\n         * On slower devices, it is possible for an intersection observer entry to contain multiple\n         * objects in the array. This happens when quickly scrolling an image into view and then out of\n         * view. In this case, the last object represents the current state of the component.\n         */\n        if (data[data.length - 1].isIntersecting) {\n          this.load();\n          this.removeIO();\n        }\n      });\n      this.io.observe(this.el);\n    } else {\n      // fall back to setTimeout for Safari and IE\n      setTimeout(() => this.load(), 200);\n    }\n  }\n  load() {\n    this.loadError = this.onError;\n    this.loadSrc = this.src;\n    this.ionImgWillLoad.emit();\n  }\n  removeIO() {\n    if (this.io) {\n      this.io.disconnect();\n      this.io = undefined;\n    }\n  }\n  render() {\n    const {\n      loadSrc,\n      alt,\n      onLoad,\n      loadError,\n      inheritedAttributes\n    } = this;\n    const {\n      draggable\n    } = inheritedAttributes;\n    return h(Host, {\n      key: 'da600442894427dee1974a28e545613afac69fca',\n      class: getIonMode(this)\n    }, h(\"img\", {\n      key: '16df0c7069af86c0fa7ce5af598bc0f63b4eb71a',\n      decoding: \"async\",\n      src: loadSrc,\n      alt: alt,\n      onLoad: onLoad,\n      onError: loadError,\n      part: \"image\",\n      draggable: isDraggable(draggable)\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"src\": [\"srcChanged\"]\n    };\n  }\n};\n/**\n * Enumerated strings must be set as booleans\n * as Stencil will not render 'false' in the DOM.\n * The need to explicitly render draggable=\"true\"\n * as only certain elements are draggable by default.\n * https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/draggable.\n */\nconst isDraggable = draggable => {\n  switch (draggable) {\n    case 'true':\n      return true;\n    case 'false':\n      return false;\n    default:\n      return undefined;\n  }\n};\nImg.style = IonImgStyle0;\nexport { Img as ion_img };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAOM,QACA,cACA,KAsGA;AA/GN;AAAA;AAGA;AACA;AACA;AACA;AACA,IAAM,SAAS;AACf,IAAM,eAAe;AACrB,IAAM,MAAM,MAAM;AAAA,MAChB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,aAAK,gBAAgB,YAAY,MAAM,iBAAiB,CAAC;AACzD,aAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,aAAK,sBAAsB,CAAC;AAC5B,aAAK,SAAS,MAAM;AAClB,eAAK,cAAc,KAAK;AAAA,QAC1B;AACA,aAAK,UAAU,MAAM;AACnB,eAAK,SAAS,KAAK;AAAA,QACrB;AACA,aAAK,UAAU;AACf,aAAK,YAAY;AACjB,aAAK,MAAM;AACX,aAAK,MAAM;AAAA,MACb;AAAA,MACA,aAAa;AACX,aAAK,MAAM;AAAA,MACb;AAAA,MACA,oBAAoB;AAClB,aAAK,sBAAsB,kBAAkB,KAAK,IAAI,CAAC,WAAW,CAAC;AAAA,MACrE;AAAA,MACA,mBAAmB;AACjB,aAAK,MAAM;AAAA,MACb;AAAA,MACA,QAAQ;AACN,YAAI,KAAK,QAAQ,QAAW;AAC1B;AAAA,QACF;AACA,YAAI,OAAO,WAAW,eAAe,0BAA0B,UAAU,+BAA+B,UAAU,oBAAoB,OAAO,0BAA0B,WAAW;AAChL,eAAK,SAAS;AACd,eAAK,KAAK,IAAI,qBAAqB,UAAQ;AAMzC,gBAAI,KAAK,KAAK,SAAS,CAAC,EAAE,gBAAgB;AACxC,mBAAK,KAAK;AACV,mBAAK,SAAS;AAAA,YAChB;AAAA,UACF,CAAC;AACD,eAAK,GAAG,QAAQ,KAAK,EAAE;AAAA,QACzB,OAAO;AAEL,qBAAW,MAAM,KAAK,KAAK,GAAG,GAAG;AAAA,QACnC;AAAA,MACF;AAAA,MACA,OAAO;AACL,aAAK,YAAY,KAAK;AACtB,aAAK,UAAU,KAAK;AACpB,aAAK,eAAe,KAAK;AAAA,MAC3B;AAAA,MACA,WAAW;AACT,YAAI,KAAK,IAAI;AACX,eAAK,GAAG,WAAW;AACnB,eAAK,KAAK;AAAA,QACZ;AAAA,MACF;AAAA,MACA,SAAS;AACP,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,OAAO,WAAW,IAAI;AAAA,QACxB,GAAG,EAAE,OAAO;AAAA,UACV,KAAK;AAAA,UACL,UAAU;AAAA,UACV,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA,SAAS;AAAA,UACT,MAAM;AAAA,UACN,WAAW,YAAY,SAAS;AAAA,QAClC,CAAC,CAAC;AAAA,MACJ;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,OAAO,CAAC,YAAY;AAAA,QACtB;AAAA,MACF;AAAA,IACF;AAQA,IAAM,cAAc,eAAa;AAC/B,cAAQ,WAAW;AAAA,QACjB,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AACA,QAAI,QAAQ;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}