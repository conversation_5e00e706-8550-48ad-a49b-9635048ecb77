{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/overlays-d99dcb0a.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index-a5d50daf.js';\nimport { f as focusVisibleElement, c as componentOnReady, a as addEventListener, b as removeEventListener, g as getElementRoot } from './helpers-d94bc8ad.js';\nimport { OVERLAY_BACK_BUTTON_PRIORITY, shouldUseCloseWatcher } from './hardware-back-button-a7eb8233.js';\nimport { c as config, d as printIonError, p as printIonWarning } from './index-cfd9c1f2.js';\nimport { b as getIonMode, a as isPlatform } from './ionic-global-b26f573e.js';\nimport { C as CoreDelegate } from './framework-delegate-56b467ad.js';\nimport { B as BACKDROP_NO_SCROLL } from './gesture-controller-314a54f6.js';\n\n/**\n * This query string selects elements that\n * are eligible to receive focus. We select\n * interactive elements that meet the following\n * criteria:\n * 1. <PERSON><PERSON> does not have a negative tabindex\n * 2. Element does not have `hidden`\n * 3. <PERSON><PERSON> does not have `disabled` for non-Ionic components.\n * 4. Element does not have `disabled` or `disabled=\"true\"` for Ionic components.\n * Note: We need this distinction because `disabled=\"false\"` is\n * valid usage for the disabled property on ion-button.\n */\nconst focusableQueryString = '[tabindex]:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^=\"-\"]):not([hidden]):not([disabled]), textarea:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), button:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), select:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), .ion-focusable[disabled=\"false\"]:not([tabindex^=\"-\"]):not([hidden])';\n/**\n * Focuses the first descendant in a context\n * that can receive focus. If none exists,\n * a fallback element will be focused.\n * This fallback is typically an ancestor\n * container such as a menu or overlay so focus does not\n * leave the container we are trying to trap focus in.\n *\n * If no fallback is specified then we focus the container itself.\n */\nconst focusFirstDescendant = (ref, fallbackElement) => {\n  const firstInput = ref.querySelector(focusableQueryString);\n  focusElementInContext(firstInput, fallbackElement !== null && fallbackElement !== void 0 ? fallbackElement : ref);\n};\n/**\n * Focuses the last descendant in a context\n * that can receive focus. If none exists,\n * a fallback element will be focused.\n * This fallback is typically an ancestor\n * container such as a menu or overlay so focus does not\n * leave the container we are trying to trap focus in.\n *\n * If no fallback is specified then we focus the container itself.\n */\nconst focusLastDescendant = (ref, fallbackElement) => {\n  const inputs = Array.from(ref.querySelectorAll(focusableQueryString));\n  const lastInput = inputs.length > 0 ? inputs[inputs.length - 1] : null;\n  focusElementInContext(lastInput, fallbackElement !== null && fallbackElement !== void 0 ? fallbackElement : ref);\n};\n/**\n * Focuses a particular element in a context. If the element\n * doesn't have anything focusable associated with it then\n * a fallback element will be focused.\n *\n * This fallback is typically an ancestor\n * container such as a menu or overlay so focus does not\n * leave the container we are trying to trap focus in.\n * This should be used instead of the focus() method\n * on most elements because the focusable element\n * may not be the host element.\n *\n * For example, if an ion-button should be focused\n * then we should actually focus the native <button>\n * element inside of ion-button's shadow root, not\n * the host element itself.\n */\nconst focusElementInContext = (hostToFocus, fallbackElement) => {\n  let elementToFocus = hostToFocus;\n  const shadowRoot = hostToFocus === null || hostToFocus === void 0 ? void 0 : hostToFocus.shadowRoot;\n  if (shadowRoot) {\n    // If there are no inner focusable elements, just focus the host element.\n    elementToFocus = shadowRoot.querySelector(focusableQueryString) || hostToFocus;\n  }\n  if (elementToFocus) {\n    const radioGroup = elementToFocus.closest('ion-radio-group');\n    if (radioGroup) {\n      radioGroup.setFocus();\n    } else {\n      focusVisibleElement(elementToFocus);\n    }\n  } else {\n    // Focus fallback element instead of letting focus escape\n    fallbackElement.focus();\n  }\n};\nlet lastOverlayIndex = 0;\nlet lastId = 0;\nconst activeAnimations = new WeakMap();\nconst createController = tagName => {\n  return {\n    create(options) {\n      return createOverlay(tagName, options);\n    },\n    dismiss(data, role, id) {\n      return dismissOverlay(document, data, role, tagName, id);\n    },\n    async getTop() {\n      return getPresentedOverlay(document, tagName);\n    }\n  };\n};\nconst alertController = /*@__PURE__*/createController('ion-alert');\nconst actionSheetController = /*@__PURE__*/createController('ion-action-sheet');\nconst loadingController = /*@__PURE__*/createController('ion-loading');\nconst modalController = /*@__PURE__*/createController('ion-modal');\n/**\n * @deprecated Use the inline ion-picker component instead.\n */\nconst pickerController = /*@__PURE__*/createController('ion-picker-legacy');\nconst popoverController = /*@__PURE__*/createController('ion-popover');\nconst toastController = /*@__PURE__*/createController('ion-toast');\n/**\n * Prepares the overlay element to be presented.\n */\nconst prepareOverlay = el => {\n  if (typeof document !== 'undefined') {\n    /**\n     * Adds a single instance of event listeners for application behaviors:\n     *\n     * - Escape Key behavior to dismiss an overlay\n     * - Trapping focus within an overlay\n     * - Back button behavior to dismiss an overlay\n     *\n     * This only occurs when the first overlay is created.\n     */\n    connectListeners(document);\n  }\n  const overlayIndex = lastOverlayIndex++;\n  /**\n   * overlayIndex is used in the overlay components to set a zIndex.\n   * This ensures that the most recently presented overlay will be\n   * on top.\n   */\n  el.overlayIndex = overlayIndex;\n};\n/**\n * Assigns an incrementing id to an overlay element, that does not\n * already have an id assigned to it.\n *\n * Used to track unique instances of an overlay element.\n */\nconst setOverlayId = el => {\n  if (!el.hasAttribute('id')) {\n    el.id = `ion-overlay-${++lastId}`;\n  }\n  return el.id;\n};\nconst createOverlay = (tagName, opts) => {\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n  if (typeof window !== 'undefined' && typeof window.customElements !== 'undefined') {\n    return window.customElements.whenDefined(tagName).then(() => {\n      const element = document.createElement(tagName);\n      element.classList.add('overlay-hidden');\n      /**\n       * Convert the passed in overlay options into props\n       * that get passed down into the new overlay.\n       */\n      Object.assign(element, Object.assign(Object.assign({}, opts), {\n        hasController: true\n      }));\n      // append the overlay element to the document body\n      getAppRoot(document).appendChild(element);\n      return new Promise(resolve => componentOnReady(element, resolve));\n    });\n  }\n  return Promise.resolve();\n};\nconst isOverlayHidden = overlay => overlay.classList.contains('overlay-hidden');\n/**\n * Focuses a particular element in an overlay. If the element\n * doesn't have anything focusable associated with it then\n * the overlay itself will be focused.\n * This should be used instead of the focus() method\n * on most elements because the focusable element\n * may not be the host element.\n *\n * For example, if an ion-button should be focused\n * then we should actually focus the native <button>\n * element inside of ion-button's shadow root, not\n * the host element itself.\n */\nconst focusElementInOverlay = (hostToFocus, overlay) => {\n  let elementToFocus = hostToFocus;\n  const shadowRoot = hostToFocus === null || hostToFocus === void 0 ? void 0 : hostToFocus.shadowRoot;\n  if (shadowRoot) {\n    // If there are no inner focusable elements, just focus the host element.\n    elementToFocus = shadowRoot.querySelector(focusableQueryString) || hostToFocus;\n  }\n  if (elementToFocus) {\n    focusVisibleElement(elementToFocus);\n  } else {\n    // Focus overlay instead of letting focus escape\n    overlay.focus();\n  }\n};\n/**\n * Traps keyboard focus inside of overlay components.\n * Based on https://w3c.github.io/aria-practices/examples/dialog-modal/alertdialog.html\n * This includes the following components: Action Sheet, Alert, Loading, Modal,\n * Picker, and Popover.\n * Should NOT include: Toast\n */\nconst trapKeyboardFocus = (ev, doc) => {\n  const lastOverlay = getPresentedOverlay(doc, 'ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover');\n  const target = ev.target;\n  /**\n   * If no active overlay, ignore this event.\n   *\n   * If this component uses the shadow dom,\n   * this global listener is pointless\n   * since it will not catch the focus\n   * traps as they are inside the shadow root.\n   * We need to add a listener to the shadow root\n   * itself to ensure the focus trap works.\n   */\n  if (!lastOverlay || !target) {\n    return;\n  }\n  /**\n   * If the ion-disable-focus-trap class\n   * is present on an overlay, then this component\n   * instance has opted out of focus trapping.\n   * An example of this is when the sheet modal\n   * has a backdrop that is disabled. The content\n   * behind the sheet should be focusable until\n   * the backdrop is enabled.\n   */\n  if (lastOverlay.classList.contains(FOCUS_TRAP_DISABLE_CLASS)) {\n    return;\n  }\n  const trapScopedFocus = () => {\n    /**\n     * If we are focusing the overlay, clear\n     * the last focused element so that hitting\n     * tab activates the first focusable element\n     * in the overlay wrapper.\n     */\n    if (lastOverlay === target) {\n      lastOverlay.lastFocus = undefined;\n      /**\n       * Toasts can be presented from an overlay.\n       * However, focus should still be returned to\n       * the overlay when clicking a toast. Normally,\n       * focus would be returned to the last focusable\n       * descendant in the overlay which may not always be\n       * the button that the toast was presented from. In this case,\n       * the focus may be returned to an unexpected element.\n       * To account for this, we make sure to return focus to the\n       * last focused element in the overlay if focus is\n       * moved to the toast.\n       */\n    } else if (target.tagName === 'ION-TOAST') {\n      focusElementInOverlay(lastOverlay.lastFocus, lastOverlay);\n      /**\n       * Otherwise, we must be focusing an element\n       * inside of the overlay. The two possible options\n       * here are an input/button/etc or the ion-focus-trap\n       * element. The focus trap element is used to prevent\n       * the keyboard focus from leaving the overlay when\n       * using Tab or screen assistants.\n       */\n    } else {\n      /**\n       * We do not want to focus the traps, so get the overlay\n       * wrapper element as the traps live outside of the wrapper.\n       */\n      const overlayRoot = getElementRoot(lastOverlay);\n      if (!overlayRoot.contains(target)) {\n        return;\n      }\n      const overlayWrapper = overlayRoot.querySelector('.ion-overlay-wrapper');\n      if (!overlayWrapper) {\n        return;\n      }\n      /**\n       * If the target is inside the wrapper, let the browser\n       * focus as normal and keep a log of the last focused element.\n       * Additionally, if the backdrop was tapped we should not\n       * move focus back inside the wrapper as that could cause\n       * an interactive elements focus state to activate.\n       */\n      if (overlayWrapper.contains(target) || target === overlayRoot.querySelector('ion-backdrop')) {\n        lastOverlay.lastFocus = target;\n      } else {\n        /**\n         * Otherwise, we must have focused one of the focus traps.\n         * We need to wrap the focus to either the first element\n         * or the last element.\n         */\n        /**\n         * Once we call `focusFirstDescendant` and focus the first\n         * descendant, another focus event will fire which will\n         * cause `lastOverlay.lastFocus` to be updated before\n         * we can run the code after that. We will cache the value\n         * here to avoid that.\n         */\n        const lastFocus = lastOverlay.lastFocus;\n        // Focus the first element in the overlay wrapper\n        focusFirstDescendant(overlayWrapper, lastOverlay);\n        /**\n         * If the cached last focused element is the\n         * same as the active element, then we need\n         * to wrap focus to the last descendant. This happens\n         * when the first descendant is focused, and the user\n         * presses Shift + Tab. The previous line will focus\n         * the same descendant again (the first one), causing\n         * last focus to equal the active element.\n         */\n        if (lastFocus === doc.activeElement) {\n          focusLastDescendant(overlayWrapper, lastOverlay);\n        }\n        lastOverlay.lastFocus = doc.activeElement;\n      }\n    }\n  };\n  const trapShadowFocus = () => {\n    /**\n     * If the target is inside the wrapper, let the browser\n     * focus as normal and keep a log of the last focused element.\n     */\n    if (lastOverlay.contains(target)) {\n      lastOverlay.lastFocus = target;\n      /**\n       * Toasts can be presented from an overlay.\n       * However, focus should still be returned to\n       * the overlay when clicking a toast. Normally,\n       * focus would be returned to the last focusable\n       * descendant in the overlay which may not always be\n       * the button that the toast was presented from. In this case,\n       * the focus may be returned to an unexpected element.\n       * To account for this, we make sure to return focus to the\n       * last focused element in the overlay if focus is\n       * moved to the toast.\n       */\n    } else if (target.tagName === 'ION-TOAST') {\n      focusElementInOverlay(lastOverlay.lastFocus, lastOverlay);\n    } else {\n      /**\n       * Otherwise, we are about to have focus\n       * go out of the overlay. We need to wrap\n       * the focus to either the first element\n       * or the last element.\n       */\n      /**\n       * Once we call `focusFirstDescendant` and focus the first\n       * descendant, another focus event will fire which will\n       * cause `lastOverlay.lastFocus` to be updated before\n       * we can run the code after that. We will cache the value\n       * here to avoid that.\n       */\n      const lastFocus = lastOverlay.lastFocus;\n      // Focus the first element in the overlay wrapper\n      focusFirstDescendant(lastOverlay);\n      /**\n       * If the cached last focused element is the\n       * same as the active element, then we need\n       * to wrap focus to the last descendant. This happens\n       * when the first descendant is focused, and the user\n       * presses Shift + Tab. The previous line will focus\n       * the same descendant again (the first one), causing\n       * last focus to equal the active element.\n       */\n      if (lastFocus === doc.activeElement) {\n        focusLastDescendant(lastOverlay);\n      }\n      lastOverlay.lastFocus = doc.activeElement;\n    }\n  };\n  if (lastOverlay.shadowRoot) {\n    trapShadowFocus();\n  } else {\n    trapScopedFocus();\n  }\n};\nconst connectListeners = doc => {\n  if (lastOverlayIndex === 0) {\n    lastOverlayIndex = 1;\n    doc.addEventListener('focus', ev => {\n      trapKeyboardFocus(ev, doc);\n    }, true);\n    // handle back-button click\n    doc.addEventListener('ionBackButton', ev => {\n      const lastOverlay = getPresentedOverlay(doc);\n      if (lastOverlay === null || lastOverlay === void 0 ? void 0 : lastOverlay.backdropDismiss) {\n        ev.detail.register(OVERLAY_BACK_BUTTON_PRIORITY, () => {\n          /**\n           * Do not return this promise otherwise\n           * the hardware back button utility will\n           * be blocked until the overlay dismisses.\n           * This is important for a modal with canDismiss.\n           * If the application presents a confirmation alert\n           * in the \"canDismiss\" callback, then it will be impossible\n           * to use the hardware back button to dismiss the alert\n           * dialog because the hardware back button utility\n           * is blocked on waiting for the modal to dismiss.\n           */\n          lastOverlay.dismiss(undefined, BACKDROP);\n        });\n      }\n    });\n    /**\n     * Handle ESC to close overlay.\n     * CloseWatcher also handles pressing the Esc\n     * key, so if a browser supports CloseWatcher then\n     * this behavior will be handled via the ionBackButton\n     * event.\n     */\n    if (!shouldUseCloseWatcher()) {\n      doc.addEventListener('keydown', ev => {\n        if (ev.key === 'Escape') {\n          const lastOverlay = getPresentedOverlay(doc);\n          if (lastOverlay === null || lastOverlay === void 0 ? void 0 : lastOverlay.backdropDismiss) {\n            lastOverlay.dismiss(undefined, BACKDROP);\n          }\n        }\n      });\n    }\n  }\n};\nconst dismissOverlay = (doc, data, role, overlayTag, id) => {\n  const overlay = getPresentedOverlay(doc, overlayTag, id);\n  if (!overlay) {\n    return Promise.reject('overlay does not exist');\n  }\n  return overlay.dismiss(data, role);\n};\n/**\n * Returns a list of all overlays in the DOM even if they are not presented.\n */\nconst getOverlays = (doc, selector) => {\n  if (selector === undefined) {\n    selector = 'ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast';\n  }\n  return Array.from(doc.querySelectorAll(selector)).filter(c => c.overlayIndex > 0);\n};\n/**\n * Returns a list of all presented overlays.\n * Inline overlays can exist in the DOM but not be presented,\n * so there are times when we want to exclude those.\n * @param doc The document to find the element within.\n * @param overlayTag The selector for the overlay, defaults to Ionic overlay components.\n */\nconst getPresentedOverlays = (doc, overlayTag) => {\n  return getOverlays(doc, overlayTag).filter(o => !isOverlayHidden(o));\n};\n/**\n * Returns a presented overlay element.\n * @param doc The document to find the element within.\n * @param overlayTag The selector for the overlay, defaults to Ionic overlay components.\n * @param id The unique identifier for the overlay instance.\n * @returns The overlay element or `undefined` if no overlay element is found.\n */\nconst getPresentedOverlay = (doc, overlayTag, id) => {\n  const overlays = getPresentedOverlays(doc, overlayTag);\n  return id === undefined ? overlays[overlays.length - 1] : overlays.find(o => o.id === id);\n};\n/**\n * When an overlay is presented, the main\n * focus is the overlay not the page content.\n * We need to remove the page content from the\n * accessibility tree otherwise when\n * users use \"read screen from top\" gestures with\n * TalkBack and VoiceOver, the screen reader will begin\n * to read the content underneath the overlay.\n *\n * We need a container where all page components\n * exist that is separate from where the overlays\n * are added in the DOM. For most apps, this element\n * is the top most ion-router-outlet. In the event\n * that devs are not using a router,\n * they will need to add the \"ion-view-container-root\"\n * id to the element that contains all of their views.\n *\n * TODO: If Framework supports having multiple top\n * level router outlets we would need to update this.\n * Example: One outlet for side menu and one outlet\n * for main content.\n */\nconst setRootAriaHidden = (hidden = false) => {\n  const root = getAppRoot(document);\n  const viewContainer = root.querySelector('ion-router-outlet, ion-nav, #ion-view-container-root');\n  if (!viewContainer) {\n    return;\n  }\n  if (hidden) {\n    viewContainer.setAttribute('aria-hidden', 'true');\n  } else {\n    viewContainer.removeAttribute('aria-hidden');\n  }\n};\nconst present = async (overlay, name, iosEnterAnimation, mdEnterAnimation, opts) => {\n  var _a, _b;\n  if (overlay.presented) {\n    return;\n  }\n  /**\n   * Due to accessibility guidelines, toasts do not have\n   * focus traps.\n   *\n   * All other overlays should have focus traps to prevent\n   * the keyboard focus from leaving the overlay.\n   */\n  if (overlay.el.tagName !== 'ION-TOAST') {\n    setRootAriaHidden(true);\n    document.body.classList.add(BACKDROP_NO_SCROLL);\n  }\n  hideUnderlyingOverlaysFromScreenReaders(overlay.el);\n  hideAnimatingOverlayFromScreenReaders(overlay.el);\n  overlay.presented = true;\n  overlay.willPresent.emit();\n  (_a = overlay.willPresentShorthand) === null || _a === void 0 ? void 0 : _a.emit();\n  const mode = getIonMode(overlay);\n  // get the user's animation fn if one was provided\n  const animationBuilder = overlay.enterAnimation ? overlay.enterAnimation : config.get(name, mode === 'ios' ? iosEnterAnimation : mdEnterAnimation);\n  const completed = await overlayAnimation(overlay, animationBuilder, overlay.el, opts);\n  if (completed) {\n    overlay.didPresent.emit();\n    (_b = overlay.didPresentShorthand) === null || _b === void 0 ? void 0 : _b.emit();\n  }\n  /**\n   * When an overlay that steals focus\n   * is dismissed, focus should be returned\n   * to the element that was focused\n   * prior to the overlay opening. Toast\n   * does not steal focus and is excluded\n   * from returning focus as a result.\n   */\n  if (overlay.el.tagName !== 'ION-TOAST') {\n    restoreElementFocus(overlay.el);\n  }\n  /**\n   * If the focused element is already\n   * inside the overlay component then\n   * focus should not be moved from that\n   * to the overlay container.\n   */\n  if (overlay.keyboardClose && (document.activeElement === null || !overlay.el.contains(document.activeElement))) {\n    overlay.el.focus();\n  }\n  /**\n   * If this overlay was previously dismissed without being\n   * the topmost one (such as by manually calling dismiss()),\n   * it would still have aria-hidden on being presented again.\n   * Removing it here ensures the overlay is visible to screen\n   * readers.\n   *\n   * If this overlay was being presented, then it was hidden\n   * from screen readers during the animation. Now that the\n   * animation is complete, we can reveal the overlay to\n   * screen readers.\n   */\n  overlay.el.removeAttribute('aria-hidden');\n};\n/**\n * When an overlay component is dismissed,\n * focus should be returned to the element\n * that presented the overlay. Otherwise\n * focus will be set on the body which\n * means that people using screen readers\n * or tabbing will need to re-navigate\n * to where they were before they\n * opened the overlay.\n */\nconst restoreElementFocus = async overlayEl => {\n  let previousElement = document.activeElement;\n  if (!previousElement) {\n    return;\n  }\n  const shadowRoot = previousElement === null || previousElement === void 0 ? void 0 : previousElement.shadowRoot;\n  if (shadowRoot) {\n    // If there are no inner focusable elements, just focus the host element.\n    previousElement = shadowRoot.querySelector(focusableQueryString) || previousElement;\n  }\n  await overlayEl.onDidDismiss();\n  /**\n   * After onDidDismiss, the overlay loses focus\n   * because it is removed from the document\n   *\n   * > An element will also lose focus [...]\n   * > if the element is removed from the document)\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/Element/blur_event\n   *\n   * Additionally, `document.activeElement` returns:\n   *\n   * > The Element which currently has focus,\n   * > `<body>` or null if there is\n   * > no focused element.\n   *\n   * https://developer.mozilla.org/en-US/docs/Web/API/Document/activeElement#value\n   *\n   * However, if the user has already focused\n   * an element sometime between onWillDismiss\n   * and onDidDismiss (for example, focusing a\n   * text box after tapping a button in an\n   * action sheet) then don't restore focus to\n   * previous element\n   */\n  if (document.activeElement === null || document.activeElement === document.body) {\n    previousElement.focus();\n  }\n};\nconst dismiss = async (overlay, data, role, name, iosLeaveAnimation, mdLeaveAnimation, opts) => {\n  var _a, _b;\n  if (!overlay.presented) {\n    return false;\n  }\n  const presentedOverlays = doc !== undefined ? getPresentedOverlays(doc) : [];\n  /**\n   * For accessibility, toasts lack focus traps and don't receive\n   * `aria-hidden` on the root element when presented.\n   *\n   * All other overlays use focus traps to keep keyboard focus\n   * within the overlay, setting `aria-hidden` on the root element\n   * to enhance accessibility.\n   *\n   * Therefore, we must remove `aria-hidden` from the root element\n   * when the last non-toast overlay is dismissed.\n   */\n  const overlaysNotToast = presentedOverlays.filter(o => o.tagName !== 'ION-TOAST');\n  const lastOverlayNotToast = overlaysNotToast.length === 1 && overlaysNotToast[0].id === overlay.el.id;\n  /**\n   * If this is the last visible overlay that is not a toast\n   * then we want to re-add the root to the accessibility tree.\n   */\n  if (lastOverlayNotToast) {\n    setRootAriaHidden(false);\n    document.body.classList.remove(BACKDROP_NO_SCROLL);\n  }\n  overlay.presented = false;\n  try {\n    /**\n     * There is no need to show the overlay to screen readers during\n     * the dismiss animation. This is because the overlay will be removed\n     * from the DOM after the animation is complete.\n     */\n    hideAnimatingOverlayFromScreenReaders(overlay.el);\n    // Overlay contents should not be clickable during dismiss\n    overlay.el.style.setProperty('pointer-events', 'none');\n    overlay.willDismiss.emit({\n      data,\n      role\n    });\n    (_a = overlay.willDismissShorthand) === null || _a === void 0 ? void 0 : _a.emit({\n      data,\n      role\n    });\n    const mode = getIonMode(overlay);\n    const animationBuilder = overlay.leaveAnimation ? overlay.leaveAnimation : config.get(name, mode === 'ios' ? iosLeaveAnimation : mdLeaveAnimation);\n    // If dismissed via gesture, no need to play leaving animation again\n    if (role !== GESTURE) {\n      await overlayAnimation(overlay, animationBuilder, overlay.el, opts);\n    }\n    overlay.didDismiss.emit({\n      data,\n      role\n    });\n    (_b = overlay.didDismissShorthand) === null || _b === void 0 ? void 0 : _b.emit({\n      data,\n      role\n    });\n    // Get a reference to all animations currently assigned to this overlay\n    // Then tear them down to return the overlay to its initial visual state\n    const animations = activeAnimations.get(overlay) || [];\n    animations.forEach(ani => ani.destroy());\n    activeAnimations.delete(overlay);\n    /**\n     * Make overlay hidden again in case it is being reused.\n     * We can safely remove pointer-events: none as\n     * overlay-hidden will set display: none.\n     */\n    overlay.el.classList.add('overlay-hidden');\n    overlay.el.style.removeProperty('pointer-events');\n    /**\n     * Clear any focus trapping references\n     * when the overlay is dismissed.\n     */\n    if (overlay.el.lastFocus !== undefined) {\n      overlay.el.lastFocus = undefined;\n    }\n  } catch (err) {\n    printIonError(`[${overlay.el.tagName.toLowerCase()}] - `, err);\n  }\n  overlay.el.remove();\n  revealOverlaysToScreenReaders();\n  return true;\n};\nconst getAppRoot = doc => {\n  return doc.querySelector('ion-app') || doc.body;\n};\nconst overlayAnimation = async (overlay, animationBuilder, baseEl, opts) => {\n  // Make overlay visible in case it's hidden\n  baseEl.classList.remove('overlay-hidden');\n  const aniRoot = overlay.el;\n  const animation = animationBuilder(aniRoot, opts);\n  if (!overlay.animated || !config.getBoolean('animated', true)) {\n    animation.duration(0);\n  }\n  if (overlay.keyboardClose) {\n    animation.beforeAddWrite(() => {\n      const activeElement = baseEl.ownerDocument.activeElement;\n      if (activeElement === null || activeElement === void 0 ? void 0 : activeElement.matches('input,ion-input, ion-textarea')) {\n        activeElement.blur();\n      }\n    });\n  }\n  const activeAni = activeAnimations.get(overlay) || [];\n  activeAnimations.set(overlay, [...activeAni, animation]);\n  await animation.play();\n  return true;\n};\nconst eventMethod = (element, eventName) => {\n  let resolve;\n  const promise = new Promise(r => resolve = r);\n  onceEvent(element, eventName, event => {\n    resolve(event.detail);\n  });\n  return promise;\n};\nconst onceEvent = (element, eventName, callback) => {\n  const handler = ev => {\n    removeEventListener(element, eventName, handler);\n    callback(ev);\n  };\n  addEventListener(element, eventName, handler);\n};\nconst isCancel = role => {\n  return role === 'cancel' || role === BACKDROP;\n};\nconst defaultGate = h => h();\n/**\n * Calls a developer provided method while avoiding\n * Angular Zones. Since the handler is provided by\n * the developer, we should throw any errors\n * received so that developer-provided bug\n * tracking software can log it.\n */\nconst safeCall = (handler, arg) => {\n  if (typeof handler === 'function') {\n    const jmp = config.get('_zoneGate', defaultGate);\n    return jmp(() => {\n      try {\n        return handler(arg);\n      } catch (e) {\n        throw e;\n      }\n    });\n  }\n  return undefined;\n};\nconst BACKDROP = 'backdrop';\nconst GESTURE = 'gesture';\nconst OVERLAY_GESTURE_PRIORITY = 39;\n/**\n * Creates a delegate controller.\n *\n * Requires that the component has the following properties:\n * - `el: HTMLElement`\n * - `hasController: boolean`\n * - `delegate?: FrameworkDelegate`\n *\n * @param ref The component class instance.\n */\nconst createDelegateController = ref => {\n  let inline = false;\n  let workingDelegate;\n  const coreDelegate = CoreDelegate();\n  /**\n   * Determines whether or not an overlay is being used\n   * inline or via a controller/JS and returns the correct delegate.\n   * By default, subsequent calls to getDelegate will use\n   * a cached version of the delegate.\n   * This is useful for calling dismiss after present,\n   * so that the correct delegate is given.\n   * @param force `true` to force the non-cached version of the delegate.\n   * @returns The delegate to use and whether or not the overlay is inline.\n   */\n  const getDelegate = (force = false) => {\n    if (workingDelegate && !force) {\n      return {\n        delegate: workingDelegate,\n        inline\n      };\n    }\n    const {\n      el,\n      hasController,\n      delegate\n    } = ref;\n    /**\n     * If using overlay inline\n     * we potentially need to use the coreDelegate\n     * so that this works in vanilla JS apps.\n     * If a developer has presented this component\n     * via a controller, then we can assume\n     * the component is already in the\n     * correct place.\n     */\n    const parentEl = el.parentNode;\n    inline = parentEl !== null && !hasController;\n    workingDelegate = inline ? delegate || coreDelegate : delegate;\n    return {\n      inline,\n      delegate: workingDelegate\n    };\n  };\n  /**\n   * Attaches a component in the DOM. Teleports the component\n   * to the root of the app.\n   * @param component The component to optionally construct and append to the element.\n   */\n  const attachViewToDom = async component => {\n    const {\n      delegate\n    } = getDelegate(true);\n    if (delegate) {\n      return await delegate.attachViewToDom(ref.el, component);\n    }\n    const {\n      hasController\n    } = ref;\n    if (hasController && component !== undefined) {\n      throw new Error('framework delegate is missing');\n    }\n    return null;\n  };\n  /**\n   * Moves a component back to its original location in the DOM.\n   */\n  const removeViewFromDom = () => {\n    const {\n      delegate\n    } = getDelegate();\n    if (delegate && ref.el !== undefined) {\n      delegate.removeViewFromDom(ref.el.parentElement, ref.el);\n    }\n  };\n  return {\n    attachViewToDom,\n    removeViewFromDom\n  };\n};\n/**\n * Constructs a trigger interaction for an overlay.\n * Presents an overlay when the trigger is clicked.\n *\n * Usage:\n * ```ts\n * triggerController = createTriggerController();\n * triggerController.addClickListener(el, trigger);\n * ```\n */\nconst createTriggerController = () => {\n  let destroyTriggerInteraction;\n  /**\n   * Removes the click listener from the trigger element.\n   */\n  const removeClickListener = () => {\n    if (destroyTriggerInteraction) {\n      destroyTriggerInteraction();\n      destroyTriggerInteraction = undefined;\n    }\n  };\n  /**\n   * Adds a click listener to the trigger element.\n   * Presents the overlay when the trigger is clicked.\n   * @param el The overlay element.\n   * @param trigger The ID of the element to add a click listener to.\n   */\n  const addClickListener = (el, trigger) => {\n    removeClickListener();\n    const triggerEl = trigger !== undefined ? document.getElementById(trigger) : null;\n    if (!triggerEl) {\n      printIonWarning(`[${el.tagName.toLowerCase()}] - A trigger element with the ID \"${trigger}\" was not found in the DOM. The trigger element must be in the DOM when the \"trigger\" property is set on an overlay component.`, el);\n      return;\n    }\n    const configureTriggerInteraction = (targetEl, overlayEl) => {\n      const openOverlay = () => {\n        overlayEl.present();\n      };\n      targetEl.addEventListener('click', openOverlay);\n      return () => {\n        targetEl.removeEventListener('click', openOverlay);\n      };\n    };\n    destroyTriggerInteraction = configureTriggerInteraction(triggerEl, el);\n  };\n  return {\n    addClickListener,\n    removeClickListener\n  };\n};\n/**\n * The overlay that is being animated also needs to hide from screen\n * readers during its animation. This ensures that assistive technologies\n * like TalkBack do not announce or interact with the content until the\n * animation is complete, avoiding confusion for users.\n *\n * When the overlay is presented on an Android device, TalkBack's focus rings\n * may appear in the wrong position due to the transition (specifically\n * `transform` styles). This occurs because the focus rings are initially\n * displayed at the starting position of the elements before the transition\n * begins. This workaround ensures the focus rings do not appear in the\n * incorrect location.\n *\n * If this solution is applied to iOS devices, then it leads to a bug where\n * the overlays cannot be accessed by screen readers. This is due to\n * VoiceOver not being able to update the accessibility tree when the\n * `aria-hidden` is removed.\n *\n * @param overlay - The overlay that is being animated.\n */\nconst hideAnimatingOverlayFromScreenReaders = overlay => {\n  if (doc === undefined) return;\n  if (isPlatform('android')) {\n    /**\n     * Once the animation is complete, this attribute will be removed.\n     * This is done at the end of the `present` method.\n     */\n    overlay.setAttribute('aria-hidden', 'true');\n  }\n};\n/**\n * Ensure that underlying overlays have aria-hidden if necessary so that screen readers\n * cannot move focus to these elements. Note that we cannot rely on focus/focusin/focusout\n * events here because those events do not fire when the screen readers moves to a non-focusable\n * element such as text.\n * Without this logic screen readers would be able to move focus outside of the top focus-trapped overlay.\n *\n * @param newTopMostOverlay - The overlay that is being presented. Since the overlay has not been\n * fully presented yet at the time this function is called it will not be included in the getPresentedOverlays result.\n */\nconst hideUnderlyingOverlaysFromScreenReaders = newTopMostOverlay => {\n  var _a;\n  if (doc === undefined) return;\n  const overlays = getPresentedOverlays(doc);\n  for (let i = overlays.length - 1; i >= 0; i--) {\n    const presentedOverlay = overlays[i];\n    const nextPresentedOverlay = (_a = overlays[i + 1]) !== null && _a !== void 0 ? _a : newTopMostOverlay;\n    /**\n     * If next overlay has aria-hidden then all remaining overlays will have it too.\n     * Or, if the next overlay is a Toast that does not have aria-hidden then current overlay\n     * should not have aria-hidden either so focus can remain in the current overlay.\n     */\n    if (nextPresentedOverlay.hasAttribute('aria-hidden') || nextPresentedOverlay.tagName !== 'ION-TOAST') {\n      presentedOverlay.setAttribute('aria-hidden', 'true');\n    }\n  }\n};\n/**\n * When dismissing an overlay we need to reveal the new top-most overlay to screen readers.\n * If the top-most overlay is a Toast we potentially need to reveal more overlays since\n * focus is never automatically moved to the Toast.\n */\nconst revealOverlaysToScreenReaders = () => {\n  if (doc === undefined) return;\n  const overlays = getPresentedOverlays(doc);\n  for (let i = overlays.length - 1; i >= 0; i--) {\n    const currentOverlay = overlays[i];\n    /**\n     * If the current we are looking at is a Toast then we can remove aria-hidden.\n     * However, we potentially need to keep looking at the overlay stack because there\n     * could be more Toasts underneath. Additionally, we need to unhide the closest non-Toast\n     * overlay too so focus can move there since focus is never automatically moved to the Toast.\n     */\n    currentOverlay.removeAttribute('aria-hidden');\n    /**\n     * If we found a non-Toast element then we can just remove aria-hidden and stop searching entirely\n     * since this overlay should always receive focus. As a result, all underlying overlays should still\n     * be hidden from screen readers.\n     */\n    if (currentOverlay.tagName !== 'ION-TOAST') {\n      break;\n    }\n  }\n};\nconst FOCUS_TRAP_DISABLE_CLASS = 'ion-disable-focus-trap';\nexport { BACKDROP as B, FOCUS_TRAP_DISABLE_CLASS as F, GESTURE as G, OVERLAY_GESTURE_PRIORITY as O, alertController as a, actionSheetController as b, popoverController as c, createDelegateController as d, createTriggerController as e, present as f, dismiss as g, eventMethod as h, isCancel as i, prepareOverlay as j, setOverlayId as k, loadingController as l, modalController as m, focusFirstDescendant as n, getPresentedOverlay as o, pickerController as p, focusLastDescendant as q, safeCall as s, toastController as t };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAuBM,sBAWA,sBAcA,qBAsBA,uBAmBF,kBACA,QACE,kBACA,kBAaA,iBACA,uBACA,mBACA,iBAIA,kBACA,mBACA,iBAIA,gBA2BA,cAMA,eAoBA,iBAcA,uBAqBA,mBA4KA,kBA6CA,gBAUA,aAaA,sBAUA,qBA0BA,mBAYA,SAyEA,qBAuCA,SAqFA,YAGA,kBAqBA,aAQA,WAOA,UAGA,aAQA,UAaA,UACA,SACA,0BAWA,0BAyFA,yBA4DA,uCAoBA,yCAsBA,+BAsBA;AAp9BN;AAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAcA,IAAM,uBAAuB;AAW7B,IAAM,uBAAuB,CAAC,KAAK,oBAAoB;AACrD,YAAM,aAAa,IAAI,cAAc,oBAAoB;AACzD,4BAAsB,YAAY,oBAAoB,QAAQ,oBAAoB,SAAS,kBAAkB,GAAG;AAAA,IAClH;AAWA,IAAM,sBAAsB,CAAC,KAAK,oBAAoB;AACpD,YAAM,SAAS,MAAM,KAAK,IAAI,iBAAiB,oBAAoB,CAAC;AACpE,YAAM,YAAY,OAAO,SAAS,IAAI,OAAO,OAAO,SAAS,CAAC,IAAI;AAClE,4BAAsB,WAAW,oBAAoB,QAAQ,oBAAoB,SAAS,kBAAkB,GAAG;AAAA,IACjH;AAkBA,IAAM,wBAAwB,CAAC,aAAa,oBAAoB;AAC9D,UAAI,iBAAiB;AACrB,YAAM,aAAa,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY;AACzF,UAAI,YAAY;AAEd,yBAAiB,WAAW,cAAc,oBAAoB,KAAK;AAAA,MACrE;AACA,UAAI,gBAAgB;AAClB,cAAM,aAAa,eAAe,QAAQ,iBAAiB;AAC3D,YAAI,YAAY;AACd,qBAAW,SAAS;AAAA,QACtB,OAAO;AACL,8BAAoB,cAAc;AAAA,QACpC;AAAA,MACF,OAAO;AAEL,wBAAgB,MAAM;AAAA,MACxB;AAAA,IACF;AACA,IAAI,mBAAmB;AACvB,IAAI,SAAS;AACb,IAAM,mBAAmB,oBAAI,QAAQ;AACrC,IAAM,mBAAmB,aAAW;AAClC,aAAO;AAAA,QACL,OAAO,SAAS;AACd,iBAAO,cAAc,SAAS,OAAO;AAAA,QACvC;AAAA,QACA,QAAQ,MAAM,MAAM,IAAI;AACtB,iBAAO,eAAe,UAAU,MAAM,MAAM,SAAS,EAAE;AAAA,QACzD;AAAA,QACM,SAAS;AAAA;AACb,mBAAO,oBAAoB,UAAU,OAAO;AAAA,UAC9C;AAAA;AAAA,MACF;AAAA,IACF;AACA,IAAM,kBAA+B,iCAAiB,WAAW;AACjE,IAAM,wBAAqC,iCAAiB,kBAAkB;AAC9E,IAAM,oBAAiC,iCAAiB,aAAa;AACrE,IAAM,kBAA+B,iCAAiB,WAAW;AAIjE,IAAM,mBAAgC,iCAAiB,mBAAmB;AAC1E,IAAM,oBAAiC,iCAAiB,aAAa;AACrE,IAAM,kBAA+B,iCAAiB,WAAW;AAIjE,IAAM,iBAAiB,QAAM;AAC3B,UAAI,OAAO,aAAa,aAAa;AAUnC,yBAAiB,QAAQ;AAAA,MAC3B;AACA,YAAM,eAAe;AAMrB,SAAG,eAAe;AAAA,IACpB;AAOA,IAAM,eAAe,QAAM;AACzB,UAAI,CAAC,GAAG,aAAa,IAAI,GAAG;AAC1B,WAAG,KAAK,eAAe,EAAE,MAAM;AAAA,MACjC;AACA,aAAO,GAAG;AAAA,IACZ;AACA,IAAM,gBAAgB,CAAC,SAAS,SAAS;AAEvC,UAAI,OAAO,WAAW,eAAe,OAAO,OAAO,mBAAmB,aAAa;AACjF,eAAO,OAAO,eAAe,YAAY,OAAO,EAAE,KAAK,MAAM;AAC3D,gBAAM,UAAU,SAAS,cAAc,OAAO;AAC9C,kBAAQ,UAAU,IAAI,gBAAgB;AAKtC,iBAAO,OAAO,SAAS,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG;AAAA,YAC5D,eAAe;AAAA,UACjB,CAAC,CAAC;AAEF,qBAAW,QAAQ,EAAE,YAAY,OAAO;AACxC,iBAAO,IAAI,QAAQ,aAAW,iBAAiB,SAAS,OAAO,CAAC;AAAA,QAClE,CAAC;AAAA,MACH;AACA,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,IAAM,kBAAkB,aAAW,QAAQ,UAAU,SAAS,gBAAgB;AAc9E,IAAM,wBAAwB,CAAC,aAAa,YAAY;AACtD,UAAI,iBAAiB;AACrB,YAAM,aAAa,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY;AACzF,UAAI,YAAY;AAEd,yBAAiB,WAAW,cAAc,oBAAoB,KAAK;AAAA,MACrE;AACA,UAAI,gBAAgB;AAClB,4BAAoB,cAAc;AAAA,MACpC,OAAO;AAEL,gBAAQ,MAAM;AAAA,MAChB;AAAA,IACF;AAQA,IAAM,oBAAoB,CAAC,IAAIA,SAAQ;AACrC,YAAM,cAAc,oBAAoBA,MAAK,gFAAgF;AAC7H,YAAM,SAAS,GAAG;AAWlB,UAAI,CAAC,eAAe,CAAC,QAAQ;AAC3B;AAAA,MACF;AAUA,UAAI,YAAY,UAAU,SAAS,wBAAwB,GAAG;AAC5D;AAAA,MACF;AACA,YAAM,kBAAkB,MAAM;AAO5B,YAAI,gBAAgB,QAAQ;AAC1B,sBAAY,YAAY;AAAA,QAa1B,WAAW,OAAO,YAAY,aAAa;AACzC,gCAAsB,YAAY,WAAW,WAAW;AAAA,QAS1D,OAAO;AAKL,gBAAM,cAAc,eAAe,WAAW;AAC9C,cAAI,CAAC,YAAY,SAAS,MAAM,GAAG;AACjC;AAAA,UACF;AACA,gBAAM,iBAAiB,YAAY,cAAc,sBAAsB;AACvE,cAAI,CAAC,gBAAgB;AACnB;AAAA,UACF;AAQA,cAAI,eAAe,SAAS,MAAM,KAAK,WAAW,YAAY,cAAc,cAAc,GAAG;AAC3F,wBAAY,YAAY;AAAA,UAC1B,OAAO;AAaL,kBAAM,YAAY,YAAY;AAE9B,iCAAqB,gBAAgB,WAAW;AAUhD,gBAAI,cAAcA,KAAI,eAAe;AACnC,kCAAoB,gBAAgB,WAAW;AAAA,YACjD;AACA,wBAAY,YAAYA,KAAI;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AACA,YAAM,kBAAkB,MAAM;AAK5B,YAAI,YAAY,SAAS,MAAM,GAAG;AAChC,sBAAY,YAAY;AAAA,QAa1B,WAAW,OAAO,YAAY,aAAa;AACzC,gCAAsB,YAAY,WAAW,WAAW;AAAA,QAC1D,OAAO;AAcL,gBAAM,YAAY,YAAY;AAE9B,+BAAqB,WAAW;AAUhC,cAAI,cAAcA,KAAI,eAAe;AACnC,gCAAoB,WAAW;AAAA,UACjC;AACA,sBAAY,YAAYA,KAAI;AAAA,QAC9B;AAAA,MACF;AACA,UAAI,YAAY,YAAY;AAC1B,wBAAgB;AAAA,MAClB,OAAO;AACL,wBAAgB;AAAA,MAClB;AAAA,IACF;AACA,IAAM,mBAAmB,CAAAA,SAAO;AAC9B,UAAI,qBAAqB,GAAG;AAC1B,2BAAmB;AACnB,QAAAA,KAAI,iBAAiB,SAAS,QAAM;AAClC,4BAAkB,IAAIA,IAAG;AAAA,QAC3B,GAAG,IAAI;AAEP,QAAAA,KAAI,iBAAiB,iBAAiB,QAAM;AAC1C,gBAAM,cAAc,oBAAoBA,IAAG;AAC3C,cAAI,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,iBAAiB;AACzF,eAAG,OAAO,SAAS,8BAA8B,MAAM;AAYrD,0BAAY,QAAQ,QAAW,QAAQ;AAAA,YACzC,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAQD,YAAI,CAAC,sBAAsB,GAAG;AAC5B,UAAAA,KAAI,iBAAiB,WAAW,QAAM;AACpC,gBAAI,GAAG,QAAQ,UAAU;AACvB,oBAAM,cAAc,oBAAoBA,IAAG;AAC3C,kBAAI,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,iBAAiB;AACzF,4BAAY,QAAQ,QAAW,QAAQ;AAAA,cACzC;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AACA,IAAM,iBAAiB,CAACA,MAAK,MAAM,MAAM,YAAY,OAAO;AAC1D,YAAM,UAAU,oBAAoBA,MAAK,YAAY,EAAE;AACvD,UAAI,CAAC,SAAS;AACZ,eAAO,QAAQ,OAAO,wBAAwB;AAAA,MAChD;AACA,aAAO,QAAQ,QAAQ,MAAM,IAAI;AAAA,IACnC;AAIA,IAAM,cAAc,CAACA,MAAK,aAAa;AACrC,UAAI,aAAa,QAAW;AAC1B,mBAAW;AAAA,MACb;AACA,aAAO,MAAM,KAAKA,KAAI,iBAAiB,QAAQ,CAAC,EAAE,OAAO,OAAK,EAAE,eAAe,CAAC;AAAA,IAClF;AAQA,IAAM,uBAAuB,CAACA,MAAK,eAAe;AAChD,aAAO,YAAYA,MAAK,UAAU,EAAE,OAAO,OAAK,CAAC,gBAAgB,CAAC,CAAC;AAAA,IACrE;AAQA,IAAM,sBAAsB,CAACA,MAAK,YAAY,OAAO;AACnD,YAAM,WAAW,qBAAqBA,MAAK,UAAU;AACrD,aAAO,OAAO,SAAY,SAAS,SAAS,SAAS,CAAC,IAAI,SAAS,KAAK,OAAK,EAAE,OAAO,EAAE;AAAA,IAC1F;AAuBA,IAAM,oBAAoB,CAAC,SAAS,UAAU;AAC5C,YAAM,OAAO,WAAW,QAAQ;AAChC,YAAM,gBAAgB,KAAK,cAAc,sDAAsD;AAC/F,UAAI,CAAC,eAAe;AAClB;AAAA,MACF;AACA,UAAI,QAAQ;AACV,sBAAc,aAAa,eAAe,MAAM;AAAA,MAClD,OAAO;AACL,sBAAc,gBAAgB,aAAa;AAAA,MAC7C;AAAA,IACF;AACA,IAAM,UAAU,CAAO,SAAS,MAAM,mBAAmB,kBAAkB,SAAS;AAClF,UAAI,IAAI;AACR,UAAI,QAAQ,WAAW;AACrB;AAAA,MACF;AAQA,UAAI,QAAQ,GAAG,YAAY,aAAa;AACtC,0BAAkB,IAAI;AACtB,iBAAS,KAAK,UAAU,IAAI,kBAAkB;AAAA,MAChD;AACA,8CAAwC,QAAQ,EAAE;AAClD,4CAAsC,QAAQ,EAAE;AAChD,cAAQ,YAAY;AACpB,cAAQ,YAAY,KAAK;AACzB,OAAC,KAAK,QAAQ,0BAA0B,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AACjF,YAAM,OAAO,WAAW,OAAO;AAE/B,YAAM,mBAAmB,QAAQ,iBAAiB,QAAQ,iBAAiB,OAAO,IAAI,MAAM,SAAS,QAAQ,oBAAoB,gBAAgB;AACjJ,YAAM,YAAY,MAAM,iBAAiB,SAAS,kBAAkB,QAAQ,IAAI,IAAI;AACpF,UAAI,WAAW;AACb,gBAAQ,WAAW,KAAK;AACxB,SAAC,KAAK,QAAQ,yBAAyB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAAA,MAClF;AASA,UAAI,QAAQ,GAAG,YAAY,aAAa;AACtC,4BAAoB,QAAQ,EAAE;AAAA,MAChC;AAOA,UAAI,QAAQ,kBAAkB,SAAS,kBAAkB,QAAQ,CAAC,QAAQ,GAAG,SAAS,SAAS,aAAa,IAAI;AAC9G,gBAAQ,GAAG,MAAM;AAAA,MACnB;AAaA,cAAQ,GAAG,gBAAgB,aAAa;AAAA,IAC1C;AAWA,IAAM,sBAAsB,CAAM,cAAa;AAC7C,UAAI,kBAAkB,SAAS;AAC/B,UAAI,CAAC,iBAAiB;AACpB;AAAA,MACF;AACA,YAAM,aAAa,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB;AACrG,UAAI,YAAY;AAEd,0BAAkB,WAAW,cAAc,oBAAoB,KAAK;AAAA,MACtE;AACA,YAAM,UAAU,aAAa;AAyB7B,UAAI,SAAS,kBAAkB,QAAQ,SAAS,kBAAkB,SAAS,MAAM;AAC/E,wBAAgB,MAAM;AAAA,MACxB;AAAA,IACF;AACA,IAAM,UAAU,CAAO,SAAS,MAAM,MAAM,MAAM,mBAAmB,kBAAkB,SAAS;AAC9F,UAAI,IAAI;AACR,UAAI,CAAC,QAAQ,WAAW;AACtB,eAAO;AAAA,MACT;AACA,YAAM,oBAAoB,QAAQ,SAAY,qBAAqB,GAAG,IAAI,CAAC;AAY3E,YAAM,mBAAmB,kBAAkB,OAAO,OAAK,EAAE,YAAY,WAAW;AAChF,YAAM,sBAAsB,iBAAiB,WAAW,KAAK,iBAAiB,CAAC,EAAE,OAAO,QAAQ,GAAG;AAKnG,UAAI,qBAAqB;AACvB,0BAAkB,KAAK;AACvB,iBAAS,KAAK,UAAU,OAAO,kBAAkB;AAAA,MACnD;AACA,cAAQ,YAAY;AACpB,UAAI;AAMF,8CAAsC,QAAQ,EAAE;AAEhD,gBAAQ,GAAG,MAAM,YAAY,kBAAkB,MAAM;AACrD,gBAAQ,YAAY,KAAK;AAAA,UACvB;AAAA,UACA;AAAA,QACF,CAAC;AACD,SAAC,KAAK,QAAQ,0BAA0B,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAAA,UAC/E;AAAA,UACA;AAAA,QACF,CAAC;AACD,cAAM,OAAO,WAAW,OAAO;AAC/B,cAAM,mBAAmB,QAAQ,iBAAiB,QAAQ,iBAAiB,OAAO,IAAI,MAAM,SAAS,QAAQ,oBAAoB,gBAAgB;AAEjJ,YAAI,SAAS,SAAS;AACpB,gBAAM,iBAAiB,SAAS,kBAAkB,QAAQ,IAAI,IAAI;AAAA,QACpE;AACA,gBAAQ,WAAW,KAAK;AAAA,UACtB;AAAA,UACA;AAAA,QACF,CAAC;AACD,SAAC,KAAK,QAAQ,yBAAyB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAAA,UAC9E;AAAA,UACA;AAAA,QACF,CAAC;AAGD,cAAM,aAAa,iBAAiB,IAAI,OAAO,KAAK,CAAC;AACrD,mBAAW,QAAQ,SAAO,IAAI,QAAQ,CAAC;AACvC,yBAAiB,OAAO,OAAO;AAM/B,gBAAQ,GAAG,UAAU,IAAI,gBAAgB;AACzC,gBAAQ,GAAG,MAAM,eAAe,gBAAgB;AAKhD,YAAI,QAAQ,GAAG,cAAc,QAAW;AACtC,kBAAQ,GAAG,YAAY;AAAA,QACzB;AAAA,MACF,SAAS,KAAK;AACZ,sBAAc,IAAI,QAAQ,GAAG,QAAQ,YAAY,CAAC,QAAQ,GAAG;AAAA,MAC/D;AACA,cAAQ,GAAG,OAAO;AAClB,oCAA8B;AAC9B,aAAO;AAAA,IACT;AACA,IAAM,aAAa,CAAAA,SAAO;AACxB,aAAOA,KAAI,cAAc,SAAS,KAAKA,KAAI;AAAA,IAC7C;AACA,IAAM,mBAAmB,CAAO,SAAS,kBAAkB,QAAQ,SAAS;AAE1E,aAAO,UAAU,OAAO,gBAAgB;AACxC,YAAM,UAAU,QAAQ;AACxB,YAAM,YAAY,iBAAiB,SAAS,IAAI;AAChD,UAAI,CAAC,QAAQ,YAAY,CAAC,OAAO,WAAW,YAAY,IAAI,GAAG;AAC7D,kBAAU,SAAS,CAAC;AAAA,MACtB;AACA,UAAI,QAAQ,eAAe;AACzB,kBAAU,eAAe,MAAM;AAC7B,gBAAM,gBAAgB,OAAO,cAAc;AAC3C,cAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,QAAQ,+BAA+B,GAAG;AACxH,0BAAc,KAAK;AAAA,UACrB;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,YAAY,iBAAiB,IAAI,OAAO,KAAK,CAAC;AACpD,uBAAiB,IAAI,SAAS,CAAC,GAAG,WAAW,SAAS,CAAC;AACvD,YAAM,UAAU,KAAK;AACrB,aAAO;AAAA,IACT;AACA,IAAM,cAAc,CAAC,SAAS,cAAc;AAC1C,UAAI;AACJ,YAAM,UAAU,IAAI,QAAQ,OAAK,UAAU,CAAC;AAC5C,gBAAU,SAAS,WAAW,WAAS;AACrC,gBAAQ,MAAM,MAAM;AAAA,MACtB,CAAC;AACD,aAAO;AAAA,IACT;AACA,IAAM,YAAY,CAAC,SAAS,WAAW,aAAa;AAClD,YAAM,UAAU,QAAM;AACpB,4BAAoB,SAAS,WAAW,OAAO;AAC/C,iBAAS,EAAE;AAAA,MACb;AACA,uBAAiB,SAAS,WAAW,OAAO;AAAA,IAC9C;AACA,IAAM,WAAW,UAAQ;AACvB,aAAO,SAAS,YAAY,SAAS;AAAA,IACvC;AACA,IAAM,cAAc,OAAK,EAAE;AAQ3B,IAAM,WAAW,CAAC,SAAS,QAAQ;AACjC,UAAI,OAAO,YAAY,YAAY;AACjC,cAAM,MAAM,OAAO,IAAI,aAAa,WAAW;AAC/C,eAAO,IAAI,MAAM;AACf,cAAI;AACF,mBAAO,QAAQ,GAAG;AAAA,UACpB,SAAS,GAAG;AACV,kBAAM;AAAA,UACR;AAAA,QACF,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AACA,IAAM,WAAW;AACjB,IAAM,UAAU;AAChB,IAAM,2BAA2B;AAWjC,IAAM,2BAA2B,SAAO;AACtC,UAAI,SAAS;AACb,UAAI;AACJ,YAAM,eAAe,aAAa;AAWlC,YAAM,cAAc,CAAC,QAAQ,UAAU;AACrC,YAAI,mBAAmB,CAAC,OAAO;AAC7B,iBAAO;AAAA,YACL,UAAU;AAAA,YACV;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AAUJ,cAAM,WAAW,GAAG;AACpB,iBAAS,aAAa,QAAQ,CAAC;AAC/B,0BAAkB,SAAS,YAAY,eAAe;AACtD,eAAO;AAAA,UACL;AAAA,UACA,UAAU;AAAA,QACZ;AAAA,MACF;AAMA,YAAM,kBAAkB,CAAM,cAAa;AACzC,cAAM;AAAA,UACJ;AAAA,QACF,IAAI,YAAY,IAAI;AACpB,YAAI,UAAU;AACZ,iBAAO,MAAM,SAAS,gBAAgB,IAAI,IAAI,SAAS;AAAA,QACzD;AACA,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,YAAI,iBAAiB,cAAc,QAAW;AAC5C,gBAAM,IAAI,MAAM,+BAA+B;AAAA,QACjD;AACA,eAAO;AAAA,MACT;AAIA,YAAM,oBAAoB,MAAM;AAC9B,cAAM;AAAA,UACJ;AAAA,QACF,IAAI,YAAY;AAChB,YAAI,YAAY,IAAI,OAAO,QAAW;AACpC,mBAAS,kBAAkB,IAAI,GAAG,eAAe,IAAI,EAAE;AAAA,QACzD;AAAA,MACF;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAWA,IAAM,0BAA0B,MAAM;AACpC,UAAI;AAIJ,YAAM,sBAAsB,MAAM;AAChC,YAAI,2BAA2B;AAC7B,oCAA0B;AAC1B,sCAA4B;AAAA,QAC9B;AAAA,MACF;AAOA,YAAM,mBAAmB,CAAC,IAAI,YAAY;AACxC,4BAAoB;AACpB,cAAM,YAAY,YAAY,SAAY,SAAS,eAAe,OAAO,IAAI;AAC7E,YAAI,CAAC,WAAW;AACd,0BAAgB,IAAI,GAAG,QAAQ,YAAY,CAAC,sCAAsC,OAAO,kIAAkI,EAAE;AAC7N;AAAA,QACF;AACA,cAAM,8BAA8B,CAAC,UAAU,cAAc;AAC3D,gBAAM,cAAc,MAAM;AACxB,sBAAU,QAAQ;AAAA,UACpB;AACA,mBAAS,iBAAiB,SAAS,WAAW;AAC9C,iBAAO,MAAM;AACX,qBAAS,oBAAoB,SAAS,WAAW;AAAA,UACnD;AAAA,QACF;AACA,oCAA4B,4BAA4B,WAAW,EAAE;AAAA,MACvE;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAqBA,IAAM,wCAAwC,aAAW;AACvD,UAAI,QAAQ,OAAW;AACvB,UAAI,WAAW,SAAS,GAAG;AAKzB,gBAAQ,aAAa,eAAe,MAAM;AAAA,MAC5C;AAAA,IACF;AAWA,IAAM,0CAA0C,uBAAqB;AACnE,UAAI;AACJ,UAAI,QAAQ,OAAW;AACvB,YAAM,WAAW,qBAAqB,GAAG;AACzC,eAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,cAAM,mBAAmB,SAAS,CAAC;AACnC,cAAM,wBAAwB,KAAK,SAAS,IAAI,CAAC,OAAO,QAAQ,OAAO,SAAS,KAAK;AAMrF,YAAI,qBAAqB,aAAa,aAAa,KAAK,qBAAqB,YAAY,aAAa;AACpG,2BAAiB,aAAa,eAAe,MAAM;AAAA,QACrD;AAAA,MACF;AAAA,IACF;AAMA,IAAM,gCAAgC,MAAM;AAC1C,UAAI,QAAQ,OAAW;AACvB,YAAM,WAAW,qBAAqB,GAAG;AACzC,eAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,cAAM,iBAAiB,SAAS,CAAC;AAOjC,uBAAe,gBAAgB,aAAa;AAM5C,YAAI,eAAe,YAAY,aAAa;AAC1C;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,IAAM,2BAA2B;AAAA;AAAA;", "names": ["doc"], "x_google_ignoreList": [0]}