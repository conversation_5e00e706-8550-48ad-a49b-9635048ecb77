﻿using Eventing.EventBus;
using GoodDriver.SetupDomain.Cqrs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.SetupDomain.Cqrs.Domain
{
    public abstract class AggregateRoot<TKey> : IAggregateRoot<TKey>, IEntity<TKey>, IDomainEventRoot, ICloneable
    {
        private readonly List<IDomainEvent> uncommittedEvents = new List<IDomainEvent>();

        private IRouteDomainEvents<TKey> registeredRoutes;

        public virtual TKey Id { get; set; }

        protected virtual IRouteDomainEvents<TKey> RegisteredRoutes
        {
            get
            {
                return registeredRoutes ?? (registeredRoutes = new ConventionDomainEventRouter<TKey>(throwOnApplyNotFound: true, this));
            }
            set
            {
                if (value == null)
                {
                    throw new InvalidOperationException("AggregateBase must have an event router to function");
                }

                registeredRoutes = value;
            }
        }

        protected AggregateRoot()
        {
        }

        protected AggregateRoot(TKey id)
        {
            if (id == null)
            {
                throw new ArgumentException("An aggregate root id can not be empty");
            }

            Id = id;
        }

        protected AggregateRoot(IRouteDomainEvents<TKey> handler)
        {
            if (handler != null)
            {
                RegisteredRoutes = handler;
                RegisteredRoutes.Register(this);
            }
        }

        protected AggregateRoot(IRouteDomainEvents<TKey> handler, TKey id)
            : this(id)
        {
            if (handler != null)
            {
                RegisteredRoutes = handler;
                RegisteredRoutes.Register(this);
            }
        }

        public virtual void LoadsFromHistory(IEnumerable<IDomainEvent> history)
        {
            foreach (IDomainEvent item in history)
            {
                ((IDomainEventRoot)this).ApplyEvent(item);
            }
        }

        public virtual void Register<T>(Action<T> route)
        {
            RegisteredRoutes.Register(route);
        }

        public virtual void AddEvent(IDomainEvent @event)
        {
            if (@event == null)
            {
                throw new ArgumentNullException("event");
            }

            uncommittedEvents.Add(@event);
        }

        protected virtual void RaiseEvent(IDomainEvent @event)
        {
            if (@event == null)
            {
                throw new ArgumentNullException("event");
            }

            ((IDomainEventRoot)this).ApplyEvent(@event);
            uncommittedEvents.Add(@event);
        }

        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }

        public override bool Equals(object obj)
        {
            return Equals(obj as IAggregateRoot<TKey>);
        }

        public virtual bool Equals(IAggregateRoot<TKey> other)
        {
            if (other != null && other.Id != null)
            {
                return other.Id.Equals(Id);
            }

            return false;
        }

        public virtual object Clone()
        {
            return MemberwiseClone();
        }

        public virtual IEnumerable<IDomainEvent> GetUncommittedEvents()
        {
            return uncommittedEvents;
        }

        public virtual void ClearUncommittedEvents()
        {
            uncommittedEvents.Clear();
        }

        public virtual void ApplyEvent(IDomainEvent @event)
        {
            RegisteredRoutes.Dispatch(@event);
        }
    }
}
