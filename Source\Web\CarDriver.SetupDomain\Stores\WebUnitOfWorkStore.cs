﻿using GoodDriver.SetupDomain.Common;
using GoodDriver.SetupDomain.Data;
using GoodDriver.SetupDomain.UnitOfWork;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.SetupDomain.Stores
{
    public class WebUnitOfWorkStore : IUnitOfWorkStore
    {
        private IHttpContextAccessor httpContextAccessor;

        public WebUnitOfWorkStore(IHttpContextAccessor httpContextAccessor)
        {
            if (httpContextAccessor == null)
            {
                throw new ArgumentNullException("httpContextAccessor");
            }

            this.httpContextAccessor = httpContextAccessor;
        }

        public void Store(string alias, IUnitOfWork unitOfWork)
        {
            if (unitOfWork == null)
            {
                throw new ArgumentNullException("unitOfWork");
            }

            if (string.IsNullOrEmpty(alias))
            {
                throw new ArgumentNullException("alias");
            }

            HttpContext httpContext = httpContextAccessor.HttpContext;
            if (httpContext != null)
            {
                httpContext.Items[alias] = unitOfWork;
            }
            else
            {
                CallContext.SetData(alias, unitOfWork);
            }
        }

        public void Remove(string alias)
        {
            if (string.IsNullOrEmpty(alias))
            {
                throw new ArgumentNullException("alias");
            }

            HttpContext httpContext = httpContextAccessor.HttpContext;
            if (httpContext != null)
            {
                httpContext.Items.Remove(alias);
            }
            else
            {
                CallContext.SetData(alias, null);
            }
        }

        public IUnitOfWork Get(string alias)
        {
            if (string.IsNullOrEmpty(alias))
            {
                throw new ArgumentNullException("alias");
            }

            HttpContext httpContext = httpContextAccessor.HttpContext;
            if (httpContext != null)
            {
                return httpContext.Items[alias] as IUnitOfWork;
            }

            return CallContext.GetData(alias) as IUnitOfWork;
        }
    }
}
