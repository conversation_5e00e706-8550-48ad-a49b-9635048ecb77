import { Component, OnInit, OnDestroy } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { SyncService } from 'src/app/core/services/sync.service';
import { NetworkService } from 'src/app/core/services/network.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-sync-status',
  templateUrl: './sync-status.component.html',
  styleUrls: ['./sync-status.component.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule]
})
export class SyncStatusComponent implements OnInit, OnDestroy {
  isOnline = true;
  syncInProgress = false;
  pendingSyncCount = 0;
  lastSyncTime: Date | null = null;
  
  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    private syncService: SyncService,
    private networkService: NetworkService
  ) {}

  ngOnInit() {
    // Subscribe to network status
    this.subscriptions.push(
      this.networkService.getOnlineStatus().subscribe(isOnline => {
        this.isOnline = isOnline;
      })
    );
    
    // Subscribe to sync status
    this.subscriptions.push(
      this.syncService.syncInProgress$.subscribe(inProgress => {
        this.syncInProgress = inProgress;
      })
    );
    
    // Subscribe to pending sync count
    this.subscriptions.push(
      this.syncService.pendingSyncCount$.subscribe(count => {
        this.pendingSyncCount = count;
      })
    );
    
    // Subscribe to last sync time
    this.subscriptions.push(
      this.syncService.lastSyncTime$.subscribe(time => {
        this.lastSyncTime = time;
      })
    );
  }
  
  ngOnDestroy() {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
  
  /**
   * Returns a formatted string for the last sync time
   */
  getLastSyncTimeFormatted(): string {
    if (!this.lastSyncTime) {
      return 'Nunca';
    }
    
    return this.lastSyncTime.toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
  
  /**
   * Returns the appropriate icon for the sync status
   */
  getSyncStatusIcon(): string {
    if (this.syncInProgress) {
      return 'sync-circle';
    }
    
    if (this.pendingSyncCount > 0) {
      return 'warning';
    }
    
    return 'checkmark-circle-outline';
  }
  
  /**
   * Returns the appropriate color for the sync status
   */
  getSyncStatusColor(): string {
    if (this.syncInProgress) {
      return 'primary';
    }
    
    if (this.pendingSyncCount > 0) {
      return 'warning';
    }
    
    return 'success';
  }
  
  /**
   * Returns the appropriate message for the sync status
   */
  getSyncStatusMessage(): string {
    if (this.syncInProgress) {
      return 'Sincronização em andamento...';
    }
    
    if (this.pendingSyncCount > 0) {
      return `${this.pendingSyncCount} item(s) aguardando sincronização`;
    }
    
    return 'Todos os dados estão sincronizados';
  }
}
