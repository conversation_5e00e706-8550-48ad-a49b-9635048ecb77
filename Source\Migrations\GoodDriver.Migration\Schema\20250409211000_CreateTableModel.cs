﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Migration.Schema
{
	[FluentMigrator.Migration(20250409211000)]
	public class CreateTableModel : FluentMigrator.Migration
	{
		public override void Down()
		{
			if (Schema.Table("Model").Exists())
			{
				Delete.Table("Model");
			}
		}

		public override void Up()
		{
			if (!Schema.Table("Model").Exists())	
			{
				Create.Table("Model")
					.WithColumn("Id").AsInt32().PrimaryKey().NotNullable().Identity()
					.WithColumn("Name").AsString(100).NotNullable()
                    .WithColumn("ReferenceCode").AsString(50).NotNullable()
                    .WithColumn("ReferenceMonth").AsString(100).NotNullable()
                    .WithColumn("ShortName").AsString(100).NotNullable()
                    .WithColumn("BrandId").AsInt32().NotNullable().ForeignKey("FK_Model_Brand", "Brand", "Id")
					.WithColumn("CreatedOn").AsDateTime().NotNullable()
					.WithColumn("UpdatedOn").AsDateTime().Nullable();				
			}
		}
	}
}
