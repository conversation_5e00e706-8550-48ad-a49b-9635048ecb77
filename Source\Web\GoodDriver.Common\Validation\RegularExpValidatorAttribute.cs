﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Common.Validation
{
    public class RegularExpValidatorAttribute : RegularExpressionAttribute, IRuleSetAttribute
    {
        private string _prefix = "ExpressionInvalid";

        public List<string> RulesSet { get; set; }

        public string Prefix
        {
            get
            {
                return _prefix;
            }
            protected set
            {
                _prefix = value;
            }
        }

        public RegularExpValidatorAttribute(string pattern)
            : base(pattern)
        {
            RulesSet = null;
        }

        public RegularExpValidatorAttribute(string pattern, params string[] rules)
            : this(pattern)
        {
            RulesSet = new List<string>(rules);
        }
    }
}
