﻿﻿using Rogerio.Cqrs.Commands;
using System.ComponentModel.DataAnnotations;

namespace GoodDriver.Contracts.Sync.Commands
{
    public class SyncJourneyCommand : ICommand
    {
        [Required]
        public string Id { get; set; }
        
        [Required]
        public string UserId { get; set; }
        
        [Required]
        public string VehicleId { get; set; }
        
        [Required]
        public DateTime StartDate { get; set; }
        
        public DateTime? EndDate { get; set; }
        
        public double? Distance { get; set; }
        
        public string DeviceId { get; set; }
        
        [Required]
        public string SyncStatus { get; set; }
        
        public List<SyncJourneyInfoItem> JourneyInfos { get; set; } = new List<SyncJourneyInfoItem>();
        
        public DateTime CreatedOn { get; set; } = DateTime.Now;
        
        public DateTime? UpdatedOn { get; set; }
    }
    
    public class SyncJourneyInfoItem
    {
        [Required]
        public string Id { get; set; }
        
        [Required]
        public string JourneyId { get; set; }
        
        [Required]
        public double Latitude { get; set; }
        
        [Required]
        public double Longitude { get; set; }
        
        [Required]
        public DateTime Timestamp { get; set; }
        
        public string Address { get; set; }
        
        public string OccurrenceType { get; set; }
        
        [Required]
        public string SyncStatus { get; set; }
        
        public DateTime CreatedOn { get; set; } = DateTime.Now;
        
        public DateTime? UpdatedOn { get; set; }
    }
}
