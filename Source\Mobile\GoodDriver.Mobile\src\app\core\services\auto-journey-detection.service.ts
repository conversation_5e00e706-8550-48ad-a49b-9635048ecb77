import { Injectable } from '@angular/core';
import { registerPlugin } from '@capacitor/core';
import { Motion } from '@capacitor/motion';
import { JourneyStorageService } from './journey-storage.service';
import { ToastService } from './toast.service';
import { BehaviorSubject } from 'rxjs';
import { Platform } from '@ionic/angular';
import { App } from '@capacitor/app';
import { Geolocation } from '@capacitor/geolocation';

import type {
  Location,
  BackgroundGeolocationPlugin
} from '@capacitor-community/background-geolocation';

const BackgroundGeolocation = registerPlugin<BackgroundGeolocationPlugin>(
  'BackgroundGeolocation'
);

@Injectable({
  providedIn: 'root'
})
export class AutoJourneyDetectionService {
  // Estado da detecção automática
  private isEnabled = false;
  private isRunning = false;
  private currentJourneyId: string | null = null;
  private lastLocation: any = null;
  private movingTimeCounter = 0;
  private stationaryTimeCounter = 0;
  private speedThreshold = 10; // km/h
  private movingTimeThreshold = 60; // segundos
  private stationaryTimeThreshold = 180; // segundos
  private checkInterval = 10; // segundos
  private locationWatcher: any = null;
  private motionWatcher: any = null;
  private backgroundWatcher: string | null = null;
  private intervalId: any = null;

  private detectionEnabledSubject = new BehaviorSubject<boolean>(false);
  public detectionEnabled$ = this.detectionEnabledSubject.asObservable();

  private journeyActiveSubject = new BehaviorSubject<boolean>(false);
  public journeyActive$ = this.journeyActiveSubject.asObservable();

  constructor(
    private journeyService: JourneyStorageService,
    private toastService: ToastService,
    private platform: Platform
  ) {
    this.platform.ready().then(() => {
      this.setupAppStateListeners();
    });
  }

  private setupAppStateListeners() {
    App.addListener('appStateChange', ({ isActive }) => {
      if (!this.isEnabled) return;

      if (isActive) {
        console.log('App voltou para foreground');
        this.stopBackgroundMonitoring();
        if (this.isRunning) {
          this.startForegroundMonitoring();
        }
      } else {
        console.log('App foi para background');
        this.stopForegroundMonitoring();
        if (this.isRunning) {
          this.startBackgroundMonitoring();
        }
      }
    });
  }

  public async enableAutoDetection(): Promise<void> {
    if (this.isEnabled) return;

    try {
      await this.requestPermissions();
      this.isEnabled = true;
      this.detectionEnabledSubject.next(true);
      this.startDetection();
      await this.toastService.showToast('Detecção automática de viagens ativada', 'success');
    } catch (error) {
      console.error('Erro ao ativar detecção automática:', error);
      await this.toastService.showToast('Erro ao ativar detecção automática. Verifique as permissões.', 'danger');
    }
  }

  public async disableAutoDetection(): Promise<void> {
    if (!this.isEnabled) return;

    if (this.currentJourneyId) {
      await this.finishJourney();
    }

    this.stopDetection();
    this.isEnabled = false;
    this.detectionEnabledSubject.next(false);

    await this.toastService.showToast('Detecção automática de viagens desativada', 'success');
  }

  private startDetection(): void {
    if (this.isRunning) return;

    this.isRunning = true;
    this.movingTimeCounter = 0;
    this.stationaryTimeCounter = 0;
    this.startForegroundMonitoring();
  }

  private stopDetection(): void {
    if (!this.isRunning) return;

    this.stopForegroundMonitoring();
    this.stopBackgroundMonitoring();

    this.isRunning = false;
    this.movingTimeCounter = 0;
    this.stationaryTimeCounter = 0;
  }

  private startForegroundMonitoring(): void {
    this.intervalId = setInterval(() => this.checkMovementStatus(), this.checkInterval * 1000);
    this.setupLocationWatcher();
    this.setupMotionWatcher();
  }

  private stopForegroundMonitoring(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    if (this.locationWatcher) {
      this.locationWatcher.remove();
      this.locationWatcher = null;
    }

    if (this.motionWatcher) {
      this.motionWatcher.remove();
      this.motionWatcher = null;
    }
  }

  private async startBackgroundMonitoring(): Promise<void> {
    try {
      const watcherId = await BackgroundGeolocation.addWatcher(
        {
          backgroundMessage: "Monitorando viagem em segundo plano",
          backgroundTitle: "Rastreamento de viagem",
          requestPermissions: true,
          stale: false,
          distanceFilter: 50,
          // stopOnTerminate: false,
          // startOnBoot: true,
          // notificationChannelName: "Viagens",
          // notificationText: "Monitorando sua viagem",
          // notificationTitle: "GoodDriver",
          // notificationIconColor: "#0074d9",
        },
        (location: any, error: any) => {
          if (error) {
            console.error("Erro no background geolocation:", error);
            return;
          }

          if (location) {
            this.lastLocation = location;
            this.processLocationUpdate(location);
          }
        }
      );

      this.backgroundWatcher = watcherId;
    } catch (error) {
      console.error("Erro ao iniciar background geolocation:", error);
    }
  }

  private async stopBackgroundMonitoring(): Promise<void> {
    if (this.backgroundWatcher) {
      await BackgroundGeolocation.removeWatcher({
        id: this.backgroundWatcher
      });
      this.backgroundWatcher = null;
    }
  }

  private setupLocationWatcher(): void {
    // Implementar com Geolocation.watchPosition (não incluso aqui)
  }

  private setupMotionWatcher(): void {
    this.motionWatcher = Motion.addListener('accel', (event) => {
      // Processar dados de aceleração
    });
  }

  private async checkMovementStatus(): Promise<void> {
    if (!this.lastLocation) return;

    const speed = this.lastLocation.speed ? this.lastLocation.speed * 3.6 : 0;

    if (speed > this.speedThreshold) {
      this.movingTimeCounter += this.checkInterval;
      this.stationaryTimeCounter = 0;

      if (this.movingTimeCounter >= this.movingTimeThreshold && !this.currentJourneyId) {
        await this.startJourney();
      }
    } else {
      this.stationaryTimeCounter += this.checkInterval;
      this.movingTimeCounter = 0;

      if (this.stationaryTimeCounter >= this.stationaryTimeThreshold && this.currentJourneyId) {
        await this.finishJourney();
      }
    }
  }

  private processLocationUpdate(location: any): void {
    this.lastLocation = location;

    if (this.currentJourneyId) {
      this.journeyService.addLocation(
        this.currentJourneyId,
        location.latitude,
        location.longitude
      );
    }
  }

  private async startJourney(): Promise<void> {
    try {
      const journeyId = await this.journeyService.startJourney();
      this.currentJourneyId = journeyId;
      this.journeyActiveSubject.next(true);
      await this.toastService.showToast('Viagem iniciada automaticamente', 'success');
    } catch (error) {
      console.error('Erro ao iniciar viagem automaticamente:', error);
    }
  }

  private async finishJourney(): Promise<void> {
    if (!this.currentJourneyId) return;

    try {
      await this.journeyService.endJourney(this.currentJourneyId);
      await this.toastService.showToast('Viagem finalizada automaticamente', 'success');
      this.currentJourneyId = null;
      this.journeyActiveSubject.next(false);
    } catch (error) {
      console.error('Erro ao finalizar viagem automaticamente:', error);
    }
  }

  private async requestPermissions(): Promise<void> {
    try {
      const result = await Geolocation.requestPermissions();
      console.log('Permissões de localização:', result);
    } catch (error) {
      console.error('Erro ao solicitar permissões:', error);
      throw error;
    }
  }
}
