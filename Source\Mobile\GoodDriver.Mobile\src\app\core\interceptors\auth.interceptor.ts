import { HttpInterceptorFn } from '@angular/common/http';
//import { Observable } from 'rxjs';
import { SessionService } from '../services/session.service';
import { inject } from '@angular/core';
import { from, switchMap } from 'rxjs';

export const authInterceptor : HttpInterceptorFn = (req, next) => {
    
    const sessionService = inject(SessionService);
    let token: string | null = null; // Inicializa o token como nulo

    const excludedUrls: string[] = [
        '/authentication',
        '/create',
        '/refreshToken'
      ];

    const shouldSkip = excludedUrls.some((url: string) => req.url.includes(url));

    if (shouldSkip) {
      return next(req); // não adiciona o token (usei para login, refreshToken e create user)
    }

    return from(sessionService.getToken()).pipe(
        switchMap((token: string | null) => {
          if (token) {
            const authReq = req.clone({
              setHeaders: {
                Authorization: `bearer ${token}`,
              },
            });
            console.log('AuthReq headers:', authReq.headers.keys());
            console.log('Token:', authReq.headers.get('Authorization')); // Log do token
            return next(authReq);
          }
          return next(req);
        })
      );
    };

    //token = await getTokenSession(); // Obtenha o token de forma síncrona
    // if (sessionService?.getToken) {
    //   await sessionService.getToken()?.then(resolvedToken => {
    //     token = resolvedToken || null;
    //   });
    // }

//     if (token) {
//       const authReq = req.clone({
//         setHeaders: {
//           Authorization: `bearer ${token}`,
//         },
//       });
//       return next(authReq);
//     }
  
//     return next(req);
// };

export const getTokenSession = async () => {
    const sessionService = inject(SessionService);
    const token = await sessionService.getToken(); // Obtenha o token de forma síncrona
    return token;
};
