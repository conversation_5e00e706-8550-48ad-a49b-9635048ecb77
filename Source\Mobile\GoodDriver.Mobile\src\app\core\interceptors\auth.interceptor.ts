import { HttpInterceptorFn, HttpErrorResponse } from '@angular/common/http';
//import { Observable } from 'rxjs';
import { SessionService } from '../services/session.service';
import { inject } from '@angular/core';
import { from, switchMap, catchError, throwError, tap } from 'rxjs';

// Interceptor para logging de requisições HTTP
export const loggingInterceptor: HttpInterceptorFn = (req, next) => {
  const startTime = Date.now();
  
  console.log(`[HTTP] ${req.method} ${req.url} - Starting request`);
  
  return next(req).pipe(
    tap({
      next: (response) => {
        const duration = Date.now() - startTime;
        console.log(`[HTTP] ${req.method} ${req.url} - Success (${duration}ms)`);
      },
      error: (error: HttpErrorResponse) => {
        const duration = Date.now() - startTime;
        console.error(`[HTTP] ${req.method} ${req.url} - Error ${error.status} (${duration}ms)`, error);
      }
    })
  );
};

export const authInterceptor: HttpInterceptorFn = (req, next) => {
  const sessionService = inject(SessionService);

  // URLs que não precisam de autenticação (endpoints públicos)
  const excludedUrls: string[] = [
    '/authentication',
    '/refreshToken',
    '/api/user/authentication',
    '/api/user/create'
  ];

  // Verifica se a URL deve ser excluída da autenticação
  const shouldSkip = excludedUrls.some((url: string) => req.url.includes(url));

  if (shouldSkip) {
    console.log(`[AuthInterceptor] Skipping authentication for: ${req.url}`);
    return next(req);
  }

  // Adiciona o token JWT para todas as outras requisições
  return from(sessionService.getToken()).pipe(
    switchMap((token: string | null) => {
      if (token) {
        const authReq = req.clone({
          setHeaders: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
        });
        
        console.log(`[AuthInterceptor] Adding JWT token to request: ${req.url}`);
        console.log(`[AuthInterceptor] Token: ${token.substring(0, 20)}...`);
        
        return next(authReq);
      } else {
        console.warn(`[AuthInterceptor] No JWT token found for request: ${req.url}`);
        // Continua sem token - a API deve retornar 401 se autenticação for necessária
        return next(req);
      }
    }),
    catchError((error: HttpErrorResponse) => {
      console.error(`[AuthInterceptor] Error processing request: ${req.url}`, error);
      
      // Se receber 401 (Unauthorized), pode tentar refresh do token
      if (error.status === 401) {
        console.warn(`[AuthInterceptor] Received 401 for ${req.url}. Token may be expired.`);
        // Aqui você pode implementar lógica de refresh de token se necessário
        // Por enquanto, apenas loga o erro
      }
      
      return throwError(() => error);
    })
  );
};

// Função auxiliar para obter o token (mantida para compatibilidade)
export const getTokenSession = async () => {
  const sessionService = inject(SessionService);
  const token = await sessionService.getToken();
  return token;
};
