﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\GoodDriver.Common\GoodDriver.Common.csproj" />
    <ProjectReference Include="..\GoodDriver.Contracts\GoodDriver.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Rogerio.Commom">
      <HintPath>..\..\..\..\RogerioFramework\RogerioIT\Rogerio.Commom\bin\Debug\net8.0\Rogerio.Commom.dll</HintPath>
    </Reference>
    <Reference Include="Rogerio.Cqrs">
      <HintPath>..\..\..\..\RogerioFramework\RogerioIT\Rogerio.Cqrs\bin\Debug\net8.0\Rogerio.Cqrs.dll</HintPath>
    </Reference>
    <Reference Include="Rogerio.Data">
      <HintPath>..\..\..\..\RogerioFramework\RogerioIT\Rogerio.Data\bin\Debug\net8.0\Rogerio.Data.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Sync\" />
  </ItemGroup>

</Project>
