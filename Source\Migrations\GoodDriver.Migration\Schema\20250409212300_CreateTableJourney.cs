﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Migration.Schema
{
	[FluentMigrator.Migration(20250409212300)]
	public class CreateTableJourney : FluentMigrator.Migration
	{
		public override void Down()
		{
			if (Schema.Table("Journey").Exists())
			{
				Delete.Table("Journey");
			}
		}

		public override void Up()
		{
			if (!Schema.Table("Journey").Exists())
			{
				Create.Table("Journey")
					.WithColumn("Id").AsGuid().PrimaryKey().NotNullable()
					.WithColumn("UserId").AsGuid().NotNullable().ForeignKey("FK_Journey_User", "User", "Id")
					.WithColumn("VehicleId").AsGuid().NotNullable().ForeignKey("FK_Journey_Vehicle", "Vehicle", "Id")
					.WithColumn("StartDate").AsDateTime().NotNullable()
					.WithColumn("EndDate").AsDateTime().Nullable()
					.WithColumn("Distance").AsDouble().Nullable()
					.WithColumn("Score").AsInt32().Nullable()
					.WithColumn("CreatedOn").AsDateTime().NotNullable()
					.WithColumn("UpdatedOn").AsDateTime().Nullable()
					.WithColumn("DeviceId").AsGuid().NotNullable().ForeignKey("FK_Journey_Device", "Device", "Id");
			}
		}	
	}
}
