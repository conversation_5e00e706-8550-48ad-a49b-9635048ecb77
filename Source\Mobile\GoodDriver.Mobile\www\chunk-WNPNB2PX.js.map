{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/helpers-d94bc8ad.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as printIonError } from './index-cfd9c1f2.js';\nconst transitionEndAsync = (el, expectedDuration = 0) => {\n  return new Promise(resolve => {\n    transitionEnd(el, expectedDuration, resolve);\n  });\n};\n/**\n * Allows developer to wait for a transition\n * to finish and fallback to a timer if the\n * transition is cancelled or otherwise\n * never finishes. Also see transitionEndAsync\n * which is an await-able version of this.\n */\nconst transitionEnd = (el, expectedDuration = 0, callback) => {\n  let unRegTrans;\n  let animationTimeout;\n  const opts = {\n    passive: true\n  };\n  const ANIMATION_FALLBACK_TIMEOUT = 500;\n  const unregister = () => {\n    if (unRegTrans) {\n      unRegTrans();\n    }\n  };\n  const onTransitionEnd = ev => {\n    if (ev === undefined || el === ev.target) {\n      unregister();\n      callback(ev);\n    }\n  };\n  if (el) {\n    el.addEventListener('webkitTransitionEnd', onTransitionEnd, opts);\n    el.addEventListener('transitionend', onTransitionEnd, opts);\n    animationTimeout = setTimeout(onTransitionEnd, expectedDuration + ANIMATION_FALLBACK_TIMEOUT);\n    unRegTrans = () => {\n      if (animationTimeout !== undefined) {\n        clearTimeout(animationTimeout);\n        animationTimeout = undefined;\n      }\n      el.removeEventListener('webkitTransitionEnd', onTransitionEnd, opts);\n      el.removeEventListener('transitionend', onTransitionEnd, opts);\n    };\n  }\n  return unregister;\n};\n/**\n * Waits for a component to be ready for\n * both custom element and non-custom element builds.\n * If non-custom element build, el.componentOnReady\n * will be used.\n * For custom element builds, we wait a frame\n * so that the inner contents of the component\n * have a chance to render.\n *\n * Use this utility rather than calling\n * el.componentOnReady yourself.\n */\nconst componentOnReady = (el, callback) => {\n  if (el.componentOnReady) {\n    // eslint-disable-next-line custom-rules/no-component-on-ready-method\n    el.componentOnReady().then(resolvedEl => callback(resolvedEl));\n  } else {\n    raf(() => callback(el));\n  }\n};\n/**\n * This functions checks if a Stencil component is using\n * the lazy loaded build of Stencil. Returns `true` if\n * the component is lazy loaded. Returns `false` otherwise.\n */\nconst hasLazyBuild = stencilEl => {\n  return stencilEl.componentOnReady !== undefined;\n};\n/**\n * Elements inside of web components sometimes need to inherit global attributes\n * set on the host. For example, the inner input in `ion-input` should inherit\n * the `title` attribute that developers set directly on `ion-input`. This\n * helper function should be called in componentWillLoad and assigned to a variable\n * that is later used in the render function.\n *\n * This does not need to be reactive as changing attributes on the host element\n * does not trigger a re-render.\n */\nconst inheritAttributes = (el, attributes = []) => {\n  const attributeObject = {};\n  attributes.forEach(attr => {\n    if (el.hasAttribute(attr)) {\n      const value = el.getAttribute(attr);\n      if (value !== null) {\n        attributeObject[attr] = el.getAttribute(attr);\n      }\n      el.removeAttribute(attr);\n    }\n  });\n  return attributeObject;\n};\n/**\n * List of available ARIA attributes + `role`.\n * Removed deprecated attributes.\n * https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes\n */\nconst ariaAttributes = ['role', 'aria-activedescendant', 'aria-atomic', 'aria-autocomplete', 'aria-braillelabel', 'aria-brailleroledescription', 'aria-busy', 'aria-checked', 'aria-colcount', 'aria-colindex', 'aria-colindextext', 'aria-colspan', 'aria-controls', 'aria-current', 'aria-describedby', 'aria-description', 'aria-details', 'aria-disabled', 'aria-errormessage', 'aria-expanded', 'aria-flowto', 'aria-haspopup', 'aria-hidden', 'aria-invalid', 'aria-keyshortcuts', 'aria-label', 'aria-labelledby', 'aria-level', 'aria-live', 'aria-multiline', 'aria-multiselectable', 'aria-orientation', 'aria-owns', 'aria-placeholder', 'aria-posinset', 'aria-pressed', 'aria-readonly', 'aria-relevant', 'aria-required', 'aria-roledescription', 'aria-rowcount', 'aria-rowindex', 'aria-rowindextext', 'aria-rowspan', 'aria-selected', 'aria-setsize', 'aria-sort', 'aria-valuemax', 'aria-valuemin', 'aria-valuenow', 'aria-valuetext'];\n/**\n * Returns an array of aria attributes that should be copied from\n * the shadow host element to a target within the light DOM.\n * @param el The element that the attributes should be copied from.\n * @param ignoreList The list of aria-attributes to ignore reflecting and removing from the host.\n * Use this in instances where we manually specify aria attributes on the `<Host>` element.\n */\nconst inheritAriaAttributes = (el, ignoreList) => {\n  let attributesToInherit = ariaAttributes;\n  if (ignoreList && ignoreList.length > 0) {\n    attributesToInherit = attributesToInherit.filter(attr => !ignoreList.includes(attr));\n  }\n  return inheritAttributes(el, attributesToInherit);\n};\nconst addEventListener = (el, eventName, callback, opts) => {\n  return el.addEventListener(eventName, callback, opts);\n};\nconst removeEventListener = (el, eventName, callback, opts) => {\n  return el.removeEventListener(eventName, callback, opts);\n};\n/**\n * Gets the root context of a shadow dom element\n * On newer browsers this will be the shadowRoot,\n * but for older browser this may just be the\n * element itself.\n *\n * Useful for whenever you need to explicitly\n * do \"myElement.shadowRoot!.querySelector(...)\".\n */\nconst getElementRoot = (el, fallback = el) => {\n  return el.shadowRoot || fallback;\n};\n/**\n * Patched version of requestAnimationFrame that avoids ngzone\n * Use only when you know ngzone should not run\n */\nconst raf = h => {\n  if (typeof __zone_symbol__requestAnimationFrame === 'function') {\n    return __zone_symbol__requestAnimationFrame(h);\n  }\n  if (typeof requestAnimationFrame === 'function') {\n    return requestAnimationFrame(h);\n  }\n  return setTimeout(h);\n};\nconst hasShadowDom = el => {\n  return !!el.shadowRoot && !!el.attachShadow;\n};\nconst focusVisibleElement = el => {\n  el.focus();\n  /**\n   * When programmatically focusing an element,\n   * the focus-visible utility will not run because\n   * it is expecting a keyboard event to have triggered this;\n   * however, there are times when we need to manually control\n   * this behavior so we call the `setFocus` method on ion-app\n   * which will let us explicitly set the elements to focus.\n   */\n  if (el.classList.contains('ion-focusable')) {\n    const app = el.closest('ion-app');\n    if (app) {\n      app.setFocus([el]);\n    }\n  }\n};\n/**\n * This method is used to add a hidden input to a host element that contains\n * a Shadow DOM. It does not add the input inside of the Shadow root which\n * allows it to be picked up inside of forms. It should contain the same\n * values as the host element.\n *\n * @param always Add a hidden input even if the container does not use Shadow\n * @param container The element where the input will be added\n * @param name The name of the input\n * @param value The value of the input\n * @param disabled If true, the input is disabled\n */\nconst renderHiddenInput = (always, container, name, value, disabled) => {\n  if (always || hasShadowDom(container)) {\n    let input = container.querySelector('input.aux-input');\n    if (!input) {\n      input = container.ownerDocument.createElement('input');\n      input.type = 'hidden';\n      input.classList.add('aux-input');\n      container.appendChild(input);\n    }\n    input.disabled = disabled;\n    input.name = name;\n    input.value = value || '';\n  }\n};\nconst clamp = (min, n, max) => {\n  return Math.max(min, Math.min(n, max));\n};\nconst assert = (actual, reason) => {\n  if (!actual) {\n    const message = 'ASSERT: ' + reason;\n    printIonError(message);\n    debugger; // eslint-disable-line\n    throw new Error(message);\n  }\n};\nconst pointerCoord = ev => {\n  // get X coordinates for either a mouse click\n  // or a touch depending on the given event\n  if (ev) {\n    const changedTouches = ev.changedTouches;\n    if (changedTouches && changedTouches.length > 0) {\n      const touch = changedTouches[0];\n      return {\n        x: touch.clientX,\n        y: touch.clientY\n      };\n    }\n    if (ev.pageX !== undefined) {\n      return {\n        x: ev.pageX,\n        y: ev.pageY\n      };\n    }\n  }\n  return {\n    x: 0,\n    y: 0\n  };\n};\n/**\n * @hidden\n * Given a side, return if it should be on the end\n * based on the value of dir\n * @param side the side\n * @param isRTL whether the application dir is rtl\n */\nconst isEndSide = side => {\n  const isRTL = document.dir === 'rtl';\n  switch (side) {\n    case 'start':\n      return isRTL;\n    case 'end':\n      return !isRTL;\n    default:\n      throw new Error(`\"${side}\" is not a valid value for [side]. Use \"start\" or \"end\" instead.`);\n  }\n};\nconst debounceEvent = (event, wait) => {\n  const original = event._original || event;\n  return {\n    _original: event,\n    emit: debounce(original.emit.bind(original), wait)\n  };\n};\nconst debounce = (func, wait = 0) => {\n  let timer;\n  return (...args) => {\n    clearTimeout(timer);\n    timer = setTimeout(func, wait, ...args);\n  };\n};\n/**\n * Check whether the two string maps are shallow equal.\n *\n * undefined is treated as an empty map.\n *\n * @returns whether the keys are the same and the values are shallow equal.\n */\nconst shallowEqualStringMap = (map1, map2) => {\n  map1 !== null && map1 !== void 0 ? map1 : map1 = {};\n  map2 !== null && map2 !== void 0 ? map2 : map2 = {};\n  if (map1 === map2) {\n    return true;\n  }\n  const keys1 = Object.keys(map1);\n  if (keys1.length !== Object.keys(map2).length) {\n    return false;\n  }\n  for (const k1 of keys1) {\n    if (!(k1 in map2)) {\n      return false;\n    }\n    if (map1[k1] !== map2[k1]) {\n      return false;\n    }\n  }\n  return true;\n};\n/**\n * Checks input for usable number. Not NaN and not Infinite.\n */\nconst isSafeNumber = input => {\n  return typeof input === 'number' && !isNaN(input) && isFinite(input);\n};\nexport { addEventListener as a, removeEventListener as b, componentOnReady as c, renderHiddenInput as d, debounceEvent as e, focusVisibleElement as f, getElementRoot as g, inheritAttributes as h, inheritAriaAttributes as i, clamp as j, hasLazyBuild as k, isSafeNumber as l, hasShadowDom as m, assert as n, isEndSide as o, debounce as p, pointerCoord as q, raf as r, shallowEqualStringMap as s, transitionEndAsync as t };"], "mappings": ";;;;;;;;;AAAA,IAIM,oBAYA,eA6CA,kBAaA,cAaA,mBAkBA,gBAQA,uBAOA,kBAGA,qBAYA,gBAOA,KASA,cAGA,qBA6BA,mBAcA,OAGA,QAQA,cA+BA,WAWA,eAOA,UAcA,uBAuBA;AAtSN;AAAA;AAAA;AAGA;AACA,IAAM,qBAAqB,CAAC,IAAI,mBAAmB,MAAM;AACvD,aAAO,IAAI,QAAQ,aAAW;AAC5B,sBAAc,IAAI,kBAAkB,OAAO;AAAA,MAC7C,CAAC;AAAA,IACH;AAQA,IAAM,gBAAgB,CAAC,IAAI,mBAAmB,GAAG,aAAa;AAC5D,UAAI;AACJ,UAAI;AACJ,YAAM,OAAO;AAAA,QACX,SAAS;AAAA,MACX;AACA,YAAM,6BAA6B;AACnC,YAAM,aAAa,MAAM;AACvB,YAAI,YAAY;AACd,qBAAW;AAAA,QACb;AAAA,MACF;AACA,YAAM,kBAAkB,QAAM;AAC5B,YAAI,OAAO,UAAa,OAAO,GAAG,QAAQ;AACxC,qBAAW;AACX,mBAAS,EAAE;AAAA,QACb;AAAA,MACF;AACA,UAAI,IAAI;AACN,WAAG,iBAAiB,uBAAuB,iBAAiB,IAAI;AAChE,WAAG,iBAAiB,iBAAiB,iBAAiB,IAAI;AAC1D,2BAAmB,WAAW,iBAAiB,mBAAmB,0BAA0B;AAC5F,qBAAa,MAAM;AACjB,cAAI,qBAAqB,QAAW;AAClC,yBAAa,gBAAgB;AAC7B,+BAAmB;AAAA,UACrB;AACA,aAAG,oBAAoB,uBAAuB,iBAAiB,IAAI;AACnE,aAAG,oBAAoB,iBAAiB,iBAAiB,IAAI;AAAA,QAC/D;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAaA,IAAM,mBAAmB,CAAC,IAAI,aAAa;AACzC,UAAI,GAAG,kBAAkB;AAEvB,WAAG,iBAAiB,EAAE,KAAK,gBAAc,SAAS,UAAU,CAAC;AAAA,MAC/D,OAAO;AACL,YAAI,MAAM,SAAS,EAAE,CAAC;AAAA,MACxB;AAAA,IACF;AAMA,IAAM,eAAe,eAAa;AAChC,aAAO,UAAU,qBAAqB;AAAA,IACxC;AAWA,IAAM,oBAAoB,CAAC,IAAI,aAAa,CAAC,MAAM;AACjD,YAAM,kBAAkB,CAAC;AACzB,iBAAW,QAAQ,UAAQ;AACzB,YAAI,GAAG,aAAa,IAAI,GAAG;AACzB,gBAAM,QAAQ,GAAG,aAAa,IAAI;AAClC,cAAI,UAAU,MAAM;AAClB,4BAAgB,IAAI,IAAI,GAAG,aAAa,IAAI;AAAA,UAC9C;AACA,aAAG,gBAAgB,IAAI;AAAA,QACzB;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAMA,IAAM,iBAAiB,CAAC,QAAQ,yBAAyB,eAAe,qBAAqB,qBAAqB,+BAA+B,aAAa,gBAAgB,iBAAiB,iBAAiB,qBAAqB,gBAAgB,iBAAiB,gBAAgB,oBAAoB,oBAAoB,gBAAgB,iBAAiB,qBAAqB,iBAAiB,eAAe,iBAAiB,eAAe,gBAAgB,qBAAqB,cAAc,mBAAmB,cAAc,aAAa,kBAAkB,wBAAwB,oBAAoB,aAAa,oBAAoB,iBAAiB,gBAAgB,iBAAiB,iBAAiB,iBAAiB,wBAAwB,iBAAiB,iBAAiB,qBAAqB,gBAAgB,iBAAiB,gBAAgB,aAAa,iBAAiB,iBAAiB,iBAAiB,gBAAgB;AAQx5B,IAAM,wBAAwB,CAAC,IAAI,eAAe;AAChD,UAAI,sBAAsB;AAC1B,UAAI,cAAc,WAAW,SAAS,GAAG;AACvC,8BAAsB,oBAAoB,OAAO,UAAQ,CAAC,WAAW,SAAS,IAAI,CAAC;AAAA,MACrF;AACA,aAAO,kBAAkB,IAAI,mBAAmB;AAAA,IAClD;AACA,IAAM,mBAAmB,CAAC,IAAI,WAAW,UAAU,SAAS;AAC1D,aAAO,GAAG,iBAAiB,WAAW,UAAU,IAAI;AAAA,IACtD;AACA,IAAM,sBAAsB,CAAC,IAAI,WAAW,UAAU,SAAS;AAC7D,aAAO,GAAG,oBAAoB,WAAW,UAAU,IAAI;AAAA,IACzD;AAUA,IAAM,iBAAiB,CAAC,IAAI,WAAW,OAAO;AAC5C,aAAO,GAAG,cAAc;AAAA,IAC1B;AAKA,IAAM,MAAM,OAAK;AACf,UAAI,OAAO,yCAAyC,YAAY;AAC9D,eAAO,qCAAqC,CAAC;AAAA,MAC/C;AACA,UAAI,OAAO,0BAA0B,YAAY;AAC/C,eAAO,sBAAsB,CAAC;AAAA,MAChC;AACA,aAAO,WAAW,CAAC;AAAA,IACrB;AACA,IAAM,eAAe,QAAM;AACzB,aAAO,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,GAAG;AAAA,IACjC;AACA,IAAM,sBAAsB,QAAM;AAChC,SAAG,MAAM;AAST,UAAI,GAAG,UAAU,SAAS,eAAe,GAAG;AAC1C,cAAM,MAAM,GAAG,QAAQ,SAAS;AAChC,YAAI,KAAK;AACP,cAAI,SAAS,CAAC,EAAE,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAaA,IAAM,oBAAoB,CAAC,QAAQ,WAAW,MAAM,OAAO,aAAa;AACtE,UAAI,UAAU,aAAa,SAAS,GAAG;AACrC,YAAI,QAAQ,UAAU,cAAc,iBAAiB;AACrD,YAAI,CAAC,OAAO;AACV,kBAAQ,UAAU,cAAc,cAAc,OAAO;AACrD,gBAAM,OAAO;AACb,gBAAM,UAAU,IAAI,WAAW;AAC/B,oBAAU,YAAY,KAAK;AAAA,QAC7B;AACA,cAAM,WAAW;AACjB,cAAM,OAAO;AACb,cAAM,QAAQ,SAAS;AAAA,MACzB;AAAA,IACF;AACA,IAAM,QAAQ,CAAC,KAAK,GAAG,QAAQ;AAC7B,aAAO,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG,GAAG,CAAC;AAAA,IACvC;AACA,IAAM,SAAS,CAAC,QAAQ,WAAW;AACjC,UAAI,CAAC,QAAQ;AACX,cAAM,UAAU,aAAa;AAC7B,sBAAc,OAAO;AACrB;AACA,cAAM,IAAI,MAAM,OAAO;AAAA,MACzB;AAAA,IACF;AACA,IAAM,eAAe,QAAM;AAGzB,UAAI,IAAI;AACN,cAAM,iBAAiB,GAAG;AAC1B,YAAI,kBAAkB,eAAe,SAAS,GAAG;AAC/C,gBAAM,QAAQ,eAAe,CAAC;AAC9B,iBAAO;AAAA,YACL,GAAG,MAAM;AAAA,YACT,GAAG,MAAM;AAAA,UACX;AAAA,QACF;AACA,YAAI,GAAG,UAAU,QAAW;AAC1B,iBAAO;AAAA,YACL,GAAG,GAAG;AAAA,YACN,GAAG,GAAG;AAAA,UACR;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,IACF;AAQA,IAAM,YAAY,UAAQ;AACxB,YAAM,QAAQ,SAAS,QAAQ;AAC/B,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO,CAAC;AAAA,QACV;AACE,gBAAM,IAAI,MAAM,IAAI,IAAI,kEAAkE;AAAA,MAC9F;AAAA,IACF;AACA,IAAM,gBAAgB,CAAC,OAAO,SAAS;AACrC,YAAM,WAAW,MAAM,aAAa;AACpC,aAAO;AAAA,QACL,WAAW;AAAA,QACX,MAAM,SAAS,SAAS,KAAK,KAAK,QAAQ,GAAG,IAAI;AAAA,MACnD;AAAA,IACF;AACA,IAAM,WAAW,CAAC,MAAM,OAAO,MAAM;AACnC,UAAI;AACJ,aAAO,IAAI,SAAS;AAClB,qBAAa,KAAK;AAClB,gBAAQ,WAAW,MAAM,MAAM,GAAG,IAAI;AAAA,MACxC;AAAA,IACF;AAQA,IAAM,wBAAwB,CAAC,MAAM,SAAS;AAC5C,eAAS,QAAQ,SAAS,SAAS,OAAO,OAAO,CAAC;AAClD,eAAS,QAAQ,SAAS,SAAS,OAAO,OAAO,CAAC;AAClD,UAAI,SAAS,MAAM;AACjB,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,UAAI,MAAM,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AAC7C,eAAO;AAAA,MACT;AACA,iBAAW,MAAM,OAAO;AACtB,YAAI,EAAE,MAAM,OAAO;AACjB,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,EAAE,MAAM,KAAK,EAAE,GAAG;AACzB,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAIA,IAAM,eAAe,WAAS;AAC5B,aAAO,OAAO,UAAU,YAAY,CAAC,MAAM,KAAK,KAAK,SAAS,KAAK;AAAA,IACrE;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}