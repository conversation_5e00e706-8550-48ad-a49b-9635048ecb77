<ion-header>
  <ion-toolbar>
    <ion-title>Novo Veículo</ion-title>
  </ion-toolbar>
  <ion-buttons slot="start">
    <ion-back-button defaultHref="/tabs/vehicles"></ion-back-button>
  </ion-buttons>
</ion-header>

<ion-content class="ion-padding">
  <!-- Alerta de conexão com a internet -->
  <ion-card color="warning" *ngIf="!isOnline">
    <ion-card-header>
      <ion-card-title>
        <ion-icon name="wifi-outline"></ion-icon>
        Atenção
      </ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <p>Para cadastrar um veículo, é necessário estar conectado à internet para obter as marcas e modelos atualizados.</p>
      <p>Por favor, verifique sua conexão e tente novamente.</p>
    </ion-card-content>
  </ion-card>

  <!-- Alerta informativo -->
  <ion-card color="light" *ngIf="isOnline">
    <ion-card-content>
      <ion-icon name="information-circle-outline" color="primary"></ion-icon>
      <span> É necessário estar conectado à internet para obter as marcas e modelos de veículos atualizados.</span>
    </ion-card-content>
  </ion-card>

  <form [formGroup]="vehicleForm">
    <!-- Marca -->
    <ion-item>
      <ion-label position="stacked">Marca</ion-label>
      <ion-select formControlName="brand" (ionChange)="onBrandChange($event.detail.value)" interface="popover">
        <ion-select-option *ngFor="let brand of brands" [value]="brand.id">{{ brand.name }}</ion-select-option>
      </ion-select>
    </ion-item>

    <!-- Modelo -->
    <ion-item>
      <ion-label position="stacked">Modelo</ion-label>
      <ion-select formControlName="model" (ionChange)="onModelChange($event.detail.value)" interface="popover">
        <ion-select-option *ngFor="let model of models" [value]="model.id">{{ model.name }}</ion-select-option>
      </ion-select>
    </ion-item>

    <!-- Versão -->
    <ion-item>
      <ion-label position="stacked">Versão</ion-label>
      <ion-select formControlName="version" interface="popover">
        <!-- <ion-select-option *ngFor="let versao of versoes" [value]="versao.id">{{ versao.nome }}</ion-select-option> -->
      </ion-select>
    </ion-item>

    <!-- Ano -->
    <ion-item>
      <ion-label position="stacked">Ano</ion-label>
      <ion-input formControlName="year" type="number" placeholder="Ex: 2021" min="1900" max="2099"></ion-input>
    </ion-item>

    <!-- Placa -->
    <ion-item>
      <ion-label position="stacked">Placa</ion-label>
      <ion-input formControlName="plate" maxlength="8" placeholder="AAA-0000" inputmode="text"></ion-input>
      <ion-note slot="error" *ngIf="vehicleForm.get('plate')?.hasError('pattern') && vehicleForm.get('plate')?.touched">
        Formato inválido. Use o formato AAA-0000
      </ion-note>
    </ion-item>

    <!-- Apólice (opcional) -->
    <ion-item>
      <ion-label position="stacked">Número da Apólice (opcional)</ion-label>
      <ion-input formControlName="policy" type="text"></ion-input>
    </ion-item>

    <!-- Botão salvar -->
    <ion-button expand="block" class="ion-margin-top" (click)="save()" [disabled]="vehicleForm.invalid || isSubmitting || !isOnline">
      <ion-spinner name="crescent" *ngIf="isSubmitting"></ion-spinner>
      <span *ngIf="!isSubmitting">Salvar</span>
    </ion-button>

    <!-- Mensagem de erro quando offline -->
    <div *ngIf="!isOnline" class="offline-message">
      <ion-text color="danger">
        <p class="ion-text-center">
          <ion-icon name="wifi-outline"></ion-icon>
          Não é possível salvar sem conexão com a internet
        </p>
      </ion-text>
    </div>
  </form>
</ion-content>
