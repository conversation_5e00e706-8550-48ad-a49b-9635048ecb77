import {
  componentOnReady,
  init_helpers_d94bc8ad
} from "./chunk-WNPNB2PX.js";
import {
  init_index_cfd9c1f2,
  printRequiredElementError
} from "./chunk-6SXGDLUH.js";
import {
  __async,
  __esm
} from "./chunk-4W6HR7MY.js";

// node_modules/@ionic/core/dist/esm/index-9a17db3d.js
var ION_CONTENT_TAG_NAME, ION_CONTENT_ELEMENT_SELECTOR, ION_CONTENT_CLASS_SELECTOR, ION_CONTENT_SELECTOR, isIonContent, getScrollElement, findIonContent, findClosestIonContent, scrollToTop, scrollByPoint, printIonContentErrorMsg, disableContentScrollY, resetContentScrollY;
var init_index_9a17db3d = __esm({
  "node_modules/@ionic/core/dist/esm/index-9a17db3d.js"() {
    "use strict";
    init_helpers_d94bc8ad();
    init_index_cfd9c1f2();
    ION_CONTENT_TAG_NAME = "ION-CONTENT";
    ION_CONTENT_ELEMENT_SELECTOR = "ion-content";
    ION_CONTENT_CLASS_SELECTOR = ".ion-content-scroll-host";
    ION_CONTENT_SELECTOR = `${ION_CONTENT_ELEMENT_SELECTOR}, ${ION_CONTENT_CLASS_SELECTOR}`;
    isIonContent = (el) => el.tagName === ION_CONTENT_TAG_NAME;
    getScrollElement = (el) => __async(null, null, function* () {
      if (isIonContent(el)) {
        yield new Promise((resolve) => componentOnReady(el, resolve));
        return el.getScrollElement();
      }
      return el;
    });
    findIonContent = (el) => {
      const customContentHost = el.querySelector(ION_CONTENT_CLASS_SELECTOR);
      if (customContentHost) {
        return customContentHost;
      }
      return el.querySelector(ION_CONTENT_SELECTOR);
    };
    findClosestIonContent = (el) => {
      return el.closest(ION_CONTENT_SELECTOR);
    };
    scrollToTop = (el, durationMs) => {
      if (isIonContent(el)) {
        const content = el;
        return content.scrollToTop(durationMs);
      }
      return Promise.resolve(el.scrollTo({
        top: 0,
        left: 0,
        behavior: durationMs > 0 ? "smooth" : "auto"
      }));
    };
    scrollByPoint = (el, x, y, durationMs) => {
      if (isIonContent(el)) {
        const content = el;
        return content.scrollByPoint(x, y, durationMs);
      }
      return Promise.resolve(el.scrollBy({
        top: y,
        left: x,
        behavior: durationMs > 0 ? "smooth" : "auto"
      }));
    };
    printIonContentErrorMsg = (el) => {
      return printRequiredElementError(el, ION_CONTENT_ELEMENT_SELECTOR);
    };
    disableContentScrollY = (contentEl) => {
      if (isIonContent(contentEl)) {
        const ionContent = contentEl;
        const initialScrollY = ionContent.scrollY;
        ionContent.scrollY = false;
        return initialScrollY;
      } else {
        contentEl.style.setProperty("overflow", "hidden");
        return true;
      }
    };
    resetContentScrollY = (contentEl, initialScrollY) => {
      if (isIonContent(contentEl)) {
        contentEl.scrollY = initialScrollY;
      } else {
        contentEl.style.removeProperty("overflow");
      }
    };
  }
});

export {
  ION_CONTENT_ELEMENT_SELECTOR,
  ION_CONTENT_CLASS_SELECTOR,
  isIonContent,
  getScrollElement,
  findIonContent,
  findClosestIonContent,
  scrollToTop,
  scrollByPoint,
  printIonContentErrorMsg,
  disableContentScrollY,
  resetContentScrollY,
  init_index_9a17db3d
};
/*! Bundled license information:

@ionic/core/dist/esm/index-9a17db3d.js:
  (*!
   * (C) Ionic http://ionicframework.com - MIT License
   *)
*/
//# sourceMappingURL=chunk-L6RBCVAP.js.map
