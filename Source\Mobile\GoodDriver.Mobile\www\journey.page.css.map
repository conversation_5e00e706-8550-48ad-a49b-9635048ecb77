{"version": 3, "sources": ["src/app/pages/tabs/journeys/journey/journey.page.scss"], "sourcesContent": ["// Estilos para a página de viagens\r\nion-card {\r\n  margin-bottom: var(--app-spacing-md);\r\n}\r\n\r\n.tracking-controls {\r\n  margin-top: var(--app-spacing-md);\r\n}\r\n\r\n.description {\r\n  margin-bottom: var(--app-spacing-md);\r\n  color: var(--ion-color-medium);\r\n  font-size: 0.9rem;\r\n}\r\n\r\nion-segment {\r\n  margin-bottom: var(--app-spacing-md);\r\n}\r\n\r\n.status-item {\r\n  --background: var(--app-primary-light);\r\n  border-radius: var(--app-border-radius-md);\r\n  margin-bottom: var(--app-spacing-sm);\r\n}\r\n\r\nion-button {\r\n  margin-bottom: var(--app-spacing-sm);\r\n}\r\n\r\nion-toggle {\r\n  --background: var(--ion-color-medium);\r\n  --background-checked: var(--ion-color-success);\r\n  --handle-background: var(--ion-color-light);\r\n  --handle-background-checked: var(--ion-color-light);\r\n}"], "mappings": ";AACA;AACE,iBAAA,IAAA;;AAGF,CAAA;AACE,cAAA,IAAA;;AAGF,CAAA;AACE,iBAAA,IAAA;AACA,SAAA,IAAA;AACA,aAAA;;AAGF;AACE,iBAAA,IAAA;;AAGF,CAAA;AACE,gBAAA,IAAA;AACA,iBAAA,IAAA;AACA,iBAAA,IAAA;;AAGF;AACE,iBAAA,IAAA;;AAGF;AACE,gBAAA,IAAA;AACA,wBAAA,IAAA;AACA,uBAAA,IAAA;AACA,+BAAA,IAAA;;", "names": []}