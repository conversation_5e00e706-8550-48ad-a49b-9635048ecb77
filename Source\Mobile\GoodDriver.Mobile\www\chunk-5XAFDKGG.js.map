{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/haptic-ac164e4c.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { g as getCapacitor } from './capacitor-59395cbd.js';\nvar ImpactStyle;\n(function (ImpactStyle) {\n  /**\n   * A collision between large, heavy user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Heavy\"] = \"HEAVY\";\n  /**\n   * A collision between moderately sized user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Medium\"] = \"MEDIUM\";\n  /**\n   * A collision between small, light user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Light\"] = \"LIGHT\";\n})(ImpactStyle || (ImpactStyle = {}));\nvar NotificationType;\n(function (NotificationType) {\n  /**\n   * A notification feedback type indicating that a task has completed successfully\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Success\"] = \"SUCCESS\";\n  /**\n   * A notification feedback type indicating that a task has produced a warning\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Warning\"] = \"WARNING\";\n  /**\n   * A notification feedback type indicating that a task has failed\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Error\"] = \"ERROR\";\n})(NotificationType || (NotificationType = {}));\nconst HapticEngine = {\n  getEngine() {\n    const capacitor = getCapacitor();\n    if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('Haptics')) {\n      // Capacitor\n      return capacitor.Plugins.Haptics;\n    }\n    return undefined;\n  },\n  available() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return false;\n    }\n    const capacitor = getCapacitor();\n    /**\n     * Developers can manually import the\n     * Haptics plugin in their app which will cause\n     * getEngine to return the Haptics engine. However,\n     * the Haptics engine will throw an error if\n     * used in a web browser that does not support\n     * the Vibrate API. This check avoids that error\n     * if the browser does not support the Vibrate API.\n     */\n    if ((capacitor === null || capacitor === void 0 ? void 0 : capacitor.getPlatform()) === 'web') {\n      // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n      return typeof navigator !== 'undefined' && navigator.vibrate !== undefined;\n    }\n    return true;\n  },\n  impact(options) {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    engine.impact({\n      style: options.style\n    });\n  },\n  notification(options) {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    engine.notification({\n      type: options.type\n    });\n  },\n  selection() {\n    this.impact({\n      style: ImpactStyle.Light\n    });\n  },\n  selectionStart() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    engine.selectionStart();\n  },\n  selectionChanged() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    engine.selectionChanged();\n  },\n  selectionEnd() {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    engine.selectionEnd();\n  }\n};\n/**\n * Check to see if the Haptic Plugin is available\n * @return Returns `true` or false if the plugin is available\n */\nconst hapticAvailable = () => {\n  return HapticEngine.available();\n};\n/**\n * Trigger a selection changed haptic event. Good for one-time events\n * (not for gestures)\n */\nconst hapticSelection = () => {\n  hapticAvailable() && HapticEngine.selection();\n};\n/**\n * Tell the haptic engine that a gesture for a selection change is starting.\n */\nconst hapticSelectionStart = () => {\n  hapticAvailable() && HapticEngine.selectionStart();\n};\n/**\n * Tell the haptic engine that a selection changed during a gesture.\n */\nconst hapticSelectionChanged = () => {\n  hapticAvailable() && HapticEngine.selectionChanged();\n};\n/**\n * Tell the haptic engine we are done with a gesture. This needs to be\n * called lest resources are not properly recycled.\n */\nconst hapticSelectionEnd = () => {\n  hapticAvailable() && HapticEngine.selectionEnd();\n};\n/**\n * Use this to indicate success/failure/warning to the user.\n * options should be of the type `{ style: ImpactStyle.LIGHT }` (or `MEDIUM`/`HEAVY`)\n */\nconst hapticImpact = options => {\n  hapticAvailable() && HapticEngine.impact(options);\n};\nexport { ImpactStyle as I, hapticSelectionStart as a, hapticSelectionChanged as b, hapticSelection as c, hapticImpact as d, hapticSelectionEnd as h };"], "mappings": ";;;;;;;;;AAAA,IAII,aAqBA,kBAqBE,cA+EA,iBAOA,iBAMA,sBAMA,wBAOA,oBAOA;AA9JN;AAAA;AAAA;AAGA;AAEA,KAAC,SAAUA,cAAa;AAMtB,MAAAA,aAAY,OAAO,IAAI;AAMvB,MAAAA,aAAY,QAAQ,IAAI;AAMxB,MAAAA,aAAY,OAAO,IAAI;AAAA,IACzB,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAEpC,KAAC,SAAUC,mBAAkB;AAM3B,MAAAA,kBAAiB,SAAS,IAAI;AAM9B,MAAAA,kBAAiB,SAAS,IAAI;AAM9B,MAAAA,kBAAiB,OAAO,IAAI;AAAA,IAC9B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAC9C,IAAM,eAAe;AAAA,MACnB,YAAY;AACV,cAAM,YAAY,aAAa;AAC/B,YAAI,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,kBAAkB,SAAS,GAAG;AAEhG,iBAAO,UAAU,QAAQ;AAAA,QAC3B;AACA,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AACV,cAAM,SAAS,KAAK,UAAU;AAC9B,YAAI,CAAC,QAAQ;AACX,iBAAO;AAAA,QACT;AACA,cAAM,YAAY,aAAa;AAU/B,aAAK,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,YAAY,OAAO,OAAO;AAE7F,iBAAO,OAAO,cAAc,eAAe,UAAU,YAAY;AAAA,QACnE;AACA,eAAO;AAAA,MACT;AAAA,MACA,OAAO,SAAS;AACd,cAAM,SAAS,KAAK,UAAU;AAC9B,YAAI,CAAC,QAAQ;AACX;AAAA,QACF;AACA,eAAO,OAAO;AAAA,UACZ,OAAO,QAAQ;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,MACA,aAAa,SAAS;AACpB,cAAM,SAAS,KAAK,UAAU;AAC9B,YAAI,CAAC,QAAQ;AACX;AAAA,QACF;AACA,eAAO,aAAa;AAAA,UAClB,MAAM,QAAQ;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,MACA,YAAY;AACV,aAAK,OAAO;AAAA,UACV,OAAO,YAAY;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,MACA,iBAAiB;AACf,cAAM,SAAS,KAAK,UAAU;AAC9B,YAAI,CAAC,QAAQ;AACX;AAAA,QACF;AACA,eAAO,eAAe;AAAA,MACxB;AAAA,MACA,mBAAmB;AACjB,cAAM,SAAS,KAAK,UAAU;AAC9B,YAAI,CAAC,QAAQ;AACX;AAAA,QACF;AACA,eAAO,iBAAiB;AAAA,MAC1B;AAAA,MACA,eAAe;AACb,cAAM,SAAS,KAAK,UAAU;AAC9B,YAAI,CAAC,QAAQ;AACX;AAAA,QACF;AACA,eAAO,aAAa;AAAA,MACtB;AAAA,IACF;AAKA,IAAM,kBAAkB,MAAM;AAC5B,aAAO,aAAa,UAAU;AAAA,IAChC;AAKA,IAAM,kBAAkB,MAAM;AAC5B,sBAAgB,KAAK,aAAa,UAAU;AAAA,IAC9C;AAIA,IAAM,uBAAuB,MAAM;AACjC,sBAAgB,KAAK,aAAa,eAAe;AAAA,IACnD;AAIA,IAAM,yBAAyB,MAAM;AACnC,sBAAgB,KAAK,aAAa,iBAAiB;AAAA,IACrD;AAKA,IAAM,qBAAqB,MAAM;AAC/B,sBAAgB,KAAK,aAAa,aAAa;AAAA,IACjD;AAKA,IAAM,eAAe,aAAW;AAC9B,sBAAgB,KAAK,aAAa,OAAO,OAAO;AAAA,IAClD;AAAA;AAAA;", "names": ["ImpactStyle", "NotificationType"], "x_google_ignoreList": [0]}