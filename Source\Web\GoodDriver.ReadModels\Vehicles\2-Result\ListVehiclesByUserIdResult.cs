﻿using GoodDriver.ReadModels.Brands.Result;
using Rogerio.Cqrs.Requests;
using static GoodDriver.ReadModels.Brands.Result.BrandListResult;

namespace GoodDriver.ReadModels.Vehicles._2_Result
{
    public class ListVehiclesByUserIdResult : List<ListVehiclesByUserIdResult.VehicleResult>, IRequestResult
    {
        public ListVehiclesByUserIdResult()
        {
            
        }

        public ListVehiclesByUserIdResult(IList<VehicleResult> vehicles)
        {
            if (vehicles != null && vehicles.Count > 0)
            {
                this.AddRange(vehicles);
            }
        }

        //public List<VehicleResult> Vehicles { get; set; }

        public class VehicleResult
        {
            public string Id { get; set; }
            public string Plate { get; set; }
            public int Year { get; set; }
            public string BrandId { get; set; }
            public string BrandName { get; set; }
            public string ModelId { get; set; }
            public string ModelName { get; set; }
            public string Version { get; set; }
            public string PolicyNumber { get; set; }
            public DateTime CreatedOn { get; set; }
        }
    }    
}
