﻿using Rogerio.Cqrs.Commands;
using System.ComponentModel.DataAnnotations;

namespace GoodDriver.Contracts.Vehicles.Commands
{
    public class VehicleSyncCommand : ICommand
    {
        [Required]
        public string Id { get; set; }

        [Required]
        public string UserId { get; set; }

        [Required]
        public int BrandId { get; set; }

        //[Required]
        //public string BrandName { get; set; }

        [Required]
        public int ModelId { get; set; }

        //[Required]
        //public string ModelName { get; set; }

        [Required]
        public int Year { get; set; }

        [Required]
        [StringLength(20)]
        public string Plate { get; set; }

        //public string Version { get; set; }

        public string PolicyNumber { get; set; }

        public bool IsPrimary { get; set; }

        [Required]
        public string SyncStatus { get; set; }

        public DateTime CreatedOn { get; set; } = DateTime.Now;

        public DateTime? UpdatedOn { get; set; }
    }
}
