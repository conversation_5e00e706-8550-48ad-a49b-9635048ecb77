import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ToastService } from 'src/app/core/services/toast.service';
import { Journey } from 'src/app/core/models/journey.model';
import { JourneyStorageService } from 'src/app/core/services/journey-storage.service';
import { SessionService } from 'src/app/core/services/session.service';
import { AutoJourneyDetectionService } from 'src/app/core/services/auto-journey-detection.service';
import { JourneyInfoService } from 'src/app/core/services/journeyinfo.service';
import { VehicleService } from 'src/app/core/services/vehicle.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-journey',
  templateUrl: './journey.page.html',
  styleUrls: ['./journey.page.scss'],
  standalone: true,
  imports: [
    IonicModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule
  ]
})
export class JourneyPage implements OnInit, OnDestroy {

  username: string = 'Usuário';
  userId: string = '';

  journeys: Journey[] = [];
  tracking = false;
  currentJourneyId: string | null = null;

  // Novas propriedades para detecção automática
  trackingMode: 'manual' | 'auto' = 'manual';
  autoDetectionActive = false;
  journeyInProgress = false;

  // Vehicle properties
  hasVehicles: boolean = false;
  primaryVehicle: any = null;
  isLoading: boolean = true;

  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    private router: Router,
    private toastService: ToastService,
    private journeyService: JourneyStorageService,
    private sessionService: SessionService,
    private autoDetectionService: AutoJourneyDetectionService,
    private journeyInfoService: JourneyInfoService,
    private vehicleService: VehicleService
  ) {}

  async ngOnInit(): Promise<void> {
    this.isLoading = true;
    this.username = await this.sessionService.getUserName() || 'Usuário';
    this.userId = await this.sessionService.getUserId() || '';

    // Check if the user has any vehicles
    await this.checkVehicles();

    // Load journeys
    await this.loadJourneys();

    // Inscrever-se nos observables
    this.subscriptions.push(
      this.journeyInfoService.trackingActive$.subscribe(active => {
        this.tracking = active;
      })
    );

    this.subscriptions.push(
      this.autoDetectionService.detectionEnabled$.subscribe(enabled => {
        this.autoDetectionActive = enabled;
      })
    );

    this.subscriptions.push(
      this.autoDetectionService.journeyActive$.subscribe(active => {
        this.journeyInProgress = active;
      })
    );

    this.isLoading = false;
  }

  /**
   * Checks if the user has any vehicles registered
   */
  async checkVehicles() {
    try {
      this.hasVehicles = await this.vehicleService.hasVehicles(this.userId);

      if (this.hasVehicles) {
        this.primaryVehicle = await this.vehicleService.getPrimaryVehicle(this.userId);
      }
    } catch (error) {
      console.error('Error checking vehicles:', error);
      this.hasVehicles = false;
    }
  }

  ngOnDestroy(): void {
    // Cancelar todas as inscrições
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Manipula a mudança no modo de rastreamento (manual/automático)
   */
  onTrackingModeChange(event: any) {
    this.trackingMode = event.detail.value;

    // Se mudar para automático e já estiver com detecção ativa, mostrar mensagem
    if (this.trackingMode === 'auto' && this.autoDetectionActive) {
      this.toastService.showToast('Detecção automática já está ativa', 'info');
    }

    // Se mudar para manual e estiver com uma viagem manual em andamento, manter
    if (this.trackingMode === 'manual' && this.tracking) {
      this.toastService.showToast('Viagem manual em andamento', 'info');
    }
  }

  /**
   * Ativa/desativa a detecção automática de viagens
   */
  async toggleAutoDetection() {
    try {
      if (this.autoDetectionActive) {
        await this.autoDetectionService.enableAutoDetection();
      } else {
        await this.autoDetectionService.disableAutoDetection();
      }
    } catch (error) {
      console.error('Erro ao alternar detecção automática:', error);
      // Reverter o toggle em caso de erro
      this.autoDetectionActive = !this.autoDetectionActive;
    }
  }

  /**
   * Inicia uma viagem manualmente
   */
  async startTracking() {
    // Check if the user has any vehicles
    if (!this.hasVehicles) {
      await this.toastService.showToast('Você precisa cadastrar um veículo antes de iniciar uma viagem.', 'warning');
      this.router.navigate(['/new-vehicle']);
      return;
    }

    try {
      const id = await this.journeyService.startJourney();

      if (!id) {
        await this.toastService.showToast('Não foi possível iniciar a viagem. Verifique se você tem um veículo cadastrado.', 'warning');
        return;
      }

      this.currentJourneyId = id;
      await this.toastService.showToast('Viagem iniciada.', 'success');
    }
    catch (error) {
      console.error(error);
      await this.toastService.showToast('Erro ao iniciar viagem.', 'danger');
    }
  }

  /**
   * Finaliza uma viagem manualmente
   */
  async stopTracking() {
    if (!this.currentJourneyId) {
      await this.toastService.showToast('Nenhuma viagem em andamento.');
      return;
    }

    try {
      await this.journeyService.endJourney(this.currentJourneyId);
      this.currentJourneyId = null;
      await this.loadJourneys();
      await this.toastService.showToast('Viagem finalizada.');
    } catch (error) {
      console.error(error);
      await this.toastService.showToast('Erro ao finalizar viagem.');
    }
  }

  /**
   * Carrega as viagens do usuário
   */
  async loadJourneys() {
    this.journeys = await this.journeyService.getAllJourneysByUser(this.userId);
  }

  /**
   * Realiza logout
   */
  logout() {
    // Desativar detecção automática antes de fazer logout
    if (this.autoDetectionActive) {
      this.autoDetectionService.disableAutoDetection();
    }

    this.sessionService.clearSession();
    console.log('Logout realizado');
    this.router.navigate(['/login']);
  }

}
