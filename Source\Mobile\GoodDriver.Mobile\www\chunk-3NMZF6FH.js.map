{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/keyboard-52278bd7.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { K as Keyboard } from './keyboard-73175e24.js';\nimport './capacitor-59395cbd.js';\nimport './index-a5d50daf.js';\nconst KEYBOARD_DID_OPEN = 'ionKeyboardDidShow';\nconst KEYBOARD_DID_CLOSE = 'ionKeyboardDidHide';\nconst KEYBOARD_THRESHOLD = 150;\n// TODO(FW-2832): types\nlet previousVisualViewport = {};\nlet currentVisualViewport = {};\nlet keyboardOpen = false;\n/**\n * This is only used for tests\n */\nconst resetKeyboardAssist = () => {\n  previousVisualViewport = {};\n  currentVisualViewport = {};\n  keyboardOpen = false;\n};\nconst startKeyboardAssist = win => {\n  const nativeEngine = Keyboard.getEngine();\n  /**\n   * If the native keyboard plugin is available\n   * then we are running in a native environment. As a result\n   * we should only listen on the native events instead of\n   * using the Visual Viewport as the Ionic webview manipulates\n   * how it resizes such that the Visual Viewport API is not\n   * reliable here.\n   */\n  if (nativeEngine) {\n    startNativeListeners(win);\n  } else {\n    if (!win.visualViewport) {\n      return;\n    }\n    currentVisualViewport = copyVisualViewport(win.visualViewport);\n    win.visualViewport.onresize = () => {\n      trackViewportChanges(win);\n      if (keyboardDidOpen() || keyboardDidResize(win)) {\n        setKeyboardOpen(win);\n      } else if (keyboardDidClose(win)) {\n        setKeyboardClose(win);\n      }\n    };\n  }\n};\n/**\n * Listen for events fired by native keyboard plugin\n * in Capacitor/Cordova so devs only need to listen\n * in one place.\n */\nconst startNativeListeners = win => {\n  win.addEventListener('keyboardDidShow', ev => setKeyboardOpen(win, ev));\n  win.addEventListener('keyboardDidHide', () => setKeyboardClose(win));\n};\nconst setKeyboardOpen = (win, ev) => {\n  fireKeyboardOpenEvent(win, ev);\n  keyboardOpen = true;\n};\nconst setKeyboardClose = win => {\n  fireKeyboardCloseEvent(win);\n  keyboardOpen = false;\n};\n/**\n * Returns `true` if the `keyboardOpen` flag is not\n * set, the previous visual viewport width equal the current\n * visual viewport width, and if the scaled difference\n * of the previous visual viewport height minus the current\n * visual viewport height is greater than KEYBOARD_THRESHOLD\n *\n * We need to be able to accommodate users who have zooming\n * enabled in their browser (or have zoomed in manually) which\n * is why we take into account the current visual viewport's\n * scale value.\n */\nconst keyboardDidOpen = () => {\n  const scaledHeightDifference = (previousVisualViewport.height - currentVisualViewport.height) * currentVisualViewport.scale;\n  return !keyboardOpen && previousVisualViewport.width === currentVisualViewport.width && scaledHeightDifference > KEYBOARD_THRESHOLD;\n};\n/**\n * Returns `true` if the keyboard is open,\n * but the keyboard did not close\n */\nconst keyboardDidResize = win => {\n  return keyboardOpen && !keyboardDidClose(win);\n};\n/**\n * Determine if the keyboard was closed\n * Returns `true` if the `keyboardOpen` flag is set and\n * the current visual viewport height equals the\n * layout viewport height.\n */\nconst keyboardDidClose = win => {\n  return keyboardOpen && currentVisualViewport.height === win.innerHeight;\n};\n/**\n * Dispatch a keyboard open event\n */\nconst fireKeyboardOpenEvent = (win, nativeEv) => {\n  const keyboardHeight = nativeEv ? nativeEv.keyboardHeight : win.innerHeight - currentVisualViewport.height;\n  const ev = new CustomEvent(KEYBOARD_DID_OPEN, {\n    detail: {\n      keyboardHeight\n    }\n  });\n  win.dispatchEvent(ev);\n};\n/**\n * Dispatch a keyboard close event\n */\nconst fireKeyboardCloseEvent = win => {\n  const ev = new CustomEvent(KEYBOARD_DID_CLOSE);\n  win.dispatchEvent(ev);\n};\n/**\n * Given a window object, create a copy of\n * the current visual and layout viewport states\n * while also preserving the previous visual and\n * layout viewport states\n */\nconst trackViewportChanges = win => {\n  previousVisualViewport = Object.assign({}, currentVisualViewport);\n  currentVisualViewport = copyVisualViewport(win.visualViewport);\n};\n/**\n * Creates a deep copy of the visual viewport\n * at a given state\n */\nconst copyVisualViewport = visualViewport => {\n  return {\n    width: Math.round(visualViewport.width),\n    height: Math.round(visualViewport.height),\n    offsetTop: visualViewport.offsetTop,\n    offsetLeft: visualViewport.offsetLeft,\n    pageTop: visualViewport.pageTop,\n    pageLeft: visualViewport.pageLeft,\n    scale: visualViewport.scale\n  };\n};\nexport { KEYBOARD_DID_CLOSE, KEYBOARD_DID_OPEN, copyVisualViewport, keyboardDidClose, keyboardDidOpen, keyboardDidResize, resetKeyboardAssist, setKeyboardClose, setKeyboardOpen, startKeyboardAssist, trackViewportChanges };"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAMM,mBACA,oBACA,oBAEF,wBACA,uBACA,cAIE,qBAKA,qBAgCA,sBAIA,iBAIA,kBAgBA,iBAQA,mBASA,kBAMA,uBAYA,wBAUA,sBAQA;AAlIN;AAAA;AAGA;AACA;AACA;AACA,IAAM,oBAAoB;AAC1B,IAAM,qBAAqB;AAC3B,IAAM,qBAAqB;AAE3B,IAAI,yBAAyB,CAAC;AAC9B,IAAI,wBAAwB,CAAC;AAC7B,IAAI,eAAe;AAInB,IAAM,sBAAsB,MAAM;AAChC,+BAAyB,CAAC;AAC1B,8BAAwB,CAAC;AACzB,qBAAe;AAAA,IACjB;AACA,IAAM,sBAAsB,SAAO;AACjC,YAAM,eAAe,SAAS,UAAU;AASxC,UAAI,cAAc;AAChB,6BAAqB,GAAG;AAAA,MAC1B,OAAO;AACL,YAAI,CAAC,IAAI,gBAAgB;AACvB;AAAA,QACF;AACA,gCAAwB,mBAAmB,IAAI,cAAc;AAC7D,YAAI,eAAe,WAAW,MAAM;AAClC,+BAAqB,GAAG;AACxB,cAAI,gBAAgB,KAAK,kBAAkB,GAAG,GAAG;AAC/C,4BAAgB,GAAG;AAAA,UACrB,WAAW,iBAAiB,GAAG,GAAG;AAChC,6BAAiB,GAAG;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAMA,IAAM,uBAAuB,SAAO;AAClC,UAAI,iBAAiB,mBAAmB,QAAM,gBAAgB,KAAK,EAAE,CAAC;AACtE,UAAI,iBAAiB,mBAAmB,MAAM,iBAAiB,GAAG,CAAC;AAAA,IACrE;AACA,IAAM,kBAAkB,CAAC,KAAK,OAAO;AACnC,4BAAsB,KAAK,EAAE;AAC7B,qBAAe;AAAA,IACjB;AACA,IAAM,mBAAmB,SAAO;AAC9B,6BAAuB,GAAG;AAC1B,qBAAe;AAAA,IACjB;AAaA,IAAM,kBAAkB,MAAM;AAC5B,YAAM,0BAA0B,uBAAuB,SAAS,sBAAsB,UAAU,sBAAsB;AACtH,aAAO,CAAC,gBAAgB,uBAAuB,UAAU,sBAAsB,SAAS,yBAAyB;AAAA,IACnH;AAKA,IAAM,oBAAoB,SAAO;AAC/B,aAAO,gBAAgB,CAAC,iBAAiB,GAAG;AAAA,IAC9C;AAOA,IAAM,mBAAmB,SAAO;AAC9B,aAAO,gBAAgB,sBAAsB,WAAW,IAAI;AAAA,IAC9D;AAIA,IAAM,wBAAwB,CAAC,KAAK,aAAa;AAC/C,YAAM,iBAAiB,WAAW,SAAS,iBAAiB,IAAI,cAAc,sBAAsB;AACpG,YAAM,KAAK,IAAI,YAAY,mBAAmB;AAAA,QAC5C,QAAQ;AAAA,UACN;AAAA,QACF;AAAA,MACF,CAAC;AACD,UAAI,cAAc,EAAE;AAAA,IACtB;AAIA,IAAM,yBAAyB,SAAO;AACpC,YAAM,KAAK,IAAI,YAAY,kBAAkB;AAC7C,UAAI,cAAc,EAAE;AAAA,IACtB;AAOA,IAAM,uBAAuB,SAAO;AAClC,+BAAyB,OAAO,OAAO,CAAC,GAAG,qBAAqB;AAChE,8BAAwB,mBAAmB,IAAI,cAAc;AAAA,IAC/D;AAKA,IAAM,qBAAqB,oBAAkB;AAC3C,aAAO;AAAA,QACL,OAAO,KAAK,MAAM,eAAe,KAAK;AAAA,QACtC,QAAQ,KAAK,MAAM,eAAe,MAAM;AAAA,QACxC,WAAW,eAAe;AAAA,QAC1B,YAAY,eAAe;AAAA,QAC3B,SAAS,eAAe;AAAA,QACxB,UAAU,eAAe;AAAA,QACzB,OAAO,eAAe;AAAA,MACxB;AAAA,IACF;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}