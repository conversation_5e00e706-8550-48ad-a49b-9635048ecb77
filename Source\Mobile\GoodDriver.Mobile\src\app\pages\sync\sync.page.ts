import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { SyncService } from 'src/app/core/services/sync.service';
import { NetworkService } from 'src/app/core/services/network.service';
import { ToastService } from 'src/app/core/services/toast.service';
import { SessionService } from 'src/app/core/services/session.service';
import { SyncStatusComponent } from 'src/app/shared/components/sync-status/sync-status.component';
import { Subscription } from 'rxjs';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-sync',
  templateUrl: './sync.page.html',
  styleUrls: ['./sync.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, SyncStatusComponent, RouterModule]
})
export class SyncPage implements OnInit, OnD<PERSON>roy {
  isOnline = true;
  syncInProgress = false;
  pendingSyncCount = 0;
  lastSyncTime: Date | null = null;
  username: string = '';
  
  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    private syncService: SyncService,
    private networkService: NetworkService,
    private toastService: ToastService,
    private sessionService: SessionService
  ) {}

  async ngOnInit() {
    // Get username
    this.username = await this.sessionService.getUserName() || 'Usuário';
    
    // Subscribe to network status
    this.subscriptions.push(
      this.networkService.getOnlineStatus().subscribe(isOnline => {
        this.isOnline = isOnline;
      })
    );
    
    // Subscribe to sync status
    this.subscriptions.push(
      this.syncService.syncInProgress$.subscribe(inProgress => {
        this.syncInProgress = inProgress;
      })
    );
    
    // Subscribe to pending sync count
    this.subscriptions.push(
      this.syncService.pendingSyncCount$.subscribe(count => {
        this.pendingSyncCount = count;
      })
    );
    
    // Subscribe to last sync time
    this.subscriptions.push(
      this.syncService.lastSyncTime$.subscribe(time => {
        this.lastSyncTime = time;
      })
    );
  }
  
  ngOnDestroy() {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
  
  /**
   * Triggers a manual sync
   */
  async syncAll() {
    if (!this.isOnline) {
      this.toastService.showToast('Sem conexão com a internet. Não é possível sincronizar.', 'warning');
      return;
    }
    
    if (this.syncInProgress) {
      this.toastService.showToast('Sincronização já em andamento.', 'info');
      return;
    }
    
    await this.syncService.syncAll();
  }
  
  /**
   * Returns a formatted string for the last sync time
   */
  getLastSyncTimeFormatted(): string {
    if (!this.lastSyncTime) {
      return 'Nunca';
    }
    
    return this.lastSyncTime.toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
}
