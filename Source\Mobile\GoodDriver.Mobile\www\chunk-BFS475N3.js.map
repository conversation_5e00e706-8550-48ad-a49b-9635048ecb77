{"version": 3, "sources": ["node_modules/@ionic/core/components/capacitor.js", "node_modules/@ionic/core/components/keyboard.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index6.js';\nconst getCapacitor = () => {\n  if (win !== undefined) {\n    return win.Capacitor;\n  }\n  return undefined;\n};\nexport { getCapacitor as g };", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { g as getCapacitor } from './capacitor.js';\nvar ExceptionCode;\n(function (ExceptionCode) {\n  /**\n   * API is not implemented.\n   *\n   * This usually means the API can't be used because it is not implemented for\n   * the current platform.\n   */\n  ExceptionCode[\"Unimplemented\"] = \"UNIMPLEMENTED\";\n  /**\n   * API is not available.\n   *\n   * This means the API can't be used right now because:\n   *   - it is currently missing a prerequisite, such as network connectivity\n   *   - it requires a particular platform or browser version\n   */\n  ExceptionCode[\"Unavailable\"] = \"UNAVAILABLE\";\n})(ExceptionCode || (ExceptionCode = {}));\nvar KeyboardResize;\n(function (KeyboardResize) {\n  /**\n   * Only the `body` HTML element will be resized.\n   * Relative units are not affected, because the viewport does not change.\n   *\n   * @since 1.0.0\n   */\n  KeyboardResize[\"Body\"] = \"body\";\n  /**\n   * Only the `ion-app` HTML element will be resized.\n   * Use it only for Ionic Framework apps.\n   *\n   * @since 1.0.0\n   */\n  KeyboardResize[\"Ionic\"] = \"ionic\";\n  /**\n   * The whole native Web View will be resized when the keyboard shows/hides.\n   * This affects the `vh` relative unit.\n   *\n   * @since 1.0.0\n   */\n  KeyboardResize[\"Native\"] = \"native\";\n  /**\n   * Neither the app nor the Web View are resized.\n   *\n   * @since 1.0.0\n   */\n  KeyboardResize[\"None\"] = \"none\";\n})(KeyboardResize || (KeyboardResize = {}));\nconst Keyboard = {\n  getEngine() {\n    const capacitor = getCapacitor();\n    if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('Keyboard')) {\n      return capacitor.Plugins.Keyboard;\n    }\n    return undefined;\n  },\n  getResizeMode() {\n    const engine = this.getEngine();\n    if (!(engine === null || engine === void 0 ? void 0 : engine.getResizeMode)) {\n      return Promise.resolve(undefined);\n    }\n    return engine.getResizeMode().catch(e => {\n      if (e.code === ExceptionCode.Unimplemented) {\n        // If the native implementation is not available\n        // we treat it the same as if the plugin is not available.\n        return undefined;\n      }\n      throw e;\n    });\n  }\n};\nexport { Keyboard as K, KeyboardResize as a };"], "mappings": ";;;;;;;;;AAAA,IAIM;AAJN;AAAA;AAAA;AAGA;AACA,IAAM,eAAe,MAAM;AACzB,UAAI,QAAQ,QAAW;AACrB,eAAO,IAAI;AAAA,MACb;AACA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACTA,IAII,eAkBA,gBA8BE;AApDN;AAAA;AAAA;AAGA;AAEA,KAAC,SAAUA,gBAAe;AAOxB,MAAAA,eAAc,eAAe,IAAI;AAQjC,MAAAA,eAAc,aAAa,IAAI;AAAA,IACjC,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAExC,KAAC,SAAUC,iBAAgB;AAOzB,MAAAA,gBAAe,MAAM,IAAI;AAOzB,MAAAA,gBAAe,OAAO,IAAI;AAO1B,MAAAA,gBAAe,QAAQ,IAAI;AAM3B,MAAAA,gBAAe,MAAM,IAAI;AAAA,IAC3B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAM,WAAW;AAAA,MACf,YAAY;AACV,cAAM,YAAY,aAAa;AAC/B,YAAI,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,kBAAkB,UAAU,GAAG;AACjG,iBAAO,UAAU,QAAQ;AAAA,QAC3B;AACA,eAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AACd,cAAM,SAAS,KAAK,UAAU;AAC9B,YAAI,EAAE,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,gBAAgB;AAC3E,iBAAO,QAAQ,QAAQ,MAAS;AAAA,QAClC;AACA,eAAO,OAAO,cAAc,EAAE,MAAM,OAAK;AACvC,cAAI,EAAE,SAAS,cAAc,eAAe;AAG1C,mBAAO;AAAA,UACT;AACA,gBAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA;", "names": ["ExceptionCode", "KeyboardResize"], "x_google_ignoreList": [0, 1]}