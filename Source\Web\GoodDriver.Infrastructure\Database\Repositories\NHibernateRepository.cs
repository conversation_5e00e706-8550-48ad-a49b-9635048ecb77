﻿using GoodDriver.Domain;
using Microsoft.Extensions.Configuration;
using NHibernate;
using NHibernate.Linq;
using Rogerio.NHibernate;

namespace GoodDriver.Infrastructure.Database.Repositories
{
	public class NHibernateRepository<T> : NHibernateDbInterceptor, IRepository<T> where T : class, new()
    {
        public NHibernateRepository(IConfiguration configuration, IUnitOfWorkFactory<ISession> unitOfWorkFactory)
            : base(configuration, unitOfWorkFactory)
        {
        }

        public virtual Task<object> AddAsync(T instance)
        {
            return AddAsync(instance, autoFlush: true);
        }

        protected virtual async Task<object> AddAsync<P>(P instance, bool autoFlush = true) where P : class, new()
        {
            using IUnitOfWork<ISession> unitOfWork = base.UnitOfWorkFactory.Get();
            ISession session = unitOfWork.Context;
            object result = await session.SaveAsync(instance);
            if (autoFlush)
            {
                session.Flush();
            }

            return result;
        }

        public virtual Task UpdateAsync(T instance)
        {
            return UpdateAsync(instance, autoFlush: true);
        }

        protected virtual async Task UpdateAsync<P>(P instance, bool autoFlush = true) where P : class, new()
        {
            using IUnitOfWork<ISession> unitOfWork = base.UnitOfWorkFactory.Get();
            ISession session = unitOfWork.Context;
            await session.UpdateAsync(instance);
            if (autoFlush)
            {
                session.Flush();
            }
        }

        public virtual Task DeleteAsync(T instance)
        {
            return DeleteAsync(instance, autoFlush: true);
        }

        protected virtual async Task DeleteAsync<P>(P instance, bool autoFlush = true) where P : class, new()
        {
            using IUnitOfWork<ISession> unitOfWork = base.UnitOfWorkFactory.Get();
            ISession session = unitOfWork.Context;
            await session.DeleteAsync(instance);
            if (autoFlush)
            {
                session.Flush();
            }
        }

        public virtual async Task<IList<T>> FindAllAsync()
        {
            using IUnitOfWork<ISession> unitOfWork = base.UnitOfWorkFactory.Get();
            return await unitOfWork.Context.Query<T>().ToListAsync();
        }

        public virtual async Task<IList<T>> FindAsyncBy(ISpecification<T> specification)
        {
            using IUnitOfWork<ISession> unitOfWork = base.UnitOfWorkFactory.Get();
            IQueryable<T> source = unitOfWork.Context.Query<T>();
            source = source.Where(specification.SatisfiedBy());
            if (specification.PageSize > 0)
            {
                if (specification.Page > 1)
                {
                    source = source.Skip((specification.Page.Value - 1) * specification.PageSize.Value);
                }

                source = source.Take(specification.PageSize.Value);
            }

            return await source.ToListAsync();
        }

        public virtual async Task<T> GetAsyncById(object id)
        {
            using IUnitOfWork<ISession> unitOfWork = base.UnitOfWorkFactory.Get();
            try
            {
                return await unitOfWork.Context.LoadAsync<T>(id, LockMode.None);
            }
            catch (ObjectNotFoundException)
            {
                return null;
            }
        }
    }
}
