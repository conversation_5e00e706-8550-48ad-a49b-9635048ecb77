import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastController } from '@ionic/angular';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { AuthService } from 'src/app/core/services/auth.service';
import { LoginRequestDto } from 'src/app/core/dtos/auth/login-requestDto';
import { HttpClientModule } from '@angular/common/http';
import { ToastService } from 'src/app/core/services/toast.service';
import { RouterModule } from '@angular/router';
import { SessionService } from 'src/app/core/services/session.service';




@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
  standalone: true,
  providers: [AuthService],
  imports: [
    IonicModule, 
    CommonModule,
    ReactiveFormsModule,
    HttpClientModule,
    RouterModule
  ]
})
export class LoginPage implements OnInit 
{
    loginForm: FormGroup;
  

    constructor(
      private formBuilder: FormBuilder,
      private router: Router,      
      private authService: AuthService,
      private toastService: ToastService,
      private sessionService: SessionService
    ) {
      this.loginForm = this.formBuilder.group({
        email: ['', [Validators.required, Validators.email]],
        password: ['', [Validators.required]]
      });
    }

    ngOnInit() {

      fetch('https://192.168.1.68:7226/api/user/authentication', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: '<EMAIL>', password: '123456' })
      })
      .then(res => res.json())
      .then(data => console.log('Fetch sucesso:', data))
      .catch(err => console.error('Erro no fetch direto:', err));
    }

    async login() {
      if (this.loginForm.valid) {
        const loginData = this.loginForm.value;
    
        const request: LoginRequestDto = {
          email: loginData.email,
          password: loginData.password
        };
    
        this.authService.login(request).subscribe({
          next: async (response) => {
            console.log('Login realizado com sucesso!', response);
            this.toastService.showToast('Login realizado com sucesso!', 'success');
            await this.sessionService.setSession(response.token, response.userId, response.userName, response.userEmail);
            this.router.navigate(['/tabs']);
          },
          error: (err) => {
            console.error('Erro no login:', err);

            if (err.error) {
              console.error('Erro retornado pela API:', err.error);
            }

            if (err.status) {
              console.error('Código de status:', err.status);
            }

            console.error('Detalhes:', JSON.stringify(err, null, 2)); // mostra como string formatada
            this.toastService.showToast('Falha no login. Verifique seu email e senha.', 'danger');
          }
        });
      } else {
        console.log('Formulário inválido');
        this.toastService.showToast('Preencha todos os campos corretamente.', 'warning');
      }
    }
    

    goToSignup() {
      this.router.navigate(['/signup']);
    }
}