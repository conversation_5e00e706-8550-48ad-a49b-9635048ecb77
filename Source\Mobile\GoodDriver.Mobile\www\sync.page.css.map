{"version": 3, "sources": ["src/app/pages/sync/sync.page.scss"], "sourcesContent": ["ion-card {\n  border-radius: var(--app-border-radius-md, 8px);\n  box-shadow: var(--app-card-shadow, 0 2px 4px rgba(0, 0, 0, 0.1)) !important;\n  margin: var(--app-spacing-md, 16px) var(--app-spacing-md, 16px) var(--app-spacing-lg, 24px);\n  \n  ion-card-header {\n    padding: var(--app-spacing-md, 16px);\n    \n    ion-card-title {\n      font-size: 1.25rem;\n      font-weight: var(--app-heading-font-weight, 600);\n      display: flex;\n      align-items: center;\n      \n      ion-icon {\n        margin-right: 8px;\n        font-size: 1.5rem;\n      }\n    }\n    \n    ion-card-subtitle {\n      font-size: 0.9rem;\n      color: var(--ion-color-medium);\n      margin-top: var(--app-spacing-xs, 4px);\n    }\n  }\n  \n  ion-card-content {\n    padding: var(--app-spacing-md, 16px);\n  }\n}\n\nion-item {\n  --padding-start: var(--app-spacing-md, 16px);\n  --padding-end: var(--app-spacing-md, 16px);\n  --inner-padding-end: 0;\n  \n  h2 {\n    font-weight: 500;\n    font-size: 1rem;\n  }\n  \n  p {\n    font-size: 0.9rem;\n    color: var(--ion-color-medium);\n  }\n  \n  ion-icon {\n    font-size: 1.5rem;\n  }\n}\n\nion-button {\n  margin: var(--app-spacing-md, 16px) 0 0;\n  height: 48px;\n  \n  ion-icon {\n    margin-right: 8px;\n  }\n}\n\n.sync-status {\n  margin: var(--app-spacing-md, 16px) 0;\n}\n"], "mappings": ";AAAA;AACE,iBAAA,IAAA,sBAAA,EAAA;AACA,cAAA,IAAA,iBAAA,EAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,UAAA,IAAA,gBAAA,EAAA,MAAA,IAAA,gBAAA,EAAA,MAAA,IAAA,gBAAA,EAAA;;AAEA,SAAA;AACE,WAAA,IAAA,gBAAA,EAAA;;AAEA,SAAA,gBAAA;AACE,aAAA;AACA,eAAA,IAAA,yBAAA,EAAA;AACA,WAAA;AACA,eAAA;;AAEA,SAAA,gBAAA,eAAA;AACE,gBAAA;AACA,aAAA;;AAIJ,SAAA,gBAAA;AACE,aAAA;AACA,SAAA,IAAA;AACA,cAAA,IAAA,gBAAA,EAAA;;AAIJ,SAAA;AACE,WAAA,IAAA,gBAAA,EAAA;;AAIJ;AACE,mBAAA,IAAA,gBAAA,EAAA;AACA,iBAAA,IAAA,gBAAA,EAAA;AACA,uBAAA;;AAEA,SAAA;AACE,eAAA;AACA,aAAA;;AAGF,SAAA;AACE,aAAA;AACA,SAAA,IAAA;;AAGF,SAAA;AACE,aAAA;;AAIJ;AACE,UAAA,IAAA,gBAAA,EAAA,MAAA,EAAA;AACA,UAAA;;AAEA,WAAA;AACE,gBAAA;;AAIJ,CAAA;AACE,UAAA,IAAA,gBAAA,EAAA,MAAA;;", "names": []}