﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Database\Repositories\NHibernateRepository.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Database\Repositories\VehicleNHRepository.cs~RFa56de34.TMP" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="CacheService\" />
    <Folder Include="Notifications\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FluentNHibernate" Version="3.4.1" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.4" />
    <PackageReference Include="NHibernate" Version="5.5.2" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\GoodDriver.CommandHandlers\GoodDriver.CommandHandlers.csproj" />
    <ProjectReference Include="..\GoodDriver.Domain\GoodDriver.Domain.csproj" />
    <ProjectReference Include="..\GoodDriver.EventHandlers\GoodDriver.EventHandlers.csproj" />
    <ProjectReference Include="..\GoodDriver.ReadModels\GoodDriver.ReadModels.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Rogerio.Cqrs">
      <HintPath>..\..\..\..\RogerioFramework\RogerioIT\Rogerio.Cqrs\bin\Debug\net8.0\Rogerio.Cqrs.dll</HintPath>
    </Reference>
    <Reference Include="Rogerio.Data">
      <HintPath>..\..\..\..\RogerioFramework\RogerioIT\Rogerio.Data\bin\Debug\net8.0\Rogerio.Data.dll</HintPath>
    </Reference>
    <Reference Include="Rogerio.Data.NHibernate">
      <HintPath>..\..\..\..\RogerioFramework\RogerioIT\Rogerio.Data.NHibernate\bin\Debug\net8.0\Rogerio.Data.NHibernate.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
