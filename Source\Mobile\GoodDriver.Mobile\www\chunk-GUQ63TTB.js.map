{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/framework-delegate-56b467ad.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as componentOnReady } from './helpers-d94bc8ad.js';\n\n// TODO(FW-2832): types\nconst attachComponent = async (delegate, container, component, cssClasses, componentProps, inline) => {\n  var _a;\n  if (delegate) {\n    return delegate.attachViewToDom(container, component, componentProps, cssClasses);\n  }\n  if (!inline && typeof component !== 'string' && !(component instanceof HTMLElement)) {\n    throw new Error('framework delegate is missing');\n  }\n  const el = typeof component === 'string' ? (_a = container.ownerDocument) === null || _a === void 0 ? void 0 : _a.createElement(component) : component;\n  if (cssClasses) {\n    cssClasses.forEach(c => el.classList.add(c));\n  }\n  if (componentProps) {\n    Object.assign(el, componentProps);\n  }\n  container.appendChild(el);\n  await new Promise(resolve => componentOnReady(el, resolve));\n  return el;\n};\nconst detachComponent = (delegate, element) => {\n  if (element) {\n    if (delegate) {\n      const container = element.parentElement;\n      return delegate.removeViewFromDom(container, element);\n    }\n    element.remove();\n  }\n  return Promise.resolve();\n};\nconst CoreDelegate = () => {\n  let BaseComponent;\n  let Reference;\n  const attachViewToDom = async (parentElement, userComponent, userComponentProps = {}, cssClasses = []) => {\n    var _a, _b;\n    BaseComponent = parentElement;\n    let ChildComponent;\n    /**\n     * If passing in a component via the `component` props\n     * we need to append it inside of our overlay component.\n     */\n    if (userComponent) {\n      /**\n       * If passing in the tag name, create\n       * the element otherwise just get a reference\n       * to the component.\n       */\n      const el = typeof userComponent === 'string' ? (_a = BaseComponent.ownerDocument) === null || _a === void 0 ? void 0 : _a.createElement(userComponent) : userComponent;\n      /**\n       * Add any css classes passed in\n       * via the cssClasses prop on the overlay.\n       */\n      cssClasses.forEach(c => el.classList.add(c));\n      /**\n       * Add any props passed in\n       * via the componentProps prop on the overlay.\n       */\n      Object.assign(el, userComponentProps);\n      /**\n       * Finally, append the component\n       * inside of the overlay component.\n       */\n      BaseComponent.appendChild(el);\n      ChildComponent = el;\n      await new Promise(resolve => componentOnReady(el, resolve));\n    } else if (BaseComponent.children.length > 0 && (BaseComponent.tagName === 'ION-MODAL' || BaseComponent.tagName === 'ION-POPOVER')) {\n      /**\n       * The delegate host wrapper el is only needed for modals and popovers\n       * because they allow the dev to provide custom content to the overlay.\n       */\n      const root = ChildComponent = BaseComponent.children[0];\n      if (!root.classList.contains('ion-delegate-host')) {\n        /**\n         * If the root element is not a delegate host, it means\n         * that the overlay has not been presented yet and we need\n         * to create the containing element with the specified classes.\n         */\n        const el = (_b = BaseComponent.ownerDocument) === null || _b === void 0 ? void 0 : _b.createElement('div');\n        // Add a class to track if the root element was created by the delegate.\n        el.classList.add('ion-delegate-host');\n        cssClasses.forEach(c => el.classList.add(c));\n        // Move each child from the original template to the new parent element.\n        el.append(...BaseComponent.children);\n        // Append the new parent element to the original parent element.\n        BaseComponent.appendChild(el);\n        /**\n         * Update the ChildComponent to be the\n         * newly created div in the event that one\n         * does not already exist.\n         */\n        ChildComponent = el;\n      }\n    }\n    /**\n     * Get the root of the app and\n     * add the overlay there.\n     */\n    const app = document.querySelector('ion-app') || document.body;\n    /**\n     * Create a placeholder comment so that\n     * we can return this component to where\n     * it was previously.\n     */\n    Reference = document.createComment('ionic teleport');\n    BaseComponent.parentNode.insertBefore(Reference, BaseComponent);\n    app.appendChild(BaseComponent);\n    /**\n     * We return the child component rather than the overlay\n     * reference itself since modal and popover will\n     * use this to wait for any Ionic components in the child view\n     * to be ready (i.e. componentOnReady) when using the\n     * lazy loaded component bundle.\n     *\n     * However, we fall back to returning BaseComponent\n     * in the event that a modal or popover is presented\n     * with no child content.\n     */\n    return ChildComponent !== null && ChildComponent !== void 0 ? ChildComponent : BaseComponent;\n  };\n  const removeViewFromDom = () => {\n    /**\n     * Return component to where it was previously in the DOM.\n     */\n    if (BaseComponent && Reference) {\n      Reference.parentNode.insertBefore(BaseComponent, Reference);\n      Reference.remove();\n    }\n    return Promise.resolve();\n  };\n  return {\n    attachViewToDom,\n    removeViewFromDom\n  };\n};\nexport { CoreDelegate as C, attachComponent as a, detachComponent as d };"], "mappings": ";;;;;;;;;;AAAA,IAMM,iBAmBA,iBAUA;AAnCN;AAAA;AAAA;AAGA;AAGA,IAAM,kBAAkB,CAAO,UAAU,WAAW,WAAW,YAAY,gBAAgB,WAAW;AACpG,UAAI;AACJ,UAAI,UAAU;AACZ,eAAO,SAAS,gBAAgB,WAAW,WAAW,gBAAgB,UAAU;AAAA,MAClF;AACA,UAAI,CAAC,UAAU,OAAO,cAAc,YAAY,EAAE,qBAAqB,cAAc;AACnF,cAAM,IAAI,MAAM,+BAA+B;AAAA,MACjD;AACA,YAAM,KAAK,OAAO,cAAc,YAAY,KAAK,UAAU,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,SAAS,IAAI;AAC7I,UAAI,YAAY;AACd,mBAAW,QAAQ,OAAK,GAAG,UAAU,IAAI,CAAC,CAAC;AAAA,MAC7C;AACA,UAAI,gBAAgB;AAClB,eAAO,OAAO,IAAI,cAAc;AAAA,MAClC;AACA,gBAAU,YAAY,EAAE;AACxB,YAAM,IAAI,QAAQ,aAAW,iBAAiB,IAAI,OAAO,CAAC;AAC1D,aAAO;AAAA,IACT;AACA,IAAM,kBAAkB,CAAC,UAAU,YAAY;AAC7C,UAAI,SAAS;AACX,YAAI,UAAU;AACZ,gBAAM,YAAY,QAAQ;AAC1B,iBAAO,SAAS,kBAAkB,WAAW,OAAO;AAAA,QACtD;AACA,gBAAQ,OAAO;AAAA,MACjB;AACA,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,IAAM,eAAe,MAAM;AACzB,UAAI;AACJ,UAAI;AACJ,YAAM,kBAAkB,CAAO,IAAe,OAA4D,sBAA3E,IAAe,IAA4D,mBAA3E,eAAe,eAAe,qBAAqB,CAAC,GAAG,aAAa,CAAC,GAAM;AACxG,YAAI,IAAI;AACR,wBAAgB;AAChB,YAAI;AAKJ,YAAI,eAAe;AAMjB,gBAAM,KAAK,OAAO,kBAAkB,YAAY,KAAK,cAAc,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,aAAa,IAAI;AAKzJ,qBAAW,QAAQ,OAAK,GAAG,UAAU,IAAI,CAAC,CAAC;AAK3C,iBAAO,OAAO,IAAI,kBAAkB;AAKpC,wBAAc,YAAY,EAAE;AAC5B,2BAAiB;AACjB,gBAAM,IAAI,QAAQ,aAAW,iBAAiB,IAAI,OAAO,CAAC;AAAA,QAC5D,WAAW,cAAc,SAAS,SAAS,MAAM,cAAc,YAAY,eAAe,cAAc,YAAY,gBAAgB;AAKlI,gBAAM,OAAO,iBAAiB,cAAc,SAAS,CAAC;AACtD,cAAI,CAAC,KAAK,UAAU,SAAS,mBAAmB,GAAG;AAMjD,kBAAM,MAAM,KAAK,cAAc,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,KAAK;AAEzG,eAAG,UAAU,IAAI,mBAAmB;AACpC,uBAAW,QAAQ,OAAK,GAAG,UAAU,IAAI,CAAC,CAAC;AAE3C,eAAG,OAAO,GAAG,cAAc,QAAQ;AAEnC,0BAAc,YAAY,EAAE;AAM5B,6BAAiB;AAAA,UACnB;AAAA,QACF;AAKA,cAAM,MAAM,SAAS,cAAc,SAAS,KAAK,SAAS;AAM1D,oBAAY,SAAS,cAAc,gBAAgB;AACnD,sBAAc,WAAW,aAAa,WAAW,aAAa;AAC9D,YAAI,YAAY,aAAa;AAY7B,eAAO,mBAAmB,QAAQ,mBAAmB,SAAS,iBAAiB;AAAA,MACjF;AACA,YAAM,oBAAoB,MAAM;AAI9B,YAAI,iBAAiB,WAAW;AAC9B,oBAAU,WAAW,aAAa,eAAe,SAAS;AAC1D,oBAAU,OAAO;AAAA,QACnB;AACA,eAAO,QAAQ,QAAQ;AAAA,MACzB;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}