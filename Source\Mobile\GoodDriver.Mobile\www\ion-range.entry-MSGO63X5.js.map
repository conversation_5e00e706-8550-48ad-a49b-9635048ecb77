{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-range.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { f as findClosestIonContent, d as disableContentScrollY, r as resetContentScrollY } from './index-9a17db3d.js';\nimport { l as isSafeNumber, j as clamp, e as debounceEvent, i as inheritAriaAttributes, d as renderHiddenInput } from './helpers-d94bc8ad.js';\nimport { p as printIonWarning } from './index-cfd9c1f2.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { h as hostContext, c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nfunction getDecimalPlaces(n) {\n  if (!isSafeNumber(n)) return 0;\n  if (n % 1 === 0) return 0;\n  return n.toString().split('.')[1].length;\n}\n/**\n * Fixes floating point rounding errors in a result by rounding\n * to the same specificity, or number of decimal places (*not*\n * significant figures) as provided reference numbers. If multiple\n * references are provided, the highest number of decimal places\n * between them will be used.\n *\n * The main use case is when numbers x and y are added to produce n,\n * but x and y are floats, so n may have rounding errors (such as\n * 3.1000000004 instead of 3.1). As long as only addition/subtraction\n * occurs between x and y, the specificity of the result will never\n * increase, so x and y should be passed in as the references.\n *\n * If multiplication, division, or other operations were used to\n * calculate n, the rounded result may have less specificity than\n * desired. For example, 1 / 3 = 0.33333(...), but\n * roundToMaxDecimalPlaces((1 / 3), 1, 3) will return 0, since both\n * 1 and 3 are whole numbers.\n *\n * Note that extremely precise reference numbers may lead to rounding\n * errors not being trimmed, due to the error result having the same or\n * fewer decimal places as the reference(s). This is acceptable as we\n * would not be able to tell the difference between a rounding error\n * and correct value in this case, but it does mean there is an implicit\n * precision limit. If precision that high is needed, it is recommended\n * to use a third party data type designed to handle floating point\n * errors instead.\n *\n * @param n The number to round.\n * @param references Number(s) used to calculate n, or that should otherwise\n * be used as a reference for the desired specificity.\n */\nfunction roundToMaxDecimalPlaces(n, ...references) {\n  if (!isSafeNumber(n)) return 0;\n  const maxPlaces = Math.max(...references.map(r => getDecimalPlaces(r)));\n  return Number(n.toFixed(maxPlaces));\n}\nconst rangeIosCss = \":host{--knob-handle-size:calc(var(--knob-size) * 2);display:-ms-flexbox;display:flex;position:relative;-ms-flex:3;flex:3;-ms-flex-align:center;align-items:center;font-family:var(--ion-font-family, inherit);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.range-disabled){pointer-events:none}::slotted(ion-label){-ms-flex:initial;flex:initial}::slotted(ion-icon[slot]){font-size:24px}.range-slider{position:relative;-ms-flex:1;flex:1;width:100%;height:var(--height);contain:size layout style;cursor:-webkit-grab;cursor:grab;-ms-touch-action:pan-y;touch-action:pan-y}:host(.range-pressed) .range-slider{cursor:-webkit-grabbing;cursor:grabbing}.range-pin{position:absolute;background:var(--ion-color-base);color:var(--ion-color-contrast);text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box}.range-knob-handle{top:calc((var(--height) - var(--knob-handle-size)) / 2);-webkit-margin-start:calc(0px - var(--knob-handle-size) / 2);margin-inline-start:calc(0px - var(--knob-handle-size) / 2);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-pack:center;justify-content:center;width:var(--knob-handle-size);height:var(--knob-handle-size);text-align:center}.range-knob-handle{inset-inline-start:0}:host-context([dir=rtl]) .range-knob-handle{left:unset}[dir=rtl] .range-knob-handle{left:unset}@supports selector(:dir(rtl)){.range-knob-handle:dir(rtl){left:unset}}.range-knob-handle:active,.range-knob-handle:focus{outline:none}.range-bar-container{border-radius:var(--bar-border-radius);top:calc((var(--height) - var(--bar-height)) / 2);position:absolute;width:100%;height:var(--bar-height)}.range-bar-container{inset-inline-start:0}:host-context([dir=rtl]) .range-bar-container{left:unset}[dir=rtl] .range-bar-container{left:unset}@supports selector(:dir(rtl)){.range-bar-container:dir(rtl){left:unset}}.range-bar{border-radius:var(--bar-border-radius);position:absolute;width:100%;height:var(--bar-height);background:var(--bar-background);pointer-events:none}.range-knob{border-radius:var(--knob-border-radius);top:calc(50% - var(--knob-size) / 2);position:absolute;width:var(--knob-size);height:var(--knob-size);background:var(--knob-background);-webkit-box-shadow:var(--knob-box-shadow);box-shadow:var(--knob-box-shadow);z-index:2;pointer-events:none}.range-knob{inset-inline-start:calc(50% - var(--knob-size) / 2)}:host-context([dir=rtl]) .range-knob{left:unset}[dir=rtl] .range-knob{left:unset}@supports selector(:dir(rtl)){.range-knob:dir(rtl){left:unset}}:host(.range-pressed) .range-bar-active{will-change:left, right}:host(.in-item){width:100%}:host([slot=start]),:host([slot=end]){width:auto}:host(.in-item) ::slotted(ion-label){-ms-flex-item-align:center;align-self:center}.range-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit}::slotted([slot=label]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center}:host(.range-label-placement-start) .range-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.range-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-end) .range-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.range-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.range-label-placement-stacked) .range-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:stretch;align-items:stretch}:host(.range-label-placement-stacked) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top;-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host-context([dir=rtl]):host(.range-label-placement-stacked) .label-text-wrapper,:host-context([dir=rtl]).range-label-placement-stacked .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.range-label-placement-stacked:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.in-item.range-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.range-label-placement-stacked) .native-wrapper{margin-bottom:0px}:host{--knob-border-radius:50%;--knob-background:#ffffff;--knob-box-shadow:0px 0.5px 4px rgba(0, 0, 0, 0.12), 0px 6px 13px rgba(0, 0, 0, 0.12);--knob-size:26px;--bar-height:4px;--bar-background:var(--ion-color-step-900, var(--ion-background-color-step-900, #e6e6e6));--bar-background-active:var(--ion-color-primary, #0054e9);--bar-border-radius:2px;--height:42px}:host(.range-item-start-adjustment){-webkit-padding-start:24px;padding-inline-start:24px}:host(.range-item-end-adjustment){-webkit-padding-end:24px;padding-inline-end:24px}:host(.ion-color) .range-bar-active,:host(.ion-color) .range-tick-active{background:var(--ion-color-base)}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-has-pin:not(.range-label-placement-stacked)){padding-top:calc(8px + 0.75rem)}:host(.range-has-pin.range-label-placement-stacked) .label-text-wrapper{margin-bottom:calc(8px + 0.75rem)}.range-bar-active{bottom:0;width:auto;background:var(--bar-background-active)}.range-bar-active.has-ticks{border-radius:0;-webkit-margin-start:-2px;margin-inline-start:-2px;-webkit-margin-end:-2px;margin-inline-end:-2px}.range-tick{-webkit-margin-start:-2px;margin-inline-start:-2px;border-radius:0;position:absolute;top:17px;width:4px;height:8px;background:var(--ion-color-step-900, var(--ion-background-color-step-900, #e6e6e6));pointer-events:none}.range-tick-active{background:var(--bar-background-active)}.range-pin{-webkit-transform:translate3d(0,  100%,  0) scale(0.01);transform:translate3d(0,  100%,  0) scale(0.01);-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;min-width:28px;-webkit-transition:-webkit-transform 120ms ease;transition:-webkit-transform 120ms ease;transition:transform 120ms ease;transition:transform 120ms ease, -webkit-transform 120ms ease;background:transparent;color:var(--ion-text-color, #000);font-size:0.75rem;text-align:center}.range-knob-pressed .range-pin,.range-knob-handle.ion-focused .range-pin{-webkit-transform:translate3d(0, calc(-100% + 11px), 0) scale(1);transform:translate3d(0, calc(-100% + 11px), 0) scale(1)}:host(.range-disabled){opacity:0.3}\";\nconst IonRangeIosStyle0 = rangeIosCss;\nconst rangeMdCss = \":host{--knob-handle-size:calc(var(--knob-size) * 2);display:-ms-flexbox;display:flex;position:relative;-ms-flex:3;flex:3;-ms-flex-align:center;align-items:center;font-family:var(--ion-font-family, inherit);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.range-disabled){pointer-events:none}::slotted(ion-label){-ms-flex:initial;flex:initial}::slotted(ion-icon[slot]){font-size:24px}.range-slider{position:relative;-ms-flex:1;flex:1;width:100%;height:var(--height);contain:size layout style;cursor:-webkit-grab;cursor:grab;-ms-touch-action:pan-y;touch-action:pan-y}:host(.range-pressed) .range-slider{cursor:-webkit-grabbing;cursor:grabbing}.range-pin{position:absolute;background:var(--ion-color-base);color:var(--ion-color-contrast);text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box}.range-knob-handle{top:calc((var(--height) - var(--knob-handle-size)) / 2);-webkit-margin-start:calc(0px - var(--knob-handle-size) / 2);margin-inline-start:calc(0px - var(--knob-handle-size) / 2);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-pack:center;justify-content:center;width:var(--knob-handle-size);height:var(--knob-handle-size);text-align:center}.range-knob-handle{inset-inline-start:0}:host-context([dir=rtl]) .range-knob-handle{left:unset}[dir=rtl] .range-knob-handle{left:unset}@supports selector(:dir(rtl)){.range-knob-handle:dir(rtl){left:unset}}.range-knob-handle:active,.range-knob-handle:focus{outline:none}.range-bar-container{border-radius:var(--bar-border-radius);top:calc((var(--height) - var(--bar-height)) / 2);position:absolute;width:100%;height:var(--bar-height)}.range-bar-container{inset-inline-start:0}:host-context([dir=rtl]) .range-bar-container{left:unset}[dir=rtl] .range-bar-container{left:unset}@supports selector(:dir(rtl)){.range-bar-container:dir(rtl){left:unset}}.range-bar{border-radius:var(--bar-border-radius);position:absolute;width:100%;height:var(--bar-height);background:var(--bar-background);pointer-events:none}.range-knob{border-radius:var(--knob-border-radius);top:calc(50% - var(--knob-size) / 2);position:absolute;width:var(--knob-size);height:var(--knob-size);background:var(--knob-background);-webkit-box-shadow:var(--knob-box-shadow);box-shadow:var(--knob-box-shadow);z-index:2;pointer-events:none}.range-knob{inset-inline-start:calc(50% - var(--knob-size) / 2)}:host-context([dir=rtl]) .range-knob{left:unset}[dir=rtl] .range-knob{left:unset}@supports selector(:dir(rtl)){.range-knob:dir(rtl){left:unset}}:host(.range-pressed) .range-bar-active{will-change:left, right}:host(.in-item){width:100%}:host([slot=start]),:host([slot=end]){width:auto}:host(.in-item) ::slotted(ion-label){-ms-flex-item-align:center;align-self:center}.range-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit}::slotted([slot=label]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center}:host(.range-label-placement-start) .range-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.range-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-end) .range-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.range-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.range-label-placement-stacked) .range-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:stretch;align-items:stretch}:host(.range-label-placement-stacked) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top;-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host-context([dir=rtl]):host(.range-label-placement-stacked) .label-text-wrapper,:host-context([dir=rtl]).range-label-placement-stacked .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.range-label-placement-stacked:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.in-item.range-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.range-label-placement-stacked) .native-wrapper{margin-bottom:0px}:host{--knob-border-radius:50%;--knob-background:var(--bar-background-active);--knob-box-shadow:none;--knob-size:18px;--bar-height:2px;--bar-background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.26);--bar-background-active:var(--ion-color-primary, #0054e9);--bar-border-radius:0;--height:42px;--pin-background:var(--ion-color-primary, #0054e9);--pin-color:var(--ion-color-primary-contrast, #fff)}::slotted(:not(ion-icon)[slot=start]),::slotted(:not(ion-icon)[slot=end]),.native-wrapper{font-size:0.75rem}:host(.range-item-start-adjustment){-webkit-padding-start:18px;padding-inline-start:18px}:host(.range-item-end-adjustment){-webkit-padding-end:18px;padding-inline-end:18px}:host(.ion-color) .range-bar{background:rgba(var(--ion-color-base-rgb), 0.26)}:host(.ion-color) .range-bar-active,:host(.ion-color) .range-knob,:host(.ion-color) .range-knob::before,:host(.ion-color) .range-pin,:host(.ion-color) .range-pin::before,:host(.ion-color) .range-tick{background:var(--ion-color-base);color:var(--ion-color-contrast)}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:14px;margin-inline-end:14px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:14px;margin-inline-start:14px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-has-pin:not(.range-label-placement-stacked)){padding-top:1.75rem}:host(.range-has-pin.range-label-placement-stacked) .label-text-wrapper{margin-bottom:1.75rem}.range-bar-active{bottom:0;width:auto;background:var(--bar-background-active)}.range-knob{-webkit-transform:scale(0.67);transform:scale(0.67);-webkit-transition-duration:120ms;transition-duration:120ms;-webkit-transition-property:background-color, border, -webkit-transform;transition-property:background-color, border, -webkit-transform;transition-property:transform, background-color, border;transition-property:transform, background-color, border, -webkit-transform;-webkit-transition-timing-function:ease;transition-timing-function:ease;z-index:2}.range-knob::before{border-radius:50%;position:absolute;width:var(--knob-size);height:var(--knob-size);-webkit-transform:scale(1);transform:scale(1);-webkit-transition:0.267s cubic-bezier(0, 0, 0.58, 1);transition:0.267s cubic-bezier(0, 0, 0.58, 1);background:var(--knob-background);content:\\\"\\\";opacity:0.13;pointer-events:none}.range-knob::before{inset-inline-start:0}.range-tick{position:absolute;top:calc((var(--height) - var(--bar-height)) / 2);width:var(--bar-height);height:var(--bar-height);background:var(--bar-background-active);z-index:1;pointer-events:none}.range-tick-active{background:transparent}.range-pin{padding-left:0;padding-right:0;padding-top:8px;padding-bottom:8px;border-radius:50%;-webkit-transform:translate3d(0,  0,  0) scale(0.01);transform:translate3d(0,  0,  0) scale(0.01);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:1.75rem;height:1.75rem;-webkit-transition:background 120ms ease, -webkit-transform 120ms ease;transition:background 120ms ease, -webkit-transform 120ms ease;transition:transform 120ms ease, background 120ms ease;transition:transform 120ms ease, background 120ms ease, -webkit-transform 120ms ease;background:var(--pin-background);color:var(--pin-color)}.range-pin::before{bottom:-1px;-webkit-margin-start:-13px;margin-inline-start:-13px;border-radius:50% 50% 50% 0;position:absolute;width:26px;height:26px;-webkit-transform:rotate(-45deg);transform:rotate(-45deg);-webkit-transition:background 120ms ease;transition:background 120ms ease;background:var(--pin-background);content:\\\"\\\";z-index:-1}.range-pin::before{inset-inline-start:50%}:host-context([dir=rtl]) .range-pin::before{left:unset}[dir=rtl] .range-pin::before{left:unset}@supports selector(:dir(rtl)){.range-pin::before:dir(rtl){left:unset}}.range-knob-pressed .range-pin,.range-knob-handle.ion-focused .range-pin{-webkit-transform:translate3d(0, calc(-100% + 4px), 0) scale(1);transform:translate3d(0, calc(-100% + 4px), 0) scale(1)}@media (any-hover: hover){.range-knob-handle:hover .range-knob:before{-webkit-transform:scale(2);transform:scale(2);opacity:0.13}}.range-knob-handle.ion-activated .range-knob:before,.range-knob-handle.ion-focused .range-knob:before,.range-knob-handle.range-knob-pressed .range-knob:before{-webkit-transform:scale(2);transform:scale(2)}.range-knob-handle.ion-focused .range-knob::before{opacity:0.13}.range-knob-handle.ion-activated .range-knob::before,.range-knob-handle.range-knob-pressed .range-knob::before{opacity:0.25}:host(:not(.range-has-pin)) .range-knob-pressed .range-knob,:host(:not(.range-has-pin)) .range-knob-handle.ion-focused .range-knob{-webkit-transform:scale(1);transform:scale(1)}:host(.range-disabled) .range-bar-active,:host(.range-disabled) .range-bar,:host(.range-disabled) .range-tick{background-color:var(--ion-color-step-250, var(--ion-background-color-step-250, #bfbfbf))}:host(.range-disabled) .range-knob{-webkit-transform:scale(0.55);transform:scale(0.55);outline:5px solid #fff;background-color:var(--ion-color-step-250, var(--ion-background-color-step-250, #bfbfbf))}:host(.range-disabled) .label-text-wrapper,:host(.range-disabled) ::slotted([slot=start]),:host(.range-disabled) ::slotted([slot=end]){opacity:0.38}\";\nconst IonRangeMdStyle0 = rangeMdCss;\nconst Range = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionInput = createEvent(this, \"ionInput\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionKnobMoveStart = createEvent(this, \"ionKnobMoveStart\", 7);\n    this.ionKnobMoveEnd = createEvent(this, \"ionKnobMoveEnd\", 7);\n    this.rangeId = `ion-r-${rangeIds++}`;\n    this.didLoad = false;\n    this.noUpdate = false;\n    this.hasFocus = false;\n    this.inheritedAttributes = {};\n    this.contentEl = null;\n    this.initialContentScrollY = true;\n    /**\n     * Compares two RangeValue inputs to determine if they are different.\n     *\n     * @param newVal - The new value.\n     * @param oldVal - The old value.\n     * @returns `true` if the values are different, `false` otherwise.\n     */\n    this.compareValues = (newVal, oldVal) => {\n      if (typeof newVal === 'object' && typeof oldVal === 'object') {\n        return newVal.lower !== oldVal.lower || newVal.upper !== oldVal.upper;\n      }\n      return newVal !== oldVal;\n    };\n    this.clampBounds = value => {\n      return clamp(this.min, value, this.max);\n    };\n    this.ensureValueInBounds = value => {\n      if (this.dualKnobs) {\n        return {\n          lower: this.clampBounds(value.lower),\n          upper: this.clampBounds(value.upper)\n        };\n      } else {\n        return this.clampBounds(value);\n      }\n    };\n    this.setupGesture = async () => {\n      const rangeSlider = this.rangeSlider;\n      if (rangeSlider) {\n        this.gesture = (await import('./index-39782642.js')).createGesture({\n          el: rangeSlider,\n          gestureName: 'range',\n          gesturePriority: 100,\n          /**\n           * Provide a threshold since the drag movement\n           * might be a user scrolling the view.\n           * If this is true, then the range\n           * should not move.\n           */\n          threshold: 10,\n          onStart: () => this.onStart(),\n          onMove: ev => this.onMove(ev),\n          onEnd: ev => this.onEnd(ev)\n        });\n        this.gesture.enable(!this.disabled);\n      }\n    };\n    this.handleKeyboard = (knob, isIncrease) => {\n      const {\n        ensureValueInBounds\n      } = this;\n      let step = this.step;\n      step = step > 0 ? step : 1;\n      step = step / (this.max - this.min);\n      if (!isIncrease) {\n        step *= -1;\n      }\n      if (knob === 'A') {\n        this.ratioA = clamp(0, this.ratioA + step, 1);\n      } else {\n        this.ratioB = clamp(0, this.ratioB + step, 1);\n      }\n      this.ionKnobMoveStart.emit({\n        value: ensureValueInBounds(this.value)\n      });\n      this.updateValue();\n      this.emitValueChange();\n      this.ionKnobMoveEnd.emit({\n        value: ensureValueInBounds(this.value)\n      });\n    };\n    this.onBlur = () => {\n      if (this.hasFocus) {\n        this.hasFocus = false;\n        this.ionBlur.emit();\n      }\n    };\n    this.onFocus = () => {\n      if (!this.hasFocus) {\n        this.hasFocus = true;\n        this.ionFocus.emit();\n      }\n    };\n    this.ratioA = 0;\n    this.ratioB = 0;\n    this.pressedKnob = undefined;\n    this.color = undefined;\n    this.debounce = undefined;\n    this.name = this.rangeId;\n    this.label = undefined;\n    this.dualKnobs = false;\n    this.min = 0;\n    this.max = 100;\n    this.pin = false;\n    this.pinFormatter = value => Math.round(value);\n    this.snaps = false;\n    this.step = 1;\n    this.ticks = true;\n    this.activeBarStart = undefined;\n    this.disabled = false;\n    this.value = 0;\n    this.labelPlacement = 'start';\n  }\n  debounceChanged() {\n    const {\n      ionInput,\n      debounce,\n      originalIonInput\n    } = this;\n    /**\n     * If debounce is undefined, we have to manually revert the ionInput emitter in case\n     * debounce used to be set to a number. Otherwise, the event would stay debounced.\n     */\n    this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n  }\n  minChanged(newValue) {\n    if (!isSafeNumber(newValue)) {\n      this.min = 0;\n    }\n    if (!this.noUpdate) {\n      this.updateRatio();\n    }\n  }\n  maxChanged(newValue) {\n    if (!isSafeNumber(newValue)) {\n      this.max = 100;\n    }\n    if (!this.noUpdate) {\n      this.updateRatio();\n    }\n  }\n  stepChanged(newValue) {\n    if (!isSafeNumber(newValue)) {\n      this.step = 1;\n    }\n  }\n  activeBarStartChanged() {\n    const {\n      activeBarStart\n    } = this;\n    if (activeBarStart !== undefined) {\n      if (activeBarStart > this.max) {\n        printIonWarning(`[ion-range] - The value of activeBarStart (${activeBarStart}) is greater than the max (${this.max}). Valid values are greater than or equal to the min value and less than or equal to the max value.`, this.el);\n        this.activeBarStart = this.max;\n      } else if (activeBarStart < this.min) {\n        printIonWarning(`[ion-range] - The value of activeBarStart (${activeBarStart}) is less than the min (${this.min}). Valid values are greater than or equal to the min value and less than or equal to the max value.`, this.el);\n        this.activeBarStart = this.min;\n      }\n    }\n  }\n  disabledChanged() {\n    if (this.gesture) {\n      this.gesture.enable(!this.disabled);\n    }\n  }\n  valueChanged(newValue, oldValue) {\n    const valuesChanged = this.compareValues(newValue, oldValue);\n    if (valuesChanged) {\n      this.ionInput.emit({\n        value: this.value\n      });\n    }\n    if (!this.noUpdate) {\n      this.updateRatio();\n    }\n  }\n  componentWillLoad() {\n    /**\n     * If user has custom ID set then we should\n     * not assign the default incrementing ID.\n     */\n    if (this.el.hasAttribute('id')) {\n      this.rangeId = this.el.getAttribute('id');\n    }\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n    // If min, max, or step are not safe, set them to 0, 100, and 1, respectively.\n    // Each watch does this, but not before the initial load.\n    this.min = isSafeNumber(this.min) ? this.min : 0;\n    this.max = isSafeNumber(this.max) ? this.max : 100;\n    this.step = isSafeNumber(this.step) ? this.step : 1;\n  }\n  componentDidLoad() {\n    this.originalIonInput = this.ionInput;\n    this.setupGesture();\n    this.updateRatio();\n    this.didLoad = true;\n  }\n  connectedCallback() {\n    var _a;\n    this.updateRatio();\n    this.debounceChanged();\n    this.disabledChanged();\n    this.activeBarStartChanged();\n    /**\n     * If we have not yet rendered\n     * ion-range, then rangeSlider is not defined.\n     * But if we are moving ion-range via appendChild,\n     * then rangeSlider will be defined.\n     */\n    if (this.didLoad) {\n      this.setupGesture();\n    }\n    const ionContent = findClosestIonContent(this.el);\n    this.contentEl = (_a = ionContent === null || ionContent === void 0 ? void 0 : ionContent.querySelector('.ion-content-scroll-host')) !== null && _a !== void 0 ? _a : ionContent;\n  }\n  disconnectedCallback() {\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n  }\n  getValue() {\n    var _a;\n    const value = (_a = this.value) !== null && _a !== void 0 ? _a : 0;\n    if (this.dualKnobs) {\n      if (typeof value === 'object') {\n        return value;\n      }\n      return {\n        lower: 0,\n        upper: value\n      };\n    } else {\n      if (typeof value === 'object') {\n        return value.upper;\n      }\n      return value;\n    }\n  }\n  /**\n   * Emits an `ionChange` event.\n   *\n   * This API should be called for user committed changes.\n   * This API should not be used for external value changes.\n   */\n  emitValueChange() {\n    this.value = this.ensureValueInBounds(this.value);\n    this.ionChange.emit({\n      value: this.value\n    });\n  }\n  /**\n   * The value should be updated on touch end or\n   * when the component is being dragged.\n   * This follows the native behavior of mobile devices.\n   *\n   * For example: When the user lifts their finger from the\n   * screen after tapping the bar or dragging the bar or knob.\n   */\n  onStart() {\n    this.ionKnobMoveStart.emit({\n      value: this.ensureValueInBounds(this.value)\n    });\n  }\n  /**\n   * The value should be updated while dragging the\n   * bar or knob.\n   *\n   * While the user is dragging, the view\n   * should not scroll. This is to prevent the user from\n   * feeling disoriented while dragging.\n   *\n   * The user can scroll on the view if the knob or\n   * bar is not being dragged.\n   *\n   * @param detail The details of the gesture event.\n   */\n  onMove(detail) {\n    const {\n      contentEl,\n      pressedKnob\n    } = this;\n    const currentX = detail.currentX;\n    /**\n     * Since the user is dragging on the bar or knob, the view should not scroll.\n     *\n     * This only needs to be done once.\n     */\n    if (contentEl && this.pressedKnob === undefined) {\n      this.initialContentScrollY = disableContentScrollY(contentEl);\n    }\n    /**\n     * The `pressedKnob` can be undefined if the user just\n     * started dragging the knob.\n     *\n     * This is necessary to determine which knob the user is dragging,\n     * especially when it's a dual knob.\n     * Plus, it determines when to apply certain styles.\n     *\n     * This only needs to be done once since the knob won't change\n     * while the user is dragging.\n     */\n    if (pressedKnob === undefined) {\n      this.setPressedKnob(currentX);\n    }\n    this.update(currentX);\n  }\n  /**\n   * The value should be updated on touch end:\n   * - When the user lifts their finger from the screen after\n   * tapping the bar.\n   *\n   * @param detail The details of the gesture or mouse event.\n   */\n  onEnd(detail) {\n    var _a;\n    const {\n      contentEl,\n      initialContentScrollY\n    } = this;\n    const currentX = (_a = detail.currentX) !== null && _a !== void 0 ? _a : detail.clientX;\n    /**\n     * The `pressedKnob` can be undefined if the user never\n     * dragged the knob. They just tapped on the bar.\n     *\n     * This is necessary to determine which knob the user is changing,\n     * especially when it's a dual knob.\n     * Plus, it determines when to apply certain styles.\n     */\n    if (this.pressedKnob === undefined) {\n      this.setPressedKnob(currentX);\n    }\n    /**\n     * The user is no longer dragging the bar or\n     * knob (if they were dragging it).\n     *\n     * The user can now scroll on the view in the next gesture event.\n     */\n    if (contentEl && this.pressedKnob !== undefined) {\n      resetContentScrollY(contentEl, initialContentScrollY);\n    }\n    // update the active knob's position\n    this.update(currentX);\n    /**\n     * Reset the pressed knob to undefined since the user\n     * may start dragging a different knob in the next gesture event.\n     */\n    this.pressedKnob = undefined;\n    this.emitValueChange();\n    this.ionKnobMoveEnd.emit({\n      value: this.ensureValueInBounds(this.value)\n    });\n  }\n  update(currentX) {\n    // figure out where the pointer is currently at\n    // update the knob being interacted with\n    const rect = this.rect;\n    let ratio = clamp(0, (currentX - rect.left) / rect.width, 1);\n    if (isRTL(this.el)) {\n      ratio = 1 - ratio;\n    }\n    if (this.snaps) {\n      // snaps the ratio to the current value\n      ratio = valueToRatio(ratioToValue(ratio, this.min, this.max, this.step), this.min, this.max);\n    }\n    // update which knob is pressed\n    if (this.pressedKnob === 'A') {\n      this.ratioA = ratio;\n    } else {\n      this.ratioB = ratio;\n    }\n    // Update input value\n    this.updateValue();\n  }\n  setPressedKnob(currentX) {\n    const rect = this.rect = this.rangeSlider.getBoundingClientRect();\n    // figure out which knob they started closer to\n    let ratio = clamp(0, (currentX - rect.left) / rect.width, 1);\n    if (isRTL(this.el)) {\n      ratio = 1 - ratio;\n    }\n    this.pressedKnob = !this.dualKnobs || Math.abs(this.ratioA - ratio) < Math.abs(this.ratioB - ratio) ? 'A' : 'B';\n    this.setFocus(this.pressedKnob);\n  }\n  get valA() {\n    return ratioToValue(this.ratioA, this.min, this.max, this.step);\n  }\n  get valB() {\n    return ratioToValue(this.ratioB, this.min, this.max, this.step);\n  }\n  get ratioLower() {\n    if (this.dualKnobs) {\n      return Math.min(this.ratioA, this.ratioB);\n    }\n    const {\n      activeBarStart\n    } = this;\n    if (activeBarStart == null) {\n      return 0;\n    }\n    return valueToRatio(activeBarStart, this.min, this.max);\n  }\n  get ratioUpper() {\n    if (this.dualKnobs) {\n      return Math.max(this.ratioA, this.ratioB);\n    }\n    return this.ratioA;\n  }\n  updateRatio() {\n    const value = this.getValue();\n    const {\n      min,\n      max\n    } = this;\n    if (this.dualKnobs) {\n      this.ratioA = valueToRatio(value.lower, min, max);\n      this.ratioB = valueToRatio(value.upper, min, max);\n    } else {\n      this.ratioA = valueToRatio(value, min, max);\n    }\n  }\n  updateValue() {\n    this.noUpdate = true;\n    const {\n      valA,\n      valB\n    } = this;\n    this.value = !this.dualKnobs ? valA : {\n      lower: Math.min(valA, valB),\n      upper: Math.max(valA, valB)\n    };\n    this.noUpdate = false;\n  }\n  setFocus(knob) {\n    if (this.el.shadowRoot) {\n      const knobEl = this.el.shadowRoot.querySelector(knob === 'A' ? '.range-knob-a' : '.range-knob-b');\n      if (knobEl) {\n        knobEl.focus();\n      }\n    }\n  }\n  /**\n   * Returns true if content was passed to the \"start\" slot\n   */\n  get hasStartSlotContent() {\n    return this.el.querySelector('[slot=\"start\"]') !== null;\n  }\n  /**\n   * Returns true if content was passed to the \"end\" slot\n   */\n  get hasEndSlotContent() {\n    return this.el.querySelector('[slot=\"end\"]') !== null;\n  }\n  get hasLabel() {\n    return this.label !== undefined || this.el.querySelector('[slot=\"label\"]') !== null;\n  }\n  renderRangeSlider() {\n    var _a;\n    const {\n      min,\n      max,\n      step,\n      handleKeyboard,\n      pressedKnob,\n      disabled,\n      pin,\n      ratioLower,\n      ratioUpper,\n      pinFormatter,\n      inheritedAttributes\n    } = this;\n    let barStart = `${ratioLower * 100}%`;\n    let barEnd = `${100 - ratioUpper * 100}%`;\n    const rtl = isRTL(this.el);\n    const start = rtl ? 'right' : 'left';\n    const end = rtl ? 'left' : 'right';\n    const tickStyle = tick => {\n      return {\n        [start]: tick[start]\n      };\n    };\n    if (this.dualKnobs === false) {\n      /**\n       * When the value is less than the activeBarStart or the min value,\n       * the knob will display at the start of the active bar.\n       */\n      if (this.valA < ((_a = this.activeBarStart) !== null && _a !== void 0 ? _a : this.min)) {\n        /**\n         * Sets the bar positions relative to the upper and lower limits.\n         * Converts the ratio values into percentages, used as offsets for left/right styles.\n         *\n         * The ratioUpper refers to the knob position on the bar.\n         * The ratioLower refers to the end position of the active bar (the value).\n         */\n        barStart = `${ratioUpper * 100}%`;\n        barEnd = `${100 - ratioLower * 100}%`;\n      } else {\n        /**\n         * Otherwise, the knob will display at the end of the active bar.\n         *\n         * The ratioLower refers to the start position of the active bar (the value).\n         * The ratioUpper refers to the knob position on the bar.\n         */\n        barStart = `${ratioLower * 100}%`;\n        barEnd = `${100 - ratioUpper * 100}%`;\n      }\n    }\n    const barStyle = {\n      [start]: barStart,\n      [end]: barEnd\n    };\n    const ticks = [];\n    if (this.snaps && this.ticks) {\n      for (let value = min; value <= max; value += step) {\n        const ratio = valueToRatio(value, min, max);\n        const ratioMin = Math.min(ratioLower, ratioUpper);\n        const ratioMax = Math.max(ratioLower, ratioUpper);\n        const tick = {\n          ratio,\n          /**\n           * Sets the tick mark as active when the tick is between the min bounds and the knob.\n           * When using activeBarStart, the tick mark will be active between the knob and activeBarStart.\n           */\n          active: ratio >= ratioMin && ratio <= ratioMax\n        };\n        tick[start] = `${ratio * 100}%`;\n        ticks.push(tick);\n      }\n    }\n    return h(\"div\", {\n      class: \"range-slider\",\n      ref: rangeEl => this.rangeSlider = rangeEl,\n      /**\n       * Since the gesture has a threshold, the value\n       * won't change until the user has dragged past\n       * the threshold. This is to prevent the range\n       * from moving when the user is scrolling.\n       *\n       * This results in the value not being updated\n       * and the event emitters not being triggered\n       * if the user taps on the range. This is why\n       * we need to listen for the \"pointerUp\" event.\n       */\n      onPointerUp: ev => {\n        /**\n         * If the user drags the knob on the web\n         * version (does not occur on mobile),\n         * the \"pointerUp\" event will be triggered\n         * along with the gesture's events.\n         * This leads to duplicate events.\n         *\n         * By checking if the pressedKnob is undefined,\n         * we can determine if the \"pointerUp\" event was\n         * triggered by a tap or a drag. If it was\n         * dragged, the pressedKnob will be defined.\n         */\n        if (this.pressedKnob === undefined) {\n          this.onStart();\n          this.onEnd(ev);\n        }\n      }\n    }, ticks.map(tick => h(\"div\", {\n      style: tickStyle(tick),\n      role: \"presentation\",\n      class: {\n        'range-tick': true,\n        'range-tick-active': tick.active\n      },\n      part: tick.active ? 'tick-active' : 'tick'\n    })), h(\"div\", {\n      class: \"range-bar-container\"\n    }, h(\"div\", {\n      class: \"range-bar\",\n      role: \"presentation\",\n      part: \"bar\"\n    }), h(\"div\", {\n      class: {\n        'range-bar': true,\n        'range-bar-active': true,\n        'has-ticks': ticks.length > 0\n      },\n      role: \"presentation\",\n      style: barStyle,\n      part: \"bar-active\"\n    })), renderKnob(rtl, {\n      knob: 'A',\n      pressed: pressedKnob === 'A',\n      value: this.valA,\n      ratio: this.ratioA,\n      pin,\n      pinFormatter,\n      disabled,\n      handleKeyboard,\n      min,\n      max,\n      inheritedAttributes\n    }), this.dualKnobs && renderKnob(rtl, {\n      knob: 'B',\n      pressed: pressedKnob === 'B',\n      value: this.valB,\n      ratio: this.ratioB,\n      pin,\n      pinFormatter,\n      disabled,\n      handleKeyboard,\n      min,\n      max,\n      inheritedAttributes\n    }));\n  }\n  render() {\n    const {\n      disabled,\n      el,\n      hasLabel,\n      rangeId,\n      pin,\n      pressedKnob,\n      labelPlacement,\n      label\n    } = this;\n    const inItem = hostContext('ion-item', el);\n    /**\n     * If there is no start content then the knob at\n     * the min value will be cut off by the item margin.\n     */\n    const hasStartContent = hasLabel && (labelPlacement === 'start' || labelPlacement === 'fixed') || this.hasStartSlotContent;\n    const needsStartAdjustment = inItem && !hasStartContent;\n    /**\n     * If there is no end content then the knob at\n     * the max value will be cut off by the item margin.\n     */\n    const hasEndContent = hasLabel && labelPlacement === 'end' || this.hasEndSlotContent;\n    const needsEndAdjustment = inItem && !hasEndContent;\n    const mode = getIonMode(this);\n    renderHiddenInput(true, el, this.name, JSON.stringify(this.getValue()), disabled);\n    return h(Host, {\n      key: '3e065039ee048f1f70d97dba5dae98fa1315d867',\n      onFocusin: this.onFocus,\n      onFocusout: this.onBlur,\n      id: rangeId,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'in-item': inItem,\n        'range-disabled': disabled,\n        'range-pressed': pressedKnob !== undefined,\n        'range-has-pin': pin,\n        [`range-label-placement-${labelPlacement}`]: true,\n        'range-item-start-adjustment': needsStartAdjustment,\n        'range-item-end-adjustment': needsEndAdjustment\n      })\n    }, h(\"label\", {\n      key: '27ff22842c9ea79a1b9495302b926f70c9080a95',\n      class: \"range-wrapper\",\n      id: \"range-label\"\n    }, h(\"div\", {\n      key: 'da1f9784be02dfe87d2fef34931d8b7f2148189e',\n      class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': !hasLabel\n      },\n      part: \"label\"\n    }, label !== undefined ? h(\"div\", {\n      class: \"label-text\"\n    }, label) : h(\"slot\", {\n      name: \"label\"\n    })), h(\"div\", {\n      key: '4389bf30b08214f5b5917fc30976b38f7bcdd29b',\n      class: \"native-wrapper\"\n    }, h(\"slot\", {\n      key: 'ad1b2745f8b061ee189617bb5c567e4f1d02250c',\n      name: \"start\"\n    }), this.renderRangeSlider(), h(\"slot\", {\n      key: 'c6dec9e843e232af2a5f16a0f8ee56439c545d7a',\n      name: \"end\"\n    }))));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"debounce\": [\"debounceChanged\"],\n      \"min\": [\"minChanged\"],\n      \"max\": [\"maxChanged\"],\n      \"step\": [\"stepChanged\"],\n      \"activeBarStart\": [\"activeBarStartChanged\"],\n      \"disabled\": [\"disabledChanged\"],\n      \"value\": [\"valueChanged\"]\n    };\n  }\n};\nconst renderKnob = (rtl, {\n  knob,\n  value,\n  ratio,\n  min,\n  max,\n  disabled,\n  pressed,\n  pin,\n  handleKeyboard,\n  pinFormatter,\n  inheritedAttributes\n}) => {\n  const start = rtl ? 'right' : 'left';\n  const knobStyle = () => {\n    const style = {};\n    style[start] = `${ratio * 100}%`;\n    return style;\n  };\n  // The aria label should be preferred over visible text if both are specified\n  const ariaLabel = inheritedAttributes['aria-label'];\n  return h(\"div\", {\n    onKeyDown: ev => {\n      const key = ev.key;\n      if (key === 'ArrowLeft' || key === 'ArrowDown') {\n        handleKeyboard(knob, false);\n        ev.preventDefault();\n        ev.stopPropagation();\n      } else if (key === 'ArrowRight' || key === 'ArrowUp') {\n        handleKeyboard(knob, true);\n        ev.preventDefault();\n        ev.stopPropagation();\n      }\n    },\n    class: {\n      'range-knob-handle': true,\n      'range-knob-a': knob === 'A',\n      'range-knob-b': knob === 'B',\n      'range-knob-pressed': pressed,\n      'range-knob-min': value === min,\n      'range-knob-max': value === max,\n      'ion-activatable': true,\n      'ion-focusable': true\n    },\n    style: knobStyle(),\n    role: \"slider\",\n    tabindex: disabled ? -1 : 0,\n    \"aria-label\": ariaLabel !== undefined ? ariaLabel : null,\n    \"aria-labelledby\": ariaLabel === undefined ? 'range-label' : null,\n    \"aria-valuemin\": min,\n    \"aria-valuemax\": max,\n    \"aria-disabled\": disabled ? 'true' : null,\n    \"aria-valuenow\": value\n  }, pin && h(\"div\", {\n    class: \"range-pin\",\n    role: \"presentation\",\n    part: \"pin\"\n  }, pinFormatter(value)), h(\"div\", {\n    class: \"range-knob\",\n    role: \"presentation\",\n    part: \"knob\"\n  }));\n};\nconst ratioToValue = (ratio, min, max, step) => {\n  let value = (max - min) * ratio;\n  if (step > 0) {\n    // round to nearest multiple of step, then add min\n    value = Math.round(value / step) * step + min;\n  }\n  const clampedValue = clamp(min, value, max);\n  return roundToMaxDecimalPlaces(clampedValue, min, max, step);\n};\nconst valueToRatio = (value, min, max) => {\n  return clamp(0, (value - min) / (max - min), 1);\n};\nlet rangeIds = 0;\nRange.style = {\n  ios: IonRangeIosStyle0,\n  md: IonRangeMdStyle0\n};\nexport { Range as ion_range };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,SAAS,iBAAiB,GAAG;AAC3B,MAAI,CAAC,aAAa,CAAC,EAAG,QAAO;AAC7B,MAAI,IAAI,MAAM,EAAG,QAAO;AACxB,SAAO,EAAE,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;AACpC;AAiCA,SAAS,wBAAwB,MAAM,YAAY;AACjD,MAAI,CAAC,aAAa,CAAC,EAAG,QAAO;AAC7B,QAAM,YAAY,KAAK,IAAI,GAAG,WAAW,IAAI,OAAK,iBAAiB,CAAC,CAAC,CAAC;AACtE,SAAO,OAAO,EAAE,QAAQ,SAAS,CAAC;AACpC;AAnDA,IAoDM,aACA,mBACA,YACA,kBACA,OA0rBA,YA+DA,cASA,cAGF;AA7zBJ;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AA2CA,IAAM,cAAc;AACpB,IAAM,oBAAoB;AAC1B,IAAM,aAAa;AACnB,IAAM,mBAAmB;AACzB,IAAM,QAAQ,MAAM;AAAA,MAClB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,aAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,aAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,aAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,aAAK,mBAAmB,YAAY,MAAM,oBAAoB,CAAC;AAC/D,aAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,aAAK,UAAU,SAAS,UAAU;AAClC,aAAK,UAAU;AACf,aAAK,WAAW;AAChB,aAAK,WAAW;AAChB,aAAK,sBAAsB,CAAC;AAC5B,aAAK,YAAY;AACjB,aAAK,wBAAwB;AAQ7B,aAAK,gBAAgB,CAAC,QAAQ,WAAW;AACvC,cAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;AAC5D,mBAAO,OAAO,UAAU,OAAO,SAAS,OAAO,UAAU,OAAO;AAAA,UAClE;AACA,iBAAO,WAAW;AAAA,QACpB;AACA,aAAK,cAAc,WAAS;AAC1B,iBAAO,MAAM,KAAK,KAAK,OAAO,KAAK,GAAG;AAAA,QACxC;AACA,aAAK,sBAAsB,WAAS;AAClC,cAAI,KAAK,WAAW;AAClB,mBAAO;AAAA,cACL,OAAO,KAAK,YAAY,MAAM,KAAK;AAAA,cACnC,OAAO,KAAK,YAAY,MAAM,KAAK;AAAA,YACrC;AAAA,UACF,OAAO;AACL,mBAAO,KAAK,YAAY,KAAK;AAAA,UAC/B;AAAA,QACF;AACA,aAAK,eAAe,MAAY;AAC9B,gBAAM,cAAc,KAAK;AACzB,cAAI,aAAa;AACf,iBAAK,WAAW,MAAM,OAAO,8BAAqB,GAAG,cAAc;AAAA,cACjE,IAAI;AAAA,cACJ,aAAa;AAAA,cACb,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAOjB,WAAW;AAAA,cACX,SAAS,MAAM,KAAK,QAAQ;AAAA,cAC5B,QAAQ,QAAM,KAAK,OAAO,EAAE;AAAA,cAC5B,OAAO,QAAM,KAAK,MAAM,EAAE;AAAA,YAC5B,CAAC;AACD,iBAAK,QAAQ,OAAO,CAAC,KAAK,QAAQ;AAAA,UACpC;AAAA,QACF;AACA,aAAK,iBAAiB,CAAC,MAAM,eAAe;AAC1C,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,cAAI,OAAO,KAAK;AAChB,iBAAO,OAAO,IAAI,OAAO;AACzB,iBAAO,QAAQ,KAAK,MAAM,KAAK;AAC/B,cAAI,CAAC,YAAY;AACf,oBAAQ;AAAA,UACV;AACA,cAAI,SAAS,KAAK;AAChB,iBAAK,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,CAAC;AAAA,UAC9C,OAAO;AACL,iBAAK,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,CAAC;AAAA,UAC9C;AACA,eAAK,iBAAiB,KAAK;AAAA,YACzB,OAAO,oBAAoB,KAAK,KAAK;AAAA,UACvC,CAAC;AACD,eAAK,YAAY;AACjB,eAAK,gBAAgB;AACrB,eAAK,eAAe,KAAK;AAAA,YACvB,OAAO,oBAAoB,KAAK,KAAK;AAAA,UACvC,CAAC;AAAA,QACH;AACA,aAAK,SAAS,MAAM;AAClB,cAAI,KAAK,UAAU;AACjB,iBAAK,WAAW;AAChB,iBAAK,QAAQ,KAAK;AAAA,UACpB;AAAA,QACF;AACA,aAAK,UAAU,MAAM;AACnB,cAAI,CAAC,KAAK,UAAU;AAClB,iBAAK,WAAW;AAChB,iBAAK,SAAS,KAAK;AAAA,UACrB;AAAA,QACF;AACA,aAAK,SAAS;AACd,aAAK,SAAS;AACd,aAAK,cAAc;AACnB,aAAK,QAAQ;AACb,aAAK,WAAW;AAChB,aAAK,OAAO,KAAK;AACjB,aAAK,QAAQ;AACb,aAAK,YAAY;AACjB,aAAK,MAAM;AACX,aAAK,MAAM;AACX,aAAK,MAAM;AACX,aAAK,eAAe,WAAS,KAAK,MAAM,KAAK;AAC7C,aAAK,QAAQ;AACb,aAAK,OAAO;AACZ,aAAK,QAAQ;AACb,aAAK,iBAAiB;AACtB,aAAK,WAAW;AAChB,aAAK,QAAQ;AACb,aAAK,iBAAiB;AAAA,MACxB;AAAA,MACA,kBAAkB;AAChB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AAKJ,aAAK,WAAW,aAAa,SAAY,qBAAqB,QAAQ,qBAAqB,SAAS,mBAAmB,WAAW,cAAc,UAAU,QAAQ;AAAA,MACpK;AAAA,MACA,WAAW,UAAU;AACnB,YAAI,CAAC,aAAa,QAAQ,GAAG;AAC3B,eAAK,MAAM;AAAA,QACb;AACA,YAAI,CAAC,KAAK,UAAU;AAClB,eAAK,YAAY;AAAA,QACnB;AAAA,MACF;AAAA,MACA,WAAW,UAAU;AACnB,YAAI,CAAC,aAAa,QAAQ,GAAG;AAC3B,eAAK,MAAM;AAAA,QACb;AACA,YAAI,CAAC,KAAK,UAAU;AAClB,eAAK,YAAY;AAAA,QACnB;AAAA,MACF;AAAA,MACA,YAAY,UAAU;AACpB,YAAI,CAAC,aAAa,QAAQ,GAAG;AAC3B,eAAK,OAAO;AAAA,QACd;AAAA,MACF;AAAA,MACA,wBAAwB;AACtB,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,YAAI,mBAAmB,QAAW;AAChC,cAAI,iBAAiB,KAAK,KAAK;AAC7B,4BAAgB,8CAA8C,cAAc,8BAA8B,KAAK,GAAG,uGAAuG,KAAK,EAAE;AAChO,iBAAK,iBAAiB,KAAK;AAAA,UAC7B,WAAW,iBAAiB,KAAK,KAAK;AACpC,4BAAgB,8CAA8C,cAAc,2BAA2B,KAAK,GAAG,uGAAuG,KAAK,EAAE;AAC7N,iBAAK,iBAAiB,KAAK;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AAAA,MACA,kBAAkB;AAChB,YAAI,KAAK,SAAS;AAChB,eAAK,QAAQ,OAAO,CAAC,KAAK,QAAQ;AAAA,QACpC;AAAA,MACF;AAAA,MACA,aAAa,UAAU,UAAU;AAC/B,cAAM,gBAAgB,KAAK,cAAc,UAAU,QAAQ;AAC3D,YAAI,eAAe;AACjB,eAAK,SAAS,KAAK;AAAA,YACjB,OAAO,KAAK;AAAA,UACd,CAAC;AAAA,QACH;AACA,YAAI,CAAC,KAAK,UAAU;AAClB,eAAK,YAAY;AAAA,QACnB;AAAA,MACF;AAAA,MACA,oBAAoB;AAKlB,YAAI,KAAK,GAAG,aAAa,IAAI,GAAG;AAC9B,eAAK,UAAU,KAAK,GAAG,aAAa,IAAI;AAAA,QAC1C;AACA,aAAK,sBAAsB,sBAAsB,KAAK,EAAE;AAGxD,aAAK,MAAM,aAAa,KAAK,GAAG,IAAI,KAAK,MAAM;AAC/C,aAAK,MAAM,aAAa,KAAK,GAAG,IAAI,KAAK,MAAM;AAC/C,aAAK,OAAO,aAAa,KAAK,IAAI,IAAI,KAAK,OAAO;AAAA,MACpD;AAAA,MACA,mBAAmB;AACjB,aAAK,mBAAmB,KAAK;AAC7B,aAAK,aAAa;AAClB,aAAK,YAAY;AACjB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,oBAAoB;AAClB,YAAI;AACJ,aAAK,YAAY;AACjB,aAAK,gBAAgB;AACrB,aAAK,gBAAgB;AACrB,aAAK,sBAAsB;AAO3B,YAAI,KAAK,SAAS;AAChB,eAAK,aAAa;AAAA,QACpB;AACA,cAAM,aAAa,sBAAsB,KAAK,EAAE;AAChD,aAAK,aAAa,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,cAAc,0BAA0B,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,MACxK;AAAA,MACA,uBAAuB;AACrB,YAAI,KAAK,SAAS;AAChB,eAAK,QAAQ,QAAQ;AACrB,eAAK,UAAU;AAAA,QACjB;AAAA,MACF;AAAA,MACA,WAAW;AACT,YAAI;AACJ,cAAM,SAAS,KAAK,KAAK,WAAW,QAAQ,OAAO,SAAS,KAAK;AACjE,YAAI,KAAK,WAAW;AAClB,cAAI,OAAO,UAAU,UAAU;AAC7B,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,YACL,OAAO;AAAA,YACP,OAAO;AAAA,UACT;AAAA,QACF,OAAO;AACL,cAAI,OAAO,UAAU,UAAU;AAC7B,mBAAO,MAAM;AAAA,UACf;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,kBAAkB;AAChB,aAAK,QAAQ,KAAK,oBAAoB,KAAK,KAAK;AAChD,aAAK,UAAU,KAAK;AAAA,UAClB,OAAO,KAAK;AAAA,QACd,CAAC;AAAA,MACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,UAAU;AACR,aAAK,iBAAiB,KAAK;AAAA,UACzB,OAAO,KAAK,oBAAoB,KAAK,KAAK;AAAA,QAC5C,CAAC;AAAA,MACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAcA,OAAO,QAAQ;AACb,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,WAAW,OAAO;AAMxB,YAAI,aAAa,KAAK,gBAAgB,QAAW;AAC/C,eAAK,wBAAwB,sBAAsB,SAAS;AAAA,QAC9D;AAYA,YAAI,gBAAgB,QAAW;AAC7B,eAAK,eAAe,QAAQ;AAAA,QAC9B;AACA,aAAK,OAAO,QAAQ;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,MAAM,QAAQ;AACZ,YAAI;AACJ,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,YAAY,KAAK,OAAO,cAAc,QAAQ,OAAO,SAAS,KAAK,OAAO;AAShF,YAAI,KAAK,gBAAgB,QAAW;AAClC,eAAK,eAAe,QAAQ;AAAA,QAC9B;AAOA,YAAI,aAAa,KAAK,gBAAgB,QAAW;AAC/C,8BAAoB,WAAW,qBAAqB;AAAA,QACtD;AAEA,aAAK,OAAO,QAAQ;AAKpB,aAAK,cAAc;AACnB,aAAK,gBAAgB;AACrB,aAAK,eAAe,KAAK;AAAA,UACvB,OAAO,KAAK,oBAAoB,KAAK,KAAK;AAAA,QAC5C,CAAC;AAAA,MACH;AAAA,MACA,OAAO,UAAU;AAGf,cAAM,OAAO,KAAK;AAClB,YAAI,QAAQ,MAAM,IAAI,WAAW,KAAK,QAAQ,KAAK,OAAO,CAAC;AAC3D,YAAI,MAAM,KAAK,EAAE,GAAG;AAClB,kBAAQ,IAAI;AAAA,QACd;AACA,YAAI,KAAK,OAAO;AAEd,kBAAQ,aAAa,aAAa,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK,GAAG;AAAA,QAC7F;AAEA,YAAI,KAAK,gBAAgB,KAAK;AAC5B,eAAK,SAAS;AAAA,QAChB,OAAO;AACL,eAAK,SAAS;AAAA,QAChB;AAEA,aAAK,YAAY;AAAA,MACnB;AAAA,MACA,eAAe,UAAU;AACvB,cAAM,OAAO,KAAK,OAAO,KAAK,YAAY,sBAAsB;AAEhE,YAAI,QAAQ,MAAM,IAAI,WAAW,KAAK,QAAQ,KAAK,OAAO,CAAC;AAC3D,YAAI,MAAM,KAAK,EAAE,GAAG;AAClB,kBAAQ,IAAI;AAAA,QACd;AACA,aAAK,cAAc,CAAC,KAAK,aAAa,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,MAAM;AAC5G,aAAK,SAAS,KAAK,WAAW;AAAA,MAChC;AAAA,MACA,IAAI,OAAO;AACT,eAAO,aAAa,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI;AAAA,MAChE;AAAA,MACA,IAAI,OAAO;AACT,eAAO,aAAa,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI;AAAA,MAChE;AAAA,MACA,IAAI,aAAa;AACf,YAAI,KAAK,WAAW;AAClB,iBAAO,KAAK,IAAI,KAAK,QAAQ,KAAK,MAAM;AAAA,QAC1C;AACA,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,YAAI,kBAAkB,MAAM;AAC1B,iBAAO;AAAA,QACT;AACA,eAAO,aAAa,gBAAgB,KAAK,KAAK,KAAK,GAAG;AAAA,MACxD;AAAA,MACA,IAAI,aAAa;AACf,YAAI,KAAK,WAAW;AAClB,iBAAO,KAAK,IAAI,KAAK,QAAQ,KAAK,MAAM;AAAA,QAC1C;AACA,eAAO,KAAK;AAAA,MACd;AAAA,MACA,cAAc;AACZ,cAAM,QAAQ,KAAK,SAAS;AAC5B,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,KAAK,WAAW;AAClB,eAAK,SAAS,aAAa,MAAM,OAAO,KAAK,GAAG;AAChD,eAAK,SAAS,aAAa,MAAM,OAAO,KAAK,GAAG;AAAA,QAClD,OAAO;AACL,eAAK,SAAS,aAAa,OAAO,KAAK,GAAG;AAAA,QAC5C;AAAA,MACF;AAAA,MACA,cAAc;AACZ,aAAK,WAAW;AAChB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,aAAK,QAAQ,CAAC,KAAK,YAAY,OAAO;AAAA,UACpC,OAAO,KAAK,IAAI,MAAM,IAAI;AAAA,UAC1B,OAAO,KAAK,IAAI,MAAM,IAAI;AAAA,QAC5B;AACA,aAAK,WAAW;AAAA,MAClB;AAAA,MACA,SAAS,MAAM;AACb,YAAI,KAAK,GAAG,YAAY;AACtB,gBAAM,SAAS,KAAK,GAAG,WAAW,cAAc,SAAS,MAAM,kBAAkB,eAAe;AAChG,cAAI,QAAQ;AACV,mBAAO,MAAM;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAIA,IAAI,sBAAsB;AACxB,eAAO,KAAK,GAAG,cAAc,gBAAgB,MAAM;AAAA,MACrD;AAAA;AAAA;AAAA;AAAA,MAIA,IAAI,oBAAoB;AACtB,eAAO,KAAK,GAAG,cAAc,cAAc,MAAM;AAAA,MACnD;AAAA,MACA,IAAI,WAAW;AACb,eAAO,KAAK,UAAU,UAAa,KAAK,GAAG,cAAc,gBAAgB,MAAM;AAAA,MACjF;AAAA,MACA,oBAAoB;AAClB,YAAI;AACJ,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,WAAW,GAAG,aAAa,GAAG;AAClC,YAAI,SAAS,GAAG,MAAM,aAAa,GAAG;AACtC,cAAM,MAAM,MAAM,KAAK,EAAE;AACzB,cAAM,QAAQ,MAAM,UAAU;AAC9B,cAAM,MAAM,MAAM,SAAS;AAC3B,cAAM,YAAY,UAAQ;AACxB,iBAAO;AAAA,YACL,CAAC,KAAK,GAAG,KAAK,KAAK;AAAA,UACrB;AAAA,QACF;AACA,YAAI,KAAK,cAAc,OAAO;AAK5B,cAAI,KAAK,SAAS,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,KAAK,KAAK,MAAM;AAQtF,uBAAW,GAAG,aAAa,GAAG;AAC9B,qBAAS,GAAG,MAAM,aAAa,GAAG;AAAA,UACpC,OAAO;AAOL,uBAAW,GAAG,aAAa,GAAG;AAC9B,qBAAS,GAAG,MAAM,aAAa,GAAG;AAAA,UACpC;AAAA,QACF;AACA,cAAM,WAAW;AAAA,UACf,CAAC,KAAK,GAAG;AAAA,UACT,CAAC,GAAG,GAAG;AAAA,QACT;AACA,cAAM,QAAQ,CAAC;AACf,YAAI,KAAK,SAAS,KAAK,OAAO;AAC5B,mBAAS,QAAQ,KAAK,SAAS,KAAK,SAAS,MAAM;AACjD,kBAAM,QAAQ,aAAa,OAAO,KAAK,GAAG;AAC1C,kBAAM,WAAW,KAAK,IAAI,YAAY,UAAU;AAChD,kBAAM,WAAW,KAAK,IAAI,YAAY,UAAU;AAChD,kBAAM,OAAO;AAAA,cACX;AAAA;AAAA;AAAA;AAAA;AAAA,cAKA,QAAQ,SAAS,YAAY,SAAS;AAAA,YACxC;AACA,iBAAK,KAAK,IAAI,GAAG,QAAQ,GAAG;AAC5B,kBAAM,KAAK,IAAI;AAAA,UACjB;AAAA,QACF;AACA,eAAO,EAAE,OAAO;AAAA,UACd,OAAO;AAAA,UACP,KAAK,aAAW,KAAK,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAYnC,aAAa,QAAM;AAajB,gBAAI,KAAK,gBAAgB,QAAW;AAClC,mBAAK,QAAQ;AACb,mBAAK,MAAM,EAAE;AAAA,YACf;AAAA,UACF;AAAA,QACF,GAAG,MAAM,IAAI,UAAQ,EAAE,OAAO;AAAA,UAC5B,OAAO,UAAU,IAAI;AAAA,UACrB,MAAM;AAAA,UACN,OAAO;AAAA,YACL,cAAc;AAAA,YACd,qBAAqB,KAAK;AAAA,UAC5B;AAAA,UACA,MAAM,KAAK,SAAS,gBAAgB;AAAA,QACtC,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,UACZ,OAAO;AAAA,QACT,GAAG,EAAE,OAAO;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,QACR,CAAC,GAAG,EAAE,OAAO;AAAA,UACX,OAAO;AAAA,YACL,aAAa;AAAA,YACb,oBAAoB;AAAA,YACpB,aAAa,MAAM,SAAS;AAAA,UAC9B;AAAA,UACA,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC,CAAC,GAAG,WAAW,KAAK;AAAA,UACnB,MAAM;AAAA,UACN,SAAS,gBAAgB;AAAA,UACzB,OAAO,KAAK;AAAA,UACZ,OAAO,KAAK;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC,GAAG,KAAK,aAAa,WAAW,KAAK;AAAA,UACpC,MAAM;AAAA,UACN,SAAS,gBAAgB;AAAA,UACzB,OAAO,KAAK;AAAA,UACZ,OAAO,KAAK;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC,CAAC;AAAA,MACJ;AAAA,MACA,SAAS;AACP,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,SAAS,YAAY,YAAY,EAAE;AAKzC,cAAM,kBAAkB,aAAa,mBAAmB,WAAW,mBAAmB,YAAY,KAAK;AACvG,cAAM,uBAAuB,UAAU,CAAC;AAKxC,cAAM,gBAAgB,YAAY,mBAAmB,SAAS,KAAK;AACnE,cAAM,qBAAqB,UAAU,CAAC;AACtC,cAAM,OAAO,WAAW,IAAI;AAC5B,0BAAkB,MAAM,IAAI,KAAK,MAAM,KAAK,UAAU,KAAK,SAAS,CAAC,GAAG,QAAQ;AAChF,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,WAAW,KAAK;AAAA,UAChB,YAAY,KAAK;AAAA,UACjB,IAAI;AAAA,UACJ,OAAO,mBAAmB,KAAK,OAAO;AAAA,YACpC,CAAC,IAAI,GAAG;AAAA,YACR,WAAW;AAAA,YACX,kBAAkB;AAAA,YAClB,iBAAiB,gBAAgB;AAAA,YACjC,iBAAiB;AAAA,YACjB,CAAC,yBAAyB,cAAc,EAAE,GAAG;AAAA,YAC7C,+BAA+B;AAAA,YAC/B,6BAA6B;AAAA,UAC/B,CAAC;AAAA,QACH,GAAG,EAAE,SAAS;AAAA,UACZ,KAAK;AAAA,UACL,OAAO;AAAA,UACP,IAAI;AAAA,QACN,GAAG,EAAE,OAAO;AAAA,UACV,KAAK;AAAA,UACL,OAAO;AAAA,YACL,sBAAsB;AAAA,YACtB,6BAA6B,CAAC;AAAA,UAChC;AAAA,UACA,MAAM;AAAA,QACR,GAAG,UAAU,SAAY,EAAE,OAAO;AAAA,UAChC,OAAO;AAAA,QACT,GAAG,KAAK,IAAI,EAAE,QAAQ;AAAA,UACpB,MAAM;AAAA,QACR,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,UACZ,KAAK;AAAA,UACL,OAAO;AAAA,QACT,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,UACL,MAAM;AAAA,QACR,CAAC,GAAG,KAAK,kBAAkB,GAAG,EAAE,QAAQ;AAAA,UACtC,KAAK;AAAA,UACL,MAAM;AAAA,QACR,CAAC,CAAC,CAAC,CAAC;AAAA,MACN;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,YAAY,CAAC,iBAAiB;AAAA,UAC9B,OAAO,CAAC,YAAY;AAAA,UACpB,OAAO,CAAC,YAAY;AAAA,UACpB,QAAQ,CAAC,aAAa;AAAA,UACtB,kBAAkB,CAAC,uBAAuB;AAAA,UAC1C,YAAY,CAAC,iBAAiB;AAAA,UAC9B,SAAS,CAAC,cAAc;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,IAAM,aAAa,CAAC,KAAK;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,MAAM;AACJ,YAAM,QAAQ,MAAM,UAAU;AAC9B,YAAM,YAAY,MAAM;AACtB,cAAM,QAAQ,CAAC;AACf,cAAM,KAAK,IAAI,GAAG,QAAQ,GAAG;AAC7B,eAAO;AAAA,MACT;AAEA,YAAM,YAAY,oBAAoB,YAAY;AAClD,aAAO,EAAE,OAAO;AAAA,QACd,WAAW,QAAM;AACf,gBAAM,MAAM,GAAG;AACf,cAAI,QAAQ,eAAe,QAAQ,aAAa;AAC9C,2BAAe,MAAM,KAAK;AAC1B,eAAG,eAAe;AAClB,eAAG,gBAAgB;AAAA,UACrB,WAAW,QAAQ,gBAAgB,QAAQ,WAAW;AACpD,2BAAe,MAAM,IAAI;AACzB,eAAG,eAAe;AAClB,eAAG,gBAAgB;AAAA,UACrB;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL,qBAAqB;AAAA,UACrB,gBAAgB,SAAS;AAAA,UACzB,gBAAgB,SAAS;AAAA,UACzB,sBAAsB;AAAA,UACtB,kBAAkB,UAAU;AAAA,UAC5B,kBAAkB,UAAU;AAAA,UAC5B,mBAAmB;AAAA,UACnB,iBAAiB;AAAA,QACnB;AAAA,QACA,OAAO,UAAU;AAAA,QACjB,MAAM;AAAA,QACN,UAAU,WAAW,KAAK;AAAA,QAC1B,cAAc,cAAc,SAAY,YAAY;AAAA,QACpD,mBAAmB,cAAc,SAAY,gBAAgB;AAAA,QAC7D,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,iBAAiB,WAAW,SAAS;AAAA,QACrC,iBAAiB;AAAA,MACnB,GAAG,OAAO,EAAE,OAAO;AAAA,QACjB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR,GAAG,aAAa,KAAK,CAAC,GAAG,EAAE,OAAO;AAAA,QAChC,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR,CAAC,CAAC;AAAA,IACJ;AACA,IAAM,eAAe,CAAC,OAAO,KAAK,KAAK,SAAS;AAC9C,UAAI,SAAS,MAAM,OAAO;AAC1B,UAAI,OAAO,GAAG;AAEZ,gBAAQ,KAAK,MAAM,QAAQ,IAAI,IAAI,OAAO;AAAA,MAC5C;AACA,YAAM,eAAe,MAAM,KAAK,OAAO,GAAG;AAC1C,aAAO,wBAAwB,cAAc,KAAK,KAAK,IAAI;AAAA,IAC7D;AACA,IAAM,eAAe,CAAC,OAAO,KAAK,QAAQ;AACxC,aAAO,MAAM,IAAI,QAAQ,QAAQ,MAAM,MAAM,CAAC;AAAA,IAChD;AACA,IAAI,WAAW;AACf,UAAM,QAAQ;AAAA,MACZ,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}