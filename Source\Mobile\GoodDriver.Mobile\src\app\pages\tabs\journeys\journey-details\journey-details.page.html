<ion-header [translucent]="true">
  <ion-toolbar color="primary">
    <ion-title>Detalhes da Viagem</ion-title>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/tabs/journeys"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Carregando detalhes da viagem...</p>
  </div>

  <!-- Journey content -->
  <div *ngIf="!isLoading && journey">
    <ion-card>
      <ion-card-header>
        <ion-card-title>Resumo da Viagem</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <p><strong>Início:</strong> {{ journey.startDate | date:'short' }}</p>
        <p><strong>Fim:</strong> {{ journey.endDate ? (journey.endDate | date:'short') : 'Em andamento' }}</p>
        <p><strong>Distância:</strong> {{ journey.distance | number:'1.2-2' }} km</p>
        <p *ngIf="journey.vehicleId"><strong>Veículo:</strong> {{ journey.vehicleId }}</p>
      </ion-card-content>
    </ion-card>

    <ion-card>
      <ion-card-header>
        <ion-card-title>
          Localizações Registradas
          <ion-badge color="primary" *ngIf="journey.infosJourney?.length">
            {{ journey.infosJourney?.length }}
          </ion-badge>
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <!-- No locations message -->
        <div *ngIf="!journey.infosJourney || journey.infosJourney.length === 0" class="ion-text-center ion-padding">
          <ion-icon name="location-outline" size="large" color="medium"></ion-icon>
          <p>Nenhuma localização registrada para esta viagem.</p>
          <p><small>As localizações são registradas automaticamente durante a viagem.</small></p>
        </div>

        <!-- Locations list -->
        <ion-list *ngIf="journey.infosJourney && journey.infosJourney.length > 0">
          <ion-item *ngFor="let info of journey.infosJourney; let i = index">
            <ion-avatar slot="start">
              <div class="location-number">{{ i + 1 }}</div>
            </ion-avatar>
            <ion-label>
              <h3>{{ info.timestamp | date:'short' }}</h3>
              <p>
                <ion-icon name="location-outline"></ion-icon>
                {{ info.latitude | number:'1.6-6' }}, {{ info.longitude | number:'1.6-6' }}
              </p>
              <p *ngIf="info.address">
                <ion-icon name="home-outline"></ion-icon>
                <strong>{{ info.address }}</strong>
              </p>
              <p *ngIf="!info.address" class="loading-address">
                <ion-spinner name="dots"></ion-spinner>
                Carregando endereço...
              </p>
              <p *ngIf="info.occurrenceType">
                <ion-icon name="warning-outline" color="warning"></ion-icon>
                <span class="occurrence-type">{{ info.occurrenceType }}</span>
              </p>
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Error state -->
  <div *ngIf="!isLoading && !journey" class="ion-text-center ion-padding">
    <ion-icon name="alert-circle-outline" size="large" color="danger"></ion-icon>
    <h2>Viagem não encontrada</h2>
    <p>Não foi possível carregar os detalhes desta viagem.</p>
    <ion-button routerLink="/tabs/journeys" color="primary">
      Voltar às Viagens
    </ion-button>
  </div>

</ion-content>
