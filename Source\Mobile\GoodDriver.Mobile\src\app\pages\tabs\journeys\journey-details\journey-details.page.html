<ion-header [translucent]="true">
  <ion-toolbar color="primary">
    <ion-title>Detalhes da Viagem</ion-title>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/tabs/journeys"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">

  <ion-card *ngIf="journey">
    <ion-card-header>
      <ion-card-title>Resumo</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <p><strong>Início:</strong> {{ journey.startDate | date:'short' }}</p>
      <p><strong>Fim:</strong> {{ journey.endDate ? (journey.endDate | date:'short') : 'Em andamento' }}</p>
      <p><strong>Distância:</strong> {{ journey.distance | number:'1.2-2' }} km</p>
    </ion-card-content>
  </ion-card>

  <ion-list>
    <ion-list-header>
      Localizações Registradas
    </ion-list-header>

    <div *ngIf="journey.infosJourney?.length === 0" class="ion-text-center ion-padding">
      <p>Nenhuma localização registrada para esta viagem.</p>
    </div>

    <ion-item *ngFor="let info of journey.infosJourney">
      <ion-label>
        <h3>{{ info.timestamp | date:'short' }}</h3>
        <p>Latitude: {{ info.latitude }}, Longitude: {{ info.longitude }}</p>
        <p><strong>Endereço:</strong> {{ info.address }}</p>
      </ion-label>
    </ion-item>
  </ion-list>

</ion-content>
