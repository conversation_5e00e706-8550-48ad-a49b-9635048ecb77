<ion-header [translucent]="true">
  <ion-toolbar color="primary">
    <ion-title>Detalhes da Viagem</ion-title>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/tabs/journeys"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Carregando detalhes da viagem...</p>
  </div>

  <!-- Journey content -->
  <div *ngIf="!isLoading && journey">
    <ion-card>
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="car-sport-outline"></ion-icon>
          Resumo da Viagem
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <!-- Journey Progress Indicator -->
        <div class="journey-progress" *ngIf="journey.infosJourney && journey.infosJourney.length > 1">
          <div class="progress-line">
            <div class="progress-start">
              <ion-icon name="flag-outline" color="success"></ion-icon>
            </div>
            <div class="progress-track"></div>
            <div class="progress-end">
              <ion-icon name="checkered-flag-outline" color="primary"></ion-icon>
            </div>
          </div>
          <div class="progress-info">
            <span class="progress-points">{{ journey.infosJourney.length }} pontos registrados</span>
          </div>
        </div>

        <!-- Journey Details -->
        <div class="journey-details">
          <p><strong>Início:</strong> {{ journey.startDate | date:'short' }}</p>
          <p><strong>Fim:</strong> {{ journey.endDate ? (journey.endDate | date:'short') : 'Em andamento' }}</p>
          <p><strong>Distância:</strong> {{ journey.distance | number:'1.2-2' }} km</p>
          <p *ngIf="journey.vehicleId"><strong>Veículo:</strong> {{ journey.vehicleId }}</p>
        </div>
      </ion-card-content>
    </ion-card>

    <ion-card>
      <ion-card-header>
        <ion-card-title>
          Localizações Registradas
          <ion-badge color="primary" *ngIf="journey.infosJourney?.length">
            {{ journey.infosJourney?.length }}
          </ion-badge>
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <!-- No locations message -->
        <div *ngIf="!journey.infosJourney || journey.infosJourney.length === 0" class="ion-text-center ion-padding">
          <ion-icon name="location-outline" size="large" color="medium"></ion-icon>
          <p>Nenhuma localização registrada para esta viagem.</p>
          <p><small>As localizações são registradas automaticamente durante a viagem.</small></p>
        </div>

        <!-- Locations list -->
        <ion-list *ngIf="journey.infosJourney && journey.infosJourney.length > 0">
          <ion-item *ngFor="let info of journey.infosJourney; let i = index" [class]="getLocationItemClass(i)">
            <!-- Início da viagem -->
            <ion-avatar slot="start" *ngIf="isStartLocation(i)" class="start-location">
              <ion-icon name="flag-outline" color="success"></ion-icon>
            </ion-avatar>

            <!-- Fim da viagem -->
            <ion-avatar slot="start" *ngIf="isEndLocation(i)" class="end-location">
              <ion-icon name="checkered-flag-outline" color="primary"></ion-icon>
            </ion-avatar>

            <!-- Localizações intermediárias -->
            <ion-avatar slot="start" *ngIf="isIntermediateLocation(i)">
              <div class="location-number">{{ i + 1 }}</div>
            </ion-avatar>

            <ion-label>
              <!-- Título especial para início e fim -->
              <h3 *ngIf="isStartLocation(i)" class="start-title">
                <ion-icon name="play-circle-outline" color="success"></ion-icon>
                {{ getLocationDescription(i) }}
              </h3>
              <h3 *ngIf="isEndLocation(i)" class="end-title">
                <ion-icon name="stop-circle-outline" color="primary"></ion-icon>
                {{ getLocationDescription(i) }}
              </h3>
              <h3 *ngIf="isIntermediateLocation(i)" class="intermediate-title">
                {{ getLocationDescription(i) }}
              </h3>

              <!-- Horário -->
              <p class="timestamp">
                <ion-icon name="time-outline"></ion-icon>
                {{ info.timestamp | date:'short' }}
              </p>

              <!-- Coordenadas -->
              <p class="coordinates">
                <ion-icon name="location-outline"></ion-icon>
                {{ info.latitude | number:'1.6-6' }}, {{ info.longitude | number:'1.6-6' }}
              </p>

              <!-- Endereço -->
              <p *ngIf="info.address" class="address">
                <ion-icon name="home-outline"></ion-icon>
                <strong>{{ info.address }}</strong>
              </p>
              <p *ngIf="!info.address" class="loading-address">
                <ion-spinner name="dots"></ion-spinner>
                Carregando endereço...
              </p>

              <!-- Tipo de ocorrência -->
              <p *ngIf="info.occurrenceType" class="occurrence">
                <ion-icon name="warning-outline" color="warning"></ion-icon>
                <span class="occurrence-type">{{ info.occurrenceType }}</span>
              </p>
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Error state -->
  <div *ngIf="!isLoading && !journey" class="ion-text-center ion-padding">
    <ion-icon name="alert-circle-outline" size="large" color="danger"></ion-icon>
    <h2>Viagem não encontrada</h2>
    <p>Não foi possível carregar os detalhes desta viagem.</p>
    <ion-button routerLink="/tabs/journeys" color="primary">
      Voltar às Viagens
    </ion-button>
  </div>

</ion-content>
