﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Migration.Schema
{
    [FluentMigrator.Migration(20250501202000)]
    public class AlterTableOccurrenceAddMessage : FluentMigrator.Migration
    {
        public override void Down()
        {
            if (Schema.Table("Occurrence").Column("Message").Exists())
            {
                Delete.Column("Message").FromTable("Occurrence");
            }
        }

        public override void Up()
        {
            if (!Schema.Table("Occurrence").Column("Message").Exists())
            {
                Alter.Table("Occurrence")
                    .AddColumn("Message").AsString(300).NotNullable();
            }
        }
    }
}
