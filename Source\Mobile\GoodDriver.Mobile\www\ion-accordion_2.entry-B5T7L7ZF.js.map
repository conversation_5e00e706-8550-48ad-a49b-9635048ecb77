{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-accordion_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as Host, f as getElement, c as createEvent } from './index-527b9e34.js';\nimport { r as raf, t as transitionEndAsync, a as addEventListener, b as removeEventListener, g as getElementRoot } from './helpers-d94bc8ad.js';\nimport { l as chevronDown } from './index-e2cf2ceb.js';\nimport { c as config, p as printIonWarning } from './index-cfd9c1f2.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nconst accordionIosCss = \":host{display:block;position:relative;width:100%;background-color:var(--ion-background-color, #ffffff);overflow:hidden;z-index:0}:host(.accordion-expanding) ::slotted(ion-item[slot=header]),:host(.accordion-expanded) ::slotted(ion-item[slot=header]){--border-width:0px}:host(.accordion-animated){-webkit-transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}:host(.accordion-animated) #content{-webkit-transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}#content{overflow:hidden;will-change:max-height}:host(.accordion-collapsing) #content{max-height:0 !important}:host(.accordion-collapsed) #content{display:none}:host(.accordion-expanding) #content{max-height:0}:host(.accordion-expanding) #content-wrapper{overflow:auto}:host(.accordion-disabled) #header,:host(.accordion-readonly) #header,:host(.accordion-disabled) #content,:host(.accordion-readonly) #content{pointer-events:none}:host(.accordion-disabled) #header,:host(.accordion-disabled) #content{opacity:0.4}@media (prefers-reduced-motion: reduce){:host,#content{-webkit-transition:none !important;transition:none !important}}:host(.accordion-next) ::slotted(ion-item[slot=header]){--border-width:0.55px 0px 0.55px 0px}\";\nconst IonAccordionIosStyle0 = accordionIosCss;\nconst accordionMdCss = \":host{display:block;position:relative;width:100%;background-color:var(--ion-background-color, #ffffff);overflow:hidden;z-index:0}:host(.accordion-expanding) ::slotted(ion-item[slot=header]),:host(.accordion-expanded) ::slotted(ion-item[slot=header]){--border-width:0px}:host(.accordion-animated){-webkit-transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}:host(.accordion-animated) #content{-webkit-transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}#content{overflow:hidden;will-change:max-height}:host(.accordion-collapsing) #content{max-height:0 !important}:host(.accordion-collapsed) #content{display:none}:host(.accordion-expanding) #content{max-height:0}:host(.accordion-expanding) #content-wrapper{overflow:auto}:host(.accordion-disabled) #header,:host(.accordion-readonly) #header,:host(.accordion-disabled) #content,:host(.accordion-readonly) #content{pointer-events:none}:host(.accordion-disabled) #header,:host(.accordion-disabled) #content{opacity:0.4}@media (prefers-reduced-motion: reduce){:host,#content{-webkit-transition:none !important;transition:none !important}}\";\nconst IonAccordionMdStyle0 = accordionMdCss;\nconst Accordion = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.updateListener = () => this.updateState(false);\n    this.setItemDefaults = () => {\n      const ionItem = this.getSlottedHeaderIonItem();\n      if (!ionItem) {\n        return;\n      }\n      /**\n       * For a11y purposes, we make\n       * the ion-item a button so users\n       * can tab to it and use keyboard\n       * navigation to get around.\n       */\n      ionItem.button = true;\n      ionItem.detail = false;\n      /**\n       * By default, the lines in an\n       * item should be full here, but\n       * only do that if a user has\n       * not explicitly overridden them\n       */\n      if (ionItem.lines === undefined) {\n        ionItem.lines = 'full';\n      }\n    };\n    this.getSlottedHeaderIonItem = () => {\n      const {\n        headerEl\n      } = this;\n      if (!headerEl) {\n        return;\n      }\n      /**\n       * Get the first ion-item\n       * slotted in the header slot\n       */\n      const slot = headerEl.querySelector('slot');\n      if (!slot) {\n        return;\n      }\n      // This is not defined in unit tests\n      if (slot.assignedElements === undefined) return;\n      return slot.assignedElements().find(el => el.tagName === 'ION-ITEM');\n    };\n    this.setAria = (expanded = false) => {\n      const ionItem = this.getSlottedHeaderIonItem();\n      if (!ionItem) {\n        return;\n      }\n      /**\n       * Get the native <button> element inside of\n       * ion-item because that is what will be focused\n       */\n      const root = getElementRoot(ionItem);\n      const button = root.querySelector('button');\n      if (!button) {\n        return;\n      }\n      button.setAttribute('aria-expanded', `${expanded}`);\n    };\n    this.slotToggleIcon = () => {\n      const ionItem = this.getSlottedHeaderIonItem();\n      if (!ionItem) {\n        return;\n      }\n      const {\n        toggleIconSlot,\n        toggleIcon\n      } = this;\n      /**\n       * Check if there already is a toggle icon.\n       * If so, do not add another one.\n       */\n      const existingToggleIcon = ionItem.querySelector('.ion-accordion-toggle-icon');\n      if (existingToggleIcon) {\n        return;\n      }\n      const iconEl = document.createElement('ion-icon');\n      iconEl.slot = toggleIconSlot;\n      iconEl.lazy = false;\n      iconEl.classList.add('ion-accordion-toggle-icon');\n      iconEl.icon = toggleIcon;\n      iconEl.setAttribute('aria-hidden', 'true');\n      ionItem.appendChild(iconEl);\n    };\n    this.expandAccordion = (initialUpdate = false) => {\n      const {\n        contentEl,\n        contentElWrapper\n      } = this;\n      if (initialUpdate || contentEl === undefined || contentElWrapper === undefined) {\n        this.state = 4 /* AccordionState.Expanded */;\n        return;\n      }\n      if (this.state === 4 /* AccordionState.Expanded */) {\n        return;\n      }\n      if (this.currentRaf !== undefined) {\n        cancelAnimationFrame(this.currentRaf);\n      }\n      if (this.shouldAnimate()) {\n        raf(() => {\n          this.state = 8 /* AccordionState.Expanding */;\n          this.currentRaf = raf(async () => {\n            const contentHeight = contentElWrapper.offsetHeight;\n            const waitForTransition = transitionEndAsync(contentEl, 2000);\n            contentEl.style.setProperty('max-height', `${contentHeight}px`);\n            await waitForTransition;\n            this.state = 4 /* AccordionState.Expanded */;\n            contentEl.style.removeProperty('max-height');\n          });\n        });\n      } else {\n        this.state = 4 /* AccordionState.Expanded */;\n      }\n    };\n    this.collapseAccordion = (initialUpdate = false) => {\n      const {\n        contentEl\n      } = this;\n      if (initialUpdate || contentEl === undefined) {\n        this.state = 1 /* AccordionState.Collapsed */;\n        return;\n      }\n      if (this.state === 1 /* AccordionState.Collapsed */) {\n        return;\n      }\n      if (this.currentRaf !== undefined) {\n        cancelAnimationFrame(this.currentRaf);\n      }\n      if (this.shouldAnimate()) {\n        this.currentRaf = raf(async () => {\n          const contentHeight = contentEl.offsetHeight;\n          contentEl.style.setProperty('max-height', `${contentHeight}px`);\n          raf(async () => {\n            const waitForTransition = transitionEndAsync(contentEl, 2000);\n            this.state = 2 /* AccordionState.Collapsing */;\n            await waitForTransition;\n            this.state = 1 /* AccordionState.Collapsed */;\n            contentEl.style.removeProperty('max-height');\n          });\n        });\n      } else {\n        this.state = 1 /* AccordionState.Collapsed */;\n      }\n    };\n    /**\n     * Helper function to determine if\n     * something should animate.\n     * If prefers-reduced-motion is set\n     * then we should not animate, regardless\n     * of what is set in the config.\n     */\n    this.shouldAnimate = () => {\n      if (typeof window === 'undefined') {\n        return false;\n      }\n      const prefersReducedMotion = matchMedia('(prefers-reduced-motion: reduce)').matches;\n      if (prefersReducedMotion) {\n        return false;\n      }\n      const animated = config.get('animated', true);\n      if (!animated) {\n        return false;\n      }\n      if (this.accordionGroupEl && !this.accordionGroupEl.animated) {\n        return false;\n      }\n      return true;\n    };\n    this.updateState = async (initialUpdate = false) => {\n      const accordionGroup = this.accordionGroupEl;\n      const accordionValue = this.value;\n      if (!accordionGroup) {\n        return;\n      }\n      const value = accordionGroup.value;\n      const shouldExpand = Array.isArray(value) ? value.includes(accordionValue) : value === accordionValue;\n      if (shouldExpand) {\n        this.expandAccordion(initialUpdate);\n        this.isNext = this.isPrevious = false;\n      } else {\n        this.collapseAccordion(initialUpdate);\n        /**\n         * When using popout or inset,\n         * the collapsed accordion items\n         * may need additional border radius\n         * applied. Check to see if the\n         * next or previous accordion is selected.\n         */\n        const nextAccordion = this.getNextSibling();\n        const nextAccordionValue = nextAccordion === null || nextAccordion === void 0 ? void 0 : nextAccordion.value;\n        if (nextAccordionValue !== undefined) {\n          this.isPrevious = Array.isArray(value) ? value.includes(nextAccordionValue) : value === nextAccordionValue;\n        }\n        const previousAccordion = this.getPreviousSibling();\n        const previousAccordionValue = previousAccordion === null || previousAccordion === void 0 ? void 0 : previousAccordion.value;\n        if (previousAccordionValue !== undefined) {\n          this.isNext = Array.isArray(value) ? value.includes(previousAccordionValue) : value === previousAccordionValue;\n        }\n      }\n    };\n    this.getNextSibling = () => {\n      if (!this.el) {\n        return;\n      }\n      const nextSibling = this.el.nextElementSibling;\n      if ((nextSibling === null || nextSibling === void 0 ? void 0 : nextSibling.tagName) !== 'ION-ACCORDION') {\n        return;\n      }\n      return nextSibling;\n    };\n    this.getPreviousSibling = () => {\n      if (!this.el) {\n        return;\n      }\n      const previousSibling = this.el.previousElementSibling;\n      if ((previousSibling === null || previousSibling === void 0 ? void 0 : previousSibling.tagName) !== 'ION-ACCORDION') {\n        return;\n      }\n      return previousSibling;\n    };\n    this.state = 1 /* AccordionState.Collapsed */;\n    this.isNext = false;\n    this.isPrevious = false;\n    this.value = `ion-accordion-${accordionIds++}`;\n    this.disabled = false;\n    this.readonly = false;\n    this.toggleIcon = chevronDown;\n    this.toggleIconSlot = 'end';\n  }\n  valueChanged() {\n    this.updateState();\n  }\n  connectedCallback() {\n    var _a;\n    const accordionGroupEl = this.accordionGroupEl = (_a = this.el) === null || _a === void 0 ? void 0 : _a.closest('ion-accordion-group');\n    if (accordionGroupEl) {\n      this.updateState(true);\n      addEventListener(accordionGroupEl, 'ionValueChange', this.updateListener);\n    }\n  }\n  disconnectedCallback() {\n    const accordionGroupEl = this.accordionGroupEl;\n    if (accordionGroupEl) {\n      removeEventListener(accordionGroupEl, 'ionValueChange', this.updateListener);\n    }\n  }\n  componentDidLoad() {\n    this.setItemDefaults();\n    this.slotToggleIcon();\n    /**\n     * We need to wait a tick because we\n     * just set ionItem.button = true and\n     * the button has not have been rendered yet.\n     */\n    raf(() => {\n      /**\n       * Set aria label on button inside of ion-item\n       * once the inner content has been rendered.\n       */\n      const expanded = this.state === 4 /* AccordionState.Expanded */ || this.state === 8 /* AccordionState.Expanding */;\n      this.setAria(expanded);\n    });\n  }\n  toggleExpanded() {\n    const {\n      accordionGroupEl,\n      disabled,\n      readonly,\n      value,\n      state\n    } = this;\n    if (disabled || readonly) return;\n    if (accordionGroupEl) {\n      /**\n       * Because the accordion group may or may\n       * not allow multiple accordions open, we\n       * need to request the toggling of this\n       * accordion and the accordion group will\n       * make the decision on whether or not\n       * to allow it.\n       */\n      const expand = state === 1 /* AccordionState.Collapsed */ || state === 2 /* AccordionState.Collapsing */;\n      accordionGroupEl.requestAccordionToggle(value, expand);\n    }\n  }\n  render() {\n    const {\n      disabled,\n      readonly\n    } = this;\n    const mode = getIonMode(this);\n    const expanded = this.state === 4 /* AccordionState.Expanded */ || this.state === 8 /* AccordionState.Expanding */;\n    const headerPart = expanded ? 'header expanded' : 'header';\n    const contentPart = expanded ? 'content expanded' : 'content';\n    this.setAria(expanded);\n    return h(Host, {\n      key: '073e1d02c18dcbc20c68648426e87c14750c031d',\n      class: {\n        [mode]: true,\n        'accordion-expanding': this.state === 8 /* AccordionState.Expanding */,\n        'accordion-expanded': this.state === 4 /* AccordionState.Expanded */,\n        'accordion-collapsing': this.state === 2 /* AccordionState.Collapsing */,\n        'accordion-collapsed': this.state === 1 /* AccordionState.Collapsed */,\n        'accordion-next': this.isNext,\n        'accordion-previous': this.isPrevious,\n        'accordion-disabled': disabled,\n        'accordion-readonly': readonly,\n        'accordion-animated': this.shouldAnimate()\n      }\n    }, h(\"div\", {\n      key: '9b4cf326de8bb6b4033992903c0c1bfd7eea9bcc',\n      onClick: () => this.toggleExpanded(),\n      id: \"header\",\n      part: headerPart,\n      \"aria-controls\": \"content\",\n      ref: headerEl => this.headerEl = headerEl\n    }, h(\"slot\", {\n      key: '464c32a37f64655eacf4218284214f5f30b14a1e',\n      name: \"header\"\n    })), h(\"div\", {\n      key: '8bb52e6a62d7de0106b253201a89a32e79d9a594',\n      id: \"content\",\n      part: contentPart,\n      role: \"region\",\n      \"aria-labelledby\": \"header\",\n      ref: contentEl => this.contentEl = contentEl\n    }, h(\"div\", {\n      key: '1d9dfd952ad493754aaeea7a8f625b33c2dd90a0',\n      id: \"content-wrapper\",\n      ref: contentElWrapper => this.contentElWrapper = contentElWrapper\n    }, h(\"slot\", {\n      key: '970dfbc55a612d739d0ca3b7b1a08e5c96d0c479',\n      name: \"content\"\n    }))));\n  }\n  static get delegatesFocus() {\n    return true;\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"value\": [\"valueChanged\"]\n    };\n  }\n};\nlet accordionIds = 0;\nAccordion.style = {\n  ios: IonAccordionIosStyle0,\n  md: IonAccordionMdStyle0\n};\nconst accordionGroupIosCss = \":host{display:block}:host(.accordion-group-expand-inset){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanding),:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanded){border-bottom:none}\";\nconst IonAccordionGroupIosStyle0 = accordionGroupIosCss;\nconst accordionGroupMdCss = \":host{display:block}:host(.accordion-group-expand-inset){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion){-webkit-box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanding),:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanded){margin-left:0;margin-right:0;margin-top:16px;margin-bottom:16px;border-radius:6px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-previous){border-end-end-radius:6px;border-end-start-radius:6px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-next){border-start-start-radius:6px;border-start-end-radius:6px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion):first-of-type{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}\";\nconst IonAccordionGroupMdStyle0 = accordionGroupMdCss;\nconst AccordionGroup = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionValueChange = createEvent(this, \"ionValueChange\", 7);\n    this.animated = true;\n    this.multiple = undefined;\n    this.value = undefined;\n    this.disabled = false;\n    this.readonly = false;\n    this.expand = 'compact';\n  }\n  valueChanged() {\n    const {\n      value,\n      multiple\n    } = this;\n    if (!multiple && Array.isArray(value)) {\n      /**\n       * We do some processing on the `value` array so\n       * that it looks more like an array when logged to\n       * the console.\n       * Example given ['a', 'b']\n       * Default toString() behavior: a,b\n       * Custom behavior: ['a', 'b']\n       */\n      printIonWarning(`[ion-accordion-group] - An array of values was passed, but multiple is \"false\". This is incorrect usage and may result in unexpected behaviors. To dismiss this warning, pass a string to the \"value\" property when multiple=\"false\".\n\n  Value Passed: [${value.map(v => `'${v}'`).join(', ')}]\n`, this.el);\n    }\n    /**\n     * Do not use `value` here as that will be\n     * not account for the adjustment we make above.\n     */\n    this.ionValueChange.emit({\n      value: this.value\n    });\n  }\n  async disabledChanged() {\n    const {\n      disabled\n    } = this;\n    const accordions = await this.getAccordions();\n    for (const accordion of accordions) {\n      accordion.disabled = disabled;\n    }\n  }\n  async readonlyChanged() {\n    const {\n      readonly\n    } = this;\n    const accordions = await this.getAccordions();\n    for (const accordion of accordions) {\n      accordion.readonly = readonly;\n    }\n  }\n  async onKeydown(ev) {\n    const activeElement = document.activeElement;\n    if (!activeElement) {\n      return;\n    }\n    /**\n     * Make sure focus is in the header, not the body, of the accordion. This ensures\n     * that if there are any interactable elements in the body, their keyboard\n     * interaction doesn't get stolen by the accordion. Example: using up/down keys\n     * in ion-textarea.\n     */\n    const activeAccordionHeader = activeElement.closest('ion-accordion [slot=\"header\"]');\n    if (!activeAccordionHeader) {\n      return;\n    }\n    const accordionEl = activeElement.tagName === 'ION-ACCORDION' ? activeElement : activeElement.closest('ion-accordion');\n    if (!accordionEl) {\n      return;\n    }\n    const closestGroup = accordionEl.closest('ion-accordion-group');\n    if (closestGroup !== this.el) {\n      return;\n    }\n    // If the active accordion is not in the current array of accordions, do not do anything\n    const accordions = await this.getAccordions();\n    const startingIndex = accordions.findIndex(a => a === accordionEl);\n    if (startingIndex === -1) {\n      return;\n    }\n    let accordion;\n    if (ev.key === 'ArrowDown') {\n      accordion = this.findNextAccordion(accordions, startingIndex);\n    } else if (ev.key === 'ArrowUp') {\n      accordion = this.findPreviousAccordion(accordions, startingIndex);\n    } else if (ev.key === 'Home') {\n      accordion = accordions[0];\n    } else if (ev.key === 'End') {\n      accordion = accordions[accordions.length - 1];\n    }\n    if (accordion !== undefined && accordion !== activeElement) {\n      accordion.focus();\n    }\n  }\n  async componentDidLoad() {\n    if (this.disabled) {\n      this.disabledChanged();\n    }\n    if (this.readonly) {\n      this.readonlyChanged();\n    }\n    /**\n     * When binding values in frameworks such as Angular\n     * it is possible for the value to be set after the Web Component\n     * initializes but before the value watcher is set up in Stencil.\n     * As a result, the watcher callback may not be fired.\n     * We work around this by manually calling the watcher\n     * callback when the component has loaded and the watcher\n     * is configured.\n     */\n    this.valueChanged();\n  }\n  /**\n   * Sets the value property and emits ionChange.\n   * This should only be called when the user interacts\n   * with the accordion and not for any update\n   * to the value property. The exception is when\n   * the app sets the value of a single-select\n   * accordion group to an array.\n   */\n  setValue(accordionValue) {\n    const value = this.value = accordionValue;\n    this.ionChange.emit({\n      value\n    });\n  }\n  /**\n   * This method is used to ensure that the value\n   * of ion-accordion-group is being set in a valid\n   * way. This method should only be called in\n   * response to a user generated action.\n   * @internal\n   */\n  async requestAccordionToggle(accordionValue, accordionExpand) {\n    const {\n      multiple,\n      value,\n      readonly,\n      disabled\n    } = this;\n    if (readonly || disabled) {\n      return;\n    }\n    if (accordionExpand) {\n      /**\n       * If group accepts multiple values\n       * check to see if value is already in\n       * in values array. If not, add it\n       * to the array.\n       */\n      if (multiple) {\n        const groupValue = value !== null && value !== void 0 ? value : [];\n        const processedValue = Array.isArray(groupValue) ? groupValue : [groupValue];\n        const valueExists = processedValue.find(v => v === accordionValue);\n        if (valueExists === undefined && accordionValue !== undefined) {\n          this.setValue([...processedValue, accordionValue]);\n        }\n      } else {\n        this.setValue(accordionValue);\n      }\n    } else {\n      /**\n       * If collapsing accordion, either filter the value\n       * out of the values array or unset the value.\n       */\n      if (multiple) {\n        const groupValue = value !== null && value !== void 0 ? value : [];\n        const processedValue = Array.isArray(groupValue) ? groupValue : [groupValue];\n        this.setValue(processedValue.filter(v => v !== accordionValue));\n      } else {\n        this.setValue(undefined);\n      }\n    }\n  }\n  findNextAccordion(accordions, startingIndex) {\n    const nextAccordion = accordions[startingIndex + 1];\n    if (nextAccordion === undefined) {\n      return accordions[0];\n    }\n    return nextAccordion;\n  }\n  findPreviousAccordion(accordions, startingIndex) {\n    const prevAccordion = accordions[startingIndex - 1];\n    if (prevAccordion === undefined) {\n      return accordions[accordions.length - 1];\n    }\n    return prevAccordion;\n  }\n  /**\n   * @internal\n   */\n  async getAccordions() {\n    return Array.from(this.el.querySelectorAll(':scope > ion-accordion'));\n  }\n  render() {\n    const {\n      disabled,\n      readonly,\n      expand\n    } = this;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: 'd1a79a93179474fbba66fcf11a92f4871dacc975',\n      class: {\n        [mode]: true,\n        'accordion-group-disabled': disabled,\n        'accordion-group-readonly': readonly,\n        [`accordion-group-expand-${expand}`]: true\n      },\n      role: \"presentation\"\n    }, h(\"slot\", {\n      key: 'e6b8954b686d1fbb4fc92adb07fddc97a24b0a31'\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"value\": [\"valueChanged\"],\n      \"disabled\": [\"disabledChanged\"],\n      \"readonly\": [\"readonlyChanged\"]\n    };\n  }\n};\nAccordionGroup.style = {\n  ios: IonAccordionGroupIosStyle0,\n  md: IonAccordionGroupMdStyle0\n};\nexport { Accordion as ion_accordion, AccordionGroup as ion_accordion_group };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAQM,iBACA,uBACA,gBACA,sBACA,WA+VF,cAKE,sBACA,4BACA,qBACA,2BACA;AApXN;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA,IAAM,kBAAkB;AACxB,IAAM,wBAAwB;AAC9B,IAAM,iBAAiB;AACvB,IAAM,uBAAuB;AAC7B,IAAM,YAAY,MAAM;AAAA,MACtB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,iBAAiB,MAAM,KAAK,YAAY,KAAK;AAClD,aAAK,kBAAkB,MAAM;AAC3B,gBAAM,UAAU,KAAK,wBAAwB;AAC7C,cAAI,CAAC,SAAS;AACZ;AAAA,UACF;AAOA,kBAAQ,SAAS;AACjB,kBAAQ,SAAS;AAOjB,cAAI,QAAQ,UAAU,QAAW;AAC/B,oBAAQ,QAAQ;AAAA,UAClB;AAAA,QACF;AACA,aAAK,0BAA0B,MAAM;AACnC,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,cAAI,CAAC,UAAU;AACb;AAAA,UACF;AAKA,gBAAM,OAAO,SAAS,cAAc,MAAM;AAC1C,cAAI,CAAC,MAAM;AACT;AAAA,UACF;AAEA,cAAI,KAAK,qBAAqB,OAAW;AACzC,iBAAO,KAAK,iBAAiB,EAAE,KAAK,QAAM,GAAG,YAAY,UAAU;AAAA,QACrE;AACA,aAAK,UAAU,CAAC,WAAW,UAAU;AACnC,gBAAM,UAAU,KAAK,wBAAwB;AAC7C,cAAI,CAAC,SAAS;AACZ;AAAA,UACF;AAKA,gBAAM,OAAO,eAAe,OAAO;AACnC,gBAAM,SAAS,KAAK,cAAc,QAAQ;AAC1C,cAAI,CAAC,QAAQ;AACX;AAAA,UACF;AACA,iBAAO,aAAa,iBAAiB,GAAG,QAAQ,EAAE;AAAA,QACpD;AACA,aAAK,iBAAiB,MAAM;AAC1B,gBAAM,UAAU,KAAK,wBAAwB;AAC7C,cAAI,CAAC,SAAS;AACZ;AAAA,UACF;AACA,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AAKJ,gBAAM,qBAAqB,QAAQ,cAAc,4BAA4B;AAC7E,cAAI,oBAAoB;AACtB;AAAA,UACF;AACA,gBAAM,SAAS,SAAS,cAAc,UAAU;AAChD,iBAAO,OAAO;AACd,iBAAO,OAAO;AACd,iBAAO,UAAU,IAAI,2BAA2B;AAChD,iBAAO,OAAO;AACd,iBAAO,aAAa,eAAe,MAAM;AACzC,kBAAQ,YAAY,MAAM;AAAA,QAC5B;AACA,aAAK,kBAAkB,CAAC,gBAAgB,UAAU;AAChD,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,iBAAiB,cAAc,UAAa,qBAAqB,QAAW;AAC9E,iBAAK,QAAQ;AACb;AAAA,UACF;AACA,cAAI,KAAK,UAAU,GAAiC;AAClD;AAAA,UACF;AACA,cAAI,KAAK,eAAe,QAAW;AACjC,iCAAqB,KAAK,UAAU;AAAA,UACtC;AACA,cAAI,KAAK,cAAc,GAAG;AACxB,gBAAI,MAAM;AACR,mBAAK,QAAQ;AACb,mBAAK,aAAa,IAAI,MAAY;AAChC,sBAAM,gBAAgB,iBAAiB;AACvC,sBAAM,oBAAoB,mBAAmB,WAAW,GAAI;AAC5D,0BAAU,MAAM,YAAY,cAAc,GAAG,aAAa,IAAI;AAC9D,sBAAM;AACN,qBAAK,QAAQ;AACb,0BAAU,MAAM,eAAe,YAAY;AAAA,cAC7C,EAAC;AAAA,YACH,CAAC;AAAA,UACH,OAAO;AACL,iBAAK,QAAQ;AAAA,UACf;AAAA,QACF;AACA,aAAK,oBAAoB,CAAC,gBAAgB,UAAU;AAClD,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,cAAI,iBAAiB,cAAc,QAAW;AAC5C,iBAAK,QAAQ;AACb;AAAA,UACF;AACA,cAAI,KAAK,UAAU,GAAkC;AACnD;AAAA,UACF;AACA,cAAI,KAAK,eAAe,QAAW;AACjC,iCAAqB,KAAK,UAAU;AAAA,UACtC;AACA,cAAI,KAAK,cAAc,GAAG;AACxB,iBAAK,aAAa,IAAI,MAAY;AAChC,oBAAM,gBAAgB,UAAU;AAChC,wBAAU,MAAM,YAAY,cAAc,GAAG,aAAa,IAAI;AAC9D,kBAAI,MAAY;AACd,sBAAM,oBAAoB,mBAAmB,WAAW,GAAI;AAC5D,qBAAK,QAAQ;AACb,sBAAM;AACN,qBAAK,QAAQ;AACb,0BAAU,MAAM,eAAe,YAAY;AAAA,cAC7C,EAAC;AAAA,YACH,EAAC;AAAA,UACH,OAAO;AACL,iBAAK,QAAQ;AAAA,UACf;AAAA,QACF;AAQA,aAAK,gBAAgB,MAAM;AACzB,cAAI,OAAO,WAAW,aAAa;AACjC,mBAAO;AAAA,UACT;AACA,gBAAM,uBAAuB,WAAW,kCAAkC,EAAE;AAC5E,cAAI,sBAAsB;AACxB,mBAAO;AAAA,UACT;AACA,gBAAM,WAAW,OAAO,IAAI,YAAY,IAAI;AAC5C,cAAI,CAAC,UAAU;AACb,mBAAO;AAAA,UACT;AACA,cAAI,KAAK,oBAAoB,CAAC,KAAK,iBAAiB,UAAU;AAC5D,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AACA,aAAK,cAAc,CAAO,gBAAgB,UAAU;AAClD,gBAAM,iBAAiB,KAAK;AAC5B,gBAAM,iBAAiB,KAAK;AAC5B,cAAI,CAAC,gBAAgB;AACnB;AAAA,UACF;AACA,gBAAM,QAAQ,eAAe;AAC7B,gBAAM,eAAe,MAAM,QAAQ,KAAK,IAAI,MAAM,SAAS,cAAc,IAAI,UAAU;AACvF,cAAI,cAAc;AAChB,iBAAK,gBAAgB,aAAa;AAClC,iBAAK,SAAS,KAAK,aAAa;AAAA,UAClC,OAAO;AACL,iBAAK,kBAAkB,aAAa;AAQpC,kBAAM,gBAAgB,KAAK,eAAe;AAC1C,kBAAM,qBAAqB,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc;AACvG,gBAAI,uBAAuB,QAAW;AACpC,mBAAK,aAAa,MAAM,QAAQ,KAAK,IAAI,MAAM,SAAS,kBAAkB,IAAI,UAAU;AAAA,YAC1F;AACA,kBAAM,oBAAoB,KAAK,mBAAmB;AAClD,kBAAM,yBAAyB,sBAAsB,QAAQ,sBAAsB,SAAS,SAAS,kBAAkB;AACvH,gBAAI,2BAA2B,QAAW;AACxC,mBAAK,SAAS,MAAM,QAAQ,KAAK,IAAI,MAAM,SAAS,sBAAsB,IAAI,UAAU;AAAA,YAC1F;AAAA,UACF;AAAA,QACF;AACA,aAAK,iBAAiB,MAAM;AAC1B,cAAI,CAAC,KAAK,IAAI;AACZ;AAAA,UACF;AACA,gBAAM,cAAc,KAAK,GAAG;AAC5B,eAAK,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,aAAa,iBAAiB;AACvG;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,aAAK,qBAAqB,MAAM;AAC9B,cAAI,CAAC,KAAK,IAAI;AACZ;AAAA,UACF;AACA,gBAAM,kBAAkB,KAAK,GAAG;AAChC,eAAK,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,aAAa,iBAAiB;AACnH;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,aAAK,QAAQ;AACb,aAAK,SAAS;AACd,aAAK,aAAa;AAClB,aAAK,QAAQ,iBAAiB,cAAc;AAC5C,aAAK,WAAW;AAChB,aAAK,WAAW;AAChB,aAAK,aAAa;AAClB,aAAK,iBAAiB;AAAA,MACxB;AAAA,MACA,eAAe;AACb,aAAK,YAAY;AAAA,MACnB;AAAA,MACA,oBAAoB;AAClB,YAAI;AACJ,cAAM,mBAAmB,KAAK,oBAAoB,KAAK,KAAK,QAAQ,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,qBAAqB;AACrI,YAAI,kBAAkB;AACpB,eAAK,YAAY,IAAI;AACrB,2BAAiB,kBAAkB,kBAAkB,KAAK,cAAc;AAAA,QAC1E;AAAA,MACF;AAAA,MACA,uBAAuB;AACrB,cAAM,mBAAmB,KAAK;AAC9B,YAAI,kBAAkB;AACpB,8BAAoB,kBAAkB,kBAAkB,KAAK,cAAc;AAAA,QAC7E;AAAA,MACF;AAAA,MACA,mBAAmB;AACjB,aAAK,gBAAgB;AACrB,aAAK,eAAe;AAMpB,YAAI,MAAM;AAKR,gBAAM,WAAW,KAAK,UAAU,KAAmC,KAAK,UAAU;AAClF,eAAK,QAAQ,QAAQ;AAAA,QACvB,CAAC;AAAA,MACH;AAAA,MACA,iBAAiB;AACf,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,YAAY,SAAU;AAC1B,YAAI,kBAAkB;AASpB,gBAAM,SAAS,UAAU,KAAoC,UAAU;AACvE,2BAAiB,uBAAuB,OAAO,MAAM;AAAA,QACvD;AAAA,MACF;AAAA,MACA,SAAS;AACP,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM,WAAW,KAAK,UAAU,KAAmC,KAAK,UAAU;AAClF,cAAM,aAAa,WAAW,oBAAoB;AAClD,cAAM,cAAc,WAAW,qBAAqB;AACpD,aAAK,QAAQ,QAAQ;AACrB,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,OAAO;AAAA,YACL,CAAC,IAAI,GAAG;AAAA,YACR,uBAAuB,KAAK,UAAU;AAAA,YACtC,sBAAsB,KAAK,UAAU;AAAA,YACrC,wBAAwB,KAAK,UAAU;AAAA,YACvC,uBAAuB,KAAK,UAAU;AAAA,YACtC,kBAAkB,KAAK;AAAA,YACvB,sBAAsB,KAAK;AAAA,YAC3B,sBAAsB;AAAA,YACtB,sBAAsB;AAAA,YACtB,sBAAsB,KAAK,cAAc;AAAA,UAC3C;AAAA,QACF,GAAG,EAAE,OAAO;AAAA,UACV,KAAK;AAAA,UACL,SAAS,MAAM,KAAK,eAAe;AAAA,UACnC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,iBAAiB;AAAA,UACjB,KAAK,cAAY,KAAK,WAAW;AAAA,QACnC,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,UACL,MAAM;AAAA,QACR,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,UACZ,KAAK;AAAA,UACL,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,mBAAmB;AAAA,UACnB,KAAK,eAAa,KAAK,YAAY;AAAA,QACrC,GAAG,EAAE,OAAO;AAAA,UACV,KAAK;AAAA,UACL,IAAI;AAAA,UACJ,KAAK,sBAAoB,KAAK,mBAAmB;AAAA,QACnD,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,UACL,MAAM;AAAA,QACR,CAAC,CAAC,CAAC,CAAC;AAAA,MACN;AAAA,MACA,WAAW,iBAAiB;AAC1B,eAAO;AAAA,MACT;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,SAAS,CAAC,cAAc;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,IAAI,eAAe;AACnB,cAAU,QAAQ;AAAA,MAChB,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AACA,IAAM,uBAAuB;AAC7B,IAAM,6BAA6B;AACnC,IAAM,sBAAsB;AAC5B,IAAM,4BAA4B;AAClC,IAAM,iBAAiB,MAAM;AAAA,MAC3B,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,aAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,aAAK,WAAW;AAChB,aAAK,WAAW;AAChB,aAAK,QAAQ;AACb,aAAK,WAAW;AAChB,aAAK,WAAW;AAChB,aAAK,SAAS;AAAA,MAChB;AAAA,MACA,eAAe;AACb,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,CAAC,YAAY,MAAM,QAAQ,KAAK,GAAG;AASrC,0BAAgB;AAAA;AAAA,mBAEH,MAAM,IAAI,OAAK,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC;AAAA,GACnD,KAAK,EAAE;AAAA,QACN;AAKA,aAAK,eAAe,KAAK;AAAA,UACvB,OAAO,KAAK;AAAA,QACd,CAAC;AAAA,MACH;AAAA,MACM,kBAAkB;AAAA;AACtB,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,gBAAM,aAAa,MAAM,KAAK,cAAc;AAC5C,qBAAW,aAAa,YAAY;AAClC,sBAAU,WAAW;AAAA,UACvB;AAAA,QACF;AAAA;AAAA,MACM,kBAAkB;AAAA;AACtB,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,gBAAM,aAAa,MAAM,KAAK,cAAc;AAC5C,qBAAW,aAAa,YAAY;AAClC,sBAAU,WAAW;AAAA,UACvB;AAAA,QACF;AAAA;AAAA,MACM,UAAU,IAAI;AAAA;AAClB,gBAAM,gBAAgB,SAAS;AAC/B,cAAI,CAAC,eAAe;AAClB;AAAA,UACF;AAOA,gBAAM,wBAAwB,cAAc,QAAQ,+BAA+B;AACnF,cAAI,CAAC,uBAAuB;AAC1B;AAAA,UACF;AACA,gBAAM,cAAc,cAAc,YAAY,kBAAkB,gBAAgB,cAAc,QAAQ,eAAe;AACrH,cAAI,CAAC,aAAa;AAChB;AAAA,UACF;AACA,gBAAM,eAAe,YAAY,QAAQ,qBAAqB;AAC9D,cAAI,iBAAiB,KAAK,IAAI;AAC5B;AAAA,UACF;AAEA,gBAAM,aAAa,MAAM,KAAK,cAAc;AAC5C,gBAAM,gBAAgB,WAAW,UAAU,OAAK,MAAM,WAAW;AACjE,cAAI,kBAAkB,IAAI;AACxB;AAAA,UACF;AACA,cAAI;AACJ,cAAI,GAAG,QAAQ,aAAa;AAC1B,wBAAY,KAAK,kBAAkB,YAAY,aAAa;AAAA,UAC9D,WAAW,GAAG,QAAQ,WAAW;AAC/B,wBAAY,KAAK,sBAAsB,YAAY,aAAa;AAAA,UAClE,WAAW,GAAG,QAAQ,QAAQ;AAC5B,wBAAY,WAAW,CAAC;AAAA,UAC1B,WAAW,GAAG,QAAQ,OAAO;AAC3B,wBAAY,WAAW,WAAW,SAAS,CAAC;AAAA,UAC9C;AACA,cAAI,cAAc,UAAa,cAAc,eAAe;AAC1D,sBAAU,MAAM;AAAA,UAClB;AAAA,QACF;AAAA;AAAA,MACM,mBAAmB;AAAA;AACvB,cAAI,KAAK,UAAU;AACjB,iBAAK,gBAAgB;AAAA,UACvB;AACA,cAAI,KAAK,UAAU;AACjB,iBAAK,gBAAgB;AAAA,UACvB;AAUA,eAAK,aAAa;AAAA,QACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,SAAS,gBAAgB;AACvB,cAAM,QAAQ,KAAK,QAAQ;AAC3B,aAAK,UAAU,KAAK;AAAA,UAClB;AAAA,QACF,CAAC;AAAA,MACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQM,uBAAuB,gBAAgB,iBAAiB;AAAA;AAC5D,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,YAAY,UAAU;AACxB;AAAA,UACF;AACA,cAAI,iBAAiB;AAOnB,gBAAI,UAAU;AACZ,oBAAM,aAAa,UAAU,QAAQ,UAAU,SAAS,QAAQ,CAAC;AACjE,oBAAM,iBAAiB,MAAM,QAAQ,UAAU,IAAI,aAAa,CAAC,UAAU;AAC3E,oBAAM,cAAc,eAAe,KAAK,OAAK,MAAM,cAAc;AACjE,kBAAI,gBAAgB,UAAa,mBAAmB,QAAW;AAC7D,qBAAK,SAAS,CAAC,GAAG,gBAAgB,cAAc,CAAC;AAAA,cACnD;AAAA,YACF,OAAO;AACL,mBAAK,SAAS,cAAc;AAAA,YAC9B;AAAA,UACF,OAAO;AAKL,gBAAI,UAAU;AACZ,oBAAM,aAAa,UAAU,QAAQ,UAAU,SAAS,QAAQ,CAAC;AACjE,oBAAM,iBAAiB,MAAM,QAAQ,UAAU,IAAI,aAAa,CAAC,UAAU;AAC3E,mBAAK,SAAS,eAAe,OAAO,OAAK,MAAM,cAAc,CAAC;AAAA,YAChE,OAAO;AACL,mBAAK,SAAS,MAAS;AAAA,YACzB;AAAA,UACF;AAAA,QACF;AAAA;AAAA,MACA,kBAAkB,YAAY,eAAe;AAC3C,cAAM,gBAAgB,WAAW,gBAAgB,CAAC;AAClD,YAAI,kBAAkB,QAAW;AAC/B,iBAAO,WAAW,CAAC;AAAA,QACrB;AACA,eAAO;AAAA,MACT;AAAA,MACA,sBAAsB,YAAY,eAAe;AAC/C,cAAM,gBAAgB,WAAW,gBAAgB,CAAC;AAClD,YAAI,kBAAkB,QAAW;AAC/B,iBAAO,WAAW,WAAW,SAAS,CAAC;AAAA,QACzC;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA,MAIM,gBAAgB;AAAA;AACpB,iBAAO,MAAM,KAAK,KAAK,GAAG,iBAAiB,wBAAwB,CAAC;AAAA,QACtE;AAAA;AAAA,MACA,SAAS;AACP,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,OAAO,WAAW,IAAI;AAC5B,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,OAAO;AAAA,YACL,CAAC,IAAI,GAAG;AAAA,YACR,4BAA4B;AAAA,YAC5B,4BAA4B;AAAA,YAC5B,CAAC,0BAA0B,MAAM,EAAE,GAAG;AAAA,UACxC;AAAA,UACA,MAAM;AAAA,QACR,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,QACP,CAAC,CAAC;AAAA,MACJ;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,SAAS,CAAC,cAAc;AAAA,UACxB,YAAY,CAAC,iBAAiB;AAAA,UAC9B,YAAY,CAAC,iBAAiB;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AACA,mBAAe,QAAQ;AAAA,MACrB,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}