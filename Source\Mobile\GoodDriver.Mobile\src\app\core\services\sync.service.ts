import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { NetworkService } from './network.service';
import { VehicleService } from './vehicle.service';
import { JourneyStorageService } from './journey-storage.service';
import { SessionService } from './session.service';
import { ToastService } from './toast.service';
import { SyncStatus } from '../models/sync.model';

@Injectable({
  providedIn: 'root'
})
export class SyncService {
  // BehaviorSubject to track the sync status
  private syncInProgressSubject = new BehaviorSubject<boolean>(false);
  
  // Observable that components can subscribe to
  public syncInProgress$: Observable<boolean> = this.syncInProgressSubject.asObservable();
  
  // BehaviorSubject to track the last sync time
  private lastSyncTimeSubject = new BehaviorSubject<Date | null>(null);
  
  // Observable that components can subscribe to
  public lastSyncTime$: Observable<Date | null> = this.lastSyncTimeSubject.asObservable();
  
  // BehaviorSubject to track the pending sync count
  private pendingSyncCountSubject = new BehaviorSubject<number>(0);
  
  // Observable that components can subscribe to
  public pendingSyncCount$: Observable<number> = this.pendingSyncCountSubject.asObservable();

  constructor(
    private networkService: NetworkService,
    private vehicleService: VehicleService,
    private journeyService: JourneyStorageService,
    private sessionService: SessionService,
    private toastService: ToastService
  ) {
    // Initialize the last sync time from local storage
    const lastSyncTime = localStorage.getItem('lastSyncTime');
    if (lastSyncTime) {
      this.lastSyncTimeSubject.next(new Date(lastSyncTime));
    }
    
    // Check for pending sync items on initialization
    this.checkPendingSyncItems();
  }

  /**
   * Checks for items that need to be synchronized
   */
  async checkPendingSyncItems(): Promise<void> {
    try {
      const userId = await this.sessionService.getUserId();
      if (!userId) return;
      
      // Get all vehicles for the user
      const vehicles = await this.vehicleService.listAllLocal(userId);
      
      // Get all journeys for the user
      const journeys = await this.journeyService.getAllJourneysByUser(userId);
      
      // Count items that need to be synced
      const pendingVehicles = vehicles.filter(v => 
        v.syncStatus === SyncStatus.PendingCreate || 
        v.syncStatus === SyncStatus.PendingUpdate || 
        v.syncStatus === SyncStatus.PendingDelete
      );
      
      const pendingJourneys = journeys.filter(j => 
        j.syncStatus === SyncStatus.PendingCreate || 
        j.syncStatus === SyncStatus.PendingUpdate || 
        j.syncStatus === SyncStatus.PendingDelete
      );
      
      // Update the pending sync count
      const totalPending = pendingVehicles.length + pendingJourneys.length;
      this.pendingSyncCountSubject.next(totalPending);
      
    } catch (error) {
      console.error('Error checking pending sync items:', error);
    }
  }

  /**
   * Synchronizes all pending items with the server
   */
  async syncAll(): Promise<boolean> {
    // Check if we're online
    if (!this.networkService.isOnlineNow()) {
      this.toastService.showToast('Sem conexão com a internet. Não é possível sincronizar.', 'warning');
      return false;
    }
    
    // Check if sync is already in progress
    if (this.syncInProgressSubject.getValue()) {
      this.toastService.showToast('Sincronização já em andamento.', 'info');
      return false;
    }
    
    // Set sync in progress
    this.syncInProgressSubject.next(true);
    
    try {
      const userId = await this.sessionService.getUserId();
      if (!userId) {
        this.syncInProgressSubject.next(false);
        return false;
      }
      
      // Sync vehicles
      const vehicleSuccess = await this.syncVehicles(userId);
      
      // Sync journeys
      const journeySuccess = await this.syncJourneys(userId);
      
      // Update last sync time
      const now = new Date();
      this.lastSyncTimeSubject.next(now);
      localStorage.setItem('lastSyncTime', now.toISOString());
      
      // Check for pending items again
      await this.checkPendingSyncItems();
      
      // Show success message
      this.toastService.showToast('Sincronização concluída com sucesso!', 'success');
      
      return vehicleSuccess && journeySuccess;
    } catch (error) {
      console.error('Error during synchronization:', error);
      this.toastService.showToast('Erro durante a sincronização.', 'danger');
      return false;
    } finally {
      // Set sync not in progress
      this.syncInProgressSubject.next(false);
    }
  }

  /**
   * Synchronizes vehicles with the server
   * First downloads existing vehicles, then uploads pending ones
   */
  private async syncVehicles(userId: string): Promise<boolean> {
    try {
      console.log('Iniciando sincronização de veículos...');

      // Use the new comprehensive sync method from VehicleService
      const success = await this.vehicleService.syncVehicles(userId);

      if (success) {
        console.log('Sincronização de veículos concluída com sucesso');
      } else {
        console.warn('Sincronização de veículos concluída com alguns erros');
      }

      return success;
    } catch (error) {
      console.error('Error syncing vehicles:', error);
      return false;
    }
  }

  /**
   * Synchronizes journeys with the server
   */
  private async syncJourneys(userId: string): Promise<boolean> {
    try {
      // Get all journeys for the user
      const journeys = await this.journeyService.getAllJourneysByUser(userId);
      
      // Filter journeys that need to be synced
      const pendingJourneys = journeys.filter(j => 
        j.syncStatus === SyncStatus.PendingCreate || 
        j.syncStatus === SyncStatus.PendingUpdate || 
        j.syncStatus === SyncStatus.PendingDelete
      );
           
      // Update the sync status of all pending journeys
      for (const journey of pendingJourneys) {
        const updatedJourney = {
          ...journey,
          syncStatus: SyncStatus.Synced,
          lastSyncDate: new Date()
        };

        //sync journey API
        await this.journeyService.sendJourneyToSync({
          id: updatedJourney.id,
          startDate: updatedJourney.startDate,
          endDate: updatedJourney.endDate,
          distance: updatedJourney.distance,
          userId: updatedJourney.userId,
          vehicleId: updatedJourney.vehicleId,
          infosJourney: updatedJourney.infosJourney
        });
        
        // Update the journey in local storage        
        await this.journeyService.updateJourneySync(updatedJourney);
      }
      
      return true;
    } catch (error) {
      console.error('Error syncing journeys:', error);
      return false;
    }
  }
}



