import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { AlertController } from '@ionic/angular';
import { NetworkService } from './network.service';
import { VehicleService } from './vehicle.service';
import { JourneyStorageService } from './journey-storage.service';
import { SessionService } from './session.service';
import { ToastService } from './toast.service';
import { SyncStatus } from '../models/sync.model';

interface SyncResult {
  success: boolean;
  vehiclesSuccess: boolean;
  journeysSuccess: boolean;
  vehiclesSynced: number;
  vehiclesFailed: number;
  journeysSynced: number;
  journeysFailed: number;
  errors: string[];
}

@Injectable({
  providedIn: 'root'
})
export class SyncService {
  // BehaviorSubject to track the sync status
  private syncInProgressSubject = new BehaviorSubject<boolean>(false);
  
  // Observable that components can subscribe to
  public syncInProgress$: Observable<boolean> = this.syncInProgressSubject.asObservable();
  
  // BehaviorSubject to track the last sync time
  private lastSyncTimeSubject = new BehaviorSubject<Date | null>(null);
  
  // Observable that components can subscribe to
  public lastSyncTime$: Observable<Date | null> = this.lastSyncTimeSubject.asObservable();
  
  // BehaviorSubject to track the pending sync count
  private pendingSyncCountSubject = new BehaviorSubject<number>(0);
  
  // Observable that components can subscribe to
  public pendingSyncCount$: Observable<number> = this.pendingSyncCountSubject.asObservable();

  constructor(
    private networkService: NetworkService,
    private vehicleService: VehicleService,
    private journeyService: JourneyStorageService,
    private sessionService: SessionService,
    private toastService: ToastService,
    private alertController: AlertController
  ) {
    // Initialize the last sync time from local storage
    const lastSyncTime = localStorage.getItem('lastSyncTime');
    if (lastSyncTime) {
      this.lastSyncTimeSubject.next(new Date(lastSyncTime));
    }
    
    // Check for pending sync items on initialization
    this.checkPendingSyncItems();
  }

  /**
   * Checks for items that need to be synchronized
   */
  async checkPendingSyncItems(): Promise<void> {
    try {
      const userId = await this.sessionService.getUserId();
      if (!userId) return;
      
      // Get all vehicles for the user
      const vehicles = await this.vehicleService.listAllLocal(userId);
      
      // Get all journeys for the user
      const journeys = await this.journeyService.getAllJourneysByUser(userId);
      
      // Count items that need to be synced
      const pendingVehicles = vehicles.filter(v => 
        v.syncStatus === SyncStatus.PendingCreate || 
        v.syncStatus === SyncStatus.PendingUpdate || 
        v.syncStatus === SyncStatus.PendingDelete
      );
      
      const pendingJourneys = journeys.filter(j => 
        j.syncStatus === SyncStatus.PendingCreate || 
        j.syncStatus === SyncStatus.PendingUpdate || 
        j.syncStatus === SyncStatus.PendingDelete
      );
      
      // Update the pending sync count
      const totalPending = pendingVehicles.length + pendingJourneys.length;
      this.pendingSyncCountSubject.next(totalPending);
      
    } catch (error) {
      console.error('Error checking pending sync items:', error);
    }
  }

  /**
   * Synchronizes all pending items with the server
   */
  async syncAll(): Promise<boolean> {
    // Check if we're online
    if (!this.networkService.isOnlineNow()) {
      this.toastService.showToast('Sem conexão com a internet. Não é possível sincronizar.', 'warning');
      return false;
    }

    // Check if sync is already in progress
    if (this.syncInProgressSubject.getValue()) {
      this.toastService.showToast('Sincronização já em andamento.', 'info');
      return false;
    }

    // Set sync in progress
    this.syncInProgressSubject.next(true);

    try {
      const userId = await this.sessionService.getUserId();
      if (!userId) {
        this.toastService.showToast('Erro: usuário não encontrado.', 'danger');
        return false;
      }

      // Perform detailed sync and get results
      const syncResult = await this.performDetailedSync(userId);

      // Update last sync time only if completely successful
      if (syncResult.success) {
        const now = new Date();
        this.lastSyncTimeSubject.next(now);
        localStorage.setItem('lastSyncTime', now.toISOString());
      }

      // Check for pending items again
      await this.checkPendingSyncItems();

      // Show detailed feedback message
      this.showSyncResultMessage(syncResult);

      return syncResult.success;
    } catch (error) {
      console.error('Error during synchronization:', error);
      this.toastService.showToast('Erro inesperado durante a sincronização.', 'danger');
      return false;
    } finally {
      // Set sync not in progress
      this.syncInProgressSubject.next(false);
    }
  }

  /**
   * Performs detailed synchronization and returns comprehensive results
   */
  private async performDetailedSync(userId: string): Promise<SyncResult> {
    const result: SyncResult = {
      success: false,
      vehiclesSuccess: false,
      journeysSuccess: false,
      vehiclesSynced: 0,
      vehiclesFailed: 0,
      journeysSynced: 0,
      journeysFailed: 0,
      errors: []
    };

    try {
      // Sync vehicles with detailed results
      const vehicleResult = await this.syncVehiclesDetailed(userId);
      result.vehiclesSuccess = vehicleResult.success;
      result.vehiclesSynced = vehicleResult.synced;
      result.vehiclesFailed = vehicleResult.failed;
      if (vehicleResult.errors.length > 0) {
        result.errors.push(...vehicleResult.errors);
      }

      // Sync journeys with detailed results
      const journeyResult = await this.syncJourneysDetailed(userId);
      result.journeysSuccess = journeyResult.success;
      result.journeysSynced = journeyResult.synced;
      result.journeysFailed = journeyResult.failed;
      if (journeyResult.errors.length > 0) {
        result.errors.push(...journeyResult.errors);
      }

      // Overall success if both are successful
      result.success = result.vehiclesSuccess && result.journeysSuccess;

    } catch (error) {
      result.errors.push(`Erro geral na sincronização: ${error}`);
      console.error('Error in detailed sync:', error);
    }

    return result;
  }

  /**
   * Shows appropriate message based on sync results
   */
  private showSyncResultMessage(result: SyncResult): void {
    if (result.success) {
      const totalSynced = result.vehiclesSynced + result.journeysSynced;
      if (totalSynced > 0) {
        this.toastService.showToast(
          `Sincronização concluída! ${result.vehiclesSynced} veículos e ${result.journeysSynced} viagens sincronizados.`,
          'success'
        );
      } else {
        this.toastService.showToast('Sincronização concluída! Nenhum item pendente.', 'success');
      }
    } else {
      const totalFailed = result.vehiclesFailed + result.journeysFailed;
      const totalSynced = result.vehiclesSynced + result.journeysSynced;

      // Preparar mensagem de erro detalhada
      let errorMessage = '';
      let errorDetails = '';

      if (totalSynced > 0 && totalFailed > 0) {
        errorMessage = `Sincronização parcial: ${totalSynced} itens sincronizados, ${totalFailed} falharam.`;
      } else if (totalFailed > 0) {
        errorMessage = `Erro na sincronização: ${totalFailed} itens falharam.`;
      } else {
        errorMessage = 'Erro na sincronização.';
      }

      // Adicionar detalhes dos erros se houver
      if (result.errors.length > 0) {
        errorDetails = this.formatErrorDetails(result.errors);
        errorMessage += ` ${errorDetails}`;
      }

      // Mostrar mensagem principal
      this.toastService.showToast(errorMessage, totalSynced > 0 ? 'warning' : 'danger');

      // Se há muitos erros, oferecer opção de ver detalhes
      if (result.errors.length > 2) {
        setTimeout(() => {
          this.showErrorDetailsOption(result.errors);
        }, 2000);
      }
    }
  }

  /**
   * Formata os detalhes dos erros para exibição
   */
  private formatErrorDetails(errors: string[]): string {
    if (errors.length === 0) return '';

    if (errors.length === 1) {
      return `Erro: ${this.simplifyErrorMessage(errors[0])}`;
    }

    if (errors.length <= 2) {
      return `Erros: ${errors.map(e => this.simplifyErrorMessage(e)).join(', ')}`;
    }

    // Para muitos erros, mostrar apenas os tipos principais
    const mainCauses = this.getMainErrorCauses(errors);
    return `Principais erros: ${mainCauses}`;
  }

  /**
   * Simplifica mensagens de erro para o usuário
   */
  private simplifyErrorMessage(error: string): string {
    const errorLower = error.toLowerCase();

    // Erros de rede
    if (errorLower.includes('network') || errorLower.includes('fetch') || errorLower.includes('connection')) {
      return 'Problema de conexão';
    }

    // Erros de servidor
    if (errorLower.includes('500') || errorLower.includes('internal server')) {
      return 'Erro no servidor';
    }

    // Erros de autorização
    if (errorLower.includes('401') || errorLower.includes('unauthorized') || errorLower.includes('forbidden')) {
      return 'Erro de autorização';
    }

    // Erros de dados
    if (errorLower.includes('400') || errorLower.includes('bad request') || errorLower.includes('validation')) {
      return 'Dados inválidos';
    }

    // Timeout
    if (errorLower.includes('timeout')) {
      return 'Tempo esgotado';
    }

    // Erro genérico - pegar apenas a parte mais relevante
    const parts = error.split(':');
    if (parts.length > 1) {
      return parts[parts.length - 1].trim().substring(0, 50);
    }

    return error.substring(0, 50);
  }

  /**
   * Identifica as principais causas dos erros
   */
  private getMainErrorCauses(errors: string[]): string {
    const causes = {
      network: 0,
      server: 0,
      auth: 0,
      data: 0,
      timeout: 0,
      other: 0
    };

    errors.forEach(error => {
      const errorLower = error.toLowerCase();

      if (errorLower.includes('network') || errorLower.includes('fetch') || errorLower.includes('connection')) {
        causes.network++;
      } else if (errorLower.includes('500') || errorLower.includes('internal server')) {
        causes.server++;
      } else if (errorLower.includes('401') || errorLower.includes('unauthorized') || errorLower.includes('forbidden')) {
        causes.auth++;
      } else if (errorLower.includes('400') || errorLower.includes('bad request') || errorLower.includes('validation')) {
        causes.data++;
      } else if (errorLower.includes('timeout')) {
        causes.timeout++;
      } else {
        causes.other++;
      }
    });

    const mainCauses = [];
    if (causes.network > 0) mainCauses.push(`${causes.network} problemas de conexão`);
    if (causes.server > 0) mainCauses.push(`${causes.server} erros do servidor`);
    if (causes.auth > 0) mainCauses.push(`${causes.auth} erros de autorização`);
    if (causes.data > 0) mainCauses.push(`${causes.data} dados inválidos`);
    if (causes.timeout > 0) mainCauses.push(`${causes.timeout} timeouts`);
    if (causes.other > 0) mainCauses.push(`${causes.other} outros erros`);

    return mainCauses.join(', ') || 'Erros diversos';
  }

  /**
   * Synchronizes vehicles with detailed results
   */
  private async syncVehiclesDetailed(userId: string): Promise<{success: boolean, synced: number, failed: number, errors: string[]}> {
    const result = {
      success: false,
      synced: 0,
      failed: 0,
      errors: [] as string[]
    };

    try {
      console.log('Iniciando sincronização de veículos...');

      // Get pending vehicles count before sync
      const pendingVehicles = await this.vehicleService.getPendingSyncVehicles(userId);
      const initialPendingCount = pendingVehicles.length;

      // Use the comprehensive sync method from VehicleService
      const success = await this.vehicleService.syncVehicles(userId);

      // Get pending vehicles count after sync to calculate success/failure
      const remainingPendingVehicles = await this.vehicleService.getPendingSyncVehicles(userId);
      const remainingCount = remainingPendingVehicles.length;

      result.synced = initialPendingCount - remainingCount;
      result.failed = remainingCount;
      result.success = success && remainingCount === 0;

      if (success) {
        console.log(`Sincronização de veículos concluída: ${result.synced} sincronizados, ${result.failed} falharam`);
      } else {
        console.warn(`Sincronização de veículos com erros: ${result.synced} sincronizados, ${result.failed} falharam`);

        // Tentar obter mais detalhes sobre os erros dos veículos
        if (result.failed > 0) {
          result.errors.push(`${result.failed} veículos falharam na sincronização`);
        } else {
          result.errors.push('Erro geral na sincronização de veículos');
        }
      }

    } catch (error) {
      console.error('Error syncing vehicles:', error);
      result.errors.push(`Erro na sincronização de veículos: ${error}`);
    }

    return result;
  }

  /**
   * Synchronizes journeys with detailed results
   */
  private async syncJourneysDetailed(userId: string): Promise<{success: boolean, synced: number, failed: number, errors: string[]}> {
    const result = {
      success: false,
      synced: 0,
      failed: 0,
      errors: [] as string[]
    };

    try {
      console.log('Iniciando sincronização de viagens...');

      // Get all journeys for the user
      const journeys = await this.journeyService.getAllJourneysByUser(userId);

      // Filter journeys that need to be synced
      const pendingJourneys = journeys.filter(j =>
        j.syncStatus === SyncStatus.PendingCreate ||
        j.syncStatus === SyncStatus.PendingUpdate ||
        j.syncStatus === SyncStatus.PendingDelete
      );

      if (pendingJourneys.length === 0) {
        console.log('Nenhuma viagem pendente para sincronizar');
        result.success = true;
        return result;
      }

      console.log(`${pendingJourneys.length} viagens pendentes para sincronizar`);

      // Process each journey individually
      for (const journey of pendingJourneys) {
        try {
          // Sync journey API
          await this.journeyService.sendJourneyToSync({
            id: journey.id,
            startDate: journey.startDate,
            endDate: journey.endDate,
            distance: journey.distance,
            userId: journey.userId,
            vehicleId: journey.vehicleId,
            infosJourney: journey.infosJourney
          });

          // Update the journey sync status in local storage
          const updatedJourney = {
            ...journey,
            syncStatus: SyncStatus.Synced,
            lastSyncDate: new Date()
          };

          await this.journeyService.updateJourneySync(updatedJourney);

          result.synced++;
          console.log(`Viagem sincronizada: ${journey.id}`);

        } catch (error) {
          result.failed++;
          const errorMsg = `Erro ao sincronizar viagem ${journey.id}: ${error}`;
          console.error(errorMsg);
          result.errors.push(errorMsg);

          // Keep the journey as pending for retry later
          // Don't update sync status on failure
        }
      }

      console.log(`${result.synced}/${pendingJourneys.length} viagens sincronizadas com sucesso`);

      if (result.failed > 0) {
        console.warn(`${result.failed} viagens falharam na sincronização`);
      }

      // Success only if all journeys were synced successfully
      result.success = result.failed === 0;

    } catch (error) {
      const errorMsg = `Erro geral na sincronização de viagens: ${error}`;
      console.error(errorMsg);
      result.errors.push(errorMsg);
    }

    return result;
  }

  /**
   * Mostra opção para ver detalhes dos erros
   */
  private async showErrorDetailsOption(errors: string[]): Promise<void> {
    // Mostrar toast informativo
    this.toastService.showToast(
      `${errors.length} erros encontrados. Verificando detalhes...`,
      'warning'
    );

    // Aguardar um pouco e mostrar o modal com detalhes
    setTimeout(() => {
      this.showErrorDetailsModal(errors);
    }, 1000);
  }

  /**
   * Mostra modal com detalhes completos dos erros
   */
  private async showErrorDetailsModal(errors: string[]): Promise<void> {
    const errorList = errors.map((error, index) =>
      `${index + 1}. ${this.simplifyErrorMessage(error)}`
    ).join('\n');

    const alert = await this.alertController.create({
      header: 'Detalhes dos Erros de Sincronização',
      message: `Foram encontrados ${errors.length} erros durante a sincronização:\n\n${errorList}`,
      buttons: [
        {
          text: 'Tentar Novamente',
          handler: () => {
            this.syncAll();
          }
        },
        {
          text: 'Fechar',
          role: 'cancel'
        }
      ]
    });

    await alert.present();
  }
}



