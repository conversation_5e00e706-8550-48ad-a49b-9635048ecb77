import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { NetworkService } from './network.service';
import { VehicleService } from './vehicle.service';
import { JourneyStorageService } from './journey-storage.service';
import { SessionService } from './session.service';
import { ToastService } from './toast.service';
import { SyncStatus } from '../models/sync.model';

interface SyncResult {
  success: boolean;
  vehiclesSuccess: boolean;
  journeysSuccess: boolean;
  vehiclesSynced: number;
  vehiclesFailed: number;
  journeysSynced: number;
  journeysFailed: number;
  errors: string[];
}

@Injectable({
  providedIn: 'root'
})
export class SyncService {
  // BehaviorSubject to track the sync status
  private syncInProgressSubject = new BehaviorSubject<boolean>(false);
  
  // Observable that components can subscribe to
  public syncInProgress$: Observable<boolean> = this.syncInProgressSubject.asObservable();
  
  // BehaviorSubject to track the last sync time
  private lastSyncTimeSubject = new BehaviorSubject<Date | null>(null);
  
  // Observable that components can subscribe to
  public lastSyncTime$: Observable<Date | null> = this.lastSyncTimeSubject.asObservable();
  
  // BehaviorSubject to track the pending sync count
  private pendingSyncCountSubject = new BehaviorSubject<number>(0);
  
  // Observable that components can subscribe to
  public pendingSyncCount$: Observable<number> = this.pendingSyncCountSubject.asObservable();

  constructor(
    private networkService: NetworkService,
    private vehicleService: VehicleService,
    private journeyService: JourneyStorageService,
    private sessionService: SessionService,
    private toastService: ToastService
  ) {
    // Initialize the last sync time from local storage
    const lastSyncTime = localStorage.getItem('lastSyncTime');
    if (lastSyncTime) {
      this.lastSyncTimeSubject.next(new Date(lastSyncTime));
    }
    
    // Check for pending sync items on initialization
    this.checkPendingSyncItems();
  }

  /**
   * Checks for items that need to be synchronized
   */
  async checkPendingSyncItems(): Promise<void> {
    try {
      const userId = await this.sessionService.getUserId();
      if (!userId) return;
      
      // Get all vehicles for the user
      const vehicles = await this.vehicleService.listAllLocal(userId);
      
      // Get all journeys for the user
      const journeys = await this.journeyService.getAllJourneysByUser(userId);
      
      // Count items that need to be synced
      const pendingVehicles = vehicles.filter(v => 
        v.syncStatus === SyncStatus.PendingCreate || 
        v.syncStatus === SyncStatus.PendingUpdate || 
        v.syncStatus === SyncStatus.PendingDelete
      );
      
      const pendingJourneys = journeys.filter(j => 
        j.syncStatus === SyncStatus.PendingCreate || 
        j.syncStatus === SyncStatus.PendingUpdate || 
        j.syncStatus === SyncStatus.PendingDelete
      );
      
      // Update the pending sync count
      const totalPending = pendingVehicles.length + pendingJourneys.length;
      this.pendingSyncCountSubject.next(totalPending);
      
    } catch (error) {
      console.error('Error checking pending sync items:', error);
    }
  }

  /**
   * Synchronizes all pending items with the server
   */
  async syncAll(): Promise<boolean> {
    // Check if we're online
    if (!this.networkService.isOnlineNow()) {
      this.toastService.showToast('Sem conexão com a internet. Não é possível sincronizar.', 'warning');
      return false;
    }

    // Check if sync is already in progress
    if (this.syncInProgressSubject.getValue()) {
      this.toastService.showToast('Sincronização já em andamento.', 'info');
      return false;
    }

    // Set sync in progress
    this.syncInProgressSubject.next(true);

    try {
      const userId = await this.sessionService.getUserId();
      if (!userId) {
        this.toastService.showToast('Erro: usuário não encontrado.', 'danger');
        return false;
      }

      // Perform detailed sync and get results
      const syncResult = await this.performDetailedSync(userId);

      // Update last sync time only if completely successful
      if (syncResult.success) {
        const now = new Date();
        this.lastSyncTimeSubject.next(now);
        localStorage.setItem('lastSyncTime', now.toISOString());
      }

      // Check for pending items again
      await this.checkPendingSyncItems();

      // Show detailed feedback message
      this.showSyncResultMessage(syncResult);

      return syncResult.success;
    } catch (error) {
      console.error('Error during synchronization:', error);
      this.toastService.showToast('Erro inesperado durante a sincronização.', 'danger');
      return false;
    } finally {
      // Set sync not in progress
      this.syncInProgressSubject.next(false);
    }
  }

  /**
   * Performs detailed synchronization and returns comprehensive results
   */
  private async performDetailedSync(userId: string): Promise<SyncResult> {
    const result: SyncResult = {
      success: false,
      vehiclesSuccess: false,
      journeysSuccess: false,
      vehiclesSynced: 0,
      vehiclesFailed: 0,
      journeysSynced: 0,
      journeysFailed: 0,
      errors: []
    };

    try {
      // Sync vehicles with detailed results
      const vehicleResult = await this.syncVehiclesDetailed(userId);
      result.vehiclesSuccess = vehicleResult.success;
      result.vehiclesSynced = vehicleResult.synced;
      result.vehiclesFailed = vehicleResult.failed;
      if (vehicleResult.errors.length > 0) {
        result.errors.push(...vehicleResult.errors);
      }

      // Sync journeys with detailed results
      const journeyResult = await this.syncJourneysDetailed(userId);
      result.journeysSuccess = journeyResult.success;
      result.journeysSynced = journeyResult.synced;
      result.journeysFailed = journeyResult.failed;
      if (journeyResult.errors.length > 0) {
        result.errors.push(...journeyResult.errors);
      }

      // Overall success if both are successful
      result.success = result.vehiclesSuccess && result.journeysSuccess;

    } catch (error) {
      result.errors.push(`Erro geral na sincronização: ${error}`);
      console.error('Error in detailed sync:', error);
    }

    return result;
  }

  /**
   * Shows appropriate message based on sync results
   */
  private showSyncResultMessage(result: SyncResult): void {
    if (result.success) {
      const totalSynced = result.vehiclesSynced + result.journeysSynced;
      if (totalSynced > 0) {
        this.toastService.showToast(
          `Sincronização concluída! ${result.vehiclesSynced} veículos e ${result.journeysSynced} viagens sincronizados.`,
          'success'
        );
      } else {
        this.toastService.showToast('Sincronização concluída! Nenhum item pendente.', 'success');
      }
    } else {
      const totalFailed = result.vehiclesFailed + result.journeysFailed;
      const totalSynced = result.vehiclesSynced + result.journeysSynced;

      if (totalSynced > 0 && totalFailed > 0) {
        this.toastService.showToast(
          `Sincronização parcial: ${totalSynced} itens sincronizados, ${totalFailed} falharam.`,
          'warning'
        );
      } else if (totalFailed > 0) {
        this.toastService.showToast(
          `Erro na sincronização: ${totalFailed} itens falharam.`,
          'danger'
        );
      } else {
        this.toastService.showToast('Erro na sincronização.', 'danger');
      }
    }
  }

  /**
   * Synchronizes vehicles with detailed results
   */
  private async syncVehiclesDetailed(userId: string): Promise<{success: boolean, synced: number, failed: number, errors: string[]}> {
    const result = {
      success: false,
      synced: 0,
      failed: 0,
      errors: [] as string[]
    };

    try {
      console.log('Iniciando sincronização de veículos...');

      // Get pending vehicles count before sync
      const pendingVehicles = await this.vehicleService.getPendingSyncVehicles(userId);
      const initialPendingCount = pendingVehicles.length;

      // Use the comprehensive sync method from VehicleService
      const success = await this.vehicleService.syncVehicles(userId);

      // Get pending vehicles count after sync to calculate success/failure
      const remainingPendingVehicles = await this.vehicleService.getPendingSyncVehicles(userId);
      const remainingCount = remainingPendingVehicles.length;

      result.synced = initialPendingCount - remainingCount;
      result.failed = remainingCount;
      result.success = success && remainingCount === 0;

      if (success) {
        console.log(`Sincronização de veículos concluída: ${result.synced} sincronizados, ${result.failed} falharam`);
      } else {
        console.warn(`Sincronização de veículos com erros: ${result.synced} sincronizados, ${result.failed} falharam`);
        result.errors.push('Erro na sincronização de veículos');
      }

    } catch (error) {
      console.error('Error syncing vehicles:', error);
      result.errors.push(`Erro na sincronização de veículos: ${error}`);
    }

    return result;
  }

  /**
   * Synchronizes journeys with detailed results
   */
  private async syncJourneysDetailed(userId: string): Promise<{success: boolean, synced: number, failed: number, errors: string[]}> {
    const result = {
      success: false,
      synced: 0,
      failed: 0,
      errors: [] as string[]
    };

    try {
      console.log('Iniciando sincronização de viagens...');

      // Get all journeys for the user
      const journeys = await this.journeyService.getAllJourneysByUser(userId);

      // Filter journeys that need to be synced
      const pendingJourneys = journeys.filter(j =>
        j.syncStatus === SyncStatus.PendingCreate ||
        j.syncStatus === SyncStatus.PendingUpdate ||
        j.syncStatus === SyncStatus.PendingDelete
      );

      if (pendingJourneys.length === 0) {
        console.log('Nenhuma viagem pendente para sincronizar');
        result.success = true;
        return result;
      }

      console.log(`${pendingJourneys.length} viagens pendentes para sincronizar`);

      // Process each journey individually
      for (const journey of pendingJourneys) {
        try {
          // Sync journey API
          await this.journeyService.sendJourneyToSync({
            id: journey.id,
            startDate: journey.startDate,
            endDate: journey.endDate,
            distance: journey.distance,
            userId: journey.userId,
            vehicleId: journey.vehicleId,
            infosJourney: journey.infosJourney
          });

          // Update the journey sync status in local storage
          const updatedJourney = {
            ...journey,
            syncStatus: SyncStatus.Synced,
            lastSyncDate: new Date()
          };

          await this.journeyService.updateJourneySync(updatedJourney);

          result.synced++;
          console.log(`Viagem sincronizada: ${journey.id}`);

        } catch (error) {
          result.failed++;
          const errorMsg = `Erro ao sincronizar viagem ${journey.id}: ${error}`;
          console.error(errorMsg);
          result.errors.push(errorMsg);

          // Keep the journey as pending for retry later
          // Don't update sync status on failure
        }
      }

      console.log(`${result.synced}/${pendingJourneys.length} viagens sincronizadas com sucesso`);

      if (result.failed > 0) {
        console.warn(`${result.failed} viagens falharam na sincronização`);
      }

      // Success only if all journeys were synced successfully
      result.success = result.failed === 0;

    } catch (error) {
      const errorMsg = `Erro geral na sincronização de viagens: ${error}`;
      console.error(errorMsg);
      result.errors.push(errorMsg);
    }

    return result;
  }
}



