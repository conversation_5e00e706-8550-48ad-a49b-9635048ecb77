{"version": 3, "sources": ["node_modules/@capacitor/motion/dist/esm/web.js"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\nexport class MotionWeb extends WebPlugin {\n  constructor() {\n    super();\n    this.registerWindowListener('devicemotion', 'accel');\n    this.registerWindowListener('deviceorientation', 'orientation');\n  }\n}\n"], "mappings": ";;;;;;;;;AAAA,IACa;AADb;AAAA;AAAA;AACO,IAAM,YAAN,cAAwB,UAAU;AAAA,MACvC,cAAc;AACZ,cAAM;AACN,aAAK,uBAAuB,gBAAgB,OAAO;AACnD,aAAK,uBAAuB,qBAAqB,aAAa;AAAA,MAChE;AAAA,IACF;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}