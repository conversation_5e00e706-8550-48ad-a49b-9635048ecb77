<ion-header [translucent]="true">
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/login"></ion-back-button>
    </ion-buttons>
    <ion-title>Cadastro</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ion-padding">
  <div class="signup-container">
    <ion-card>
      <ion-card-header>
        <ion-card-title class="ion-text-center"><PERSON><PERSON><PERSON></ion-card-title>
        <ion-card-subtitle class="ion-text-center">Preencha os dados para se cadastrar</ion-card-subtitle>
      </ion-card-header>
      
      <ion-card-content>
        <form [formGroup]="signupForm" (ngSubmit)="signup()">
          <ion-item>
            <ion-label position="floating">Nome</ion-label>
            <ion-input type="text" formControlName="name"></ion-input>
          </ion-item>
          <div class="error-message" *ngIf="signupForm.get('name')?.touched && signupForm.get('name')?.hasError('required')">
            Nome é obrigatório
          </div>
          
          <ion-item>
            <ion-label position="floating">Email</ion-label>
            <ion-input type="email" formControlName="email"></ion-input>
          </ion-item>
          <div class="error-message" *ngIf="signupForm.get('email')?.touched && signupForm.get('email')?.hasError('required')">
            Email é obrigatório
          </div>
          <div class="error-message" *ngIf="signupForm.get('email')?.touched && signupForm.get('email')?.hasError('email')">
            Digite um email válido
          </div>
          
          <ion-item>
            <ion-label position="floating">Senha</ion-label>
            <ion-input type="password" formControlName="password"></ion-input>
          </ion-item>
          <div class="error-message" *ngIf="signupForm.get('password')?.touched && signupForm.get('password')?.hasError('required')">
            Senha é obrigatória
          </div>
          <div class="error-message" *ngIf="signupForm.get('password')?.touched && signupForm.get('password')?.hasError('minlength')">
            A senha deve ter pelo menos 6 caracteres
          </div>
          
          <ion-item>
            <ion-label position="floating">Confirmar Senha</ion-label>
            <ion-input type="password" formControlName="confirmPassword"></ion-input>
          </ion-item>
          <div class="error-message" *ngIf="signupForm.get('confirmPassword')?.touched && signupForm.get('confirmPassword')?.hasError('required')">
            Confirmação de senha é obrigatória
          </div>
          <div class="error-message" *ngIf="signupForm.hasError('passwordMismatch') && signupForm.get('confirmPassword')?.touched">
            As senhas não coincidem
          </div>
          
          <ion-button expand="block" type="submit" [disabled]="!signupForm.valid" class="ion-margin-top">
            Cadastrar
          </ion-button>
        </form>
        
        <div class="ion-text-center ion-margin-top">
          <ion-button fill="clear" (click)="goToLogin()">
            Já tem conta? Faça login
          </ion-button>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
</ion-content>