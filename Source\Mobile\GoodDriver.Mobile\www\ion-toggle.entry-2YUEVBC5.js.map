{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-toggle.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { i as inheritAriaAttributes, d as renderHiddenInput } from './helpers-d94bc8ad.js';\nimport { c as hapticSelection } from './haptic-ac164e4c.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { f as checkmarkOutline, r as removeOutline, g as ellipseOutline } from './index-e2cf2ceb.js';\nimport { c as config } from './index-cfd9c1f2.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport './capacitor-59395cbd.js';\nimport './index-a5d50daf.js';\nconst toggleIosCss = \":host{-webkit-box-sizing:content-box !important;box-sizing:content-box !important;display:inline-block;position:relative;max-width:100%;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}:host(.ion-focused) input{border:2px solid #5e9ed6}:host(.toggle-disabled){pointer-events:none}input{display:none}.toggle-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.toggle-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.toggle-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.toggle-bottom{padding-top:4px;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;font-size:0.75rem;white-space:normal}:host(.toggle-label-placement-stacked) .toggle-bottom{font-size:1rem}.toggle-bottom .error-text{display:none;color:var(--ion-color-danger, #c5000f)}.toggle-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .toggle-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .toggle-bottom .helper-text{display:none}:host(.toggle-label-placement-start) .toggle-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.toggle-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-end) .toggle-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse;-ms-flex-pack:start;justify-content:start}:host(.toggle-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.toggle-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.toggle-label-placement-stacked) .toggle-wrapper{-ms-flex-direction:column;flex-direction:column;text-align:center}:host(.toggle-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.toggle-justify-space-between) .toggle-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.toggle-justify-start) .toggle-wrapper{-ms-flex-pack:start;justify-content:start}:host(.toggle-justify-end) .toggle-wrapper{-ms-flex-pack:end;justify-content:end}:host(.toggle-alignment-start) .toggle-wrapper{-ms-flex-align:start;align-items:start}:host(.toggle-alignment-center) .toggle-wrapper{-ms-flex-align:center;align-items:center}:host(.toggle-justify-space-between),:host(.toggle-justify-start),:host(.toggle-justify-end),:host(.toggle-alignment-start),:host(.toggle-alignment-center){display:block}.toggle-icon-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;-webkit-transition:var(--handle-transition);transition:var(--handle-transition);will-change:transform}.toggle-icon{border-radius:var(--border-radius);display:block;position:relative;width:100%;height:100%;background:var(--track-background);overflow:inherit}:host(.toggle-checked) .toggle-icon{background:var(--track-background-checked)}.toggle-inner{border-radius:var(--handle-border-radius);position:absolute;left:var(--handle-spacing);width:var(--handle-width);height:var(--handle-height);max-height:var(--handle-max-height);-webkit-transition:var(--handle-transition);transition:var(--handle-transition);background:var(--handle-background);-webkit-box-shadow:var(--handle-box-shadow);box-shadow:var(--handle-box-shadow);contain:strict}:host(.toggle-ltr) .toggle-inner{left:var(--handle-spacing)}:host(.toggle-rtl) .toggle-inner{right:var(--handle-spacing)}:host(.toggle-ltr.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(100% - var(--handle-width)), 0, 0);transform:translate3d(calc(100% - var(--handle-width)), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(-100% + var(--handle-width)), 0, 0);transform:translate3d(calc(-100% + var(--handle-width)), 0, 0)}:host(.toggle-checked) .toggle-inner{background:var(--handle-background-checked)}:host(.toggle-ltr.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0)}:host{--track-background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.088);--track-background-checked:var(--ion-color-primary, #0054e9);--border-radius:15.5px;--handle-background:#ffffff;--handle-background-checked:#ffffff;--handle-border-radius:25.5px;--handle-box-shadow:0 3px 4px rgba(0, 0, 0, 0.06), 0 3px 8px rgba(0, 0, 0, 0.06);--handle-height:calc(31px - (2px * 2));--handle-max-height:calc(100% - var(--handle-spacing) * 2);--handle-width:calc(31px - (2px * 2));--handle-spacing:2px;--handle-transition:transform 300ms, width 120ms ease-in-out 80ms, left 110ms ease-in-out 80ms, right 110ms ease-in-out 80ms}.native-wrapper .toggle-icon{width:51px;height:31px;overflow:hidden}:host(.ion-color.toggle-checked) .toggle-icon{background:var(--ion-color-base)}:host(.toggle-activated) .toggle-switch-icon{opacity:0}.toggle-icon{-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);-webkit-transition:background-color 300ms;transition:background-color 300ms}.toggle-inner{will-change:transform}.toggle-switch-icon{position:absolute;top:50%;width:11px;height:11px;-webkit-transform:translateY(-50%);transform:translateY(-50%);-webkit-transition:opacity 300ms, color 300ms;transition:opacity 300ms, color 300ms}.toggle-switch-icon{position:absolute;color:var(--ion-color-dark, #222428)}:host(.toggle-ltr) .toggle-switch-icon{right:6px}:host(.toggle-rtl) .toggle-switch-icon{right:initial;left:6px;}:host(.toggle-checked) .toggle-switch-icon.toggle-switch-icon-checked{color:var(--ion-color-contrast, #fff)}:host(.toggle-checked) .toggle-switch-icon:not(.toggle-switch-icon-checked){opacity:0}.toggle-switch-icon-checked{position:absolute;width:15px;height:15px;-webkit-transform:translateY(-50%) rotate(90deg);transform:translateY(-50%) rotate(90deg)}:host(.toggle-ltr) .toggle-switch-icon-checked{right:initial;left:4px;}:host(.toggle-rtl) .toggle-switch-icon-checked{right:4px}:host(.toggle-activated) .toggle-icon::before,:host(.toggle-checked) .toggle-icon::before{-webkit-transform:scale3d(0, 0, 0);transform:scale3d(0, 0, 0)}:host(.toggle-activated.toggle-checked) .toggle-inner::before{-webkit-transform:scale3d(0, 0, 0);transform:scale3d(0, 0, 0)}:host(.toggle-activated) .toggle-inner{width:calc(var(--handle-width) + 6px)}:host(.toggle-ltr.toggle-activated.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(100% - var(--handle-width) - 6px), 0, 0);transform:translate3d(calc(100% - var(--handle-width) - 6px), 0, 0)}:host(.toggle-rtl.toggle-activated.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(-100% + var(--handle-width) + 6px), 0, 0);transform:translate3d(calc(-100% + var(--handle-width) + 6px), 0, 0)}:host(.toggle-disabled){opacity:0.3}\";\nconst IonToggleIosStyle0 = toggleIosCss;\nconst toggleMdCss = \":host{-webkit-box-sizing:content-box !important;box-sizing:content-box !important;display:inline-block;position:relative;max-width:100%;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}:host(.ion-focused) input{border:2px solid #5e9ed6}:host(.toggle-disabled){pointer-events:none}input{display:none}.toggle-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.toggle-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.toggle-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.toggle-bottom{padding-top:4px;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;font-size:0.75rem;white-space:normal}:host(.toggle-label-placement-stacked) .toggle-bottom{font-size:1rem}.toggle-bottom .error-text{display:none;color:var(--ion-color-danger, #c5000f)}.toggle-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .toggle-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .toggle-bottom .helper-text{display:none}:host(.toggle-label-placement-start) .toggle-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.toggle-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-end) .toggle-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse;-ms-flex-pack:start;justify-content:start}:host(.toggle-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.toggle-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.toggle-label-placement-stacked) .toggle-wrapper{-ms-flex-direction:column;flex-direction:column;text-align:center}:host(.toggle-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.toggle-justify-space-between) .toggle-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.toggle-justify-start) .toggle-wrapper{-ms-flex-pack:start;justify-content:start}:host(.toggle-justify-end) .toggle-wrapper{-ms-flex-pack:end;justify-content:end}:host(.toggle-alignment-start) .toggle-wrapper{-ms-flex-align:start;align-items:start}:host(.toggle-alignment-center) .toggle-wrapper{-ms-flex-align:center;align-items:center}:host(.toggle-justify-space-between),:host(.toggle-justify-start),:host(.toggle-justify-end),:host(.toggle-alignment-start),:host(.toggle-alignment-center){display:block}.toggle-icon-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;-webkit-transition:var(--handle-transition);transition:var(--handle-transition);will-change:transform}.toggle-icon{border-radius:var(--border-radius);display:block;position:relative;width:100%;height:100%;background:var(--track-background);overflow:inherit}:host(.toggle-checked) .toggle-icon{background:var(--track-background-checked)}.toggle-inner{border-radius:var(--handle-border-radius);position:absolute;left:var(--handle-spacing);width:var(--handle-width);height:var(--handle-height);max-height:var(--handle-max-height);-webkit-transition:var(--handle-transition);transition:var(--handle-transition);background:var(--handle-background);-webkit-box-shadow:var(--handle-box-shadow);box-shadow:var(--handle-box-shadow);contain:strict}:host(.toggle-ltr) .toggle-inner{left:var(--handle-spacing)}:host(.toggle-rtl) .toggle-inner{right:var(--handle-spacing)}:host(.toggle-ltr.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(100% - var(--handle-width)), 0, 0);transform:translate3d(calc(100% - var(--handle-width)), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(-100% + var(--handle-width)), 0, 0);transform:translate3d(calc(-100% + var(--handle-width)), 0, 0)}:host(.toggle-checked) .toggle-inner{background:var(--handle-background-checked)}:host(.toggle-ltr.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0)}:host{--track-background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.39);--track-background-checked:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.5);--border-radius:14px;--handle-background:#ffffff;--handle-background-checked:var(--ion-color-primary, #0054e9);--handle-border-radius:50%;--handle-box-shadow:0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);--handle-width:20px;--handle-height:20px;--handle-max-height:calc(100% + 6px);--handle-spacing:0;--handle-transition:transform 160ms cubic-bezier(0.4, 0, 0.2, 1), background-color 160ms cubic-bezier(0.4, 0, 0.2, 1)}.native-wrapper .toggle-icon{width:36px;height:14px}:host(.ion-color.toggle-checked) .toggle-icon{background:rgba(var(--ion-color-base-rgb), 0.5)}:host(.ion-color.toggle-checked) .toggle-inner{background:var(--ion-color-base)}:host(.toggle-checked) .toggle-inner{color:var(--ion-color-contrast, #fff)}.toggle-icon{-webkit-transition:background-color 160ms;transition:background-color 160ms}.toggle-inner{will-change:background-color, transform;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;color:#000}.toggle-inner .toggle-switch-icon{-webkit-padding-start:1px;padding-inline-start:1px;-webkit-padding-end:1px;padding-inline-end:1px;padding-top:1px;padding-bottom:1px;width:100%;height:100%}:host(.toggle-disabled){opacity:0.38}\";\nconst IonToggleMdStyle0 = toggleMdCss;\nconst Toggle = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.inputId = `ion-tg-${toggleIds++}`;\n    this.inputLabelId = `${this.inputId}-lbl`;\n    this.helperTextId = `${this.inputId}-helper-text`;\n    this.errorTextId = `${this.inputId}-error-text`;\n    this.lastDrag = 0;\n    this.inheritedAttributes = {};\n    this.didLoad = false;\n    this.setupGesture = async () => {\n      const {\n        toggleTrack\n      } = this;\n      if (toggleTrack) {\n        this.gesture = (await import('./index-39782642.js')).createGesture({\n          el: toggleTrack,\n          gestureName: 'toggle',\n          gesturePriority: 100,\n          threshold: 5,\n          passive: false,\n          onStart: () => this.onStart(),\n          onMove: ev => this.onMove(ev),\n          onEnd: ev => this.onEnd(ev)\n        });\n        this.disabledChanged();\n      }\n    };\n    this.onKeyDown = ev => {\n      if (ev.key === ' ') {\n        ev.preventDefault();\n        if (!this.disabled) {\n          this.toggleChecked();\n        }\n      }\n    };\n    this.onClick = ev => {\n      if (this.disabled) {\n        return;\n      }\n      ev.preventDefault();\n      if (this.lastDrag + 300 < Date.now()) {\n        this.toggleChecked();\n      }\n    };\n    /**\n     * Stops propagation when the display label is clicked,\n     * otherwise, two clicks will be triggered.\n     */\n    this.onDivLabelClick = ev => {\n      ev.stopPropagation();\n    };\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.getSwitchLabelIcon = (mode, checked) => {\n      if (mode === 'md') {\n        return checked ? checkmarkOutline : removeOutline;\n      }\n      return checked ? removeOutline : ellipseOutline;\n    };\n    this.activated = false;\n    this.color = undefined;\n    this.name = this.inputId;\n    this.checked = false;\n    this.disabled = false;\n    this.errorText = undefined;\n    this.helperText = undefined;\n    this.value = 'on';\n    this.enableOnOffLabels = config.get('toggleOnOffLabels');\n    this.labelPlacement = 'start';\n    this.justify = undefined;\n    this.alignment = undefined;\n    this.required = false;\n  }\n  disabledChanged() {\n    if (this.gesture) {\n      this.gesture.enable(!this.disabled);\n    }\n  }\n  toggleChecked() {\n    const {\n      checked,\n      value\n    } = this;\n    const isNowChecked = !checked;\n    this.checked = isNowChecked;\n    this.setFocus();\n    this.ionChange.emit({\n      checked: isNowChecked,\n      value\n    });\n  }\n  async connectedCallback() {\n    /**\n     * If we have not yet rendered\n     * ion-toggle, then toggleTrack is not defined.\n     * But if we are moving ion-toggle via appendChild,\n     * then toggleTrack will be defined.\n     */\n    if (this.didLoad) {\n      this.setupGesture();\n    }\n  }\n  componentDidLoad() {\n    this.setupGesture();\n    this.didLoad = true;\n  }\n  disconnectedCallback() {\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = Object.assign({}, inheritAriaAttributes(this.el));\n  }\n  onStart() {\n    this.activated = true;\n    // touch-action does not work in iOS\n    this.setFocus();\n  }\n  onMove(detail) {\n    if (shouldToggle(isRTL(this.el), this.checked, detail.deltaX, -10)) {\n      this.toggleChecked();\n      hapticSelection();\n    }\n  }\n  onEnd(ev) {\n    this.activated = false;\n    this.lastDrag = Date.now();\n    ev.event.preventDefault();\n    ev.event.stopImmediatePropagation();\n  }\n  getValue() {\n    return this.value || '';\n  }\n  setFocus() {\n    if (this.focusEl) {\n      this.focusEl.focus();\n    }\n  }\n  renderOnOffSwitchLabels(mode, checked) {\n    const icon = this.getSwitchLabelIcon(mode, checked);\n    return h(\"ion-icon\", {\n      class: {\n        'toggle-switch-icon': true,\n        'toggle-switch-icon-checked': checked\n      },\n      icon: icon,\n      \"aria-hidden\": \"true\"\n    });\n  }\n  renderToggleControl() {\n    const mode = getIonMode(this);\n    const {\n      enableOnOffLabels,\n      checked\n    } = this;\n    return h(\"div\", {\n      class: \"toggle-icon\",\n      part: \"track\",\n      ref: el => this.toggleTrack = el\n    }, enableOnOffLabels && mode === 'ios' && [this.renderOnOffSwitchLabels(mode, true), this.renderOnOffSwitchLabels(mode, false)], h(\"div\", {\n      class: \"toggle-icon-wrapper\"\n    }, h(\"div\", {\n      class: \"toggle-inner\",\n      part: \"handle\"\n    }, enableOnOffLabels && mode === 'md' && this.renderOnOffSwitchLabels(mode, checked))));\n  }\n  get hasLabel() {\n    return this.el.textContent !== '';\n  }\n  getHintTextID() {\n    const {\n      el,\n      helperText,\n      errorText,\n      helperTextId,\n      errorTextId\n    } = this;\n    if (el.classList.contains('ion-touched') && el.classList.contains('ion-invalid') && errorText) {\n      return errorTextId;\n    }\n    if (helperText) {\n      return helperTextId;\n    }\n    return undefined;\n  }\n  /**\n   * Responsible for rendering helper text and error text.\n   * This element should only be rendered if hint text is set.\n   */\n  renderHintText() {\n    const {\n      helperText,\n      errorText,\n      helperTextId,\n      errorTextId\n    } = this;\n    /**\n     * undefined and empty string values should\n     * be treated as not having helper/error text.\n     */\n    const hasHintText = !!helperText || !!errorText;\n    if (!hasHintText) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"toggle-bottom\"\n    }, h(\"div\", {\n      id: helperTextId,\n      class: \"helper-text\",\n      part: \"supporting-text helper-text\"\n    }, helperText), h(\"div\", {\n      id: errorTextId,\n      class: \"error-text\",\n      part: \"supporting-text error-text\"\n    }, errorText));\n  }\n  render() {\n    const {\n      activated,\n      alignment,\n      checked,\n      color,\n      disabled,\n      el,\n      errorTextId,\n      hasLabel,\n      inheritedAttributes,\n      inputId,\n      inputLabelId,\n      justify,\n      labelPlacement,\n      name,\n      required\n    } = this;\n    const mode = getIonMode(this);\n    const value = this.getValue();\n    const rtl = isRTL(el) ? 'rtl' : 'ltr';\n    renderHiddenInput(true, el, name, checked ? value : '', disabled);\n    return h(Host, {\n      key: 'd9dad2132e9d6cf8e9844fefa009402e1a637ef8',\n      role: \"switch\",\n      \"aria-checked\": `${checked}`,\n      \"aria-describedby\": this.getHintTextID(),\n      \"aria-invalid\": this.getHintTextID() === errorTextId,\n      onClick: this.onClick,\n      \"aria-labelledby\": hasLabel ? inputLabelId : null,\n      \"aria-label\": inheritedAttributes['aria-label'] || null,\n      \"aria-disabled\": disabled ? 'true' : null,\n      tabindex: disabled ? undefined : 0,\n      onKeyDown: this.onKeyDown,\n      class: createColorClasses(color, {\n        [mode]: true,\n        'in-item': hostContext('ion-item', el),\n        'toggle-activated': activated,\n        'toggle-checked': checked,\n        'toggle-disabled': disabled,\n        [`toggle-justify-${justify}`]: justify !== undefined,\n        [`toggle-alignment-${alignment}`]: alignment !== undefined,\n        [`toggle-label-placement-${labelPlacement}`]: true,\n        [`toggle-${rtl}`]: true\n      })\n    }, h(\"label\", {\n      key: '4becda2f40a735e941ecaba26f14231271e38197',\n      class: \"toggle-wrapper\",\n      htmlFor: inputId\n    }, h(\"input\", Object.assign({\n      key: 'c1946dd6aa23dee3562915a7165e012c48b79890',\n      type: \"checkbox\",\n      role: \"switch\",\n      \"aria-checked\": `${checked}`,\n      checked: checked,\n      disabled: disabled,\n      id: inputId,\n      onFocus: () => this.onFocus(),\n      onBlur: () => this.onBlur(),\n      ref: focusEl => this.focusEl = focusEl,\n      required: required\n    }, inheritedAttributes)), h(\"div\", {\n      key: '2493dc0aa587f5f9d9a7d0a388f92928f2db0cf3',\n      class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': !hasLabel\n      },\n      part: \"label\",\n      id: inputLabelId,\n      onClick: this.onDivLabelClick\n    }, h(\"slot\", {\n      key: '798220850b311a26b081914d5c774b757bde2992'\n    }), this.renderHintText()), h(\"div\", {\n      key: '3c6142c9697b60646f286f6fd59f54609377f7d0',\n      class: \"native-wrapper\"\n    }, this.renderToggleControl())));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"disabled\": [\"disabledChanged\"]\n    };\n  }\n};\nconst shouldToggle = (rtl, checked, deltaX, margin) => {\n  if (checked) {\n    return !rtl && margin > deltaX || rtl && -margin < deltaX;\n  } else {\n    return !rtl && -margin < deltaX || rtl && margin > deltaX;\n  }\n};\nlet toggleIds = 0;\nToggle.style = {\n  ios: IonToggleIosStyle0,\n  md: IonToggleMdStyle0\n};\nexport { Toggle as ion_toggle };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAaM,cACA,oBACA,aACA,mBACA,QAwTA,cAOF;AAhVJ;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAM,eAAe;AACrB,IAAM,qBAAqB;AAC3B,IAAM,cAAc;AACpB,IAAM,oBAAoB;AAC1B,IAAM,SAAS,MAAM;AAAA,MACnB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,aAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,aAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,aAAK,UAAU,UAAU,WAAW;AACpC,aAAK,eAAe,GAAG,KAAK,OAAO;AACnC,aAAK,eAAe,GAAG,KAAK,OAAO;AACnC,aAAK,cAAc,GAAG,KAAK,OAAO;AAClC,aAAK,WAAW;AAChB,aAAK,sBAAsB,CAAC;AAC5B,aAAK,UAAU;AACf,aAAK,eAAe,MAAY;AAC9B,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,cAAI,aAAa;AACf,iBAAK,WAAW,MAAM,OAAO,8BAAqB,GAAG,cAAc;AAAA,cACjE,IAAI;AAAA,cACJ,aAAa;AAAA,cACb,iBAAiB;AAAA,cACjB,WAAW;AAAA,cACX,SAAS;AAAA,cACT,SAAS,MAAM,KAAK,QAAQ;AAAA,cAC5B,QAAQ,QAAM,KAAK,OAAO,EAAE;AAAA,cAC5B,OAAO,QAAM,KAAK,MAAM,EAAE;AAAA,YAC5B,CAAC;AACD,iBAAK,gBAAgB;AAAA,UACvB;AAAA,QACF;AACA,aAAK,YAAY,QAAM;AACrB,cAAI,GAAG,QAAQ,KAAK;AAClB,eAAG,eAAe;AAClB,gBAAI,CAAC,KAAK,UAAU;AAClB,mBAAK,cAAc;AAAA,YACrB;AAAA,UACF;AAAA,QACF;AACA,aAAK,UAAU,QAAM;AACnB,cAAI,KAAK,UAAU;AACjB;AAAA,UACF;AACA,aAAG,eAAe;AAClB,cAAI,KAAK,WAAW,MAAM,KAAK,IAAI,GAAG;AACpC,iBAAK,cAAc;AAAA,UACrB;AAAA,QACF;AAKA,aAAK,kBAAkB,QAAM;AAC3B,aAAG,gBAAgB;AAAA,QACrB;AACA,aAAK,UAAU,MAAM;AACnB,eAAK,SAAS,KAAK;AAAA,QACrB;AACA,aAAK,SAAS,MAAM;AAClB,eAAK,QAAQ,KAAK;AAAA,QACpB;AACA,aAAK,qBAAqB,CAAC,MAAM,YAAY;AAC3C,cAAI,SAAS,MAAM;AACjB,mBAAO,UAAU,mBAAmB;AAAA,UACtC;AACA,iBAAO,UAAU,gBAAgB;AAAA,QACnC;AACA,aAAK,YAAY;AACjB,aAAK,QAAQ;AACb,aAAK,OAAO,KAAK;AACjB,aAAK,UAAU;AACf,aAAK,WAAW;AAChB,aAAK,YAAY;AACjB,aAAK,aAAa;AAClB,aAAK,QAAQ;AACb,aAAK,oBAAoB,OAAO,IAAI,mBAAmB;AACvD,aAAK,iBAAiB;AACtB,aAAK,UAAU;AACf,aAAK,YAAY;AACjB,aAAK,WAAW;AAAA,MAClB;AAAA,MACA,kBAAkB;AAChB,YAAI,KAAK,SAAS;AAChB,eAAK,QAAQ,OAAO,CAAC,KAAK,QAAQ;AAAA,QACpC;AAAA,MACF;AAAA,MACA,gBAAgB;AACd,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,eAAe,CAAC;AACtB,aAAK,UAAU;AACf,aAAK,SAAS;AACd,aAAK,UAAU,KAAK;AAAA,UAClB,SAAS;AAAA,UACT;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACM,oBAAoB;AAAA;AAOxB,cAAI,KAAK,SAAS;AAChB,iBAAK,aAAa;AAAA,UACpB;AAAA,QACF;AAAA;AAAA,MACA,mBAAmB;AACjB,aAAK,aAAa;AAClB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,uBAAuB;AACrB,YAAI,KAAK,SAAS;AAChB,eAAK,QAAQ,QAAQ;AACrB,eAAK,UAAU;AAAA,QACjB;AAAA,MACF;AAAA,MACA,oBAAoB;AAClB,aAAK,sBAAsB,OAAO,OAAO,CAAC,GAAG,sBAAsB,KAAK,EAAE,CAAC;AAAA,MAC7E;AAAA,MACA,UAAU;AACR,aAAK,YAAY;AAEjB,aAAK,SAAS;AAAA,MAChB;AAAA,MACA,OAAO,QAAQ;AACb,YAAI,aAAa,MAAM,KAAK,EAAE,GAAG,KAAK,SAAS,OAAO,QAAQ,GAAG,GAAG;AAClE,eAAK,cAAc;AACnB,0BAAgB;AAAA,QAClB;AAAA,MACF;AAAA,MACA,MAAM,IAAI;AACR,aAAK,YAAY;AACjB,aAAK,WAAW,KAAK,IAAI;AACzB,WAAG,MAAM,eAAe;AACxB,WAAG,MAAM,yBAAyB;AAAA,MACpC;AAAA,MACA,WAAW;AACT,eAAO,KAAK,SAAS;AAAA,MACvB;AAAA,MACA,WAAW;AACT,YAAI,KAAK,SAAS;AAChB,eAAK,QAAQ,MAAM;AAAA,QACrB;AAAA,MACF;AAAA,MACA,wBAAwB,MAAM,SAAS;AACrC,cAAM,OAAO,KAAK,mBAAmB,MAAM,OAAO;AAClD,eAAO,EAAE,YAAY;AAAA,UACnB,OAAO;AAAA,YACL,sBAAsB;AAAA,YACtB,8BAA8B;AAAA,UAChC;AAAA,UACA;AAAA,UACA,eAAe;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,MACA,sBAAsB;AACpB,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,eAAO,EAAE,OAAO;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,UACN,KAAK,QAAM,KAAK,cAAc;AAAA,QAChC,GAAG,qBAAqB,SAAS,SAAS,CAAC,KAAK,wBAAwB,MAAM,IAAI,GAAG,KAAK,wBAAwB,MAAM,KAAK,CAAC,GAAG,EAAE,OAAO;AAAA,UACxI,OAAO;AAAA,QACT,GAAG,EAAE,OAAO;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QACR,GAAG,qBAAqB,SAAS,QAAQ,KAAK,wBAAwB,MAAM,OAAO,CAAC,CAAC,CAAC;AAAA,MACxF;AAAA,MACA,IAAI,WAAW;AACb,eAAO,KAAK,GAAG,gBAAgB;AAAA,MACjC;AAAA,MACA,gBAAgB;AACd,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,GAAG,UAAU,SAAS,aAAa,KAAK,GAAG,UAAU,SAAS,aAAa,KAAK,WAAW;AAC7F,iBAAO;AAAA,QACT;AACA,YAAI,YAAY;AACd,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,iBAAiB;AACf,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AAKJ,cAAM,cAAc,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,YAAI,CAAC,aAAa;AAChB;AAAA,QACF;AACA,eAAO,EAAE,OAAO;AAAA,UACd,OAAO;AAAA,QACT,GAAG,EAAE,OAAO;AAAA,UACV,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,GAAG,UAAU,GAAG,EAAE,OAAO;AAAA,UACvB,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,GAAG,SAAS,CAAC;AAAA,MACf;AAAA,MACA,SAAS;AACP,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM,QAAQ,KAAK,SAAS;AAC5B,cAAM,MAAM,MAAM,EAAE,IAAI,QAAQ;AAChC,0BAAkB,MAAM,IAAI,MAAM,UAAU,QAAQ,IAAI,QAAQ;AAChE,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,MAAM;AAAA,UACN,gBAAgB,GAAG,OAAO;AAAA,UAC1B,oBAAoB,KAAK,cAAc;AAAA,UACvC,gBAAgB,KAAK,cAAc,MAAM;AAAA,UACzC,SAAS,KAAK;AAAA,UACd,mBAAmB,WAAW,eAAe;AAAA,UAC7C,cAAc,oBAAoB,YAAY,KAAK;AAAA,UACnD,iBAAiB,WAAW,SAAS;AAAA,UACrC,UAAU,WAAW,SAAY;AAAA,UACjC,WAAW,KAAK;AAAA,UAChB,OAAO,mBAAmB,OAAO;AAAA,YAC/B,CAAC,IAAI,GAAG;AAAA,YACR,WAAW,YAAY,YAAY,EAAE;AAAA,YACrC,oBAAoB;AAAA,YACpB,kBAAkB;AAAA,YAClB,mBAAmB;AAAA,YACnB,CAAC,kBAAkB,OAAO,EAAE,GAAG,YAAY;AAAA,YAC3C,CAAC,oBAAoB,SAAS,EAAE,GAAG,cAAc;AAAA,YACjD,CAAC,0BAA0B,cAAc,EAAE,GAAG;AAAA,YAC9C,CAAC,UAAU,GAAG,EAAE,GAAG;AAAA,UACrB,CAAC;AAAA,QACH,GAAG,EAAE,SAAS;AAAA,UACZ,KAAK;AAAA,UACL,OAAO;AAAA,UACP,SAAS;AAAA,QACX,GAAG,EAAE,SAAS,OAAO,OAAO;AAAA,UAC1B,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,gBAAgB,GAAG,OAAO;AAAA,UAC1B;AAAA,UACA;AAAA,UACA,IAAI;AAAA,UACJ,SAAS,MAAM,KAAK,QAAQ;AAAA,UAC5B,QAAQ,MAAM,KAAK,OAAO;AAAA,UAC1B,KAAK,aAAW,KAAK,UAAU;AAAA,UAC/B;AAAA,QACF,GAAG,mBAAmB,CAAC,GAAG,EAAE,OAAO;AAAA,UACjC,KAAK;AAAA,UACL,OAAO;AAAA,YACL,sBAAsB;AAAA,YACtB,6BAA6B,CAAC;AAAA,UAChC;AAAA,UACA,MAAM;AAAA,UACN,IAAI;AAAA,UACJ,SAAS,KAAK;AAAA,QAChB,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,QACP,CAAC,GAAG,KAAK,eAAe,CAAC,GAAG,EAAE,OAAO;AAAA,UACnC,KAAK;AAAA,UACL,OAAO;AAAA,QACT,GAAG,KAAK,oBAAoB,CAAC,CAAC,CAAC;AAAA,MACjC;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,YAAY,CAAC,iBAAiB;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AACA,IAAM,eAAe,CAAC,KAAK,SAAS,QAAQ,WAAW;AACrD,UAAI,SAAS;AACX,eAAO,CAAC,OAAO,SAAS,UAAU,OAAO,CAAC,SAAS;AAAA,MACrD,OAAO;AACL,eAAO,CAAC,OAAO,CAAC,SAAS,UAAU,OAAO,SAAS;AAAA,MACrD;AAAA,IACF;AACA,IAAI,YAAY;AAChB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}