/**
 * Custom App Styles
 * -----------------------------------------------------
 * Import custom theme files
 */
 @use './theme/mixins.scss';
 @use './theme/components.scss';
 @use './theme/utilities.scss';

/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
/* @import "@ionic/angular/css/palettes/dark.class.css"; */
@import '@ionic/angular/css/palettes/dark.system.css';


/**
 * Global App Styles
 * -----------------------------------------------------
 * These styles apply to the entire app
 */

// Apply base font family to the entire app
* {
  font-family: var(--app-font-family);
}

// Set default text color
body, ion-content {
  color: var(--app-text-color);
}

// Set default background color
ion-content {
  --background: var(--app-background-color);
}

// Improve focus styles for accessibility
:focus {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

// Smooth scrolling
html {
  scroll-behavior: smooth;
}

// Improve tap highlight on mobile
* {
  -webkit-tap-highlight-color: rgba(var(--ion-color-primary-rgb), 0.2);
}

// Improve form elements
ion-input, ion-textarea, ion-select {
  margin-bottom: var(--app-spacing-md);
}

// Improve buttons
ion-button {
  text-transform: none;
}

// Improve card styles
ion-card {
  overflow: hidden;
  border-radius: var(--app-border-radius-md);
  box-shadow: var(--app-card-shadow);
  margin: var(--app-spacing-md);

  ion-card-header {
    padding-bottom: var(--app-spacing-sm);

    ion-card-title {
      font-size: 18px;
      font-weight: var(--app-heading-font-weight);
      color: var(--ion-color-primary);
    }

    ion-card-subtitle {
      font-size: 14px;
      color: var(--ion-color-medium);
    }
  }

  ion-card-content {
    padding: var(--app-spacing-md);
  }
}

// Custom list styles
ion-list {
  background: transparent;

  ion-item {
    --padding-start: var(--app-spacing-md);
    --padding-end: var(--app-spacing-md);
    --padding-top: var(--app-spacing-sm);
    --padding-bottom: var(--app-spacing-sm);
    --background: transparent;

    &:last-child {
      --border-width: 0;
    }
  }
}

// Custom input styles
ion-item.custom-input {
  --background: var(--app-input-background);
  --border-radius: var(--app-border-radius-md);
  --padding-start: var(--app-spacing-md);
  margin-bottom: var(--app-spacing-md);

  ion-input, ion-textarea, ion-select {
    --padding-start: 0;
  }
}

// Logo animation
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
}
