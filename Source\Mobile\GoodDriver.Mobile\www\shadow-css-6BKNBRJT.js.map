{"version": 3, "sources": ["node_modules/@stencil/core/internal/client/shadow-css.js"], "sourcesContent": ["// src/utils/regular-expression.ts\nvar escapeRegExpSpecialCharacters = text => {\n  return text.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n};\n\n// src/utils/shadow-css.ts\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n *\n * This file is a port of shadowCSS from `webcomponents.js` to TypeScript.\n * https://github.com/webcomponents/webcomponentsjs/blob/4efecd7e0e/src/ShadowCSS/ShadowCSS.js\n * https://github.com/angular/angular/blob/master/packages/compiler/src/shadow_css.ts\n */\nvar safeSelector = selector => {\n  const placeholders = [];\n  let index = 0;\n  selector = selector.replace(/(\\[[^\\]]*\\])/g, (_, keep) => {\n    const replaceBy = `__ph-${index}__`;\n    placeholders.push(keep);\n    index++;\n    return replaceBy;\n  });\n  const content = selector.replace(/(:nth-[-\\w]+)(\\([^)]+\\))/g, (_, pseudo, exp) => {\n    const replaceBy = `__ph-${index}__`;\n    placeholders.push(exp);\n    index++;\n    return pseudo + replaceBy;\n  });\n  const ss = {\n    content,\n    placeholders\n  };\n  return ss;\n};\nvar restoreSafeSelector = (placeholders, content) => {\n  return content.replace(/__ph-(\\d+)__/g, (_, index) => placeholders[+index]);\n};\nvar _polyfillHost = \"-shadowcsshost\";\nvar _polyfillSlotted = \"-shadowcssslotted\";\nvar _polyfillHostContext = \"-shadowcsscontext\";\nvar _parenSuffix = \")(?:\\\\(((?:\\\\([^)(]*\\\\)|[^)(]*)+?)\\\\))?([^,{]*)\";\nvar _cssColonHostRe = new RegExp(\"(\" + _polyfillHost + _parenSuffix, \"gim\");\nvar _cssColonHostContextRe = new RegExp(\"(\" + _polyfillHostContext + _parenSuffix, \"gim\");\nvar _cssColonSlottedRe = new RegExp(\"(\" + _polyfillSlotted + _parenSuffix, \"gim\");\nvar _polyfillHostNoCombinator = _polyfillHost + \"-no-combinator\";\nvar _polyfillHostNoCombinatorRe = /-shadowcsshost-no-combinator([^\\s]*)/;\nvar _shadowDOMSelectorsRe = [/::shadow/g, /::content/g];\nvar _selectorReSuffix = \"([>\\\\s~+[.,{:][\\\\s\\\\S]*)?$\";\nvar _polyfillHostRe = /-shadowcsshost/gim;\nvar createSupportsRuleRe = selector => new RegExp(`((?<!(^@supports(.*)))|(?<={.*))(${selector}\\\\b)`, \"gim\");\nvar _colonSlottedRe = createSupportsRuleRe(\"::slotted\");\nvar _colonHostRe = createSupportsRuleRe(\":host\");\nvar _colonHostContextRe = createSupportsRuleRe(\":host-context\");\nvar _commentRe = /\\/\\*\\s*[\\s\\S]*?\\*\\//g;\nvar stripComments = input => {\n  return input.replace(_commentRe, \"\");\n};\nvar _commentWithHashRe = /\\/\\*\\s*#\\s*source(Mapping)?URL=[\\s\\S]+?\\*\\//g;\nvar extractCommentsWithHash = input => {\n  return input.match(_commentWithHashRe) || [];\n};\nvar _ruleRe = /(\\s*)([^;\\{\\}]+?)(\\s*)((?:{%BLOCK%}?\\s*;?)|(?:\\s*;))/g;\nvar _curlyRe = /([{}])/g;\nvar _selectorPartsRe = /(^.*?[^\\\\])??((:+)(.*)|$)/;\nvar OPEN_CURLY = \"{\";\nvar CLOSE_CURLY = \"}\";\nvar BLOCK_PLACEHOLDER = \"%BLOCK%\";\nvar processRules = (input, ruleCallback) => {\n  const inputWithEscapedBlocks = escapeBlocks(input);\n  let nextBlockIndex = 0;\n  return inputWithEscapedBlocks.escapedString.replace(_ruleRe, (...m) => {\n    const selector = m[2];\n    let content = \"\";\n    let suffix = m[4];\n    let contentPrefix = \"\";\n    if (suffix && suffix.startsWith(\"{\" + BLOCK_PLACEHOLDER)) {\n      content = inputWithEscapedBlocks.blocks[nextBlockIndex++];\n      suffix = suffix.substring(BLOCK_PLACEHOLDER.length + 1);\n      contentPrefix = \"{\";\n    }\n    const cssRule = {\n      selector,\n      content\n    };\n    const rule = ruleCallback(cssRule);\n    return `${m[1]}${rule.selector}${m[3]}${contentPrefix}${rule.content}${suffix}`;\n  });\n};\nvar escapeBlocks = input => {\n  const inputParts = input.split(_curlyRe);\n  const resultParts = [];\n  const escapedBlocks = [];\n  let bracketCount = 0;\n  let currentBlockParts = [];\n  for (let partIndex = 0; partIndex < inputParts.length; partIndex++) {\n    const part = inputParts[partIndex];\n    if (part === CLOSE_CURLY) {\n      bracketCount--;\n    }\n    if (bracketCount > 0) {\n      currentBlockParts.push(part);\n    } else {\n      if (currentBlockParts.length > 0) {\n        escapedBlocks.push(currentBlockParts.join(\"\"));\n        resultParts.push(BLOCK_PLACEHOLDER);\n        currentBlockParts = [];\n      }\n      resultParts.push(part);\n    }\n    if (part === OPEN_CURLY) {\n      bracketCount++;\n    }\n  }\n  if (currentBlockParts.length > 0) {\n    escapedBlocks.push(currentBlockParts.join(\"\"));\n    resultParts.push(BLOCK_PLACEHOLDER);\n  }\n  const strEscapedBlocks = {\n    escapedString: resultParts.join(\"\"),\n    blocks: escapedBlocks\n  };\n  return strEscapedBlocks;\n};\nvar insertPolyfillHostInCssText = cssText => {\n  cssText = cssText.replace(_colonHostContextRe, `$1${_polyfillHostContext}`).replace(_colonHostRe, `$1${_polyfillHost}`).replace(_colonSlottedRe, `$1${_polyfillSlotted}`);\n  return cssText;\n};\nvar convertColonRule = (cssText, regExp, partReplacer) => {\n  return cssText.replace(regExp, (...m) => {\n    if (m[2]) {\n      const parts = m[2].split(\",\");\n      const r = [];\n      for (let i = 0; i < parts.length; i++) {\n        const p = parts[i].trim();\n        if (!p) break;\n        r.push(partReplacer(_polyfillHostNoCombinator, p, m[3]));\n      }\n      return r.join(\",\");\n    } else {\n      return _polyfillHostNoCombinator + m[3];\n    }\n  });\n};\nvar colonHostPartReplacer = (host, part, suffix) => {\n  return host + part.replace(_polyfillHost, \"\") + suffix;\n};\nvar convertColonHost = cssText => {\n  return convertColonRule(cssText, _cssColonHostRe, colonHostPartReplacer);\n};\nvar colonHostContextPartReplacer = (host, part, suffix) => {\n  if (part.indexOf(_polyfillHost) > -1) {\n    return colonHostPartReplacer(host, part, suffix);\n  } else {\n    return host + part + suffix + \", \" + part + \" \" + host + suffix;\n  }\n};\nvar convertColonSlotted = (cssText, slotScopeId) => {\n  const slotClass = \".\" + slotScopeId + \" > \";\n  const selectors = [];\n  cssText = cssText.replace(_cssColonSlottedRe, (...m) => {\n    if (m[2]) {\n      const compound = m[2].trim();\n      const suffix = m[3];\n      const slottedSelector = slotClass + compound + suffix;\n      let prefixSelector = \"\";\n      for (let i = m[4] - 1; i >= 0; i--) {\n        const char = m[5][i];\n        if (char === \"}\" || char === \",\") {\n          break;\n        }\n        prefixSelector = char + prefixSelector;\n      }\n      const orgSelector = (prefixSelector + slottedSelector).trim();\n      const addedSelector = `${prefixSelector.trimEnd()}${slottedSelector.trim()}`.trim();\n      if (orgSelector !== addedSelector) {\n        const updatedSelector = `${addedSelector}, ${orgSelector}`;\n        selectors.push({\n          orgSelector,\n          updatedSelector\n        });\n      }\n      return slottedSelector;\n    } else {\n      return _polyfillHostNoCombinator + m[3];\n    }\n  });\n  return {\n    selectors,\n    cssText\n  };\n};\nvar convertColonHostContext = cssText => {\n  return convertColonRule(cssText, _cssColonHostContextRe, colonHostContextPartReplacer);\n};\nvar convertShadowDOMSelectors = cssText => {\n  return _shadowDOMSelectorsRe.reduce((result, pattern) => result.replace(pattern, \" \"), cssText);\n};\nvar makeScopeMatcher = scopeSelector2 => {\n  const lre = /\\[/g;\n  const rre = /\\]/g;\n  scopeSelector2 = scopeSelector2.replace(lre, \"\\\\[\").replace(rre, \"\\\\]\");\n  return new RegExp(\"^(\" + scopeSelector2 + \")\" + _selectorReSuffix, \"m\");\n};\nvar selectorNeedsScoping = (selector, scopeSelector2) => {\n  const re = makeScopeMatcher(scopeSelector2);\n  return !re.test(selector);\n};\nvar injectScopingSelector = (selector, scopingSelector) => {\n  return selector.replace(_selectorPartsRe, (_, before = \"\", _colonGroup, colon = \"\", after = \"\") => {\n    return before + scopingSelector + colon + after;\n  });\n};\nvar applySimpleSelectorScope = (selector, scopeSelector2, hostSelector) => {\n  _polyfillHostRe.lastIndex = 0;\n  if (_polyfillHostRe.test(selector)) {\n    const replaceBy = `.${hostSelector}`;\n    return selector.replace(_polyfillHostNoCombinatorRe, (_, selector2) => injectScopingSelector(selector2, replaceBy)).replace(_polyfillHostRe, replaceBy + \" \");\n  }\n  return scopeSelector2 + \" \" + selector;\n};\nvar applyStrictSelectorScope = (selector, scopeSelector2, hostSelector) => {\n  const isRe = /\\[is=([^\\]]*)\\]/g;\n  scopeSelector2 = scopeSelector2.replace(isRe, (_, ...parts) => parts[0]);\n  const className = \".\" + scopeSelector2;\n  const _scopeSelectorPart = p => {\n    let scopedP = p.trim();\n    if (!scopedP) {\n      return \"\";\n    }\n    if (p.indexOf(_polyfillHostNoCombinator) > -1) {\n      scopedP = applySimpleSelectorScope(p, scopeSelector2, hostSelector);\n    } else {\n      const t = p.replace(_polyfillHostRe, \"\");\n      if (t.length > 0) {\n        scopedP = injectScopingSelector(t, className);\n      }\n    }\n    return scopedP;\n  };\n  const safeContent = safeSelector(selector);\n  selector = safeContent.content;\n  let scopedSelector = \"\";\n  let startIndex = 0;\n  let res;\n  const sep = /( |>|\\+|~(?!=))\\s*/g;\n  const hasHost = selector.indexOf(_polyfillHostNoCombinator) > -1;\n  let shouldScope = !hasHost;\n  while ((res = sep.exec(selector)) !== null) {\n    const separator = res[1];\n    const part2 = selector.slice(startIndex, res.index).trim();\n    shouldScope = shouldScope || part2.indexOf(_polyfillHostNoCombinator) > -1;\n    const scopedPart = shouldScope ? _scopeSelectorPart(part2) : part2;\n    scopedSelector += `${scopedPart} ${separator} `;\n    startIndex = sep.lastIndex;\n  }\n  const part = selector.substring(startIndex);\n  shouldScope = shouldScope || part.indexOf(_polyfillHostNoCombinator) > -1;\n  scopedSelector += shouldScope ? _scopeSelectorPart(part) : part;\n  return restoreSafeSelector(safeContent.placeholders, scopedSelector);\n};\nvar scopeSelector = (selector, scopeSelectorText, hostSelector, slotSelector) => {\n  return selector.split(\",\").map(shallowPart => {\n    if (slotSelector && shallowPart.indexOf(\".\" + slotSelector) > -1) {\n      return shallowPart.trim();\n    }\n    if (selectorNeedsScoping(shallowPart, scopeSelectorText)) {\n      return applyStrictSelectorScope(shallowPart, scopeSelectorText, hostSelector).trim();\n    } else {\n      return shallowPart.trim();\n    }\n  }).join(\", \");\n};\nvar scopeSelectors = (cssText, scopeSelectorText, hostSelector, slotSelector) => {\n  return processRules(cssText, rule => {\n    let selector = rule.selector;\n    let content = rule.content;\n    if (rule.selector[0] !== \"@\") {\n      selector = scopeSelector(rule.selector, scopeSelectorText, hostSelector, slotSelector);\n    } else if (rule.selector.startsWith(\"@media\") || rule.selector.startsWith(\"@supports\") || rule.selector.startsWith(\"@page\") || rule.selector.startsWith(\"@document\")) {\n      content = scopeSelectors(rule.content, scopeSelectorText, hostSelector, slotSelector);\n    }\n    const cssRule = {\n      selector: selector.replace(/\\s{2,}/g, \" \").trim(),\n      content\n    };\n    return cssRule;\n  });\n};\nvar scopeCssText = (cssText, scopeId, hostScopeId, slotScopeId) => {\n  cssText = insertPolyfillHostInCssText(cssText);\n  cssText = convertColonHost(cssText);\n  cssText = convertColonHostContext(cssText);\n  const slotted = convertColonSlotted(cssText, slotScopeId);\n  cssText = slotted.cssText;\n  cssText = convertShadowDOMSelectors(cssText);\n  if (scopeId) {\n    cssText = scopeSelectors(cssText, scopeId, hostScopeId, slotScopeId);\n  }\n  cssText = replaceShadowCssHost(cssText, hostScopeId);\n  cssText = cssText.replace(/>\\s*\\*\\s+([^{, ]+)/gm, \" $1 \");\n  return {\n    cssText: cssText.trim(),\n    // We need to replace the shadow CSS host string in each of these selectors since we created\n    // them prior to the replacement happening in the components CSS text.\n    slottedSelectors: slotted.selectors.map(ref => ({\n      orgSelector: replaceShadowCssHost(ref.orgSelector, hostScopeId),\n      updatedSelector: replaceShadowCssHost(ref.updatedSelector, hostScopeId)\n    }))\n  };\n};\nvar replaceShadowCssHost = (cssText, hostScopeId) => {\n  return cssText.replace(/-shadowcsshost-no-combinator/g, `.${hostScopeId}`);\n};\nvar scopeCss = (cssText, scopeId) => {\n  const hostScopeId = scopeId + \"-h\";\n  const slotScopeId = scopeId + \"-s\";\n  const commentsWithHash = extractCommentsWithHash(cssText);\n  cssText = stripComments(cssText);\n  const scoped = scopeCssText(cssText, scopeId, hostScopeId, slotScopeId);\n  cssText = [scoped.cssText, ...commentsWithHash].join(\"\\n\");\n  scoped.slottedSelectors.forEach(slottedSelector => {\n    const regex = new RegExp(escapeRegExpSpecialCharacters(slottedSelector.orgSelector), \"g\");\n    cssText = cssText.replace(regex, slottedSelector.updatedSelector);\n  });\n  return cssText;\n};\nexport { scopeCss };"], "mappings": ";;;;;AAAA,IACI,+BAgBA,cAqBA,qBAGA,eACA,kBACA,sBACA,cACA,iBACA,wBACA,oBACA,2BACA,6BACA,uBACA,mBACA,iBACA,sBACA,iBACA,cACA,qBACA,YACA,eAGA,oBACA,yBAGA,SACA,UACA,kBACA,YACA,aACA,mBACA,cAqBA,cAmCA,6BAIA,kBAgBA,uBAGA,kBAGA,8BAOA,qBAmCA,yBAGA,2BAGA,kBAMA,sBAIA,uBAKA,0BAQA,0BAwCA,eAYA,gBAgBA,cAsBA,sBAGA;AA7TJ;AAAA;AACA,IAAI,gCAAgC,UAAQ;AAC1C,aAAO,KAAK,QAAQ,uBAAuB,MAAM;AAAA,IACnD;AAcA,IAAI,eAAe,cAAY;AAC7B,YAAM,eAAe,CAAC;AACtB,UAAI,QAAQ;AACZ,iBAAW,SAAS,QAAQ,iBAAiB,CAAC,GAAG,SAAS;AACxD,cAAM,YAAY,QAAQ,KAAK;AAC/B,qBAAa,KAAK,IAAI;AACtB;AACA,eAAO;AAAA,MACT,CAAC;AACD,YAAM,UAAU,SAAS,QAAQ,6BAA6B,CAAC,GAAG,QAAQ,QAAQ;AAChF,cAAM,YAAY,QAAQ,KAAK;AAC/B,qBAAa,KAAK,GAAG;AACrB;AACA,eAAO,SAAS;AAAA,MAClB,CAAC;AACD,YAAM,KAAK;AAAA,QACT;AAAA,QACA;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,IAAI,sBAAsB,CAAC,cAAc,YAAY;AACnD,aAAO,QAAQ,QAAQ,iBAAiB,CAAC,GAAG,UAAU,aAAa,CAAC,KAAK,CAAC;AAAA,IAC5E;AACA,IAAI,gBAAgB;AACpB,IAAI,mBAAmB;AACvB,IAAI,uBAAuB;AAC3B,IAAI,eAAe;AACnB,IAAI,kBAAkB,IAAI,OAAO,MAAM,gBAAgB,cAAc,KAAK;AAC1E,IAAI,yBAAyB,IAAI,OAAO,MAAM,uBAAuB,cAAc,KAAK;AACxF,IAAI,qBAAqB,IAAI,OAAO,MAAM,mBAAmB,cAAc,KAAK;AAChF,IAAI,4BAA4B,gBAAgB;AAChD,IAAI,8BAA8B;AAClC,IAAI,wBAAwB,CAAC,aAAa,YAAY;AACtD,IAAI,oBAAoB;AACxB,IAAI,kBAAkB;AACtB,IAAI,uBAAuB,cAAY,IAAI,OAAO,oCAAoC,QAAQ,QAAQ,KAAK;AAC3G,IAAI,kBAAkB,qBAAqB,WAAW;AACtD,IAAI,eAAe,qBAAqB,OAAO;AAC/C,IAAI,sBAAsB,qBAAqB,eAAe;AAC9D,IAAI,aAAa;AACjB,IAAI,gBAAgB,WAAS;AAC3B,aAAO,MAAM,QAAQ,YAAY,EAAE;AAAA,IACrC;AACA,IAAI,qBAAqB;AACzB,IAAI,0BAA0B,WAAS;AACrC,aAAO,MAAM,MAAM,kBAAkB,KAAK,CAAC;AAAA,IAC7C;AACA,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,mBAAmB;AACvB,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,oBAAoB;AACxB,IAAI,eAAe,CAAC,OAAO,iBAAiB;AAC1C,YAAM,yBAAyB,aAAa,KAAK;AACjD,UAAI,iBAAiB;AACrB,aAAO,uBAAuB,cAAc,QAAQ,SAAS,IAAI,MAAM;AACrE,cAAM,WAAW,EAAE,CAAC;AACpB,YAAI,UAAU;AACd,YAAI,SAAS,EAAE,CAAC;AAChB,YAAI,gBAAgB;AACpB,YAAI,UAAU,OAAO,WAAW,MAAM,iBAAiB,GAAG;AACxD,oBAAU,uBAAuB,OAAO,gBAAgB;AACxD,mBAAS,OAAO,UAAU,kBAAkB,SAAS,CAAC;AACtD,0BAAgB;AAAA,QAClB;AACA,cAAM,UAAU;AAAA,UACd;AAAA,UACA;AAAA,QACF;AACA,cAAM,OAAO,aAAa,OAAO;AACjC,eAAO,GAAG,EAAE,CAAC,CAAC,GAAG,KAAK,QAAQ,GAAG,EAAE,CAAC,CAAC,GAAG,aAAa,GAAG,KAAK,OAAO,GAAG,MAAM;AAAA,MAC/E,CAAC;AAAA,IACH;AACA,IAAI,eAAe,WAAS;AAC1B,YAAM,aAAa,MAAM,MAAM,QAAQ;AACvC,YAAM,cAAc,CAAC;AACrB,YAAM,gBAAgB,CAAC;AACvB,UAAI,eAAe;AACnB,UAAI,oBAAoB,CAAC;AACzB,eAAS,YAAY,GAAG,YAAY,WAAW,QAAQ,aAAa;AAClE,cAAM,OAAO,WAAW,SAAS;AACjC,YAAI,SAAS,aAAa;AACxB;AAAA,QACF;AACA,YAAI,eAAe,GAAG;AACpB,4BAAkB,KAAK,IAAI;AAAA,QAC7B,OAAO;AACL,cAAI,kBAAkB,SAAS,GAAG;AAChC,0BAAc,KAAK,kBAAkB,KAAK,EAAE,CAAC;AAC7C,wBAAY,KAAK,iBAAiB;AAClC,gCAAoB,CAAC;AAAA,UACvB;AACA,sBAAY,KAAK,IAAI;AAAA,QACvB;AACA,YAAI,SAAS,YAAY;AACvB;AAAA,QACF;AAAA,MACF;AACA,UAAI,kBAAkB,SAAS,GAAG;AAChC,sBAAc,KAAK,kBAAkB,KAAK,EAAE,CAAC;AAC7C,oBAAY,KAAK,iBAAiB;AAAA,MACpC;AACA,YAAM,mBAAmB;AAAA,QACvB,eAAe,YAAY,KAAK,EAAE;AAAA,QAClC,QAAQ;AAAA,MACV;AACA,aAAO;AAAA,IACT;AACA,IAAI,8BAA8B,aAAW;AAC3C,gBAAU,QAAQ,QAAQ,qBAAqB,KAAK,oBAAoB,EAAE,EAAE,QAAQ,cAAc,KAAK,aAAa,EAAE,EAAE,QAAQ,iBAAiB,KAAK,gBAAgB,EAAE;AACxK,aAAO;AAAA,IACT;AACA,IAAI,mBAAmB,CAAC,SAAS,QAAQ,iBAAiB;AACxD,aAAO,QAAQ,QAAQ,QAAQ,IAAI,MAAM;AACvC,YAAI,EAAE,CAAC,GAAG;AACR,gBAAM,QAAQ,EAAE,CAAC,EAAE,MAAM,GAAG;AAC5B,gBAAM,IAAI,CAAC;AACX,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,kBAAM,IAAI,MAAM,CAAC,EAAE,KAAK;AACxB,gBAAI,CAAC,EAAG;AACR,cAAE,KAAK,aAAa,2BAA2B,GAAG,EAAE,CAAC,CAAC,CAAC;AAAA,UACzD;AACA,iBAAO,EAAE,KAAK,GAAG;AAAA,QACnB,OAAO;AACL,iBAAO,4BAA4B,EAAE,CAAC;AAAA,QACxC;AAAA,MACF,CAAC;AAAA,IACH;AACA,IAAI,wBAAwB,CAAC,MAAM,MAAM,WAAW;AAClD,aAAO,OAAO,KAAK,QAAQ,eAAe,EAAE,IAAI;AAAA,IAClD;AACA,IAAI,mBAAmB,aAAW;AAChC,aAAO,iBAAiB,SAAS,iBAAiB,qBAAqB;AAAA,IACzE;AACA,IAAI,+BAA+B,CAAC,MAAM,MAAM,WAAW;AACzD,UAAI,KAAK,QAAQ,aAAa,IAAI,IAAI;AACpC,eAAO,sBAAsB,MAAM,MAAM,MAAM;AAAA,MACjD,OAAO;AACL,eAAO,OAAO,OAAO,SAAS,OAAO,OAAO,MAAM,OAAO;AAAA,MAC3D;AAAA,IACF;AACA,IAAI,sBAAsB,CAAC,SAAS,gBAAgB;AAClD,YAAM,YAAY,MAAM,cAAc;AACtC,YAAM,YAAY,CAAC;AACnB,gBAAU,QAAQ,QAAQ,oBAAoB,IAAI,MAAM;AACtD,YAAI,EAAE,CAAC,GAAG;AACR,gBAAM,WAAW,EAAE,CAAC,EAAE,KAAK;AAC3B,gBAAM,SAAS,EAAE,CAAC;AAClB,gBAAM,kBAAkB,YAAY,WAAW;AAC/C,cAAI,iBAAiB;AACrB,mBAAS,IAAI,EAAE,CAAC,IAAI,GAAG,KAAK,GAAG,KAAK;AAClC,kBAAM,OAAO,EAAE,CAAC,EAAE,CAAC;AACnB,gBAAI,SAAS,OAAO,SAAS,KAAK;AAChC;AAAA,YACF;AACA,6BAAiB,OAAO;AAAA,UAC1B;AACA,gBAAM,eAAe,iBAAiB,iBAAiB,KAAK;AAC5D,gBAAM,gBAAgB,GAAG,eAAe,QAAQ,CAAC,GAAG,gBAAgB,KAAK,CAAC,GAAG,KAAK;AAClF,cAAI,gBAAgB,eAAe;AACjC,kBAAM,kBAAkB,GAAG,aAAa,KAAK,WAAW;AACxD,sBAAU,KAAK;AAAA,cACb;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH;AACA,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,4BAA4B,EAAE,CAAC;AAAA,QACxC;AAAA,MACF,CAAC;AACD,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,IAAI,0BAA0B,aAAW;AACvC,aAAO,iBAAiB,SAAS,wBAAwB,4BAA4B;AAAA,IACvF;AACA,IAAI,4BAA4B,aAAW;AACzC,aAAO,sBAAsB,OAAO,CAAC,QAAQ,YAAY,OAAO,QAAQ,SAAS,GAAG,GAAG,OAAO;AAAA,IAChG;AACA,IAAI,mBAAmB,oBAAkB;AACvC,YAAM,MAAM;AACZ,YAAM,MAAM;AACZ,uBAAiB,eAAe,QAAQ,KAAK,KAAK,EAAE,QAAQ,KAAK,KAAK;AACtE,aAAO,IAAI,OAAO,OAAO,iBAAiB,MAAM,mBAAmB,GAAG;AAAA,IACxE;AACA,IAAI,uBAAuB,CAAC,UAAU,mBAAmB;AACvD,YAAM,KAAK,iBAAiB,cAAc;AAC1C,aAAO,CAAC,GAAG,KAAK,QAAQ;AAAA,IAC1B;AACA,IAAI,wBAAwB,CAAC,UAAU,oBAAoB;AACzD,aAAO,SAAS,QAAQ,kBAAkB,CAAC,GAAG,SAAS,IAAI,aAAa,QAAQ,IAAI,QAAQ,OAAO;AACjG,eAAO,SAAS,kBAAkB,QAAQ;AAAA,MAC5C,CAAC;AAAA,IACH;AACA,IAAI,2BAA2B,CAAC,UAAU,gBAAgB,iBAAiB;AACzE,sBAAgB,YAAY;AAC5B,UAAI,gBAAgB,KAAK,QAAQ,GAAG;AAClC,cAAM,YAAY,IAAI,YAAY;AAClC,eAAO,SAAS,QAAQ,6BAA6B,CAAC,GAAG,cAAc,sBAAsB,WAAW,SAAS,CAAC,EAAE,QAAQ,iBAAiB,YAAY,GAAG;AAAA,MAC9J;AACA,aAAO,iBAAiB,MAAM;AAAA,IAChC;AACA,IAAI,2BAA2B,CAAC,UAAU,gBAAgB,iBAAiB;AACzE,YAAM,OAAO;AACb,uBAAiB,eAAe,QAAQ,MAAM,CAAC,MAAM,UAAU,MAAM,CAAC,CAAC;AACvE,YAAM,YAAY,MAAM;AACxB,YAAM,qBAAqB,OAAK;AAC9B,YAAI,UAAU,EAAE,KAAK;AACrB,YAAI,CAAC,SAAS;AACZ,iBAAO;AAAA,QACT;AACA,YAAI,EAAE,QAAQ,yBAAyB,IAAI,IAAI;AAC7C,oBAAU,yBAAyB,GAAG,gBAAgB,YAAY;AAAA,QACpE,OAAO;AACL,gBAAM,IAAI,EAAE,QAAQ,iBAAiB,EAAE;AACvC,cAAI,EAAE,SAAS,GAAG;AAChB,sBAAU,sBAAsB,GAAG,SAAS;AAAA,UAC9C;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,cAAc,aAAa,QAAQ;AACzC,iBAAW,YAAY;AACvB,UAAI,iBAAiB;AACrB,UAAI,aAAa;AACjB,UAAI;AACJ,YAAM,MAAM;AACZ,YAAM,UAAU,SAAS,QAAQ,yBAAyB,IAAI;AAC9D,UAAI,cAAc,CAAC;AACnB,cAAQ,MAAM,IAAI,KAAK,QAAQ,OAAO,MAAM;AAC1C,cAAM,YAAY,IAAI,CAAC;AACvB,cAAM,QAAQ,SAAS,MAAM,YAAY,IAAI,KAAK,EAAE,KAAK;AACzD,sBAAc,eAAe,MAAM,QAAQ,yBAAyB,IAAI;AACxE,cAAM,aAAa,cAAc,mBAAmB,KAAK,IAAI;AAC7D,0BAAkB,GAAG,UAAU,IAAI,SAAS;AAC5C,qBAAa,IAAI;AAAA,MACnB;AACA,YAAM,OAAO,SAAS,UAAU,UAAU;AAC1C,oBAAc,eAAe,KAAK,QAAQ,yBAAyB,IAAI;AACvE,wBAAkB,cAAc,mBAAmB,IAAI,IAAI;AAC3D,aAAO,oBAAoB,YAAY,cAAc,cAAc;AAAA,IACrE;AACA,IAAI,gBAAgB,CAAC,UAAU,mBAAmB,cAAc,iBAAiB;AAC/E,aAAO,SAAS,MAAM,GAAG,EAAE,IAAI,iBAAe;AAC5C,YAAI,gBAAgB,YAAY,QAAQ,MAAM,YAAY,IAAI,IAAI;AAChE,iBAAO,YAAY,KAAK;AAAA,QAC1B;AACA,YAAI,qBAAqB,aAAa,iBAAiB,GAAG;AACxD,iBAAO,yBAAyB,aAAa,mBAAmB,YAAY,EAAE,KAAK;AAAA,QACrF,OAAO;AACL,iBAAO,YAAY,KAAK;AAAA,QAC1B;AAAA,MACF,CAAC,EAAE,KAAK,IAAI;AAAA,IACd;AACA,IAAI,iBAAiB,CAAC,SAAS,mBAAmB,cAAc,iBAAiB;AAC/E,aAAO,aAAa,SAAS,UAAQ;AACnC,YAAI,WAAW,KAAK;AACpB,YAAI,UAAU,KAAK;AACnB,YAAI,KAAK,SAAS,CAAC,MAAM,KAAK;AAC5B,qBAAW,cAAc,KAAK,UAAU,mBAAmB,cAAc,YAAY;AAAA,QACvF,WAAW,KAAK,SAAS,WAAW,QAAQ,KAAK,KAAK,SAAS,WAAW,WAAW,KAAK,KAAK,SAAS,WAAW,OAAO,KAAK,KAAK,SAAS,WAAW,WAAW,GAAG;AACpK,oBAAU,eAAe,KAAK,SAAS,mBAAmB,cAAc,YAAY;AAAA,QACtF;AACA,cAAM,UAAU;AAAA,UACd,UAAU,SAAS,QAAQ,WAAW,GAAG,EAAE,KAAK;AAAA,UAChD;AAAA,QACF;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,IAAI,eAAe,CAAC,SAAS,SAAS,aAAa,gBAAgB;AACjE,gBAAU,4BAA4B,OAAO;AAC7C,gBAAU,iBAAiB,OAAO;AAClC,gBAAU,wBAAwB,OAAO;AACzC,YAAM,UAAU,oBAAoB,SAAS,WAAW;AACxD,gBAAU,QAAQ;AAClB,gBAAU,0BAA0B,OAAO;AAC3C,UAAI,SAAS;AACX,kBAAU,eAAe,SAAS,SAAS,aAAa,WAAW;AAAA,MACrE;AACA,gBAAU,qBAAqB,SAAS,WAAW;AACnD,gBAAU,QAAQ,QAAQ,wBAAwB,MAAM;AACxD,aAAO;AAAA,QACL,SAAS,QAAQ,KAAK;AAAA;AAAA;AAAA,QAGtB,kBAAkB,QAAQ,UAAU,IAAI,UAAQ;AAAA,UAC9C,aAAa,qBAAqB,IAAI,aAAa,WAAW;AAAA,UAC9D,iBAAiB,qBAAqB,IAAI,iBAAiB,WAAW;AAAA,QACxE,EAAE;AAAA,MACJ;AAAA,IACF;AACA,IAAI,uBAAuB,CAAC,SAAS,gBAAgB;AACnD,aAAO,QAAQ,QAAQ,iCAAiC,IAAI,WAAW,EAAE;AAAA,IAC3E;AACA,IAAI,WAAW,CAAC,SAAS,YAAY;AACnC,YAAM,cAAc,UAAU;AAC9B,YAAM,cAAc,UAAU;AAC9B,YAAM,mBAAmB,wBAAwB,OAAO;AACxD,gBAAU,cAAc,OAAO;AAC/B,YAAM,SAAS,aAAa,SAAS,SAAS,aAAa,WAAW;AACtE,gBAAU,CAAC,OAAO,SAAS,GAAG,gBAAgB,EAAE,KAAK,IAAI;AACzD,aAAO,iBAAiB,QAAQ,qBAAmB;AACjD,cAAM,QAAQ,IAAI,OAAO,8BAA8B,gBAAgB,WAAW,GAAG,GAAG;AACxF,kBAAU,QAAQ,QAAQ,OAAO,gBAAgB,eAAe;AAAA,MAClE,CAAC;AACD,aAAO;AAAA,IACT;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}