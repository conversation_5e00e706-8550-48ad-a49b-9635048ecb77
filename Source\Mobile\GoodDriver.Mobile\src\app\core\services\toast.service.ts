import { Injectable } from '@angular/core';
import { ToastController } from '@ionic/angular';

@Injectable({
  providedIn: 'root' //reutilizavél em qualquer parte do app
})
export class ToastService {

  constructor(private toastController: ToastController) {}

  async showToast(message: string, type: 'success' | 'danger' | 'warning' | 'info' = 'success') {
    const toast = await this.toastController.create({
      message,
      duration: 3000,
      position: 'bottom',
      color: type,
      buttons: [
        {
          side: 'end',
          icon: 'close',
          role: 'cancel'
        }
      ]
    });
    await toast.present();
  }
}
