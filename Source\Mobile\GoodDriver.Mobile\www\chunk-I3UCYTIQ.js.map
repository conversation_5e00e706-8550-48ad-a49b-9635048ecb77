{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/cubic-bezier-fe2083dc.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Based on:\n * https://stackoverflow.com/questions/7348009/y-coordinate-for-a-given-x-cubic-bezier\n * https://math.stackexchange.com/questions/26846/is-there-an-explicit-form-for-cubic-b%C3%A9zier-curves\n */\n/**\n * EXPERIMENTAL\n * Given a cubic-bezier curve, get the x value (time) given\n * the y value (progression).\n * Ex: cubic-bezier(0.32, 0.72, 0, 1);\n * P0: (0, 0)\n * P1: (0.32, 0.72)\n * P2: (0, 1)\n * P3: (1, 1)\n *\n * If you give a cubic bezier curve that never reaches the\n * provided progression, this function will return an empty array.\n */\nconst getTimeGivenProgression = (p0, p1, p2, p3, progression) => {\n  return solveCubicBezier(p0[1], p1[1], p2[1], p3[1], progression).map(tValue => {\n    return solveCubicParametricEquation(p0[0], p1[0], p2[0], p3[0], tValue);\n  });\n};\n/**\n * Solve a cubic equation in one dimension (time)\n */\nconst solveCubicParametricEquation = (p0, p1, p2, p3, t) => {\n  const partA = 3 * p1 * Math.pow(t - 1, 2);\n  const partB = -3 * p2 * t + 3 * p2 + p3 * t;\n  const partC = p0 * Math.pow(t - 1, 3);\n  return t * (partA + t * partB) - partC;\n};\n/**\n * Find the `t` value for a cubic bezier using Cardano's formula\n */\nconst solveCubicBezier = (p0, p1, p2, p3, refPoint) => {\n  p0 -= refPoint;\n  p1 -= refPoint;\n  p2 -= refPoint;\n  p3 -= refPoint;\n  const roots = solveCubicEquation(p3 - 3 * p2 + 3 * p1 - p0, 3 * p2 - 6 * p1 + 3 * p0, 3 * p1 - 3 * p0, p0);\n  return roots.filter(root => root >= 0 && root <= 1);\n};\nconst solveQuadraticEquation = (a, b, c) => {\n  const discriminant = b * b - 4 * a * c;\n  if (discriminant < 0) {\n    return [];\n  } else {\n    return [(-b + Math.sqrt(discriminant)) / (2 * a), (-b - Math.sqrt(discriminant)) / (2 * a)];\n  }\n};\nconst solveCubicEquation = (a, b, c, d) => {\n  if (a === 0) {\n    return solveQuadraticEquation(b, c, d);\n  }\n  b /= a;\n  c /= a;\n  d /= a;\n  const p = (3 * c - b * b) / 3;\n  const q = (2 * b * b * b - 9 * b * c + 27 * d) / 27;\n  if (p === 0) {\n    return [Math.pow(-q, 1 / 3)];\n  } else if (q === 0) {\n    return [Math.sqrt(-p), -Math.sqrt(-p)];\n  }\n  const discriminant = Math.pow(q / 2, 2) + Math.pow(p / 3, 3);\n  if (discriminant === 0) {\n    return [Math.pow(q / 2, 1 / 2) - b / 3];\n  } else if (discriminant > 0) {\n    return [Math.pow(-(q / 2) + Math.sqrt(discriminant), 1 / 3) - Math.pow(q / 2 + Math.sqrt(discriminant), 1 / 3) - b / 3];\n  }\n  const r = Math.sqrt(Math.pow(-(p / 3), 3));\n  const phi = Math.acos(-(q / (2 * Math.sqrt(Math.pow(-(p / 3), 3)))));\n  const s = 2 * Math.pow(r, 1 / 3);\n  return [s * Math.cos(phi / 3) - b / 3, s * Math.cos((phi + 2 * Math.PI) / 3) - b / 3, s * Math.cos((phi + 4 * Math.PI) / 3) - b / 3];\n};\nexport { getTimeGivenProgression as g };"], "mappings": ";;;;;AAAA,IAqBM,yBAQA,8BASA,kBAQA,wBAQA;AAtDN;AAAA;AAAA;AAqBA,IAAM,0BAA0B,CAAC,IAAI,IAAI,IAAI,IAAI,gBAAgB;AAC/D,aAAO,iBAAiB,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,WAAW,EAAE,IAAI,YAAU;AAC7E,eAAO,6BAA6B,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM;AAAA,MACxE,CAAC;AAAA,IACH;AAIA,IAAM,+BAA+B,CAAC,IAAI,IAAI,IAAI,IAAI,MAAM;AAC1D,YAAM,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC;AACxC,YAAM,QAAQ,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK;AAC1C,YAAM,QAAQ,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC;AACpC,aAAO,KAAK,QAAQ,IAAI,SAAS;AAAA,IACnC;AAIA,IAAM,mBAAmB,CAAC,IAAI,IAAI,IAAI,IAAI,aAAa;AACrD,YAAM;AACN,YAAM;AACN,YAAM;AACN,YAAM;AACN,YAAM,QAAQ,mBAAmB,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;AACzG,aAAO,MAAM,OAAO,UAAQ,QAAQ,KAAK,QAAQ,CAAC;AAAA,IACpD;AACA,IAAM,yBAAyB,CAAC,GAAG,GAAG,MAAM;AAC1C,YAAM,eAAe,IAAI,IAAI,IAAI,IAAI;AACrC,UAAI,eAAe,GAAG;AACpB,eAAO,CAAC;AAAA,MACV,OAAO;AACL,eAAO,EAAE,CAAC,IAAI,KAAK,KAAK,YAAY,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,YAAY,MAAM,IAAI,EAAE;AAAA,MAC5F;AAAA,IACF;AACA,IAAM,qBAAqB,CAAC,GAAG,GAAG,GAAG,MAAM;AACzC,UAAI,MAAM,GAAG;AACX,eAAO,uBAAuB,GAAG,GAAG,CAAC;AAAA,MACvC;AACA,WAAK;AACL,WAAK;AACL,WAAK;AACL,YAAM,KAAK,IAAI,IAAI,IAAI,KAAK;AAC5B,YAAM,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK;AACjD,UAAI,MAAM,GAAG;AACX,eAAO,CAAC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,MAC7B,WAAW,MAAM,GAAG;AAClB,eAAO,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;AAAA,MACvC;AACA,YAAM,eAAe,KAAK,IAAI,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC;AAC3D,UAAI,iBAAiB,GAAG;AACtB,eAAO,CAAC,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC;AAAA,MACxC,WAAW,eAAe,GAAG;AAC3B,eAAO,CAAC,KAAK,IAAI,EAAE,IAAI,KAAK,KAAK,KAAK,YAAY,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,YAAY,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC;AAAA,MACxH;AACA,YAAM,IAAI,KAAK,KAAK,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC,CAAC;AACzC,YAAM,MAAM,KAAK,KAAK,EAAE,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC,CAAC,GAAG;AACnE,YAAM,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC;AAC/B,aAAO,CAAC,IAAI,KAAK,IAAI,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC;AAAA,IACrI;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}