{"version": 3, "sources": ["node_modules/@capacitor/geolocation/dist/esm/web.js"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\nexport class GeolocationWeb extends WebPlugin {\n  async getCurrentPosition(options) {\n    return new Promise((resolve, reject) => {\n      navigator.geolocation.getCurrentPosition(pos => {\n        resolve(pos);\n      }, err => {\n        reject(err);\n      }, Object.assign({\n        enableHighAccuracy: false,\n        timeout: 10000,\n        maximumAge: 0\n      }, options));\n    });\n  }\n  async watchPosition(options, callback) {\n    const id = navigator.geolocation.watchPosition(pos => {\n      callback(pos);\n    }, err => {\n      callback(null, err);\n    }, Object.assign({\n      enableHighAccuracy: false,\n      timeout: 10000,\n      maximumAge: 0,\n      minimumUpdateInterval: 5000\n    }, options));\n    return `${id}`;\n  }\n  async clearWatch(options) {\n    navigator.geolocation.clearWatch(parseInt(options.id, 10));\n  }\n  async checkPermissions() {\n    if (typeof navigator === 'undefined' || !navigator.permissions) {\n      throw this.unavailable('Permissions API not available in this browser');\n    }\n    const permission = await navigator.permissions.query({\n      name: 'geolocation'\n    });\n    return {\n      location: permission.state,\n      coarseLocation: permission.state\n    };\n  }\n  async requestPermissions() {\n    throw this.unimplemented('Not implemented on web.');\n  }\n}\nconst Geolocation = new GeolocationWeb();\nexport { Geolocation };\n"], "mappings": ";;;;;;;;;;AAAA,IACa,gBA8CP;AA/CN;AAAA;AAAA;AACO,IAAM,iBAAN,cAA6B,UAAU;AAAA,MACtC,mBAAmB,SAAS;AAAA;AAChC,iBAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,sBAAU,YAAY,mBAAmB,SAAO;AAC9C,sBAAQ,GAAG;AAAA,YACb,GAAG,SAAO;AACR,qBAAO,GAAG;AAAA,YACZ,GAAG,OAAO,OAAO;AAAA,cACf,oBAAoB;AAAA,cACpB,SAAS;AAAA,cACT,YAAY;AAAA,YACd,GAAG,OAAO,CAAC;AAAA,UACb,CAAC;AAAA,QACH;AAAA;AAAA,MACM,cAAc,SAAS,UAAU;AAAA;AACrC,gBAAM,KAAK,UAAU,YAAY,cAAc,SAAO;AACpD,qBAAS,GAAG;AAAA,UACd,GAAG,SAAO;AACR,qBAAS,MAAM,GAAG;AAAA,UACpB,GAAG,OAAO,OAAO;AAAA,YACf,oBAAoB;AAAA,YACpB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,uBAAuB;AAAA,UACzB,GAAG,OAAO,CAAC;AACX,iBAAO,GAAG,EAAE;AAAA,QACd;AAAA;AAAA,MACM,WAAW,SAAS;AAAA;AACxB,oBAAU,YAAY,WAAW,SAAS,QAAQ,IAAI,EAAE,CAAC;AAAA,QAC3D;AAAA;AAAA,MACM,mBAAmB;AAAA;AACvB,cAAI,OAAO,cAAc,eAAe,CAAC,UAAU,aAAa;AAC9D,kBAAM,KAAK,YAAY,+CAA+C;AAAA,UACxE;AACA,gBAAM,aAAa,MAAM,UAAU,YAAY,MAAM;AAAA,YACnD,MAAM;AAAA,UACR,CAAC;AACD,iBAAO;AAAA,YACL,UAAU,WAAW;AAAA,YACrB,gBAAgB,WAAW;AAAA,UAC7B;AAAA,QACF;AAAA;AAAA,MACM,qBAAqB;AAAA;AACzB,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,IACF;AACA,IAAM,cAAc,IAAI,eAAe;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}