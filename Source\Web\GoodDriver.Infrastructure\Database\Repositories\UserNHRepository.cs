﻿using GoodDriver.Domain.Users;
using Microsoft.Extensions.Configuration;
using NHibernate;
using NHibernate.Linq;
using Rogerio.Data;	
using Rogerio.Data.Specification;
using System.Linq.Expressions;

namespace GoodDriver.Infrastructure.Database.Repositories
{
	public class UserNHRepository : EventBusNHRepository<User>, IUserRepository
    {
        public UserNHRepository(IConfiguration configuration, IUnitOfWorkFactory<ISession> unitOfWorkFactory, IServiceProvider serviceProvider) : base(configuration, unitOfWorkFactory, serviceProvider)
        {
        }

        public async Task<User> GetAsyncBy(Expression<Func<User, bool>> predicate)
        {
            using (var unitOfWork = UnitOfWorkFactory.Get())
            {
                var context = unitOfWork.Context;
                return await
                context.Query<User>()
                       .FirstOrDefaultAsync(predicate);
            }
        }

        public async Task<List<User>> FindAsyncBy(Expression<Func<User, bool>> predicate)
        {
            using (var unitOfWork = UnitOfWorkFactory.Get())
            {
                var context = unitOfWork.Context;
                return await
                context.Query<User>()
                       .Where(predicate)
                       .ToListAsync();
            }
        }

		//public Task<User> GetAsyncById(object id)
		//{
		//	throw new NotImplementedException();
		//}

		//public Task<IList<User>> FindAllAsync()
		//{
		//	throw new NotImplementedException();
		//}

		//public Task UpdateAsync(User model)
		//{
		//	throw new NotImplementedException();
		//}

		//public Task<object> AddAsync(User model)
		//{
		//	throw new NotImplementedException();
		//}

		//public Task DeleteAsync(User model)
		//{
		//	throw new NotImplementedException();
		//}

		//public Task<IList<User>> FindAsyncBy(ISpecification<User> specification)
		//{
		//	throw new NotImplementedException();
		//}
	}
}
