{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-split-pane.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { p as printIonWarning } from './index-cfd9c1f2.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nconst splitPaneIosCss = \":host{--side-width:100%;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;contain:strict}:host(.split-pane-visible) ::slotted(.split-pane-main){left:0;right:0;top:0;bottom:0;position:relative;-ms-flex:1;flex:1;-webkit-box-shadow:none;box-shadow:none;overflow:hidden;z-index:0}::slotted(.split-pane-side:not(ion-menu)){display:none}:host{--border:0.55px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--side-min-width:270px;--side-max-width:28%}\";\nconst IonSplitPaneIosStyle0 = splitPaneIosCss;\nconst splitPaneMdCss = \":host{--side-width:100%;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;contain:strict}:host(.split-pane-visible) ::slotted(.split-pane-main){left:0;right:0;top:0;bottom:0;position:relative;-ms-flex:1;flex:1;-webkit-box-shadow:none;box-shadow:none;overflow:hidden;z-index:0}::slotted(.split-pane-side:not(ion-menu)){display:none}:host{--border:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--side-min-width:270px;--side-max-width:28%}\";\nconst IonSplitPaneMdStyle0 = splitPaneMdCss;\n\n// TODO(FW-2832): types\nconst SPLIT_PANE_MAIN = 'split-pane-main';\nconst SPLIT_PANE_SIDE = 'split-pane-side';\nconst QUERY = {\n  xs: '(min-width: 0px)',\n  sm: '(min-width: 576px)',\n  md: '(min-width: 768px)',\n  lg: '(min-width: 992px)',\n  xl: '(min-width: 1200px)',\n  never: ''\n};\nconst SplitPane = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionSplitPaneVisible = createEvent(this, \"ionSplitPaneVisible\", 7);\n    this.visible = false;\n    this.contentId = undefined;\n    this.disabled = false;\n    this.when = QUERY['lg'];\n  }\n  visibleChanged(visible) {\n    this.ionSplitPaneVisible.emit({\n      visible\n    });\n  }\n  /**\n   * @internal\n   */\n  async isVisible() {\n    return Promise.resolve(this.visible);\n  }\n  async connectedCallback() {\n    // TODO: connectedCallback is fired in CE build\n    // before WC is defined. This needs to be fixed in Stencil.\n    if (typeof customElements !== 'undefined' && customElements != null) {\n      await customElements.whenDefined('ion-split-pane');\n    }\n    this.styleMainElement();\n    this.updateState();\n  }\n  disconnectedCallback() {\n    if (this.rmL) {\n      this.rmL();\n      this.rmL = undefined;\n    }\n  }\n  updateState() {\n    if (this.rmL) {\n      this.rmL();\n      this.rmL = undefined;\n    }\n    // Check if the split-pane is disabled\n    if (this.disabled) {\n      this.visible = false;\n      return;\n    }\n    // When query is a boolean\n    const query = this.when;\n    if (typeof query === 'boolean') {\n      this.visible = query;\n      return;\n    }\n    // When query is a string, let's find first if it is a shortcut\n    const mediaQuery = QUERY[query] || query;\n    // Media query is empty or null, we hide it\n    if (mediaQuery.length === 0) {\n      this.visible = false;\n      return;\n    }\n    // Listen on media query\n    const callback = q => {\n      this.visible = q.matches;\n    };\n    const mediaList = window.matchMedia(mediaQuery);\n    // TODO FW-5869\n    mediaList.addListener(callback);\n    this.rmL = () => mediaList.removeListener(callback);\n    this.visible = mediaList.matches;\n  }\n  /**\n   * Attempt to find the main content\n   * element inside of the split pane.\n   * If found, set it as the main node.\n   *\n   * We assume that the main node\n   * is available in the DOM on split\n   * pane load.\n   */\n  styleMainElement() {\n    const contentId = this.contentId;\n    const children = this.el.children;\n    const nu = this.el.childElementCount;\n    let foundMain = false;\n    for (let i = 0; i < nu; i++) {\n      const child = children[i];\n      const isMain = contentId !== undefined && child.id === contentId;\n      if (isMain) {\n        if (foundMain) {\n          printIonWarning('[ion-split-pane] - Cannot have more than one main node.');\n          return;\n        } else {\n          setPaneClass(child, isMain);\n          foundMain = true;\n        }\n      }\n    }\n    if (!foundMain) {\n      printIonWarning('[ion-split-pane] - Does not have a specified main node.');\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '098801b5a318e2fc6913fb0d9079b1552927b99b',\n      class: {\n        [mode]: true,\n        // Used internally for styling\n        [`split-pane-${mode}`]: true,\n        'split-pane-visible': this.visible\n      }\n    }, h(\"slot\", {\n      key: '8cbc6a942ecba54fc3c62027d46917db067b65c8'\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"visible\": [\"visibleChanged\"],\n      \"disabled\": [\"updateState\"],\n      \"when\": [\"updateState\"]\n    };\n  }\n};\nconst setPaneClass = (el, isMain) => {\n  let toAdd;\n  let toRemove;\n  if (isMain) {\n    toAdd = SPLIT_PANE_MAIN;\n    toRemove = SPLIT_PANE_SIDE;\n  } else {\n    toAdd = SPLIT_PANE_SIDE;\n    toRemove = SPLIT_PANE_MAIN;\n  }\n  const classList = el.classList;\n  classList.add(toAdd);\n  classList.remove(toRemove);\n};\nSplitPane.style = {\n  ios: IonSplitPaneIosStyle0,\n  md: IonSplitPaneMdStyle0\n};\nexport { SplitPane as ion_split_pane };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,IAMM,iBACA,uBACA,gBACA,sBAGA,iBACA,iBACA,OAQA,WA4HA;AAlJN;AAAA;AAGA;AACA;AACA;AACA,IAAM,kBAAkB;AACxB,IAAM,wBAAwB;AAC9B,IAAM,iBAAiB;AACvB,IAAM,uBAAuB;AAG7B,IAAM,kBAAkB;AACxB,IAAM,kBAAkB;AACxB,IAAM,QAAQ;AAAA,MACZ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,OAAO;AAAA,IACT;AACA,IAAM,YAAY,MAAM;AAAA,MACtB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,sBAAsB,YAAY,MAAM,uBAAuB,CAAC;AACrE,aAAK,UAAU;AACf,aAAK,YAAY;AACjB,aAAK,WAAW;AAChB,aAAK,OAAO,MAAM,IAAI;AAAA,MACxB;AAAA,MACA,eAAe,SAAS;AACtB,aAAK,oBAAoB,KAAK;AAAA,UAC5B;AAAA,QACF,CAAC;AAAA,MACH;AAAA;AAAA;AAAA;AAAA,MAIM,YAAY;AAAA;AAChB,iBAAO,QAAQ,QAAQ,KAAK,OAAO;AAAA,QACrC;AAAA;AAAA,MACM,oBAAoB;AAAA;AAGxB,cAAI,OAAO,mBAAmB,eAAe,kBAAkB,MAAM;AACnE,kBAAM,eAAe,YAAY,gBAAgB;AAAA,UACnD;AACA,eAAK,iBAAiB;AACtB,eAAK,YAAY;AAAA,QACnB;AAAA;AAAA,MACA,uBAAuB;AACrB,YAAI,KAAK,KAAK;AACZ,eAAK,IAAI;AACT,eAAK,MAAM;AAAA,QACb;AAAA,MACF;AAAA,MACA,cAAc;AACZ,YAAI,KAAK,KAAK;AACZ,eAAK,IAAI;AACT,eAAK,MAAM;AAAA,QACb;AAEA,YAAI,KAAK,UAAU;AACjB,eAAK,UAAU;AACf;AAAA,QACF;AAEA,cAAM,QAAQ,KAAK;AACnB,YAAI,OAAO,UAAU,WAAW;AAC9B,eAAK,UAAU;AACf;AAAA,QACF;AAEA,cAAM,aAAa,MAAM,KAAK,KAAK;AAEnC,YAAI,WAAW,WAAW,GAAG;AAC3B,eAAK,UAAU;AACf;AAAA,QACF;AAEA,cAAM,WAAW,OAAK;AACpB,eAAK,UAAU,EAAE;AAAA,QACnB;AACA,cAAM,YAAY,OAAO,WAAW,UAAU;AAE9C,kBAAU,YAAY,QAAQ;AAC9B,aAAK,MAAM,MAAM,UAAU,eAAe,QAAQ;AAClD,aAAK,UAAU,UAAU;AAAA,MAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUA,mBAAmB;AACjB,cAAM,YAAY,KAAK;AACvB,cAAM,WAAW,KAAK,GAAG;AACzB,cAAM,KAAK,KAAK,GAAG;AACnB,YAAI,YAAY;AAChB,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,gBAAM,QAAQ,SAAS,CAAC;AACxB,gBAAM,SAAS,cAAc,UAAa,MAAM,OAAO;AACvD,cAAI,QAAQ;AACV,gBAAI,WAAW;AACb,8BAAgB,yDAAyD;AACzE;AAAA,YACF,OAAO;AACL,2BAAa,OAAO,MAAM;AAC1B,0BAAY;AAAA,YACd;AAAA,UACF;AAAA,QACF;AACA,YAAI,CAAC,WAAW;AACd,0BAAgB,yDAAyD;AAAA,QAC3E;AAAA,MACF;AAAA,MACA,SAAS;AACP,cAAM,OAAO,WAAW,IAAI;AAC5B,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,OAAO;AAAA,YACL,CAAC,IAAI,GAAG;AAAA;AAAA,YAER,CAAC,cAAc,IAAI,EAAE,GAAG;AAAA,YACxB,sBAAsB,KAAK;AAAA,UAC7B;AAAA,QACF,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,QACP,CAAC,CAAC;AAAA,MACJ;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,WAAW,CAAC,gBAAgB;AAAA,UAC5B,YAAY,CAAC,aAAa;AAAA,UAC1B,QAAQ,CAAC,aAAa;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,IAAM,eAAe,CAAC,IAAI,WAAW;AACnC,UAAI;AACJ,UAAI;AACJ,UAAI,QAAQ;AACV,gBAAQ;AACR,mBAAW;AAAA,MACb,OAAO;AACL,gBAAQ;AACR,mBAAW;AAAA,MACb;AACA,YAAM,YAAY,GAAG;AACrB,gBAAU,IAAI,KAAK;AACnB,gBAAU,OAAO,QAAQ;AAAA,IAC3B;AACA,cAAU,QAAQ;AAAA,MAChB,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}