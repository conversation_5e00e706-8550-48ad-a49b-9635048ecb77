﻿using GoodDriver.Domain.Users;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Domain.Devices
{
	public class Device
	{
		protected Device() { } // for NHibernate
		protected Device(string id) : this()
        {
			this.Id = id;
			CreatedOn = UpdatedOn = DateTime.Now;
		}

        public Device(string id, User user, string brand, string model, string systemOperational = null) : this(id)
		{
			this.User = user ?? throw new ArgumentNullException(nameof(user));
			this.Brand = brand ?? throw new ArgumentNullException(nameof(brand));
			this.Model = model ?? throw new ArgumentNullException(nameof(model));
			this.SystemOperational = systemOperational;
		}
        

        public string Id { get; private set; }

        public User	User { get; private set; }

		public string Brand { get; private set; }

        public string Model { get; private set; }

        public string SystemOperational { get; private set; }

		public DateTime CreatedOn { get; private set; }

		public DateTime UpdatedOn { get; private set; }
	}
}
