{"version": 3, "sources": ["node_modules/@capacitor/app/dist/esm/definitions.js", "node_modules/@capacitor/app/dist/esm/index.js", "src/app/core/services/auto-journey-detection.service.ts", "src/app/pages/tabs/journeys/journey/journey.page.ts", "src/app/pages/tabs/journeys/journey/journey.page.html"], "sourcesContent": ["export {};\n", "import { registerPlugin } from '@capacitor/core';\nconst App = registerPlugin('App', {\n  web: () => import('./web').then(m => new m.AppWeb())\n});\nexport * from './definitions';\nexport { App };\n", "import { Injectable } from '@angular/core';\nimport { registerPlugin } from '@capacitor/core';\nimport { Motion } from '@capacitor/motion';\nimport { JourneyStorageService } from './journey-storage.service';\nimport { ToastService } from './toast.service';\nimport { BehaviorSubject } from 'rxjs';\nimport { Platform } from '@ionic/angular';\nimport { App } from '@capacitor/app';\nimport { Geolocation } from '@capacitor/geolocation';\n\nimport type {\n  Location,\n  BackgroundGeolocationPlugin\n} from '@capacitor-community/background-geolocation';\n\nconst BackgroundGeolocation = registerPlugin<BackgroundGeolocationPlugin>(\n  'BackgroundGeolocation'\n);\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AutoJourneyDetectionService {\n  // Estado da detecção automática\n  private isEnabled = false;\n  private isRunning = false;\n  private currentJourneyId: string | null = null;\n  private lastLocation: any = null;\n  private movingTimeCounter = 0;\n  private stationaryTimeCounter = 0;\n  private speedThreshold = 10; // km/h\n  private movingTimeThreshold = 60; // segundos\n  private stationaryTimeThreshold = 180; // segundos\n  private checkInterval = 10; // segundos\n  private locationWatcher: any = null;\n  private motionWatcher: any = null;\n  private backgroundWatcher: string | null = null;\n  private intervalId: any = null;\n\n  private detectionEnabledSubject = new BehaviorSubject<boolean>(false);\n  public detectionEnabled$ = this.detectionEnabledSubject.asObservable();\n\n  private journeyActiveSubject = new BehaviorSubject<boolean>(false);\n  public journeyActive$ = this.journeyActiveSubject.asObservable();\n\n  constructor(\n    private journeyService: JourneyStorageService,\n    private toastService: ToastService,\n    private platform: Platform\n  ) {\n    this.platform.ready().then(() => {\n      this.setupAppStateListeners();\n    });\n  }\n\n  private setupAppStateListeners() {\n    App.addListener('appStateChange', ({ isActive }) => {\n      if (!this.isEnabled) return;\n\n      if (isActive) {\n        console.log('App voltou para foreground');\n        this.stopBackgroundMonitoring();\n        if (this.isRunning) {\n          this.startForegroundMonitoring();\n        }\n      } else {\n        console.log('App foi para background');\n        this.stopForegroundMonitoring();\n        if (this.isRunning) {\n          this.startBackgroundMonitoring();\n        }\n      }\n    });\n  }\n\n  public async enableAutoDetection(): Promise<void> {\n    if (this.isEnabled) return;\n\n    try {\n      await this.requestPermissions();\n      this.isEnabled = true;\n      this.detectionEnabledSubject.next(true);\n      this.startDetection();\n      await this.toastService.showToast('Detecção automática de viagens ativada', 'success');\n    } catch (error) {\n      console.error('Erro ao ativar detecção automática:', error);\n      await this.toastService.showToast('Erro ao ativar detecção automática. Verifique as permissões.', 'danger');\n    }\n  }\n\n  public async disableAutoDetection(): Promise<void> {\n    if (!this.isEnabled) return;\n\n    if (this.currentJourneyId) {\n      await this.finishJourney();\n    }\n\n    this.stopDetection();\n    this.isEnabled = false;\n    this.detectionEnabledSubject.next(false);\n\n    await this.toastService.showToast('Detecção automática de viagens desativada', 'success');\n  }\n\n  private startDetection(): void {\n    if (this.isRunning) return;\n\n    this.isRunning = true;\n    this.movingTimeCounter = 0;\n    this.stationaryTimeCounter = 0;\n    this.startForegroundMonitoring();\n  }\n\n  private stopDetection(): void {\n    if (!this.isRunning) return;\n\n    this.stopForegroundMonitoring();\n    this.stopBackgroundMonitoring();\n\n    this.isRunning = false;\n    this.movingTimeCounter = 0;\n    this.stationaryTimeCounter = 0;\n  }\n\n  private startForegroundMonitoring(): void {\n    this.intervalId = setInterval(() => this.checkMovementStatus(), this.checkInterval * 1000);\n    this.setupLocationWatcher();\n    this.setupMotionWatcher();\n  }\n\n  private stopForegroundMonitoring(): void {\n    if (this.intervalId) {\n      clearInterval(this.intervalId);\n      this.intervalId = null;\n    }\n\n    if (this.locationWatcher) {\n      this.locationWatcher.remove();\n      this.locationWatcher = null;\n    }\n\n    if (this.motionWatcher) {\n      this.motionWatcher.remove();\n      this.motionWatcher = null;\n    }\n  }\n\n  private async startBackgroundMonitoring(): Promise<void> {\n    try {\n      const watcherId = await BackgroundGeolocation.addWatcher(\n        {\n          backgroundMessage: \"Monitorando viagem em segundo plano\",\n          backgroundTitle: \"Rastreamento de viagem\",\n          requestPermissions: true,\n          stale: false,\n          distanceFilter: 50,\n          // stopOnTerminate: false,\n          // startOnBoot: true,\n          // notificationChannelName: \"Viagens\",\n          // notificationText: \"Monitorando sua viagem\",\n          // notificationTitle: \"GoodDriver\",\n          // notificationIconColor: \"#0074d9\",\n        },\n        (location: any, error: any) => {\n          if (error) {\n            console.error(\"Erro no background geolocation:\", error);\n            return;\n          }\n\n          if (location) {\n            this.lastLocation = location;\n            this.processLocationUpdate(location);\n          }\n        }\n      );\n\n      this.backgroundWatcher = watcherId;\n    } catch (error) {\n      console.error(\"Erro ao iniciar background geolocation:\", error);\n    }\n  }\n\n  private async stopBackgroundMonitoring(): Promise<void> {\n    if (this.backgroundWatcher) {\n      await BackgroundGeolocation.removeWatcher({\n        id: this.backgroundWatcher\n      });\n      this.backgroundWatcher = null;\n    }\n  }\n\n  private setupLocationWatcher(): void {\n    // Implementar com Geolocation.watchPosition (não incluso aqui)\n  }\n\n  private setupMotionWatcher(): void {\n    this.motionWatcher = Motion.addListener('accel', (event) => {\n      // Processar dados de aceleração\n    });\n  }\n\n  private async checkMovementStatus(): Promise<void> {\n    if (!this.lastLocation) return;\n\n    const speed = this.lastLocation.speed ? this.lastLocation.speed * 3.6 : 0;\n\n    if (speed > this.speedThreshold) {\n      this.movingTimeCounter += this.checkInterval;\n      this.stationaryTimeCounter = 0;\n\n      if (this.movingTimeCounter >= this.movingTimeThreshold && !this.currentJourneyId) {\n        await this.startJourney();\n      }\n    } else {\n      this.stationaryTimeCounter += this.checkInterval;\n      this.movingTimeCounter = 0;\n\n      if (this.stationaryTimeCounter >= this.stationaryTimeThreshold && this.currentJourneyId) {\n        await this.finishJourney();\n      }\n    }\n  }\n\n  private processLocationUpdate(location: any): void {\n    this.lastLocation = location;\n\n    if (this.currentJourneyId) {\n      this.journeyService.addLocation(\n        this.currentJourneyId,\n        location.latitude,\n        location.longitude\n      );\n    }\n  }\n\n  private async startJourney(): Promise<void> {\n    try {\n      const journeyId = await this.journeyService.startJourney();\n      this.currentJourneyId = journeyId;\n      this.journeyActiveSubject.next(true);\n      await this.toastService.showToast('Viagem iniciada automaticamente', 'success');\n    } catch (error) {\n      console.error('Erro ao iniciar viagem automaticamente:', error);\n    }\n  }\n\n  private async finishJourney(): Promise<void> {\n    if (!this.currentJourneyId) return;\n\n    try {\n      await this.journeyService.endJourney(this.currentJourneyId);\n      await this.toastService.showToast('Viagem finalizada automaticamente', 'success');\n      this.currentJourneyId = null;\n      this.journeyActiveSubject.next(false);\n    } catch (error) {\n      console.error('Erro ao finalizar viagem automaticamente:', error);\n    }\n  }\n\n  private async requestPermissions(): Promise<void> {\n    try {\n      const result = await Geolocation.requestPermissions();\n      console.log('Permissões de localização:', result);\n    } catch (error) {\n      console.error('Erro ao solicitar permissões:', error);\n      throw error;\n    }\n  }\n}\n", "import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { Router, RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ToastService } from 'src/app/core/services/toast.service';\nimport { Journey } from 'src/app/core/models/journey.model';\nimport { JourneyStorageService } from 'src/app/core/services/journey-storage.service';\nimport { SessionService } from 'src/app/core/services/session.service';\nimport { AutoJourneyDetectionService } from 'src/app/core/services/auto-journey-detection.service';\nimport { JourneyInfoService } from 'src/app/core/services/journeyinfo.service';\nimport { VehicleService } from 'src/app/core/services/vehicle.service';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-journey',\n  templateUrl: './journey.page.html',\n  styleUrls: ['./journey.page.scss'],\n  standalone: true,\n  imports: [\n    IonicModule,\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    RouterModule\n  ]\n})\nexport class JourneyPage implements OnInit, OnDestroy {\n\n  username: string = 'Usuário';\n  userId: string = '';\n\n  journeys: Journey[] = [];\n  tracking = false;\n  currentJourneyId: string | null = null;\n\n  // Novas propriedades para detecção automática\n  trackingMode: 'manual' | 'auto' = 'manual';\n  autoDetectionActive = false;\n  journeyInProgress = false;\n\n  // Vehicle properties\n  hasVehicles: boolean = false;\n  primaryVehicle: any = null;\n  isLoading: boolean = true;\n\n  // Subscriptions\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private router: Router,\n    private toastService: ToastService,\n    private journeyService: JourneyStorageService,\n    private sessionService: SessionService,\n    private autoDetectionService: AutoJourneyDetectionService,\n    private journeyInfoService: JourneyInfoService,\n    private vehicleService: VehicleService\n  ) {}\n\n  async ngOnInit(): Promise<void> {\n    this.isLoading = true;\n    this.username = await this.sessionService.getUserName() || 'Usuário';\n    this.userId = await this.sessionService.getUserId() || '';\n\n    // Check if the user has any vehicles\n    await this.checkVehicles();\n\n    // Load journeys\n    await this.loadJourneys();\n\n    // Inscrever-se nos observables\n    this.subscriptions.push(\n      this.journeyInfoService.trackingActive$.subscribe(active => {\n        this.tracking = active;\n      })\n    );\n\n    this.subscriptions.push(\n      this.autoDetectionService.detectionEnabled$.subscribe(enabled => {\n        this.autoDetectionActive = enabled;\n      })\n    );\n\n    this.subscriptions.push(\n      this.autoDetectionService.journeyActive$.subscribe(active => {\n        this.journeyInProgress = active;\n      })\n    );\n\n    this.isLoading = false;\n  }\n\n  /**\n   * Checks if the user has any vehicles registered\n   */\n  async checkVehicles() {\n    try {\n      this.hasVehicles = await this.vehicleService.hasVehicles(this.userId);\n\n      if (this.hasVehicles) {\n        this.primaryVehicle = await this.vehicleService.getPrimaryVehicle(this.userId);\n      }\n    } catch (error) {\n      console.error('Error checking vehicles:', error);\n      this.hasVehicles = false;\n    }\n  }\n\n  ngOnDestroy(): void {\n    // Cancelar todas as inscrições\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  /**\n   * Manipula a mudança no modo de rastreamento (manual/automático)\n   */\n  onTrackingModeChange(event: any) {\n    this.trackingMode = event.detail.value;\n\n    // Se mudar para automático e já estiver com detecção ativa, mostrar mensagem\n    if (this.trackingMode === 'auto' && this.autoDetectionActive) {\n      this.toastService.showToast('Detecção automática já está ativa', 'info');\n    }\n\n    // Se mudar para manual e estiver com uma viagem manual em andamento, manter\n    if (this.trackingMode === 'manual' && this.tracking) {\n      this.toastService.showToast('Viagem manual em andamento', 'info');\n    }\n  }\n\n  /**\n   * Ativa/desativa a detecção automática de viagens\n   */\n  async toggleAutoDetection() {\n    try {\n      if (this.autoDetectionActive) {\n        await this.autoDetectionService.enableAutoDetection();\n      } else {\n        await this.autoDetectionService.disableAutoDetection();\n      }\n    } catch (error) {\n      console.error('Erro ao alternar detecção automática:', error);\n      // Reverter o toggle em caso de erro\n      this.autoDetectionActive = !this.autoDetectionActive;\n    }\n  }\n\n  /**\n   * Inicia uma viagem manualmente\n   */\n  async startTracking() {\n    // Check if the user has any vehicles\n    if (!this.hasVehicles) {\n      await this.toastService.showToast('Você precisa cadastrar um veículo antes de iniciar uma viagem.', 'warning');\n      this.router.navigate(['/new-vehicle']);\n      return;\n    }\n\n    try {\n      const id = await this.journeyService.startJourney();\n\n      if (!id) {\n        await this.toastService.showToast('Não foi possível iniciar a viagem. Verifique se você tem um veículo cadastrado.', 'warning');\n        return;\n      }\n\n      this.currentJourneyId = id;\n      await this.toastService.showToast('Viagem iniciada.', 'success');\n    }\n    catch (error) {\n      console.error(error);\n      await this.toastService.showToast('Erro ao iniciar viagem.', 'danger');\n    }\n  }\n\n  /**\n   * Finaliza uma viagem manualmente\n   */\n  async stopTracking() {\n    if (!this.currentJourneyId) {\n      await this.toastService.showToast('Nenhuma viagem em andamento.');\n      return;\n    }\n\n    try {\n      await this.journeyService.endJourney(this.currentJourneyId);\n      this.currentJourneyId = null;\n      await this.loadJourneys();\n      await this.toastService.showToast('Viagem finalizada.');\n    } catch (error) {\n      console.error(error);\n      await this.toastService.showToast('Erro ao finalizar viagem.');\n    }\n  }\n\n  /**\n   * Carrega as viagens do usuário\n   */\n  async loadJourneys() {\n    this.journeys = await this.journeyService.getAllJourneysByUser(this.userId);\n  }\n\n  /**\n   * Realiza logout\n   */\n  logout() {\n    // Desativar detecção automática antes de fazer logout\n    if (this.autoDetectionActive) {\n      this.autoDetectionService.disableAutoDetection();\n    }\n\n    this.sessionService.clearSession();\n    console.log('Logout realizado');\n    this.router.navigate(['/login']);\n  }\n\n}\n", "<ion-content [fullscreen]=\"true\" class=\"ion-padding\">\n  <!-- Indicador de carregamento -->\n  <div *ngIf=\"isLoading\" class=\"ion-text-center ion-padding\">\n    <ion-spinner></ion-spinner>\n  </div>\n\n  <div *ngIf=\"!isLoading\">\n    <ion-card>\n      <ion-card-header>\n        <ion-card-title class=\"ion-text-center\">Bem-vindo, {{username}}!</ion-card-title>\n      </ion-card-header>\n    </ion-card>\n\n    <!-- Alerta de veículo não cadastrado -->\n    <ion-card *ngIf=\"!hasVehicles\" color=\"warning\">\n      <ion-card-header>\n        <ion-card-title>\n          <ion-icon name=\"warning-outline\"></ion-icon>\n          Atenção\n        </ion-card-title>\n      </ion-card-header>\n      <ion-card-content>\n        <p>Você ainda não possui nenhum veículo cadastrado. Para utilizar o rastreamento de viagens, é necessário cadastrar pelo menos um veículo.</p>\n        <ion-button expand=\"block\" color=\"primary\" routerLink=\"/new-vehicle\" class=\"ion-margin-top\">\n          <ion-icon name=\"car-outline\" slot=\"start\"></ion-icon>\n          Cadastrar Veículo\n        </ion-button>\n      </ion-card-content>\n    </ion-card>\n\n  <ion-card>\n    <ion-card-header>\n      <ion-card-title>Rastreamento de Viagens</ion-card-title>\n    </ion-card-header>\n    <ion-card-content>\n      <ion-segment [(ngModel)]=\"trackingMode\" (ionChange)=\"onTrackingModeChange($event)\">\n        <ion-segment-button value=\"manual\">\n          <ion-label>Manual</ion-label>\n        </ion-segment-button>\n        <ion-segment-button value=\"auto\">\n          <ion-label>Automático</ion-label>\n        </ion-segment-button>\n      </ion-segment>\n\n      <!-- Modo Manual -->\n      <div *ngIf=\"trackingMode === 'manual'\" class=\"tracking-controls\">\n        <p class=\"description\">Inicie e finalize suas viagens manualmente.</p>\n\n        <ion-button expand=\"full\" color=\"success\" (click)=\"startTracking()\" [disabled]=\"tracking || autoDetectionActive\">\n          <ion-icon name=\"play\" slot=\"start\"></ion-icon>\n          Iniciar Viagem\n        </ion-button>\n\n        <ion-button expand=\"full\" color=\"danger\" (click)=\"stopTracking()\" [disabled]=\"!tracking\">\n          <ion-icon name=\"stop\" slot=\"start\"></ion-icon>\n          Finalizar Viagem\n        </ion-button>\n      </div>\n\n      <!-- Modo Automático -->\n      <div *ngIf=\"trackingMode === 'auto'\" class=\"tracking-controls\">\n        <p class=\"description\">O aplicativo detectará automaticamente quando você iniciar e finalizar uma viagem.</p>\n\n        <ion-item lines=\"none\" class=\"status-item\">\n          <ion-icon [name]=\"autoDetectionActive ? 'checkmark-circle' : 'close-circle'\"\n                   [color]=\"autoDetectionActive ? 'success' : 'medium'\"\n                   slot=\"start\"></ion-icon>\n          <ion-label>\n            <h2>Detecção Automática</h2>\n            <p>{{ autoDetectionActive ? 'Ativada' : 'Desativada' }}</p>\n          </ion-label>\n          <ion-toggle [(ngModel)]=\"autoDetectionActive\" (ionChange)=\"toggleAutoDetection()\"\n                     [disabled]=\"tracking\"></ion-toggle>\n        </ion-item>\n\n        <ion-item lines=\"none\" class=\"status-item\" *ngIf=\"autoDetectionActive\">\n          <ion-icon [name]=\"journeyInProgress ? 'car' : 'car-outline'\"\n                   [color]=\"journeyInProgress ? 'primary' : 'medium'\"\n                   slot=\"start\"></ion-icon>\n          <ion-label>\n            <h2>Status</h2>\n            <p>{{ journeyInProgress ? 'Viagem em andamento' : 'Aguardando movimento' }}</p>\n          </ion-label>\n        </ion-item>\n      </div>\n    </ion-card-content>\n  </ion-card>\n\n  <ion-list>\n    <ion-list-header>\n      Viagens Registradas\n    </ion-list-header>\n\n    <div *ngIf=\"journeys?.length === 0\" class=\"ion-text-center\">\n      <p>Nenhuma viagem registrada.</p>\n      <p>Inicie uma viagem para começar a registrar.</p>\n    </div>\n\n    <div *ngIf=\"journeys?.length && journeys.length > 0\" class=\"ion-text-center\">\n      <p>Você já registrou {{ journeys.length }} viagens.</p>\n      <p>Toque em uma viagem para ver mais detalhes.</p>\n    </div>\n\n    <ion-item *ngFor=\"let journey of journeys\" [routerLink]=\"['/journey-details', journey.id]\" [queryParams]=\"{ data: journey | json }\">\n      <ion-label>\n        <h2>Início: {{ journey.startDate | date:'short' }}</h2>\n        <p>Fim: {{ journey.endDate ? (journey.endDate | date:'short') : 'Em andamento' }}</p>\n        <p>Distância: {{ journey.distance | number:'1.2-2' }} km</p>\n      </ion-label>\n    </ion-item>\n  </ion-list>\n  </div>\n</ion-content>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IACM;AADN,IAAAA,YAAA;AAAA;AAAA;AAAA;AAIA;AAHA,IAAM,MAAM,eAAe,OAAO;AAAA,MAChC,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,OAAO,CAAC;AAAA,IACrD,CAAC;AAAA;AAAA;;;ACHD,IAeM,uBAOO;AAtBb;;;;AACA;AACA,IAAAC;AAGA;AAEA,IAAAA;AACA,IAAAA;;;;;AAOA,IAAM,wBAAwB,eAC5B,uBAAuB;AAMnB,IAAO,+BAAP,MAAO,6BAA2B;MAuBtC,YACU,gBACA,cACA,UAAkB;AAFlB,aAAA,iBAAA;AACA,aAAA,eAAA;AACA,aAAA,WAAA;AAxBF,aAAA,YAAY;AACZ,aAAA,YAAY;AACZ,aAAA,mBAAkC;AAClC,aAAA,eAAoB;AACpB,aAAA,oBAAoB;AACpB,aAAA,wBAAwB;AACxB,aAAA,iBAAiB;AACjB,aAAA,sBAAsB;AACtB,aAAA,0BAA0B;AAC1B,aAAA,gBAAgB;AAChB,aAAA,kBAAuB;AACvB,aAAA,gBAAqB;AACrB,aAAA,oBAAmC;AACnC,aAAA,aAAkB;AAElB,aAAA,0BAA0B,IAAI,gBAAyB,KAAK;AAC7D,aAAA,oBAAoB,KAAK,wBAAwB,aAAY;AAE5D,aAAA,uBAAuB,IAAI,gBAAyB,KAAK;AAC1D,aAAA,iBAAiB,KAAK,qBAAqB,aAAY;AAO5D,aAAK,SAAS,MAAK,EAAG,KAAK,MAAK;AAC9B,eAAK,uBAAsB;QAC7B,CAAC;MACH;MAEQ,yBAAsB;AAC5B,YAAI,YAAY,kBAAkB,CAAC,EAAE,SAAQ,MAAM;AACjD,cAAI,CAAC,KAAK;AAAW;AAErB,cAAI,UAAU;AACZ,oBAAQ,IAAI,4BAA4B;AACxC,iBAAK,yBAAwB;AAC7B,gBAAI,KAAK,WAAW;AAClB,mBAAK,0BAAyB;YAChC;UACF,OAAO;AACL,oBAAQ,IAAI,yBAAyB;AACrC,iBAAK,yBAAwB;AAC7B,gBAAI,KAAK,WAAW;AAClB,mBAAK,0BAAyB;YAChC;UACF;QACF,CAAC;MACH;MAEa,sBAAmB;;AAC9B,cAAI,KAAK;AAAW;AAEpB,cAAI;AACF,kBAAM,KAAK,mBAAkB;AAC7B,iBAAK,YAAY;AACjB,iBAAK,wBAAwB,KAAK,IAAI;AACtC,iBAAK,eAAc;AACnB,kBAAM,KAAK,aAAa,UAAU,mDAA0C,SAAS;UACvF,SAAS,OAAO;AACd,oBAAQ,MAAM,gDAAuC,KAAK;AAC1D,kBAAM,KAAK,aAAa,UAAU,4EAAgE,QAAQ;UAC5G;QACF;;MAEa,uBAAoB;;AAC/B,cAAI,CAAC,KAAK;AAAW;AAErB,cAAI,KAAK,kBAAkB;AACzB,kBAAM,KAAK,cAAa;UAC1B;AAEA,eAAK,cAAa;AAClB,eAAK,YAAY;AACjB,eAAK,wBAAwB,KAAK,KAAK;AAEvC,gBAAM,KAAK,aAAa,UAAU,sDAA6C,SAAS;QAC1F;;MAEQ,iBAAc;AACpB,YAAI,KAAK;AAAW;AAEpB,aAAK,YAAY;AACjB,aAAK,oBAAoB;AACzB,aAAK,wBAAwB;AAC7B,aAAK,0BAAyB;MAChC;MAEQ,gBAAa;AACnB,YAAI,CAAC,KAAK;AAAW;AAErB,aAAK,yBAAwB;AAC7B,aAAK,yBAAwB;AAE7B,aAAK,YAAY;AACjB,aAAK,oBAAoB;AACzB,aAAK,wBAAwB;MAC/B;MAEQ,4BAAyB;AAC/B,aAAK,aAAa,YAAY,MAAM,KAAK,oBAAmB,GAAI,KAAK,gBAAgB,GAAI;AACzF,aAAK,qBAAoB;AACzB,aAAK,mBAAkB;MACzB;MAEQ,2BAAwB;AAC9B,YAAI,KAAK,YAAY;AACnB,wBAAc,KAAK,UAAU;AAC7B,eAAK,aAAa;QACpB;AAEA,YAAI,KAAK,iBAAiB;AACxB,eAAK,gBAAgB,OAAM;AAC3B,eAAK,kBAAkB;QACzB;AAEA,YAAI,KAAK,eAAe;AACtB,eAAK,cAAc,OAAM;AACzB,eAAK,gBAAgB;QACvB;MACF;MAEc,4BAAyB;;AACrC,cAAI;AACF,kBAAM,YAAY,MAAM,sBAAsB,WAC5C;cACE,mBAAmB;cACnB,iBAAiB;cACjB,oBAAoB;cACpB,OAAO;cACP,gBAAgB;;;;;;;eAQlB,CAAC,UAAe,UAAc;AAC5B,kBAAI,OAAO;AACT,wBAAQ,MAAM,mCAAmC,KAAK;AACtD;cACF;AAEA,kBAAI,UAAU;AACZ,qBAAK,eAAe;AACpB,qBAAK,sBAAsB,QAAQ;cACrC;YACF,CAAC;AAGH,iBAAK,oBAAoB;UAC3B,SAAS,OAAO;AACd,oBAAQ,MAAM,2CAA2C,KAAK;UAChE;QACF;;MAEc,2BAAwB;;AACpC,cAAI,KAAK,mBAAmB;AAC1B,kBAAM,sBAAsB,cAAc;cACxC,IAAI,KAAK;aACV;AACD,iBAAK,oBAAoB;UAC3B;QACF;;MAEQ,uBAAoB;MAE5B;MAEQ,qBAAkB;AACxB,aAAK,gBAAgB,OAAO,YAAY,SAAS,CAAC,UAAS;QAE3D,CAAC;MACH;MAEc,sBAAmB;;AAC/B,cAAI,CAAC,KAAK;AAAc;AAExB,gBAAM,QAAQ,KAAK,aAAa,QAAQ,KAAK,aAAa,QAAQ,MAAM;AAExE,cAAI,QAAQ,KAAK,gBAAgB;AAC/B,iBAAK,qBAAqB,KAAK;AAC/B,iBAAK,wBAAwB;AAE7B,gBAAI,KAAK,qBAAqB,KAAK,uBAAuB,CAAC,KAAK,kBAAkB;AAChF,oBAAM,KAAK,aAAY;YACzB;UACF,OAAO;AACL,iBAAK,yBAAyB,KAAK;AACnC,iBAAK,oBAAoB;AAEzB,gBAAI,KAAK,yBAAyB,KAAK,2BAA2B,KAAK,kBAAkB;AACvF,oBAAM,KAAK,cAAa;YAC1B;UACF;QACF;;MAEQ,sBAAsB,UAAa;AACzC,aAAK,eAAe;AAEpB,YAAI,KAAK,kBAAkB;AACzB,eAAK,eAAe,YAClB,KAAK,kBACL,SAAS,UACT,SAAS,SAAS;QAEtB;MACF;MAEc,eAAY;;AACxB,cAAI;AACF,kBAAM,YAAY,MAAM,KAAK,eAAe,aAAY;AACxD,iBAAK,mBAAmB;AACxB,iBAAK,qBAAqB,KAAK,IAAI;AACnC,kBAAM,KAAK,aAAa,UAAU,mCAAmC,SAAS;UAChF,SAAS,OAAO;AACd,oBAAQ,MAAM,2CAA2C,KAAK;UAChE;QACF;;MAEc,gBAAa;;AACzB,cAAI,CAAC,KAAK;AAAkB;AAE5B,cAAI;AACF,kBAAM,KAAK,eAAe,WAAW,KAAK,gBAAgB;AAC1D,kBAAM,KAAK,aAAa,UAAU,qCAAqC,SAAS;AAChF,iBAAK,mBAAmB;AACxB,iBAAK,qBAAqB,KAAK,KAAK;UACtC,SAAS,OAAO;AACd,oBAAQ,MAAM,6CAA6C,KAAK;UAClE;QACF;;MAEc,qBAAkB;;AAC9B,cAAI;AACF,kBAAM,SAAS,MAAM,YAAY,mBAAkB;AACnD,oBAAQ,IAAI,uCAA8B,MAAM;UAClD,SAAS,OAAO;AACd,oBAAQ,MAAM,oCAAiC,KAAK;AACpD,kBAAM;UACR;QACF;;;;uCArPW,8BAA2B,mBAAA,qBAAA,GAAA,mBAAA,YAAA,GAAA,mBAAA,QAAA,CAAA;IAAA;oGAA3B,8BAA2B,SAA3B,6BAA2B,WAAA,YAF1B,OAAM,CAAA;AAEd,IAAO,8BAAP;;0EAAO,6BAA2B,CAAA;cAHvC;eAAW;UACV,YAAY;SACb;;;;;;;;;AEnBC,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,oBAAA,GAAA,aAAA;AACF,IAAA,uBAAA;;;;;AAUE,IAAA,yBAAA,GAAA,YAAA,EAAA,EAA+C,GAAA,iBAAA,EAC5B,GAAA,gBAAA;AAEb,IAAA,oBAAA,GAAA,YAAA,EAAA;AACA,IAAA,iBAAA,GAAA,iBAAA;AACF,IAAA,uBAAA,EAAiB;AAEnB,IAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,GAAA;AACb,IAAA,iBAAA,GAAA,2JAAA;AAAuI,IAAA,uBAAA;AAC1I,IAAA,yBAAA,GAAA,cAAA,EAAA;AACE,IAAA,oBAAA,GAAA,YAAA,EAAA;AACA,IAAA,iBAAA,IAAA,wBAAA;AACF,IAAA,uBAAA,EAAa,EACI;;;;;;AAkBnB,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiE,GAAA,KAAA,EAAA;AACxC,IAAA,iBAAA,GAAA,6CAAA;AAA2C,IAAA,uBAAA;AAElE,IAAA,yBAAA,GAAA,cAAA,EAAA;AAA0C,IAAA,qBAAA,SAAA,SAAA,gEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,cAAA,CAAe;IAAA,CAAA;AAChE,IAAA,oBAAA,GAAA,YAAA,EAAA;AACA,IAAA,iBAAA,GAAA,kBAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,cAAA,EAAA;AAAyC,IAAA,qBAAA,SAAA,SAAA,gEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,CAAc;IAAA,CAAA;AAC9D,IAAA,oBAAA,GAAA,YAAA,EAAA;AACA,IAAA,iBAAA,GAAA,oBAAA;AACF,IAAA,uBAAA,EAAa;;;;AARuD,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,YAAA,OAAA,mBAAA;AAKF,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,CAAA,OAAA,QAAA;;;;;AAsBlE,IAAA,yBAAA,GAAA,YAAA,EAAA;AACE,IAAA,oBAAA,GAAA,YAAA,EAAA;AAGA,IAAA,yBAAA,GAAA,WAAA,EAAW,GAAA,IAAA;AACL,IAAA,iBAAA,GAAA,QAAA;AAAM,IAAA,uBAAA;AACV,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,CAAA;AAAwE,IAAA,uBAAA,EAAI,EACrE;;;;AANF,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,oBAAA,QAAA,aAAA,EAAkD,SAAA,OAAA,oBAAA,YAAA,QAAA;AAKvD,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,oBAAA,wBAAA,sBAAA;;;;;;AArBT,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA+D,GAAA,KAAA,EAAA;AACtC,IAAA,iBAAA,GAAA,0FAAA;AAAkF,IAAA,uBAAA;AAEzG,IAAA,yBAAA,GAAA,YAAA,EAAA;AACE,IAAA,oBAAA,GAAA,YAAA,EAAA;AAGA,IAAA,yBAAA,GAAA,WAAA,EAAW,GAAA,IAAA;AACL,IAAA,iBAAA,GAAA,8BAAA;AAAmB,IAAA,uBAAA;AACvB,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,CAAA;AAAoD,IAAA,uBAAA,EAAI;AAE7D,IAAA,yBAAA,IAAA,cAAA,EAAA;AAAY,IAAA,2BAAA,iBAAA,SAAA,uEAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,MAAA,6BAAA,OAAA,qBAAA,MAAA,MAAA,OAAA,sBAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAAkC,IAAA,qBAAA,aAAA,SAAA,qEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAa,OAAA,oBAAA,CAAqB;IAAA,CAAA;AAC/C,IAAA,uBAAA,EAAa;AAGhD,IAAA,qBAAA,IAAA,+CAAA,GAAA,GAAA,YAAA,EAAA;AASF,IAAA,uBAAA;;;;AApBc,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,sBAAA,qBAAA,cAAA,EAAkE,SAAA,OAAA,sBAAA,YAAA,QAAA;AAKvE,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,sBAAA,YAAA,YAAA;AAEO,IAAA,oBAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,mBAAA;AACD,IAAA,qBAAA,YAAA,OAAA,QAAA;AAG+B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,mBAAA;;;;;AAkBhD,IAAA,yBAAA,GAAA,OAAA,CAAA,EAA4D,GAAA,GAAA;AACvD,IAAA,iBAAA,GAAA,4BAAA;AAA0B,IAAA,uBAAA;AAC7B,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,gDAAA;AAA2C,IAAA,uBAAA,EAAI;;;;;AAGpD,IAAA,yBAAA,GAAA,OAAA,CAAA,EAA6E,GAAA,GAAA;AACxE,IAAA,iBAAA,CAAA;AAAgD,IAAA,uBAAA;AACnD,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,6CAAA;AAA2C,IAAA,uBAAA,EAAI;;;;AAD/C,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,4BAAA,OAAA,SAAA,QAAA,WAAA;;;;;AAIL,IAAA,yBAAA,GAAA,YAAA,EAAA;;AACE,IAAA,yBAAA,GAAA,WAAA,EAAW,GAAA,IAAA;AACL,IAAA,iBAAA,CAAA;;AAA8C,IAAA,uBAAA;AAClD,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,CAAA;;AAA8E,IAAA,uBAAA;AACjF,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,EAAA;;AAAqD,IAAA,uBAAA,EAAI,EAClD;;;;AAL6B,IAAA,qBAAA,cAAA,0BAAA,IAAA,KAAA,WAAA,EAAA,CAAA,EAA+C,eAAA,0BAAA,IAAA,KAAA,sBAAA,GAAA,GAAA,UAAA,CAAA,CAAA;AAElF,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,eAAA,sBAAA,GAAA,GAAA,WAAA,WAAA,OAAA,GAAA,EAAA;AACD,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,SAAA,WAAA,UAAA,sBAAA,GAAA,IAAA,WAAA,SAAA,OAAA,IAAA,gBAAA,EAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,kBAAA,sBAAA,IAAA,IAAA,WAAA,UAAA,OAAA,GAAA,KAAA;;;;;;AArGT,IAAA,yBAAA,GAAA,KAAA,EAAwB,GAAA,UAAA,EACZ,GAAA,iBAAA,EACS,GAAA,kBAAA,CAAA;AACyB,IAAA,iBAAA,CAAA;AAAwB,IAAA,uBAAA,EAAiB,EACjE;AAIpB,IAAA,qBAAA,GAAA,uCAAA,IAAA,GAAA,YAAA,CAAA;AAgBF,IAAA,yBAAA,GAAA,UAAA,EAAU,GAAA,iBAAA,EACS,GAAA,gBAAA;AACC,IAAA,iBAAA,GAAA,yBAAA;AAAuB,IAAA,uBAAA,EAAiB;AAE1D,IAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,eAAA,CAAA;AACH,IAAA,2BAAA,iBAAA,SAAA,iEAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,cAAA,MAAA,MAAA,OAAA,eAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAA2B,IAAA,qBAAA,aAAA,SAAA,6DAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAa,OAAA,qBAAA,MAAA,CAA4B;IAAA,CAAA;AAC/E,IAAA,yBAAA,IAAA,sBAAA,CAAA,EAAmC,IAAA,WAAA;AACtB,IAAA,iBAAA,IAAA,QAAA;AAAM,IAAA,uBAAA,EAAY;AAE/B,IAAA,yBAAA,IAAA,sBAAA,CAAA,EAAiC,IAAA,WAAA;AACpB,IAAA,iBAAA,IAAA,eAAA;AAAU,IAAA,uBAAA,EAAY,EACd;AAIvB,IAAA,qBAAA,IAAA,mCAAA,GAAA,GAAA,OAAA,CAAA,EAAiE,IAAA,mCAAA,IAAA,GAAA,OAAA,CAAA;AAwCnE,IAAA,uBAAA,EAAmB;AAGrB,IAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,iBAAA;AAEN,IAAA,iBAAA,IAAA,uBAAA;AACF,IAAA,uBAAA;AAEA,IAAA,qBAAA,IAAA,mCAAA,GAAA,GAAA,OAAA,EAAA,EAA4D,IAAA,mCAAA,GAAA,GAAA,OAAA,EAAA,EAKiB,IAAA,wCAAA,IAAA,IAAA,YAAA,EAAA;AAY/E,IAAA,uBAAA,EAAW;;;;AArGmC,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,eAAA,OAAA,UAAA,GAAA;AAKjC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,OAAA,WAAA;AAqBI,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,YAAA;AAUP,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,iBAAA,QAAA;AAeA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,iBAAA,MAAA;AAiCF,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,SAAA,OAAA,YAAA,OAAA,OAAA,OAAA,SAAA,YAAA,CAAA;AAKA,IAAA,oBAAA;AAAA,IAAA,qBAAA,SAAA,OAAA,YAAA,OAAA,OAAA,OAAA,SAAA,WAAA,OAAA,SAAA,SAAA,CAAA;AAKwB,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,QAAA;;;ADvGlC,cA2Ba;AA3Bb;;;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AAuBM,IAAO,eAAP,MAAO,aAAW;MAsBtB,YACU,QACA,cACA,gBACA,gBACA,sBACA,oBACA,gBAA8B;AAN9B,aAAA,SAAA;AACA,aAAA,eAAA;AACA,aAAA,iBAAA;AACA,aAAA,iBAAA;AACA,aAAA,uBAAA;AACA,aAAA,qBAAA;AACA,aAAA,iBAAA;AA3BV,aAAA,WAAmB;AACnB,aAAA,SAAiB;AAEjB,aAAA,WAAsB,CAAA;AACtB,aAAA,WAAW;AACX,aAAA,mBAAkC;AAGlC,aAAA,eAAkC;AAClC,aAAA,sBAAsB;AACtB,aAAA,oBAAoB;AAGpB,aAAA,cAAuB;AACvB,aAAA,iBAAsB;AACtB,aAAA,YAAqB;AAGb,aAAA,gBAAgC,CAAA;MAUrC;MAEG,WAAQ;;AACZ,eAAK,YAAY;AACjB,eAAK,YAAW,MAAM,KAAK,eAAe,YAAW,MAAM;AAC3D,eAAK,UAAS,MAAM,KAAK,eAAe,UAAS,MAAM;AAGvD,gBAAM,KAAK,cAAa;AAGxB,gBAAM,KAAK,aAAY;AAGvB,eAAK,cAAc,KACjB,KAAK,mBAAmB,gBAAgB,UAAU,YAAS;AACzD,iBAAK,WAAW;UAClB,CAAC,CAAC;AAGJ,eAAK,cAAc,KACjB,KAAK,qBAAqB,kBAAkB,UAAU,aAAU;AAC9D,iBAAK,sBAAsB;UAC7B,CAAC,CAAC;AAGJ,eAAK,cAAc,KACjB,KAAK,qBAAqB,eAAe,UAAU,YAAS;AAC1D,iBAAK,oBAAoB;UAC3B,CAAC,CAAC;AAGJ,eAAK,YAAY;QACnB;;;;;MAKM,gBAAa;;AACjB,cAAI;AACF,iBAAK,cAAc,MAAM,KAAK,eAAe,YAAY,KAAK,MAAM;AAEpE,gBAAI,KAAK,aAAa;AACpB,mBAAK,iBAAiB,MAAM,KAAK,eAAe,kBAAkB,KAAK,MAAM;YAC/E;UACF,SAAS,OAAO;AACd,oBAAQ,MAAM,4BAA4B,KAAK;AAC/C,iBAAK,cAAc;UACrB;QACF;;MAEA,cAAW;AAET,aAAK,cAAc,QAAQ,SAAO,IAAI,YAAW,CAAE;MACrD;;;;MAKA,qBAAqB,OAAU;AAC7B,aAAK,eAAe,MAAM,OAAO;AAGjC,YAAI,KAAK,iBAAiB,UAAU,KAAK,qBAAqB;AAC5D,eAAK,aAAa,UAAU,oDAAqC,MAAM;QACzE;AAGA,YAAI,KAAK,iBAAiB,YAAY,KAAK,UAAU;AACnD,eAAK,aAAa,UAAU,8BAA8B,MAAM;QAClE;MACF;;;;MAKM,sBAAmB;;AACvB,cAAI;AACF,gBAAI,KAAK,qBAAqB;AAC5B,oBAAM,KAAK,qBAAqB,oBAAmB;YACrD,OAAO;AACL,oBAAM,KAAK,qBAAqB,qBAAoB;YACtD;UACF,SAAS,OAAO;AACd,oBAAQ,MAAM,kDAAyC,KAAK;AAE5D,iBAAK,sBAAsB,CAAC,KAAK;UACnC;QACF;;;;;MAKM,gBAAa;;AAEjB,cAAI,CAAC,KAAK,aAAa;AACrB,kBAAM,KAAK,aAAa,UAAU,wEAAkE,SAAS;AAC7G,iBAAK,OAAO,SAAS,CAAC,cAAc,CAAC;AACrC;UACF;AAEA,cAAI;AACF,kBAAM,KAAK,MAAM,KAAK,eAAe,aAAY;AAEjD,gBAAI,CAAC,IAAI;AACP,oBAAM,KAAK,aAAa,UAAU,+FAAmF,SAAS;AAC9H;YACF;AAEA,iBAAK,mBAAmB;AACxB,kBAAM,KAAK,aAAa,UAAU,oBAAoB,SAAS;UACjE,SACO,OAAO;AACZ,oBAAQ,MAAM,KAAK;AACnB,kBAAM,KAAK,aAAa,UAAU,2BAA2B,QAAQ;UACvE;QACF;;;;;MAKM,eAAY;;AAChB,cAAI,CAAC,KAAK,kBAAkB;AAC1B,kBAAM,KAAK,aAAa,UAAU,8BAA8B;AAChE;UACF;AAEA,cAAI;AACF,kBAAM,KAAK,eAAe,WAAW,KAAK,gBAAgB;AAC1D,iBAAK,mBAAmB;AACxB,kBAAM,KAAK,aAAY;AACvB,kBAAM,KAAK,aAAa,UAAU,oBAAoB;UACxD,SAAS,OAAO;AACd,oBAAQ,MAAM,KAAK;AACnB,kBAAM,KAAK,aAAa,UAAU,2BAA2B;UAC/D;QACF;;;;;MAKM,eAAY;;AAChB,eAAK,WAAW,MAAM,KAAK,eAAe,qBAAqB,KAAK,MAAM;QAC5E;;;;;MAKA,SAAM;AAEJ,YAAI,KAAK,qBAAqB;AAC5B,eAAK,qBAAqB,qBAAoB;QAChD;AAEA,aAAK,eAAe,aAAY;AAChC,gBAAQ,IAAI,kBAAkB;AAC9B,aAAK,OAAO,SAAS,CAAC,QAAQ,CAAC;MACjC;;;uCA3LW,cAAW,4BAAA,MAAA,GAAA,4BAAA,YAAA,GAAA,4BAAA,qBAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,2BAAA,GAAA,4BAAA,kBAAA,GAAA,4BAAA,cAAA,CAAA;IAAA;iFAAX,cAAW,WAAA,CAAA,CAAA,aAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,eAAA,GAAA,YAAA,GAAA,CAAA,SAAA,+BAAA,GAAA,MAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,mBAAA,aAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,SAAA,WAAA,GAAA,MAAA,GAAA,CAAA,GAAA,iBAAA,aAAA,SAAA,GAAA,CAAA,SAAA,QAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,eAAA,GAAA,SAAA,SAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,QAAA,iBAAA,GAAA,CAAA,UAAA,SAAA,SAAA,WAAA,cAAA,gBAAA,GAAA,gBAAA,GAAA,CAAA,QAAA,eAAA,QAAA,OAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,UAAA,QAAA,SAAA,WAAA,GAAA,SAAA,UAAA,GAAA,CAAA,QAAA,QAAA,QAAA,OAAA,GAAA,CAAA,UAAA,QAAA,SAAA,UAAA,GAAA,SAAA,UAAA,GAAA,CAAA,QAAA,QAAA,QAAA,OAAA,GAAA,CAAA,SAAA,QAAA,GAAA,aAAA,GAAA,CAAA,QAAA,SAAA,GAAA,QAAA,OAAA,GAAA,CAAA,GAAA,iBAAA,aAAA,WAAA,UAAA,GAAA,CAAA,SAAA,QAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,aAAA,CAAA,GAAA,UAAA,SAAA,qBAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;AC3BxB,QAAA,yBAAA,GAAA,eAAA,CAAA;AAEE,QAAA,qBAAA,GAAA,4BAAA,GAAA,GAAA,OAAA,CAAA,EAA2D,GAAA,4BAAA,IAAA,GAAA,OAAA,CAAA;AA8G7D,QAAA,uBAAA;;;AAhHa,QAAA,qBAAA,cAAA,IAAA;AAEL,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,SAAA;AAIA,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,CAAA,IAAA,SAAA;;;MDcJ;MAAW;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MACX;MAAY;MAAA;MAAA;MAAA;MAAA;MACZ;MAAW;MAAA;MACX;MACA;MAAY;IAAA,GAAA,QAAA,CAAA,o4BAAA,EAAA,CAAA;AAGV,IAAO,cAAP;;0EAAO,aAAW,CAAA;cAbvB;2BACW,eAAa,YAGX,MAAI,SACP;UACP;UACA;UACA;UACA;UACA;WACD,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iBAAA,QAAA,CAAA,0zBAAA,EAAA,CAAA;;;;iFAEU,aAAW,EAAA,WAAA,eAAA,UAAA,uDAAA,YAAA,GAAA,CAAA;IAAA,GAAA;;;", "names": ["init_esm", "init_esm"], "x_google_ignoreList": [0, 1]}