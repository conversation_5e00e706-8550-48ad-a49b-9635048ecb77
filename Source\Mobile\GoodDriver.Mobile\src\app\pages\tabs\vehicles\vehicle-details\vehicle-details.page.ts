import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { Vehicle } from 'src/app/core/models/vehicle.model';
import { VehicleService } from 'src/app/core/services/vehicle.service';
import { ToastService } from 'src/app/core/services/toast.service';
import { SessionService } from 'src/app/core/services/session.service';
import { VehicleStateService } from 'src/app/core/services/vehicle-state.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-vehicle-details',
  templateUrl: './vehicle-details.page.html',
  styleUrls: ['./vehicle-details.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule]
})
export class VehicleDetailsPage implements OnInit, OnDestroy {
  vehicleId: string = '';
  vehicle: Vehicle | null = null;
  isLoading = true;

  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private vehicleService: VehicleService,
    private toastService: ToastService,
    private sessionService: SessionService,
    private vehicleStateService: VehicleStateService
  ) {}

  ngOnInit() {
    this.vehicleId = this.route.snapshot.paramMap.get('id') || '';
    this.loadVehicleDetails();

    // Subscribe to primary vehicle changes
    this.subscriptions.push(
      this.vehicleStateService.primaryVehicle$.subscribe(primaryVehicle => {
        if (primaryVehicle && this.vehicle && primaryVehicle.id === this.vehicle.id) {
          this.vehicle.isPrimary = primaryVehicle.isPrimary;
        }
      })
    );
  }

  ngOnDestroy() {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  async loadVehicleDetails() {
    if (!this.vehicleId) {
      this.toastService.showToast('ID do veículo não encontrado', 'danger');
      this.router.navigate(['/tabs/vehicles']);
      return;
    }

    this.isLoading = true;
    try {
      const userId = await this.sessionService.getUserId() || '';
      const vehicles = await this.vehicleService.listAllLocal(userId);
      this.vehicle = vehicles.find(v => v.id === this.vehicleId) || null;

      if (!this.vehicle) {
        this.toastService.showToast('Veículo não encontrado', 'danger');
        this.router.navigate(['/tabs/vehicles']);
      }
    } catch (error) {
      console.error('Error loading vehicle details:', error);
      this.toastService.showToast('Erro ao carregar detalhes do veículo', 'danger');
    } finally {
      this.isLoading = false;
    }
  }

  goBack() {
    this.router.navigate(['/tabs/vehicles']);
  }

  /**
   * Sets this vehicle as the primary vehicle
   */
  async setPrimaryVehicle() {
    if (!this.vehicle) return;

    const userId = await this.sessionService.getUserId() || '';
    try {
      const success = await this.vehicleService.setPrimaryVehicle(this.vehicle.id, userId);
      if (success) {
        this.toastService.showToast('Veículo definido como principal');
        // Update the vehicle object to reflect the change
        this.vehicle.isPrimary = true;

        // The VehicleService will update the VehicleStateService,
        // which will notify all subscribers about the change
      } else {
        this.toastService.showToast('Erro ao definir veículo como principal', 'danger');
      }
    } catch (error) {
      console.error('Error setting primary vehicle:', error);
      this.toastService.showToast('Erro ao definir veículo como principal', 'danger');
    }
  }
}
