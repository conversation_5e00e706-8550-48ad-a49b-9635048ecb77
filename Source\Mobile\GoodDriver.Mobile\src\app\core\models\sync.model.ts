/**
 * Enum representing the synchronization status of a record
 */
export enum SyncStatus {
    /** Record exists only locally and needs to be created on the server */
    PendingCreate = 'PENDING_CREATE',
  
    /** Record exists locally and on the server but has local changes that need to be sent to the server */
    PendingUpdate = 'PENDING_UPDATE',
  
    /** Record exists locally and on the server but needs to be deleted on the server */
    PendingDelete = 'PENDING_DELETE',
  
    /** Record is synchronized with the server */
    Synced = 'SYNCED',
  
    /** Record failed to synchronize with the server */
    Failed = 'FAILED'
  }