import {
  getIonPageElement,
  init_index_68c0d151
} from "./chunk-AY6ZGCP5.js";
import {
  createAnimation,
  init_animation_8b25e105
} from "./chunk-YY7NCLAA.js";
import {
  init_index_527b9e34
} from "./chunk-V6QEVD67.js";
import {
  init_helpers_d94bc8ad
} from "./chunk-WNPNB2PX.js";
import {
  init_index_a5d50daf
} from "./chunk-WX4TOLMF.js";
import {
  init_index_cfd9c1f2
} from "./chunk-6SXGDLUH.js";
import {
  __esm
} from "./chunk-4W6HR7MY.js";

// node_modules/@ionic/core/dist/esm/md.transition-30ce8d1b.js
var mdTransitionAnimation;
var init_md_transition_30ce8d1b = __esm({
  "node_modules/@ionic/core/dist/esm/md.transition-30ce8d1b.js"() {
    init_animation_8b25e105();
    init_index_68c0d151();
    init_index_cfd9c1f2();
    init_index_a5d50daf();
    init_index_527b9e34();
    init_helpers_d94bc8ad();
    mdTransitionAnimation = (_, opts) => {
      var _a, _b, _c;
      const OFF_BOTTOM = "40px";
      const CENTER = "0px";
      const backDirection = opts.direction === "back";
      const enteringEl = opts.enteringEl;
      const leavingEl = opts.leavingEl;
      const ionPageElement = getIonPageElement(enteringEl);
      const enteringToolbarEle = ionPageElement.querySelector("ion-toolbar");
      const rootTransition = createAnimation();
      rootTransition.addElement(ionPageElement).fill("both").beforeRemoveClass("ion-page-invisible");
      if (backDirection) {
        rootTransition.duration(((_a = opts.duration) !== null && _a !== void 0 ? _a : 0) || 200).easing("cubic-bezier(0.47,0,0.745,0.715)");
      } else {
        rootTransition.duration(((_b = opts.duration) !== null && _b !== void 0 ? _b : 0) || 280).easing("cubic-bezier(0.36,0.66,0.04,1)").fromTo("transform", `translateY(${OFF_BOTTOM})`, `translateY(${CENTER})`).fromTo("opacity", 0.01, 1);
      }
      if (enteringToolbarEle) {
        const enteringToolBar = createAnimation();
        enteringToolBar.addElement(enteringToolbarEle);
        rootTransition.addAnimation(enteringToolBar);
      }
      if (leavingEl && backDirection) {
        rootTransition.duration(((_c = opts.duration) !== null && _c !== void 0 ? _c : 0) || 200).easing("cubic-bezier(0.47,0,0.745,0.715)");
        const leavingPage = createAnimation();
        leavingPage.addElement(getIonPageElement(leavingEl)).onFinish((currentStep) => {
          if (currentStep === 1 && leavingPage.elements.length > 0) {
            leavingPage.elements[0].style.setProperty("display", "none");
          }
        }).fromTo("transform", `translateY(${CENTER})`, `translateY(${OFF_BOTTOM})`).fromTo("opacity", 1, 0);
        rootTransition.addAnimation(leavingPage);
      }
      return rootTransition;
    };
  }
});

export {
  mdTransitionAnimation,
  init_md_transition_30ce8d1b
};
/*! Bundled license information:

@ionic/core/dist/esm/md.transition-30ce8d1b.js:
  (*!
   * (C) Ionic http://ionicframework.com - MIT License
   *)
*/
//# sourceMappingURL=chunk-GJIVKQBF.js.map
