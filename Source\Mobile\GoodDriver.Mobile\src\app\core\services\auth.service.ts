import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { LoginRequestDto } from '../dtos/auth/login-requestDto';
import { Observable } from 'rxjs';
import { LoginResponseDto } from '../dtos/auth/login-responseDto';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class AuthService
{
    constructor(
      private http: HttpClient,
      private apiService: ApiService
    ) {}

  login(data: LoginRequestDto): Observable<LoginResponseDto> {
    const url = this.apiService.getUrl('Authentication');
    return this.http.post<LoginResponseDto>(url, data);
  }
}
