{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/swipe-back-0184f6b3.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { j as clamp } from './helpers-d94bc8ad.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { createGesture } from './index-39782642.js';\nimport './index-cfd9c1f2.js';\nimport './gesture-controller-314a54f6.js';\nconst createSwipeBackGesture = (el, canStartHandler, onStartHandler, onMoveHandler, onEndHandler) => {\n  const win = el.ownerDocument.defaultView;\n  let rtl = isRTL(el);\n  /**\n   * Determine if a gesture is near the edge\n   * of the screen. If true, then the swipe\n   * to go back gesture should proceed.\n   */\n  const isAtEdge = detail => {\n    const threshold = 50;\n    const {\n      startX\n    } = detail;\n    if (rtl) {\n      return startX >= win.innerWidth - threshold;\n    }\n    return startX <= threshold;\n  };\n  const getDeltaX = detail => {\n    return rtl ? -detail.deltaX : detail.deltaX;\n  };\n  const getVelocityX = detail => {\n    return rtl ? -detail.velocityX : detail.velocityX;\n  };\n  const canStart = detail => {\n    /**\n     * The user's locale can change mid-session,\n     * so we need to check text direction at\n     * the beginning of every gesture.\n     */\n    rtl = isRTL(el);\n    return isAtEdge(detail) && canStartHandler();\n  };\n  const onMove = detail => {\n    // set the transition animation's progress\n    const delta = getDeltaX(detail);\n    const stepValue = delta / win.innerWidth;\n    onMoveHandler(stepValue);\n  };\n  const onEnd = detail => {\n    // the swipe back gesture has ended\n    const delta = getDeltaX(detail);\n    const width = win.innerWidth;\n    const stepValue = delta / width;\n    const velocity = getVelocityX(detail);\n    const z = width / 2.0;\n    const shouldComplete = velocity >= 0 && (velocity > 0.2 || delta > z);\n    const missing = shouldComplete ? 1 - stepValue : stepValue;\n    const missingDistance = missing * width;\n    let realDur = 0;\n    if (missingDistance > 5) {\n      const dur = missingDistance / Math.abs(velocity);\n      realDur = Math.min(dur, 540);\n    }\n    onEndHandler(shouldComplete, stepValue <= 0 ? 0.01 : clamp(0, stepValue, 0.9999), realDur);\n  };\n  return createGesture({\n    el,\n    gestureName: 'goback-swipe',\n    /**\n     * Swipe to go back should have priority over other horizontal swipe\n     * gestures. These gestures have a priority of 100 which is why 101 was chosen here.\n     */\n    gesturePriority: 101,\n    threshold: 10,\n    canStart,\n    onStart: onStartHandler,\n    onMove,\n    onEnd\n  });\n};\nexport { createSwipeBackGesture };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAQM;AARN;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA,IAAM,yBAAyB,CAAC,IAAI,iBAAiB,gBAAgB,eAAe,iBAAiB;AACnG,YAAM,MAAM,GAAG,cAAc;AAC7B,UAAI,MAAM,MAAM,EAAE;AAMlB,YAAM,WAAW,YAAU;AACzB,cAAM,YAAY;AAClB,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,YAAI,KAAK;AACP,iBAAO,UAAU,IAAI,aAAa;AAAA,QACpC;AACA,eAAO,UAAU;AAAA,MACnB;AACA,YAAM,YAAY,YAAU;AAC1B,eAAO,MAAM,CAAC,OAAO,SAAS,OAAO;AAAA,MACvC;AACA,YAAM,eAAe,YAAU;AAC7B,eAAO,MAAM,CAAC,OAAO,YAAY,OAAO;AAAA,MAC1C;AACA,YAAM,WAAW,YAAU;AAMzB,cAAM,MAAM,EAAE;AACd,eAAO,SAAS,MAAM,KAAK,gBAAgB;AAAA,MAC7C;AACA,YAAM,SAAS,YAAU;AAEvB,cAAM,QAAQ,UAAU,MAAM;AAC9B,cAAM,YAAY,QAAQ,IAAI;AAC9B,sBAAc,SAAS;AAAA,MACzB;AACA,YAAM,QAAQ,YAAU;AAEtB,cAAM,QAAQ,UAAU,MAAM;AAC9B,cAAM,QAAQ,IAAI;AAClB,cAAM,YAAY,QAAQ;AAC1B,cAAM,WAAW,aAAa,MAAM;AACpC,cAAM,IAAI,QAAQ;AAClB,cAAM,iBAAiB,YAAY,MAAM,WAAW,OAAO,QAAQ;AACnE,cAAM,UAAU,iBAAiB,IAAI,YAAY;AACjD,cAAM,kBAAkB,UAAU;AAClC,YAAI,UAAU;AACd,YAAI,kBAAkB,GAAG;AACvB,gBAAM,MAAM,kBAAkB,KAAK,IAAI,QAAQ;AAC/C,oBAAU,KAAK,IAAI,KAAK,GAAG;AAAA,QAC7B;AACA,qBAAa,gBAAgB,aAAa,IAAI,OAAO,MAAM,GAAG,WAAW,MAAM,GAAG,OAAO;AAAA,MAC3F;AACA,aAAO,cAAc;AAAA,QACnB;AAAA,QACA,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,QAKb,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX;AAAA,QACA,SAAS;AAAA,QACT;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}