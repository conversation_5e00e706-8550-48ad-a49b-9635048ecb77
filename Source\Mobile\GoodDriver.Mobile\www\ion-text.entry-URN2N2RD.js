import {
  createColorClasses,
  init_theme_01f3f29c
} from "./chunk-CHFZ6R2P.js";
import {
  getIonMode,
  init_ionic_global_b26f573e
} from "./chunk-XPKADKFH.js";
import {
  Host,
  h,
  init_index_527b9e34,
  registerInstance
} from "./chunk-V6QEVD67.js";
import {
  init_index_cfd9c1f2
} from "./chunk-6SXGDLUH.js";
import {
  __esm
} from "./chunk-4W6HR7MY.js";

// node_modules/@ionic/core/dist/esm/ion-text.entry.js
var textCss, IonTextStyle0, Text;
var init_ion_text_entry = __esm({
  "node_modules/@ionic/core/dist/esm/ion-text.entry.js"() {
    init_index_527b9e34();
    init_theme_01f3f29c();
    init_ionic_global_b26f573e();
    init_index_cfd9c1f2();
    textCss = ":host(.ion-color){color:var(--ion-color-base)}";
    IonTextStyle0 = textCss;
    Text = class {
      constructor(hostRef) {
        registerInstance(this, hostRef);
        this.color = void 0;
      }
      render() {
        const mode = getIonMode(this);
        return h(Host, {
          key: "0c2546ea3f24b0a6bfd606199441d0a4edfa4ca1",
          class: createColorClasses(this.color, {
            [mode]: true
          })
        }, h("slot", {
          key: "b7623ccb06f9461090a1f33e9f85886c7a4d5eff"
        }));
      }
    };
    Text.style = IonTextStyle0;
  }
});
init_ion_text_entry();
export {
  Text as ion_text
};
/*! Bundled license information:

@ionic/core/dist/esm/ion-text.entry.js:
  (*!
   * (C) Ionic http://ionicframework.com - MIT License
   *)
*/
//# sourceMappingURL=ion-text.entry-URN2N2RD.js.map
