﻿using GoodDriver.ReadModels.Brands.Result;
using Rogerio.Cqrs.Requests;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static GoodDriver.ReadModels.Brands.Result.BrandListResult;

namespace GoodDriver.ReadModels.Models._2_Result
{
    public class ModelListResult : List<ModelListResult.ModelListItemResult>, IRequestResult
    {
        public ModelListResult()
        {
            
        }

        public ModelListResult(IList<ModelListItemResult> models)
        {
            if (models != null && models.Count > 0)
            {
                this.AddRange(models);
            }
        }


        public class ModelListItemResult
        {
            public int Id { get; private set; }

            public string Name { get; private set; }

            public string ReferenceCode { get; private set; }

            public string ReferenceMonth { get; private set; }

            public string ShortName { get; private set; }

            public int BrandId { get; private set; }

            public DateTime CreatedOn { get; private set; }

            public DateTime? DeletedOn { get; private set; }
        }

    }
}
