﻿using GoodDriver.ReadModels.Brands._0_Handler;
using GoodDriver.ReadModels.Brands.Queries;
using GoodDriver.ReadModels.Brands.Result;
using GoodDriver.ReadModels.Models._0_Handler;
using GoodDriver.ReadModels.Models._1_Query;
using GoodDriver.ReadModels.Models._2_Result;
using GoodDriver.ReadModels.Vehicles._0_Handler;
using GoodDriver.ReadModels.Vehicles._1_Query;
using GoodDriver.ReadModels.Vehicles._2_Result;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Rogerio.Cqrs.Bus;
using Rogerio.Cqrs.Requests;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Infrastructure.Installers
{
    public static class QueryInstaller
    {
        public static void Install(IServiceCollection services)
        {
            services.AddSingleton<BrandQueryHandler>();
            services.AddSingleton<ModelQueryHandler>();
            services.AddSingleton<VehicleQueryHandler>();

            services.AddSingleton<IRequestBus>(a =>
            {
                var container = a.GetService<MemoryContainerBus>();

                //Brand
                var brandQueryHandler = a.GetService<BrandQueryHandler>();
                container.Register<BrandListQuery, BrandListResult>(brandQueryHandler);

                //Model
                var modelQueryHandler = a.GetService<ModelQueryHandler>();
                container.Register<ModelListQuery, ModelListResult>(modelQueryHandler);

                //Vehicle
                var vehicleQueryHandler = a.GetService<VehicleQueryHandler>();
                container.Register<ListVehiclesByUserIdQuery, ListVehiclesByUserIdResult>(vehicleQueryHandler);

                return container;
            });
        }
    }
}
