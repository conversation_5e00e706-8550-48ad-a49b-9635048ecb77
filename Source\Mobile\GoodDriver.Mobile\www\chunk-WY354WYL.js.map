{"version": 3, "sources": ["node_modules/@capacitor/core/dist/index.js"], "sourcesContent": ["/*! Capacitor: https://capacitorjs.com/ - MIT License */\nvar ExceptionCode;\n(function (ExceptionCode) {\n  /**\n   * API is not implemented.\n   *\n   * This usually means the API can't be used because it is not implemented for\n   * the current platform.\n   */\n  ExceptionCode[\"Unimplemented\"] = \"UNIMPLEMENTED\";\n  /**\n   * API is not available.\n   *\n   * This means the API can't be used right now because:\n   *   - it is currently missing a prerequisite, such as network connectivity\n   *   - it requires a particular platform or browser version\n   */\n  ExceptionCode[\"Unavailable\"] = \"UNAVAILABLE\";\n})(ExceptionCode || (ExceptionCode = {}));\nclass CapacitorException extends Error {\n  constructor(message, code, data) {\n    super(message);\n    this.message = message;\n    this.code = code;\n    this.data = data;\n  }\n}\nconst getPlatformId = win => {\n  var _a, _b;\n  if (win === null || win === void 0 ? void 0 : win.androidBridge) {\n    return 'android';\n  } else if ((_b = (_a = win === null || win === void 0 ? void 0 : win.webkit) === null || _a === void 0 ? void 0 : _a.messageHandlers) === null || _b === void 0 ? void 0 : _b.bridge) {\n    return 'ios';\n  } else {\n    return 'web';\n  }\n};\nconst createCapacitor = win => {\n  const capCustomPlatform = win.CapacitorCustomPlatform || null;\n  const cap = win.Capacitor || {};\n  const Plugins = cap.Plugins = cap.Plugins || {};\n  const getPlatform = () => {\n    return capCustomPlatform !== null ? capCustomPlatform.name : getPlatformId(win);\n  };\n  const isNativePlatform = () => getPlatform() !== 'web';\n  const isPluginAvailable = pluginName => {\n    const plugin = registeredPlugins.get(pluginName);\n    if (plugin === null || plugin === void 0 ? void 0 : plugin.platforms.has(getPlatform())) {\n      // JS implementation available for the current platform.\n      return true;\n    }\n    if (getPluginHeader(pluginName)) {\n      // Native implementation available.\n      return true;\n    }\n    return false;\n  };\n  const getPluginHeader = pluginName => {\n    var _a;\n    return (_a = cap.PluginHeaders) === null || _a === void 0 ? void 0 : _a.find(h => h.name === pluginName);\n  };\n  const handleError = err => win.console.error(err);\n  const registeredPlugins = new Map();\n  const registerPlugin = (pluginName, jsImplementations = {}) => {\n    const registeredPlugin = registeredPlugins.get(pluginName);\n    if (registeredPlugin) {\n      console.warn(`Capacitor plugin \"${pluginName}\" already registered. Cannot register plugins twice.`);\n      return registeredPlugin.proxy;\n    }\n    const platform = getPlatform();\n    const pluginHeader = getPluginHeader(pluginName);\n    let jsImplementation;\n    const loadPluginImplementation = async () => {\n      if (!jsImplementation && platform in jsImplementations) {\n        jsImplementation = typeof jsImplementations[platform] === 'function' ? jsImplementation = await jsImplementations[platform]() : jsImplementation = jsImplementations[platform];\n      } else if (capCustomPlatform !== null && !jsImplementation && 'web' in jsImplementations) {\n        jsImplementation = typeof jsImplementations['web'] === 'function' ? jsImplementation = await jsImplementations['web']() : jsImplementation = jsImplementations['web'];\n      }\n      return jsImplementation;\n    };\n    const createPluginMethod = (impl, prop) => {\n      var _a, _b;\n      if (pluginHeader) {\n        const methodHeader = pluginHeader === null || pluginHeader === void 0 ? void 0 : pluginHeader.methods.find(m => prop === m.name);\n        if (methodHeader) {\n          if (methodHeader.rtype === 'promise') {\n            return options => cap.nativePromise(pluginName, prop.toString(), options);\n          } else {\n            return (options, callback) => cap.nativeCallback(pluginName, prop.toString(), options, callback);\n          }\n        } else if (impl) {\n          return (_a = impl[prop]) === null || _a === void 0 ? void 0 : _a.bind(impl);\n        }\n      } else if (impl) {\n        return (_b = impl[prop]) === null || _b === void 0 ? void 0 : _b.bind(impl);\n      } else {\n        throw new CapacitorException(`\"${pluginName}\" plugin is not implemented on ${platform}`, ExceptionCode.Unimplemented);\n      }\n    };\n    const createPluginMethodWrapper = prop => {\n      let remove;\n      const wrapper = (...args) => {\n        const p = loadPluginImplementation().then(impl => {\n          const fn = createPluginMethod(impl, prop);\n          if (fn) {\n            const p = fn(...args);\n            remove = p === null || p === void 0 ? void 0 : p.remove;\n            return p;\n          } else {\n            throw new CapacitorException(`\"${pluginName}.${prop}()\" is not implemented on ${platform}`, ExceptionCode.Unimplemented);\n          }\n        });\n        if (prop === 'addListener') {\n          p.remove = async () => remove();\n        }\n        return p;\n      };\n      // Some flair ✨\n      wrapper.toString = () => `${prop.toString()}() { [capacitor code] }`;\n      Object.defineProperty(wrapper, 'name', {\n        value: prop,\n        writable: false,\n        configurable: false\n      });\n      return wrapper;\n    };\n    const addListener = createPluginMethodWrapper('addListener');\n    const removeListener = createPluginMethodWrapper('removeListener');\n    const addListenerNative = (eventName, callback) => {\n      const call = addListener({\n        eventName\n      }, callback);\n      const remove = async () => {\n        const callbackId = await call;\n        removeListener({\n          eventName,\n          callbackId\n        }, callback);\n      };\n      const p = new Promise(resolve => call.then(() => resolve({\n        remove\n      })));\n      p.remove = async () => {\n        console.warn(`Using addListener() without 'await' is deprecated.`);\n        await remove();\n      };\n      return p;\n    };\n    const proxy = new Proxy({}, {\n      get(_, prop) {\n        switch (prop) {\n          // https://github.com/facebook/react/issues/20030\n          case '$$typeof':\n            return undefined;\n          case 'toJSON':\n            return () => ({});\n          case 'addListener':\n            return pluginHeader ? addListenerNative : addListener;\n          case 'removeListener':\n            return removeListener;\n          default:\n            return createPluginMethodWrapper(prop);\n        }\n      }\n    });\n    Plugins[pluginName] = proxy;\n    registeredPlugins.set(pluginName, {\n      name: pluginName,\n      proxy,\n      platforms: new Set([...Object.keys(jsImplementations), ...(pluginHeader ? [platform] : [])])\n    });\n    return proxy;\n  };\n  // Add in convertFileSrc for web, it will already be available in native context\n  if (!cap.convertFileSrc) {\n    cap.convertFileSrc = filePath => filePath;\n  }\n  cap.getPlatform = getPlatform;\n  cap.handleError = handleError;\n  cap.isNativePlatform = isNativePlatform;\n  cap.isPluginAvailable = isPluginAvailable;\n  cap.registerPlugin = registerPlugin;\n  cap.Exception = CapacitorException;\n  cap.DEBUG = !!cap.DEBUG;\n  cap.isLoggingEnabled = !!cap.isLoggingEnabled;\n  return cap;\n};\nconst initCapacitorGlobal = win => win.Capacitor = createCapacitor(win);\nconst Capacitor = /*#__PURE__*/initCapacitorGlobal(typeof globalThis !== 'undefined' ? globalThis : typeof self !== 'undefined' ? self : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : {});\nconst registerPlugin = Capacitor.registerPlugin;\n\n/**\n * Base class web plugins should extend.\n */\nclass WebPlugin {\n  constructor() {\n    this.listeners = {};\n    this.retainedEventArguments = {};\n    this.windowListeners = {};\n  }\n  addListener(eventName, listenerFunc) {\n    let firstListener = false;\n    const listeners = this.listeners[eventName];\n    if (!listeners) {\n      this.listeners[eventName] = [];\n      firstListener = true;\n    }\n    this.listeners[eventName].push(listenerFunc);\n    // If we haven't added a window listener for this event and it requires one,\n    // go ahead and add it\n    const windowListener = this.windowListeners[eventName];\n    if (windowListener && !windowListener.registered) {\n      this.addWindowListener(windowListener);\n    }\n    if (firstListener) {\n      this.sendRetainedArgumentsForEvent(eventName);\n    }\n    const remove = async () => this.removeListener(eventName, listenerFunc);\n    const p = Promise.resolve({\n      remove\n    });\n    return p;\n  }\n  async removeAllListeners() {\n    this.listeners = {};\n    for (const listener in this.windowListeners) {\n      this.removeWindowListener(this.windowListeners[listener]);\n    }\n    this.windowListeners = {};\n  }\n  notifyListeners(eventName, data, retainUntilConsumed) {\n    const listeners = this.listeners[eventName];\n    if (!listeners) {\n      if (retainUntilConsumed) {\n        let args = this.retainedEventArguments[eventName];\n        if (!args) {\n          args = [];\n        }\n        args.push(data);\n        this.retainedEventArguments[eventName] = args;\n      }\n      return;\n    }\n    listeners.forEach(listener => listener(data));\n  }\n  hasListeners(eventName) {\n    var _a;\n    return !!((_a = this.listeners[eventName]) === null || _a === void 0 ? void 0 : _a.length);\n  }\n  registerWindowListener(windowEventName, pluginEventName) {\n    this.windowListeners[pluginEventName] = {\n      registered: false,\n      windowEventName,\n      pluginEventName,\n      handler: event => {\n        this.notifyListeners(pluginEventName, event);\n      }\n    };\n  }\n  unimplemented(msg = 'not implemented') {\n    return new Capacitor.Exception(msg, ExceptionCode.Unimplemented);\n  }\n  unavailable(msg = 'not available') {\n    return new Capacitor.Exception(msg, ExceptionCode.Unavailable);\n  }\n  async removeListener(eventName, listenerFunc) {\n    const listeners = this.listeners[eventName];\n    if (!listeners) {\n      return;\n    }\n    const index = listeners.indexOf(listenerFunc);\n    this.listeners[eventName].splice(index, 1);\n    // If there are no more listeners for this type of event,\n    // remove the window listener\n    if (!this.listeners[eventName].length) {\n      this.removeWindowListener(this.windowListeners[eventName]);\n    }\n  }\n  addWindowListener(handle) {\n    window.addEventListener(handle.windowEventName, handle.handler);\n    handle.registered = true;\n  }\n  removeWindowListener(handle) {\n    if (!handle) {\n      return;\n    }\n    window.removeEventListener(handle.windowEventName, handle.handler);\n    handle.registered = false;\n  }\n  sendRetainedArgumentsForEvent(eventName) {\n    const args = this.retainedEventArguments[eventName];\n    if (!args) {\n      return;\n    }\n    delete this.retainedEventArguments[eventName];\n    args.forEach(arg => {\n      this.notifyListeners(eventName, arg);\n    });\n  }\n}\nconst WebView = /*#__PURE__*/registerPlugin('WebView');\n/******** END WEB VIEW PLUGIN ********/\n/******** COOKIES PLUGIN ********/\n/**\n * Safely web encode a string value (inspired by js-cookie)\n * @param str The string value to encode\n */\nconst encode = str => encodeURIComponent(str).replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent).replace(/[()]/g, escape);\n/**\n * Safely web decode a string value (inspired by js-cookie)\n * @param str The string value to decode\n */\nconst decode = str => str.replace(/(%[\\dA-F]{2})+/gi, decodeURIComponent);\nclass CapacitorCookiesPluginWeb extends WebPlugin {\n  async getCookies() {\n    const cookies = document.cookie;\n    const cookieMap = {};\n    cookies.split(';').forEach(cookie => {\n      if (cookie.length <= 0) return;\n      // Replace first \"=\" with CAP_COOKIE to prevent splitting on additional \"=\"\n      let [key, value] = cookie.replace(/=/, 'CAP_COOKIE').split('CAP_COOKIE');\n      key = decode(key).trim();\n      value = decode(value).trim();\n      cookieMap[key] = value;\n    });\n    return cookieMap;\n  }\n  async setCookie(options) {\n    try {\n      // Safely Encoded Key/Value\n      const encodedKey = encode(options.key);\n      const encodedValue = encode(options.value);\n      // Clean & sanitize options\n      const expires = `; expires=${(options.expires || '').replace('expires=', '')}`; // Default is \"; expires=\"\n      const path = (options.path || '/').replace('path=', ''); // Default is \"path=/\"\n      const domain = options.url != null && options.url.length > 0 ? `domain=${options.url}` : '';\n      document.cookie = `${encodedKey}=${encodedValue || ''}${expires}; path=${path}; ${domain};`;\n    } catch (error) {\n      return Promise.reject(error);\n    }\n  }\n  async deleteCookie(options) {\n    try {\n      document.cookie = `${options.key}=; Max-Age=0`;\n    } catch (error) {\n      return Promise.reject(error);\n    }\n  }\n  async clearCookies() {\n    try {\n      const cookies = document.cookie.split(';') || [];\n      for (const cookie of cookies) {\n        document.cookie = cookie.replace(/^ +/, '').replace(/=.*/, `=;expires=${new Date().toUTCString()};path=/`);\n      }\n    } catch (error) {\n      return Promise.reject(error);\n    }\n  }\n  async clearAllCookies() {\n    try {\n      await this.clearCookies();\n    } catch (error) {\n      return Promise.reject(error);\n    }\n  }\n}\nconst CapacitorCookies = registerPlugin('CapacitorCookies', {\n  web: () => new CapacitorCookiesPluginWeb()\n});\n// UTILITY FUNCTIONS\n/**\n * Read in a Blob value and return it as a base64 string\n * @param blob The blob value to convert to a base64 string\n */\nconst readBlobAsBase64 = async blob => new Promise((resolve, reject) => {\n  const reader = new FileReader();\n  reader.onload = () => {\n    const base64String = reader.result;\n    // remove prefix \"data:application/pdf;base64,\"\n    resolve(base64String.indexOf(',') >= 0 ? base64String.split(',')[1] : base64String);\n  };\n  reader.onerror = error => reject(error);\n  reader.readAsDataURL(blob);\n});\n/**\n * Normalize an HttpHeaders map by lowercasing all of the values\n * @param headers The HttpHeaders object to normalize\n */\nconst normalizeHttpHeaders = (headers = {}) => {\n  const originalKeys = Object.keys(headers);\n  const loweredKeys = Object.keys(headers).map(k => k.toLocaleLowerCase());\n  const normalized = loweredKeys.reduce((acc, key, index) => {\n    acc[key] = headers[originalKeys[index]];\n    return acc;\n  }, {});\n  return normalized;\n};\n/**\n * Builds a string of url parameters that\n * @param params A map of url parameters\n * @param shouldEncode true if you should encodeURIComponent() the values (true by default)\n */\nconst buildUrlParams = (params, shouldEncode = true) => {\n  if (!params) return null;\n  const output = Object.entries(params).reduce((accumulator, entry) => {\n    const [key, value] = entry;\n    let encodedValue;\n    let item;\n    if (Array.isArray(value)) {\n      item = '';\n      value.forEach(str => {\n        encodedValue = shouldEncode ? encodeURIComponent(str) : str;\n        item += `${key}=${encodedValue}&`;\n      });\n      // last character will always be \"&\" so slice it off\n      item.slice(0, -1);\n    } else {\n      encodedValue = shouldEncode ? encodeURIComponent(value) : value;\n      item = `${key}=${encodedValue}`;\n    }\n    return `${accumulator}&${item}`;\n  }, '');\n  // Remove initial \"&\" from the reduce\n  return output.substr(1);\n};\n/**\n * Build the RequestInit object based on the options passed into the initial request\n * @param options The Http plugin options\n * @param extra Any extra RequestInit values\n */\nconst buildRequestInit = (options, extra = {}) => {\n  const output = Object.assign({\n    method: options.method || 'GET',\n    headers: options.headers\n  }, extra);\n  // Get the content-type\n  const headers = normalizeHttpHeaders(options.headers);\n  const type = headers['content-type'] || '';\n  // If body is already a string, then pass it through as-is.\n  if (typeof options.data === 'string') {\n    output.body = options.data;\n  }\n  // Build request initializers based off of content-type\n  else if (type.includes('application/x-www-form-urlencoded')) {\n    const params = new URLSearchParams();\n    for (const [key, value] of Object.entries(options.data || {})) {\n      params.set(key, value);\n    }\n    output.body = params.toString();\n  } else if (type.includes('multipart/form-data') || options.data instanceof FormData) {\n    const form = new FormData();\n    if (options.data instanceof FormData) {\n      options.data.forEach((value, key) => {\n        form.append(key, value);\n      });\n    } else {\n      for (const key of Object.keys(options.data)) {\n        form.append(key, options.data[key]);\n      }\n    }\n    output.body = form;\n    const headers = new Headers(output.headers);\n    headers.delete('content-type'); // content-type will be set by `window.fetch` to includy boundary\n    output.headers = headers;\n  } else if (type.includes('application/json') || typeof options.data === 'object') {\n    output.body = JSON.stringify(options.data);\n  }\n  return output;\n};\n// WEB IMPLEMENTATION\nclass CapacitorHttpPluginWeb extends WebPlugin {\n  /**\n   * Perform an Http request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  async request(options) {\n    const requestInit = buildRequestInit(options, options.webFetchExtra);\n    const urlParams = buildUrlParams(options.params, options.shouldEncodeUrlParams);\n    const url = urlParams ? `${options.url}?${urlParams}` : options.url;\n    const response = await fetch(url, requestInit);\n    const contentType = response.headers.get('content-type') || '';\n    // Default to 'text' responseType so no parsing happens\n    let {\n      responseType = 'text'\n    } = response.ok ? options : {};\n    // If the response content-type is json, force the response to be json\n    if (contentType.includes('application/json')) {\n      responseType = 'json';\n    }\n    let data;\n    let blob;\n    switch (responseType) {\n      case 'arraybuffer':\n      case 'blob':\n        blob = await response.blob();\n        data = await readBlobAsBase64(blob);\n        break;\n      case 'json':\n        data = await response.json();\n        break;\n      case 'document':\n      case 'text':\n      default:\n        data = await response.text();\n    }\n    // Convert fetch headers to Capacitor HttpHeaders\n    const headers = {};\n    response.headers.forEach((value, key) => {\n      headers[key] = value;\n    });\n    return {\n      data,\n      headers,\n      status: response.status,\n      url: response.url\n    };\n  }\n  /**\n   * Perform an Http GET request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  async get(options) {\n    return this.request(Object.assign(Object.assign({}, options), {\n      method: 'GET'\n    }));\n  }\n  /**\n   * Perform an Http POST request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  async post(options) {\n    return this.request(Object.assign(Object.assign({}, options), {\n      method: 'POST'\n    }));\n  }\n  /**\n   * Perform an Http PUT request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  async put(options) {\n    return this.request(Object.assign(Object.assign({}, options), {\n      method: 'PUT'\n    }));\n  }\n  /**\n   * Perform an Http PATCH request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  async patch(options) {\n    return this.request(Object.assign(Object.assign({}, options), {\n      method: 'PATCH'\n    }));\n  }\n  /**\n   * Perform an Http DELETE request given a set of options\n   * @param options Options to build the HTTP request\n   */\n  async delete(options) {\n    return this.request(Object.assign(Object.assign({}, options), {\n      method: 'DELETE'\n    }));\n  }\n}\nconst CapacitorHttp = registerPlugin('CapacitorHttp', {\n  web: () => new CapacitorHttpPluginWeb()\n});\n/******** END HTTP PLUGIN ********/\n\nexport { Capacitor, CapacitorCookies, CapacitorException, CapacitorHttp, ExceptionCode, WebPlugin, WebView, buildRequestInit, registerPlugin };\n"], "mappings": ";;;;;;AAAA,IACI,eAkBE,oBAQA,eAUA,iBAsJA,qBACA,WACA,gBAKA,WAiHA,QAKA,QACA,2BAqDA,kBAQA,kBAcA,sBAcA,gBA4BA,kBAwCA,wBA6FA;AAnjBN;AAAA;AAAA;AAEA,KAAC,SAAUA,gBAAe;AAOxB,MAAAA,eAAc,eAAe,IAAI;AAQjC,MAAAA,eAAc,aAAa,IAAI;AAAA,IACjC,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,IAAM,qBAAN,cAAiC,MAAM;AAAA,MACrC,YAAY,SAAS,MAAM,MAAM;AAC/B,cAAM,OAAO;AACb,aAAK,UAAU;AACf,aAAK,OAAO;AACZ,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AACA,IAAM,gBAAgB,SAAO;AAC3B,UAAI,IAAI;AACR,UAAI,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,eAAe;AAC/D,eAAO;AAAA,MACT,YAAY,MAAM,KAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ;AACpL,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,IAAM,kBAAkB,SAAO;AAC7B,YAAM,oBAAoB,IAAI,2BAA2B;AACzD,YAAM,MAAM,IAAI,aAAa,CAAC;AAC9B,YAAM,UAAU,IAAI,UAAU,IAAI,WAAW,CAAC;AAC9C,YAAM,cAAc,MAAM;AACxB,eAAO,sBAAsB,OAAO,kBAAkB,OAAO,cAAc,GAAG;AAAA,MAChF;AACA,YAAM,mBAAmB,MAAM,YAAY,MAAM;AACjD,YAAM,oBAAoB,gBAAc;AACtC,cAAM,SAAS,kBAAkB,IAAI,UAAU;AAC/C,YAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,UAAU,IAAI,YAAY,CAAC,GAAG;AAEvF,iBAAO;AAAA,QACT;AACA,YAAI,gBAAgB,UAAU,GAAG;AAE/B,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AACA,YAAM,kBAAkB,gBAAc;AACpC,YAAI;AACJ,gBAAQ,KAAK,IAAI,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,OAAK,EAAE,SAAS,UAAU;AAAA,MACzG;AACA,YAAM,cAAc,SAAO,IAAI,QAAQ,MAAM,GAAG;AAChD,YAAM,oBAAoB,oBAAI,IAAI;AAClC,YAAMC,kBAAiB,CAAC,YAAY,oBAAoB,CAAC,MAAM;AAC7D,cAAM,mBAAmB,kBAAkB,IAAI,UAAU;AACzD,YAAI,kBAAkB;AACpB,kBAAQ,KAAK,qBAAqB,UAAU,sDAAsD;AAClG,iBAAO,iBAAiB;AAAA,QAC1B;AACA,cAAM,WAAW,YAAY;AAC7B,cAAM,eAAe,gBAAgB,UAAU;AAC/C,YAAI;AACJ,cAAM,2BAA2B,MAAY;AAC3C,cAAI,CAAC,oBAAoB,YAAY,mBAAmB;AACtD,+BAAmB,OAAO,kBAAkB,QAAQ,MAAM,aAAa,mBAAmB,MAAM,kBAAkB,QAAQ,EAAE,IAAI,mBAAmB,kBAAkB,QAAQ;AAAA,UAC/K,WAAW,sBAAsB,QAAQ,CAAC,oBAAoB,SAAS,mBAAmB;AACxF,+BAAmB,OAAO,kBAAkB,KAAK,MAAM,aAAa,mBAAmB,MAAM,kBAAkB,KAAK,EAAE,IAAI,mBAAmB,kBAAkB,KAAK;AAAA,UACtK;AACA,iBAAO;AAAA,QACT;AACA,cAAM,qBAAqB,CAAC,MAAM,SAAS;AACzC,cAAI,IAAI;AACR,cAAI,cAAc;AAChB,kBAAM,eAAe,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,QAAQ,KAAK,OAAK,SAAS,EAAE,IAAI;AAC/H,gBAAI,cAAc;AAChB,kBAAI,aAAa,UAAU,WAAW;AACpC,uBAAO,aAAW,IAAI,cAAc,YAAY,KAAK,SAAS,GAAG,OAAO;AAAA,cAC1E,OAAO;AACL,uBAAO,CAAC,SAAS,aAAa,IAAI,eAAe,YAAY,KAAK,SAAS,GAAG,SAAS,QAAQ;AAAA,cACjG;AAAA,YACF,WAAW,MAAM;AACf,sBAAQ,KAAK,KAAK,IAAI,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI;AAAA,YAC5E;AAAA,UACF,WAAW,MAAM;AACf,oBAAQ,KAAK,KAAK,IAAI,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI;AAAA,UAC5E,OAAO;AACL,kBAAM,IAAI,mBAAmB,IAAI,UAAU,kCAAkC,QAAQ,IAAI,cAAc,aAAa;AAAA,UACtH;AAAA,QACF;AACA,cAAM,4BAA4B,UAAQ;AACxC,cAAI;AACJ,gBAAM,UAAU,IAAI,SAAS;AAC3B,kBAAM,IAAI,yBAAyB,EAAE,KAAK,UAAQ;AAChD,oBAAM,KAAK,mBAAmB,MAAM,IAAI;AACxC,kBAAI,IAAI;AACN,sBAAMC,KAAI,GAAG,GAAG,IAAI;AACpB,yBAASA,OAAM,QAAQA,OAAM,SAAS,SAASA,GAAE;AACjD,uBAAOA;AAAA,cACT,OAAO;AACL,sBAAM,IAAI,mBAAmB,IAAI,UAAU,IAAI,IAAI,6BAA6B,QAAQ,IAAI,cAAc,aAAa;AAAA,cACzH;AAAA,YACF,CAAC;AACD,gBAAI,SAAS,eAAe;AAC1B,gBAAE,SAAS,MAAS;AAAG,8BAAO;AAAA;AAAA,YAChC;AACA,mBAAO;AAAA,UACT;AAEA,kBAAQ,WAAW,MAAM,GAAG,KAAK,SAAS,CAAC;AAC3C,iBAAO,eAAe,SAAS,QAAQ;AAAA,YACrC,OAAO;AAAA,YACP,UAAU;AAAA,YACV,cAAc;AAAA,UAChB,CAAC;AACD,iBAAO;AAAA,QACT;AACA,cAAM,cAAc,0BAA0B,aAAa;AAC3D,cAAM,iBAAiB,0BAA0B,gBAAgB;AACjE,cAAM,oBAAoB,CAAC,WAAW,aAAa;AACjD,gBAAM,OAAO,YAAY;AAAA,YACvB;AAAA,UACF,GAAG,QAAQ;AACX,gBAAM,SAAS,MAAY;AACzB,kBAAM,aAAa,MAAM;AACzB,2BAAe;AAAA,cACb;AAAA,cACA;AAAA,YACF,GAAG,QAAQ;AAAA,UACb;AACA,gBAAM,IAAI,IAAI,QAAQ,aAAW,KAAK,KAAK,MAAM,QAAQ;AAAA,YACvD;AAAA,UACF,CAAC,CAAC,CAAC;AACH,YAAE,SAAS,MAAY;AACrB,oBAAQ,KAAK,oDAAoD;AACjE,kBAAM,OAAO;AAAA,UACf;AACA,iBAAO;AAAA,QACT;AACA,cAAM,QAAQ,IAAI,MAAM,CAAC,GAAG;AAAA,UAC1B,IAAI,GAAG,MAAM;AACX,oBAAQ,MAAM;AAAA;AAAA,cAEZ,KAAK;AACH,uBAAO;AAAA,cACT,KAAK;AACH,uBAAO,OAAO,CAAC;AAAA,cACjB,KAAK;AACH,uBAAO,eAAe,oBAAoB;AAAA,cAC5C,KAAK;AACH,uBAAO;AAAA,cACT;AACE,uBAAO,0BAA0B,IAAI;AAAA,YACzC;AAAA,UACF;AAAA,QACF,CAAC;AACD,gBAAQ,UAAU,IAAI;AACtB,0BAAkB,IAAI,YAAY;AAAA,UAChC,MAAM;AAAA,UACN;AAAA,UACA,WAAW,oBAAI,IAAI,CAAC,GAAG,OAAO,KAAK,iBAAiB,GAAG,GAAI,eAAe,CAAC,QAAQ,IAAI,CAAC,CAAE,CAAC;AAAA,QAC7F,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,IAAI,gBAAgB;AACvB,YAAI,iBAAiB,cAAY;AAAA,MACnC;AACA,UAAI,cAAc;AAClB,UAAI,cAAc;AAClB,UAAI,mBAAmB;AACvB,UAAI,oBAAoB;AACxB,UAAI,iBAAiBD;AACrB,UAAI,YAAY;AAChB,UAAI,QAAQ,CAAC,CAAC,IAAI;AAClB,UAAI,mBAAmB,CAAC,CAAC,IAAI;AAC7B,aAAO;AAAA,IACT;AACA,IAAM,sBAAsB,SAAO,IAAI,YAAY,gBAAgB,GAAG;AACtE,IAAM,YAAyB,oCAAoB,OAAO,eAAe,cAAc,aAAa,OAAO,SAAS,cAAc,OAAO,OAAO,WAAW,cAAc,SAAS,OAAO,WAAW,cAAc,SAAS,CAAC,CAAC;AAC7N,IAAM,iBAAiB,UAAU;AAKjC,IAAM,YAAN,MAAgB;AAAA,MACd,cAAc;AACZ,aAAK,YAAY,CAAC;AAClB,aAAK,yBAAyB,CAAC;AAC/B,aAAK,kBAAkB,CAAC;AAAA,MAC1B;AAAA,MACA,YAAY,WAAW,cAAc;AACnC,YAAI,gBAAgB;AACpB,cAAM,YAAY,KAAK,UAAU,SAAS;AAC1C,YAAI,CAAC,WAAW;AACd,eAAK,UAAU,SAAS,IAAI,CAAC;AAC7B,0BAAgB;AAAA,QAClB;AACA,aAAK,UAAU,SAAS,EAAE,KAAK,YAAY;AAG3C,cAAM,iBAAiB,KAAK,gBAAgB,SAAS;AACrD,YAAI,kBAAkB,CAAC,eAAe,YAAY;AAChD,eAAK,kBAAkB,cAAc;AAAA,QACvC;AACA,YAAI,eAAe;AACjB,eAAK,8BAA8B,SAAS;AAAA,QAC9C;AACA,cAAM,SAAS,MAAS;AAAG,sBAAK,eAAe,WAAW,YAAY;AAAA;AACtE,cAAM,IAAI,QAAQ,QAAQ;AAAA,UACxB;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT;AAAA,MACM,qBAAqB;AAAA;AACzB,eAAK,YAAY,CAAC;AAClB,qBAAW,YAAY,KAAK,iBAAiB;AAC3C,iBAAK,qBAAqB,KAAK,gBAAgB,QAAQ,CAAC;AAAA,UAC1D;AACA,eAAK,kBAAkB,CAAC;AAAA,QAC1B;AAAA;AAAA,MACA,gBAAgB,WAAW,MAAM,qBAAqB;AACpD,cAAM,YAAY,KAAK,UAAU,SAAS;AAC1C,YAAI,CAAC,WAAW;AACd,cAAI,qBAAqB;AACvB,gBAAI,OAAO,KAAK,uBAAuB,SAAS;AAChD,gBAAI,CAAC,MAAM;AACT,qBAAO,CAAC;AAAA,YACV;AACA,iBAAK,KAAK,IAAI;AACd,iBAAK,uBAAuB,SAAS,IAAI;AAAA,UAC3C;AACA;AAAA,QACF;AACA,kBAAU,QAAQ,cAAY,SAAS,IAAI,CAAC;AAAA,MAC9C;AAAA,MACA,aAAa,WAAW;AACtB,YAAI;AACJ,eAAO,CAAC,GAAG,KAAK,KAAK,UAAU,SAAS,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MACrF;AAAA,MACA,uBAAuB,iBAAiB,iBAAiB;AACvD,aAAK,gBAAgB,eAAe,IAAI;AAAA,UACtC,YAAY;AAAA,UACZ;AAAA,UACA;AAAA,UACA,SAAS,WAAS;AAChB,iBAAK,gBAAgB,iBAAiB,KAAK;AAAA,UAC7C;AAAA,QACF;AAAA,MACF;AAAA,MACA,cAAc,MAAM,mBAAmB;AACrC,eAAO,IAAI,UAAU,UAAU,KAAK,cAAc,aAAa;AAAA,MACjE;AAAA,MACA,YAAY,MAAM,iBAAiB;AACjC,eAAO,IAAI,UAAU,UAAU,KAAK,cAAc,WAAW;AAAA,MAC/D;AAAA,MACM,eAAe,WAAW,cAAc;AAAA;AAC5C,gBAAM,YAAY,KAAK,UAAU,SAAS;AAC1C,cAAI,CAAC,WAAW;AACd;AAAA,UACF;AACA,gBAAM,QAAQ,UAAU,QAAQ,YAAY;AAC5C,eAAK,UAAU,SAAS,EAAE,OAAO,OAAO,CAAC;AAGzC,cAAI,CAAC,KAAK,UAAU,SAAS,EAAE,QAAQ;AACrC,iBAAK,qBAAqB,KAAK,gBAAgB,SAAS,CAAC;AAAA,UAC3D;AAAA,QACF;AAAA;AAAA,MACA,kBAAkB,QAAQ;AACxB,eAAO,iBAAiB,OAAO,iBAAiB,OAAO,OAAO;AAC9D,eAAO,aAAa;AAAA,MACtB;AAAA,MACA,qBAAqB,QAAQ;AAC3B,YAAI,CAAC,QAAQ;AACX;AAAA,QACF;AACA,eAAO,oBAAoB,OAAO,iBAAiB,OAAO,OAAO;AACjE,eAAO,aAAa;AAAA,MACtB;AAAA,MACA,8BAA8B,WAAW;AACvC,cAAM,OAAO,KAAK,uBAAuB,SAAS;AAClD,YAAI,CAAC,MAAM;AACT;AAAA,QACF;AACA,eAAO,KAAK,uBAAuB,SAAS;AAC5C,aAAK,QAAQ,SAAO;AAClB,eAAK,gBAAgB,WAAW,GAAG;AAAA,QACrC,CAAC;AAAA,MACH;AAAA,IACF;AAQA,IAAM,SAAS,SAAO,mBAAmB,GAAG,EAAE,QAAQ,wBAAwB,kBAAkB,EAAE,QAAQ,SAAS,MAAM;AAKzH,IAAM,SAAS,SAAO,IAAI,QAAQ,oBAAoB,kBAAkB;AACxE,IAAM,4BAAN,cAAwC,UAAU;AAAA,MAC1C,aAAa;AAAA;AACjB,gBAAM,UAAU,SAAS;AACzB,gBAAM,YAAY,CAAC;AACnB,kBAAQ,MAAM,GAAG,EAAE,QAAQ,YAAU;AACnC,gBAAI,OAAO,UAAU,EAAG;AAExB,gBAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,YAAY,EAAE,MAAM,YAAY;AACvE,kBAAM,OAAO,GAAG,EAAE,KAAK;AACvB,oBAAQ,OAAO,KAAK,EAAE,KAAK;AAC3B,sBAAU,GAAG,IAAI;AAAA,UACnB,CAAC;AACD,iBAAO;AAAA,QACT;AAAA;AAAA,MACM,UAAU,SAAS;AAAA;AACvB,cAAI;AAEF,kBAAM,aAAa,OAAO,QAAQ,GAAG;AACrC,kBAAM,eAAe,OAAO,QAAQ,KAAK;AAEzC,kBAAM,UAAU,cAAc,QAAQ,WAAW,IAAI,QAAQ,YAAY,EAAE,CAAC;AAC5E,kBAAM,QAAQ,QAAQ,QAAQ,KAAK,QAAQ,SAAS,EAAE;AACtD,kBAAM,SAAS,QAAQ,OAAO,QAAQ,QAAQ,IAAI,SAAS,IAAI,UAAU,QAAQ,GAAG,KAAK;AACzF,qBAAS,SAAS,GAAG,UAAU,IAAI,gBAAgB,EAAE,GAAG,OAAO,UAAU,IAAI,KAAK,MAAM;AAAA,UAC1F,SAAS,OAAO;AACd,mBAAO,QAAQ,OAAO,KAAK;AAAA,UAC7B;AAAA,QACF;AAAA;AAAA,MACM,aAAa,SAAS;AAAA;AAC1B,cAAI;AACF,qBAAS,SAAS,GAAG,QAAQ,GAAG;AAAA,UAClC,SAAS,OAAO;AACd,mBAAO,QAAQ,OAAO,KAAK;AAAA,UAC7B;AAAA,QACF;AAAA;AAAA,MACM,eAAe;AAAA;AACnB,cAAI;AACF,kBAAM,UAAU,SAAS,OAAO,MAAM,GAAG,KAAK,CAAC;AAC/C,uBAAW,UAAU,SAAS;AAC5B,uBAAS,SAAS,OAAO,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,cAAa,oBAAI,KAAK,GAAE,YAAY,CAAC,SAAS;AAAA,YAC3G;AAAA,UACF,SAAS,OAAO;AACd,mBAAO,QAAQ,OAAO,KAAK;AAAA,UAC7B;AAAA,QACF;AAAA;AAAA,MACM,kBAAkB;AAAA;AACtB,cAAI;AACF,kBAAM,KAAK,aAAa;AAAA,UAC1B,SAAS,OAAO;AACd,mBAAO,QAAQ,OAAO,KAAK;AAAA,UAC7B;AAAA,QACF;AAAA;AAAA,IACF;AACA,IAAM,mBAAmB,eAAe,oBAAoB;AAAA,MAC1D,KAAK,MAAM,IAAI,0BAA0B;AAAA,IAC3C,CAAC;AAMD,IAAM,mBAAmB,CAAM,SAAK;AAAG,iBAAI,QAAQ,CAAC,SAAS,WAAW;AACtE,cAAM,SAAS,IAAI,WAAW;AAC9B,eAAO,SAAS,MAAM;AACpB,gBAAM,eAAe,OAAO;AAE5B,kBAAQ,aAAa,QAAQ,GAAG,KAAK,IAAI,aAAa,MAAM,GAAG,EAAE,CAAC,IAAI,YAAY;AAAA,QACpF;AACA,eAAO,UAAU,WAAS,OAAO,KAAK;AACtC,eAAO,cAAc,IAAI;AAAA,MAC3B,CAAC;AAAA;AAKD,IAAM,uBAAuB,CAAC,UAAU,CAAC,MAAM;AAC7C,YAAM,eAAe,OAAO,KAAK,OAAO;AACxC,YAAM,cAAc,OAAO,KAAK,OAAO,EAAE,IAAI,OAAK,EAAE,kBAAkB,CAAC;AACvE,YAAM,aAAa,YAAY,OAAO,CAAC,KAAK,KAAK,UAAU;AACzD,YAAI,GAAG,IAAI,QAAQ,aAAa,KAAK,CAAC;AACtC,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AACL,aAAO;AAAA,IACT;AAMA,IAAM,iBAAiB,CAAC,QAAQ,eAAe,SAAS;AACtD,UAAI,CAAC,OAAQ,QAAO;AACpB,YAAM,SAAS,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,aAAa,UAAU;AACnE,cAAM,CAAC,KAAK,KAAK,IAAI;AACrB,YAAI;AACJ,YAAI;AACJ,YAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,iBAAO;AACP,gBAAM,QAAQ,SAAO;AACnB,2BAAe,eAAe,mBAAmB,GAAG,IAAI;AACxD,oBAAQ,GAAG,GAAG,IAAI,YAAY;AAAA,UAChC,CAAC;AAED,eAAK,MAAM,GAAG,EAAE;AAAA,QAClB,OAAO;AACL,yBAAe,eAAe,mBAAmB,KAAK,IAAI;AAC1D,iBAAO,GAAG,GAAG,IAAI,YAAY;AAAA,QAC/B;AACA,eAAO,GAAG,WAAW,IAAI,IAAI;AAAA,MAC/B,GAAG,EAAE;AAEL,aAAO,OAAO,OAAO,CAAC;AAAA,IACxB;AAMA,IAAM,mBAAmB,CAAC,SAAS,QAAQ,CAAC,MAAM;AAChD,YAAM,SAAS,OAAO,OAAO;AAAA,QAC3B,QAAQ,QAAQ,UAAU;AAAA,QAC1B,SAAS,QAAQ;AAAA,MACnB,GAAG,KAAK;AAER,YAAM,UAAU,qBAAqB,QAAQ,OAAO;AACpD,YAAM,OAAO,QAAQ,cAAc,KAAK;AAExC,UAAI,OAAO,QAAQ,SAAS,UAAU;AACpC,eAAO,OAAO,QAAQ;AAAA,MACxB,WAES,KAAK,SAAS,mCAAmC,GAAG;AAC3D,cAAM,SAAS,IAAI,gBAAgB;AACnC,mBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,CAAC,CAAC,GAAG;AAC7D,iBAAO,IAAI,KAAK,KAAK;AAAA,QACvB;AACA,eAAO,OAAO,OAAO,SAAS;AAAA,MAChC,WAAW,KAAK,SAAS,qBAAqB,KAAK,QAAQ,gBAAgB,UAAU;AACnF,cAAM,OAAO,IAAI,SAAS;AAC1B,YAAI,QAAQ,gBAAgB,UAAU;AACpC,kBAAQ,KAAK,QAAQ,CAAC,OAAO,QAAQ;AACnC,iBAAK,OAAO,KAAK,KAAK;AAAA,UACxB,CAAC;AAAA,QACH,OAAO;AACL,qBAAW,OAAO,OAAO,KAAK,QAAQ,IAAI,GAAG;AAC3C,iBAAK,OAAO,KAAK,QAAQ,KAAK,GAAG,CAAC;AAAA,UACpC;AAAA,QACF;AACA,eAAO,OAAO;AACd,cAAME,WAAU,IAAI,QAAQ,OAAO,OAAO;AAC1C,QAAAA,SAAQ,OAAO,cAAc;AAC7B,eAAO,UAAUA;AAAA,MACnB,WAAW,KAAK,SAAS,kBAAkB,KAAK,OAAO,QAAQ,SAAS,UAAU;AAChF,eAAO,OAAO,KAAK,UAAU,QAAQ,IAAI;AAAA,MAC3C;AACA,aAAO;AAAA,IACT;AAEA,IAAM,yBAAN,cAAqC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKvC,QAAQ,SAAS;AAAA;AACrB,gBAAM,cAAc,iBAAiB,SAAS,QAAQ,aAAa;AACnE,gBAAM,YAAY,eAAe,QAAQ,QAAQ,QAAQ,qBAAqB;AAC9E,gBAAM,MAAM,YAAY,GAAG,QAAQ,GAAG,IAAI,SAAS,KAAK,QAAQ;AAChE,gBAAM,WAAW,MAAM,MAAM,KAAK,WAAW;AAC7C,gBAAM,cAAc,SAAS,QAAQ,IAAI,cAAc,KAAK;AAE5D,cAAI;AAAA,YACF,eAAe;AAAA,UACjB,IAAI,SAAS,KAAK,UAAU,CAAC;AAE7B,cAAI,YAAY,SAAS,kBAAkB,GAAG;AAC5C,2BAAe;AAAA,UACjB;AACA,cAAI;AACJ,cAAI;AACJ,kBAAQ,cAAc;AAAA,YACpB,KAAK;AAAA,YACL,KAAK;AACH,qBAAO,MAAM,SAAS,KAAK;AAC3B,qBAAO,MAAM,iBAAiB,IAAI;AAClC;AAAA,YACF,KAAK;AACH,qBAAO,MAAM,SAAS,KAAK;AAC3B;AAAA,YACF,KAAK;AAAA,YACL,KAAK;AAAA,YACL;AACE,qBAAO,MAAM,SAAS,KAAK;AAAA,UAC/B;AAEA,gBAAM,UAAU,CAAC;AACjB,mBAAS,QAAQ,QAAQ,CAAC,OAAO,QAAQ;AACvC,oBAAQ,GAAG,IAAI;AAAA,UACjB,CAAC;AACD,iBAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA,QAAQ,SAAS;AAAA,YACjB,KAAK,SAAS;AAAA,UAChB;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAKM,IAAI,SAAS;AAAA;AACjB,iBAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG;AAAA,YAC5D,QAAQ;AAAA,UACV,CAAC,CAAC;AAAA,QACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAKM,KAAK,SAAS;AAAA;AAClB,iBAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG;AAAA,YAC5D,QAAQ;AAAA,UACV,CAAC,CAAC;AAAA,QACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAKM,IAAI,SAAS;AAAA;AACjB,iBAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG;AAAA,YAC5D,QAAQ;AAAA,UACV,CAAC,CAAC;AAAA,QACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAKM,MAAM,SAAS;AAAA;AACnB,iBAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG;AAAA,YAC5D,QAAQ;AAAA,UACV,CAAC,CAAC;AAAA,QACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAKM,OAAO,SAAS;AAAA;AACpB,iBAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG;AAAA,YAC5D,QAAQ;AAAA,UACV,CAAC,CAAC;AAAA,QACJ;AAAA;AAAA,IACF;AACA,IAAM,gBAAgB,eAAe,iBAAiB;AAAA,MACpD,KAAK,MAAM,IAAI,uBAAuB;AAAA,IACxC,CAAC;AAAA;AAAA;", "names": ["ExceptionCode", "registerPlugin", "p", "headers"], "x_google_ignoreList": [0]}