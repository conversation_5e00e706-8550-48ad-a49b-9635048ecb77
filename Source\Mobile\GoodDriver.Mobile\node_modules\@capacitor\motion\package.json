{"name": "@capacitor/motion", "version": "7.0.0", "description": "The Motion API tracks accelerometer and device orientation (compass heading, etc.)", "main": "dist/plugin.cjs.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "unpkg": "dist/plugin.js", "files": ["dist/"], "author": "Ionic <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ionic-team/capacitor-plugins"}, "bugs": {"url": "https://github.com/ionic-team/capacitor-plugins/issues"}, "keywords": ["capacitor", "plugin", "native"], "scripts": {"verify": "npm run verify:ios && npm run verify:android && npm run verify:web", "verify:ios": "true", "verify:android": "true", "verify:web": "npm run build", "lint": "npm run eslint && npm run prettier -- --check", "fmt": "npm run eslint -- --fix && npm run prettier -- --write", "eslint": "eslint . --ext ts", "prettier": "prettier \"**/*.{css,html,ts,js,java}\"", "docgen": "docgen --api MotionPlugin --output-readme README.md --output-json dist/docs.json", "build": "npm run clean && npm run docgen && tsc && rollup -c rollup.config.mjs", "clean": "rimraf ./dist", "watch": "tsc --watch", "prepublishOnly": "npm run build"}, "devDependencies": {"@capacitor/android": "next", "@capacitor/core": "next", "@capacitor/docgen": "0.2.2", "@capacitor/ios": "next", "@ionic/eslint-config": "^0.4.0", "@ionic/prettier-config": "~1.0.1", "eslint": "^8.57.0", "prettier": "~2.3.0", "prettier-plugin-java": "~1.0.2", "rimraf": "^6.0.1", "rollup": "^4.26.0", "typescript": "~4.1.5"}, "peerDependencies": {"@capacitor/core": ">=7.0.0"}, "prettier": "@ionic/prettier-config", "eslintConfig": {"extends": "@ionic/eslint-config/recommended"}, "capacitor": {}, "publishConfig": {"access": "public"}, "gitHead": "9fa6354a30cc1f2e92b56ddb10048723e08076e9"}