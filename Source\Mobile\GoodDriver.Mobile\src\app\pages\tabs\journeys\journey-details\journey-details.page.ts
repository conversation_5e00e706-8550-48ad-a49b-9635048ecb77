import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Journey } from 'src/app/core/models/journey.model';
import { JourneyInfo } from 'src/app/core/models/journeyInfo.model';
import { DataStorageService } from 'src/app/core/services/data-storage-service';
import { JourneyStorageService } from 'src/app/core/services/journey-storage.service';
import { ToastService } from 'src/app/core/services/toast.service';
import { GeocodingService } from 'src/app/core/services/geocoding.service';

import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-journey-details',
  standalone: true,
  templateUrl: './journey-details.page.html',
  styleUrls: [ './journey-details.page.scss'],
  imports: [
    IonicModule,
    CommonModule,
  ]
})
export class JourneyDetailsPage implements OnInit {

  journey!: Journey;
  isLoading = true;

  constructor(
    private route: ActivatedRoute,
    private dataStorageService: DataStorageService,
    private journeyStorageService: JourneyStorageService,
    private toastService: ToastService,
    private geocodingService: GeocodingService
  ) {}

  async ngOnInit() {
    const journeyId = this.route.snapshot.paramMap.get('id');
    const journeyStr = this.route.snapshot.queryParamMap.get('data');

    console.log('Journey Details - ID:', journeyId);
    console.log('Journey Details - Data from query:', journeyStr);

    if (journeyStr) {
      try {
        this.journey = JSON.parse(journeyStr);
        console.log('Parsed journey:', this.journey);
        console.log('Journey infosJourney:', this.journey.infosJourney);
        console.log('Journey infosJourney length:', this.journey.infosJourney?.length);

        // Always try to load journey locations from database to ensure we have the latest data
        console.log('Loading journey locations from database...');
        await this.loadJourneyLocations(this.journey.id);

        // Load addresses for all locations
        if (this.journey.infosJourney && this.journey.infosJourney.length > 0) {
          console.log('Loading addresses for', this.journey.infosJourney.length, 'locations');
          await this.loadAddresses();
        } else {
          console.log('No locations found for this journey');
        }
      } catch (error) {
        console.error('Error parsing journey data:', error);
        this.toastService.showToast('Erro ao carregar dados da viagem', 'danger');
      }
    } else if (journeyId) {
      // If no data in query params, load from database
      console.log('No data in query params, loading from database...');
      await this.loadJourneyFromDatabase(journeyId);
    }

    // Debug: Check if journeyInfo table has any data
    await this.debugJourneyInfoTable();

    this.isLoading = false;
  }

  /**
   * Load journey locations from database
   */
  async loadJourneyLocations(journeyId: string): Promise<void> {
    try {
      console.log('Querying journeyInfo table for journeyId:', journeyId);
      const locations = await this.dataStorageService.select('journeyInfo', `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);

      console.log('Raw locations from database:', locations);
      console.log('Locations type:', typeof locations);
      console.log('Is array:', Array.isArray(locations));

      if (Array.isArray(locations) && locations.length > 0) {
        this.journey.infosJourney = locations.map((location: any) => ({
          id: location.id,
          journeyId: location.journeyId,
          latitude: location.latitude,
          longitude: location.longitude,
          timestamp: location.timestamp,
          address: location.address,
          occurrenceType: location.occurrenceType,
          syncStatus: location.syncStatus,
          lastSyncDate: location.lastSyncDate ? new Date(location.lastSyncDate) : undefined
        }));

        console.log('Loaded', this.journey.infosJourney.length, 'locations from database');
        console.log('Mapped locations:', this.journey.infosJourney);
      } else {
        console.log('No locations found in database for journey:', journeyId);
        console.log('Locations result:', locations);
        this.journey.infosJourney = [];
      }
    } catch (error) {
      console.error('Error loading journey locations:', error);
      this.journey.infosJourney = [];
    }
  }

  /**
   * Load journey from database by ID
   */
  async loadJourneyFromDatabase(journeyId: string): Promise<void> {
    try {
      const journeyData = await this.dataStorageService.select('journeys', `WHERE id = '${journeyId}'`);

      if (Array.isArray(journeyData) && journeyData.length > 0) {
        const data = journeyData[0];
        this.journey = {
          id: data.id,
          startDate: data.startDate,
          endDate: data.endDate,
          distance: data.distance,
          userId: data.userId,
          vehicleId: data.vehicleId,
          infosJourney: [],
          syncStatus: data.syncStatus,
          lastSyncDate: data.lastSyncDate ? new Date(data.lastSyncDate) : undefined
        };

        // Load locations for this journey
        await this.loadJourneyLocations(journeyId);

        // Load addresses
        if (this.journey.infosJourney && this.journey.infosJourney.length > 0) {
          await this.loadAddresses();
        }
      } else {
        console.error('Journey not found in database:', journeyId);
        this.toastService.showToast('Viagem não encontrada', 'danger');
      }
    } catch (error) {
      console.error('Error loading journey from database:', error);
      this.toastService.showToast('Erro ao carregar viagem', 'danger');
    }
  }

  /**
   * Load addresses for all journey locations
   * Prioritizes start and end locations
   */
  async loadAddresses(): Promise<void> {
    if (!this.journey.infosJourney || this.journey.infosJourney.length === 0) return;

    console.log('Loading addresses for journey locations...');

    // First, load addresses for start and end locations (priority)
    const priorityLocations: { info: any, index: number }[] = [];

    // Add start location
    if (this.journey.infosJourney[0] && !this.journey.infosJourney[0].address) {
      priorityLocations.push({ info: this.journey.infosJourney[0], index: 0 });
    }

    // Add end location (if different from start)
    const lastIndex = this.journey.infosJourney.length - 1;
    if (lastIndex > 0 && this.journey.infosJourney[lastIndex] && !this.journey.infosJourney[lastIndex].address) {
      priorityLocations.push({ info: this.journey.infosJourney[lastIndex], index: lastIndex });
    }

    // Load priority addresses first
    if (priorityLocations.length > 0) {
      console.log('Loading priority addresses (start/end)...');
      await Promise.all(
        priorityLocations.map(async ({ info, index }) => {
          try {
            console.log(`Loading address for ${index === 0 ? 'start' : 'end'} location...`);
            info.address = await this.geocodingService.getReverseGeocodeWithCache(info.latitude, info.longitude);
            console.log(`${index === 0 ? 'Start' : 'End'} address loaded:`, info.address);
          } catch (error) {
            console.error(`Error loading address for ${index === 0 ? 'start' : 'end'} location:`, error);
            info.address = 'Endereço não disponível';
          }
        })
      );

      // Small delay before loading other addresses
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Then load addresses for intermediate locations in batches
    const intermediateLocations = this.journey.infosJourney.slice(1, -1).filter(info => !info.address);

    if (intermediateLocations.length > 0) {
      console.log(`Loading addresses for ${intermediateLocations.length} intermediate locations...`);

      const batchSize = 3; // Smaller batch size for intermediate locations

      for (let i = 0; i < intermediateLocations.length; i += batchSize) {
        const batch = intermediateLocations.slice(i, i + batchSize);

        await Promise.all(
          batch.map(async (info) => {
            try {
              info.address = await this.geocodingService.getReverseGeocodeWithCache(info.latitude, info.longitude);
            } catch (error) {
              console.error('Error loading address for intermediate location:', error);
              info.address = 'Endereço não disponível';
            }
          })
        );

        // Delay between batches to be respectful to the API
        if (i + batchSize < intermediateLocations.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }

    console.log('Finished loading all addresses');
  }

  /**
   * Debug method to check journeyInfo table contents
   */
  async debugJourneyInfoTable(): Promise<void> {
    try {
      console.log('=== DEBUG: JourneyInfo Table ===');

      // Get all journeyInfo records
      const allJourneyInfo = await this.dataStorageService.select('journeyInfo', '');
      console.log('All journeyInfo records:', allJourneyInfo);
      console.log('Total journeyInfo records:', Array.isArray(allJourneyInfo) ? allJourneyInfo.length : 0);

      // Get all journey records
      const allJourneys = await this.dataStorageService.select('journeys', '');
      console.log('All journey records:', allJourneys);
      console.log('Total journey records:', Array.isArray(allJourneys) ? allJourneys.length : 0);

      console.log('=== END DEBUG ===');
    } catch (error) {
      console.error('Error in debug method:', error);
    }
  }

  /**
   * Get CSS class for location item based on position
   */
  getLocationItemClass(index: number): string {
    if (!this.journey.infosJourney || this.journey.infosJourney.length === 0) {
      return '';
    }

    const totalLocations = this.journey.infosJourney.length;

    if (index === 0) {
      return 'start-location-item';
    } else if (index === totalLocations - 1 && totalLocations > 1) {
      return 'end-location-item';
    } else {
      return 'intermediate-location-item';
    }
  }

  /**
   * Get location description based on position
   */
  getLocationDescription(index: number): string {
    if (!this.journey.infosJourney || this.journey.infosJourney.length === 0) {
      return '';
    }

    const totalLocations = this.journey.infosJourney.length;

    if (index === 0) {
      return 'Início da Viagem';
    } else if (index === totalLocations - 1 && totalLocations > 1) {
      return 'Fim da Viagem';
    } else {
      return `Ponto ${index + 1}`;
    }
  }

  /**
   * Check if location is start point
   */
  isStartLocation(index: number): boolean {
    return index === 0;
  }

  /**
   * Check if location is end point
   */
  isEndLocation(index: number): boolean {
    if (!this.journey.infosJourney || this.journey.infosJourney.length <= 1) {
      return false;
    }
    return index === this.journey.infosJourney.length - 1;
  }

  /**
   * Check if location is intermediate point
   */
  isIntermediateLocation(index: number): boolean {
    return !this.isStartLocation(index) && !this.isEndLocation(index);
  }


}
