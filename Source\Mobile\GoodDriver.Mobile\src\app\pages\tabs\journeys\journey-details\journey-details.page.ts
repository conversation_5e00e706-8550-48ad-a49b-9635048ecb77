import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Journey } from 'src/app/core/models/journey.model';
import { JourneyInfo } from 'src/app/core/models/journeyInfo.model';
import { DataStorageService } from 'src/app/core/services/data-storage-service';
import { IonHeader, IonCardContent } from "@ionic/angular/standalone";
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-journey-details',
  standalone: true,
  templateUrl: './journey-details.page.html',
  styleUrls: [ './journey-details.page.scss'],
  imports: [
    IonicModule,
    CommonModule,
  ]
})
export class JourneyDetailsPage implements OnInit {

  journey!: Journey;

  constructor(
    private route: ActivatedRoute,
    private dataStorageService: DataStorageService
  ) {}

  async ngOnInit() {
    const journeyId = this.route.snapshot.paramMap.get('id');
    const journeyStr = this.route.snapshot.queryParamMap.get('data');

    if (journeyStr) {
      this.journey = JSON.parse(journeyStr);
      this.journey.infosJourney?.map(async info => {
        info.address = await this.reverseGeocode(info.latitude, info.longitude);
        return info;
      });        
    }
  }

  async reverseGeocode(lat: number, lon: number): Promise<string> {
    const url = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lon}`;
    const response = await fetch(url);
    const data = await response.json();
    return data.display_name || 'Endereço não encontrado';
  }
}
