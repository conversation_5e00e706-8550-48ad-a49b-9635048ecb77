﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Migration.Data
{
    [FluentMigrator.Migration(20250425221700)]
    public class InsertBrands : FluentMigrator.Migration
    {
        public override void Down()
        {
            Delete.FromTable("Brand");

        }

        public override void Up()
        {
            string sql = @"IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Acura')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Acura', 'https://cdn.fakesite.com/logos/acura.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Agrale')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Agrale', 'https://cdn.fakesite.com/logos/agrale.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = '<PERSON> Romeo')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Alfa Romeo', 'https://cdn.fakesite.com/logos/alfaromeo.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'AM Gen')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('AM Gen', 'https://cdn.fakesite.com/logos/amgen.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Asia Motors')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Asia Motors', 'https://cdn.fakesite.com/logos/asiamotors.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'ASTON MARTIN')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('ASTON MARTIN', 'https://cdn.fakesite.com/logos/astonmartin.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Audi')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Audi', 'https://cdn.fakesite.com/logos/audi.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Baby')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Baby', 'https://cdn.fakesite.com/logos/baby.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'BMW')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('BMW', 'https://cdn.fakesite.com/logos/bmw.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'BRM')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('BRM', 'https://cdn.fakesite.com/logos/brm.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Bugre')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Bugre', 'https://cdn.fakesite.com/logos/bugre.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'BYD')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('BYD', 'https://cdn.fakesite.com/logos/byd.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'CAB Motors')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('CAB Motors', 'https://cdn.fakesite.com/logos/cabmotors.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Cadillac')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Cadillac', 'https://cdn.fakesite.com/logos/cadillac.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Caoa Chery')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Caoa Chery', 'https://cdn.fakesite.com/logos/caoachery.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Caoa Chery-Chery')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Caoa Chery-Chery', 'https://cdn.fakesite.com/logos/caoacherychery.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'CBT Jipe')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('CBT Jipe', 'https://cdn.fakesite.com/logos/cbtjipe.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'CHANA')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('CHANA', 'https://cdn.fakesite.com/logos/chana.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'CHANGAN')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('CHANGAN', 'https://cdn.fakesite.com/logos/changan.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Chrysler')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Chrysler', 'https://cdn.fakesite.com/logos/chrysler.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Citroën')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Citroën', 'https://cdn.fakesite.com/logos/citroën.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Cross Lander')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Cross Lander', 'https://cdn.fakesite.com/logos/crosslander.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'D2D Motors')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('D2D Motors', 'https://cdn.fakesite.com/logos/d2dmotors.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Daewoo')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Daewoo', 'https://cdn.fakesite.com/logos/daewoo.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Daihatsu')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Daihatsu', 'https://cdn.fakesite.com/logos/daihatsu.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'DFSK')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('DFSK', 'https://cdn.fakesite.com/logos/dfsk.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Dodge')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Dodge', 'https://cdn.fakesite.com/logos/dodge.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'EFFA')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('EFFA', 'https://cdn.fakesite.com/logos/effa.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Engesa')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Engesa', 'https://cdn.fakesite.com/logos/engesa.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Envemo')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Envemo', 'https://cdn.fakesite.com/logos/envemo.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Ferrari')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Ferrari', 'https://cdn.fakesite.com/logos/ferrari.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Fiat')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Fiat', 'https://cdn.fakesite.com/logos/fiat.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Fibravan')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Fibravan', 'https://cdn.fakesite.com/logos/fibravan.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Ford')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Ford', 'https://cdn.fakesite.com/logos/ford.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'FOTON')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('FOTON', 'https://cdn.fakesite.com/logos/foton.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Fyber')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Fyber', 'https://cdn.fakesite.com/logos/fyber.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'GEELY')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('GEELY', 'https://cdn.fakesite.com/logos/geely.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'GM - Chevrolet')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('GM - Chevrolet', 'https://cdn.fakesite.com/logos/gmchevrolet.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'GREAT WALL')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('GREAT WALL', 'https://cdn.fakesite.com/logos/greatwall.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Gurgel')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Gurgel', 'https://cdn.fakesite.com/logos/gurgel.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'GWM')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('GWM', 'https://cdn.fakesite.com/logos/gwm.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'HAFEI')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('HAFEI', 'https://cdn.fakesite.com/logos/hafei.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'HITECH ELECTRIC')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('HITECH ELECTRIC', 'https://cdn.fakesite.com/logos/hitechelectric.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Honda')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Honda', 'https://cdn.fakesite.com/logos/honda.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Hyundai')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Hyundai', 'https://cdn.fakesite.com/logos/hyundai.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Isuzu')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Isuzu', 'https://cdn.fakesite.com/logos/isuzu.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'IVECO')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('IVECO', 'https://cdn.fakesite.com/logos/iveco.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'JAC')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('JAC', 'https://cdn.fakesite.com/logos/jac.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Jaguar')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Jaguar', 'https://cdn.fakesite.com/logos/jaguar.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Jeep')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Jeep', 'https://cdn.fakesite.com/logos/jeep.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'JINBEI')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('JINBEI', 'https://cdn.fakesite.com/logos/jinbei.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'JPX')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('JPX', 'https://cdn.fakesite.com/logos/jpx.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Kia Motors')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Kia Motors', 'https://cdn.fakesite.com/logos/kiamotors.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Lada')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Lada', 'https://cdn.fakesite.com/logos/lada.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'LAMBORGHINI')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('LAMBORGHINI', 'https://cdn.fakesite.com/logos/lamborghini.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Land Rover')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Land Rover', 'https://cdn.fakesite.com/logos/landrover.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Lexus')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Lexus', 'https://cdn.fakesite.com/logos/lexus.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'LIFAN')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('LIFAN', 'https://cdn.fakesite.com/logos/lifan.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'LOBINI')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('LOBINI', 'https://cdn.fakesite.com/logos/lobini.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Lotus')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Lotus', 'https://cdn.fakesite.com/logos/lotus.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Mahindra')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Mahindra', 'https://cdn.fakesite.com/logos/mahindra.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Maserati')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Maserati', 'https://cdn.fakesite.com/logos/maserati.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Matra')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Matra', 'https://cdn.fakesite.com/logos/matra.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Mazda')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Mazda', 'https://cdn.fakesite.com/logos/mazda.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Mclaren')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Mclaren', 'https://cdn.fakesite.com/logos/mclaren.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Mercedes-Benz')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Mercedes-Benz', 'https://cdn.fakesite.com/logos/mercedesbenz.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Mercury')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Mercury', 'https://cdn.fakesite.com/logos/mercury.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'MG')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('MG', 'https://cdn.fakesite.com/logos/mg.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'MINI')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('MINI', 'https://cdn.fakesite.com/logos/mini.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Mitsubishi')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Mitsubishi', 'https://cdn.fakesite.com/logos/mitsubishi.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Miura')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Miura', 'https://cdn.fakesite.com/logos/miura.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Nissan')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Nissan', 'https://cdn.fakesite.com/logos/nissan.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Peugeot')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Peugeot', 'https://cdn.fakesite.com/logos/peugeot.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Plymouth')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Plymouth', 'https://cdn.fakesite.com/logos/plymouth.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Pontiac')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Pontiac', 'https://cdn.fakesite.com/logos/pontiac.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Porsche')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Porsche', 'https://cdn.fakesite.com/logos/porsche.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'RAM')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('RAM', 'https://cdn.fakesite.com/logos/ram.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'RELY')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('RELY', 'https://cdn.fakesite.com/logos/rely.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Renault')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Renault', 'https://cdn.fakesite.com/logos/renault.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Rolls-Royce')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Rolls-Royce', 'https://cdn.fakesite.com/logos/rollsroyce.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Rover')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Rover', 'https://cdn.fakesite.com/logos/rover.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Saab')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Saab', 'https://cdn.fakesite.com/logos/saab.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Saturn')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Saturn', 'https://cdn.fakesite.com/logos/saturn.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Seat')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Seat', 'https://cdn.fakesite.com/logos/seat.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'SERES')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('SERES', 'https://cdn.fakesite.com/logos/seres.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'SHINERAY')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('SHINERAY', 'https://cdn.fakesite.com/logos/shineray.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'smart')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('smart', 'https://cdn.fakesite.com/logos/smart.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'SSANGYONG')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('SSANGYONG', 'https://cdn.fakesite.com/logos/ssangyong.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Subaru')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Subaru', 'https://cdn.fakesite.com/logos/subaru.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Suzuki')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Suzuki', 'https://cdn.fakesite.com/logos/suzuki.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'TAC')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('TAC', 'https://cdn.fakesite.com/logos/tac.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Toyota')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Toyota', 'https://cdn.fakesite.com/logos/toyota.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Troller')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Troller', 'https://cdn.fakesite.com/logos/troller.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Volvo')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Volvo', 'https://cdn.fakesite.com/logos/volvo.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'VW - VolksWagen')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('VW - VolksWagen', 'https://cdn.fakesite.com/logos/vwvolkswagen.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Wake')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Wake', 'https://cdn.fakesite.com/logos/wake.svg', GETDATE())
        END
    
        IF NOT EXISTS (SELECT 1 FROM Brand WHERE Name = 'Walk')
        BEGIN
            INSERT INTO Brand (Name, Ico, CreatedOn) 
            VALUES ('Walk', 'https://cdn.fakesite.com/logos/walk.svg', GETDATE())
        END";

            Execute.Sql(sql);
        }
    }
}
