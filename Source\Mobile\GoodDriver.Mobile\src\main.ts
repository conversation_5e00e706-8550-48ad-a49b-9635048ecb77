import { bootstrapApplication } from '@angular/platform-browser';
import { provideRouter, withPreloading, PreloadAllModules } from '@angular/router';
import { provideIonicAngular } from '@ionic/angular/standalone';
import { AppComponent } from './app/app.component';
import { AppInitializerService } from './app/core/services/app-initializer.service';
import { APP_INITIALIZER, importProvidersFrom } from '@angular/core';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { authInterceptor } from './app/core/interceptors/auth.interceptor';
import { routes } from './app/app.routes';
import { SessionService } from './app/core/services/session.service';
import { IonicStorageModule } from '@ionic/storage-angular';
import { Drivers } from '@ionic/storage';
import { Capacitor } from '@capacitor/core';
import { CapacitorSQLite, SQLiteConnection } from '@capacitor-community/sqlite';


// Initialize Capacitor SQLite for web platform if needed
const initSQLiteWeb = async () => {
  const platform = Capacitor.getPlatform();
  if (platform === 'web') {
    try {
      // Initialize the web assembly for SQLite
      const jeepSqliteEl = document.createElement('jeep-sqlite') as any;
      document.body.appendChild(jeepSqliteEl);
      await customElements.whenDefined('jeep-sqlite');
      if (jeepSqliteEl.initWebStore) {
        await jeepSqliteEl.initWebStore();
      }
    } catch (error) {
      console.error('Error initializing SQLite for web:', error);
    }
  }
};

// Call the initialization function
initSQLiteWeb().catch(err => console.error('Error in SQLite initialization:', err));

bootstrapApplication(AppComponent, {
  providers: [
    importProvidersFrom(
      IonicStorageModule.forRoot({
        name: 'journeydb',
        driverOrder: [Drivers.IndexedDB, Drivers.LocalStorage]
      })
    ),
    provideHttpClient(withInterceptors([authInterceptor])),
    provideIonicAngular(),
    provideRouter(routes, withPreloading(PreloadAllModules)),
    {
      provide: APP_INITIALIZER,
      useFactory: (appInit: AppInitializerService) => {
        return () => appInit.init();
      },
      deps: [AppInitializerService],
      multi: true,
    },
    // Add SQLite connection as a provider
    {
      provide: SQLiteConnection,
      useFactory: () => new SQLiteConnection(CapacitorSQLite)
    }
  ],
});
