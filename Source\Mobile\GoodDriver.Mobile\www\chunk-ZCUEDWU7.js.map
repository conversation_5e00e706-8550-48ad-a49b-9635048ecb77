{"version": 3, "sources": ["node_modules/uuid/dist/esm-browser/rng.js", "node_modules/uuid/dist/esm-browser/regex.js", "node_modules/uuid/dist/esm-browser/validate.js", "node_modules/uuid/dist/esm-browser/stringify.js", "node_modules/uuid/dist/esm-browser/v4.js", "node_modules/uuid/dist/esm-browser/index.js"], "sourcesContent": ["// Unique ID creation requires a high quality random # generator. In the browser we therefore\n// require the crypto API and do not support built-in fallback to lower quality random number\n// generators (like Math.random()).\nvar getRandomValues;\nvar rnds8 = new Uint8Array(16);\nexport default function rng() {\n  // lazy load so that environments that need to polyfill have a chance to do so\n  if (!getRandomValues) {\n    // getRandomValues needs to be invoked in a context where \"this\" is a Crypto implementation. Also,\n    // find the complete implementation of crypto (msCrypto) on IE11.\n    getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto) || typeof msCrypto !== 'undefined' && typeof msCrypto.getRandomValues === 'function' && msCrypto.getRandomValues.bind(msCrypto);\n    if (!getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n  }\n  return getRandomValues(rnds8);\n}", "export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;", "import REGEX from './regex.js';\nfunction validate(uuid) {\n  return typeof uuid === 'string' && REGEX.test(uuid);\n}\nexport default validate;", "import validate from './validate.js';\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nvar byteToHex = [];\nfor (var i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).substr(1));\n}\nfunction stringify(arr) {\n  var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  var uuid = (byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]]).toLowerCase(); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!validate(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n  return uuid;\n}\nexport default stringify;", "import rng from './rng.js';\nimport stringify from './stringify.js';\nfunction v4(options, buf, offset) {\n  options = options || {};\n  var rnds = options.random || (options.rng || rng)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n    for (var i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n    return buf;\n  }\n  return stringify(rnds);\n}\nexport default v4;", "export { default as v1 } from './v1.js';\nexport { default as v3 } from './v3.js';\nexport { default as v4 } from './v4.js';\nexport { default as v5 } from './v5.js';\nexport { default as NIL } from './nil.js';\nexport { default as version } from './version.js';\nexport { default as validate } from './validate.js';\nexport { default as stringify } from './stringify.js';\nexport { default as parse } from './parse.js';"], "mappings": ";;;;;AAKe,SAAR,MAAuB;AAE5B,MAAI,CAAC,iBAAiB;AAGpB,sBAAkB,OAAO,WAAW,eAAe,OAAO,mBAAmB,OAAO,gBAAgB,KAAK,MAAM,KAAK,OAAO,aAAa,eAAe,OAAO,SAAS,oBAAoB,cAAc,SAAS,gBAAgB,KAAK,QAAQ;AAC/O,QAAI,CAAC,iBAAiB;AACpB,YAAM,IAAI,MAAM,0GAA0G;AAAA,IAC5H;AAAA,EACF;AACA,SAAO,gBAAgB,KAAK;AAC9B;AAhBA,IAGI,iBACA;AAJJ;AAAA;AAAA;AAIA,IAAI,QAAQ,IAAI,WAAW,EAAE;AAAA;AAAA;;;ACJ7B,IAAO;AAAP;AAAA;AAAA;AAAA,IAAO,gBAAQ;AAAA;AAAA;;;ACCf,SAAS,SAAS,MAAM;AACtB,SAAO,OAAO,SAAS,YAAY,cAAM,KAAK,IAAI;AACpD;AAHA,IAIO;AAJP;AAAA;AAAA;AAAA;AAIA,IAAO,mBAAQ;AAAA;AAAA;;;ACMf,SAAS,UAAU,KAAK;AACtB,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAGjF,MAAI,QAAQ,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,GAAG,YAAY;AAMrgB,MAAI,CAAC,iBAAS,IAAI,GAAG;AACnB,UAAM,UAAU,6BAA6B;AAAA,EAC/C;AACA,SAAO;AACT;AAxBA,IAMI,WACK,GAkBF;AAzBP;AAAA;AAAA;AAAA;AAMA,IAAI,YAAY,CAAC;AACjB,SAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,gBAAU,MAAM,IAAI,KAAO,SAAS,EAAE,EAAE,OAAO,CAAC,CAAC;AAAA,IACnD;AAgBA,IAAO,oBAAQ;AAAA;AAAA;;;ACvBf,SAAS,GAAG,SAAS,KAAK,QAAQ;AAChC,YAAU,WAAW,CAAC;AACtB,MAAI,OAAO,QAAQ,WAAW,QAAQ,OAAO,KAAK;AAElD,OAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAO;AAC3B,OAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAO;AAE3B,MAAI,KAAK;AACP,aAAS,UAAU;AACnB,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,UAAI,SAAS,CAAC,IAAI,KAAK,CAAC;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AACA,SAAO,kBAAU,IAAI;AACvB;AAjBA,IAkBO;AAlBP;AAAA;AAAA;AAAA;AACA;AAiBA,IAAO,aAAQ;AAAA;AAAA;;;AClBf;AAAA;AAAA;AAEA;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0, 1, 2, 3, 4, 5]}