import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class GeocodingService {

  constructor() { }

  /**
   * Converte coordenadas em endereço usando múltiplas estratégias
   */
  async reverseGeocode(lat: number, lon: number): Promise<string> {
    try {
      console.log(`Iniciando reverse geocoding para: ${lat}, ${lon}`);

      // Método 1: API BigDataCloud (suporta CORS)
      let address = await this.tryBigDataCloudAPI(lat, lon);
      if (address && address !== 'Endereço não encontrado') {
        return address;
      }

      // Método 2: API OpenCage (se tiver chave)
      address = await this.tryOpenCageAPI(lat, lon);
      if (address && address !== 'Endereço não encontrado') {
        return address;
      }

      // Método 3: Proxy CORS para Nominatim
      address = await this.tryNominatimWithProxy(lat, lon);
      if (address && address !== 'Endereço não encontrado') {
        return address;
      }

      // Método 4: Fallback para coordenadas formatadas
      return this.formatCoordinatesAsAddress(lat, lon);
      
    } catch (error) {
      console.error('Erro geral no reverse geocoding:', error);
      return this.formatCoordinatesAsAddress(lat, lon);
    }
  }

  /**
   * API BigDataCloud - Gratuita e suporta CORS
   */
  private async tryBigDataCloudAPI(lat: number, lon: number): Promise<string> {
    try {
      const url = `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lon}&localityLanguage=pt`;
      
      console.log('Tentando BigDataCloud API...');
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data) {
        const addressParts = [];
        
        // Monta endereço hierárquico
        if (data.locality) addressParts.push(data.locality);
        if (data.city && data.city !== data.locality) addressParts.push(data.city);
        if (data.principalSubdivision) addressParts.push(data.principalSubdivision);
        if (data.countryName) addressParts.push(data.countryName);
        
        if (addressParts.length > 0) {
          const address = addressParts.join(', ');
          console.log('Endereço obtido via BigDataCloud:', address);
          return address;
        }
      }
      
      return 'Endereço não encontrado';
    } catch (error) {
      console.error('Erro na BigDataCloud API:', error);
      return 'Endereço não encontrado';
    }
  }

  /**
   * API OpenCage - Requer chave API (opcional)
   */
  private async tryOpenCageAPI(lat: number, lon: number): Promise<string> {
    try {
      // Substitua 'YOUR_API_KEY' por uma chave real se disponível
      const apiKey = 'YOUR_API_KEY';
      
      if (!apiKey || apiKey === 'YOUR_API_KEY') {
        console.log('OpenCage API key não configurada, pulando...');
        return 'Endereço não encontrado';
      }
      
      const url = `https://api.opencagedata.com/geocode/v1/json?q=${lat}+${lon}&key=${apiKey}&language=pt&pretty=1`;
      
      console.log('Tentando OpenCage API...');
      
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data && data.results && data.results.length > 0) {
        const result = data.results[0];
        const address = result.formatted;
        console.log('Endereço obtido via OpenCage:', address);
        return address;
      }
      
      return 'Endereço não encontrado';
    } catch (error) {
      console.error('Erro na OpenCage API:', error);
      return 'Endereço não encontrado';
    }
  }

  /**
   * Nominatim com proxy CORS
   */
  private async tryNominatimWithProxy(lat: number, lon: number): Promise<string> {
    try {
      const proxyUrl = 'https://api.allorigins.win/get?url=';
      const targetUrl = encodeURIComponent(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lon}&addressdetails=1&accept-language=pt-BR,pt,en`
      );
      const url = proxyUrl + targetUrl;
      
      console.log('Tentando Nominatim com proxy...');
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const proxyData = await response.json();
      
      if (proxyData && proxyData.contents) {
        const data = JSON.parse(proxyData.contents);
        
        if (data && data.display_name) {
          console.log('Endereço obtido via Nominatim (proxy):', data.display_name);
          return data.display_name;
        }
      }
      
      return 'Endereço não encontrado';
    } catch (error) {
      console.error('Erro no Nominatim com proxy:', error);
      return 'Endereço não encontrado';
    }
  }

  /**
   * Formata coordenadas como endereço quando todas as APIs falham
   */
  private formatCoordinatesAsAddress(lat: number, lon: number): string {
    const latDirection = lat >= 0 ? 'N' : 'S';
    const lonDirection = lon >= 0 ? 'L' : 'O';
    
    const latFormatted = Math.abs(lat).toFixed(6);
    const lonFormatted = Math.abs(lon).toFixed(6);
    
    return `${latFormatted}°${latDirection}, ${lonFormatted}°${lonDirection}`;
  }

  /**
   * Verifica se uma string é um endereço válido ou apenas coordenadas
   */
  isValidAddress(address: string): boolean {
    // Se contém apenas números, pontos, vírgulas e direções cardeais, é coordenada
    const coordinatePattern = /^[\d\.\-°NSLO,\s]+$/;
    return !coordinatePattern.test(address);
  }

  /**
   * Obtém endereço com cache simples para evitar requisições repetidas
   */
  private addressCache = new Map<string, string>();

  async getReverseGeocodeWithCache(lat: number, lon: number): Promise<string> {
    const key = `${lat.toFixed(6)},${lon.toFixed(6)}`;
    
    if (this.addressCache.has(key)) {
      console.log('Endereço obtido do cache:', this.addressCache.get(key));
      return this.addressCache.get(key)!;
    }
    
    const address = await this.reverseGeocode(lat, lon);
    this.addressCache.set(key, address);
    
    return address;
  }

  /**
   * Limpa o cache de endereços
   */
  clearCache(): void {
    this.addressCache.clear();
    console.log('Cache de endereços limpo');
  }
}
