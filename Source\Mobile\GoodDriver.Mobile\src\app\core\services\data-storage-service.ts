import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';
import { SQLiteStorageService } from 'src/app/core/storage/sqlite-storage.service';
import { IndexedDBStorageService } from 'src/app/core/storage/indexedDb.storage.service';
// import { IDataStorage } from '../storage/data-storage.interface';

@Injectable({
  providedIn: 'root',
})
export class DataStorageService {
  private storage: IndexedDBStorageService | SQLiteStorageService;

  constructor(
    private platform: Platform,
    private sqlite: SQLiteStorageService,
    private indexeddb: IndexedDBStorageService
  ) {
    const isNativePlatform = this.platform.is('cordova') || this.platform.is('capacitor');
    console.log('Current Platform is Cordova or Capacitor?: ', isNativePlatform ? 'Yes' : 'No');

    // Sempre usar IndexedDB no navegador, SQLite apenas em dispositivos nativos
    this.storage = isNativePlatform ? this.sqlite : this.indexeddb;

    console.log('Selected storage:', isNativePlatform ? 'SQLite' : 'IndexedDB');
  }

  async init() {
    console.log('Inicializando banco de dados...');

    try {
      await this.storage.init();
      console.log('Banco de dados inicializado com sucesso');

      // Log do status do storage
      if (this.storage === this.sqlite) {
        const status = this.sqlite.getStatus();
        console.log('SQLite Status:', status);
      } else {
        console.log('IndexedDB inicializado para navegador');
      }

    } catch (error) {
      console.error('Erro ao inicializar banco de dados:', error);

      // Se SQLite falhar, tentar fallback para IndexedDB
      if (this.storage === this.sqlite) {
        console.log('Tentando fallback para IndexedDB...');
        this.storage = this.indexeddb;
        await this.storage.init();
        console.log('Fallback para IndexedDB realizado com sucesso');
      } else {
        throw error;
      }
    }
  }

  insert(table: string, data: any) {
    return this.storage.insert(table, data);
  }

  select(table: string, condition: string) {
    return this.storage.select(table, condition);
  }

  update(table: string, data: any, condition: string) {
    return this.storage.update(table, data, condition);
  }

  delete(table: string, condition: string) {
    return this.storage.delete(table, condition);
  }

  getDatabase() {
    return this.storage;
  }

  /**
   * Verifica se está usando SQLite
   */
  isUsingSQLite(): boolean {
    return this.storage === this.sqlite;
  }

  /**
   * Verifica se está usando IndexedDB
   */
  isUsingIndexedDB(): boolean {
    return this.storage === this.indexeddb;
  }

  /**
   * Obtém informações sobre o storage atual
   */
  getStorageInfo(): { type: string, isAvailable: boolean, details?: any } {
    if (this.storage === this.sqlite) {
      return {
        type: 'SQLite',
        isAvailable: this.sqlite.isAvailable(),
        details: this.sqlite.getStatus()
      };
    } else {
      return {
        type: 'IndexedDB',
        isAvailable: true,
        details: { platform: 'web' }
      };
    }
  }
}
