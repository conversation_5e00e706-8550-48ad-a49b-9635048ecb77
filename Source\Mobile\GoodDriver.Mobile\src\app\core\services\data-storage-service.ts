import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';
import { SQLiteStorageService } from 'src/app/core/storage/sqlite-storage.service';
import { IndexedDBStorageService } from 'src/app/core/storage/indexedDb.storage.service';
// import { IDataStorage } from '../storage/data-storage.interface';

@Injectable({
  providedIn: 'root',
})
export class DataStorageService {
  private storage: IndexedDBStorageService | SQLiteStorageService;

  constructor(
    private platform: Platform,
    private sqlite: SQLiteStorageService,
    private indexeddb: IndexedDBStorageService
  ) {
    console.log('Current Platform is Cordova or Capacitor?: ', this.platform.is('cordova') || this.platform.is('capacitor') ? 'Yes' : 'No');
    this.storage =
      this.platform.is('cordova') || this.platform.is('capacitor')
        ? this.sqlite
        : this.indexeddb;
  }

  async init() {
    console.log('Inicializando banco de dados...');     // Aqui você pode adicionar outros logs ou código para inicializar o banco de dados.
    await this.storage.init();
  }

  insert(table: string, data: any) {
    return this.storage.insert(table, data);
  }

  select(table: string, condition: string) {
    return this.storage.select(table, condition);
  }

  update(table: string, data: any, condition: string) {
    return this.storage.update(table, data, condition);
  }

  delete(table: string, condition: string) {
    return this.storage.delete(table, condition);
  }

  getDatabase() {
    return this.storage;
  }
}
