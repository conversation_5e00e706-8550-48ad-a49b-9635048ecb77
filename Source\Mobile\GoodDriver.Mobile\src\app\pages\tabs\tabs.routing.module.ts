import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TabsPage } from './tabs.page';
import { HomePage } from './home/<USER>';

const routes: Routes = [
  {
    path: '',
    component: TabsPage,
    children: [
      {
        path: 'home',
        component: HomePage,
      },
      {
        path: 'journeys',
        loadComponent: () =>
          import('./journeys/journey/journey.page').then(m => m.JourneyPage),
      },
      {
        path: 'vehicles',
        loadComponent: () =>
          import('./vehicles/list-vehicles/list-vehicles.page').then(m => m.ListVehiclesPage),
      },
      {
        path: 'vehicles/edit/:id',
        loadComponent: () => import('./vehicles/edit-vehicle/edit-vehicle.page').then(m => m.EditVehiclePage)
      },
      {
        path: 'sync',
        loadComponent: () =>
          import('../../pages/sync/sync.page').then(m => m.SyncPage),
      },
      {
        path: '',
        redirectTo: 'home',
        pathMatch: 'full',
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TabsRoutingModule {}
