﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Folder Include="Brands\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\GoodDriver.Contracts\GoodDriver.Contracts.csproj" />
    <ProjectReference Include="..\GoodDriver.Domain\GoodDriver.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Rogerio.Cqrs">
      <HintPath>..\..\..\..\RogerioFramework\RogerioIT\Rogerio.Cqrs\bin\Debug\net8.0\Rogerio.Cqrs.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
