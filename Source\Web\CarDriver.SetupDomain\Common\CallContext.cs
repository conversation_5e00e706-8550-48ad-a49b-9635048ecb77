﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.SetupDomain.Common
{
    public static class CallContext
    {
        private static ConcurrentDictionary<string, AsyncLocal<object>> state = new ConcurrentDictionary<string, AsyncLocal<object>>();

        public static void SetData(string name, object data)
        {
            state.GetOrAdd(name, (string _) => new AsyncLocal<object>()).Value = data;
        }

        public static object GetData(string name)
        {
            if (!state.TryGetValue(name, out var value))
            {
                return null;
            }

            return value.Value;
        }
    }
}
