{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-tab_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as Host, f as getElement, c as createEvent } from './index-527b9e34.js';\nimport { a as attachComponent } from './framework-delegate-56b467ad.js';\nimport { d as printIonError } from './index-cfd9c1f2.js';\nimport './helpers-d94bc8ad.js';\nconst tabCss = \":host(.tab-hidden){display:none !important}\";\nconst IonTabStyle0 = tabCss;\nconst Tab = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.loaded = false;\n    this.active = false;\n    this.delegate = undefined;\n    this.tab = undefined;\n    this.component = undefined;\n  }\n  async componentWillLoad() {\n    if (this.active) {\n      await this.setActive();\n    }\n  }\n  /** Set the active component for the tab */\n  async setActive() {\n    await this.prepareLazyLoaded();\n    this.active = true;\n  }\n  changeActive(isActive) {\n    if (isActive) {\n      this.prepareLazyLoaded();\n    }\n  }\n  prepareLazyLoaded() {\n    if (!this.loaded && this.component != null) {\n      this.loaded = true;\n      try {\n        return attachComponent(this.delegate, this.el, this.component, ['ion-page']);\n      } catch (e) {\n        printIonError('[ion-tab] - Exception in prepareLazyLoaded:', e);\n      }\n    }\n    return Promise.resolve(undefined);\n  }\n  render() {\n    const {\n      tab,\n      active,\n      component\n    } = this;\n    return h(Host, {\n      key: 'c36c113e74e12b58459df9e3b546ad4856187e90',\n      role: \"tabpanel\",\n      \"aria-hidden\": !active ? 'true' : null,\n      \"aria-labelledby\": `tab-button-${tab}`,\n      class: {\n        'ion-page': component === undefined,\n        'tab-hidden': !active\n      }\n    }, h(\"slot\", {\n      key: '0d7821dac70ba7a12edfb3331988f3df1566cc1a'\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"active\": [\"changeActive\"]\n    };\n  }\n};\nTab.style = IonTabStyle0;\nconst tabsCss = \":host{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:100%;height:100%;contain:layout size style;z-index:0}.tabs-inner{position:relative;-ms-flex:1;flex:1;contain:layout size style}\";\nconst IonTabsStyle0 = tabsCss;\nconst Tabs = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionNavWillLoad = createEvent(this, \"ionNavWillLoad\", 7);\n    this.ionTabsWillChange = createEvent(this, \"ionTabsWillChange\", 3);\n    this.ionTabsDidChange = createEvent(this, \"ionTabsDidChange\", 3);\n    this.transitioning = false;\n    this.onTabClicked = ev => {\n      const {\n        href,\n        tab\n      } = ev.detail;\n      if (this.useRouter && href !== undefined) {\n        const router = document.querySelector('ion-router');\n        if (router) {\n          router.push(href);\n        }\n      } else {\n        this.select(tab);\n      }\n    };\n    this.selectedTab = undefined;\n    this.useRouter = false;\n  }\n  async componentWillLoad() {\n    if (!this.useRouter) {\n      /**\n       * JavaScript and StencilJS use `ion-router`, while\n       * the other frameworks use `ion-router-outlet`.\n       *\n       * If either component is present then tabs will not use\n       * a basic tab-based navigation. It will use the history\n       * stack or URL updates associated with the router.\n       */\n      this.useRouter = (!!this.el.querySelector('ion-router-outlet') || !!document.querySelector('ion-router')) && !this.el.closest('[no-router]');\n    }\n    if (!this.useRouter) {\n      const tabs = this.tabs;\n      if (tabs.length > 0) {\n        await this.select(tabs[0]);\n      }\n    }\n    this.ionNavWillLoad.emit();\n  }\n  componentWillRender() {\n    const tabBar = this.el.querySelector('ion-tab-bar');\n    if (tabBar) {\n      const tab = this.selectedTab ? this.selectedTab.tab : undefined;\n      tabBar.selectedTab = tab;\n    }\n  }\n  /**\n   * Select a tab by the value of its `tab` property or an element reference. This method is only available for vanilla JavaScript projects. The Angular, React, and Vue implementations of tabs are coupled to each framework's router.\n   *\n   * @param tab The tab instance to select. If passed a string, it should be the value of the tab's `tab` property.\n   */\n  async select(tab) {\n    const selectedTab = getTab(this.tabs, tab);\n    if (!this.shouldSwitch(selectedTab)) {\n      return false;\n    }\n    await this.setActive(selectedTab);\n    await this.notifyRouter();\n    this.tabSwitch();\n    return true;\n  }\n  /**\n   * Get a specific tab by the value of its `tab` property or an element reference. This method is only available for vanilla JavaScript projects. The Angular, React, and Vue implementations of tabs are coupled to each framework's router.\n   *\n   * @param tab The tab instance to select. If passed a string, it should be the value of the tab's `tab` property.\n   */\n  async getTab(tab) {\n    return getTab(this.tabs, tab);\n  }\n  /**\n   * Get the currently selected tab. This method is only available for vanilla JavaScript projects. The Angular, React, and Vue implementations of tabs are coupled to each framework's router.\n   */\n  getSelected() {\n    return Promise.resolve(this.selectedTab ? this.selectedTab.tab : undefined);\n  }\n  /** @internal */\n  async setRouteId(id) {\n    const selectedTab = getTab(this.tabs, id);\n    if (!this.shouldSwitch(selectedTab)) {\n      return {\n        changed: false,\n        element: this.selectedTab\n      };\n    }\n    await this.setActive(selectedTab);\n    return {\n      changed: true,\n      element: this.selectedTab,\n      markVisible: () => this.tabSwitch()\n    };\n  }\n  /** @internal */\n  async getRouteId() {\n    var _a;\n    const tabId = (_a = this.selectedTab) === null || _a === void 0 ? void 0 : _a.tab;\n    return tabId !== undefined ? {\n      id: tabId,\n      element: this.selectedTab\n    } : undefined;\n  }\n  setActive(selectedTab) {\n    if (this.transitioning) {\n      return Promise.reject('transitioning already happening');\n    }\n    this.transitioning = true;\n    this.leavingTab = this.selectedTab;\n    this.selectedTab = selectedTab;\n    this.ionTabsWillChange.emit({\n      tab: selectedTab.tab\n    });\n    selectedTab.active = true;\n    return Promise.resolve();\n  }\n  tabSwitch() {\n    const selectedTab = this.selectedTab;\n    const leavingTab = this.leavingTab;\n    this.leavingTab = undefined;\n    this.transitioning = false;\n    if (!selectedTab) {\n      return;\n    }\n    if (leavingTab !== selectedTab) {\n      if (leavingTab) {\n        leavingTab.active = false;\n      }\n      this.ionTabsDidChange.emit({\n        tab: selectedTab.tab\n      });\n    }\n  }\n  notifyRouter() {\n    if (this.useRouter) {\n      const router = document.querySelector('ion-router');\n      if (router) {\n        return router.navChanged('forward');\n      }\n    }\n    return Promise.resolve(false);\n  }\n  shouldSwitch(selectedTab) {\n    const leavingTab = this.selectedTab;\n    return selectedTab !== undefined && selectedTab !== leavingTab && !this.transitioning;\n  }\n  get tabs() {\n    return Array.from(this.el.querySelectorAll('ion-tab'));\n  }\n  render() {\n    return h(Host, {\n      key: '20b97196d78c1b3f3faf31618a8a2347e087f06b',\n      onIonTabButtonClick: this.onTabClicked\n    }, h(\"slot\", {\n      key: 'b0823fbae6e47743cfd12c376b365ad7e32cec7c',\n      name: \"top\"\n    }), h(\"div\", {\n      key: 'eaffd7e4d69ab9489a387e3bbb36e3bab72203a0',\n      class: \"tabs-inner\"\n    }, h(\"slot\", {\n      key: '20bb66a2937e3ec473aa59c4075ce581b5411677'\n    })), h(\"slot\", {\n      key: '1529dd361f050f52074f51c73b3982ba827dc3a5',\n      name: \"bottom\"\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nconst getTab = (tabs, tab) => {\n  const tabEl = typeof tab === 'string' ? tabs.find(t => t.tab === tab) : tab;\n  if (!tabEl) {\n    printIonError(`[ion-tabs] - Tab with id: \"${tabEl}\" does not exist`);\n  }\n  return tabEl;\n};\nTabs.style = IonTabsStyle0;\nexport { Tab as ion_tab, Tabs as ion_tabs };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAOM,QACA,cACA,KAgEA,SACA,eACA,MA4KA;AAvPN;AAAA;AAGA;AACA;AACA;AACA;AACA,IAAM,SAAS;AACf,IAAM,eAAe;AACrB,IAAM,MAAM,MAAM;AAAA,MAChB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,SAAS;AACd,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,aAAK,MAAM;AACX,aAAK,YAAY;AAAA,MACnB;AAAA,MACM,oBAAoB;AAAA;AACxB,cAAI,KAAK,QAAQ;AACf,kBAAM,KAAK,UAAU;AAAA,UACvB;AAAA,QACF;AAAA;AAAA;AAAA,MAEM,YAAY;AAAA;AAChB,gBAAM,KAAK,kBAAkB;AAC7B,eAAK,SAAS;AAAA,QAChB;AAAA;AAAA,MACA,aAAa,UAAU;AACrB,YAAI,UAAU;AACZ,eAAK,kBAAkB;AAAA,QACzB;AAAA,MACF;AAAA,MACA,oBAAoB;AAClB,YAAI,CAAC,KAAK,UAAU,KAAK,aAAa,MAAM;AAC1C,eAAK,SAAS;AACd,cAAI;AACF,mBAAO,gBAAgB,KAAK,UAAU,KAAK,IAAI,KAAK,WAAW,CAAC,UAAU,CAAC;AAAA,UAC7E,SAAS,GAAG;AACV,0BAAc,+CAA+C,CAAC;AAAA,UAChE;AAAA,QACF;AACA,eAAO,QAAQ,QAAQ,MAAS;AAAA,MAClC;AAAA,MACA,SAAS;AACP,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,MAAM;AAAA,UACN,eAAe,CAAC,SAAS,SAAS;AAAA,UAClC,mBAAmB,cAAc,GAAG;AAAA,UACpC,OAAO;AAAA,YACL,YAAY,cAAc;AAAA,YAC1B,cAAc,CAAC;AAAA,UACjB;AAAA,QACF,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,QACP,CAAC,CAAC;AAAA,MACJ;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,UAAU,CAAC,cAAc;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AACA,QAAI,QAAQ;AACZ,IAAM,UAAU;AAChB,IAAM,gBAAgB;AACtB,IAAM,OAAO,MAAM;AAAA,MACjB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,aAAK,oBAAoB,YAAY,MAAM,qBAAqB,CAAC;AACjE,aAAK,mBAAmB,YAAY,MAAM,oBAAoB,CAAC;AAC/D,aAAK,gBAAgB;AACrB,aAAK,eAAe,QAAM;AACxB,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,GAAG;AACP,cAAI,KAAK,aAAa,SAAS,QAAW;AACxC,kBAAM,SAAS,SAAS,cAAc,YAAY;AAClD,gBAAI,QAAQ;AACV,qBAAO,KAAK,IAAI;AAAA,YAClB;AAAA,UACF,OAAO;AACL,iBAAK,OAAO,GAAG;AAAA,UACjB;AAAA,QACF;AACA,aAAK,cAAc;AACnB,aAAK,YAAY;AAAA,MACnB;AAAA,MACM,oBAAoB;AAAA;AACxB,cAAI,CAAC,KAAK,WAAW;AASnB,iBAAK,aAAa,CAAC,CAAC,KAAK,GAAG,cAAc,mBAAmB,KAAK,CAAC,CAAC,SAAS,cAAc,YAAY,MAAM,CAAC,KAAK,GAAG,QAAQ,aAAa;AAAA,UAC7I;AACA,cAAI,CAAC,KAAK,WAAW;AACnB,kBAAM,OAAO,KAAK;AAClB,gBAAI,KAAK,SAAS,GAAG;AACnB,oBAAM,KAAK,OAAO,KAAK,CAAC,CAAC;AAAA,YAC3B;AAAA,UACF;AACA,eAAK,eAAe,KAAK;AAAA,QAC3B;AAAA;AAAA,MACA,sBAAsB;AACpB,cAAM,SAAS,KAAK,GAAG,cAAc,aAAa;AAClD,YAAI,QAAQ;AACV,gBAAM,MAAM,KAAK,cAAc,KAAK,YAAY,MAAM;AACtD,iBAAO,cAAc;AAAA,QACvB;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMM,OAAO,KAAK;AAAA;AAChB,gBAAM,cAAc,OAAO,KAAK,MAAM,GAAG;AACzC,cAAI,CAAC,KAAK,aAAa,WAAW,GAAG;AACnC,mBAAO;AAAA,UACT;AACA,gBAAM,KAAK,UAAU,WAAW;AAChC,gBAAM,KAAK,aAAa;AACxB,eAAK,UAAU;AACf,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMM,OAAO,KAAK;AAAA;AAChB,iBAAO,OAAO,KAAK,MAAM,GAAG;AAAA,QAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,MAIA,cAAc;AACZ,eAAO,QAAQ,QAAQ,KAAK,cAAc,KAAK,YAAY,MAAM,MAAS;AAAA,MAC5E;AAAA;AAAA,MAEM,WAAW,IAAI;AAAA;AACnB,gBAAM,cAAc,OAAO,KAAK,MAAM,EAAE;AACxC,cAAI,CAAC,KAAK,aAAa,WAAW,GAAG;AACnC,mBAAO;AAAA,cACL,SAAS;AAAA,cACT,SAAS,KAAK;AAAA,YAChB;AAAA,UACF;AACA,gBAAM,KAAK,UAAU,WAAW;AAChC,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,SAAS,KAAK;AAAA,YACd,aAAa,MAAM,KAAK,UAAU;AAAA,UACpC;AAAA,QACF;AAAA;AAAA;AAAA,MAEM,aAAa;AAAA;AACjB,cAAI;AACJ,gBAAM,SAAS,KAAK,KAAK,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC9E,iBAAO,UAAU,SAAY;AAAA,YAC3B,IAAI;AAAA,YACJ,SAAS,KAAK;AAAA,UAChB,IAAI;AAAA,QACN;AAAA;AAAA,MACA,UAAU,aAAa;AACrB,YAAI,KAAK,eAAe;AACtB,iBAAO,QAAQ,OAAO,iCAAiC;AAAA,QACzD;AACA,aAAK,gBAAgB;AACrB,aAAK,aAAa,KAAK;AACvB,aAAK,cAAc;AACnB,aAAK,kBAAkB,KAAK;AAAA,UAC1B,KAAK,YAAY;AAAA,QACnB,CAAC;AACD,oBAAY,SAAS;AACrB,eAAO,QAAQ,QAAQ;AAAA,MACzB;AAAA,MACA,YAAY;AACV,cAAM,cAAc,KAAK;AACzB,cAAM,aAAa,KAAK;AACxB,aAAK,aAAa;AAClB,aAAK,gBAAgB;AACrB,YAAI,CAAC,aAAa;AAChB;AAAA,QACF;AACA,YAAI,eAAe,aAAa;AAC9B,cAAI,YAAY;AACd,uBAAW,SAAS;AAAA,UACtB;AACA,eAAK,iBAAiB,KAAK;AAAA,YACzB,KAAK,YAAY;AAAA,UACnB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,eAAe;AACb,YAAI,KAAK,WAAW;AAClB,gBAAM,SAAS,SAAS,cAAc,YAAY;AAClD,cAAI,QAAQ;AACV,mBAAO,OAAO,WAAW,SAAS;AAAA,UACpC;AAAA,QACF;AACA,eAAO,QAAQ,QAAQ,KAAK;AAAA,MAC9B;AAAA,MACA,aAAa,aAAa;AACxB,cAAM,aAAa,KAAK;AACxB,eAAO,gBAAgB,UAAa,gBAAgB,cAAc,CAAC,KAAK;AAAA,MAC1E;AAAA,MACA,IAAI,OAAO;AACT,eAAO,MAAM,KAAK,KAAK,GAAG,iBAAiB,SAAS,CAAC;AAAA,MACvD;AAAA,MACA,SAAS;AACP,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,qBAAqB,KAAK;AAAA,QAC5B,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,UACL,MAAM;AAAA,QACR,CAAC,GAAG,EAAE,OAAO;AAAA,UACX,KAAK;AAAA,UACL,OAAO;AAAA,QACT,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,QACP,CAAC,CAAC,GAAG,EAAE,QAAQ;AAAA,UACb,KAAK;AAAA,UACL,MAAM;AAAA,QACR,CAAC,CAAC;AAAA,MACJ;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,IACF;AACA,IAAM,SAAS,CAAC,MAAM,QAAQ;AAC5B,YAAM,QAAQ,OAAO,QAAQ,WAAW,KAAK,KAAK,OAAK,EAAE,QAAQ,GAAG,IAAI;AACxE,UAAI,CAAC,OAAO;AACV,sBAAc,8BAA8B,KAAK,kBAAkB;AAAA,MACrE;AACA,aAAO;AAAA,IACT;AACA,SAAK,QAAQ;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}