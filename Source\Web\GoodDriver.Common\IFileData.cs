﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Common
{
    public interface IFileData
    {
        string ParamName { get; set; }

        string Name { get; set; }

        string Url { get; set; }

        string ContentType { get; set; }

        Stream Stream { get; set; }

        string Extension { get; }

        long Length { get; set; }
    }
}
