{"version": 3, "sources": ["src/app/shared/components/sync-status/sync-status.component.scss"], "sourcesContent": ["ion-card {\n  margin: var(--app-spacing-md);\n  \n  ion-card-header {\n    ion-card-title {\n      display: flex;\n      align-items: center;\n      \n      ion-icon {\n        margin-right: var(--app-spacing-sm);\n      }\n    }\n  }\n  \n  ion-card-content {\n    ion-item {\n      --padding-start: 0;\n      \n      ion-icon {\n        font-size: 1.5rem;\n      }\n      \n      h2 {\n        font-weight: 500;\n        margin-bottom: var(--app-spacing-xs);\n      }\n      \n      p {\n        color: var(--ion-color-medium);\n      }\n    }\n    \n    ion-progress-bar {\n      margin: var(--app-spacing-md) 0;\n    }\n  }\n}\n"], "mappings": ";AAAA;AACE,UAAA,IAAA;;AAGE,SAAA,gBAAA;AACE,WAAA;AACA,eAAA;;AAEA,SAAA,gBAAA,eAAA;AACE,gBAAA,IAAA;;AAMJ,SAAA,iBAAA;AACE,mBAAA;;AAEA,SAAA,iBAAA,SAAA;AACE,aAAA;;AAGF,SAAA,iBAAA,SAAA;AACE,eAAA;AACA,iBAAA,IAAA;;AAGF,SAAA,iBAAA,SAAA;AACE,SAAA,IAAA;;AAIJ,SAAA,iBAAA;AACE,UAAA,IAAA,kBAAA;;", "names": []}