﻿using GoodDriver.SetupDomain.UnitOfWork;
using Microsoft.Extensions.Configuration;
using System.Data.Common;
using System.Data;
using GoodDriver.SetupDomain.Data;
using NHibernate;

namespace GoodDriver.SetupDomain.NHibernate
{
    public class NHibernateDbInterceptor : BaseDataAccess
    {
        protected IUnitOfWorkFactory<ISession> UnitOfWorkFactory { get; private set; }

        public NHibernateDbInterceptor(IConfiguration configuration)
            : base(configuration)
        {
            UnitOfWorkFactory = null;
        }

        public NHibernateDbInterceptor(IConfiguration configuration, IUnitOfWorkFactory<ISession> unitOfWorkFactory)
            : base(configuration)
        {
            UnitOfWorkFactory = unitOfWorkFactory;
        }

        public async Task Connect(Func<IDbConnection, IDbTransaction, Task> action)
        {
            if (UnitOfWorkFactory != null)
            {
                using IUnitOfWork<ISession> unitOfWork = UnitOfWorkFactory.Get();
                DbConnection connection2 = unitOfWork.Context.Connection;
                bool isOpen = false;
                try
                {
                    if (connection2.State != ConnectionState.Open)
                    {
                        connection2.Open();
                        isOpen = true;
                    }

                    using DbCommand command = connection2.CreateCommand();
                    unitOfWork.Context.GetCurrentTransaction()?.Enlist(command);
                    await action(connection2, command.Transaction);
                }
                finally
                {
                    if (isOpen)
                    {
                        connection2.Close();
                    }
                }
            }
            else
            {
                using IDbConnection connection = CreateConnection();
                await action(connection, null);
            }
        }
    }
}
