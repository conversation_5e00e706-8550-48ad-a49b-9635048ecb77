<!-- src/app/pages/tabs/vehicles/list-vehicles/list-vehicles.page.html -->
<ion-header>
  <ion-toolbar>
    <ion-title>Veículos</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
  <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <ion-button expand="block" color="primary" routerLink="/new-vehicle">
    Cadastrar Novo Veículo
  </ion-button>

  <!-- Indicador de carregamento -->
  <div *ngIf="isLoading" class="ion-text-center ion-padding">
    <ion-spinner></ion-spinner>
  </div>

  <!-- Lista de veículos-->
  <ion-list *ngIf="vehicles?.length && !isLoading; else noVehicles">
    <ion-item-sliding *ngFor="let vehicle of vehicles">
      <ion-item [routerLink]="['/vehicle-details', vehicle.id]" detail="true">
        <ion-label>
          <div class="vehicle-header">
            <h2>{{ vehicle.plate }}</h2>
            <ion-badge color="success" *ngIf="vehicle.isPrimary">Principal</ion-badge>
          </div>
          <p><b>{{ vehicle.brandName || 'Marca não especificada' }}</b> - {{ vehicle.modelName || 'Modelo não especificado' }}</p>
          <p>Ano: {{ vehicle.year }}</p>
        </ion-label>
      </ion-item>

      <ion-item-options side="end">
        <ion-item-option color="primary" *ngIf="!vehicle.isPrimary" (click)="setPrimaryVehicle(vehicle.id)">
          <ion-icon slot="icon-only" name="star"></ion-icon>
          Principal
        </ion-item-option>
      </ion-item-options>
    </ion-item-sliding>
  </ion-list>

  <!-- Mensagem caso não existam veículos -->
  <ng-template #noVehicles>
    <ion-text color="medium" *ngIf="!isLoading">
      <p class="ion-text-center">Nenhum veículo cadastrado.</p>
    </ion-text>
  </ng-template>
</ion-content>