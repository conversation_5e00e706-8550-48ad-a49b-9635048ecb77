﻿using Rogerio.Cqrs.Commands;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Contracts.Vehicles.Commands
{
    public class VehicleCreateCommand : ICommand
    {
        public VehicleCreateCommand(string vehicleId, string userId, string plate, int year, int brandId, int modelId)
        {
            if (string.IsNullOrEmpty(vehicleId))
                throw new ArgumentNullException(nameof(vehicleId));

            if (string.IsNullOrEmpty(userId))
                throw new ArgumentNullException(nameof(userId));

            if (string.IsNullOrEmpty(plate))
                throw new ArgumentNullException(nameof(plate));
            
            if (year < 1980 || year > DateTime.Now.Year)
                throw new ArgumentOutOfRangeException(nameof(year), "Ano deve estar entre 1990 and o ano atual.");

            if (brandId <= 0)
                throw new ArgumentNullException(nameof(brandId));

            if (modelId <= 0)
                throw new ArgumentNullException(nameof(modelId));

            UserId = userId;
            Plate = plate;
            Year = year;
            BrandId = brandId;
            ModelId = modelId;
            VehicleId = vehicleId;
        }

        public string UserId { get; set; }
        public string Plate { get; set; }
        public int Year { get; set; }
        public int BrandId { get; set; }
        public int ModelId { get; set; }

        public string VehicleId { get; set; }

    }
}
