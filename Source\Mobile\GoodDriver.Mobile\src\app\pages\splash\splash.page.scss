.splash {
  --background: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.1) 0%, rgba(255, 255, 255, 1) 100%);

  .splash-content {
    text-align: center;
    animation: fadeIn 1.2s ease-in-out;

    .logo {
      width: 180px;
      height: 180px;
      margin-bottom: 24px;
      animation: pulse 2s infinite;
    }

    h1 {
      font-size: 32px;
      font-weight: 600;
      color: var(--ion-color-primary);
      margin-bottom: 8px;
    }

    p {
      font-size: 18px;
      color: var(--ion-color-secondary);
      margin-top: 0;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}