{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-spinner.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as Host } from './index-527b9e34.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { c as config } from './index-cfd9c1f2.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport { S as SPINNERS } from './spinner-configs-964f7cf3.js';\nconst spinnerCss = \":host{display:inline-block;position:relative;width:28px;height:28px;color:var(--color);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}:host(.ion-color){color:var(--ion-color-base)}svg{-webkit-transform-origin:center;transform-origin:center;position:absolute;top:0;left:0;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0)}:host-context([dir=rtl]) svg{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}[dir=rtl] svg{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}@supports selector(:dir(rtl)){svg:dir(rtl){-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}}:host(.spinner-lines) line,:host(.spinner-lines-small) line{stroke-width:7px}:host(.spinner-lines-sharp) line,:host(.spinner-lines-sharp-small) line{stroke-width:4px}:host(.spinner-lines) line,:host(.spinner-lines-small) line,:host(.spinner-lines-sharp) line,:host(.spinner-lines-sharp-small) line{stroke-linecap:round;stroke:currentColor}:host(.spinner-lines) svg,:host(.spinner-lines-small) svg,:host(.spinner-lines-sharp) svg,:host(.spinner-lines-sharp-small) svg{-webkit-animation:spinner-fade-out 1s linear infinite;animation:spinner-fade-out 1s linear infinite}:host(.spinner-bubbles) svg{-webkit-animation:spinner-scale-out 1s linear infinite;animation:spinner-scale-out 1s linear infinite;fill:currentColor}:host(.spinner-circles) svg{-webkit-animation:spinner-fade-out 1s linear infinite;animation:spinner-fade-out 1s linear infinite;fill:currentColor}:host(.spinner-crescent) circle{fill:transparent;stroke-width:4px;stroke-dasharray:128px;stroke-dashoffset:82px;stroke:currentColor}:host(.spinner-crescent) svg{-webkit-animation:spinner-rotate 1s linear infinite;animation:spinner-rotate 1s linear infinite}:host(.spinner-dots) circle{stroke-width:0;fill:currentColor}:host(.spinner-dots) svg{-webkit-animation:spinner-dots 1s linear infinite;animation:spinner-dots 1s linear infinite}:host(.spinner-circular) svg{-webkit-animation:spinner-circular linear infinite;animation:spinner-circular linear infinite}:host(.spinner-circular) circle{-webkit-animation:spinner-circular-inner ease-in-out infinite;animation:spinner-circular-inner ease-in-out infinite;stroke:currentColor;stroke-dasharray:80px, 200px;stroke-dashoffset:0px;stroke-width:5.6;fill:none}:host(.spinner-paused),:host(.spinner-paused) svg,:host(.spinner-paused) circle{-webkit-animation-play-state:paused;animation-play-state:paused}@-webkit-keyframes spinner-fade-out{0%{opacity:1}100%{opacity:0}}@keyframes spinner-fade-out{0%{opacity:1}100%{opacity:0}}@-webkit-keyframes spinner-scale-out{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1)}100%{-webkit-transform:scale(0, 0);transform:scale(0, 0)}}@keyframes spinner-scale-out{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1)}100%{-webkit-transform:scale(0, 0);transform:scale(0, 0)}}@-webkit-keyframes spinner-rotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spinner-rotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes spinner-dots{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}50%{-webkit-transform:scale(0.4, 0.4);transform:scale(0.4, 0.4);opacity:0.3}100%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}}@keyframes spinner-dots{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}50%{-webkit-transform:scale(0.4, 0.4);transform:scale(0.4, 0.4);opacity:0.3}100%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}}@-webkit-keyframes spinner-circular{100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spinner-circular{100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes spinner-circular-inner{0%{stroke-dasharray:1px, 200px;stroke-dashoffset:0px}50%{stroke-dasharray:100px, 200px;stroke-dashoffset:-15px}100%{stroke-dasharray:100px, 200px;stroke-dashoffset:-125px}}@keyframes spinner-circular-inner{0%{stroke-dasharray:1px, 200px;stroke-dashoffset:0px}50%{stroke-dasharray:100px, 200px;stroke-dashoffset:-15px}100%{stroke-dasharray:100px, 200px;stroke-dashoffset:-125px}}\";\nconst IonSpinnerStyle0 = spinnerCss;\nconst Spinner = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.color = undefined;\n    this.duration = undefined;\n    this.name = undefined;\n    this.paused = false;\n  }\n  getName() {\n    const spinnerName = this.name || config.get('spinner');\n    const mode = getIonMode(this);\n    if (spinnerName) {\n      return spinnerName;\n    }\n    return mode === 'ios' ? 'lines' : 'circular';\n  }\n  render() {\n    var _a;\n    const self = this;\n    const mode = getIonMode(self);\n    const spinnerName = self.getName();\n    const spinner = (_a = SPINNERS[spinnerName]) !== null && _a !== void 0 ? _a : SPINNERS['lines'];\n    const duration = typeof self.duration === 'number' && self.duration > 10 ? self.duration : spinner.dur;\n    const svgs = [];\n    if (spinner.circles !== undefined) {\n      for (let i = 0; i < spinner.circles; i++) {\n        svgs.push(buildCircle(spinner, duration, i, spinner.circles));\n      }\n    } else if (spinner.lines !== undefined) {\n      for (let i = 0; i < spinner.lines; i++) {\n        svgs.push(buildLine(spinner, duration, i, spinner.lines));\n      }\n    }\n    return h(Host, {\n      key: 'e0dfa8a3ee2a0469eb31323f506750bd77d65797',\n      class: createColorClasses(self.color, {\n        [mode]: true,\n        [`spinner-${spinnerName}`]: true,\n        'spinner-paused': self.paused || config.getBoolean('_testing')\n      }),\n      role: \"progressbar\",\n      style: spinner.elmDuration ? {\n        animationDuration: duration + 'ms'\n      } : {}\n    }, svgs);\n  }\n};\nconst buildCircle = (spinner, duration, index, total) => {\n  const data = spinner.fn(duration, index, total);\n  data.style['animation-duration'] = duration + 'ms';\n  return h(\"svg\", {\n    viewBox: data.viewBox || '0 0 64 64',\n    style: data.style\n  }, h(\"circle\", {\n    transform: data.transform || 'translate(32,32)',\n    cx: data.cx,\n    cy: data.cy,\n    r: data.r,\n    style: spinner.elmDuration ? {\n      animationDuration: duration + 'ms'\n    } : {}\n  }));\n};\nconst buildLine = (spinner, duration, index, total) => {\n  const data = spinner.fn(duration, index, total);\n  data.style['animation-duration'] = duration + 'ms';\n  return h(\"svg\", {\n    viewBox: data.viewBox || '0 0 64 64',\n    style: data.style\n  }, h(\"line\", {\n    transform: \"translate(32,32)\",\n    y1: data.y1,\n    y2: data.y2\n  }));\n};\nSpinner.style = IonSpinnerStyle0;\nexport { Spinner as ion_spinner };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAQM,YACA,kBACA,SA+CA,aAgBA;AAzEN;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA,IAAM,aAAa;AACnB,IAAM,mBAAmB;AACzB,IAAM,UAAU,MAAM;AAAA,MACpB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,QAAQ;AACb,aAAK,WAAW;AAChB,aAAK,OAAO;AACZ,aAAK,SAAS;AAAA,MAChB;AAAA,MACA,UAAU;AACR,cAAM,cAAc,KAAK,QAAQ,OAAO,IAAI,SAAS;AACrD,cAAM,OAAO,WAAW,IAAI;AAC5B,YAAI,aAAa;AACf,iBAAO;AAAA,QACT;AACA,eAAO,SAAS,QAAQ,UAAU;AAAA,MACpC;AAAA,MACA,SAAS;AACP,YAAI;AACJ,cAAM,OAAO;AACb,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM,cAAc,KAAK,QAAQ;AACjC,cAAM,WAAW,KAAK,SAAS,WAAW,OAAO,QAAQ,OAAO,SAAS,KAAK,SAAS,OAAO;AAC9F,cAAM,WAAW,OAAO,KAAK,aAAa,YAAY,KAAK,WAAW,KAAK,KAAK,WAAW,QAAQ;AACnG,cAAM,OAAO,CAAC;AACd,YAAI,QAAQ,YAAY,QAAW;AACjC,mBAAS,IAAI,GAAG,IAAI,QAAQ,SAAS,KAAK;AACxC,iBAAK,KAAK,YAAY,SAAS,UAAU,GAAG,QAAQ,OAAO,CAAC;AAAA,UAC9D;AAAA,QACF,WAAW,QAAQ,UAAU,QAAW;AACtC,mBAAS,IAAI,GAAG,IAAI,QAAQ,OAAO,KAAK;AACtC,iBAAK,KAAK,UAAU,SAAS,UAAU,GAAG,QAAQ,KAAK,CAAC;AAAA,UAC1D;AAAA,QACF;AACA,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,OAAO,mBAAmB,KAAK,OAAO;AAAA,YACpC,CAAC,IAAI,GAAG;AAAA,YACR,CAAC,WAAW,WAAW,EAAE,GAAG;AAAA,YAC5B,kBAAkB,KAAK,UAAU,OAAO,WAAW,UAAU;AAAA,UAC/D,CAAC;AAAA,UACD,MAAM;AAAA,UACN,OAAO,QAAQ,cAAc;AAAA,YAC3B,mBAAmB,WAAW;AAAA,UAChC,IAAI,CAAC;AAAA,QACP,GAAG,IAAI;AAAA,MACT;AAAA,IACF;AACA,IAAM,cAAc,CAAC,SAAS,UAAU,OAAO,UAAU;AACvD,YAAM,OAAO,QAAQ,GAAG,UAAU,OAAO,KAAK;AAC9C,WAAK,MAAM,oBAAoB,IAAI,WAAW;AAC9C,aAAO,EAAE,OAAO;AAAA,QACd,SAAS,KAAK,WAAW;AAAA,QACzB,OAAO,KAAK;AAAA,MACd,GAAG,EAAE,UAAU;AAAA,QACb,WAAW,KAAK,aAAa;AAAA,QAC7B,IAAI,KAAK;AAAA,QACT,IAAI,KAAK;AAAA,QACT,GAAG,KAAK;AAAA,QACR,OAAO,QAAQ,cAAc;AAAA,UAC3B,mBAAmB,WAAW;AAAA,QAChC,IAAI,CAAC;AAAA,MACP,CAAC,CAAC;AAAA,IACJ;AACA,IAAM,YAAY,CAAC,SAAS,UAAU,OAAO,UAAU;AACrD,YAAM,OAAO,QAAQ,GAAG,UAAU,OAAO,KAAK;AAC9C,WAAK,MAAM,oBAAoB,IAAI,WAAW;AAC9C,aAAO,EAAE,OAAO;AAAA,QACd,SAAS,KAAK,WAAW;AAAA,QACzB,OAAO,KAAK;AAAA,MACd,GAAG,EAAE,QAAQ;AAAA,QACX,WAAW;AAAA,QACX,IAAI,KAAK;AAAA,QACT,IAAI,KAAK;AAAA,MACX,CAAC,CAAC;AAAA,IACJ;AACA,YAAQ,QAAQ;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}