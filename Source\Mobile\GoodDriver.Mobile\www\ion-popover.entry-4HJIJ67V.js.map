{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-popover.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { B as BACKDROP, j as prepareOverlay, k as setOverlayId, f as present, n as focusFirstDescendant, g as dismiss, h as eventMethod, F as FOCUS_TRAP_DISABLE_CLASS } from './overlays-d99dcb0a.js';\nimport { C as CoreDelegate, a as attachComponent, d as detachComponent } from './framework-delegate-56b467ad.js';\nimport { r as raf, g as getElementRoot, a as addEventListener, k as hasLazyBuild } from './helpers-d94bc8ad.js';\nimport { c as createLockController } from './lock-controller-316928be.js';\nimport { p as printIonWarning } from './index-cfd9c1f2.js';\nimport { b as getIonMode, a as isPlatform } from './ionic-global-b26f573e.js';\nimport { g as getClassMap } from './theme-01f3f29c.js';\nimport { e as deepReady, w as waitForMount } from './index-68c0d151.js';\nimport { c as createAnimation } from './animation-8b25e105.js';\nimport './index-a5d50daf.js';\nimport './hardware-back-button-a7eb8233.js';\nimport './gesture-controller-314a54f6.js';\n\n/**\n * Returns the dimensions of the popover\n * arrow on `ios` mode. If arrow is disabled\n * returns (0, 0).\n */\nconst getArrowDimensions = arrowEl => {\n  if (!arrowEl) {\n    return {\n      arrowWidth: 0,\n      arrowHeight: 0\n    };\n  }\n  const {\n    width,\n    height\n  } = arrowEl.getBoundingClientRect();\n  return {\n    arrowWidth: width,\n    arrowHeight: height\n  };\n};\n/**\n * Returns the recommended dimensions of the popover\n * that takes into account whether or not the width\n * should match the trigger width.\n */\nconst getPopoverDimensions = (size, contentEl, triggerEl) => {\n  const contentDimentions = contentEl.getBoundingClientRect();\n  const contentHeight = contentDimentions.height;\n  let contentWidth = contentDimentions.width;\n  if (size === 'cover' && triggerEl) {\n    const triggerDimensions = triggerEl.getBoundingClientRect();\n    contentWidth = triggerDimensions.width;\n  }\n  return {\n    contentWidth,\n    contentHeight\n  };\n};\nconst configureDismissInteraction = (triggerEl, triggerAction, popoverEl, parentPopoverEl) => {\n  let dismissCallbacks = [];\n  const root = getElementRoot(parentPopoverEl);\n  const parentContentEl = root.querySelector('.popover-content');\n  switch (triggerAction) {\n    case 'hover':\n      dismissCallbacks = [{\n        /**\n         * Do not use mouseover here\n         * as this will causes the event to\n         * be dispatched on each underlying\n         * element rather than on the popover\n         * content as a whole.\n         */\n        eventName: 'mouseenter',\n        callback: ev => {\n          /**\n           * Do not dismiss the popover is we\n           * are hovering over its trigger.\n           * This would be easier if we used mouseover\n           * but this would cause the event to be dispatched\n           * more often than we would like, potentially\n           * causing performance issues.\n           */\n          const element = document.elementFromPoint(ev.clientX, ev.clientY);\n          if (element === triggerEl) {\n            return;\n          }\n          popoverEl.dismiss(undefined, undefined, false);\n        }\n      }];\n      break;\n    case 'context-menu':\n    case 'click':\n    default:\n      dismissCallbacks = [{\n        eventName: 'click',\n        callback: ev => {\n          /**\n           * Do not dismiss the popover is we\n           * are hovering over its trigger.\n           */\n          const target = ev.target;\n          const closestTrigger = target.closest('[data-ion-popover-trigger]');\n          if (closestTrigger === triggerEl) {\n            /**\n             * stopPropagation here so if the\n             * popover has dismissOnSelect=\"true\"\n             * the popover does not dismiss since\n             * we just clicked a trigger element.\n             */\n            ev.stopPropagation();\n            return;\n          }\n          popoverEl.dismiss(undefined, undefined, false);\n        }\n      }];\n      break;\n  }\n  dismissCallbacks.forEach(({\n    eventName,\n    callback\n  }) => parentContentEl.addEventListener(eventName, callback));\n  return () => {\n    dismissCallbacks.forEach(({\n      eventName,\n      callback\n    }) => parentContentEl.removeEventListener(eventName, callback));\n  };\n};\n/**\n * Configures the triggerEl to respond\n * to user interaction based upon the triggerAction\n * prop that devs have defined.\n */\nconst configureTriggerInteraction = (triggerEl, triggerAction, popoverEl) => {\n  let triggerCallbacks = [];\n  /**\n   * Based upon the kind of trigger interaction\n   * the user wants, we setup the correct event\n   * listeners.\n   */\n  switch (triggerAction) {\n    case 'hover':\n      let hoverTimeout;\n      triggerCallbacks = [{\n        eventName: 'mouseenter',\n        callback: async ev => {\n          ev.stopPropagation();\n          if (hoverTimeout) {\n            clearTimeout(hoverTimeout);\n          }\n          /**\n           * Hovering over a trigger should not\n           * immediately open the next popover.\n           */\n          hoverTimeout = setTimeout(() => {\n            raf(() => {\n              popoverEl.presentFromTrigger(ev);\n              hoverTimeout = undefined;\n            });\n          }, 100);\n        }\n      }, {\n        eventName: 'mouseleave',\n        callback: ev => {\n          if (hoverTimeout) {\n            clearTimeout(hoverTimeout);\n          }\n          /**\n           * If mouse is over another popover\n           * that is not this popover then we should\n           * close this popover.\n           */\n          const target = ev.relatedTarget;\n          if (!target) {\n            return;\n          }\n          if (target.closest('ion-popover') !== popoverEl) {\n            popoverEl.dismiss(undefined, undefined, false);\n          }\n        }\n      }, {\n        /**\n         * stopPropagation here prevents the popover\n         * from dismissing when dismiss-on-select=\"true\".\n         */\n        eventName: 'click',\n        callback: ev => ev.stopPropagation()\n      }, {\n        eventName: 'ionPopoverActivateTrigger',\n        callback: ev => popoverEl.presentFromTrigger(ev, true)\n      }];\n      break;\n    case 'context-menu':\n      triggerCallbacks = [{\n        eventName: 'contextmenu',\n        callback: ev => {\n          /**\n           * Prevents the platform context\n           * menu from appearing.\n           */\n          ev.preventDefault();\n          popoverEl.presentFromTrigger(ev);\n        }\n      }, {\n        eventName: 'click',\n        callback: ev => ev.stopPropagation()\n      }, {\n        eventName: 'ionPopoverActivateTrigger',\n        callback: ev => popoverEl.presentFromTrigger(ev, true)\n      }];\n      break;\n    case 'click':\n    default:\n      triggerCallbacks = [{\n        /**\n         * Do not do a stopPropagation() here\n         * because if you had two click triggers\n         * then clicking the first trigger and then\n         * clicking the second trigger would not cause\n         * the first popover to dismiss.\n         */\n        eventName: 'click',\n        callback: ev => popoverEl.presentFromTrigger(ev)\n      }, {\n        eventName: 'ionPopoverActivateTrigger',\n        callback: ev => popoverEl.presentFromTrigger(ev, true)\n      }];\n      break;\n  }\n  triggerCallbacks.forEach(({\n    eventName,\n    callback\n  }) => triggerEl.addEventListener(eventName, callback));\n  triggerEl.setAttribute('data-ion-popover-trigger', 'true');\n  return () => {\n    triggerCallbacks.forEach(({\n      eventName,\n      callback\n    }) => triggerEl.removeEventListener(eventName, callback));\n    triggerEl.removeAttribute('data-ion-popover-trigger');\n  };\n};\n/**\n * Returns the index of an ion-item in an array of ion-items.\n */\nconst getIndexOfItem = (items, item) => {\n  if (!item || item.tagName !== 'ION-ITEM') {\n    return -1;\n  }\n  return items.findIndex(el => el === item);\n};\n/**\n * Given an array of elements and a currently focused ion-item\n * returns the next ion-item relative to the focused one or\n * undefined.\n */\nconst getNextItem = (items, currentItem) => {\n  const currentItemIndex = getIndexOfItem(items, currentItem);\n  return items[currentItemIndex + 1];\n};\n/**\n * Given an array of elements and a currently focused ion-item\n * returns the previous ion-item relative to the focused one or\n * undefined.\n */\nconst getPrevItem = (items, currentItem) => {\n  const currentItemIndex = getIndexOfItem(items, currentItem);\n  return items[currentItemIndex - 1];\n};\n/** Focus the internal button of the ion-item */\nconst focusItem = item => {\n  const root = getElementRoot(item);\n  const button = root.querySelector('button');\n  if (button) {\n    raf(() => button.focus());\n  }\n};\n/**\n * Returns `true` if `el` has been designated\n * as a trigger element for an ion-popover.\n */\nconst isTriggerElement = el => el.hasAttribute('data-ion-popover-trigger');\nconst configureKeyboardInteraction = popoverEl => {\n  const callback = async ev => {\n    var _a;\n    const activeElement = document.activeElement;\n    let items = [];\n    const targetTagName = (_a = ev.target) === null || _a === void 0 ? void 0 : _a.tagName;\n    /**\n     * Only handle custom keyboard interactions for the host popover element\n     * and children ion-item elements.\n     */\n    if (targetTagName !== 'ION-POPOVER' && targetTagName !== 'ION-ITEM') {\n      return;\n    }\n    /**\n     * Complex selectors with :not() are :not supported\n     * in older versions of Chromium so we need to do a\n     * try/catch here so errors are not thrown.\n     */\n    try {\n      /**\n       * Select all ion-items that are not children of child popovers.\n       * i.e. only select ion-item elements that are part of this popover\n       */\n      items = Array.from(popoverEl.querySelectorAll('ion-item:not(ion-popover ion-popover *):not([disabled])'));\n      /* eslint-disable-next-line */\n    } catch (_b) {}\n    switch (ev.key) {\n      /**\n       * If we are in a child popover\n       * then pressing the left arrow key\n       * should close this popover and move\n       * focus to the popover that presented\n       * this one.\n       */\n      case 'ArrowLeft':\n        const parentPopover = await popoverEl.getParentPopover();\n        if (parentPopover) {\n          popoverEl.dismiss(undefined, undefined, false);\n        }\n        break;\n      /**\n       * ArrowDown should move focus to the next focusable ion-item.\n       */\n      case 'ArrowDown':\n        // Disable movement/scroll with keyboard\n        ev.preventDefault();\n        const nextItem = getNextItem(items, activeElement);\n        if (nextItem !== undefined) {\n          focusItem(nextItem);\n        }\n        break;\n      /**\n       * ArrowUp should move focus to the previous focusable ion-item.\n       */\n      case 'ArrowUp':\n        // Disable movement/scroll with keyboard\n        ev.preventDefault();\n        const prevItem = getPrevItem(items, activeElement);\n        if (prevItem !== undefined) {\n          focusItem(prevItem);\n        }\n        break;\n      /**\n       * Home should move focus to the first focusable ion-item.\n       */\n      case 'Home':\n        ev.preventDefault();\n        const firstItem = items[0];\n        if (firstItem !== undefined) {\n          focusItem(firstItem);\n        }\n        break;\n      /**\n       * End should move focus to the last focusable ion-item.\n       */\n      case 'End':\n        ev.preventDefault();\n        const lastItem = items[items.length - 1];\n        if (lastItem !== undefined) {\n          focusItem(lastItem);\n        }\n        break;\n      /**\n       * ArrowRight, Spacebar, or Enter should activate\n       * the currently focused trigger item to open a\n       * popover if the element is a trigger item.\n       */\n      case 'ArrowRight':\n      case ' ':\n      case 'Enter':\n        if (activeElement && isTriggerElement(activeElement)) {\n          const rightEvent = new CustomEvent('ionPopoverActivateTrigger');\n          activeElement.dispatchEvent(rightEvent);\n        }\n        break;\n    }\n  };\n  popoverEl.addEventListener('keydown', callback);\n  return () => popoverEl.removeEventListener('keydown', callback);\n};\n/**\n * Positions a popover by taking into account\n * the reference point, preferred side, alignment\n * and viewport dimensions.\n */\nconst getPopoverPosition = (isRTL, contentWidth, contentHeight, arrowWidth, arrowHeight, reference, side, align, defaultPosition, triggerEl, event) => {\n  var _a;\n  let referenceCoordinates = {\n    top: 0,\n    left: 0,\n    width: 0,\n    height: 0\n  };\n  /**\n   * Calculate position relative to the\n   * x-y coordinates in the event that\n   * was passed in\n   */\n  switch (reference) {\n    case 'event':\n      if (!event) {\n        return defaultPosition;\n      }\n      const mouseEv = event;\n      referenceCoordinates = {\n        top: mouseEv.clientY,\n        left: mouseEv.clientX,\n        width: 1,\n        height: 1\n      };\n      break;\n    /**\n     * Calculate position relative to the bounding\n     * box on either the trigger element\n     * specified via the `trigger` prop or\n     * the target specified on the event\n     * that was passed in.\n     */\n    case 'trigger':\n    default:\n      const customEv = event;\n      /**\n       * ionShadowTarget is used when we need to align the\n       * popover with an element inside of the shadow root\n       * of an Ionic component. Ex: Presenting a popover\n       * by clicking on the collapsed indicator inside\n       * of `ion-breadcrumb` and centering it relative\n       * to the indicator rather than `ion-breadcrumb`\n       * as a whole.\n       */\n      const actualTriggerEl = triggerEl || ((_a = customEv === null || customEv === void 0 ? void 0 : customEv.detail) === null || _a === void 0 ? void 0 : _a.ionShadowTarget) || (customEv === null || customEv === void 0 ? void 0 : customEv.target);\n      if (!actualTriggerEl) {\n        return defaultPosition;\n      }\n      const triggerBoundingBox = actualTriggerEl.getBoundingClientRect();\n      referenceCoordinates = {\n        top: triggerBoundingBox.top,\n        left: triggerBoundingBox.left,\n        width: triggerBoundingBox.width,\n        height: triggerBoundingBox.height\n      };\n      break;\n  }\n  /**\n   * Get top/left offset that would allow\n   * popover to be positioned on the\n   * preferred side of the reference.\n   */\n  const coordinates = calculatePopoverSide(side, referenceCoordinates, contentWidth, contentHeight, arrowWidth, arrowHeight, isRTL);\n  /**\n   * Get the top/left adjustments that\n   * would allow the popover content\n   * to have the correct alignment.\n   */\n  const alignedCoordinates = calculatePopoverAlign(align, side, referenceCoordinates, contentWidth, contentHeight);\n  const top = coordinates.top + alignedCoordinates.top;\n  const left = coordinates.left + alignedCoordinates.left;\n  const {\n    arrowTop,\n    arrowLeft\n  } = calculateArrowPosition(side, arrowWidth, arrowHeight, top, left, contentWidth, contentHeight, isRTL);\n  const {\n    originX,\n    originY\n  } = calculatePopoverOrigin(side, align, isRTL);\n  return {\n    top,\n    left,\n    referenceCoordinates,\n    arrowTop,\n    arrowLeft,\n    originX,\n    originY\n  };\n};\n/**\n * Determines the transform-origin\n * of the popover animation so that it\n * is in line with what the side and alignment\n * prop values are. Currently only used\n * with the MD animation.\n */\nconst calculatePopoverOrigin = (side, align, isRTL) => {\n  switch (side) {\n    case 'top':\n      return {\n        originX: getOriginXAlignment(align),\n        originY: 'bottom'\n      };\n    case 'bottom':\n      return {\n        originX: getOriginXAlignment(align),\n        originY: 'top'\n      };\n    case 'left':\n      return {\n        originX: 'right',\n        originY: getOriginYAlignment(align)\n      };\n    case 'right':\n      return {\n        originX: 'left',\n        originY: getOriginYAlignment(align)\n      };\n    case 'start':\n      return {\n        originX: isRTL ? 'left' : 'right',\n        originY: getOriginYAlignment(align)\n      };\n    case 'end':\n      return {\n        originX: isRTL ? 'right' : 'left',\n        originY: getOriginYAlignment(align)\n      };\n  }\n};\nconst getOriginXAlignment = align => {\n  switch (align) {\n    case 'start':\n      return 'left';\n    case 'center':\n      return 'center';\n    case 'end':\n      return 'right';\n  }\n};\nconst getOriginYAlignment = align => {\n  switch (align) {\n    case 'start':\n      return 'top';\n    case 'center':\n      return 'center';\n    case 'end':\n      return 'bottom';\n  }\n};\n/**\n * Calculates where the arrow positioning\n * should be relative to the popover content.\n */\nconst calculateArrowPosition = (side, arrowWidth, arrowHeight, top, left, contentWidth, contentHeight, isRTL) => {\n  /**\n   * Note: When side is left, right, start, or end, the arrow is\n   * been rotated using a `transform`, so to move the arrow up or down\n   * by its dimension, you need to use `arrowWidth`.\n   */\n  const leftPosition = {\n    arrowTop: top + contentHeight / 2 - arrowWidth / 2,\n    arrowLeft: left + contentWidth - arrowWidth / 2\n  };\n  /**\n   * Move the arrow to the left by arrowWidth and then\n   * again by half of its width because we have rotated\n   * the arrow using a transform.\n   */\n  const rightPosition = {\n    arrowTop: top + contentHeight / 2 - arrowWidth / 2,\n    arrowLeft: left - arrowWidth * 1.5\n  };\n  switch (side) {\n    case 'top':\n      return {\n        arrowTop: top + contentHeight,\n        arrowLeft: left + contentWidth / 2 - arrowWidth / 2\n      };\n    case 'bottom':\n      return {\n        arrowTop: top - arrowHeight,\n        arrowLeft: left + contentWidth / 2 - arrowWidth / 2\n      };\n    case 'left':\n      return leftPosition;\n    case 'right':\n      return rightPosition;\n    case 'start':\n      return isRTL ? rightPosition : leftPosition;\n    case 'end':\n      return isRTL ? leftPosition : rightPosition;\n    default:\n      return {\n        arrowTop: 0,\n        arrowLeft: 0\n      };\n  }\n};\n/**\n * Calculates the required top/left\n * values needed to position the popover\n * content on the side specified in the\n * `side` prop.\n */\nconst calculatePopoverSide = (side, triggerBoundingBox, contentWidth, contentHeight, arrowWidth, arrowHeight, isRTL) => {\n  const sideLeft = {\n    top: triggerBoundingBox.top,\n    left: triggerBoundingBox.left - contentWidth - arrowWidth\n  };\n  const sideRight = {\n    top: triggerBoundingBox.top,\n    left: triggerBoundingBox.left + triggerBoundingBox.width + arrowWidth\n  };\n  switch (side) {\n    case 'top':\n      return {\n        top: triggerBoundingBox.top - contentHeight - arrowHeight,\n        left: triggerBoundingBox.left\n      };\n    case 'right':\n      return sideRight;\n    case 'bottom':\n      return {\n        top: triggerBoundingBox.top + triggerBoundingBox.height + arrowHeight,\n        left: triggerBoundingBox.left\n      };\n    case 'left':\n      return sideLeft;\n    case 'start':\n      return isRTL ? sideRight : sideLeft;\n    case 'end':\n      return isRTL ? sideLeft : sideRight;\n  }\n};\n/**\n * Calculates the required top/left\n * offset values needed to provide the\n * correct alignment regardless while taking\n * into account the side the popover is on.\n */\nconst calculatePopoverAlign = (align, side, triggerBoundingBox, contentWidth, contentHeight) => {\n  switch (align) {\n    case 'center':\n      return calculatePopoverCenterAlign(side, triggerBoundingBox, contentWidth, contentHeight);\n    case 'end':\n      return calculatePopoverEndAlign(side, triggerBoundingBox, contentWidth, contentHeight);\n    case 'start':\n    default:\n      return {\n        top: 0,\n        left: 0\n      };\n  }\n};\n/**\n * Calculate the end alignment for\n * the popover. If side is on the x-axis\n * then the align values refer to the top\n * and bottom margins of the content.\n * If side is on the y-axis then the\n * align values refer to the left and right\n * margins of the content.\n */\nconst calculatePopoverEndAlign = (side, triggerBoundingBox, contentWidth, contentHeight) => {\n  switch (side) {\n    case 'start':\n    case 'end':\n    case 'left':\n    case 'right':\n      return {\n        top: -(contentHeight - triggerBoundingBox.height),\n        left: 0\n      };\n    case 'top':\n    case 'bottom':\n    default:\n      return {\n        top: 0,\n        left: -(contentWidth - triggerBoundingBox.width)\n      };\n  }\n};\n/**\n * Calculate the center alignment for\n * the popover. If side is on the x-axis\n * then the align values refer to the top\n * and bottom margins of the content.\n * If side is on the y-axis then the\n * align values refer to the left and right\n * margins of the content.\n */\nconst calculatePopoverCenterAlign = (side, triggerBoundingBox, contentWidth, contentHeight) => {\n  switch (side) {\n    case 'start':\n    case 'end':\n    case 'left':\n    case 'right':\n      return {\n        top: -(contentHeight / 2 - triggerBoundingBox.height / 2),\n        left: 0\n      };\n    case 'top':\n    case 'bottom':\n    default:\n      return {\n        top: 0,\n        left: -(contentWidth / 2 - triggerBoundingBox.width / 2)\n      };\n  }\n};\n/**\n * Adjusts popover positioning coordinates\n * such that popover does not appear offscreen\n * or overlapping safe area bounds.\n */\nconst calculateWindowAdjustment = (side, coordTop, coordLeft, bodyPadding, bodyWidth, bodyHeight, contentWidth, contentHeight, safeAreaMargin, contentOriginX, contentOriginY, triggerCoordinates, coordArrowTop = 0, coordArrowLeft = 0, arrowHeight = 0) => {\n  let arrowTop = coordArrowTop;\n  const arrowLeft = coordArrowLeft;\n  let left = coordLeft;\n  let top = coordTop;\n  let bottom;\n  let originX = contentOriginX;\n  let originY = contentOriginY;\n  let checkSafeAreaLeft = false;\n  let checkSafeAreaRight = false;\n  const triggerTop = triggerCoordinates ? triggerCoordinates.top + triggerCoordinates.height : bodyHeight / 2 - contentHeight / 2;\n  const triggerHeight = triggerCoordinates ? triggerCoordinates.height : 0;\n  let addPopoverBottomClass = false;\n  /**\n   * Adjust popover so it does not\n   * go off the left of the screen.\n   */\n  if (left < bodyPadding + safeAreaMargin) {\n    left = bodyPadding;\n    checkSafeAreaLeft = true;\n    originX = 'left';\n    /**\n     * Adjust popover so it does not\n     * go off the right of the screen.\n     */\n  } else if (contentWidth + bodyPadding + left + safeAreaMargin > bodyWidth) {\n    checkSafeAreaRight = true;\n    left = bodyWidth - contentWidth - bodyPadding;\n    originX = 'right';\n  }\n  /**\n   * Adjust popover so it does not\n   * go off the top of the screen.\n   * If popover is on the left or the right of\n   * the trigger, then we should not adjust top\n   * margins.\n   */\n  if (triggerTop + triggerHeight + contentHeight > bodyHeight && (side === 'top' || side === 'bottom')) {\n    if (triggerTop - contentHeight > 0) {\n      /**\n       * While we strive to align the popover with the trigger\n       * on smaller screens this is not always possible. As a result,\n       * we adjust the popover up so that it does not hang\n       * off the bottom of the screen. However, we do not want to move\n       * the popover up so much that it goes off the top of the screen.\n       *\n       * We chose 12 here so that the popover position looks a bit nicer as\n       * it is not right up against the edge of the screen.\n       */\n      top = Math.max(12, triggerTop - contentHeight - triggerHeight - (arrowHeight - 1));\n      arrowTop = top + contentHeight;\n      originY = 'bottom';\n      addPopoverBottomClass = true;\n      /**\n       * If not enough room for popover to appear\n       * above trigger, then cut it off.\n       */\n    } else {\n      bottom = bodyPadding;\n    }\n  }\n  return {\n    top,\n    left,\n    bottom,\n    originX,\n    originY,\n    checkSafeAreaLeft,\n    checkSafeAreaRight,\n    arrowTop,\n    arrowLeft,\n    addPopoverBottomClass\n  };\n};\nconst shouldShowArrow = (side, didAdjustBounds = false, ev, trigger) => {\n  /**\n   * If no event provided and\n   * we do not have a trigger,\n   * then this popover was likely\n   * presented via the popoverController\n   * or users called `present` manually.\n   * In this case, the arrow should not be\n   * shown as we do not have a reference.\n   */\n  if (!ev && !trigger) {\n    return false;\n  }\n  /**\n   * If popover is on the left or the right\n   * of a trigger, but we needed to adjust the\n   * popover due to screen bounds, then we should\n   * hide the arrow as it will never be pointing\n   * at the trigger.\n   */\n  if (side !== 'top' && side !== 'bottom' && didAdjustBounds) {\n    return false;\n  }\n  return true;\n};\nconst POPOVER_IOS_BODY_PADDING = 5;\n/**\n * iOS Popover Enter Animation\n */\n// TODO(FW-2832): types\nconst iosEnterAnimation = (baseEl, opts) => {\n  var _a;\n  const {\n    event: ev,\n    size,\n    trigger,\n    reference,\n    side,\n    align\n  } = opts;\n  const doc = baseEl.ownerDocument;\n  const isRTL = doc.dir === 'rtl';\n  const bodyWidth = doc.defaultView.innerWidth;\n  const bodyHeight = doc.defaultView.innerHeight;\n  const root = getElementRoot(baseEl);\n  const contentEl = root.querySelector('.popover-content');\n  const arrowEl = root.querySelector('.popover-arrow');\n  const referenceSizeEl = trigger || ((_a = ev === null || ev === void 0 ? void 0 : ev.detail) === null || _a === void 0 ? void 0 : _a.ionShadowTarget) || (ev === null || ev === void 0 ? void 0 : ev.target);\n  const {\n    contentWidth,\n    contentHeight\n  } = getPopoverDimensions(size, contentEl, referenceSizeEl);\n  const {\n    arrowWidth,\n    arrowHeight\n  } = getArrowDimensions(arrowEl);\n  const defaultPosition = {\n    top: bodyHeight / 2 - contentHeight / 2,\n    left: bodyWidth / 2 - contentWidth / 2,\n    originX: isRTL ? 'right' : 'left',\n    originY: 'top'\n  };\n  const results = getPopoverPosition(isRTL, contentWidth, contentHeight, arrowWidth, arrowHeight, reference, side, align, defaultPosition, trigger, ev);\n  const padding = size === 'cover' ? 0 : POPOVER_IOS_BODY_PADDING;\n  const margin = size === 'cover' ? 0 : 25;\n  const {\n    originX,\n    originY,\n    top,\n    left,\n    bottom,\n    checkSafeAreaLeft,\n    checkSafeAreaRight,\n    arrowTop,\n    arrowLeft,\n    addPopoverBottomClass\n  } = calculateWindowAdjustment(side, results.top, results.left, padding, bodyWidth, bodyHeight, contentWidth, contentHeight, margin, results.originX, results.originY, results.referenceCoordinates, results.arrowTop, results.arrowLeft, arrowHeight);\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const contentAnimation = createAnimation();\n  backdropAnimation.addElement(root.querySelector('ion-backdrop')).fromTo('opacity', 0.01, 'var(--backdrop-opacity)').beforeStyles({\n    'pointer-events': 'none'\n  }).afterClearStyles(['pointer-events']);\n  // In Chromium, if the wrapper animates, the backdrop filter doesn't work.\n  // The Chromium team stated that this behavior is expected and not a bug. The element animating opacity creates a backdrop root for the backdrop-filter.\n  // To get around this, instead of animating the wrapper, animate both the arrow and content.\n  // https://bugs.chromium.org/p/chromium/issues/detail?id=1148826\n  contentAnimation.addElement(root.querySelector('.popover-arrow')).addElement(root.querySelector('.popover-content')).fromTo('opacity', 0.01, 1);\n  // TODO(FW-4376) Ensure that arrow also blurs when translucent\n  return baseAnimation.easing('ease').duration(100).beforeAddWrite(() => {\n    if (size === 'cover') {\n      baseEl.style.setProperty('--width', `${contentWidth}px`);\n    }\n    if (addPopoverBottomClass) {\n      baseEl.classList.add('popover-bottom');\n    }\n    if (bottom !== undefined) {\n      contentEl.style.setProperty('bottom', `${bottom}px`);\n    }\n    const safeAreaLeft = ' + var(--ion-safe-area-left, 0)';\n    const safeAreaRight = ' - var(--ion-safe-area-right, 0)';\n    let leftValue = `${left}px`;\n    if (checkSafeAreaLeft) {\n      leftValue = `${left}px${safeAreaLeft}`;\n    }\n    if (checkSafeAreaRight) {\n      leftValue = `${left}px${safeAreaRight}`;\n    }\n    contentEl.style.setProperty('top', `calc(${top}px + var(--offset-y, 0))`);\n    contentEl.style.setProperty('left', `calc(${leftValue} + var(--offset-x, 0))`);\n    contentEl.style.setProperty('transform-origin', `${originY} ${originX}`);\n    if (arrowEl !== null) {\n      const didAdjustBounds = results.top !== top || results.left !== left;\n      const showArrow = shouldShowArrow(side, didAdjustBounds, ev, trigger);\n      if (showArrow) {\n        arrowEl.style.setProperty('top', `calc(${arrowTop}px + var(--offset-y, 0))`);\n        arrowEl.style.setProperty('left', `calc(${arrowLeft}px + var(--offset-x, 0))`);\n      } else {\n        arrowEl.style.setProperty('display', 'none');\n      }\n    }\n  }).addAnimation([backdropAnimation, contentAnimation]);\n};\n\n/**\n * iOS Popover Leave Animation\n */\nconst iosLeaveAnimation = baseEl => {\n  const root = getElementRoot(baseEl);\n  const contentEl = root.querySelector('.popover-content');\n  const arrowEl = root.querySelector('.popover-arrow');\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const contentAnimation = createAnimation();\n  backdropAnimation.addElement(root.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n  contentAnimation.addElement(root.querySelector('.popover-arrow')).addElement(root.querySelector('.popover-content')).fromTo('opacity', 0.99, 0);\n  return baseAnimation.easing('ease').afterAddWrite(() => {\n    baseEl.style.removeProperty('--width');\n    baseEl.classList.remove('popover-bottom');\n    contentEl.style.removeProperty('top');\n    contentEl.style.removeProperty('left');\n    contentEl.style.removeProperty('bottom');\n    contentEl.style.removeProperty('transform-origin');\n    if (arrowEl) {\n      arrowEl.style.removeProperty('top');\n      arrowEl.style.removeProperty('left');\n      arrowEl.style.removeProperty('display');\n    }\n  }).duration(300).addAnimation([backdropAnimation, contentAnimation]);\n};\nconst POPOVER_MD_BODY_PADDING = 12;\n/**\n * Md Popover Enter Animation\n */\n// TODO(FW-2832): types\nconst mdEnterAnimation = (baseEl, opts) => {\n  var _a;\n  const {\n    event: ev,\n    size,\n    trigger,\n    reference,\n    side,\n    align\n  } = opts;\n  const doc = baseEl.ownerDocument;\n  const isRTL = doc.dir === 'rtl';\n  const bodyWidth = doc.defaultView.innerWidth;\n  const bodyHeight = doc.defaultView.innerHeight;\n  const root = getElementRoot(baseEl);\n  const contentEl = root.querySelector('.popover-content');\n  const referenceSizeEl = trigger || ((_a = ev === null || ev === void 0 ? void 0 : ev.detail) === null || _a === void 0 ? void 0 : _a.ionShadowTarget) || (ev === null || ev === void 0 ? void 0 : ev.target);\n  const {\n    contentWidth,\n    contentHeight\n  } = getPopoverDimensions(size, contentEl, referenceSizeEl);\n  const defaultPosition = {\n    top: bodyHeight / 2 - contentHeight / 2,\n    left: bodyWidth / 2 - contentWidth / 2,\n    originX: isRTL ? 'right' : 'left',\n    originY: 'top'\n  };\n  const results = getPopoverPosition(isRTL, contentWidth, contentHeight, 0, 0, reference, side, align, defaultPosition, trigger, ev);\n  const padding = size === 'cover' ? 0 : POPOVER_MD_BODY_PADDING;\n  const {\n    originX,\n    originY,\n    top,\n    left,\n    bottom\n  } = calculateWindowAdjustment(side, results.top, results.left, padding, bodyWidth, bodyHeight, contentWidth, contentHeight, 0, results.originX, results.originY, results.referenceCoordinates);\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  const contentAnimation = createAnimation();\n  const viewportAnimation = createAnimation();\n  backdropAnimation.addElement(root.querySelector('ion-backdrop')).fromTo('opacity', 0.01, 'var(--backdrop-opacity)').beforeStyles({\n    'pointer-events': 'none'\n  }).afterClearStyles(['pointer-events']);\n  wrapperAnimation.addElement(root.querySelector('.popover-wrapper')).duration(150).fromTo('opacity', 0.01, 1);\n  contentAnimation.addElement(contentEl).beforeStyles({\n    top: `calc(${top}px + var(--offset-y, 0px))`,\n    left: `calc(${left}px + var(--offset-x, 0px))`,\n    'transform-origin': `${originY} ${originX}`\n  }).beforeAddWrite(() => {\n    if (bottom !== undefined) {\n      contentEl.style.setProperty('bottom', `${bottom}px`);\n    }\n  }).fromTo('transform', 'scale(0.8)', 'scale(1)');\n  viewportAnimation.addElement(root.querySelector('.popover-viewport')).fromTo('opacity', 0.01, 1);\n  return baseAnimation.easing('cubic-bezier(0.36,0.66,0.04,1)').duration(300).beforeAddWrite(() => {\n    if (size === 'cover') {\n      baseEl.style.setProperty('--width', `${contentWidth}px`);\n    }\n    if (originY === 'bottom') {\n      baseEl.classList.add('popover-bottom');\n    }\n  }).addAnimation([backdropAnimation, wrapperAnimation, contentAnimation, viewportAnimation]);\n};\n\n/**\n * Md Popover Leave Animation\n */\nconst mdLeaveAnimation = baseEl => {\n  const root = getElementRoot(baseEl);\n  const contentEl = root.querySelector('.popover-content');\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(root.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n  wrapperAnimation.addElement(root.querySelector('.popover-wrapper')).fromTo('opacity', 0.99, 0);\n  return baseAnimation.easing('ease').afterAddWrite(() => {\n    baseEl.style.removeProperty('--width');\n    baseEl.classList.remove('popover-bottom');\n    contentEl.style.removeProperty('top');\n    contentEl.style.removeProperty('left');\n    contentEl.style.removeProperty('bottom');\n    contentEl.style.removeProperty('transform-origin');\n  }).duration(150).addAnimation([backdropAnimation, wrapperAnimation]);\n};\nconst popoverIosCss = \":host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:200px;--max-height:90%;--box-shadow:none;--backdrop-opacity:var(--ion-backdrop-opacity, 0.08)}:host(.popover-desktop){--box-shadow:0px 4px 16px 0px rgba(0, 0, 0, 0.12)}.popover-content{border-radius:10px}:host(.popover-desktop) .popover-content{border:0.5px solid var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.popover-arrow{display:block;position:absolute;width:20px;height:10px;overflow:hidden;z-index:11}.popover-arrow::after{top:3px;border-radius:3px;position:absolute;width:14px;height:14px;-webkit-transform:rotate(45deg);transform:rotate(45deg);background:var(--background);content:\\\"\\\";z-index:10}.popover-arrow::after{inset-inline-start:3px}:host(.popover-bottom) .popover-arrow{top:auto;bottom:-10px}:host(.popover-bottom) .popover-arrow::after{top:-6px}:host(.popover-side-left) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host(.popover-side-right) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host(.popover-side-top) .popover-arrow{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.popover-side-start) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host-context([dir=rtl]):host(.popover-side-start) .popover-arrow,:host-context([dir=rtl]).popover-side-start .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}@supports selector(:dir(rtl)){:host(.popover-side-start:dir(rtl)) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}}:host(.popover-side-end) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host-context([dir=rtl]):host(.popover-side-end) .popover-arrow,:host-context([dir=rtl]).popover-side-end .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}@supports selector(:dir(rtl)){:host(.popover-side-end:dir(rtl)) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}}.popover-arrow,.popover-content{opacity:0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.popover-translucent) .popover-content,:host(.popover-translucent) .popover-arrow::after{background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}\";\nconst IonPopoverIosStyle0 = popoverIosCss;\nconst popoverMdCss = \":host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:250px;--max-height:90%;--box-shadow:0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}.popover-content{border-radius:4px;-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]) .popover-content{-webkit-transform-origin:right top;transform-origin:right top}[dir=rtl] .popover-content{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.popover-content:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}.popover-viewport{-webkit-transition-delay:100ms;transition-delay:100ms}.popover-wrapper{opacity:0}\";\nconst IonPopoverMdStyle0 = popoverMdCss;\nconst Popover = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.didPresent = createEvent(this, \"ionPopoverDidPresent\", 7);\n    this.willPresent = createEvent(this, \"ionPopoverWillPresent\", 7);\n    this.willDismiss = createEvent(this, \"ionPopoverWillDismiss\", 7);\n    this.didDismiss = createEvent(this, \"ionPopoverDidDismiss\", 7);\n    this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n    this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n    this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n    this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n    this.ionMount = createEvent(this, \"ionMount\", 7);\n    this.parentPopover = null;\n    this.coreDelegate = CoreDelegate();\n    this.lockController = createLockController();\n    this.inline = false;\n    this.focusDescendantOnPresent = false;\n    this.onBackdropTap = () => {\n      this.dismiss(undefined, BACKDROP);\n    };\n    this.onLifecycle = modalEvent => {\n      const el = this.usersElement;\n      const name = LIFECYCLE_MAP[modalEvent.type];\n      if (el && name) {\n        const event = new CustomEvent(name, {\n          bubbles: false,\n          cancelable: false,\n          detail: modalEvent.detail\n        });\n        el.dispatchEvent(event);\n      }\n    };\n    this.configureTriggerInteraction = () => {\n      const {\n        trigger,\n        triggerAction,\n        el,\n        destroyTriggerInteraction\n      } = this;\n      if (destroyTriggerInteraction) {\n        destroyTriggerInteraction();\n      }\n      if (trigger === undefined) {\n        return;\n      }\n      const triggerEl = this.triggerEl = trigger !== undefined ? document.getElementById(trigger) : null;\n      if (!triggerEl) {\n        printIonWarning(`[ion-popover] - A trigger element with the ID \"${trigger}\" was not found in the DOM. The trigger element must be in the DOM when the \"trigger\" property is set on ion-popover.`, this.el);\n        return;\n      }\n      this.destroyTriggerInteraction = configureTriggerInteraction(triggerEl, triggerAction, el);\n    };\n    this.configureKeyboardInteraction = () => {\n      const {\n        destroyKeyboardInteraction,\n        el\n      } = this;\n      if (destroyKeyboardInteraction) {\n        destroyKeyboardInteraction();\n      }\n      this.destroyKeyboardInteraction = configureKeyboardInteraction(el);\n    };\n    this.configureDismissInteraction = () => {\n      const {\n        destroyDismissInteraction,\n        parentPopover,\n        triggerAction,\n        triggerEl,\n        el\n      } = this;\n      if (!parentPopover || !triggerEl) {\n        return;\n      }\n      if (destroyDismissInteraction) {\n        destroyDismissInteraction();\n      }\n      this.destroyDismissInteraction = configureDismissInteraction(triggerEl, triggerAction, el, parentPopover);\n    };\n    this.presented = false;\n    this.hasController = false;\n    this.delegate = undefined;\n    this.overlayIndex = undefined;\n    this.enterAnimation = undefined;\n    this.leaveAnimation = undefined;\n    this.component = undefined;\n    this.componentProps = undefined;\n    this.keyboardClose = true;\n    this.cssClass = undefined;\n    this.backdropDismiss = true;\n    this.event = undefined;\n    this.showBackdrop = true;\n    this.translucent = false;\n    this.animated = true;\n    this.htmlAttributes = undefined;\n    this.triggerAction = 'click';\n    this.trigger = undefined;\n    this.size = 'auto';\n    this.dismissOnSelect = false;\n    this.reference = 'trigger';\n    this.side = 'bottom';\n    this.alignment = undefined;\n    this.arrow = true;\n    this.isOpen = false;\n    this.keyboardEvents = false;\n    this.focusTrap = true;\n    this.keepContentsMounted = false;\n  }\n  onTriggerChange() {\n    this.configureTriggerInteraction();\n  }\n  onIsOpenChange(newValue, oldValue) {\n    if (newValue === true && oldValue === false) {\n      this.present();\n    } else if (newValue === false && oldValue === true) {\n      this.dismiss();\n    }\n  }\n  connectedCallback() {\n    const {\n      configureTriggerInteraction,\n      el\n    } = this;\n    prepareOverlay(el);\n    configureTriggerInteraction();\n  }\n  disconnectedCallback() {\n    const {\n      destroyTriggerInteraction\n    } = this;\n    if (destroyTriggerInteraction) {\n      destroyTriggerInteraction();\n    }\n  }\n  componentWillLoad() {\n    var _a, _b;\n    const {\n      el\n    } = this;\n    const popoverId = (_b = (_a = this.htmlAttributes) === null || _a === void 0 ? void 0 : _a.id) !== null && _b !== void 0 ? _b : setOverlayId(el);\n    this.parentPopover = el.closest(`ion-popover:not(#${popoverId})`);\n    if (this.alignment === undefined) {\n      this.alignment = getIonMode(this) === 'ios' ? 'center' : 'start';\n    }\n  }\n  componentDidLoad() {\n    const {\n      parentPopover,\n      isOpen\n    } = this;\n    /**\n     * If popover was rendered with isOpen=\"true\"\n     * then we should open popover immediately.\n     */\n    if (isOpen === true) {\n      raf(() => this.present());\n    }\n    if (parentPopover) {\n      addEventListener(parentPopover, 'ionPopoverWillDismiss', () => {\n        this.dismiss(undefined, undefined, false);\n      });\n    }\n    /**\n     * When binding values in frameworks such as Angular\n     * it is possible for the value to be set after the Web Component\n     * initializes but before the value watcher is set up in Stencil.\n     * As a result, the watcher callback may not be fired.\n     * We work around this by manually calling the watcher\n     * callback when the component has loaded and the watcher\n     * is configured.\n     */\n    this.configureTriggerInteraction();\n  }\n  /**\n   * When opening a popover from a trigger, we should not be\n   * modifying the `event` prop from inside the component.\n   * Additionally, when pressing the \"Right\" arrow key, we need\n   * to shift focus to the first descendant in the newly presented\n   * popover.\n   *\n   * @internal\n   */\n  async presentFromTrigger(event, focusDescendant = false) {\n    this.focusDescendantOnPresent = focusDescendant;\n    await this.present(event);\n    this.focusDescendantOnPresent = false;\n  }\n  /**\n   * Determines whether or not an overlay\n   * is being used inline or via a controller/JS\n   * and returns the correct delegate.\n   * By default, subsequent calls to getDelegate\n   * will use a cached version of the delegate.\n   * This is useful for calling dismiss after\n   * present so that the correct delegate is given.\n   */\n  getDelegate(force = false) {\n    if (this.workingDelegate && !force) {\n      return {\n        delegate: this.workingDelegate,\n        inline: this.inline\n      };\n    }\n    /**\n     * If using overlay inline\n     * we potentially need to use the coreDelegate\n     * so that this works in vanilla JS apps.\n     * If a developer has presented this component\n     * via a controller, then we can assume\n     * the component is already in the\n     * correct place.\n     */\n    const parentEl = this.el.parentNode;\n    const inline = this.inline = parentEl !== null && !this.hasController;\n    const delegate = this.workingDelegate = inline ? this.delegate || this.coreDelegate : this.delegate;\n    return {\n      inline,\n      delegate\n    };\n  }\n  /**\n   * Present the popover overlay after it has been created.\n   * Developers can pass a mouse, touch, or pointer event\n   * to position the popover relative to where that event\n   * was dispatched.\n   */\n  async present(event) {\n    const unlock = await this.lockController.lock();\n    if (this.presented) {\n      unlock();\n      return;\n    }\n    const {\n      el\n    } = this;\n    const {\n      inline,\n      delegate\n    } = this.getDelegate(true);\n    /**\n     * Emit ionMount so JS Frameworks have an opportunity\n     * to add the child component to the DOM. The child\n     * component will be assigned to this.usersElement below.\n     */\n    this.ionMount.emit();\n    this.usersElement = await attachComponent(delegate, el, this.component, ['popover-viewport'], this.componentProps, inline);\n    if (!this.keyboardEvents) {\n      this.configureKeyboardInteraction();\n    }\n    this.configureDismissInteraction();\n    /**\n     * When using the lazy loaded build of Stencil, we need to wait\n     * for every Stencil component instance to be ready before presenting\n     * otherwise there can be a flash of unstyled content. With the\n     * custom elements bundle we need to wait for the JS framework\n     * mount the inner contents of the overlay otherwise WebKit may\n     * get the transition incorrect.\n     */\n    if (hasLazyBuild(el)) {\n      await deepReady(this.usersElement);\n      /**\n       * If keepContentsMounted=\"true\" then the\n       * JS Framework has already mounted the inner\n       * contents so there is no need to wait.\n       * Otherwise, we need to wait for the JS\n       * Framework to mount the inner contents\n       * of this component.\n       */\n    } else if (!this.keepContentsMounted) {\n      await waitForMount();\n    }\n    await present(this, 'popoverEnter', iosEnterAnimation, mdEnterAnimation, {\n      event: event || this.event,\n      size: this.size,\n      trigger: this.triggerEl,\n      reference: this.reference,\n      side: this.side,\n      align: this.alignment\n    });\n    /**\n     * If popover is nested and was\n     * presented using the \"Right\" arrow key,\n     * we need to move focus to the first\n     * descendant inside of the popover.\n     */\n    if (this.focusDescendantOnPresent) {\n      focusFirstDescendant(el);\n    }\n    unlock();\n  }\n  /**\n   * Dismiss the popover overlay after it has been presented.\n   *\n   * @param data Any data to emit in the dismiss events.\n   * @param role The role of the element that is dismissing the popover. For example, 'cancel' or 'backdrop'.\n   * @param dismissParentPopover If `true`, dismissing this popover will also dismiss\n   * a parent popover if this popover is nested. Defaults to `true`.\n   *\n   * This is a no-op if the overlay has not been presented yet. If you want\n   * to remove an overlay from the DOM that was never presented, use the\n   * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n   */\n  async dismiss(data, role, dismissParentPopover = true) {\n    const unlock = await this.lockController.lock();\n    const {\n      destroyKeyboardInteraction,\n      destroyDismissInteraction\n    } = this;\n    if (dismissParentPopover && this.parentPopover) {\n      this.parentPopover.dismiss(data, role, dismissParentPopover);\n    }\n    const shouldDismiss = await dismiss(this, data, role, 'popoverLeave', iosLeaveAnimation, mdLeaveAnimation, this.event);\n    if (shouldDismiss) {\n      if (destroyKeyboardInteraction) {\n        destroyKeyboardInteraction();\n        this.destroyKeyboardInteraction = undefined;\n      }\n      if (destroyDismissInteraction) {\n        destroyDismissInteraction();\n        this.destroyDismissInteraction = undefined;\n      }\n      /**\n       * If using popover inline\n       * we potentially need to use the coreDelegate\n       * so that this works in vanilla JS apps\n       */\n      const {\n        delegate\n      } = this.getDelegate();\n      await detachComponent(delegate, this.usersElement);\n    }\n    unlock();\n    return shouldDismiss;\n  }\n  /**\n   * @internal\n   */\n  async getParentPopover() {\n    return this.parentPopover;\n  }\n  /**\n   * Returns a promise that resolves when the popover did dismiss.\n   */\n  onDidDismiss() {\n    return eventMethod(this.el, 'ionPopoverDidDismiss');\n  }\n  /**\n   * Returns a promise that resolves when the popover will dismiss.\n   */\n  onWillDismiss() {\n    return eventMethod(this.el, 'ionPopoverWillDismiss');\n  }\n  render() {\n    const mode = getIonMode(this);\n    const {\n      onLifecycle,\n      parentPopover,\n      dismissOnSelect,\n      side,\n      arrow,\n      htmlAttributes,\n      focusTrap\n    } = this;\n    const desktop = isPlatform('desktop');\n    const enableArrow = arrow && !parentPopover;\n    return h(Host, Object.assign({\n      key: 'ff24e8d9677711248a36994cce568e74ba151499',\n      \"aria-modal\": \"true\",\n      \"no-router\": true,\n      tabindex: \"-1\"\n    }, htmlAttributes, {\n      style: {\n        zIndex: `${20000 + this.overlayIndex}`\n      },\n      class: Object.assign(Object.assign({}, getClassMap(this.cssClass)), {\n        [mode]: true,\n        'popover-translucent': this.translucent,\n        'overlay-hidden': true,\n        'popover-desktop': desktop,\n        [`popover-side-${side}`]: true,\n        [FOCUS_TRAP_DISABLE_CLASS]: focusTrap === false,\n        'popover-nested': !!parentPopover\n      }),\n      onIonPopoverDidPresent: onLifecycle,\n      onIonPopoverWillPresent: onLifecycle,\n      onIonPopoverWillDismiss: onLifecycle,\n      onIonPopoverDidDismiss: onLifecycle,\n      onIonBackdropTap: this.onBackdropTap\n    }), !parentPopover && h(\"ion-backdrop\", {\n      key: 'aca68b4002a08b0e563a976a867141162c20f8b4',\n      tappable: this.backdropDismiss,\n      visible: this.showBackdrop,\n      part: \"backdrop\"\n    }), h(\"div\", {\n      key: '62d21d1eab5c6d675d49932559ffb161747e5fec',\n      class: \"popover-wrapper ion-overlay-wrapper\",\n      onClick: dismissOnSelect ? () => this.dismiss() : undefined\n    }, enableArrow && h(\"div\", {\n      key: '1b46cc77d5302637fc979353483bb5fd780fd1d3',\n      class: \"popover-arrow\",\n      part: \"arrow\"\n    }), h(\"div\", {\n      key: 'a5657bff26e46d1959b71eb0992e7dc8fcae86f1',\n      class: \"popover-content\",\n      part: \"content\"\n    }, h(\"slot\", {\n      key: 'e1a98007226a46b51109e7004c4d338ca1bc0f9e'\n    }))));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"trigger\": [\"onTriggerChange\"],\n      \"triggerAction\": [\"onTriggerChange\"],\n      \"isOpen\": [\"onIsOpenChange\"]\n    };\n  }\n};\nconst LIFECYCLE_MAP = {\n  ionPopoverDidPresent: 'ionViewDidEnter',\n  ionPopoverWillPresent: 'ionViewWillEnter',\n  ionPopoverWillDismiss: 'ionViewWillLeave',\n  ionPopoverDidDismiss: 'ionViewDidLeave'\n};\nPopover.style = {\n  ios: IonPopoverIosStyle0,\n  md: IonPopoverMdStyle0\n};\nexport { Popover as ion_popover };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAsBM,oBAqBA,sBAaA,6BA2EA,6BAgHA,gBAWA,aASA,aAKA,WAWA,kBACA,8BAyGA,oBAiGA,wBAkCA,qBAUA,qBAcA,wBAmDA,sBAoCA,uBAuBA,0BA4BA,6BAwBA,2BA0EA,iBAyBA,0BAKA,mBAiGA,mBAuBA,yBAKA,kBAoEA,kBAiBA,eACA,qBACA,cACA,oBACA,SAmaA;AA/5CN;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA,IAAM,qBAAqB,aAAW;AACpC,UAAI,CAAC,SAAS;AACZ,eAAO;AAAA,UACL,YAAY;AAAA,UACZ,aAAa;AAAA,QACf;AAAA,MACF;AACA,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,QAAQ,sBAAsB;AAClC,aAAO;AAAA,QACL,YAAY;AAAA,QACZ,aAAa;AAAA,MACf;AAAA,IACF;AAMA,IAAM,uBAAuB,CAAC,MAAM,WAAW,cAAc;AAC3D,YAAM,oBAAoB,UAAU,sBAAsB;AAC1D,YAAM,gBAAgB,kBAAkB;AACxC,UAAI,eAAe,kBAAkB;AACrC,UAAI,SAAS,WAAW,WAAW;AACjC,cAAM,oBAAoB,UAAU,sBAAsB;AAC1D,uBAAe,kBAAkB;AAAA,MACnC;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,IAAM,8BAA8B,CAAC,WAAW,eAAe,WAAW,oBAAoB;AAC5F,UAAI,mBAAmB,CAAC;AACxB,YAAM,OAAO,eAAe,eAAe;AAC3C,YAAM,kBAAkB,KAAK,cAAc,kBAAkB;AAC7D,cAAQ,eAAe;AAAA,QACrB,KAAK;AACH,6BAAmB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAQlB,WAAW;AAAA,YACX,UAAU,QAAM;AASd,oBAAM,UAAU,SAAS,iBAAiB,GAAG,SAAS,GAAG,OAAO;AAChE,kBAAI,YAAY,WAAW;AACzB;AAAA,cACF;AACA,wBAAU,QAAQ,QAAW,QAAW,KAAK;AAAA,YAC/C;AAAA,UACF,CAAC;AACD;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AACE,6BAAmB,CAAC;AAAA,YAClB,WAAW;AAAA,YACX,UAAU,QAAM;AAKd,oBAAM,SAAS,GAAG;AAClB,oBAAM,iBAAiB,OAAO,QAAQ,4BAA4B;AAClE,kBAAI,mBAAmB,WAAW;AAOhC,mBAAG,gBAAgB;AACnB;AAAA,cACF;AACA,wBAAU,QAAQ,QAAW,QAAW,KAAK;AAAA,YAC/C;AAAA,UACF,CAAC;AACD;AAAA,MACJ;AACA,uBAAiB,QAAQ,CAAC;AAAA,QACxB;AAAA,QACA;AAAA,MACF,MAAM,gBAAgB,iBAAiB,WAAW,QAAQ,CAAC;AAC3D,aAAO,MAAM;AACX,yBAAiB,QAAQ,CAAC;AAAA,UACxB;AAAA,UACA;AAAA,QACF,MAAM,gBAAgB,oBAAoB,WAAW,QAAQ,CAAC;AAAA,MAChE;AAAA,IACF;AAMA,IAAM,8BAA8B,CAAC,WAAW,eAAe,cAAc;AAC3E,UAAI,mBAAmB,CAAC;AAMxB,cAAQ,eAAe;AAAA,QACrB,KAAK;AACH,cAAI;AACJ,6BAAmB,CAAC;AAAA,YAClB,WAAW;AAAA,YACX,UAAU,CAAM,OAAM;AACpB,iBAAG,gBAAgB;AACnB,kBAAI,cAAc;AAChB,6BAAa,YAAY;AAAA,cAC3B;AAKA,6BAAe,WAAW,MAAM;AAC9B,oBAAI,MAAM;AACR,4BAAU,mBAAmB,EAAE;AAC/B,iCAAe;AAAA,gBACjB,CAAC;AAAA,cACH,GAAG,GAAG;AAAA,YACR;AAAA,UACF,GAAG;AAAA,YACD,WAAW;AAAA,YACX,UAAU,QAAM;AACd,kBAAI,cAAc;AAChB,6BAAa,YAAY;AAAA,cAC3B;AAMA,oBAAM,SAAS,GAAG;AAClB,kBAAI,CAAC,QAAQ;AACX;AAAA,cACF;AACA,kBAAI,OAAO,QAAQ,aAAa,MAAM,WAAW;AAC/C,0BAAU,QAAQ,QAAW,QAAW,KAAK;AAAA,cAC/C;AAAA,YACF;AAAA,UACF,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,YAKD,WAAW;AAAA,YACX,UAAU,QAAM,GAAG,gBAAgB;AAAA,UACrC,GAAG;AAAA,YACD,WAAW;AAAA,YACX,UAAU,QAAM,UAAU,mBAAmB,IAAI,IAAI;AAAA,UACvD,CAAC;AACD;AAAA,QACF,KAAK;AACH,6BAAmB,CAAC;AAAA,YAClB,WAAW;AAAA,YACX,UAAU,QAAM;AAKd,iBAAG,eAAe;AAClB,wBAAU,mBAAmB,EAAE;AAAA,YACjC;AAAA,UACF,GAAG;AAAA,YACD,WAAW;AAAA,YACX,UAAU,QAAM,GAAG,gBAAgB;AAAA,UACrC,GAAG;AAAA,YACD,WAAW;AAAA,YACX,UAAU,QAAM,UAAU,mBAAmB,IAAI,IAAI;AAAA,UACvD,CAAC;AACD;AAAA,QACF,KAAK;AAAA,QACL;AACE,6BAAmB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAQlB,WAAW;AAAA,YACX,UAAU,QAAM,UAAU,mBAAmB,EAAE;AAAA,UACjD,GAAG;AAAA,YACD,WAAW;AAAA,YACX,UAAU,QAAM,UAAU,mBAAmB,IAAI,IAAI;AAAA,UACvD,CAAC;AACD;AAAA,MACJ;AACA,uBAAiB,QAAQ,CAAC;AAAA,QACxB;AAAA,QACA;AAAA,MACF,MAAM,UAAU,iBAAiB,WAAW,QAAQ,CAAC;AACrD,gBAAU,aAAa,4BAA4B,MAAM;AACzD,aAAO,MAAM;AACX,yBAAiB,QAAQ,CAAC;AAAA,UACxB;AAAA,UACA;AAAA,QACF,MAAM,UAAU,oBAAoB,WAAW,QAAQ,CAAC;AACxD,kBAAU,gBAAgB,0BAA0B;AAAA,MACtD;AAAA,IACF;AAIA,IAAM,iBAAiB,CAAC,OAAO,SAAS;AACtC,UAAI,CAAC,QAAQ,KAAK,YAAY,YAAY;AACxC,eAAO;AAAA,MACT;AACA,aAAO,MAAM,UAAU,QAAM,OAAO,IAAI;AAAA,IAC1C;AAMA,IAAM,cAAc,CAAC,OAAO,gBAAgB;AAC1C,YAAM,mBAAmB,eAAe,OAAO,WAAW;AAC1D,aAAO,MAAM,mBAAmB,CAAC;AAAA,IACnC;AAMA,IAAM,cAAc,CAAC,OAAO,gBAAgB;AAC1C,YAAM,mBAAmB,eAAe,OAAO,WAAW;AAC1D,aAAO,MAAM,mBAAmB,CAAC;AAAA,IACnC;AAEA,IAAM,YAAY,UAAQ;AACxB,YAAM,OAAO,eAAe,IAAI;AAChC,YAAM,SAAS,KAAK,cAAc,QAAQ;AAC1C,UAAI,QAAQ;AACV,YAAI,MAAM,OAAO,MAAM,CAAC;AAAA,MAC1B;AAAA,IACF;AAKA,IAAM,mBAAmB,QAAM,GAAG,aAAa,0BAA0B;AACzE,IAAM,+BAA+B,eAAa;AAChD,YAAM,WAAW,CAAM,OAAM;AAC3B,YAAI;AACJ,cAAM,gBAAgB,SAAS;AAC/B,YAAI,QAAQ,CAAC;AACb,cAAM,iBAAiB,KAAK,GAAG,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAK/E,YAAI,kBAAkB,iBAAiB,kBAAkB,YAAY;AACnE;AAAA,QACF;AAMA,YAAI;AAKF,kBAAQ,MAAM,KAAK,UAAU,iBAAiB,yDAAyD,CAAC;AAAA,QAE1G,SAAS,IAAI;AAAA,QAAC;AACd,gBAAQ,GAAG,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQd,KAAK;AACH,kBAAM,gBAAgB,MAAM,UAAU,iBAAiB;AACvD,gBAAI,eAAe;AACjB,wBAAU,QAAQ,QAAW,QAAW,KAAK;AAAA,YAC/C;AACA;AAAA;AAAA;AAAA;AAAA,UAIF,KAAK;AAEH,eAAG,eAAe;AAClB,kBAAM,WAAW,YAAY,OAAO,aAAa;AACjD,gBAAI,aAAa,QAAW;AAC1B,wBAAU,QAAQ;AAAA,YACpB;AACA;AAAA;AAAA;AAAA;AAAA,UAIF,KAAK;AAEH,eAAG,eAAe;AAClB,kBAAM,WAAW,YAAY,OAAO,aAAa;AACjD,gBAAI,aAAa,QAAW;AAC1B,wBAAU,QAAQ;AAAA,YACpB;AACA;AAAA;AAAA;AAAA;AAAA,UAIF,KAAK;AACH,eAAG,eAAe;AAClB,kBAAM,YAAY,MAAM,CAAC;AACzB,gBAAI,cAAc,QAAW;AAC3B,wBAAU,SAAS;AAAA,YACrB;AACA;AAAA;AAAA;AAAA;AAAA,UAIF,KAAK;AACH,eAAG,eAAe;AAClB,kBAAM,WAAW,MAAM,MAAM,SAAS,CAAC;AACvC,gBAAI,aAAa,QAAW;AAC1B,wBAAU,QAAQ;AAAA,YACpB;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAMF,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,gBAAI,iBAAiB,iBAAiB,aAAa,GAAG;AACpD,oBAAM,aAAa,IAAI,YAAY,2BAA2B;AAC9D,4BAAc,cAAc,UAAU;AAAA,YACxC;AACA;AAAA,QACJ;AAAA,MACF;AACA,gBAAU,iBAAiB,WAAW,QAAQ;AAC9C,aAAO,MAAM,UAAU,oBAAoB,WAAW,QAAQ;AAAA,IAChE;AAMA,IAAM,qBAAqB,CAAC,OAAO,cAAc,eAAe,YAAY,aAAa,WAAW,MAAM,OAAO,iBAAiB,WAAW,UAAU;AACrJ,UAAI;AACJ,UAAI,uBAAuB;AAAA,QACzB,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAMA,cAAQ,WAAW;AAAA,QACjB,KAAK;AACH,cAAI,CAAC,OAAO;AACV,mBAAO;AAAA,UACT;AACA,gBAAM,UAAU;AAChB,iCAAuB;AAAA,YACrB,KAAK,QAAQ;AAAA,YACb,MAAM,QAAQ;AAAA,YACd,OAAO;AAAA,YACP,QAAQ;AAAA,UACV;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQF,KAAK;AAAA,QACL;AACE,gBAAM,WAAW;AAUjB,gBAAM,kBAAkB,eAAe,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,qBAAqB,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS;AAC3O,cAAI,CAAC,iBAAiB;AACpB,mBAAO;AAAA,UACT;AACA,gBAAM,qBAAqB,gBAAgB,sBAAsB;AACjE,iCAAuB;AAAA,YACrB,KAAK,mBAAmB;AAAA,YACxB,MAAM,mBAAmB;AAAA,YACzB,OAAO,mBAAmB;AAAA,YAC1B,QAAQ,mBAAmB;AAAA,UAC7B;AACA;AAAA,MACJ;AAMA,YAAM,cAAc,qBAAqB,MAAM,sBAAsB,cAAc,eAAe,YAAY,aAAa,KAAK;AAMhI,YAAM,qBAAqB,sBAAsB,OAAO,MAAM,sBAAsB,cAAc,aAAa;AAC/G,YAAM,MAAM,YAAY,MAAM,mBAAmB;AACjD,YAAM,OAAO,YAAY,OAAO,mBAAmB;AACnD,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,uBAAuB,MAAM,YAAY,aAAa,KAAK,MAAM,cAAc,eAAe,KAAK;AACvG,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,uBAAuB,MAAM,OAAO,KAAK;AAC7C,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAQA,IAAM,yBAAyB,CAAC,MAAM,OAAO,UAAU;AACrD,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,YACL,SAAS,oBAAoB,KAAK;AAAA,YAClC,SAAS;AAAA,UACX;AAAA,QACF,KAAK;AACH,iBAAO;AAAA,YACL,SAAS,oBAAoB,KAAK;AAAA,YAClC,SAAS;AAAA,UACX;AAAA,QACF,KAAK;AACH,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,SAAS,oBAAoB,KAAK;AAAA,UACpC;AAAA,QACF,KAAK;AACH,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,SAAS,oBAAoB,KAAK;AAAA,UACpC;AAAA,QACF,KAAK;AACH,iBAAO;AAAA,YACL,SAAS,QAAQ,SAAS;AAAA,YAC1B,SAAS,oBAAoB,KAAK;AAAA,UACpC;AAAA,QACF,KAAK;AACH,iBAAO;AAAA,YACL,SAAS,QAAQ,UAAU;AAAA,YAC3B,SAAS,oBAAoB,KAAK;AAAA,UACpC;AAAA,MACJ;AAAA,IACF;AACA,IAAM,sBAAsB,WAAS;AACnC,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,MACX;AAAA,IACF;AACA,IAAM,sBAAsB,WAAS;AACnC,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,MACX;AAAA,IACF;AAKA,IAAM,yBAAyB,CAAC,MAAM,YAAY,aAAa,KAAK,MAAM,cAAc,eAAe,UAAU;AAM/G,YAAM,eAAe;AAAA,QACnB,UAAU,MAAM,gBAAgB,IAAI,aAAa;AAAA,QACjD,WAAW,OAAO,eAAe,aAAa;AAAA,MAChD;AAMA,YAAM,gBAAgB;AAAA,QACpB,UAAU,MAAM,gBAAgB,IAAI,aAAa;AAAA,QACjD,WAAW,OAAO,aAAa;AAAA,MACjC;AACA,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,YACL,UAAU,MAAM;AAAA,YAChB,WAAW,OAAO,eAAe,IAAI,aAAa;AAAA,UACpD;AAAA,QACF,KAAK;AACH,iBAAO;AAAA,YACL,UAAU,MAAM;AAAA,YAChB,WAAW,OAAO,eAAe,IAAI,aAAa;AAAA,UACpD;AAAA,QACF,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO,QAAQ,gBAAgB;AAAA,QACjC,KAAK;AACH,iBAAO,QAAQ,eAAe;AAAA,QAChC;AACE,iBAAO;AAAA,YACL,UAAU;AAAA,YACV,WAAW;AAAA,UACb;AAAA,MACJ;AAAA,IACF;AAOA,IAAM,uBAAuB,CAAC,MAAM,oBAAoB,cAAc,eAAe,YAAY,aAAa,UAAU;AACtH,YAAM,WAAW;AAAA,QACf,KAAK,mBAAmB;AAAA,QACxB,MAAM,mBAAmB,OAAO,eAAe;AAAA,MACjD;AACA,YAAM,YAAY;AAAA,QAChB,KAAK,mBAAmB;AAAA,QACxB,MAAM,mBAAmB,OAAO,mBAAmB,QAAQ;AAAA,MAC7D;AACA,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,YACL,KAAK,mBAAmB,MAAM,gBAAgB;AAAA,YAC9C,MAAM,mBAAmB;AAAA,UAC3B;AAAA,QACF,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,YACL,KAAK,mBAAmB,MAAM,mBAAmB,SAAS;AAAA,YAC1D,MAAM,mBAAmB;AAAA,UAC3B;AAAA,QACF,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO,QAAQ,YAAY;AAAA,QAC7B,KAAK;AACH,iBAAO,QAAQ,WAAW;AAAA,MAC9B;AAAA,IACF;AAOA,IAAM,wBAAwB,CAAC,OAAO,MAAM,oBAAoB,cAAc,kBAAkB;AAC9F,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,iBAAO,4BAA4B,MAAM,oBAAoB,cAAc,aAAa;AAAA,QAC1F,KAAK;AACH,iBAAO,yBAAyB,MAAM,oBAAoB,cAAc,aAAa;AAAA,QACvF,KAAK;AAAA,QACL;AACE,iBAAO;AAAA,YACL,KAAK;AAAA,YACL,MAAM;AAAA,UACR;AAAA,MACJ;AAAA,IACF;AAUA,IAAM,2BAA2B,CAAC,MAAM,oBAAoB,cAAc,kBAAkB;AAC1F,cAAQ,MAAM;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,YACL,KAAK,EAAE,gBAAgB,mBAAmB;AAAA,YAC1C,MAAM;AAAA,UACR;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AACE,iBAAO;AAAA,YACL,KAAK;AAAA,YACL,MAAM,EAAE,eAAe,mBAAmB;AAAA,UAC5C;AAAA,MACJ;AAAA,IACF;AAUA,IAAM,8BAA8B,CAAC,MAAM,oBAAoB,cAAc,kBAAkB;AAC7F,cAAQ,MAAM;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,YACL,KAAK,EAAE,gBAAgB,IAAI,mBAAmB,SAAS;AAAA,YACvD,MAAM;AAAA,UACR;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AACE,iBAAO;AAAA,YACL,KAAK;AAAA,YACL,MAAM,EAAE,eAAe,IAAI,mBAAmB,QAAQ;AAAA,UACxD;AAAA,MACJ;AAAA,IACF;AAMA,IAAM,4BAA4B,CAAC,MAAM,UAAU,WAAW,aAAa,WAAW,YAAY,cAAc,eAAe,gBAAgB,gBAAgB,gBAAgB,oBAAoB,gBAAgB,GAAG,iBAAiB,GAAG,cAAc,MAAM;AAC5P,UAAI,WAAW;AACf,YAAM,YAAY;AAClB,UAAI,OAAO;AACX,UAAI,MAAM;AACV,UAAI;AACJ,UAAI,UAAU;AACd,UAAI,UAAU;AACd,UAAI,oBAAoB;AACxB,UAAI,qBAAqB;AACzB,YAAM,aAAa,qBAAqB,mBAAmB,MAAM,mBAAmB,SAAS,aAAa,IAAI,gBAAgB;AAC9H,YAAM,gBAAgB,qBAAqB,mBAAmB,SAAS;AACvE,UAAI,wBAAwB;AAK5B,UAAI,OAAO,cAAc,gBAAgB;AACvC,eAAO;AACP,4BAAoB;AACpB,kBAAU;AAAA,MAKZ,WAAW,eAAe,cAAc,OAAO,iBAAiB,WAAW;AACzE,6BAAqB;AACrB,eAAO,YAAY,eAAe;AAClC,kBAAU;AAAA,MACZ;AAQA,UAAI,aAAa,gBAAgB,gBAAgB,eAAe,SAAS,SAAS,SAAS,WAAW;AACpG,YAAI,aAAa,gBAAgB,GAAG;AAWlC,gBAAM,KAAK,IAAI,IAAI,aAAa,gBAAgB,iBAAiB,cAAc,EAAE;AACjF,qBAAW,MAAM;AACjB,oBAAU;AACV,kCAAwB;AAAA,QAK1B,OAAO;AACL,mBAAS;AAAA,QACX;AAAA,MACF;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,IAAM,kBAAkB,CAAC,MAAM,kBAAkB,OAAO,IAAI,YAAY;AAUtE,UAAI,CAAC,MAAM,CAAC,SAAS;AACnB,eAAO;AAAA,MACT;AAQA,UAAI,SAAS,SAAS,SAAS,YAAY,iBAAiB;AAC1D,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,IAAM,2BAA2B;AAKjC,IAAM,oBAAoB,CAAC,QAAQ,SAAS;AAC1C,UAAI;AACJ,YAAM;AAAA,QACJ,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,MAAM,OAAO;AACnB,YAAM,QAAQ,IAAI,QAAQ;AAC1B,YAAM,YAAY,IAAI,YAAY;AAClC,YAAM,aAAa,IAAI,YAAY;AACnC,YAAM,OAAO,eAAe,MAAM;AAClC,YAAM,YAAY,KAAK,cAAc,kBAAkB;AACvD,YAAM,UAAU,KAAK,cAAc,gBAAgB;AACnD,YAAM,kBAAkB,aAAa,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,qBAAqB,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG;AACrM,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,qBAAqB,MAAM,WAAW,eAAe;AACzD,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,mBAAmB,OAAO;AAC9B,YAAM,kBAAkB;AAAA,QACtB,KAAK,aAAa,IAAI,gBAAgB;AAAA,QACtC,MAAM,YAAY,IAAI,eAAe;AAAA,QACrC,SAAS,QAAQ,UAAU;AAAA,QAC3B,SAAS;AAAA,MACX;AACA,YAAM,UAAU,mBAAmB,OAAO,cAAc,eAAe,YAAY,aAAa,WAAW,MAAM,OAAO,iBAAiB,SAAS,EAAE;AACpJ,YAAM,UAAU,SAAS,UAAU,IAAI;AACvC,YAAM,SAAS,SAAS,UAAU,IAAI;AACtC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,0BAA0B,MAAM,QAAQ,KAAK,QAAQ,MAAM,SAAS,WAAW,YAAY,cAAc,eAAe,QAAQ,QAAQ,SAAS,QAAQ,SAAS,QAAQ,sBAAsB,QAAQ,UAAU,QAAQ,WAAW,WAAW;AACpP,YAAM,gBAAgB,gBAAgB;AACtC,YAAM,oBAAoB,gBAAgB;AAC1C,YAAM,mBAAmB,gBAAgB;AACzC,wBAAkB,WAAW,KAAK,cAAc,cAAc,CAAC,EAAE,OAAO,WAAW,MAAM,yBAAyB,EAAE,aAAa;AAAA,QAC/H,kBAAkB;AAAA,MACpB,CAAC,EAAE,iBAAiB,CAAC,gBAAgB,CAAC;AAKtC,uBAAiB,WAAW,KAAK,cAAc,gBAAgB,CAAC,EAAE,WAAW,KAAK,cAAc,kBAAkB,CAAC,EAAE,OAAO,WAAW,MAAM,CAAC;AAE9I,aAAO,cAAc,OAAO,MAAM,EAAE,SAAS,GAAG,EAAE,eAAe,MAAM;AACrE,YAAI,SAAS,SAAS;AACpB,iBAAO,MAAM,YAAY,WAAW,GAAG,YAAY,IAAI;AAAA,QACzD;AACA,YAAI,uBAAuB;AACzB,iBAAO,UAAU,IAAI,gBAAgB;AAAA,QACvC;AACA,YAAI,WAAW,QAAW;AACxB,oBAAU,MAAM,YAAY,UAAU,GAAG,MAAM,IAAI;AAAA,QACrD;AACA,cAAM,eAAe;AACrB,cAAM,gBAAgB;AACtB,YAAI,YAAY,GAAG,IAAI;AACvB,YAAI,mBAAmB;AACrB,sBAAY,GAAG,IAAI,KAAK,YAAY;AAAA,QACtC;AACA,YAAI,oBAAoB;AACtB,sBAAY,GAAG,IAAI,KAAK,aAAa;AAAA,QACvC;AACA,kBAAU,MAAM,YAAY,OAAO,QAAQ,GAAG,0BAA0B;AACxE,kBAAU,MAAM,YAAY,QAAQ,QAAQ,SAAS,wBAAwB;AAC7E,kBAAU,MAAM,YAAY,oBAAoB,GAAG,OAAO,IAAI,OAAO,EAAE;AACvE,YAAI,YAAY,MAAM;AACpB,gBAAM,kBAAkB,QAAQ,QAAQ,OAAO,QAAQ,SAAS;AAChE,gBAAM,YAAY,gBAAgB,MAAM,iBAAiB,IAAI,OAAO;AACpE,cAAI,WAAW;AACb,oBAAQ,MAAM,YAAY,OAAO,QAAQ,QAAQ,0BAA0B;AAC3E,oBAAQ,MAAM,YAAY,QAAQ,QAAQ,SAAS,0BAA0B;AAAA,UAC/E,OAAO;AACL,oBAAQ,MAAM,YAAY,WAAW,MAAM;AAAA,UAC7C;AAAA,QACF;AAAA,MACF,CAAC,EAAE,aAAa,CAAC,mBAAmB,gBAAgB,CAAC;AAAA,IACvD;AAKA,IAAM,oBAAoB,YAAU;AAClC,YAAM,OAAO,eAAe,MAAM;AAClC,YAAM,YAAY,KAAK,cAAc,kBAAkB;AACvD,YAAM,UAAU,KAAK,cAAc,gBAAgB;AACnD,YAAM,gBAAgB,gBAAgB;AACtC,YAAM,oBAAoB,gBAAgB;AAC1C,YAAM,mBAAmB,gBAAgB;AACzC,wBAAkB,WAAW,KAAK,cAAc,cAAc,CAAC,EAAE,OAAO,WAAW,2BAA2B,CAAC;AAC/G,uBAAiB,WAAW,KAAK,cAAc,gBAAgB,CAAC,EAAE,WAAW,KAAK,cAAc,kBAAkB,CAAC,EAAE,OAAO,WAAW,MAAM,CAAC;AAC9I,aAAO,cAAc,OAAO,MAAM,EAAE,cAAc,MAAM;AACtD,eAAO,MAAM,eAAe,SAAS;AACrC,eAAO,UAAU,OAAO,gBAAgB;AACxC,kBAAU,MAAM,eAAe,KAAK;AACpC,kBAAU,MAAM,eAAe,MAAM;AACrC,kBAAU,MAAM,eAAe,QAAQ;AACvC,kBAAU,MAAM,eAAe,kBAAkB;AACjD,YAAI,SAAS;AACX,kBAAQ,MAAM,eAAe,KAAK;AAClC,kBAAQ,MAAM,eAAe,MAAM;AACnC,kBAAQ,MAAM,eAAe,SAAS;AAAA,QACxC;AAAA,MACF,CAAC,EAAE,SAAS,GAAG,EAAE,aAAa,CAAC,mBAAmB,gBAAgB,CAAC;AAAA,IACrE;AACA,IAAM,0BAA0B;AAKhC,IAAM,mBAAmB,CAAC,QAAQ,SAAS;AACzC,UAAI;AACJ,YAAM;AAAA,QACJ,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,MAAM,OAAO;AACnB,YAAM,QAAQ,IAAI,QAAQ;AAC1B,YAAM,YAAY,IAAI,YAAY;AAClC,YAAM,aAAa,IAAI,YAAY;AACnC,YAAM,OAAO,eAAe,MAAM;AAClC,YAAM,YAAY,KAAK,cAAc,kBAAkB;AACvD,YAAM,kBAAkB,aAAa,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,qBAAqB,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG;AACrM,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,qBAAqB,MAAM,WAAW,eAAe;AACzD,YAAM,kBAAkB;AAAA,QACtB,KAAK,aAAa,IAAI,gBAAgB;AAAA,QACtC,MAAM,YAAY,IAAI,eAAe;AAAA,QACrC,SAAS,QAAQ,UAAU;AAAA,QAC3B,SAAS;AAAA,MACX;AACA,YAAM,UAAU,mBAAmB,OAAO,cAAc,eAAe,GAAG,GAAG,WAAW,MAAM,OAAO,iBAAiB,SAAS,EAAE;AACjI,YAAM,UAAU,SAAS,UAAU,IAAI;AACvC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,0BAA0B,MAAM,QAAQ,KAAK,QAAQ,MAAM,SAAS,WAAW,YAAY,cAAc,eAAe,GAAG,QAAQ,SAAS,QAAQ,SAAS,QAAQ,oBAAoB;AAC7L,YAAM,gBAAgB,gBAAgB;AACtC,YAAM,oBAAoB,gBAAgB;AAC1C,YAAM,mBAAmB,gBAAgB;AACzC,YAAM,mBAAmB,gBAAgB;AACzC,YAAM,oBAAoB,gBAAgB;AAC1C,wBAAkB,WAAW,KAAK,cAAc,cAAc,CAAC,EAAE,OAAO,WAAW,MAAM,yBAAyB,EAAE,aAAa;AAAA,QAC/H,kBAAkB;AAAA,MACpB,CAAC,EAAE,iBAAiB,CAAC,gBAAgB,CAAC;AACtC,uBAAiB,WAAW,KAAK,cAAc,kBAAkB,CAAC,EAAE,SAAS,GAAG,EAAE,OAAO,WAAW,MAAM,CAAC;AAC3G,uBAAiB,WAAW,SAAS,EAAE,aAAa;AAAA,QAClD,KAAK,QAAQ,GAAG;AAAA,QAChB,MAAM,QAAQ,IAAI;AAAA,QAClB,oBAAoB,GAAG,OAAO,IAAI,OAAO;AAAA,MAC3C,CAAC,EAAE,eAAe,MAAM;AACtB,YAAI,WAAW,QAAW;AACxB,oBAAU,MAAM,YAAY,UAAU,GAAG,MAAM,IAAI;AAAA,QACrD;AAAA,MACF,CAAC,EAAE,OAAO,aAAa,cAAc,UAAU;AAC/C,wBAAkB,WAAW,KAAK,cAAc,mBAAmB,CAAC,EAAE,OAAO,WAAW,MAAM,CAAC;AAC/F,aAAO,cAAc,OAAO,gCAAgC,EAAE,SAAS,GAAG,EAAE,eAAe,MAAM;AAC/F,YAAI,SAAS,SAAS;AACpB,iBAAO,MAAM,YAAY,WAAW,GAAG,YAAY,IAAI;AAAA,QACzD;AACA,YAAI,YAAY,UAAU;AACxB,iBAAO,UAAU,IAAI,gBAAgB;AAAA,QACvC;AAAA,MACF,CAAC,EAAE,aAAa,CAAC,mBAAmB,kBAAkB,kBAAkB,iBAAiB,CAAC;AAAA,IAC5F;AAKA,IAAM,mBAAmB,YAAU;AACjC,YAAM,OAAO,eAAe,MAAM;AAClC,YAAM,YAAY,KAAK,cAAc,kBAAkB;AACvD,YAAM,gBAAgB,gBAAgB;AACtC,YAAM,oBAAoB,gBAAgB;AAC1C,YAAM,mBAAmB,gBAAgB;AACzC,wBAAkB,WAAW,KAAK,cAAc,cAAc,CAAC,EAAE,OAAO,WAAW,2BAA2B,CAAC;AAC/G,uBAAiB,WAAW,KAAK,cAAc,kBAAkB,CAAC,EAAE,OAAO,WAAW,MAAM,CAAC;AAC7F,aAAO,cAAc,OAAO,MAAM,EAAE,cAAc,MAAM;AACtD,eAAO,MAAM,eAAe,SAAS;AACrC,eAAO,UAAU,OAAO,gBAAgB;AACxC,kBAAU,MAAM,eAAe,KAAK;AACpC,kBAAU,MAAM,eAAe,MAAM;AACrC,kBAAU,MAAM,eAAe,QAAQ;AACvC,kBAAU,MAAM,eAAe,kBAAkB;AAAA,MACnD,CAAC,EAAE,SAAS,GAAG,EAAE,aAAa,CAAC,mBAAmB,gBAAgB,CAAC;AAAA,IACrE;AACA,IAAM,gBAAgB;AACtB,IAAM,sBAAsB;AAC5B,IAAM,eAAe;AACrB,IAAM,qBAAqB;AAC3B,IAAM,UAAU,MAAM;AAAA,MACpB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,aAAa,YAAY,MAAM,wBAAwB,CAAC;AAC7D,aAAK,cAAc,YAAY,MAAM,yBAAyB,CAAC;AAC/D,aAAK,cAAc,YAAY,MAAM,yBAAyB,CAAC;AAC/D,aAAK,aAAa,YAAY,MAAM,wBAAwB,CAAC;AAC7D,aAAK,sBAAsB,YAAY,MAAM,cAAc,CAAC;AAC5D,aAAK,uBAAuB,YAAY,MAAM,eAAe,CAAC;AAC9D,aAAK,uBAAuB,YAAY,MAAM,eAAe,CAAC;AAC9D,aAAK,sBAAsB,YAAY,MAAM,cAAc,CAAC;AAC5D,aAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,aAAK,gBAAgB;AACrB,aAAK,eAAe,aAAa;AACjC,aAAK,iBAAiB,qBAAqB;AAC3C,aAAK,SAAS;AACd,aAAK,2BAA2B;AAChC,aAAK,gBAAgB,MAAM;AACzB,eAAK,QAAQ,QAAW,QAAQ;AAAA,QAClC;AACA,aAAK,cAAc,gBAAc;AAC/B,gBAAM,KAAK,KAAK;AAChB,gBAAM,OAAO,cAAc,WAAW,IAAI;AAC1C,cAAI,MAAM,MAAM;AACd,kBAAM,QAAQ,IAAI,YAAY,MAAM;AAAA,cAClC,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ,WAAW;AAAA,YACrB,CAAC;AACD,eAAG,cAAc,KAAK;AAAA,UACxB;AAAA,QACF;AACA,aAAK,8BAA8B,MAAM;AACvC,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,2BAA2B;AAC7B,sCAA0B;AAAA,UAC5B;AACA,cAAI,YAAY,QAAW;AACzB;AAAA,UACF;AACA,gBAAM,YAAY,KAAK,YAAY,YAAY,SAAY,SAAS,eAAe,OAAO,IAAI;AAC9F,cAAI,CAAC,WAAW;AACd,4BAAgB,kDAAkD,OAAO,yHAAyH,KAAK,EAAE;AACzM;AAAA,UACF;AACA,eAAK,4BAA4B,4BAA4B,WAAW,eAAe,EAAE;AAAA,QAC3F;AACA,aAAK,+BAA+B,MAAM;AACxC,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,4BAA4B;AAC9B,uCAA2B;AAAA,UAC7B;AACA,eAAK,6BAA6B,6BAA6B,EAAE;AAAA,QACnE;AACA,aAAK,8BAA8B,MAAM;AACvC,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,CAAC,iBAAiB,CAAC,WAAW;AAChC;AAAA,UACF;AACA,cAAI,2BAA2B;AAC7B,sCAA0B;AAAA,UAC5B;AACA,eAAK,4BAA4B,4BAA4B,WAAW,eAAe,IAAI,aAAa;AAAA,QAC1G;AACA,aAAK,YAAY;AACjB,aAAK,gBAAgB;AACrB,aAAK,WAAW;AAChB,aAAK,eAAe;AACpB,aAAK,iBAAiB;AACtB,aAAK,iBAAiB;AACtB,aAAK,YAAY;AACjB,aAAK,iBAAiB;AACtB,aAAK,gBAAgB;AACrB,aAAK,WAAW;AAChB,aAAK,kBAAkB;AACvB,aAAK,QAAQ;AACb,aAAK,eAAe;AACpB,aAAK,cAAc;AACnB,aAAK,WAAW;AAChB,aAAK,iBAAiB;AACtB,aAAK,gBAAgB;AACrB,aAAK,UAAU;AACf,aAAK,OAAO;AACZ,aAAK,kBAAkB;AACvB,aAAK,YAAY;AACjB,aAAK,OAAO;AACZ,aAAK,YAAY;AACjB,aAAK,QAAQ;AACb,aAAK,SAAS;AACd,aAAK,iBAAiB;AACtB,aAAK,YAAY;AACjB,aAAK,sBAAsB;AAAA,MAC7B;AAAA,MACA,kBAAkB;AAChB,aAAK,4BAA4B;AAAA,MACnC;AAAA,MACA,eAAe,UAAU,UAAU;AACjC,YAAI,aAAa,QAAQ,aAAa,OAAO;AAC3C,eAAK,QAAQ;AAAA,QACf,WAAW,aAAa,SAAS,aAAa,MAAM;AAClD,eAAK,QAAQ;AAAA,QACf;AAAA,MACF;AAAA,MACA,oBAAoB;AAClB,cAAM;AAAA,UACJ,6BAAAA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,uBAAe,EAAE;AACjB,QAAAA,6BAA4B;AAAA,MAC9B;AAAA,MACA,uBAAuB;AACrB,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,YAAI,2BAA2B;AAC7B,oCAA0B;AAAA,QAC5B;AAAA,MACF;AAAA,MACA,oBAAoB;AAClB,YAAI,IAAI;AACR,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,cAAM,aAAa,MAAM,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,QAAQ,OAAO,SAAS,KAAK,aAAa,EAAE;AAC/I,aAAK,gBAAgB,GAAG,QAAQ,oBAAoB,SAAS,GAAG;AAChE,YAAI,KAAK,cAAc,QAAW;AAChC,eAAK,YAAY,WAAW,IAAI,MAAM,QAAQ,WAAW;AAAA,QAC3D;AAAA,MACF;AAAA,MACA,mBAAmB;AACjB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AAKJ,YAAI,WAAW,MAAM;AACnB,cAAI,MAAM,KAAK,QAAQ,CAAC;AAAA,QAC1B;AACA,YAAI,eAAe;AACjB,2BAAiB,eAAe,yBAAyB,MAAM;AAC7D,iBAAK,QAAQ,QAAW,QAAW,KAAK;AAAA,UAC1C,CAAC;AAAA,QACH;AAUA,aAAK,4BAA4B;AAAA,MACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUM,mBAAmB,OAAO,kBAAkB,OAAO;AAAA;AACvD,eAAK,2BAA2B;AAChC,gBAAM,KAAK,QAAQ,KAAK;AACxB,eAAK,2BAA2B;AAAA,QAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUA,YAAY,QAAQ,OAAO;AACzB,YAAI,KAAK,mBAAmB,CAAC,OAAO;AAClC,iBAAO;AAAA,YACL,UAAU,KAAK;AAAA,YACf,QAAQ,KAAK;AAAA,UACf;AAAA,QACF;AAUA,cAAM,WAAW,KAAK,GAAG;AACzB,cAAM,SAAS,KAAK,SAAS,aAAa,QAAQ,CAAC,KAAK;AACxD,cAAM,WAAW,KAAK,kBAAkB,SAAS,KAAK,YAAY,KAAK,eAAe,KAAK;AAC3F,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOM,QAAQ,OAAO;AAAA;AACnB,gBAAM,SAAS,MAAM,KAAK,eAAe,KAAK;AAC9C,cAAI,KAAK,WAAW;AAClB,mBAAO;AACP;AAAA,UACF;AACA,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,KAAK,YAAY,IAAI;AAMzB,eAAK,SAAS,KAAK;AACnB,eAAK,eAAe,MAAM,gBAAgB,UAAU,IAAI,KAAK,WAAW,CAAC,kBAAkB,GAAG,KAAK,gBAAgB,MAAM;AACzH,cAAI,CAAC,KAAK,gBAAgB;AACxB,iBAAK,6BAA6B;AAAA,UACpC;AACA,eAAK,4BAA4B;AASjC,cAAI,aAAa,EAAE,GAAG;AACpB,kBAAM,UAAU,KAAK,YAAY;AAAA,UASnC,WAAW,CAAC,KAAK,qBAAqB;AACpC,kBAAM,aAAa;AAAA,UACrB;AACA,gBAAM,QAAQ,MAAM,gBAAgB,mBAAmB,kBAAkB;AAAA,YACvE,OAAO,SAAS,KAAK;AAAA,YACrB,MAAM,KAAK;AAAA,YACX,SAAS,KAAK;AAAA,YACd,WAAW,KAAK;AAAA,YAChB,MAAM,KAAK;AAAA,YACX,OAAO,KAAK;AAAA,UACd,CAAC;AAOD,cAAI,KAAK,0BAA0B;AACjC,iCAAqB,EAAE;AAAA,UACzB;AACA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaM,QAAQ,MAAM,MAAM,uBAAuB,MAAM;AAAA;AACrD,gBAAM,SAAS,MAAM,KAAK,eAAe,KAAK;AAC9C,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,wBAAwB,KAAK,eAAe;AAC9C,iBAAK,cAAc,QAAQ,MAAM,MAAM,oBAAoB;AAAA,UAC7D;AACA,gBAAM,gBAAgB,MAAM,QAAQ,MAAM,MAAM,MAAM,gBAAgB,mBAAmB,kBAAkB,KAAK,KAAK;AACrH,cAAI,eAAe;AACjB,gBAAI,4BAA4B;AAC9B,yCAA2B;AAC3B,mBAAK,6BAA6B;AAAA,YACpC;AACA,gBAAI,2BAA2B;AAC7B,wCAA0B;AAC1B,mBAAK,4BAA4B;AAAA,YACnC;AAMA,kBAAM;AAAA,cACJ;AAAA,YACF,IAAI,KAAK,YAAY;AACrB,kBAAM,gBAAgB,UAAU,KAAK,YAAY;AAAA,UACnD;AACA,iBAAO;AACP,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAIM,mBAAmB;AAAA;AACvB,iBAAO,KAAK;AAAA,QACd;AAAA;AAAA;AAAA;AAAA;AAAA,MAIA,eAAe;AACb,eAAO,YAAY,KAAK,IAAI,sBAAsB;AAAA,MACpD;AAAA;AAAA;AAAA;AAAA,MAIA,gBAAgB;AACd,eAAO,YAAY,KAAK,IAAI,uBAAuB;AAAA,MACrD;AAAA,MACA,SAAS;AACP,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,UAAU,WAAW,SAAS;AACpC,cAAM,cAAc,SAAS,CAAC;AAC9B,eAAO,EAAE,MAAM,OAAO,OAAO;AAAA,UAC3B,KAAK;AAAA,UACL,cAAc;AAAA,UACd,aAAa;AAAA,UACb,UAAU;AAAA,QACZ,GAAG,gBAAgB;AAAA,UACjB,OAAO;AAAA,YACL,QAAQ,GAAG,MAAQ,KAAK,YAAY;AAAA,UACtC;AAAA,UACA,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,KAAK,QAAQ,CAAC,GAAG;AAAA,YAClE,CAAC,IAAI,GAAG;AAAA,YACR,uBAAuB,KAAK;AAAA,YAC5B,kBAAkB;AAAA,YAClB,mBAAmB;AAAA,YACnB,CAAC,gBAAgB,IAAI,EAAE,GAAG;AAAA,YAC1B,CAAC,wBAAwB,GAAG,cAAc;AAAA,YAC1C,kBAAkB,CAAC,CAAC;AAAA,UACtB,CAAC;AAAA,UACD,wBAAwB;AAAA,UACxB,yBAAyB;AAAA,UACzB,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,UACxB,kBAAkB,KAAK;AAAA,QACzB,CAAC,GAAG,CAAC,iBAAiB,EAAE,gBAAgB;AAAA,UACtC,KAAK;AAAA,UACL,UAAU,KAAK;AAAA,UACf,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,QACR,CAAC,GAAG,EAAE,OAAO;AAAA,UACX,KAAK;AAAA,UACL,OAAO;AAAA,UACP,SAAS,kBAAkB,MAAM,KAAK,QAAQ,IAAI;AAAA,QACpD,GAAG,eAAe,EAAE,OAAO;AAAA,UACzB,KAAK;AAAA,UACL,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC,GAAG,EAAE,OAAO;AAAA,UACX,KAAK;AAAA,UACL,OAAO;AAAA,UACP,MAAM;AAAA,QACR,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,QACP,CAAC,CAAC,CAAC,CAAC;AAAA,MACN;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,WAAW,CAAC,iBAAiB;AAAA,UAC7B,iBAAiB,CAAC,iBAAiB;AAAA,UACnC,UAAU,CAAC,gBAAgB;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AACA,IAAM,gBAAgB;AAAA,MACpB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,sBAAsB;AAAA,IACxB;AACA,YAAQ,QAAQ;AAAA,MACd,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AAAA;AAAA;", "names": ["configureTriggerInteraction"], "x_google_ignoreList": [0]}