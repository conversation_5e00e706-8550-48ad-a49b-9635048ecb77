import {
  getCapacitor,
  init_capacitor_59395cbd
} from "./chunk-2Q24MQWY.js";
import {
  __esm
} from "./chunk-4W6HR7MY.js";

// node_modules/@ionic/core/dist/esm/haptic-ac164e4c.js
var ImpactStyle, NotificationType, HapticEngine, hapticAvailable, hapticSelection, hapticSelectionStart, hapticSelectionChanged, hapticSelectionEnd, hapticImpact;
var init_haptic_ac164e4c = __esm({
  "node_modules/@ionic/core/dist/esm/haptic-ac164e4c.js"() {
    "use strict";
    init_capacitor_59395cbd();
    (function(ImpactStyle2) {
      ImpactStyle2["Heavy"] = "HEAVY";
      ImpactStyle2["Medium"] = "MEDIUM";
      ImpactStyle2["Light"] = "LIGHT";
    })(ImpactStyle || (ImpactStyle = {}));
    (function(NotificationType2) {
      NotificationType2["Success"] = "SUCCESS";
      NotificationType2["Warning"] = "WARNING";
      NotificationType2["Error"] = "ERROR";
    })(NotificationType || (NotificationType = {}));
    HapticEngine = {
      getEngine() {
        const capacitor = getCapacitor();
        if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable("Haptics")) {
          return capacitor.Plugins.Haptics;
        }
        return void 0;
      },
      available() {
        const engine = this.getEngine();
        if (!engine) {
          return false;
        }
        const capacitor = getCapacitor();
        if ((capacitor === null || capacitor === void 0 ? void 0 : capacitor.getPlatform()) === "web") {
          return typeof navigator !== "undefined" && navigator.vibrate !== void 0;
        }
        return true;
      },
      impact(options) {
        const engine = this.getEngine();
        if (!engine) {
          return;
        }
        engine.impact({
          style: options.style
        });
      },
      notification(options) {
        const engine = this.getEngine();
        if (!engine) {
          return;
        }
        engine.notification({
          type: options.type
        });
      },
      selection() {
        this.impact({
          style: ImpactStyle.Light
        });
      },
      selectionStart() {
        const engine = this.getEngine();
        if (!engine) {
          return;
        }
        engine.selectionStart();
      },
      selectionChanged() {
        const engine = this.getEngine();
        if (!engine) {
          return;
        }
        engine.selectionChanged();
      },
      selectionEnd() {
        const engine = this.getEngine();
        if (!engine) {
          return;
        }
        engine.selectionEnd();
      }
    };
    hapticAvailable = () => {
      return HapticEngine.available();
    };
    hapticSelection = () => {
      hapticAvailable() && HapticEngine.selection();
    };
    hapticSelectionStart = () => {
      hapticAvailable() && HapticEngine.selectionStart();
    };
    hapticSelectionChanged = () => {
      hapticAvailable() && HapticEngine.selectionChanged();
    };
    hapticSelectionEnd = () => {
      hapticAvailable() && HapticEngine.selectionEnd();
    };
    hapticImpact = (options) => {
      hapticAvailable() && HapticEngine.impact(options);
    };
  }
});

export {
  ImpactStyle,
  hapticSelection,
  hapticSelectionStart,
  hapticSelectionChanged,
  hapticSelectionEnd,
  hapticImpact,
  init_haptic_ac164e4c
};
/*! Bundled license information:

@ionic/core/dist/esm/haptic-ac164e4c.js:
  (*!
   * (C) Ionic http://ionicframework.com - MIT License
   *)
*/
//# sourceMappingURL=chunk-5XAFDKGG.js.map
