﻿using FluentNHibernate.Mapping;
using GoodDriver.Domain.Users;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Infrastructure.Database.Maps
{
    public class UserMap : ClassMap<User>
    {
        public UserMap()
        {
            Id(u => u.Id);
            Map(a => a.Name).Not.Nullable();
			Map(a => a.Phone).Not.Nullable();
			Map(a => a.Active).Not.Nullable();
			Map(a => a.Email).Not.Nullable();
			Map(a => a.Password).Not.Nullable();
            Map(a => a.Token).Nullable();
            Map(a => a.CreatedOn).Not.Nullable();            
            Map(a => a.UpdatedOn).Nullable();
			Map(a => a.Status).Not.Nullable();
			Map(a => a.ImageUrl).Nullable();
			Map(a => a.LastAccess).Nullable();
			Map(a => a.ZipCode).Nullable();
			Map(a => a.PasswordSalt).Not.Nullable();

            HasMany(x => x.Vehicles)
                .KeyColumn("UserId")
                .ExtraLazyLoad()
                .AsBag()
                .BatchSize(15)
                .Inverse()
                .Cascade.AllDeleteOrphan()
                .Table("Vehicle");
        }
    }
}
