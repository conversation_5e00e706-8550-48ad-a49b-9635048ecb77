import {
  WebPlugin,
  init_dist
} from "./chunk-WY354WYL.js";
import {
  __async,
  __esm
} from "./chunk-4W6HR7MY.js";

// node_modules/@capacitor/app/dist/esm/web.js
var AppWeb;
var init_web = __esm({
  "node_modules/@capacitor/app/dist/esm/web.js"() {
    init_dist();
    AppWeb = class extends WebPlugin {
      constructor() {
        super();
        this.handleVisibilityChange = () => {
          const data = {
            isActive: document.hidden !== true
          };
          this.notifyListeners("appStateChange", data);
          if (document.hidden) {
            this.notifyListeners("pause", null);
          } else {
            this.notifyListeners("resume", null);
          }
        };
        document.addEventListener("visibilitychange", this.handleVisibilityChange, false);
      }
      exitApp() {
        throw this.unimplemented("Not implemented on web.");
      }
      getInfo() {
        return __async(this, null, function* () {
          throw this.unimplemented("Not implemented on web.");
        });
      }
      getLaunchUrl() {
        return __async(this, null, function* () {
          return {
            url: ""
          };
        });
      }
      getState() {
        return __async(this, null, function* () {
          return {
            isActive: document.hidden !== true
          };
        });
      }
      minimizeApp() {
        return __async(this, null, function* () {
          throw this.unimplemented("Not implemented on web.");
        });
      }
    };
  }
});
init_web();
export {
  AppWeb
};
//# sourceMappingURL=web-JSNOAPSN.js.map
