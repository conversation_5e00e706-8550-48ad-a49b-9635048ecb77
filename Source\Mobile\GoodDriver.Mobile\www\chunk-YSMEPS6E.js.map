{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/gesture-controller-314a54f6.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nclass GestureController {\n  constructor() {\n    this.gestureId = 0;\n    this.requestedStart = new Map();\n    this.disabledGestures = new Map();\n    this.disabledScroll = new Set();\n  }\n  /**\n   * Creates a gesture delegate based on the GestureConfig passed\n   */\n  createGesture(config) {\n    var _a;\n    return new GestureDelegate(this, this.newID(), config.name, (_a = config.priority) !== null && _a !== void 0 ? _a : 0, !!config.disableScroll);\n  }\n  /**\n   * Creates a blocker that will block any other gesture events from firing. Set in the ion-gesture component.\n   */\n  createBlocker(opts = {}) {\n    return new BlockerDelegate(this, this.newID(), opts.disable, !!opts.disableScroll);\n  }\n  start(gestureName, id, priority) {\n    if (!this.canStart(gestureName)) {\n      this.requestedStart.delete(id);\n      return false;\n    }\n    this.requestedStart.set(id, priority);\n    return true;\n  }\n  capture(gestureName, id, priority) {\n    if (!this.start(gestureName, id, priority)) {\n      return false;\n    }\n    const requestedStart = this.requestedStart;\n    let maxPriority = -10000;\n    requestedStart.forEach(value => {\n      maxPriority = Math.max(maxPriority, value);\n    });\n    if (maxPriority === priority) {\n      this.capturedId = id;\n      requestedStart.clear();\n      const event = new CustomEvent('ionGestureCaptured', {\n        detail: {\n          gestureName\n        }\n      });\n      document.dispatchEvent(event);\n      return true;\n    }\n    requestedStart.delete(id);\n    return false;\n  }\n  release(id) {\n    this.requestedStart.delete(id);\n    if (this.capturedId === id) {\n      this.capturedId = undefined;\n    }\n  }\n  disableGesture(gestureName, id) {\n    let set = this.disabledGestures.get(gestureName);\n    if (set === undefined) {\n      set = new Set();\n      this.disabledGestures.set(gestureName, set);\n    }\n    set.add(id);\n  }\n  enableGesture(gestureName, id) {\n    const set = this.disabledGestures.get(gestureName);\n    if (set !== undefined) {\n      set.delete(id);\n    }\n  }\n  disableScroll(id) {\n    this.disabledScroll.add(id);\n    if (this.disabledScroll.size === 1) {\n      document.body.classList.add(BACKDROP_NO_SCROLL);\n    }\n  }\n  enableScroll(id) {\n    this.disabledScroll.delete(id);\n    if (this.disabledScroll.size === 0) {\n      document.body.classList.remove(BACKDROP_NO_SCROLL);\n    }\n  }\n  canStart(gestureName) {\n    if (this.capturedId !== undefined) {\n      // a gesture already captured\n      return false;\n    }\n    if (this.isDisabled(gestureName)) {\n      return false;\n    }\n    return true;\n  }\n  isCaptured() {\n    return this.capturedId !== undefined;\n  }\n  isScrollDisabled() {\n    return this.disabledScroll.size > 0;\n  }\n  isDisabled(gestureName) {\n    const disabled = this.disabledGestures.get(gestureName);\n    if (disabled && disabled.size > 0) {\n      return true;\n    }\n    return false;\n  }\n  newID() {\n    this.gestureId++;\n    return this.gestureId;\n  }\n}\nclass GestureDelegate {\n  constructor(ctrl, id, name, priority, disableScroll) {\n    this.id = id;\n    this.name = name;\n    this.disableScroll = disableScroll;\n    this.priority = priority * 1000000 + id;\n    this.ctrl = ctrl;\n  }\n  canStart() {\n    if (!this.ctrl) {\n      return false;\n    }\n    return this.ctrl.canStart(this.name);\n  }\n  start() {\n    if (!this.ctrl) {\n      return false;\n    }\n    return this.ctrl.start(this.name, this.id, this.priority);\n  }\n  capture() {\n    if (!this.ctrl) {\n      return false;\n    }\n    const captured = this.ctrl.capture(this.name, this.id, this.priority);\n    if (captured && this.disableScroll) {\n      this.ctrl.disableScroll(this.id);\n    }\n    return captured;\n  }\n  release() {\n    if (this.ctrl) {\n      this.ctrl.release(this.id);\n      if (this.disableScroll) {\n        this.ctrl.enableScroll(this.id);\n      }\n    }\n  }\n  destroy() {\n    this.release();\n    this.ctrl = undefined;\n  }\n}\nclass BlockerDelegate {\n  constructor(ctrl, id, disable, disableScroll) {\n    this.id = id;\n    this.disable = disable;\n    this.disableScroll = disableScroll;\n    this.ctrl = ctrl;\n  }\n  block() {\n    if (!this.ctrl) {\n      return;\n    }\n    if (this.disable) {\n      for (const gesture of this.disable) {\n        this.ctrl.disableGesture(gesture, this.id);\n      }\n    }\n    if (this.disableScroll) {\n      this.ctrl.disableScroll(this.id);\n    }\n  }\n  unblock() {\n    if (!this.ctrl) {\n      return;\n    }\n    if (this.disable) {\n      for (const gesture of this.disable) {\n        this.ctrl.enableGesture(gesture, this.id);\n      }\n    }\n    if (this.disableScroll) {\n      this.ctrl.enableScroll(this.id);\n    }\n  }\n  destroy() {\n    this.unblock();\n    this.ctrl = undefined;\n  }\n}\nconst BACKDROP_NO_SCROLL = 'backdrop-no-scroll';\nconst GESTURE_CONTROLLER = new GestureController();\nexport { BACKDROP_NO_SCROLL as B, GESTURE_CONTROLLER as G };"], "mappings": ";;;;;AAAA,IAGM,mBA+GA,iBA2CA,iBAsCA,oBACA;AApMN;AAAA;AAAA;AAGA,IAAM,oBAAN,MAAwB;AAAA,MACtB,cAAc;AACZ,aAAK,YAAY;AACjB,aAAK,iBAAiB,oBAAI,IAAI;AAC9B,aAAK,mBAAmB,oBAAI,IAAI;AAChC,aAAK,iBAAiB,oBAAI,IAAI;AAAA,MAChC;AAAA;AAAA;AAAA;AAAA,MAIA,cAAc,QAAQ;AACpB,YAAI;AACJ,eAAO,IAAI,gBAAgB,MAAM,KAAK,MAAM,GAAG,OAAO,OAAO,KAAK,OAAO,cAAc,QAAQ,OAAO,SAAS,KAAK,GAAG,CAAC,CAAC,OAAO,aAAa;AAAA,MAC/I;AAAA;AAAA;AAAA;AAAA,MAIA,cAAc,OAAO,CAAC,GAAG;AACvB,eAAO,IAAI,gBAAgB,MAAM,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,KAAK,aAAa;AAAA,MACnF;AAAA,MACA,MAAM,aAAa,IAAI,UAAU;AAC/B,YAAI,CAAC,KAAK,SAAS,WAAW,GAAG;AAC/B,eAAK,eAAe,OAAO,EAAE;AAC7B,iBAAO;AAAA,QACT;AACA,aAAK,eAAe,IAAI,IAAI,QAAQ;AACpC,eAAO;AAAA,MACT;AAAA,MACA,QAAQ,aAAa,IAAI,UAAU;AACjC,YAAI,CAAC,KAAK,MAAM,aAAa,IAAI,QAAQ,GAAG;AAC1C,iBAAO;AAAA,QACT;AACA,cAAM,iBAAiB,KAAK;AAC5B,YAAI,cAAc;AAClB,uBAAe,QAAQ,WAAS;AAC9B,wBAAc,KAAK,IAAI,aAAa,KAAK;AAAA,QAC3C,CAAC;AACD,YAAI,gBAAgB,UAAU;AAC5B,eAAK,aAAa;AAClB,yBAAe,MAAM;AACrB,gBAAM,QAAQ,IAAI,YAAY,sBAAsB;AAAA,YAClD,QAAQ;AAAA,cACN;AAAA,YACF;AAAA,UACF,CAAC;AACD,mBAAS,cAAc,KAAK;AAC5B,iBAAO;AAAA,QACT;AACA,uBAAe,OAAO,EAAE;AACxB,eAAO;AAAA,MACT;AAAA,MACA,QAAQ,IAAI;AACV,aAAK,eAAe,OAAO,EAAE;AAC7B,YAAI,KAAK,eAAe,IAAI;AAC1B,eAAK,aAAa;AAAA,QACpB;AAAA,MACF;AAAA,MACA,eAAe,aAAa,IAAI;AAC9B,YAAI,MAAM,KAAK,iBAAiB,IAAI,WAAW;AAC/C,YAAI,QAAQ,QAAW;AACrB,gBAAM,oBAAI,IAAI;AACd,eAAK,iBAAiB,IAAI,aAAa,GAAG;AAAA,QAC5C;AACA,YAAI,IAAI,EAAE;AAAA,MACZ;AAAA,MACA,cAAc,aAAa,IAAI;AAC7B,cAAM,MAAM,KAAK,iBAAiB,IAAI,WAAW;AACjD,YAAI,QAAQ,QAAW;AACrB,cAAI,OAAO,EAAE;AAAA,QACf;AAAA,MACF;AAAA,MACA,cAAc,IAAI;AAChB,aAAK,eAAe,IAAI,EAAE;AAC1B,YAAI,KAAK,eAAe,SAAS,GAAG;AAClC,mBAAS,KAAK,UAAU,IAAI,kBAAkB;AAAA,QAChD;AAAA,MACF;AAAA,MACA,aAAa,IAAI;AACf,aAAK,eAAe,OAAO,EAAE;AAC7B,YAAI,KAAK,eAAe,SAAS,GAAG;AAClC,mBAAS,KAAK,UAAU,OAAO,kBAAkB;AAAA,QACnD;AAAA,MACF;AAAA,MACA,SAAS,aAAa;AACpB,YAAI,KAAK,eAAe,QAAW;AAEjC,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,WAAW,WAAW,GAAG;AAChC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,MACA,aAAa;AACX,eAAO,KAAK,eAAe;AAAA,MAC7B;AAAA,MACA,mBAAmB;AACjB,eAAO,KAAK,eAAe,OAAO;AAAA,MACpC;AAAA,MACA,WAAW,aAAa;AACtB,cAAM,WAAW,KAAK,iBAAiB,IAAI,WAAW;AACtD,YAAI,YAAY,SAAS,OAAO,GAAG;AACjC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,MACA,QAAQ;AACN,aAAK;AACL,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AACA,IAAM,kBAAN,MAAsB;AAAA,MACpB,YAAY,MAAM,IAAI,MAAM,UAAU,eAAe;AACnD,aAAK,KAAK;AACV,aAAK,OAAO;AACZ,aAAK,gBAAgB;AACrB,aAAK,WAAW,WAAW,MAAU;AACrC,aAAK,OAAO;AAAA,MACd;AAAA,MACA,WAAW;AACT,YAAI,CAAC,KAAK,MAAM;AACd,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,KAAK,SAAS,KAAK,IAAI;AAAA,MACrC;AAAA,MACA,QAAQ;AACN,YAAI,CAAC,KAAK,MAAM;AACd,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI,KAAK,QAAQ;AAAA,MAC1D;AAAA,MACA,UAAU;AACR,YAAI,CAAC,KAAK,MAAM;AACd,iBAAO;AAAA,QACT;AACA,cAAM,WAAW,KAAK,KAAK,QAAQ,KAAK,MAAM,KAAK,IAAI,KAAK,QAAQ;AACpE,YAAI,YAAY,KAAK,eAAe;AAClC,eAAK,KAAK,cAAc,KAAK,EAAE;AAAA,QACjC;AACA,eAAO;AAAA,MACT;AAAA,MACA,UAAU;AACR,YAAI,KAAK,MAAM;AACb,eAAK,KAAK,QAAQ,KAAK,EAAE;AACzB,cAAI,KAAK,eAAe;AACtB,iBAAK,KAAK,aAAa,KAAK,EAAE;AAAA,UAChC;AAAA,QACF;AAAA,MACF;AAAA,MACA,UAAU;AACR,aAAK,QAAQ;AACb,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AACA,IAAM,kBAAN,MAAsB;AAAA,MACpB,YAAY,MAAM,IAAI,SAAS,eAAe;AAC5C,aAAK,KAAK;AACV,aAAK,UAAU;AACf,aAAK,gBAAgB;AACrB,aAAK,OAAO;AAAA,MACd;AAAA,MACA,QAAQ;AACN,YAAI,CAAC,KAAK,MAAM;AACd;AAAA,QACF;AACA,YAAI,KAAK,SAAS;AAChB,qBAAW,WAAW,KAAK,SAAS;AAClC,iBAAK,KAAK,eAAe,SAAS,KAAK,EAAE;AAAA,UAC3C;AAAA,QACF;AACA,YAAI,KAAK,eAAe;AACtB,eAAK,KAAK,cAAc,KAAK,EAAE;AAAA,QACjC;AAAA,MACF;AAAA,MACA,UAAU;AACR,YAAI,CAAC,KAAK,MAAM;AACd;AAAA,QACF;AACA,YAAI,KAAK,SAAS;AAChB,qBAAW,WAAW,KAAK,SAAS;AAClC,iBAAK,KAAK,cAAc,SAAS,KAAK,EAAE;AAAA,UAC1C;AAAA,QACF;AACA,YAAI,KAAK,eAAe;AACtB,eAAK,KAAK,aAAa,KAAK,EAAE;AAAA,QAChC;AAAA,MACF;AAAA,MACA,UAAU;AACR,aAAK,QAAQ;AACb,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AACA,IAAM,qBAAqB;AAC3B,IAAM,qBAAqB,IAAI,kBAAkB;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}