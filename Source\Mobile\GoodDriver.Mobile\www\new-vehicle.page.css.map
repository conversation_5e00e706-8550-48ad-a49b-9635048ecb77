{"version": 3, "sources": ["src/app/pages/tabs/vehicles/create-vehicle/new-vehicle.page.scss"], "sourcesContent": ["ion-card {\r\n  margin-bottom: 16px;\r\n\r\n  ion-card-title {\r\n    display: flex;\r\n    align-items: center;\r\n    font-size: 18px;\r\n\r\n    ion-icon {\r\n      margin-right: 8px;\r\n      font-size: 24px;\r\n    }\r\n  }\r\n}\r\n\r\nion-card[color=\"light\"] {\r\n  ion-card-content {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px;\r\n\r\n    ion-icon {\r\n      font-size: 20px;\r\n      margin-right: 8px;\r\n    }\r\n  }\r\n}\r\n\r\n.offline-message {\r\n  margin-top: 16px;\r\n\r\n  p {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    ion-icon {\r\n      margin-right: 8px;\r\n      font-size: 18px;\r\n    }\r\n  }\r\n}\r\n\r\nion-item {\r\n  margin-bottom: 8px;\r\n}"], "mappings": ";AAAA;AACE,iBAAA;;AAEA,SAAA;AACE,WAAA;AACA,eAAA;AACA,aAAA;;AAEA,SAAA,eAAA;AACE,gBAAA;AACA,aAAA;;AAMJ,QAAA,CAAA,aAAA;AACE,WAAA;AACA,eAAA;AACA,WAAA;;AAEA,QAAA,CAAA,aAAA,iBAAA;AACE,aAAA;AACA,gBAAA;;AAKN,CAAA;AACE,cAAA;;AAEA,CAHF,gBAGE;AACE,WAAA;AACA,eAAA;AACA,mBAAA;;AAEA,CARJ,gBAQI,EAAA;AACE,gBAAA;AACA,aAAA;;AAKN;AACE,iBAAA;;", "names": []}