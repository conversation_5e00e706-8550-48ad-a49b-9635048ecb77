import {
  HomePage,
  init_home_page
} from "./chunk-LLEFUVOE.js";
import {
  CommonModule,
  Component,
  IonIcon,
  IonLabel,
  IonTabBar,
  IonTabButton,
  IonTabs2 as IonTabs,
  IonicModule,
  NgModule,
  Router,
  RouterModule,
  init_common,
  init_core,
  init_ionic_angular,
  init_router,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-EC6CHFTM.js";
import "./chunk-GJIVKQBF.js";
import "./chunk-WY354WYL.js";
import "./chunk-3TAP6XVY.js";
import "./chunk-BQEXAUCY.js";
import "./chunk-EUHNYIV3.js";
import "./chunk-AY6ZGCP5.js";
import "./chunk-I3UCYTIQ.js";
import "./chunk-4JI7VCBZ.js";
import "./chunk-YY7NCLAA.js";
import "./chunk-GUQ63TTB.js";
import "./chunk-CHFZ6R2P.js";
import "./chunk-XPKADKFH.js";
import "./chunk-V6QEVD67.js";
import "./chunk-WNPNB2PX.js";
import "./chunk-NRG74LSH.js";
import "./chunk-YSMEPS6E.js";
import "./chunk-NHEUXSZ2.js";
import "./chunk-WX4TOLMF.js";
import "./chunk-6SXGDLUH.js";
import "./chunk-NNIHVF7T.js";
import "./chunk-ZNALOMZJ.js";
import "./chunk-GHA2GP6C.js";
import "./chunk-H2CU6QHT.js";
import "./chunk-6PMAHZVS.js";
import "./chunk-DOEZTQY3.js";
import "./chunk-O4BCOVEV.js";
import "./chunk-TD4SIQNM.js";
import "./chunk-VU5YACRM.js";
import {
  __esm
} from "./chunk-4W6HR7MY.js";

// src/app/pages/tabs/tabs.page.ts
var _TabsPage, TabsPage;
var init_tabs_page = __esm({
  "src/app/pages/tabs/tabs.page.ts"() {
    "use strict";
    init_core();
    init_router();
    init_ionic_angular();
    init_common();
    init_core();
    init_router();
    init_ionic_angular();
    _TabsPage = class _TabsPage {
      constructor(router) {
        this.router = router;
      }
      ngOnInit() {
      }
      logout() {
        console.log("Logout realizado");
        this.router.navigate(["/login"]);
      }
    };
    _TabsPage.\u0275fac = function TabsPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TabsPage)(\u0275\u0275directiveInject(Router));
    };
    _TabsPage.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _TabsPage, selectors: [["tabs"]], decls: 18, vars: 0, consts: [["slot", "bottom", 1, "custom-tabs"], ["tab", "home"], ["name", "home-outline"], ["tab", "vehicles"], ["name", "car-outline"], ["tab", "journeys"], ["name", "map-outline"], ["tab", "sync"], ["name", "sync-outline"]], template: function TabsPage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-tabs")(1, "ion-tab-bar", 0)(2, "ion-tab-button", 1);
        \u0275\u0275element(3, "ion-icon", 2);
        \u0275\u0275elementStart(4, "ion-label");
        \u0275\u0275text(5, "Home");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(6, "ion-tab-button", 3);
        \u0275\u0275element(7, "ion-icon", 4);
        \u0275\u0275elementStart(8, "ion-label");
        \u0275\u0275text(9, "Ve\xEDculos");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(10, "ion-tab-button", 5);
        \u0275\u0275element(11, "ion-icon", 6);
        \u0275\u0275elementStart(12, "ion-label");
        \u0275\u0275text(13, "Viagens");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(14, "ion-tab-button", 7);
        \u0275\u0275element(15, "ion-icon", 8);
        \u0275\u0275elementStart(16, "ion-label");
        \u0275\u0275text(17, "Sincronizar");
        \u0275\u0275elementEnd()()()();
      }
    }, dependencies: [
      IonicModule,
      IonIcon,
      IonLabel,
      IonTabBar,
      IonTabButton,
      IonTabs,
      CommonModule,
      RouterModule
    ], encapsulation: 2 });
    TabsPage = _TabsPage;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TabsPage, [{
        type: Component,
        args: [{ selector: "tabs", imports: [
          IonicModule,
          CommonModule,
          RouterModule
        ], template: '<ion-tabs>\r\n  <ion-tab-bar slot="bottom" class="custom-tabs">\r\n    <ion-tab-button tab="home">\r\n      <ion-icon name="home-outline"></ion-icon>\r\n      <ion-label>Home</ion-label>\r\n    </ion-tab-button>\r\n\r\n    <ion-tab-button tab="vehicles">\r\n      <ion-icon name="car-outline"></ion-icon>\r\n      <ion-label>Ve\xEDculos</ion-label>\r\n    </ion-tab-button>\r\n\r\n    <ion-tab-button tab="journeys">\r\n      <ion-icon name="map-outline"></ion-icon>\r\n      <ion-label>Viagens</ion-label>\r\n    </ion-tab-button>\r\n\r\n    <ion-tab-button tab="sync">\r\n      <ion-icon name="sync-outline"></ion-icon>\r\n      <ion-label>Sincronizar</ion-label>\r\n    </ion-tab-button>\r\n  </ion-tab-bar>\r\n</ion-tabs>\r\n' }]
      }], () => [{ type: Router }], null);
    })();
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(TabsPage, { className: "TabsPage", filePath: "src/app/pages/tabs/tabs.page.ts", lineNumber: 17 });
    })();
  }
});

// src/app/pages/tabs/tabs.routing.module.ts
var routes, _TabsRoutingModule, TabsRoutingModule;
var init_tabs_routing_module = __esm({
  "src/app/pages/tabs/tabs.routing.module.ts"() {
    "use strict";
    init_core();
    init_router();
    init_tabs_page();
    init_home_page();
    init_core();
    routes = [
      {
        path: "",
        component: TabsPage,
        children: [
          {
            path: "home",
            component: HomePage
          },
          {
            path: "journeys",
            loadComponent: () => import("./journey.page-UMDIJSY7.js").then((m) => m.JourneyPage)
          },
          {
            path: "vehicles",
            loadComponent: () => import("./list-vehicles.page-7QRYGE6J.js").then((m) => m.ListVehiclesPage)
          },
          {
            path: "vehicles/edit/:id",
            loadComponent: () => import("./edit-vehicle.page-GZFKGUQF.js").then((m) => m.EditVehiclePage)
          },
          {
            path: "sync",
            loadComponent: () => import("./sync.page-ICGJLSBU.js").then((m) => m.SyncPage)
          },
          {
            path: "",
            redirectTo: "home",
            pathMatch: "full"
          }
        ]
      }
    ];
    _TabsRoutingModule = class _TabsRoutingModule {
    };
    _TabsRoutingModule.\u0275fac = function TabsRoutingModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TabsRoutingModule)();
    };
    _TabsRoutingModule.\u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({ type: _TabsRoutingModule });
    _TabsRoutingModule.\u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({ imports: [RouterModule.forChild(routes), RouterModule] });
    TabsRoutingModule = _TabsRoutingModule;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TabsRoutingModule, [{
        type: NgModule,
        args: [{
          imports: [RouterModule.forChild(routes)],
          exports: [RouterModule]
        }]
      }], null, null);
    })();
  }
});

// src/app/pages/tabs/tabs.module.ts
var _TabsPageModule, TabsPageModule;
var init_tabs_module = __esm({
  "src/app/pages/tabs/tabs.module.ts"() {
    init_core();
    init_ionic_angular();
    init_common();
    init_tabs_page();
    init_tabs_routing_module();
    init_home_page();
    init_core();
    _TabsPageModule = class _TabsPageModule {
    };
    _TabsPageModule.\u0275fac = function TabsPageModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TabsPageModule)();
    };
    _TabsPageModule.\u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({ type: _TabsPageModule });
    _TabsPageModule.\u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({ imports: [
      CommonModule,
      IonicModule,
      TabsRoutingModule,
      TabsPage,
      HomePage
    ] });
    TabsPageModule = _TabsPageModule;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TabsPageModule, [{
        type: NgModule,
        args: [{
          imports: [
            CommonModule,
            IonicModule,
            TabsRoutingModule,
            TabsPage,
            HomePage
          ]
        }]
      }], null, null);
    })();
  }
});
init_tabs_module();
export {
  TabsPageModule
};
//# sourceMappingURL=tabs.module-556FDPOP.js.map
