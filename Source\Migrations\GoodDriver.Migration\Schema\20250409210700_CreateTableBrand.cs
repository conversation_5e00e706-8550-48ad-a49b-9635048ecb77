﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Migration.Schema
{
	[FluentMigrator.Migration(20250409210700)]
	public class CreateTableBrand : FluentMigrator.Migration
	{
		public override void Down()
		{
			if (Schema.Table("Brand").Exists())
			{
				Delete.Table("Brand");
			}
		}

		public override void Up()
		{
			if (!Schema.Table("Brand").Exists())
			{
				Create.Table("Brand")
					.WithColumn("Id").AsInt32().PrimaryKey().NotNullable().Identity()
					.WithColumn("Name").AsString(50).NotNullable()
					.WithColumn("CreatedOn").AsDateTime().NotNullable()
					.WithColumn("UpdatedOn").AsDateTime().Nullable()
					.WithColumn("Ico").AsString(200).Nullable();
			}
		}
	}
}
