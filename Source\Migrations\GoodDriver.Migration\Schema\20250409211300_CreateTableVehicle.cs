﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Migration.Schema
{
	[FluentMigrator.Migration(20250409211300)]
	public class CreateTableVehicle : FluentMigrator.Migration
	{
		public override void Down()
		{
			if (Schema.Table("Vehicle").Exists())
			{
				Delete.Table("Vehicle");
			}
		}

	public override void Up()
	{
		if (!Schema.Table("Vehicle").Exists())
		{
			Create.Table("Vehicle")
				.WithColumn("Id").AsGuid().PrimaryKey().NotNullable()
				.WithColumn("UserId").AsGuid().NotNullable()
				.WithColumn("BrandId").AsInt32().NotNullable().ForeignKey("FK_Vehicle_Brand", "Brand", "Id")
				.WithColumn("ModelId").AsInt32().NotNullable().ForeignKey("FK_Vehicle_Model", "Model", "Id")
				.WithColumn("Year").AsInt32().NotNullable()
				.WithColumn("Plate").AsString(10).NotNullable()
				.WithColumn("Version").AsString(50).Nullable()
				.WithColumn("CreatedOn").AsDateTime().NotNullable()
				.WithColumn("UpdatedOn").AsDateTime().Nullable()
				.WithColumn("PolicyNumber").AsString(50).Nullable();
		}
	} 
	}
}

