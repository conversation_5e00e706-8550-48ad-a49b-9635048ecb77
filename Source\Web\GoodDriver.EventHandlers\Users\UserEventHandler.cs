﻿using GoodDriver.Common;
using GoodDriver.Contracts.Users.Events;
using GoodDriver.Domain.Users;
using Microsoft.Extensions.Options;
using Rogerio.Cqrs.Events;

namespace GoodDriver.EventHandlers.Users
{
	public class UserEventHandler : IEventHandler<UserCreatedEvent>
	{
		private readonly IUserRepository repository;
		//private readonly INotificationProvider notificationProvider;
		private readonly AppSettings appSettings;

		public UserEventHandler(IUserRepository repository, IOptions<AppSettings> appSettings)
        {
			this.appSettings = appSettings.Value;
			this.repository = repository;
		}

        public Task HandleAsync(UserCreatedEvent @event)
		{
			var parameters = new Dictionary<string, string>();

			parameters.Add("InviteUrl", Util.CombineUrl(appSettings.ApplicationWebUrl, "User/ConfirmMail?Token=" + @event.Token));
			parameters.Add("Name", @event.Name);
			parameters.Add("Email", @event.Email);

			//notificationProvider.Send(NotificationType.Email, Common.Constants.EmailTemplates.InviteUser, @event.Email, @event.Name, parameters);
			

			return Task.CompletedTask;
		}
	}
}
