﻿using GoodDriver.API.Controllers.Sync;
using GoodDriver.Contracts.Sync.Commands;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Rogerio.Commom.Exceptions;
using Rogerio.Cqrs.Commands;
using Rogerio.Cqrs.Requests;
using Rogerio.Security.Domain;

namespace GoodDriver.API.Controllers.Journeys
{
    [ApiController]
    [Route("api/journey")]
    public class JourneyController : BaseControllerGoodDriver
    {
        private readonly ICommandBus commandBus;
        private readonly IConfiguration _config;
        private readonly IRequestBus requestBus;

        public JourneyController(IConfiguration config,
            ILogger<SyncController> logger,
            ISecurityManager securityManager,
            ICommandBus commandBus,
            IRequestBus requestBus)
            : base(securityManager)
        {
            _config = config;
            this.commandBus = commandBus;
            this.requestBus = requestBus;
        }

        /// <summary>
        /// Synchronizes user data from the mobile app
        /// </summary>
        [HttpPost("user")]
        [Authorize]
        public async Task<IActionResult> Sync<PERSON><PERSON><PERSON>([FromBody] SyncJourneyCommand command)
        {
            if (!ModelState.IsValid)
                return BadRequest(new { Success = false, Message = "Invalid data for user synchronization." });

            try
            {
                // Set the user ID from the authenticated user if not provided
                if (string.IsNullOrEmpty(command.UserId))
                {
                    command.UserId = User.Id;
                }

                await commandBus.SendAsync(command);
                return Ok(new { Success = true, Message = "User data synchronized successfully." });
            }
            catch (BusinessException ex)
            {
                return BadRequest(new { message = ex.Message, errorCode = ex.Key });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
