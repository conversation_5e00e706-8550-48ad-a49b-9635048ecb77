import {
  __esm
} from "./chunk-4W6HR7MY.js";

// node_modules/@ionic/core/dist/esm/compare-with-utils-a96ff2ea.js
var compareOptions, isOptionSelected;
var init_compare_with_utils_a96ff2ea = __esm({
  "node_modules/@ionic/core/dist/esm/compare-with-utils-a96ff2ea.js"() {
    "use strict";
    compareOptions = (currentValue, compareValue, compareWith) => {
      if (typeof compareWith === "function") {
        return compareWith(currentValue, compareValue);
      } else if (typeof compareWith === "string") {
        return currentValue[compareWith] === compareValue[compareWith];
      } else {
        return Array.isArray(compareValue) ? compareValue.includes(currentValue) : currentValue === compareValue;
      }
    };
    isOptionSelected = (currentValue, compareValue, compareWith) => {
      if (currentValue === void 0) {
        return false;
      }
      if (Array.isArray(currentValue)) {
        return currentValue.some((val) => compareOptions(val, compareValue, compareWith));
      } else {
        return compareOptions(currentValue, compareValue, compareWith);
      }
    };
  }
});

export {
  compareOptions,
  isOptionSelected,
  init_compare_with_utils_a96ff2ea
};
/*! Bundled license information:

@ionic/core/dist/esm/compare-with-utils-a96ff2ea.js:
  (*!
   * (C) Ionic http://ionicframework.com - MIT License
   *)
*/
//# sourceMappingURL=chunk-VXMFGR7L.js.map
