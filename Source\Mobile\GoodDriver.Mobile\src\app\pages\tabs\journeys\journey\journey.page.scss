// Estilos para a página de viagens
ion-card {
  margin-bottom: var(--app-spacing-md);
}

.tracking-controls {
  margin-top: var(--app-spacing-md);
}

.description {
  margin-bottom: var(--app-spacing-md);
  color: var(--ion-color-medium);
  font-size: 0.9rem;
}

ion-segment {
  margin-bottom: var(--app-spacing-md);
}

.status-item {
  --background: var(--app-primary-light);
  border-radius: var(--app-border-radius-md);
  margin-bottom: var(--app-spacing-sm);
}

ion-button {
  margin-bottom: var(--app-spacing-sm);
}

ion-toggle {
  --background: var(--ion-color-medium);
  --background-checked: var(--ion-color-success);
  --handle-background: var(--ion-color-light);
  --handle-background-checked: var(--ion-color-light);
}