﻿using FluentNHibernate.Mapping;
using GoodDriver.Domain.Journeys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Infrastructure.Database.Maps
{
	public class JourneyInfoMap : ClassMap<JourneyInfo>
	{
        public JourneyInfoMap()
        {
			Id(u => u.Id);
			References(a => a.Journey).Column("JourneyId").Not.Nullable().Fetch.Join().Cascade.None();
			Map(a => a.Date).Not.Nullable();
			Map(a => a.Latitude).Not.Nullable();
			Map(a => a.Longitude).Not.Nullable();

			References(a => a.Occurrence).Column("OccurrenceId").Nullable().Fetch.Join().Cascade.None();
		}
    }
}
