import {
  JourneyStorageService,
  init_journey_storage_service
} from "./chunk-L56FEOJB.js";
import "./chunk-ZCUEDWU7.js";
import {
  ActivatedRoute,
  CommonModule,
  Component,
  DataStorageService,
  DatePipe,
  DecimalPipe,
  IonAvatar,
  IonBackButton2 as IonBackButton,
  IonBadge,
  IonButton,
  IonButtons,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonContent,
  IonHeader,
  IonIcon,
  IonItem,
  IonLabel,
  IonList,
  IonSpinner,
  IonTitle,
  IonToolbar,
  IonicModule,
  NgForOf,
  NgIf,
  RouterLinkDelegateDirective2 as RouterLinkDelegateDirective,
  ToastService,
  init_common,
  init_core,
  init_data_storage_service,
  init_ionic_angular,
  init_router,
  init_toast_service,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2
} from "./chunk-EC6CHFTM.js";
import "./chunk-GJIVKQBF.js";
import "./chunk-WY354WYL.js";
import "./chunk-3TAP6XVY.js";
import "./chunk-BQEXAUCY.js";
import "./chunk-EUHNYIV3.js";
import "./chunk-AY6ZGCP5.js";
import "./chunk-I3UCYTIQ.js";
import "./chunk-4JI7VCBZ.js";
import "./chunk-YY7NCLAA.js";
import "./chunk-GUQ63TTB.js";
import "./chunk-CHFZ6R2P.js";
import "./chunk-XPKADKFH.js";
import "./chunk-V6QEVD67.js";
import "./chunk-WNPNB2PX.js";
import "./chunk-NRG74LSH.js";
import "./chunk-YSMEPS6E.js";
import "./chunk-NHEUXSZ2.js";
import "./chunk-WX4TOLMF.js";
import "./chunk-6SXGDLUH.js";
import "./chunk-NNIHVF7T.js";
import "./chunk-ZNALOMZJ.js";
import "./chunk-GHA2GP6C.js";
import "./chunk-H2CU6QHT.js";
import "./chunk-6PMAHZVS.js";
import "./chunk-DOEZTQY3.js";
import "./chunk-O4BCOVEV.js";
import "./chunk-TD4SIQNM.js";
import "./chunk-VU5YACRM.js";
import {
  __async,
  __esm
} from "./chunk-4W6HR7MY.js";

// src/app/pages/tabs/journeys/journey-details/journey-details.page.ts
function JourneyDetailsPage_div_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 8);
    \u0275\u0275element(1, "ion-spinner", 9);
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Carregando detalhes da viagem...");
    \u0275\u0275elementEnd()();
  }
}
function JourneyDetailsPage_div_8_div_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 14)(1, "div", 15)(2, "div", 16);
    \u0275\u0275element(3, "ion-icon", 17);
    \u0275\u0275elementEnd();
    \u0275\u0275element(4, "div", 18);
    \u0275\u0275elementStart(5, "div", 19);
    \u0275\u0275element(6, "ion-icon", 20);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(7, "div", 21)(8, "span", 22);
    \u0275\u0275text(9);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(9);
    \u0275\u0275textInterpolate1("", ctx_r0.journey.infosJourney.length, " pontos registrados");
  }
}
function JourneyDetailsPage_div_8_p_24_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p")(1, "strong");
    \u0275\u0275text(2, "Ve\xEDculo:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", ctx_r0.journey.vehicleId, "");
  }
}
function JourneyDetailsPage_div_8_ion_badge_29_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-badge", 1);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.journey.infosJourney == null ? null : ctx_r0.journey.infosJourney.length, " ");
  }
}
function JourneyDetailsPage_div_8_div_31_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 23);
    \u0275\u0275element(1, "ion-icon", 24);
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Nenhuma localiza\xE7\xE3o registrada para esta viagem.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "p")(5, "small");
    \u0275\u0275text(6, "As localiza\xE7\xF5es s\xE3o registradas automaticamente durante a viagem.");
    \u0275\u0275elementEnd()()();
  }
}
function JourneyDetailsPage_div_8_ion_list_32_ion_item_1_ion_avatar_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-avatar", 39);
    \u0275\u0275element(1, "ion-icon", 17);
    \u0275\u0275elementEnd();
  }
}
function JourneyDetailsPage_div_8_ion_list_32_ion_item_1_ion_avatar_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-avatar", 40);
    \u0275\u0275element(1, "ion-icon", 20);
    \u0275\u0275elementEnd();
  }
}
function JourneyDetailsPage_div_8_ion_list_32_ion_item_1_ion_avatar_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-avatar", 2)(1, "div", 41);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const i_r2 = \u0275\u0275nextContext().index;
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(i_r2 + 1);
  }
}
function JourneyDetailsPage_div_8_ion_list_32_ion_item_1_h3_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "h3", 42);
    \u0275\u0275element(1, "ion-icon", 43);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const i_r2 = \u0275\u0275nextContext().index;
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" ", ctx_r0.getLocationDescription(i_r2), " ");
  }
}
function JourneyDetailsPage_div_8_ion_list_32_ion_item_1_h3_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "h3", 44);
    \u0275\u0275element(1, "ion-icon", 45);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const i_r2 = \u0275\u0275nextContext().index;
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" ", ctx_r0.getLocationDescription(i_r2), " ");
  }
}
function JourneyDetailsPage_div_8_ion_list_32_ion_item_1_h3_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "h3", 46);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const i_r2 = \u0275\u0275nextContext().index;
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.getLocationDescription(i_r2), " ");
  }
}
function JourneyDetailsPage_div_8_ion_list_32_ion_item_1_p_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p", 47);
    \u0275\u0275element(1, "ion-icon", 48);
    \u0275\u0275elementStart(2, "strong");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const info_r3 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(info_r3.address);
  }
}
function JourneyDetailsPage_div_8_ion_list_32_ion_item_1_p_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p", 49);
    \u0275\u0275element(1, "ion-spinner", 50);
    \u0275\u0275text(2, " Carregando endere\xE7o... ");
    \u0275\u0275elementEnd();
  }
}
function JourneyDetailsPage_div_8_ion_list_32_ion_item_1_p_19_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p", 51);
    \u0275\u0275element(1, "ion-icon", 52);
    \u0275\u0275elementStart(2, "span", 53);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const info_r3 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(info_r3.occurrenceType);
  }
}
function JourneyDetailsPage_div_8_ion_list_32_ion_item_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-item");
    \u0275\u0275template(1, JourneyDetailsPage_div_8_ion_list_32_ion_item_1_ion_avatar_1_Template, 2, 0, "ion-avatar", 26)(2, JourneyDetailsPage_div_8_ion_list_32_ion_item_1_ion_avatar_2_Template, 2, 0, "ion-avatar", 27)(3, JourneyDetailsPage_div_8_ion_list_32_ion_item_1_ion_avatar_3_Template, 3, 1, "ion-avatar", 28);
    \u0275\u0275elementStart(4, "ion-label");
    \u0275\u0275template(5, JourneyDetailsPage_div_8_ion_list_32_ion_item_1_h3_5_Template, 3, 1, "h3", 29)(6, JourneyDetailsPage_div_8_ion_list_32_ion_item_1_h3_6_Template, 3, 1, "h3", 30)(7, JourneyDetailsPage_div_8_ion_list_32_ion_item_1_h3_7_Template, 2, 1, "h3", 31);
    \u0275\u0275elementStart(8, "p", 32);
    \u0275\u0275element(9, "ion-icon", 33);
    \u0275\u0275text(10);
    \u0275\u0275pipe(11, "date");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "p", 34);
    \u0275\u0275element(13, "ion-icon", 35);
    \u0275\u0275text(14);
    \u0275\u0275pipe(15, "number");
    \u0275\u0275pipe(16, "number");
    \u0275\u0275elementEnd();
    \u0275\u0275template(17, JourneyDetailsPage_div_8_ion_list_32_ion_item_1_p_17_Template, 4, 1, "p", 36)(18, JourneyDetailsPage_div_8_ion_list_32_ion_item_1_p_18_Template, 3, 0, "p", 37)(19, JourneyDetailsPage_div_8_ion_list_32_ion_item_1_p_19_Template, 4, 1, "p", 38);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const info_r3 = ctx.$implicit;
    const i_r2 = ctx.index;
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275classMap(ctx_r0.getLocationItemClass(i_r2));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isStartLocation(i_r2));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isEndLocation(i_r2));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isIntermediateLocation(i_r2));
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r0.isStartLocation(i_r2));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isEndLocation(i_r2));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isIntermediateLocation(i_r2));
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", \u0275\u0275pipeBind2(11, 14, info_r3.timestamp, "short"), " ");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate2(" ", \u0275\u0275pipeBind2(15, 17, info_r3.latitude, "1.6-6"), ", ", \u0275\u0275pipeBind2(16, 20, info_r3.longitude, "1.6-6"), " ");
    \u0275\u0275advance(3);
    \u0275\u0275property("ngIf", info_r3.address);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !info_r3.address);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", info_r3.occurrenceType);
  }
}
function JourneyDetailsPage_div_8_ion_list_32_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-list");
    \u0275\u0275template(1, JourneyDetailsPage_div_8_ion_list_32_ion_item_1_Template, 20, 23, "ion-item", 25);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r0.journey.infosJourney);
  }
}
function JourneyDetailsPage_div_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div")(1, "ion-card")(2, "ion-card-header")(3, "ion-card-title");
    \u0275\u0275element(4, "ion-icon", 10);
    \u0275\u0275text(5, " Resumo da Viagem ");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "ion-card-content");
    \u0275\u0275template(7, JourneyDetailsPage_div_8_div_7_Template, 10, 1, "div", 11);
    \u0275\u0275elementStart(8, "div", 12)(9, "p")(10, "strong");
    \u0275\u0275text(11, "In\xEDcio:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(12);
    \u0275\u0275pipe(13, "date");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "p")(15, "strong");
    \u0275\u0275text(16, "Fim:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(17);
    \u0275\u0275pipe(18, "date");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(19, "p")(20, "strong");
    \u0275\u0275text(21, "Dist\xE2ncia:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(22);
    \u0275\u0275pipe(23, "number");
    \u0275\u0275elementEnd();
    \u0275\u0275template(24, JourneyDetailsPage_div_8_p_24_Template, 4, 1, "p", 6);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(25, "ion-card")(26, "ion-card-header")(27, "ion-card-title");
    \u0275\u0275text(28, " Localiza\xE7\xF5es Registradas ");
    \u0275\u0275template(29, JourneyDetailsPage_div_8_ion_badge_29_Template, 2, 1, "ion-badge", 13);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(30, "ion-card-content");
    \u0275\u0275template(31, JourneyDetailsPage_div_8_div_31_Template, 7, 0, "div", 7)(32, JourneyDetailsPage_div_8_ion_list_32_Template, 2, 1, "ion-list", 6);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(7);
    \u0275\u0275property("ngIf", ctx_r0.journey.infosJourney && ctx_r0.journey.infosJourney.length > 1);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1(" ", \u0275\u0275pipeBind2(13, 8, ctx_r0.journey.startDate, "short"), "");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1(" ", ctx_r0.journey.endDate ? \u0275\u0275pipeBind2(18, 11, ctx_r0.journey.endDate, "short") : "Em andamento", "");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1(" ", \u0275\u0275pipeBind2(23, 14, ctx_r0.journey.distance, "1.2-2"), " km");
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r0.journey.vehicleId);
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", ctx_r0.journey.infosJourney == null ? null : ctx_r0.journey.infosJourney.length);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", !ctx_r0.journey.infosJourney || ctx_r0.journey.infosJourney.length === 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.journey.infosJourney && ctx_r0.journey.infosJourney.length > 0);
  }
}
function JourneyDetailsPage_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 23);
    \u0275\u0275element(1, "ion-icon", 54);
    \u0275\u0275elementStart(2, "h2");
    \u0275\u0275text(3, "Viagem n\xE3o encontrada");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "p");
    \u0275\u0275text(5, "N\xE3o foi poss\xEDvel carregar os detalhes desta viagem.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "ion-button", 55);
    \u0275\u0275text(7, " Voltar \xE0s Viagens ");
    \u0275\u0275elementEnd()();
  }
}
var _JourneyDetailsPage, JourneyDetailsPage;
var init_journey_details_page = __esm({
  "src/app/pages/tabs/journeys/journey-details/journey-details.page.ts"() {
    init_core();
    init_ionic_angular();
    init_common();
    init_core();
    init_router();
    init_data_storage_service();
    init_journey_storage_service();
    init_toast_service();
    init_ionic_angular();
    init_common();
    _JourneyDetailsPage = class _JourneyDetailsPage {
      constructor(route, dataStorageService, journeyStorageService, toastService) {
        this.route = route;
        this.dataStorageService = dataStorageService;
        this.journeyStorageService = journeyStorageService;
        this.toastService = toastService;
        this.isLoading = true;
      }
      ngOnInit() {
        return __async(this, null, function* () {
          var _a;
          const journeyId = this.route.snapshot.paramMap.get("id");
          const journeyStr = this.route.snapshot.queryParamMap.get("data");
          console.log("Journey Details - ID:", journeyId);
          console.log("Journey Details - Data from query:", journeyStr);
          if (journeyStr) {
            try {
              this.journey = JSON.parse(journeyStr);
              console.log("Parsed journey:", this.journey);
              console.log("Journey infosJourney:", this.journey.infosJourney);
              console.log("Journey infosJourney length:", (_a = this.journey.infosJourney) == null ? void 0 : _a.length);
              console.log("Loading journey locations from database...");
              yield this.loadJourneyLocations(this.journey.id);
              if (this.journey.infosJourney && this.journey.infosJourney.length > 0) {
                console.log("Loading addresses for", this.journey.infosJourney.length, "locations");
                yield this.loadAddresses();
              } else {
                console.log("No locations found for this journey");
              }
            } catch (error) {
              console.error("Error parsing journey data:", error);
              this.toastService.showToast("Erro ao carregar dados da viagem", "danger");
            }
          } else if (journeyId) {
            console.log("No data in query params, loading from database...");
            yield this.loadJourneyFromDatabase(journeyId);
          }
          yield this.debugJourneyInfoTable();
          this.isLoading = false;
        });
      }
      /**
       * Load journey locations from database
       */
      loadJourneyLocations(journeyId) {
        return __async(this, null, function* () {
          try {
            console.log("Querying journeyInfo table for journeyId:", journeyId);
            const locations = yield this.dataStorageService.select("journeyInfo", `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);
            console.log("Raw locations from database:", locations);
            console.log("Locations type:", typeof locations);
            console.log("Is array:", Array.isArray(locations));
            if (Array.isArray(locations) && locations.length > 0) {
              this.journey.infosJourney = locations.map((location) => ({
                id: location.id,
                journeyId: location.journeyId,
                latitude: location.latitude,
                longitude: location.longitude,
                timestamp: location.timestamp,
                address: location.address,
                occurrenceType: location.occurrenceType,
                syncStatus: location.syncStatus,
                lastSyncDate: location.lastSyncDate ? new Date(location.lastSyncDate) : void 0
              }));
              console.log("Loaded", this.journey.infosJourney.length, "locations from database");
              console.log("Mapped locations:", this.journey.infosJourney);
            } else {
              console.log("No locations found in database for journey:", journeyId);
              console.log("Locations result:", locations);
              this.journey.infosJourney = [];
            }
          } catch (error) {
            console.error("Error loading journey locations:", error);
            this.journey.infosJourney = [];
          }
        });
      }
      /**
       * Load journey from database by ID
       */
      loadJourneyFromDatabase(journeyId) {
        return __async(this, null, function* () {
          try {
            const journeyData = yield this.dataStorageService.select("journeys", `WHERE id = '${journeyId}'`);
            if (Array.isArray(journeyData) && journeyData.length > 0) {
              const data = journeyData[0];
              this.journey = {
                id: data.id,
                startDate: data.startDate,
                endDate: data.endDate,
                distance: data.distance,
                userId: data.userId,
                vehicleId: data.vehicleId,
                infosJourney: [],
                syncStatus: data.syncStatus,
                lastSyncDate: data.lastSyncDate ? new Date(data.lastSyncDate) : void 0
              };
              yield this.loadJourneyLocations(journeyId);
              if (this.journey.infosJourney && this.journey.infosJourney.length > 0) {
                yield this.loadAddresses();
              }
            } else {
              console.error("Journey not found in database:", journeyId);
              this.toastService.showToast("Viagem n\xE3o encontrada", "danger");
            }
          } catch (error) {
            console.error("Error loading journey from database:", error);
            this.toastService.showToast("Erro ao carregar viagem", "danger");
          }
        });
      }
      /**
       * Load addresses for all journey locations
       * Prioritizes start and end locations
       */
      loadAddresses() {
        return __async(this, null, function* () {
          if (!this.journey.infosJourney || this.journey.infosJourney.length === 0)
            return;
          console.log("Loading addresses for journey locations...");
          const priorityLocations = [];
          if (this.journey.infosJourney[0] && !this.journey.infosJourney[0].address) {
            priorityLocations.push({ info: this.journey.infosJourney[0], index: 0 });
          }
          const lastIndex = this.journey.infosJourney.length - 1;
          if (lastIndex > 0 && this.journey.infosJourney[lastIndex] && !this.journey.infosJourney[lastIndex].address) {
            priorityLocations.push({ info: this.journey.infosJourney[lastIndex], index: lastIndex });
          }
          if (priorityLocations.length > 0) {
            console.log("Loading priority addresses (start/end)...");
            yield Promise.all(priorityLocations.map((_0) => __async(this, [_0], function* ({ info, index }) {
              try {
                console.log(`Loading address for ${index === 0 ? "start" : "end"} location...`);
                info.address = yield this.reverseGeocode(info.latitude, info.longitude);
                console.log(`${index === 0 ? "Start" : "End"} address loaded:`, info.address);
              } catch (error) {
                console.error(`Error loading address for ${index === 0 ? "start" : "end"} location:`, error);
                info.address = "Endere\xE7o n\xE3o dispon\xEDvel";
              }
            })));
            yield new Promise((resolve) => setTimeout(resolve, 500));
          }
          const intermediateLocations = this.journey.infosJourney.slice(1, -1).filter((info) => !info.address);
          if (intermediateLocations.length > 0) {
            console.log(`Loading addresses for ${intermediateLocations.length} intermediate locations...`);
            const batchSize = 3;
            for (let i = 0; i < intermediateLocations.length; i += batchSize) {
              const batch = intermediateLocations.slice(i, i + batchSize);
              yield Promise.all(batch.map((info) => __async(this, null, function* () {
                try {
                  info.address = yield this.reverseGeocode(info.latitude, info.longitude);
                } catch (error) {
                  console.error("Error loading address for intermediate location:", error);
                  info.address = "Endere\xE7o n\xE3o dispon\xEDvel";
                }
              })));
              if (i + batchSize < intermediateLocations.length) {
                yield new Promise((resolve) => setTimeout(resolve, 1e3));
              }
            }
          }
          console.log("Finished loading all addresses");
        });
      }
      /**
       * Debug method to check journeyInfo table contents
       */
      debugJourneyInfoTable() {
        return __async(this, null, function* () {
          try {
            console.log("=== DEBUG: JourneyInfo Table ===");
            const allJourneyInfo = yield this.dataStorageService.select("journeyInfo", "");
            console.log("All journeyInfo records:", allJourneyInfo);
            console.log("Total journeyInfo records:", Array.isArray(allJourneyInfo) ? allJourneyInfo.length : 0);
            const allJourneys = yield this.dataStorageService.select("journeys", "");
            console.log("All journey records:", allJourneys);
            console.log("Total journey records:", Array.isArray(allJourneys) ? allJourneys.length : 0);
            console.log("=== END DEBUG ===");
          } catch (error) {
            console.error("Error in debug method:", error);
          }
        });
      }
      /**
       * Get CSS class for location item based on position
       */
      getLocationItemClass(index) {
        if (!this.journey.infosJourney || this.journey.infosJourney.length === 0) {
          return "";
        }
        const totalLocations = this.journey.infosJourney.length;
        if (index === 0) {
          return "start-location-item";
        } else if (index === totalLocations - 1 && totalLocations > 1) {
          return "end-location-item";
        } else {
          return "intermediate-location-item";
        }
      }
      /**
       * Get location description based on position
       */
      getLocationDescription(index) {
        if (!this.journey.infosJourney || this.journey.infosJourney.length === 0) {
          return "";
        }
        const totalLocations = this.journey.infosJourney.length;
        if (index === 0) {
          return "In\xEDcio da Viagem";
        } else if (index === totalLocations - 1 && totalLocations > 1) {
          return "Fim da Viagem";
        } else {
          return `Ponto ${index + 1}`;
        }
      }
      /**
       * Check if location is start point
       */
      isStartLocation(index) {
        return index === 0;
      }
      /**
       * Check if location is end point
       */
      isEndLocation(index) {
        if (!this.journey.infosJourney || this.journey.infosJourney.length <= 1) {
          return false;
        }
        return index === this.journey.infosJourney.length - 1;
      }
      /**
       * Check if location is intermediate point
       */
      isIntermediateLocation(index) {
        return !this.isStartLocation(index) && !this.isEndLocation(index);
      }
      reverseGeocode(lat, lon) {
        return __async(this, null, function* () {
          try {
            const url = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lon}`;
            const response = yield fetch(url);
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = yield response.json();
            return data.display_name || "Endere\xE7o n\xE3o encontrado";
          } catch (error) {
            console.error("Error in reverse geocoding:", error);
            return "Endere\xE7o n\xE3o dispon\xEDvel";
          }
        });
      }
    };
    _JourneyDetailsPage.\u0275fac = function JourneyDetailsPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _JourneyDetailsPage)(\u0275\u0275directiveInject(ActivatedRoute), \u0275\u0275directiveInject(DataStorageService), \u0275\u0275directiveInject(JourneyStorageService), \u0275\u0275directiveInject(ToastService));
    };
    _JourneyDetailsPage.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _JourneyDetailsPage, selectors: [["app-journey-details"]], decls: 10, vars: 4, consts: [[3, "translucent"], ["color", "primary"], ["slot", "start"], ["defaultHref", "/tabs/journeys"], [1, "ion-padding"], ["class", "loading-container", 4, "ngIf"], [4, "ngIf"], ["class", "ion-text-center ion-padding", 4, "ngIf"], [1, "loading-container"], ["name", "crescent"], ["name", "car-sport-outline"], ["class", "journey-progress", 4, "ngIf"], [1, "journey-details"], ["color", "primary", 4, "ngIf"], [1, "journey-progress"], [1, "progress-line"], [1, "progress-start"], ["name", "flag-outline", "color", "success"], [1, "progress-track"], [1, "progress-end"], ["name", "checkered-flag-outline", "color", "primary"], [1, "progress-info"], [1, "progress-points"], [1, "ion-text-center", "ion-padding"], ["name", "location-outline", "size", "large", "color", "medium"], [3, "class", 4, "ngFor", "ngForOf"], ["slot", "start", "class", "start-location", 4, "ngIf"], ["slot", "start", "class", "end-location", 4, "ngIf"], ["slot", "start", 4, "ngIf"], ["class", "start-title", 4, "ngIf"], ["class", "end-title", 4, "ngIf"], ["class", "intermediate-title", 4, "ngIf"], [1, "timestamp"], ["name", "time-outline"], [1, "coordinates"], ["name", "location-outline"], ["class", "address", 4, "ngIf"], ["class", "loading-address", 4, "ngIf"], ["class", "occurrence", 4, "ngIf"], ["slot", "start", 1, "start-location"], ["slot", "start", 1, "end-location"], [1, "location-number"], [1, "start-title"], ["name", "play-circle-outline", "color", "success"], [1, "end-title"], ["name", "stop-circle-outline", "color", "primary"], [1, "intermediate-title"], [1, "address"], ["name", "home-outline"], [1, "loading-address"], ["name", "dots"], [1, "occurrence"], ["name", "warning-outline", "color", "warning"], [1, "occurrence-type"], ["name", "alert-circle-outline", "size", "large", "color", "danger"], ["routerLink", "/tabs/journeys", "color", "primary"]], template: function JourneyDetailsPage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header", 0)(1, "ion-toolbar", 1)(2, "ion-title");
        \u0275\u0275text(3, "Detalhes da Viagem");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(4, "ion-buttons", 2);
        \u0275\u0275element(5, "ion-back-button", 3);
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(6, "ion-content", 4);
        \u0275\u0275template(7, JourneyDetailsPage_div_7_Template, 4, 0, "div", 5)(8, JourneyDetailsPage_div_8_Template, 33, 17, "div", 6)(9, JourneyDetailsPage_div_9_Template, 8, 0, "div", 7);
        \u0275\u0275elementEnd();
      }
      if (rf & 2) {
        \u0275\u0275property("translucent", true);
        \u0275\u0275advance(7);
        \u0275\u0275property("ngIf", ctx.isLoading);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", !ctx.isLoading && ctx.journey);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", !ctx.isLoading && !ctx.journey);
      }
    }, dependencies: [IonicModule, IonAvatar, IonBadge, IonButton, IonButtons, IonCard, IonCardContent, IonCardHeader, IonCardTitle, IonContent, IonHeader, IonIcon, IonItem, IonLabel, IonList, IonSpinner, IonTitle, IonToolbar, IonBackButton, RouterLinkDelegateDirective, CommonModule, NgForOf, NgIf, DecimalPipe, DatePipe], styles: ['\n\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 50vh;\n}\n.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n}\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: var(--ion-color-medium);\n  font-size: 0.9rem;\n}\n.location-number[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  background-color: var(--ion-color-primary);\n  color: var(--ion-color-primary-contrast);\n  font-weight: bold;\n  font-size: 0.9rem;\n  border-radius: 50%;\n}\n.start-location[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      var(--ion-color-success),\n      var(--ion-color-success-shade));\n  border: 3px solid var(--ion-color-success-tint);\n  box-shadow: 0 4px 12px rgba(var(--ion-color-success-rgb), 0.3);\n}\n.start-location[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 1.8rem;\n  color: white;\n}\n.end-location[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      var(--ion-color-primary),\n      var(--ion-color-primary-shade));\n  border: 3px solid var(--ion-color-primary-tint);\n  box-shadow: 0 4px 12px rgba(var(--ion-color-primary-rgb), 0.3);\n}\n.end-location[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 1.8rem;\n  color: white;\n}\n.start-location-item[_ngcontent-%COMP%] {\n  --background:\n    linear-gradient(\n      135deg,\n      rgba(var(--ion-color-success-rgb), 0.1),\n      rgba(var(--ion-color-success-rgb), 0.05));\n  border-left: 4px solid var(--ion-color-success);\n  margin-bottom: 12px;\n}\n.start-location-item[_ngcontent-%COMP%]   .start-title[_ngcontent-%COMP%] {\n  color: var(--ion-color-success);\n  font-weight: 600;\n  font-size: 1.1rem;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n}\n.start-location-item[_ngcontent-%COMP%]   .start-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n}\n.end-location-item[_ngcontent-%COMP%] {\n  --background:\n    linear-gradient(\n      135deg,\n      rgba(var(--ion-color-primary-rgb), 0.1),\n      rgba(var(--ion-color-primary-rgb), 0.05));\n  border-left: 4px solid var(--ion-color-primary);\n  margin-bottom: 12px;\n}\n.end-location-item[_ngcontent-%COMP%]   .end-title[_ngcontent-%COMP%] {\n  color: var(--ion-color-primary);\n  font-weight: 600;\n  font-size: 1.1rem;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n}\n.end-location-item[_ngcontent-%COMP%]   .end-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n}\n.intermediate-location-item[_ngcontent-%COMP%] {\n  --background: var(--ion-color-light);\n  border-left: 2px solid var(--ion-color-medium);\n}\n.intermediate-location-item[_ngcontent-%COMP%]   .intermediate-title[_ngcontent-%COMP%] {\n  color: var(--ion-color-dark);\n  font-weight: 500;\n  font-size: 1rem;\n  margin-bottom: 6px;\n}\n.loading-address[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: var(--ion-color-medium);\n  font-style: italic;\n}\n.loading-address[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%] {\n  --color: var(--ion-color-medium);\n}\n.occurrence-type[_ngcontent-%COMP%] {\n  color: var(--ion-color-warning);\n  font-weight: 500;\n  text-transform: capitalize;\n}\nion-item[_ngcontent-%COMP%] {\n  --padding-start: 16px;\n  --inner-padding-end: 16px;\n  border-radius: 12px;\n  margin-bottom: 12px;\n}\nion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%] {\n  margin: 12px 0;\n}\nion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  color: var(--ion-color-dark);\n  font-weight: 600;\n  margin-bottom: 8px;\n}\nion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin: 6px 0;\n  font-size: 0.95rem;\n  font-weight: 500;\n  color: var(--ion-color-dark);\n}\nion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 1.1rem;\n  color: var(--ion-color-primary);\n}\nion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .coordinates[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin: 4px 0;\n  font-size: 0.85rem;\n  color: var(--ion-color-medium);\n  font-family: "Courier New", monospace;\n}\nion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .coordinates[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  color: var(--ion-color-medium);\n}\nion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .address[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-start;\n  gap: 8px;\n  margin: 6px 0;\n  font-size: 0.9rem;\n  line-height: 1.4;\n}\nion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .address[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  color: var(--ion-color-tertiary);\n  margin-top: 2px;\n  flex-shrink: 0;\n}\nion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .address[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\n  color: var(--ion-color-dark);\n}\nion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .occurrence[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin: 6px 0;\n  font-size: 0.9rem;\n  padding: 6px 12px;\n  background-color: rgba(var(--ion-color-warning-rgb), 0.1);\n  border-radius: 8px;\n  border-left: 3px solid var(--ion-color-warning);\n}\nion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .occurrence[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 1.1rem;\n}\nion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:not(.timestamp):not(.coordinates):not(.address):not(.occurrence):not(.loading-address) {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin: 4px 0;\n  font-size: 0.9rem;\n}\nion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:not(.timestamp):not(.coordinates):not(.address):not(.occurrence):not(.loading-address)   ion-icon[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  color: var(--ion-color-medium);\n}\nion-card[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n}\nion-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\nion-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 1.3rem;\n  color: var(--ion-color-primary);\n}\nion-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]   ion-badge[_ngcontent-%COMP%] {\n  margin-left: auto;\n}\n.journey-progress[_ngcontent-%COMP%] {\n  margin-bottom: 20px;\n  padding: 16px;\n  background:\n    linear-gradient(\n      135deg,\n      rgba(var(--ion-color-primary-rgb), 0.05),\n      rgba(var(--ion-color-success-rgb), 0.05));\n  border-radius: 12px;\n  border: 1px solid rgba(var(--ion-color-primary-rgb), 0.1);\n}\n.journey-progress[_ngcontent-%COMP%]   .progress-line[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8px;\n}\n.journey-progress[_ngcontent-%COMP%]   .progress-line[_ngcontent-%COMP%]   .progress-start[_ngcontent-%COMP%], \n.journey-progress[_ngcontent-%COMP%]   .progress-line[_ngcontent-%COMP%]   .progress-end[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: white;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n.journey-progress[_ngcontent-%COMP%]   .progress-line[_ngcontent-%COMP%]   .progress-start[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], \n.journey-progress[_ngcontent-%COMP%]   .progress-line[_ngcontent-%COMP%]   .progress-end[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n}\n.journey-progress[_ngcontent-%COMP%]   .progress-line[_ngcontent-%COMP%]   .progress-track[_ngcontent-%COMP%] {\n  flex: 1;\n  height: 4px;\n  background:\n    linear-gradient(\n      90deg,\n      var(--ion-color-success),\n      var(--ion-color-primary));\n  margin: 0 12px;\n  border-radius: 2px;\n  position: relative;\n}\n.journey-progress[_ngcontent-%COMP%]   .progress-line[_ngcontent-%COMP%]   .progress-track[_ngcontent-%COMP%]::after {\n  content: "";\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 8px;\n  height: 8px;\n  background: var(--ion-color-warning);\n  border-radius: 50%;\n  box-shadow: 0 0 0 3px rgba(var(--ion-color-warning-rgb), 0.3);\n}\n.journey-progress[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%] {\n  text-align: center;\n}\n.journey-progress[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-points[_ngcontent-%COMP%] {\n  font-size: 0.9rem;\n  color: var(--ion-color-medium);\n  font-weight: 500;\n}\n.journey-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 8px 0;\n  font-size: 0.95rem;\n}\n.journey-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\n  color: var(--ion-color-dark);\n  font-weight: 600;\n}\nion-list[_ngcontent-%COMP%] {\n  background: transparent;\n  padding: 8px 0;\n}\nion-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%] {\n  --background: var(--ion-color-light);\n  margin-bottom: 12px;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n}\nion-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n}\nion-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:last-child {\n  margin-bottom: 0;\n}\nion-list[_ngcontent-%COMP%]   ion-item.start-location-item[_ngcontent-%COMP%], \nion-list[_ngcontent-%COMP%]   ion-item.end-location-item[_ngcontent-%COMP%] {\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n}\nion-list[_ngcontent-%COMP%]   ion-item.start-location-item[_ngcontent-%COMP%]:hover, \nion-list[_ngcontent-%COMP%]   ion-item.end-location-item[_ngcontent-%COMP%]:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);\n}\n/*# sourceMappingURL=journey-details.page.css.map */'] });
    JourneyDetailsPage = _JourneyDetailsPage;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(JourneyDetailsPage, [{
        type: Component,
        args: [{ selector: "app-journey-details", standalone: true, imports: [
          IonicModule,
          CommonModule
        ], template: `<ion-header [translucent]="true">
  <ion-toolbar color="primary">
    <ion-title>Detalhes da Viagem</ion-title>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/tabs/journeys"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Carregando detalhes da viagem...</p>
  </div>

  <!-- Journey content -->
  <div *ngIf="!isLoading && journey">
    <ion-card>
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="car-sport-outline"></ion-icon>
          Resumo da Viagem
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <!-- Journey Progress Indicator -->
        <div class="journey-progress" *ngIf="journey.infosJourney && journey.infosJourney.length > 1">
          <div class="progress-line">
            <div class="progress-start">
              <ion-icon name="flag-outline" color="success"></ion-icon>
            </div>
            <div class="progress-track"></div>
            <div class="progress-end">
              <ion-icon name="checkered-flag-outline" color="primary"></ion-icon>
            </div>
          </div>
          <div class="progress-info">
            <span class="progress-points">{{ journey.infosJourney.length }} pontos registrados</span>
          </div>
        </div>

        <!-- Journey Details -->
        <div class="journey-details">
          <p><strong>In\xEDcio:</strong> {{ journey.startDate | date:'short' }}</p>
          <p><strong>Fim:</strong> {{ journey.endDate ? (journey.endDate | date:'short') : 'Em andamento' }}</p>
          <p><strong>Dist\xE2ncia:</strong> {{ journey.distance | number:'1.2-2' }} km</p>
          <p *ngIf="journey.vehicleId"><strong>Ve\xEDculo:</strong> {{ journey.vehicleId }}</p>
        </div>
      </ion-card-content>
    </ion-card>

    <ion-card>
      <ion-card-header>
        <ion-card-title>
          Localiza\xE7\xF5es Registradas
          <ion-badge color="primary" *ngIf="journey.infosJourney?.length">
            {{ journey.infosJourney?.length }}
          </ion-badge>
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <!-- No locations message -->
        <div *ngIf="!journey.infosJourney || journey.infosJourney.length === 0" class="ion-text-center ion-padding">
          <ion-icon name="location-outline" size="large" color="medium"></ion-icon>
          <p>Nenhuma localiza\xE7\xE3o registrada para esta viagem.</p>
          <p><small>As localiza\xE7\xF5es s\xE3o registradas automaticamente durante a viagem.</small></p>
        </div>

        <!-- Locations list -->
        <ion-list *ngIf="journey.infosJourney && journey.infosJourney.length > 0">
          <ion-item *ngFor="let info of journey.infosJourney; let i = index" [class]="getLocationItemClass(i)">
            <!-- In\xEDcio da viagem -->
            <ion-avatar slot="start" *ngIf="isStartLocation(i)" class="start-location">
              <ion-icon name="flag-outline" color="success"></ion-icon>
            </ion-avatar>

            <!-- Fim da viagem -->
            <ion-avatar slot="start" *ngIf="isEndLocation(i)" class="end-location">
              <ion-icon name="checkered-flag-outline" color="primary"></ion-icon>
            </ion-avatar>

            <!-- Localiza\xE7\xF5es intermedi\xE1rias -->
            <ion-avatar slot="start" *ngIf="isIntermediateLocation(i)">
              <div class="location-number">{{ i + 1 }}</div>
            </ion-avatar>

            <ion-label>
              <!-- T\xEDtulo especial para in\xEDcio e fim -->
              <h3 *ngIf="isStartLocation(i)" class="start-title">
                <ion-icon name="play-circle-outline" color="success"></ion-icon>
                {{ getLocationDescription(i) }}
              </h3>
              <h3 *ngIf="isEndLocation(i)" class="end-title">
                <ion-icon name="stop-circle-outline" color="primary"></ion-icon>
                {{ getLocationDescription(i) }}
              </h3>
              <h3 *ngIf="isIntermediateLocation(i)" class="intermediate-title">
                {{ getLocationDescription(i) }}
              </h3>

              <!-- Hor\xE1rio -->
              <p class="timestamp">
                <ion-icon name="time-outline"></ion-icon>
                {{ info.timestamp | date:'short' }}
              </p>

              <!-- Coordenadas -->
              <p class="coordinates">
                <ion-icon name="location-outline"></ion-icon>
                {{ info.latitude | number:'1.6-6' }}, {{ info.longitude | number:'1.6-6' }}
              </p>

              <!-- Endere\xE7o -->
              <p *ngIf="info.address" class="address">
                <ion-icon name="home-outline"></ion-icon>
                <strong>{{ info.address }}</strong>
              </p>
              <p *ngIf="!info.address" class="loading-address">
                <ion-spinner name="dots"></ion-spinner>
                Carregando endere\xE7o...
              </p>

              <!-- Tipo de ocorr\xEAncia -->
              <p *ngIf="info.occurrenceType" class="occurrence">
                <ion-icon name="warning-outline" color="warning"></ion-icon>
                <span class="occurrence-type">{{ info.occurrenceType }}</span>
              </p>
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Error state -->
  <div *ngIf="!isLoading && !journey" class="ion-text-center ion-padding">
    <ion-icon name="alert-circle-outline" size="large" color="danger"></ion-icon>
    <h2>Viagem n\xE3o encontrada</h2>
    <p>N\xE3o foi poss\xEDvel carregar os detalhes desta viagem.</p>
    <ion-button routerLink="/tabs/journeys" color="primary">
      Voltar \xE0s Viagens
    </ion-button>
  </div>

</ion-content>
`, styles: ['/* src/app/pages/tabs/journeys/journey-details/journey-details.page.scss */\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 50vh;\n}\n.loading-container ion-spinner {\n  margin-bottom: 16px;\n}\n.loading-container p {\n  color: var(--ion-color-medium);\n  font-size: 0.9rem;\n}\n.location-number {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  background-color: var(--ion-color-primary);\n  color: var(--ion-color-primary-contrast);\n  font-weight: bold;\n  font-size: 0.9rem;\n  border-radius: 50%;\n}\n.start-location {\n  background:\n    linear-gradient(\n      135deg,\n      var(--ion-color-success),\n      var(--ion-color-success-shade));\n  border: 3px solid var(--ion-color-success-tint);\n  box-shadow: 0 4px 12px rgba(var(--ion-color-success-rgb), 0.3);\n}\n.start-location ion-icon {\n  font-size: 1.8rem;\n  color: white;\n}\n.end-location {\n  background:\n    linear-gradient(\n      135deg,\n      var(--ion-color-primary),\n      var(--ion-color-primary-shade));\n  border: 3px solid var(--ion-color-primary-tint);\n  box-shadow: 0 4px 12px rgba(var(--ion-color-primary-rgb), 0.3);\n}\n.end-location ion-icon {\n  font-size: 1.8rem;\n  color: white;\n}\n.start-location-item {\n  --background:\n    linear-gradient(\n      135deg,\n      rgba(var(--ion-color-success-rgb), 0.1),\n      rgba(var(--ion-color-success-rgb), 0.05));\n  border-left: 4px solid var(--ion-color-success);\n  margin-bottom: 12px;\n}\n.start-location-item .start-title {\n  color: var(--ion-color-success);\n  font-weight: 600;\n  font-size: 1.1rem;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n}\n.start-location-item .start-title ion-icon {\n  font-size: 1.2rem;\n}\n.end-location-item {\n  --background:\n    linear-gradient(\n      135deg,\n      rgba(var(--ion-color-primary-rgb), 0.1),\n      rgba(var(--ion-color-primary-rgb), 0.05));\n  border-left: 4px solid var(--ion-color-primary);\n  margin-bottom: 12px;\n}\n.end-location-item .end-title {\n  color: var(--ion-color-primary);\n  font-weight: 600;\n  font-size: 1.1rem;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n}\n.end-location-item .end-title ion-icon {\n  font-size: 1.2rem;\n}\n.intermediate-location-item {\n  --background: var(--ion-color-light);\n  border-left: 2px solid var(--ion-color-medium);\n}\n.intermediate-location-item .intermediate-title {\n  color: var(--ion-color-dark);\n  font-weight: 500;\n  font-size: 1rem;\n  margin-bottom: 6px;\n}\n.loading-address {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: var(--ion-color-medium);\n  font-style: italic;\n}\n.loading-address ion-spinner {\n  --color: var(--ion-color-medium);\n}\n.occurrence-type {\n  color: var(--ion-color-warning);\n  font-weight: 500;\n  text-transform: capitalize;\n}\nion-item {\n  --padding-start: 16px;\n  --inner-padding-end: 16px;\n  border-radius: 12px;\n  margin-bottom: 12px;\n}\nion-item ion-label {\n  margin: 12px 0;\n}\nion-item ion-label h3 {\n  color: var(--ion-color-dark);\n  font-weight: 600;\n  margin-bottom: 8px;\n}\nion-item ion-label .timestamp {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin: 6px 0;\n  font-size: 0.95rem;\n  font-weight: 500;\n  color: var(--ion-color-dark);\n}\nion-item ion-label .timestamp ion-icon {\n  font-size: 1.1rem;\n  color: var(--ion-color-primary);\n}\nion-item ion-label .coordinates {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin: 4px 0;\n  font-size: 0.85rem;\n  color: var(--ion-color-medium);\n  font-family: "Courier New", monospace;\n}\nion-item ion-label .coordinates ion-icon {\n  font-size: 1rem;\n  color: var(--ion-color-medium);\n}\nion-item ion-label .address {\n  display: flex;\n  align-items: flex-start;\n  gap: 8px;\n  margin: 6px 0;\n  font-size: 0.9rem;\n  line-height: 1.4;\n}\nion-item ion-label .address ion-icon {\n  font-size: 1rem;\n  color: var(--ion-color-tertiary);\n  margin-top: 2px;\n  flex-shrink: 0;\n}\nion-item ion-label .address strong {\n  color: var(--ion-color-dark);\n}\nion-item ion-label .occurrence {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin: 6px 0;\n  font-size: 0.9rem;\n  padding: 6px 12px;\n  background-color: rgba(var(--ion-color-warning-rgb), 0.1);\n  border-radius: 8px;\n  border-left: 3px solid var(--ion-color-warning);\n}\nion-item ion-label .occurrence ion-icon {\n  font-size: 1.1rem;\n}\nion-item ion-label p:not(.timestamp):not(.coordinates):not(.address):not(.occurrence):not(.loading-address) {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin: 4px 0;\n  font-size: 0.9rem;\n}\nion-item ion-label p:not(.timestamp):not(.coordinates):not(.address):not(.occurrence):not(.loading-address) ion-icon {\n  font-size: 1rem;\n  color: var(--ion-color-medium);\n}\nion-card {\n  margin-bottom: 16px;\n}\nion-card ion-card-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\nion-card ion-card-title ion-icon {\n  font-size: 1.3rem;\n  color: var(--ion-color-primary);\n}\nion-card ion-card-title ion-badge {\n  margin-left: auto;\n}\n.journey-progress {\n  margin-bottom: 20px;\n  padding: 16px;\n  background:\n    linear-gradient(\n      135deg,\n      rgba(var(--ion-color-primary-rgb), 0.05),\n      rgba(var(--ion-color-success-rgb), 0.05));\n  border-radius: 12px;\n  border: 1px solid rgba(var(--ion-color-primary-rgb), 0.1);\n}\n.journey-progress .progress-line {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8px;\n}\n.journey-progress .progress-line .progress-start,\n.journey-progress .progress-line .progress-end {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: white;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n.journey-progress .progress-line .progress-start ion-icon,\n.journey-progress .progress-line .progress-end ion-icon {\n  font-size: 1.5rem;\n}\n.journey-progress .progress-line .progress-track {\n  flex: 1;\n  height: 4px;\n  background:\n    linear-gradient(\n      90deg,\n      var(--ion-color-success),\n      var(--ion-color-primary));\n  margin: 0 12px;\n  border-radius: 2px;\n  position: relative;\n}\n.journey-progress .progress-line .progress-track::after {\n  content: "";\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 8px;\n  height: 8px;\n  background: var(--ion-color-warning);\n  border-radius: 50%;\n  box-shadow: 0 0 0 3px rgba(var(--ion-color-warning-rgb), 0.3);\n}\n.journey-progress .progress-info {\n  text-align: center;\n}\n.journey-progress .progress-info .progress-points {\n  font-size: 0.9rem;\n  color: var(--ion-color-medium);\n  font-weight: 500;\n}\n.journey-details p {\n  margin: 8px 0;\n  font-size: 0.95rem;\n}\n.journey-details p strong {\n  color: var(--ion-color-dark);\n  font-weight: 600;\n}\nion-list {\n  background: transparent;\n  padding: 8px 0;\n}\nion-list ion-item {\n  --background: var(--ion-color-light);\n  margin-bottom: 12px;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n}\nion-list ion-item:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n}\nion-list ion-item:last-child {\n  margin-bottom: 0;\n}\nion-list ion-item.start-location-item,\nion-list ion-item.end-location-item {\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n}\nion-list ion-item.start-location-item:hover,\nion-list ion-item.end-location-item:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);\n}\n/*# sourceMappingURL=journey-details.page.css.map */\n'] }]
      }], () => [{ type: ActivatedRoute }, { type: DataStorageService }, { type: JourneyStorageService }, { type: ToastService }], null);
    })();
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(JourneyDetailsPage, { className: "JourneyDetailsPage", filePath: "src/app/pages/tabs/journeys/journey-details/journey-details.page.ts", lineNumber: 22 });
    })();
  }
});
init_journey_details_page();
export {
  JourneyDetailsPage
};
//# sourceMappingURL=journey-details.page-ROWS3L7K.js.map
