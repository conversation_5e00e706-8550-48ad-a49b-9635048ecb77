import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { ListModelRequestDto } from '../dtos/model/list-model-requestDto';
import { ListModelResponseDto } from '../dtos/model/list-model-responseDto';
import { Model } from '../models/model.model';
import { ApiService } from './api.service';

@Injectable({
    providedIn: 'root'
  })
export class ModelService
{
    constructor(
      private http: HttpClient,
      private apiService: ApiService
    ) {}

    async getByBranch(brandId: ListModelRequestDto): Promise<Model[]>
    {
        try {
            console.log('ModelService.getByBranch called with:', brandId);
            const url = this.apiService.getUrl('models', { brandId: brandId.brandId.toString() });
            console.log('Generated URL:', url);
            const result = await firstValueFrom(this.http.get<ListModelResponseDto[]>(url));

            if (!result) return [];

            const models: Model[] = result.map((data: any) => {
                return {
                    id: data.id,
                    name: data.name,
                    referenceCode: data.referenceCode,
                    referenceMonth: data.referenceMonth,
                    shortName: data.shortName,
                    brandId: data.brandId,
                };
            });

            return models;
        }
        catch (error) {
            console.error('Error fetching models:', error);
            return [];
        }
    }
    }
