import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ListModelRequestDto } from '../dtos/model/list-model-requestDto';
import { ListModelResponseDto } from '../dtos/model/list-model-responseDto';
import { Model } from '../models/model.model';
import { ApiService } from './api.service';

@Injectable({
    providedIn: 'root'
  })
export class ModelService
{
    constructor(
      private http: HttpClient,
      private apiService: ApiService
    ) {}

    async getByBranch(brandId: ListModelRequestDto): Promise<Model[]>
    {
        try {
            const url = this.apiService.getUrl('models', { brandId: brandId as unknown as string });
            const result = await this.http.get<ListModelResponseDto[]>(url).toPromise();

            if (!result) return [];

            const models: Model[] = result.map((data: any) => {
                return {
                    id: data.id,
                    name: data.name,
                    referenceCode: data.referenceCode,
                    referenceMonth: data.referenceMonth,
                    shortName: data.shortName,
                    brandId: data.brandId,
                };
            });

            return models;
        }
        catch (error) {
            console.error('Error fetching models:', error);
            return [];
        }
    }
    }
