﻿using Rogerio.Cqrs.Domains;
using GoodDriver.Common;
using GoodDriver.Contracts.Users.Events;
using System.Text.RegularExpressions;
using static GoodDriver.Common.Constants;
using Rogerio.Commom.Exceptions;
using GoodDriver.Domain.Vehicles;


namespace GoodDriver.Domain.Users
{
	public class User : AggregateRoot<string>
    {
        public User()
        {
            Id = Guid.NewGuid().ToString();
			Token = Guid.NewGuid().ToString();
            Vehicles = new List<Vehicle>();

        }
        

        public User(string name, string phone,
            string email, string password, bool informationResponsibilityChecked) : this()
        {
            //Util.VerifyPassword(password);

            InformationResponsibilityCheck(informationResponsibilityChecked);

			RaiseEvent(new UserCreatedEvent(Id, name, phone, email, password, Token));
            Status = UserStatus.PendingConfirmation;
        }

		public void Apply(UserCreatedEvent @event)
		{
			Id = @event.Id;
			Name = @event.Name;
			//CPF = @event.CPF;			
			Phone = @event.Phone;
			Email = @event.Email.ToLower().Trim().RemoveAccents();
			PasswordSalt = Membership.GenerateSalt();
			Password = Membership.EncodePassword(@event.Password, PasswordSalt);
			CreatedOn = UpdatedOn = DateTime.Now;
			//Status = Constants.UserStatus.RegistrationCompleted;
		}

		public string Name { get; private set; }

        //[StringLengthValidator(12, ErrorMessage = "Limite máximo de 12 caracteres para Telefone")]
        public string _phone;
        //[StringLengthValidator(12, ErrorMessage = "Limite máximo de 12 caracteres para Telefone")]
        public string Phone
        {
            get { return _phone; }
            set
            {
                _phone = value;
                if (_phone != null)
                {
                    Regex regularExpression = new Regex(@"[^0-9]");
                    _phone = regularExpression.Replace(value, "");
                }
            }
        }

        public bool Active { get; private set; }

        //[StringLengthValidator(100, ErrorMessage = "Limite máximo de 100 caracteres para Email")]
        public virtual string Email { get; private set; }

        public virtual string Password { get; private set; }
        public virtual string PasswordSalt { get; private set; }

        public virtual string Token { get; private set; }
        public virtual DateTime CreatedOn { get; private set; }
        public virtual DateTime UpdatedOn { get; private set; }
        public virtual DateTime? LastAccess { get; private set; }

        public virtual string ImageUrl { get; private set; }

        public virtual bool InformationResponsibilityChecked { get; private set; }

        public string ZipCode { get; private set; }

		public virtual string Status { get; private set; }

        public IList<Vehicle> Vehicles { get; set; }

        public virtual void InformationResponsibilityCheck(bool informationResponsibilityChecked)
        {
            if (!informationResponsibilityChecked)
                throw new BusinessException("User.InformationResponsibilityCheck",
                    "É necessário concordar com a  declaração de responsabilidade das informações para prosseguir.");

            InformationResponsibilityChecked = informationResponsibilityChecked;
        }

        public virtual bool Authenticate(string email, string password)
        {
            string hash = Membership.EncodePassword(password, PasswordSalt);

            if (Email.Trim().Equals(email.Trim(), StringComparison.CurrentCultureIgnoreCase) && Password == hash)
            {
                LastAccess = DateTime.Now;
                return true;
            }
            else
                return false;
        }

        public void UpdatePassword(string password, string newPassword, string newPasswordConfirm)
        {
            Util.VerifyPassword(newPassword);

            string hash = Membership.EncodePassword(password, PasswordSalt);
            if (hash != Password) throw new BusinessException("UpdatePassword", "Senha atual inválida.");
            if (newPassword != newPasswordConfirm) throw new BusinessException("Invalid.Password", "Por favor confirme a nova Senha corretamente.");

            PasswordSalt = Membership.GenerateSalt();
            Password = Membership.EncodePassword(newPassword, PasswordSalt);
            UpdatedOn = DateTime.Now;
        }

        public void ActiveWith(string token)
        {
            if (string.Compare(this.Token, token, true) != 0) throw new BusinessException("User.InvalidToken", "Token inválido.");

            this.Active = true;
            this.Token = null;
        }

        public void ResetPassword(string token, string newPassword)
        {
            if (!IsValidToken(token))
                throw new BusinessException("User.ResetTokenInvalid", "Solicitação não reconhecida.");

            string salt = Membership.GenerateSalt();
            newPassword = Membership.EncodePassword(newPassword, salt);

            Password = newPassword;
            PasswordSalt = salt;
            Token = null;
            Active = true;
        }

        public bool IsValidToken(string token)
        {
            return string.Compare(Token, token, true) == 0;
        }

        public bool CanLogOn()
        {
            return Active;
        }

        //public void RequestResetPassword()
        //{
        //    RaiseEvent(new UserResetPasswordRequestedEvent(Id, Name, Email, Guid.NewGuid().ToString()));
        //}

        public void Apply(UserResetPasswordRequestedEvent @event)
        {
            Token = @event.Token;
        }
        //public virtual void Apply(MobilePushNotificationEvent @event) { }

        //public void RequestSendActiveEmail()
        //{
        //    RaiseEvent(new UserSendEmailActiveRequestedEvent(Id, Name, Email, Guid.NewGuid().ToString()));
        //}

        public void Apply(UserSendEmailActiveRequestedEvent @event)
        {
            Token = @event.Token;
            Active = false;
        }

        public void AddVehicle(string vehicleId, string plate, string year, int brandId, int modelId)
        {
            if (ExistsVehicle(plate))
                throw new BusinessException("User.AddVehicle", "Veículo já cadastrado.");

            var vehicle = new Vehicle(vehicleId, plate, year, this, brandId, modelId);            
            if (Vehicles == null)
                Vehicles = new List<Vehicle>();

            Vehicles.Add(vehicle);
        }

        private bool ExistsVehicle(string plate)
        {
            if (string.IsNullOrEmpty(plate))
                throw new ArgumentNullException(nameof(plate));

            plate = Regex.Replace(plate, @"[^0-9a-zA-Z]", "").ToUpper();
            return Vehicles.Any(v => v.Plate == plate);
        }

        //public void AddPushNotificationToken(string pushNotificationToken)
        //{
        //    var notificationToken = PushNotificationTokens.FirstOrDefault(a => a.Token == pushNotificationToken);

        //    if (notificationToken == null)
        //    {
        //        notificationToken = new UserPushNotificationToken(Guid.NewGuid().ToString(), Id, pushNotificationToken);
        //        PushNotificationTokens.Add(notificationToken);
        //    }
        //}
    }
}
