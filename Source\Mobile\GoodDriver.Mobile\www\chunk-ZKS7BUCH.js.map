{"version": 3, "sources": ["node_modules/@ionic/core/components/focus-visible.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst ION_FOCUSED = 'ion-focused';\nconst ION_FOCUSABLE = 'ion-focusable';\nconst FOCUS_KEYS = ['Tab', 'ArrowDown', 'Space', 'Escape', ' ', 'Shift', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'Home', 'End'];\nconst startFocusVisible = rootEl => {\n  let currentFocus = [];\n  let keyboardMode = true;\n  const ref = rootEl ? rootEl.shadowRoot : document;\n  const root = rootEl ? rootEl : document.body;\n  const setFocus = elements => {\n    currentFocus.forEach(el => el.classList.remove(ION_FOCUSED));\n    elements.forEach(el => el.classList.add(ION_FOCUSED));\n    currentFocus = elements;\n  };\n  const pointerDown = () => {\n    keyboardMode = false;\n    setFocus([]);\n  };\n  const onKeydown = ev => {\n    keyboardMode = FOCUS_KEYS.includes(ev.key);\n    if (!keyboardMode) {\n      setFocus([]);\n    }\n  };\n  const onFocusin = ev => {\n    if (keyboardMode && ev.composedPath !== undefined) {\n      const toFocus = ev.composedPath().filter(el => {\n        // TODO(FW-2832): type\n        if (el.classList) {\n          return el.classList.contains(ION_FOCUSABLE);\n        }\n        return false;\n      });\n      setFocus(toFocus);\n    }\n  };\n  const onFocusout = () => {\n    if (ref.activeElement === root) {\n      setFocus([]);\n    }\n  };\n  ref.addEventListener('keydown', onKeydown);\n  ref.addEventListener('focusin', onFocusin);\n  ref.addEventListener('focusout', onFocusout);\n  ref.addEventListener('touchstart', pointerDown, {\n    passive: true\n  });\n  ref.addEventListener('mousedown', pointerDown);\n  const destroy = () => {\n    ref.removeEventListener('keydown', onKeydown);\n    ref.removeEventListener('focusin', onFocusin);\n    ref.removeEventListener('focusout', onFocusout);\n    ref.removeEventListener('touchstart', pointerDown);\n    ref.removeEventListener('mousedown', pointerDown);\n  };\n  return {\n    destroy,\n    setFocus\n  };\n};\nexport { startFocusVisible };"], "mappings": ";;;;;AAAA,IAGM,aACA,eACA,YACA;AANN;AAAA;AAGA,IAAM,cAAc;AACpB,IAAM,gBAAgB;AACtB,IAAM,aAAa,CAAC,OAAO,aAAa,SAAS,UAAU,KAAK,SAAS,SAAS,aAAa,cAAc,WAAW,QAAQ,KAAK;AACrI,IAAM,oBAAoB,YAAU;AAClC,UAAI,eAAe,CAAC;AACpB,UAAI,eAAe;AACnB,YAAM,MAAM,SAAS,OAAO,aAAa;AACzC,YAAM,OAAO,SAAS,SAAS,SAAS;AACxC,YAAM,WAAW,cAAY;AAC3B,qBAAa,QAAQ,QAAM,GAAG,UAAU,OAAO,WAAW,CAAC;AAC3D,iBAAS,QAAQ,QAAM,GAAG,UAAU,IAAI,WAAW,CAAC;AACpD,uBAAe;AAAA,MACjB;AACA,YAAM,cAAc,MAAM;AACxB,uBAAe;AACf,iBAAS,CAAC,CAAC;AAAA,MACb;AACA,YAAM,YAAY,QAAM;AACtB,uBAAe,WAAW,SAAS,GAAG,GAAG;AACzC,YAAI,CAAC,cAAc;AACjB,mBAAS,CAAC,CAAC;AAAA,QACb;AAAA,MACF;AACA,YAAM,YAAY,QAAM;AACtB,YAAI,gBAAgB,GAAG,iBAAiB,QAAW;AACjD,gBAAM,UAAU,GAAG,aAAa,EAAE,OAAO,QAAM;AAE7C,gBAAI,GAAG,WAAW;AAChB,qBAAO,GAAG,UAAU,SAAS,aAAa;AAAA,YAC5C;AACA,mBAAO;AAAA,UACT,CAAC;AACD,mBAAS,OAAO;AAAA,QAClB;AAAA,MACF;AACA,YAAM,aAAa,MAAM;AACvB,YAAI,IAAI,kBAAkB,MAAM;AAC9B,mBAAS,CAAC,CAAC;AAAA,QACb;AAAA,MACF;AACA,UAAI,iBAAiB,WAAW,SAAS;AACzC,UAAI,iBAAiB,WAAW,SAAS;AACzC,UAAI,iBAAiB,YAAY,UAAU;AAC3C,UAAI,iBAAiB,cAAc,aAAa;AAAA,QAC9C,SAAS;AAAA,MACX,CAAC;AACD,UAAI,iBAAiB,aAAa,WAAW;AAC7C,YAAM,UAAU,MAAM;AACpB,YAAI,oBAAoB,WAAW,SAAS;AAC5C,YAAI,oBAAoB,WAAW,SAAS;AAC5C,YAAI,oBAAoB,YAAY,UAAU;AAC9C,YAAI,oBAAoB,cAAc,WAAW;AACjD,YAAI,oBAAoB,aAAa,WAAW;AAAA,MAClD;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}