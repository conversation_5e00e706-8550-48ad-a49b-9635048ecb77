// Component Styles
// --------------------------------------------------
// Global styles for common components

@use './mixins.scss';

// Cards
// --------------------------------------------------
ion-card {
  border-radius: var(--app-border-radius-md);
  box-shadow: var(--app-card-shadow) !important;
  margin: var(--app-spacing-md);

  ion-card-header {
    padding: var(--app-spacing-md);

    ion-card-title {
      font-size: 1.25rem;
      font-weight: var(--app-heading-font-weight);
      color: var(--app-text-color);
    }

    ion-card-subtitle {
      font-size: 0.9rem;
      color: var(--ion-color-medium);
      margin-top: var(--app-spacing-xs);
    }
  }

  ion-card-content {
    padding: var(--app-spacing-md);
    color: var(--app-text-color);
  }
}

// Buttons
// --------------------------------------------------
ion-button {
  --border-radius: var(--app-border-radius-md);
  --box-shadow: none;
  --padding-top: var(--app-spacing-sm);
  --padding-bottom: var(--app-spacing-sm);
  --padding-start: var(--app-spacing-lg);
  --padding-end: var(--app-spacing-lg);
  margin: var(--app-spacing-sm) 0;
  font-weight: 500;
  letter-spacing: 0.03em;
  text-transform: none;

  &.button-solid {
    --background: var(--ion-color-primary);
    --color: var(--ion-color-primary-contrast);

    &:hover {
      --background: var(--ion-color-primary-shade);
    }
  }

  &.button-outline {
    --border-color: var(--ion-color-primary);
    --background: transparent;
    --color: var(--ion-color-primary);

    &:hover {
      --background: rgba(var(--ion-color-primary-rgb), 0.05);
    }
  }

  &.button-clear {
    --color: var(--ion-color-primary);

    &:hover {
      --color: var(--ion-color-primary-shade);
    }
  }

  ion-icon {
    margin-right: var(--app-spacing-xs);
  }
}

// Form elements
// --------------------------------------------------
ion-item {
  --padding-start: var(--app-spacing-md);
  --padding-end: var(--app-spacing-md);
  --padding-top: var(--app-spacing-sm);
  --padding-bottom: var(--app-spacing-sm);
  --inner-padding-end: 0;
  --border-color: var(--app-border-color);
  --background: var(--app-background-color);
  --background-hover: rgba(var(--ion-color-primary-rgb), 0.05);

  &.item-interactive {
    --background-activated: rgba(var(--ion-color-primary-rgb), 0.1);
  }
}

ion-input, ion-textarea, ion-select {
  --padding-start: var(--app-spacing-md);
  --padding-end: var(--app-spacing-md);
  --padding-top: var(--app-spacing-md);
  --padding-bottom: var(--app-spacing-md);
  --background: var(--app-input-background);
  --color: var(--app-text-color);
  --placeholder-color: var(--ion-color-medium);
  --placeholder-opacity: 0.7;
  border-radius: var(--app-border-radius-sm);
  margin: var(--app-spacing-xs) 0;

  &.has-focus {
    --background: var(--app-input-background);
    --border-color: var(--ion-color-primary);
  }
}

ion-label {
  color: var(--app-text-color);
  font-weight: 500;
  margin-bottom: var(--app-spacing-xs);
}

// Lists
// --------------------------------------------------
ion-list {
  background: transparent;
  padding: var(--app-spacing-sm) 0;

  ion-list-header {
    padding-left: var(--app-spacing-md);
    padding-right: var(--app-spacing-md);
    font-weight: 600;
    letter-spacing: 0.03em;
    text-transform: uppercase;
    font-size: 0.8rem;
    color: var(--ion-color-medium);
  }

  ion-item {
    --padding-start: var(--app-spacing-md);
    --padding-end: var(--app-spacing-md);
    --inner-padding-end: var(--app-spacing-md);
    margin-bottom: var(--app-spacing-xs);

    &:last-child {
      --border-color: transparent;
    }

    h2 {
      font-weight: 500;
      font-size: 1rem;
      color: var(--app-text-color);
    }

    p {
      font-size: 0.9rem;
      color: var(--ion-color-medium);
    }
  }
}

// Toolbars
// --------------------------------------------------
ion-toolbar {
  --background: var(--ion-color-primary);
  --color: var(--ion-color-primary-contrast);
  --border-color: transparent;
  --padding-top: var(--app-spacing-sm);
  --padding-bottom: var(--app-spacing-sm);
  --padding-start: var(--app-spacing-md);
  --padding-end: var(--app-spacing-md);

  ion-title {
    font-size: 1.1rem;
    font-weight: 500;
  }

  ion-buttons {
    ion-button {
      --padding-start: var(--app-spacing-sm);
      --padding-end: var(--app-spacing-sm);
    }
  }
}

// Tabs
// --------------------------------------------------
ion-tab-bar {
  --background: var(--app-background-color);
  --border-color: var(--app-border-color);
  padding: var(--app-spacing-xs) 0;

  ion-tab-button {
    --color: var(--ion-color-medium);
    --color-selected: var(--ion-color-primary);

    ion-icon {
      font-size: 1.4rem;
    }

    ion-label {
      font-size: 0.7rem;
      font-weight: 500;
      margin-top: var(--app-spacing-xs);
    }
  }
}

// Badges
// --------------------------------------------------
ion-badge {
  padding: var(--app-spacing-xs) var(--app-spacing-sm);
  border-radius: var(--app-border-radius-sm);
  font-weight: 500;
  font-size: 0.75rem;
}

// Segment
// --------------------------------------------------
ion-segment {
  --background: transparent;
  padding: var(--app-spacing-sm);

  ion-segment-button {
    --background: transparent;
    --background-checked: var(--ion-color-primary);
    --background-hover: rgba(var(--ion-color-primary-rgb), 0.05);
    --border-radius: var(--app-border-radius-md);
    --border-color: var(--app-border-color);
    --border-style: solid;
    --border-width: 1px;
    --color: var(--ion-color-medium);
    --color-checked: var(--ion-color-primary-contrast);
    --indicator-color: transparent;
    min-height: 40px;
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: none;

    &::part(indicator) {
      display: none;
    }
  }
}
