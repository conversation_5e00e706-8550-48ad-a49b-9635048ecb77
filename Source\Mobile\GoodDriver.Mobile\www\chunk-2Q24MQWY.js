import {
  init_index_a5d50daf,
  win
} from "./chunk-WX4TOLMF.js";
import {
  __esm
} from "./chunk-4W6HR7MY.js";

// node_modules/@ionic/core/dist/esm/capacitor-59395cbd.js
var getCapacitor;
var init_capacitor_59395cbd = __esm({
  "node_modules/@ionic/core/dist/esm/capacitor-59395cbd.js"() {
    "use strict";
    init_index_a5d50daf();
    getCapacitor = () => {
      if (win !== void 0) {
        return win.Capacitor;
      }
      return void 0;
    };
  }
});

export {
  getCapacitor,
  init_capacitor_59395cbd
};
/*! Bundled license information:

@ionic/core/dist/esm/capacitor-59395cbd.js:
  (*!
   * (C) Ionic http://ionicframework.com - MIT License
   *)
*/
//# sourceMappingURL=chunk-2Q24MQWY.js.map
