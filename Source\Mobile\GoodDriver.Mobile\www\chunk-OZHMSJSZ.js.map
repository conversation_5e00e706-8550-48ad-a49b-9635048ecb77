{"version": 3, "sources": ["node_modules/localforage/dist/localforage.js", "node_modules/@ionic/storage/dist/esm/index.js", "node_modules/@ionic/storage-angular/fesm2020/ionic-storage-angular.mjs", "src/app/core/services/session.service.ts", "src/app/core/services/toast.service.ts", "src/app/core/models/sync.model.ts", "src/app/core/services/vehicle-state.service.ts", "src/app/core/services/network.service.ts", "src/environments/environment.ts", "src/app/core/services/api.service.ts", "src/app/core/services/vehicle.service.ts"], "sourcesContent": ["/*!\n    localForage -- Offline Storage, Improved\n    Version 1.10.0\n    https://localforage.github.io/localForage\n    (c) 2013-2017 Mozilla, Apache License 2.0\n*/\n(function (f) {\n  if (typeof exports === \"object\" && typeof module !== \"undefined\") {\n    module.exports = f();\n  } else if (typeof define === \"function\" && define.amd) {\n    define([], f);\n  } else {\n    var g;\n    if (typeof window !== \"undefined\") {\n      g = window;\n    } else if (typeof global !== \"undefined\") {\n      g = global;\n    } else if (typeof self !== \"undefined\") {\n      g = self;\n    } else {\n      g = this;\n    }\n    g.localforage = f();\n  }\n})(function () {\n  var define, module, exports;\n  return function e(t, n, r) {\n    function s(o, u) {\n      if (!n[o]) {\n        if (!t[o]) {\n          var a = typeof require == \"function\" && require;\n          if (!u && a) return a(o, !0);\n          if (i) return i(o, !0);\n          var f = new Error(\"Cannot find module '\" + o + \"'\");\n          throw f.code = \"MODULE_NOT_FOUND\", f;\n        }\n        var l = n[o] = {\n          exports: {}\n        };\n        t[o][0].call(l.exports, function (e) {\n          var n = t[o][1][e];\n          return s(n ? n : e);\n        }, l, l.exports, e, t, n, r);\n      }\n      return n[o].exports;\n    }\n    var i = typeof require == \"function\" && require;\n    for (var o = 0; o < r.length; o++) s(r[o]);\n    return s;\n  }({\n    1: [function (_dereq_, module, exports) {\n      (function (global) {\n        'use strict';\n\n        var Mutation = global.MutationObserver || global.WebKitMutationObserver;\n        var scheduleDrain;\n        {\n          if (Mutation) {\n            var called = 0;\n            var observer = new Mutation(nextTick);\n            var element = global.document.createTextNode('');\n            observer.observe(element, {\n              characterData: true\n            });\n            scheduleDrain = function () {\n              element.data = called = ++called % 2;\n            };\n          } else if (!global.setImmediate && typeof global.MessageChannel !== 'undefined') {\n            var channel = new global.MessageChannel();\n            channel.port1.onmessage = nextTick;\n            scheduleDrain = function () {\n              channel.port2.postMessage(0);\n            };\n          } else if ('document' in global && 'onreadystatechange' in global.document.createElement('script')) {\n            scheduleDrain = function () {\n              // Create a <script> element; its readystatechange event will be fired asynchronously once it is inserted\n              // into the document. Do so, thus queuing up the task. Remember to clean up once it's been called.\n              var scriptEl = global.document.createElement('script');\n              scriptEl.onreadystatechange = function () {\n                nextTick();\n                scriptEl.onreadystatechange = null;\n                scriptEl.parentNode.removeChild(scriptEl);\n                scriptEl = null;\n              };\n              global.document.documentElement.appendChild(scriptEl);\n            };\n          } else {\n            scheduleDrain = function () {\n              setTimeout(nextTick, 0);\n            };\n          }\n        }\n        var draining;\n        var queue = [];\n        //named nextTick for less confusing stack traces\n        function nextTick() {\n          draining = true;\n          var i, oldQueue;\n          var len = queue.length;\n          while (len) {\n            oldQueue = queue;\n            queue = [];\n            i = -1;\n            while (++i < len) {\n              oldQueue[i]();\n            }\n            len = queue.length;\n          }\n          draining = false;\n        }\n        module.exports = immediate;\n        function immediate(task) {\n          if (queue.push(task) === 1 && !draining) {\n            scheduleDrain();\n          }\n        }\n      }).call(this, typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : typeof window !== \"undefined\" ? window : {});\n    }, {}],\n    2: [function (_dereq_, module, exports) {\n      'use strict';\n\n      var immediate = _dereq_(1);\n\n      /* istanbul ignore next */\n      function INTERNAL() {}\n      var handlers = {};\n      var REJECTED = ['REJECTED'];\n      var FULFILLED = ['FULFILLED'];\n      var PENDING = ['PENDING'];\n      module.exports = Promise;\n      function Promise(resolver) {\n        if (typeof resolver !== 'function') {\n          throw new TypeError('resolver must be a function');\n        }\n        this.state = PENDING;\n        this.queue = [];\n        this.outcome = void 0;\n        if (resolver !== INTERNAL) {\n          safelyResolveThenable(this, resolver);\n        }\n      }\n      Promise.prototype[\"catch\"] = function (onRejected) {\n        return this.then(null, onRejected);\n      };\n      Promise.prototype.then = function (onFulfilled, onRejected) {\n        if (typeof onFulfilled !== 'function' && this.state === FULFILLED || typeof onRejected !== 'function' && this.state === REJECTED) {\n          return this;\n        }\n        var promise = new this.constructor(INTERNAL);\n        if (this.state !== PENDING) {\n          var resolver = this.state === FULFILLED ? onFulfilled : onRejected;\n          unwrap(promise, resolver, this.outcome);\n        } else {\n          this.queue.push(new QueueItem(promise, onFulfilled, onRejected));\n        }\n        return promise;\n      };\n      function QueueItem(promise, onFulfilled, onRejected) {\n        this.promise = promise;\n        if (typeof onFulfilled === 'function') {\n          this.onFulfilled = onFulfilled;\n          this.callFulfilled = this.otherCallFulfilled;\n        }\n        if (typeof onRejected === 'function') {\n          this.onRejected = onRejected;\n          this.callRejected = this.otherCallRejected;\n        }\n      }\n      QueueItem.prototype.callFulfilled = function (value) {\n        handlers.resolve(this.promise, value);\n      };\n      QueueItem.prototype.otherCallFulfilled = function (value) {\n        unwrap(this.promise, this.onFulfilled, value);\n      };\n      QueueItem.prototype.callRejected = function (value) {\n        handlers.reject(this.promise, value);\n      };\n      QueueItem.prototype.otherCallRejected = function (value) {\n        unwrap(this.promise, this.onRejected, value);\n      };\n      function unwrap(promise, func, value) {\n        immediate(function () {\n          var returnValue;\n          try {\n            returnValue = func(value);\n          } catch (e) {\n            return handlers.reject(promise, e);\n          }\n          if (returnValue === promise) {\n            handlers.reject(promise, new TypeError('Cannot resolve promise with itself'));\n          } else {\n            handlers.resolve(promise, returnValue);\n          }\n        });\n      }\n      handlers.resolve = function (self, value) {\n        var result = tryCatch(getThen, value);\n        if (result.status === 'error') {\n          return handlers.reject(self, result.value);\n        }\n        var thenable = result.value;\n        if (thenable) {\n          safelyResolveThenable(self, thenable);\n        } else {\n          self.state = FULFILLED;\n          self.outcome = value;\n          var i = -1;\n          var len = self.queue.length;\n          while (++i < len) {\n            self.queue[i].callFulfilled(value);\n          }\n        }\n        return self;\n      };\n      handlers.reject = function (self, error) {\n        self.state = REJECTED;\n        self.outcome = error;\n        var i = -1;\n        var len = self.queue.length;\n        while (++i < len) {\n          self.queue[i].callRejected(error);\n        }\n        return self;\n      };\n      function getThen(obj) {\n        // Make sure we only access the accessor once as required by the spec\n        var then = obj && obj.then;\n        if (obj && (typeof obj === 'object' || typeof obj === 'function') && typeof then === 'function') {\n          return function appyThen() {\n            then.apply(obj, arguments);\n          };\n        }\n      }\n      function safelyResolveThenable(self, thenable) {\n        // Either fulfill, reject or reject with error\n        var called = false;\n        function onError(value) {\n          if (called) {\n            return;\n          }\n          called = true;\n          handlers.reject(self, value);\n        }\n        function onSuccess(value) {\n          if (called) {\n            return;\n          }\n          called = true;\n          handlers.resolve(self, value);\n        }\n        function tryToUnwrap() {\n          thenable(onSuccess, onError);\n        }\n        var result = tryCatch(tryToUnwrap);\n        if (result.status === 'error') {\n          onError(result.value);\n        }\n      }\n      function tryCatch(func, value) {\n        var out = {};\n        try {\n          out.value = func(value);\n          out.status = 'success';\n        } catch (e) {\n          out.status = 'error';\n          out.value = e;\n        }\n        return out;\n      }\n      Promise.resolve = resolve;\n      function resolve(value) {\n        if (value instanceof this) {\n          return value;\n        }\n        return handlers.resolve(new this(INTERNAL), value);\n      }\n      Promise.reject = reject;\n      function reject(reason) {\n        var promise = new this(INTERNAL);\n        return handlers.reject(promise, reason);\n      }\n      Promise.all = all;\n      function all(iterable) {\n        var self = this;\n        if (Object.prototype.toString.call(iterable) !== '[object Array]') {\n          return this.reject(new TypeError('must be an array'));\n        }\n        var len = iterable.length;\n        var called = false;\n        if (!len) {\n          return this.resolve([]);\n        }\n        var values = new Array(len);\n        var resolved = 0;\n        var i = -1;\n        var promise = new this(INTERNAL);\n        while (++i < len) {\n          allResolver(iterable[i], i);\n        }\n        return promise;\n        function allResolver(value, i) {\n          self.resolve(value).then(resolveFromAll, function (error) {\n            if (!called) {\n              called = true;\n              handlers.reject(promise, error);\n            }\n          });\n          function resolveFromAll(outValue) {\n            values[i] = outValue;\n            if (++resolved === len && !called) {\n              called = true;\n              handlers.resolve(promise, values);\n            }\n          }\n        }\n      }\n      Promise.race = race;\n      function race(iterable) {\n        var self = this;\n        if (Object.prototype.toString.call(iterable) !== '[object Array]') {\n          return this.reject(new TypeError('must be an array'));\n        }\n        var len = iterable.length;\n        var called = false;\n        if (!len) {\n          return this.resolve([]);\n        }\n        var i = -1;\n        var promise = new this(INTERNAL);\n        while (++i < len) {\n          resolver(iterable[i]);\n        }\n        return promise;\n        function resolver(value) {\n          self.resolve(value).then(function (response) {\n            if (!called) {\n              called = true;\n              handlers.resolve(promise, response);\n            }\n          }, function (error) {\n            if (!called) {\n              called = true;\n              handlers.reject(promise, error);\n            }\n          });\n        }\n      }\n    }, {\n      \"1\": 1\n    }],\n    3: [function (_dereq_, module, exports) {\n      (function (global) {\n        'use strict';\n\n        if (typeof global.Promise !== 'function') {\n          global.Promise = _dereq_(2);\n        }\n      }).call(this, typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : typeof window !== \"undefined\" ? window : {});\n    }, {\n      \"2\": 2\n    }],\n    4: [function (_dereq_, module, exports) {\n      'use strict';\n\n      var _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n        return typeof obj;\n      } : function (obj) {\n        return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n      };\n      function _classCallCheck(instance, Constructor) {\n        if (!(instance instanceof Constructor)) {\n          throw new TypeError(\"Cannot call a class as a function\");\n        }\n      }\n      function getIDB() {\n        /* global indexedDB,webkitIndexedDB,mozIndexedDB,OIndexedDB,msIndexedDB */\n        try {\n          if (typeof indexedDB !== 'undefined') {\n            return indexedDB;\n          }\n          if (typeof webkitIndexedDB !== 'undefined') {\n            return webkitIndexedDB;\n          }\n          if (typeof mozIndexedDB !== 'undefined') {\n            return mozIndexedDB;\n          }\n          if (typeof OIndexedDB !== 'undefined') {\n            return OIndexedDB;\n          }\n          if (typeof msIndexedDB !== 'undefined') {\n            return msIndexedDB;\n          }\n        } catch (e) {\n          return;\n        }\n      }\n      var idb = getIDB();\n      function isIndexedDBValid() {\n        try {\n          // Initialize IndexedDB; fall back to vendor-prefixed versions\n          // if needed.\n          if (!idb || !idb.open) {\n            return false;\n          }\n          // We mimic PouchDB here;\n          //\n          // We test for openDatabase because IE Mobile identifies itself\n          // as Safari. Oh the lulz...\n          var isSafari = typeof openDatabase !== 'undefined' && /(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent) && !/BlackBerry/.test(navigator.platform);\n          var hasFetch = typeof fetch === 'function' && fetch.toString().indexOf('[native code') !== -1;\n\n          // Safari <10.1 does not meet our requirements for IDB support\n          // (see: https://github.com/pouchdb/pouchdb/issues/5572).\n          // Safari 10.1 shipped with fetch, we can use that to detect it.\n          // Note: this creates issues with `window.fetch` polyfills and\n          // overrides; see:\n          // https://github.com/localForage/localForage/issues/856\n          return (!isSafari || hasFetch) && typeof indexedDB !== 'undefined' &&\n          // some outdated implementations of IDB that appear on Samsung\n          // and HTC Android devices <4.4 are missing IDBKeyRange\n          // See: https://github.com/mozilla/localForage/issues/128\n          // See: https://github.com/mozilla/localForage/issues/272\n          typeof IDBKeyRange !== 'undefined';\n        } catch (e) {\n          return false;\n        }\n      }\n\n      // Abstracts constructing a Blob object, so it also works in older\n      // browsers that don't support the native Blob constructor. (i.e.\n      // old QtWebKit versions, at least).\n      // Abstracts constructing a Blob object, so it also works in older\n      // browsers that don't support the native Blob constructor. (i.e.\n      // old QtWebKit versions, at least).\n      function createBlob(parts, properties) {\n        /* global BlobBuilder,MSBlobBuilder,MozBlobBuilder,WebKitBlobBuilder */\n        parts = parts || [];\n        properties = properties || {};\n        try {\n          return new Blob(parts, properties);\n        } catch (e) {\n          if (e.name !== 'TypeError') {\n            throw e;\n          }\n          var Builder = typeof BlobBuilder !== 'undefined' ? BlobBuilder : typeof MSBlobBuilder !== 'undefined' ? MSBlobBuilder : typeof MozBlobBuilder !== 'undefined' ? MozBlobBuilder : WebKitBlobBuilder;\n          var builder = new Builder();\n          for (var i = 0; i < parts.length; i += 1) {\n            builder.append(parts[i]);\n          }\n          return builder.getBlob(properties.type);\n        }\n      }\n\n      // This is CommonJS because lie is an external dependency, so Rollup\n      // can just ignore it.\n      if (typeof Promise === 'undefined') {\n        // In the \"nopromises\" build this will just throw if you don't have\n        // a global promise object, but it would throw anyway later.\n        _dereq_(3);\n      }\n      var Promise$1 = Promise;\n      function executeCallback(promise, callback) {\n        if (callback) {\n          promise.then(function (result) {\n            callback(null, result);\n          }, function (error) {\n            callback(error);\n          });\n        }\n      }\n      function executeTwoCallbacks(promise, callback, errorCallback) {\n        if (typeof callback === 'function') {\n          promise.then(callback);\n        }\n        if (typeof errorCallback === 'function') {\n          promise[\"catch\"](errorCallback);\n        }\n      }\n      function normalizeKey(key) {\n        // Cast the key to a string, as that's all we can set as a key.\n        if (typeof key !== 'string') {\n          console.warn(key + ' used as a key, but it is not a string.');\n          key = String(key);\n        }\n        return key;\n      }\n      function getCallback() {\n        if (arguments.length && typeof arguments[arguments.length - 1] === 'function') {\n          return arguments[arguments.length - 1];\n        }\n      }\n\n      // Some code originally from async_storage.js in\n      // [Gaia](https://github.com/mozilla-b2g/gaia).\n\n      var DETECT_BLOB_SUPPORT_STORE = 'local-forage-detect-blob-support';\n      var supportsBlobs = void 0;\n      var dbContexts = {};\n      var toString = Object.prototype.toString;\n\n      // Transaction Modes\n      var READ_ONLY = 'readonly';\n      var READ_WRITE = 'readwrite';\n\n      // Transform a binary string to an array buffer, because otherwise\n      // weird stuff happens when you try to work with the binary string directly.\n      // It is known.\n      // From http://stackoverflow.com/questions/14967647/ (continues on next line)\n      // encode-decode-image-with-base64-breaks-image (2013-04-21)\n      function _binStringToArrayBuffer(bin) {\n        var length = bin.length;\n        var buf = new ArrayBuffer(length);\n        var arr = new Uint8Array(buf);\n        for (var i = 0; i < length; i++) {\n          arr[i] = bin.charCodeAt(i);\n        }\n        return buf;\n      }\n\n      //\n      // Blobs are not supported in all versions of IndexedDB, notably\n      // Chrome <37 and Android <5. In those versions, storing a blob will throw.\n      //\n      // Various other blob bugs exist in Chrome v37-42 (inclusive).\n      // Detecting them is expensive and confusing to users, and Chrome 37-42\n      // is at very low usage worldwide, so we do a hacky userAgent check instead.\n      //\n      // content-type bug: https://code.google.com/p/chromium/issues/detail?id=408120\n      // 404 bug: https://code.google.com/p/chromium/issues/detail?id=447916\n      // FileReader bug: https://code.google.com/p/chromium/issues/detail?id=447836\n      //\n      // Code borrowed from PouchDB. See:\n      // https://github.com/pouchdb/pouchdb/blob/master/packages/node_modules/pouchdb-adapter-idb/src/blobSupport.js\n      //\n      function _checkBlobSupportWithoutCaching(idb) {\n        return new Promise$1(function (resolve) {\n          var txn = idb.transaction(DETECT_BLOB_SUPPORT_STORE, READ_WRITE);\n          var blob = createBlob(['']);\n          txn.objectStore(DETECT_BLOB_SUPPORT_STORE).put(blob, 'key');\n          txn.onabort = function (e) {\n            // If the transaction aborts now its due to not being able to\n            // write to the database, likely due to the disk being full\n            e.preventDefault();\n            e.stopPropagation();\n            resolve(false);\n          };\n          txn.oncomplete = function () {\n            var matchedChrome = navigator.userAgent.match(/Chrome\\/(\\d+)/);\n            var matchedEdge = navigator.userAgent.match(/Edge\\//);\n            // MS Edge pretends to be Chrome 42:\n            // https://msdn.microsoft.com/en-us/library/hh869301%28v=vs.85%29.aspx\n            resolve(matchedEdge || !matchedChrome || parseInt(matchedChrome[1], 10) >= 43);\n          };\n        })[\"catch\"](function () {\n          return false; // error, so assume unsupported\n        });\n      }\n      function _checkBlobSupport(idb) {\n        if (typeof supportsBlobs === 'boolean') {\n          return Promise$1.resolve(supportsBlobs);\n        }\n        return _checkBlobSupportWithoutCaching(idb).then(function (value) {\n          supportsBlobs = value;\n          return supportsBlobs;\n        });\n      }\n      function _deferReadiness(dbInfo) {\n        var dbContext = dbContexts[dbInfo.name];\n\n        // Create a deferred object representing the current database operation.\n        var deferredOperation = {};\n        deferredOperation.promise = new Promise$1(function (resolve, reject) {\n          deferredOperation.resolve = resolve;\n          deferredOperation.reject = reject;\n        });\n\n        // Enqueue the deferred operation.\n        dbContext.deferredOperations.push(deferredOperation);\n\n        // Chain its promise to the database readiness.\n        if (!dbContext.dbReady) {\n          dbContext.dbReady = deferredOperation.promise;\n        } else {\n          dbContext.dbReady = dbContext.dbReady.then(function () {\n            return deferredOperation.promise;\n          });\n        }\n      }\n      function _advanceReadiness(dbInfo) {\n        var dbContext = dbContexts[dbInfo.name];\n\n        // Dequeue a deferred operation.\n        var deferredOperation = dbContext.deferredOperations.pop();\n\n        // Resolve its promise (which is part of the database readiness\n        // chain of promises).\n        if (deferredOperation) {\n          deferredOperation.resolve();\n          return deferredOperation.promise;\n        }\n      }\n      function _rejectReadiness(dbInfo, err) {\n        var dbContext = dbContexts[dbInfo.name];\n\n        // Dequeue a deferred operation.\n        var deferredOperation = dbContext.deferredOperations.pop();\n\n        // Reject its promise (which is part of the database readiness\n        // chain of promises).\n        if (deferredOperation) {\n          deferredOperation.reject(err);\n          return deferredOperation.promise;\n        }\n      }\n      function _getConnection(dbInfo, upgradeNeeded) {\n        return new Promise$1(function (resolve, reject) {\n          dbContexts[dbInfo.name] = dbContexts[dbInfo.name] || createDbContext();\n          if (dbInfo.db) {\n            if (upgradeNeeded) {\n              _deferReadiness(dbInfo);\n              dbInfo.db.close();\n            } else {\n              return resolve(dbInfo.db);\n            }\n          }\n          var dbArgs = [dbInfo.name];\n          if (upgradeNeeded) {\n            dbArgs.push(dbInfo.version);\n          }\n          var openreq = idb.open.apply(idb, dbArgs);\n          if (upgradeNeeded) {\n            openreq.onupgradeneeded = function (e) {\n              var db = openreq.result;\n              try {\n                db.createObjectStore(dbInfo.storeName);\n                if (e.oldVersion <= 1) {\n                  // Added when support for blob shims was added\n                  db.createObjectStore(DETECT_BLOB_SUPPORT_STORE);\n                }\n              } catch (ex) {\n                if (ex.name === 'ConstraintError') {\n                  console.warn('The database \"' + dbInfo.name + '\"' + ' has been upgraded from version ' + e.oldVersion + ' to version ' + e.newVersion + ', but the storage \"' + dbInfo.storeName + '\" already exists.');\n                } else {\n                  throw ex;\n                }\n              }\n            };\n          }\n          openreq.onerror = function (e) {\n            e.preventDefault();\n            reject(openreq.error);\n          };\n          openreq.onsuccess = function () {\n            var db = openreq.result;\n            db.onversionchange = function (e) {\n              // Triggered when the database is modified (e.g. adding an objectStore) or\n              // deleted (even when initiated by other sessions in different tabs).\n              // Closing the connection here prevents those operations from being blocked.\n              // If the database is accessed again later by this instance, the connection\n              // will be reopened or the database recreated as needed.\n              e.target.close();\n            };\n            resolve(db);\n            _advanceReadiness(dbInfo);\n          };\n        });\n      }\n      function _getOriginalConnection(dbInfo) {\n        return _getConnection(dbInfo, false);\n      }\n      function _getUpgradedConnection(dbInfo) {\n        return _getConnection(dbInfo, true);\n      }\n      function _isUpgradeNeeded(dbInfo, defaultVersion) {\n        if (!dbInfo.db) {\n          return true;\n        }\n        var isNewStore = !dbInfo.db.objectStoreNames.contains(dbInfo.storeName);\n        var isDowngrade = dbInfo.version < dbInfo.db.version;\n        var isUpgrade = dbInfo.version > dbInfo.db.version;\n        if (isDowngrade) {\n          // If the version is not the default one\n          // then warn for impossible downgrade.\n          if (dbInfo.version !== defaultVersion) {\n            console.warn('The database \"' + dbInfo.name + '\"' + \" can't be downgraded from version \" + dbInfo.db.version + ' to version ' + dbInfo.version + '.');\n          }\n          // Align the versions to prevent errors.\n          dbInfo.version = dbInfo.db.version;\n        }\n        if (isUpgrade || isNewStore) {\n          // If the store is new then increment the version (if needed).\n          // This will trigger an \"upgradeneeded\" event which is required\n          // for creating a store.\n          if (isNewStore) {\n            var incVersion = dbInfo.db.version + 1;\n            if (incVersion > dbInfo.version) {\n              dbInfo.version = incVersion;\n            }\n          }\n          return true;\n        }\n        return false;\n      }\n\n      // encode a blob for indexeddb engines that don't support blobs\n      function _encodeBlob(blob) {\n        return new Promise$1(function (resolve, reject) {\n          var reader = new FileReader();\n          reader.onerror = reject;\n          reader.onloadend = function (e) {\n            var base64 = btoa(e.target.result || '');\n            resolve({\n              __local_forage_encoded_blob: true,\n              data: base64,\n              type: blob.type\n            });\n          };\n          reader.readAsBinaryString(blob);\n        });\n      }\n\n      // decode an encoded blob\n      function _decodeBlob(encodedBlob) {\n        var arrayBuff = _binStringToArrayBuffer(atob(encodedBlob.data));\n        return createBlob([arrayBuff], {\n          type: encodedBlob.type\n        });\n      }\n\n      // is this one of our fancy encoded blobs?\n      function _isEncodedBlob(value) {\n        return value && value.__local_forage_encoded_blob;\n      }\n\n      // Specialize the default `ready()` function by making it dependent\n      // on the current database operations. Thus, the driver will be actually\n      // ready when it's been initialized (default) *and* there are no pending\n      // operations on the database (initiated by some other instances).\n      function _fullyReady(callback) {\n        var self = this;\n        var promise = self._initReady().then(function () {\n          var dbContext = dbContexts[self._dbInfo.name];\n          if (dbContext && dbContext.dbReady) {\n            return dbContext.dbReady;\n          }\n        });\n        executeTwoCallbacks(promise, callback, callback);\n        return promise;\n      }\n\n      // Try to establish a new db connection to replace the\n      // current one which is broken (i.e. experiencing\n      // InvalidStateError while creating a transaction).\n      function _tryReconnect(dbInfo) {\n        _deferReadiness(dbInfo);\n        var dbContext = dbContexts[dbInfo.name];\n        var forages = dbContext.forages;\n        for (var i = 0; i < forages.length; i++) {\n          var forage = forages[i];\n          if (forage._dbInfo.db) {\n            forage._dbInfo.db.close();\n            forage._dbInfo.db = null;\n          }\n        }\n        dbInfo.db = null;\n        return _getOriginalConnection(dbInfo).then(function (db) {\n          dbInfo.db = db;\n          if (_isUpgradeNeeded(dbInfo)) {\n            // Reopen the database for upgrading.\n            return _getUpgradedConnection(dbInfo);\n          }\n          return db;\n        }).then(function (db) {\n          // store the latest db reference\n          // in case the db was upgraded\n          dbInfo.db = dbContext.db = db;\n          for (var i = 0; i < forages.length; i++) {\n            forages[i]._dbInfo.db = db;\n          }\n        })[\"catch\"](function (err) {\n          _rejectReadiness(dbInfo, err);\n          throw err;\n        });\n      }\n\n      // FF doesn't like Promises (micro-tasks) and IDDB store operations,\n      // so we have to do it with callbacks\n      function createTransaction(dbInfo, mode, callback, retries) {\n        if (retries === undefined) {\n          retries = 1;\n        }\n        try {\n          var tx = dbInfo.db.transaction(dbInfo.storeName, mode);\n          callback(null, tx);\n        } catch (err) {\n          if (retries > 0 && (!dbInfo.db || err.name === 'InvalidStateError' || err.name === 'NotFoundError')) {\n            return Promise$1.resolve().then(function () {\n              if (!dbInfo.db || err.name === 'NotFoundError' && !dbInfo.db.objectStoreNames.contains(dbInfo.storeName) && dbInfo.version <= dbInfo.db.version) {\n                // increase the db version, to create the new ObjectStore\n                if (dbInfo.db) {\n                  dbInfo.version = dbInfo.db.version + 1;\n                }\n                // Reopen the database for upgrading.\n                return _getUpgradedConnection(dbInfo);\n              }\n            }).then(function () {\n              return _tryReconnect(dbInfo).then(function () {\n                createTransaction(dbInfo, mode, callback, retries - 1);\n              });\n            })[\"catch\"](callback);\n          }\n          callback(err);\n        }\n      }\n      function createDbContext() {\n        return {\n          // Running localForages sharing a database.\n          forages: [],\n          // Shared database.\n          db: null,\n          // Database readiness (promise).\n          dbReady: null,\n          // Deferred operations on the database.\n          deferredOperations: []\n        };\n      }\n\n      // Open the IndexedDB database (automatically creates one if one didn't\n      // previously exist), using any options set in the config.\n      function _initStorage(options) {\n        var self = this;\n        var dbInfo = {\n          db: null\n        };\n        if (options) {\n          for (var i in options) {\n            dbInfo[i] = options[i];\n          }\n        }\n\n        // Get the current context of the database;\n        var dbContext = dbContexts[dbInfo.name];\n\n        // ...or create a new context.\n        if (!dbContext) {\n          dbContext = createDbContext();\n          // Register the new context in the global container.\n          dbContexts[dbInfo.name] = dbContext;\n        }\n\n        // Register itself as a running localForage in the current context.\n        dbContext.forages.push(self);\n\n        // Replace the default `ready()` function with the specialized one.\n        if (!self._initReady) {\n          self._initReady = self.ready;\n          self.ready = _fullyReady;\n        }\n\n        // Create an array of initialization states of the related localForages.\n        var initPromises = [];\n        function ignoreErrors() {\n          // Don't handle errors here,\n          // just makes sure related localForages aren't pending.\n          return Promise$1.resolve();\n        }\n        for (var j = 0; j < dbContext.forages.length; j++) {\n          var forage = dbContext.forages[j];\n          if (forage !== self) {\n            // Don't wait for itself...\n            initPromises.push(forage._initReady()[\"catch\"](ignoreErrors));\n          }\n        }\n\n        // Take a snapshot of the related localForages.\n        var forages = dbContext.forages.slice(0);\n\n        // Initialize the connection process only when\n        // all the related localForages aren't pending.\n        return Promise$1.all(initPromises).then(function () {\n          dbInfo.db = dbContext.db;\n          // Get the connection or open a new one without upgrade.\n          return _getOriginalConnection(dbInfo);\n        }).then(function (db) {\n          dbInfo.db = db;\n          if (_isUpgradeNeeded(dbInfo, self._defaultConfig.version)) {\n            // Reopen the database for upgrading.\n            return _getUpgradedConnection(dbInfo);\n          }\n          return db;\n        }).then(function (db) {\n          dbInfo.db = dbContext.db = db;\n          self._dbInfo = dbInfo;\n          // Share the final connection amongst related localForages.\n          for (var k = 0; k < forages.length; k++) {\n            var forage = forages[k];\n            if (forage !== self) {\n              // Self is already up-to-date.\n              forage._dbInfo.db = dbInfo.db;\n              forage._dbInfo.version = dbInfo.version;\n            }\n          }\n        });\n      }\n      function getItem(key, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                var req = store.get(key);\n                req.onsuccess = function () {\n                  var value = req.result;\n                  if (value === undefined) {\n                    value = null;\n                  }\n                  if (_isEncodedBlob(value)) {\n                    value = _decodeBlob(value);\n                  }\n                  resolve(value);\n                };\n                req.onerror = function () {\n                  reject(req.error);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Iterate over all items stored in database.\n      function iterate(iterator, callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                var req = store.openCursor();\n                var iterationNumber = 1;\n                req.onsuccess = function () {\n                  var cursor = req.result;\n                  if (cursor) {\n                    var value = cursor.value;\n                    if (_isEncodedBlob(value)) {\n                      value = _decodeBlob(value);\n                    }\n                    var result = iterator(value, cursor.key, iterationNumber++);\n\n                    // when the iterator callback returns any\n                    // (non-`undefined`) value, then we stop\n                    // the iteration immediately\n                    if (result !== void 0) {\n                      resolve(result);\n                    } else {\n                      cursor[\"continue\"]();\n                    }\n                  } else {\n                    resolve();\n                  }\n                };\n                req.onerror = function () {\n                  reject(req.error);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function setItem(key, value, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = new Promise$1(function (resolve, reject) {\n          var dbInfo;\n          self.ready().then(function () {\n            dbInfo = self._dbInfo;\n            if (toString.call(value) === '[object Blob]') {\n              return _checkBlobSupport(dbInfo.db).then(function (blobSupport) {\n                if (blobSupport) {\n                  return value;\n                }\n                return _encodeBlob(value);\n              });\n            }\n            return value;\n          }).then(function (value) {\n            createTransaction(self._dbInfo, READ_WRITE, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n\n                // The reason we don't _save_ null is because IE 10 does\n                // not support saving the `null` type in IndexedDB. How\n                // ironic, given the bug below!\n                // See: https://github.com/mozilla/localForage/issues/161\n                if (value === null) {\n                  value = undefined;\n                }\n                var req = store.put(value, key);\n                transaction.oncomplete = function () {\n                  // Cast to undefined so the value passed to\n                  // callback/promise is the same as what one would get out\n                  // of `getItem()` later. This leads to some weirdness\n                  // (setItem('foo', undefined) will return `null`), but\n                  // it's not my fault localStorage is our baseline and that\n                  // it's weird.\n                  if (value === undefined) {\n                    value = null;\n                  }\n                  resolve(value);\n                };\n                transaction.onabort = transaction.onerror = function () {\n                  var err = req.error ? req.error : req.transaction.error;\n                  reject(err);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function removeItem(key, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_WRITE, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                // We use a Grunt task to make this safe for IE and some\n                // versions of Android (including those used by Cordova).\n                // Normally IE won't like `.delete()` and will insist on\n                // using `['delete']()`, but we have a build step that\n                // fixes this for us now.\n                var req = store[\"delete\"](key);\n                transaction.oncomplete = function () {\n                  resolve();\n                };\n                transaction.onerror = function () {\n                  reject(req.error);\n                };\n\n                // The request will be also be aborted if we've exceeded our storage\n                // space.\n                transaction.onabort = function () {\n                  var err = req.error ? req.error : req.transaction.error;\n                  reject(err);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function clear(callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_WRITE, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                var req = store.clear();\n                transaction.oncomplete = function () {\n                  resolve();\n                };\n                transaction.onabort = transaction.onerror = function () {\n                  var err = req.error ? req.error : req.transaction.error;\n                  reject(err);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function length(callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                var req = store.count();\n                req.onsuccess = function () {\n                  resolve(req.result);\n                };\n                req.onerror = function () {\n                  reject(req.error);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function key(n, callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          if (n < 0) {\n            resolve(null);\n            return;\n          }\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                var advanced = false;\n                var req = store.openKeyCursor();\n                req.onsuccess = function () {\n                  var cursor = req.result;\n                  if (!cursor) {\n                    // this means there weren't enough keys\n                    resolve(null);\n                    return;\n                  }\n                  if (n === 0) {\n                    // We have the first key, return it if that's what they\n                    // wanted.\n                    resolve(cursor.key);\n                  } else {\n                    if (!advanced) {\n                      // Otherwise, ask the cursor to skip ahead n\n                      // records.\n                      advanced = true;\n                      cursor.advance(n);\n                    } else {\n                      // When we get here, we've got the nth key.\n                      resolve(cursor.key);\n                    }\n                  }\n                };\n                req.onerror = function () {\n                  reject(req.error);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function keys(callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                var req = store.openKeyCursor();\n                var keys = [];\n                req.onsuccess = function () {\n                  var cursor = req.result;\n                  if (!cursor) {\n                    resolve(keys);\n                    return;\n                  }\n                  keys.push(cursor.key);\n                  cursor[\"continue\"]();\n                };\n                req.onerror = function () {\n                  reject(req.error);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function dropInstance(options, callback) {\n        callback = getCallback.apply(this, arguments);\n        var currentConfig = this.config();\n        options = typeof options !== 'function' && options || {};\n        if (!options.name) {\n          options.name = options.name || currentConfig.name;\n          options.storeName = options.storeName || currentConfig.storeName;\n        }\n        var self = this;\n        var promise;\n        if (!options.name) {\n          promise = Promise$1.reject('Invalid arguments');\n        } else {\n          var isCurrentDb = options.name === currentConfig.name && self._dbInfo.db;\n          var dbPromise = isCurrentDb ? Promise$1.resolve(self._dbInfo.db) : _getOriginalConnection(options).then(function (db) {\n            var dbContext = dbContexts[options.name];\n            var forages = dbContext.forages;\n            dbContext.db = db;\n            for (var i = 0; i < forages.length; i++) {\n              forages[i]._dbInfo.db = db;\n            }\n            return db;\n          });\n          if (!options.storeName) {\n            promise = dbPromise.then(function (db) {\n              _deferReadiness(options);\n              var dbContext = dbContexts[options.name];\n              var forages = dbContext.forages;\n              db.close();\n              for (var i = 0; i < forages.length; i++) {\n                var forage = forages[i];\n                forage._dbInfo.db = null;\n              }\n              var dropDBPromise = new Promise$1(function (resolve, reject) {\n                var req = idb.deleteDatabase(options.name);\n                req.onerror = function () {\n                  var db = req.result;\n                  if (db) {\n                    db.close();\n                  }\n                  reject(req.error);\n                };\n                req.onblocked = function () {\n                  // Closing all open connections in onversionchange handler should prevent this situation, but if\n                  // we do get here, it just means the request remains pending - eventually it will succeed or error\n                  console.warn('dropInstance blocked for database \"' + options.name + '\" until all open connections are closed');\n                };\n                req.onsuccess = function () {\n                  var db = req.result;\n                  if (db) {\n                    db.close();\n                  }\n                  resolve(db);\n                };\n              });\n              return dropDBPromise.then(function (db) {\n                dbContext.db = db;\n                for (var i = 0; i < forages.length; i++) {\n                  var _forage = forages[i];\n                  _advanceReadiness(_forage._dbInfo);\n                }\n              })[\"catch\"](function (err) {\n                (_rejectReadiness(options, err) || Promise$1.resolve())[\"catch\"](function () {});\n                throw err;\n              });\n            });\n          } else {\n            promise = dbPromise.then(function (db) {\n              if (!db.objectStoreNames.contains(options.storeName)) {\n                return;\n              }\n              var newVersion = db.version + 1;\n              _deferReadiness(options);\n              var dbContext = dbContexts[options.name];\n              var forages = dbContext.forages;\n              db.close();\n              for (var i = 0; i < forages.length; i++) {\n                var forage = forages[i];\n                forage._dbInfo.db = null;\n                forage._dbInfo.version = newVersion;\n              }\n              var dropObjectPromise = new Promise$1(function (resolve, reject) {\n                var req = idb.open(options.name, newVersion);\n                req.onerror = function (err) {\n                  var db = req.result;\n                  db.close();\n                  reject(err);\n                };\n                req.onupgradeneeded = function () {\n                  var db = req.result;\n                  db.deleteObjectStore(options.storeName);\n                };\n                req.onsuccess = function () {\n                  var db = req.result;\n                  db.close();\n                  resolve(db);\n                };\n              });\n              return dropObjectPromise.then(function (db) {\n                dbContext.db = db;\n                for (var j = 0; j < forages.length; j++) {\n                  var _forage2 = forages[j];\n                  _forage2._dbInfo.db = db;\n                  _advanceReadiness(_forage2._dbInfo);\n                }\n              })[\"catch\"](function (err) {\n                (_rejectReadiness(options, err) || Promise$1.resolve())[\"catch\"](function () {});\n                throw err;\n              });\n            });\n          }\n        }\n        executeCallback(promise, callback);\n        return promise;\n      }\n      var asyncStorage = {\n        _driver: 'asyncStorage',\n        _initStorage: _initStorage,\n        _support: isIndexedDBValid(),\n        iterate: iterate,\n        getItem: getItem,\n        setItem: setItem,\n        removeItem: removeItem,\n        clear: clear,\n        length: length,\n        key: key,\n        keys: keys,\n        dropInstance: dropInstance\n      };\n      function isWebSQLValid() {\n        return typeof openDatabase === 'function';\n      }\n\n      // Sadly, the best way to save binary data in WebSQL/localStorage is serializing\n      // it to Base64, so this is how we store it to prevent very strange errors with less\n      // verbose ways of binary <-> string data storage.\n      var BASE_CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n      var BLOB_TYPE_PREFIX = '~~local_forage_type~';\n      var BLOB_TYPE_PREFIX_REGEX = /^~~local_forage_type~([^~]+)~/;\n      var SERIALIZED_MARKER = '__lfsc__:';\n      var SERIALIZED_MARKER_LENGTH = SERIALIZED_MARKER.length;\n\n      // OMG the serializations!\n      var TYPE_ARRAYBUFFER = 'arbf';\n      var TYPE_BLOB = 'blob';\n      var TYPE_INT8ARRAY = 'si08';\n      var TYPE_UINT8ARRAY = 'ui08';\n      var TYPE_UINT8CLAMPEDARRAY = 'uic8';\n      var TYPE_INT16ARRAY = 'si16';\n      var TYPE_INT32ARRAY = 'si32';\n      var TYPE_UINT16ARRAY = 'ur16';\n      var TYPE_UINT32ARRAY = 'ui32';\n      var TYPE_FLOAT32ARRAY = 'fl32';\n      var TYPE_FLOAT64ARRAY = 'fl64';\n      var TYPE_SERIALIZED_MARKER_LENGTH = SERIALIZED_MARKER_LENGTH + TYPE_ARRAYBUFFER.length;\n      var toString$1 = Object.prototype.toString;\n      function stringToBuffer(serializedString) {\n        // Fill the string into a ArrayBuffer.\n        var bufferLength = serializedString.length * 0.75;\n        var len = serializedString.length;\n        var i;\n        var p = 0;\n        var encoded1, encoded2, encoded3, encoded4;\n        if (serializedString[serializedString.length - 1] === '=') {\n          bufferLength--;\n          if (serializedString[serializedString.length - 2] === '=') {\n            bufferLength--;\n          }\n        }\n        var buffer = new ArrayBuffer(bufferLength);\n        var bytes = new Uint8Array(buffer);\n        for (i = 0; i < len; i += 4) {\n          encoded1 = BASE_CHARS.indexOf(serializedString[i]);\n          encoded2 = BASE_CHARS.indexOf(serializedString[i + 1]);\n          encoded3 = BASE_CHARS.indexOf(serializedString[i + 2]);\n          encoded4 = BASE_CHARS.indexOf(serializedString[i + 3]);\n\n          /*jslint bitwise: true */\n          bytes[p++] = encoded1 << 2 | encoded2 >> 4;\n          bytes[p++] = (encoded2 & 15) << 4 | encoded3 >> 2;\n          bytes[p++] = (encoded3 & 3) << 6 | encoded4 & 63;\n        }\n        return buffer;\n      }\n\n      // Converts a buffer to a string to store, serialized, in the backend\n      // storage library.\n      function bufferToString(buffer) {\n        // base64-arraybuffer\n        var bytes = new Uint8Array(buffer);\n        var base64String = '';\n        var i;\n        for (i = 0; i < bytes.length; i += 3) {\n          /*jslint bitwise: true */\n          base64String += BASE_CHARS[bytes[i] >> 2];\n          base64String += BASE_CHARS[(bytes[i] & 3) << 4 | bytes[i + 1] >> 4];\n          base64String += BASE_CHARS[(bytes[i + 1] & 15) << 2 | bytes[i + 2] >> 6];\n          base64String += BASE_CHARS[bytes[i + 2] & 63];\n        }\n        if (bytes.length % 3 === 2) {\n          base64String = base64String.substring(0, base64String.length - 1) + '=';\n        } else if (bytes.length % 3 === 1) {\n          base64String = base64String.substring(0, base64String.length - 2) + '==';\n        }\n        return base64String;\n      }\n\n      // Serialize a value, afterwards executing a callback (which usually\n      // instructs the `setItem()` callback/promise to be executed). This is how\n      // we store binary data with localStorage.\n      function serialize(value, callback) {\n        var valueType = '';\n        if (value) {\n          valueType = toString$1.call(value);\n        }\n\n        // Cannot use `value instanceof ArrayBuffer` or such here, as these\n        // checks fail when running the tests using casper.js...\n        //\n        // TODO: See why those tests fail and use a better solution.\n        if (value && (valueType === '[object ArrayBuffer]' || value.buffer && toString$1.call(value.buffer) === '[object ArrayBuffer]')) {\n          // Convert binary arrays to a string and prefix the string with\n          // a special marker.\n          var buffer;\n          var marker = SERIALIZED_MARKER;\n          if (value instanceof ArrayBuffer) {\n            buffer = value;\n            marker += TYPE_ARRAYBUFFER;\n          } else {\n            buffer = value.buffer;\n            if (valueType === '[object Int8Array]') {\n              marker += TYPE_INT8ARRAY;\n            } else if (valueType === '[object Uint8Array]') {\n              marker += TYPE_UINT8ARRAY;\n            } else if (valueType === '[object Uint8ClampedArray]') {\n              marker += TYPE_UINT8CLAMPEDARRAY;\n            } else if (valueType === '[object Int16Array]') {\n              marker += TYPE_INT16ARRAY;\n            } else if (valueType === '[object Uint16Array]') {\n              marker += TYPE_UINT16ARRAY;\n            } else if (valueType === '[object Int32Array]') {\n              marker += TYPE_INT32ARRAY;\n            } else if (valueType === '[object Uint32Array]') {\n              marker += TYPE_UINT32ARRAY;\n            } else if (valueType === '[object Float32Array]') {\n              marker += TYPE_FLOAT32ARRAY;\n            } else if (valueType === '[object Float64Array]') {\n              marker += TYPE_FLOAT64ARRAY;\n            } else {\n              callback(new Error('Failed to get type for BinaryArray'));\n            }\n          }\n          callback(marker + bufferToString(buffer));\n        } else if (valueType === '[object Blob]') {\n          // Conver the blob to a binaryArray and then to a string.\n          var fileReader = new FileReader();\n          fileReader.onload = function () {\n            // Backwards-compatible prefix for the blob type.\n            var str = BLOB_TYPE_PREFIX + value.type + '~' + bufferToString(this.result);\n            callback(SERIALIZED_MARKER + TYPE_BLOB + str);\n          };\n          fileReader.readAsArrayBuffer(value);\n        } else {\n          try {\n            callback(JSON.stringify(value));\n          } catch (e) {\n            console.error(\"Couldn't convert value into a JSON string: \", value);\n            callback(null, e);\n          }\n        }\n      }\n\n      // Deserialize data we've inserted into a value column/field. We place\n      // special markers into our strings to mark them as encoded; this isn't\n      // as nice as a meta field, but it's the only sane thing we can do whilst\n      // keeping localStorage support intact.\n      //\n      // Oftentimes this will just deserialize JSON content, but if we have a\n      // special marker (SERIALIZED_MARKER, defined above), we will extract\n      // some kind of arraybuffer/binary data/typed array out of the string.\n      function deserialize(value) {\n        // If we haven't marked this string as being specially serialized (i.e.\n        // something other than serialized JSON), we can just return it and be\n        // done with it.\n        if (value.substring(0, SERIALIZED_MARKER_LENGTH) !== SERIALIZED_MARKER) {\n          return JSON.parse(value);\n        }\n\n        // The following code deals with deserializing some kind of Blob or\n        // TypedArray. First we separate out the type of data we're dealing\n        // with from the data itself.\n        var serializedString = value.substring(TYPE_SERIALIZED_MARKER_LENGTH);\n        var type = value.substring(SERIALIZED_MARKER_LENGTH, TYPE_SERIALIZED_MARKER_LENGTH);\n        var blobType;\n        // Backwards-compatible blob type serialization strategy.\n        // DBs created with older versions of localForage will simply not have the blob type.\n        if (type === TYPE_BLOB && BLOB_TYPE_PREFIX_REGEX.test(serializedString)) {\n          var matcher = serializedString.match(BLOB_TYPE_PREFIX_REGEX);\n          blobType = matcher[1];\n          serializedString = serializedString.substring(matcher[0].length);\n        }\n        var buffer = stringToBuffer(serializedString);\n\n        // Return the right type based on the code/type set during\n        // serialization.\n        switch (type) {\n          case TYPE_ARRAYBUFFER:\n            return buffer;\n          case TYPE_BLOB:\n            return createBlob([buffer], {\n              type: blobType\n            });\n          case TYPE_INT8ARRAY:\n            return new Int8Array(buffer);\n          case TYPE_UINT8ARRAY:\n            return new Uint8Array(buffer);\n          case TYPE_UINT8CLAMPEDARRAY:\n            return new Uint8ClampedArray(buffer);\n          case TYPE_INT16ARRAY:\n            return new Int16Array(buffer);\n          case TYPE_UINT16ARRAY:\n            return new Uint16Array(buffer);\n          case TYPE_INT32ARRAY:\n            return new Int32Array(buffer);\n          case TYPE_UINT32ARRAY:\n            return new Uint32Array(buffer);\n          case TYPE_FLOAT32ARRAY:\n            return new Float32Array(buffer);\n          case TYPE_FLOAT64ARRAY:\n            return new Float64Array(buffer);\n          default:\n            throw new Error('Unkown type: ' + type);\n        }\n      }\n      var localforageSerializer = {\n        serialize: serialize,\n        deserialize: deserialize,\n        stringToBuffer: stringToBuffer,\n        bufferToString: bufferToString\n      };\n\n      /*\n       * Includes code from:\n       *\n       * base64-arraybuffer\n       * https://github.com/niklasvh/base64-arraybuffer\n       *\n       * Copyright (c) 2012 Niklas von Hertzen\n       * Licensed under the MIT license.\n       */\n\n      function createDbTable(t, dbInfo, callback, errorCallback) {\n        t.executeSql('CREATE TABLE IF NOT EXISTS ' + dbInfo.storeName + ' ' + '(id INTEGER PRIMARY KEY, key unique, value)', [], callback, errorCallback);\n      }\n\n      // Open the WebSQL database (automatically creates one if one didn't\n      // previously exist), using any options set in the config.\n      function _initStorage$1(options) {\n        var self = this;\n        var dbInfo = {\n          db: null\n        };\n        if (options) {\n          for (var i in options) {\n            dbInfo[i] = typeof options[i] !== 'string' ? options[i].toString() : options[i];\n          }\n        }\n        var dbInfoPromise = new Promise$1(function (resolve, reject) {\n          // Open the database; the openDatabase API will automatically\n          // create it for us if it doesn't exist.\n          try {\n            dbInfo.db = openDatabase(dbInfo.name, String(dbInfo.version), dbInfo.description, dbInfo.size);\n          } catch (e) {\n            return reject(e);\n          }\n\n          // Create our key/value table if it doesn't exist.\n          dbInfo.db.transaction(function (t) {\n            createDbTable(t, dbInfo, function () {\n              self._dbInfo = dbInfo;\n              resolve();\n            }, function (t, error) {\n              reject(error);\n            });\n          }, reject);\n        });\n        dbInfo.serializer = localforageSerializer;\n        return dbInfoPromise;\n      }\n      function tryExecuteSql(t, dbInfo, sqlStatement, args, callback, errorCallback) {\n        t.executeSql(sqlStatement, args, callback, function (t, error) {\n          if (error.code === error.SYNTAX_ERR) {\n            t.executeSql('SELECT name FROM sqlite_master ' + \"WHERE type='table' AND name = ?\", [dbInfo.storeName], function (t, results) {\n              if (!results.rows.length) {\n                // if the table is missing (was deleted)\n                // re-create it table and retry\n                createDbTable(t, dbInfo, function () {\n                  t.executeSql(sqlStatement, args, callback, errorCallback);\n                }, errorCallback);\n              } else {\n                errorCallback(t, error);\n              }\n            }, errorCallback);\n          } else {\n            errorCallback(t, error);\n          }\n        }, errorCallback);\n      }\n      function getItem$1(key, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              tryExecuteSql(t, dbInfo, 'SELECT * FROM ' + dbInfo.storeName + ' WHERE key = ? LIMIT 1', [key], function (t, results) {\n                var result = results.rows.length ? results.rows.item(0).value : null;\n\n                // Check to see if this is serialized content we need to\n                // unpack.\n                if (result) {\n                  result = dbInfo.serializer.deserialize(result);\n                }\n                resolve(result);\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function iterate$1(iterator, callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              tryExecuteSql(t, dbInfo, 'SELECT * FROM ' + dbInfo.storeName, [], function (t, results) {\n                var rows = results.rows;\n                var length = rows.length;\n                for (var i = 0; i < length; i++) {\n                  var item = rows.item(i);\n                  var result = item.value;\n\n                  // Check to see if this is serialized content\n                  // we need to unpack.\n                  if (result) {\n                    result = dbInfo.serializer.deserialize(result);\n                  }\n                  result = iterator(result, item.key, i + 1);\n\n                  // void(0) prevents problems with redefinition\n                  // of `undefined`.\n                  if (result !== void 0) {\n                    resolve(result);\n                    return;\n                  }\n                }\n                resolve();\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function _setItem(key, value, callback, retriesLeft) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            // The localStorage API doesn't return undefined values in an\n            // \"expected\" way, so undefined is always cast to null in all\n            // drivers. See: https://github.com/mozilla/localForage/pull/42\n            if (value === undefined) {\n              value = null;\n            }\n\n            // Save the original value to pass to the callback.\n            var originalValue = value;\n            var dbInfo = self._dbInfo;\n            dbInfo.serializer.serialize(value, function (value, error) {\n              if (error) {\n                reject(error);\n              } else {\n                dbInfo.db.transaction(function (t) {\n                  tryExecuteSql(t, dbInfo, 'INSERT OR REPLACE INTO ' + dbInfo.storeName + ' ' + '(key, value) VALUES (?, ?)', [key, value], function () {\n                    resolve(originalValue);\n                  }, function (t, error) {\n                    reject(error);\n                  });\n                }, function (sqlError) {\n                  // The transaction failed; check\n                  // to see if it's a quota error.\n                  if (sqlError.code === sqlError.QUOTA_ERR) {\n                    // We reject the callback outright for now, but\n                    // it's worth trying to re-run the transaction.\n                    // Even if the user accepts the prompt to use\n                    // more storage on Safari, this error will\n                    // be called.\n                    //\n                    // Try to re-run the transaction.\n                    if (retriesLeft > 0) {\n                      resolve(_setItem.apply(self, [key, originalValue, callback, retriesLeft - 1]));\n                      return;\n                    }\n                    reject(sqlError);\n                  }\n                });\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function setItem$1(key, value, callback) {\n        return _setItem.apply(this, [key, value, callback, 1]);\n      }\n      function removeItem$1(key, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              tryExecuteSql(t, dbInfo, 'DELETE FROM ' + dbInfo.storeName + ' WHERE key = ?', [key], function () {\n                resolve();\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Deletes every item in the table.\n      // TODO: Find out if this resets the AUTO_INCREMENT number.\n      function clear$1(callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              tryExecuteSql(t, dbInfo, 'DELETE FROM ' + dbInfo.storeName, [], function () {\n                resolve();\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Does a simple `COUNT(key)` to get the number of items stored in\n      // localForage.\n      function length$1(callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              // Ahhh, SQL makes this one soooooo easy.\n              tryExecuteSql(t, dbInfo, 'SELECT COUNT(key) as c FROM ' + dbInfo.storeName, [], function (t, results) {\n                var result = results.rows.item(0).c;\n                resolve(result);\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Return the key located at key index X; essentially gets the key from a\n      // `WHERE id = ?`. This is the most efficient way I can think to implement\n      // this rarely-used (in my experience) part of the API, but it can seem\n      // inconsistent, because we do `INSERT OR REPLACE INTO` on `setItem()`, so\n      // the ID of each key will change every time it's updated. Perhaps a stored\n      // procedure for the `setItem()` SQL would solve this problem?\n      // TODO: Don't change ID on `setItem()`.\n      function key$1(n, callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              tryExecuteSql(t, dbInfo, 'SELECT key FROM ' + dbInfo.storeName + ' WHERE id = ? LIMIT 1', [n + 1], function (t, results) {\n                var result = results.rows.length ? results.rows.item(0).key : null;\n                resolve(result);\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function keys$1(callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              tryExecuteSql(t, dbInfo, 'SELECT key FROM ' + dbInfo.storeName, [], function (t, results) {\n                var keys = [];\n                for (var i = 0; i < results.rows.length; i++) {\n                  keys.push(results.rows.item(i).key);\n                }\n                resolve(keys);\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // https://www.w3.org/TR/webdatabase/#databases\n      // > There is no way to enumerate or delete the databases available for an origin from this API.\n      function getAllStoreNames(db) {\n        return new Promise$1(function (resolve, reject) {\n          db.transaction(function (t) {\n            t.executeSql('SELECT name FROM sqlite_master ' + \"WHERE type='table' AND name <> '__WebKitDatabaseInfoTable__'\", [], function (t, results) {\n              var storeNames = [];\n              for (var i = 0; i < results.rows.length; i++) {\n                storeNames.push(results.rows.item(i).name);\n              }\n              resolve({\n                db: db,\n                storeNames: storeNames\n              });\n            }, function (t, error) {\n              reject(error);\n            });\n          }, function (sqlError) {\n            reject(sqlError);\n          });\n        });\n      }\n      function dropInstance$1(options, callback) {\n        callback = getCallback.apply(this, arguments);\n        var currentConfig = this.config();\n        options = typeof options !== 'function' && options || {};\n        if (!options.name) {\n          options.name = options.name || currentConfig.name;\n          options.storeName = options.storeName || currentConfig.storeName;\n        }\n        var self = this;\n        var promise;\n        if (!options.name) {\n          promise = Promise$1.reject('Invalid arguments');\n        } else {\n          promise = new Promise$1(function (resolve) {\n            var db;\n            if (options.name === currentConfig.name) {\n              // use the db reference of the current instance\n              db = self._dbInfo.db;\n            } else {\n              db = openDatabase(options.name, '', '', 0);\n            }\n            if (!options.storeName) {\n              // drop all database tables\n              resolve(getAllStoreNames(db));\n            } else {\n              resolve({\n                db: db,\n                storeNames: [options.storeName]\n              });\n            }\n          }).then(function (operationInfo) {\n            return new Promise$1(function (resolve, reject) {\n              operationInfo.db.transaction(function (t) {\n                function dropTable(storeName) {\n                  return new Promise$1(function (resolve, reject) {\n                    t.executeSql('DROP TABLE IF EXISTS ' + storeName, [], function () {\n                      resolve();\n                    }, function (t, error) {\n                      reject(error);\n                    });\n                  });\n                }\n                var operations = [];\n                for (var i = 0, len = operationInfo.storeNames.length; i < len; i++) {\n                  operations.push(dropTable(operationInfo.storeNames[i]));\n                }\n                Promise$1.all(operations).then(function () {\n                  resolve();\n                })[\"catch\"](function (e) {\n                  reject(e);\n                });\n              }, function (sqlError) {\n                reject(sqlError);\n              });\n            });\n          });\n        }\n        executeCallback(promise, callback);\n        return promise;\n      }\n      var webSQLStorage = {\n        _driver: 'webSQLStorage',\n        _initStorage: _initStorage$1,\n        _support: isWebSQLValid(),\n        iterate: iterate$1,\n        getItem: getItem$1,\n        setItem: setItem$1,\n        removeItem: removeItem$1,\n        clear: clear$1,\n        length: length$1,\n        key: key$1,\n        keys: keys$1,\n        dropInstance: dropInstance$1\n      };\n      function isLocalStorageValid() {\n        try {\n          return typeof localStorage !== 'undefined' && 'setItem' in localStorage &&\n          // in IE8 typeof localStorage.setItem === 'object'\n          !!localStorage.setItem;\n        } catch (e) {\n          return false;\n        }\n      }\n      function _getKeyPrefix(options, defaultConfig) {\n        var keyPrefix = options.name + '/';\n        if (options.storeName !== defaultConfig.storeName) {\n          keyPrefix += options.storeName + '/';\n        }\n        return keyPrefix;\n      }\n\n      // Check if localStorage throws when saving an item\n      function checkIfLocalStorageThrows() {\n        var localStorageTestKey = '_localforage_support_test';\n        try {\n          localStorage.setItem(localStorageTestKey, true);\n          localStorage.removeItem(localStorageTestKey);\n          return false;\n        } catch (e) {\n          return true;\n        }\n      }\n\n      // Check if localStorage is usable and allows to save an item\n      // This method checks if localStorage is usable in Safari Private Browsing\n      // mode, or in any other case where the available quota for localStorage\n      // is 0 and there wasn't any saved items yet.\n      function _isLocalStorageUsable() {\n        return !checkIfLocalStorageThrows() || localStorage.length > 0;\n      }\n\n      // Config the localStorage backend, using options set in the config.\n      function _initStorage$2(options) {\n        var self = this;\n        var dbInfo = {};\n        if (options) {\n          for (var i in options) {\n            dbInfo[i] = options[i];\n          }\n        }\n        dbInfo.keyPrefix = _getKeyPrefix(options, self._defaultConfig);\n        if (!_isLocalStorageUsable()) {\n          return Promise$1.reject();\n        }\n        self._dbInfo = dbInfo;\n        dbInfo.serializer = localforageSerializer;\n        return Promise$1.resolve();\n      }\n\n      // Remove all keys from the datastore, effectively destroying all data in\n      // the app's key/value store!\n      function clear$2(callback) {\n        var self = this;\n        var promise = self.ready().then(function () {\n          var keyPrefix = self._dbInfo.keyPrefix;\n          for (var i = localStorage.length - 1; i >= 0; i--) {\n            var key = localStorage.key(i);\n            if (key.indexOf(keyPrefix) === 0) {\n              localStorage.removeItem(key);\n            }\n          }\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Retrieve an item from the store. Unlike the original async_storage\n      // library in Gaia, we don't modify return values at all. If a key's value\n      // is `undefined`, we pass that value to the callback function.\n      function getItem$2(key, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = self.ready().then(function () {\n          var dbInfo = self._dbInfo;\n          var result = localStorage.getItem(dbInfo.keyPrefix + key);\n\n          // If a result was found, parse it from the serialized\n          // string into a JS object. If result isn't truthy, the key\n          // is likely undefined and we'll pass it straight to the\n          // callback.\n          if (result) {\n            result = dbInfo.serializer.deserialize(result);\n          }\n          return result;\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Iterate over all items in the store.\n      function iterate$2(iterator, callback) {\n        var self = this;\n        var promise = self.ready().then(function () {\n          var dbInfo = self._dbInfo;\n          var keyPrefix = dbInfo.keyPrefix;\n          var keyPrefixLength = keyPrefix.length;\n          var length = localStorage.length;\n\n          // We use a dedicated iterator instead of the `i` variable below\n          // so other keys we fetch in localStorage aren't counted in\n          // the `iterationNumber` argument passed to the `iterate()`\n          // callback.\n          //\n          // See: github.com/mozilla/localForage/pull/435#discussion_r38061530\n          var iterationNumber = 1;\n          for (var i = 0; i < length; i++) {\n            var key = localStorage.key(i);\n            if (key.indexOf(keyPrefix) !== 0) {\n              continue;\n            }\n            var value = localStorage.getItem(key);\n\n            // If a result was found, parse it from the serialized\n            // string into a JS object. If result isn't truthy, the\n            // key is likely undefined and we'll pass it straight\n            // to the iterator.\n            if (value) {\n              value = dbInfo.serializer.deserialize(value);\n            }\n            value = iterator(value, key.substring(keyPrefixLength), iterationNumber++);\n            if (value !== void 0) {\n              return value;\n            }\n          }\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Same as localStorage's key() method, except takes a callback.\n      function key$2(n, callback) {\n        var self = this;\n        var promise = self.ready().then(function () {\n          var dbInfo = self._dbInfo;\n          var result;\n          try {\n            result = localStorage.key(n);\n          } catch (error) {\n            result = null;\n          }\n\n          // Remove the prefix from the key, if a key is found.\n          if (result) {\n            result = result.substring(dbInfo.keyPrefix.length);\n          }\n          return result;\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function keys$2(callback) {\n        var self = this;\n        var promise = self.ready().then(function () {\n          var dbInfo = self._dbInfo;\n          var length = localStorage.length;\n          var keys = [];\n          for (var i = 0; i < length; i++) {\n            var itemKey = localStorage.key(i);\n            if (itemKey.indexOf(dbInfo.keyPrefix) === 0) {\n              keys.push(itemKey.substring(dbInfo.keyPrefix.length));\n            }\n          }\n          return keys;\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Supply the number of keys in the datastore to the callback function.\n      function length$2(callback) {\n        var self = this;\n        var promise = self.keys().then(function (keys) {\n          return keys.length;\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Remove an item from the store, nice and simple.\n      function removeItem$2(key, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = self.ready().then(function () {\n          var dbInfo = self._dbInfo;\n          localStorage.removeItem(dbInfo.keyPrefix + key);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Set a key's value and run an optional callback once the value is set.\n      // Unlike Gaia's implementation, the callback function is passed the value,\n      // in case you want to operate on that value only after you're sure it\n      // saved, or something like that.\n      function setItem$2(key, value, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = self.ready().then(function () {\n          // Convert undefined values to null.\n          // https://github.com/mozilla/localForage/pull/42\n          if (value === undefined) {\n            value = null;\n          }\n\n          // Save the original value to pass to the callback.\n          var originalValue = value;\n          return new Promise$1(function (resolve, reject) {\n            var dbInfo = self._dbInfo;\n            dbInfo.serializer.serialize(value, function (value, error) {\n              if (error) {\n                reject(error);\n              } else {\n                try {\n                  localStorage.setItem(dbInfo.keyPrefix + key, value);\n                  resolve(originalValue);\n                } catch (e) {\n                  // localStorage capacity exceeded.\n                  // TODO: Make this a specific error/event.\n                  if (e.name === 'QuotaExceededError' || e.name === 'NS_ERROR_DOM_QUOTA_REACHED') {\n                    reject(e);\n                  }\n                  reject(e);\n                }\n              }\n            });\n          });\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function dropInstance$2(options, callback) {\n        callback = getCallback.apply(this, arguments);\n        options = typeof options !== 'function' && options || {};\n        if (!options.name) {\n          var currentConfig = this.config();\n          options.name = options.name || currentConfig.name;\n          options.storeName = options.storeName || currentConfig.storeName;\n        }\n        var self = this;\n        var promise;\n        if (!options.name) {\n          promise = Promise$1.reject('Invalid arguments');\n        } else {\n          promise = new Promise$1(function (resolve) {\n            if (!options.storeName) {\n              resolve(options.name + '/');\n            } else {\n              resolve(_getKeyPrefix(options, self._defaultConfig));\n            }\n          }).then(function (keyPrefix) {\n            for (var i = localStorage.length - 1; i >= 0; i--) {\n              var key = localStorage.key(i);\n              if (key.indexOf(keyPrefix) === 0) {\n                localStorage.removeItem(key);\n              }\n            }\n          });\n        }\n        executeCallback(promise, callback);\n        return promise;\n      }\n      var localStorageWrapper = {\n        _driver: 'localStorageWrapper',\n        _initStorage: _initStorage$2,\n        _support: isLocalStorageValid(),\n        iterate: iterate$2,\n        getItem: getItem$2,\n        setItem: setItem$2,\n        removeItem: removeItem$2,\n        clear: clear$2,\n        length: length$2,\n        key: key$2,\n        keys: keys$2,\n        dropInstance: dropInstance$2\n      };\n      var sameValue = function sameValue(x, y) {\n        return x === y || typeof x === 'number' && typeof y === 'number' && isNaN(x) && isNaN(y);\n      };\n      var includes = function includes(array, searchElement) {\n        var len = array.length;\n        var i = 0;\n        while (i < len) {\n          if (sameValue(array[i], searchElement)) {\n            return true;\n          }\n          i++;\n        }\n        return false;\n      };\n      var isArray = Array.isArray || function (arg) {\n        return Object.prototype.toString.call(arg) === '[object Array]';\n      };\n\n      // Drivers are stored here when `defineDriver()` is called.\n      // They are shared across all instances of localForage.\n      var DefinedDrivers = {};\n      var DriverSupport = {};\n      var DefaultDrivers = {\n        INDEXEDDB: asyncStorage,\n        WEBSQL: webSQLStorage,\n        LOCALSTORAGE: localStorageWrapper\n      };\n      var DefaultDriverOrder = [DefaultDrivers.INDEXEDDB._driver, DefaultDrivers.WEBSQL._driver, DefaultDrivers.LOCALSTORAGE._driver];\n      var OptionalDriverMethods = ['dropInstance'];\n      var LibraryMethods = ['clear', 'getItem', 'iterate', 'key', 'keys', 'length', 'removeItem', 'setItem'].concat(OptionalDriverMethods);\n      var DefaultConfig = {\n        description: '',\n        driver: DefaultDriverOrder.slice(),\n        name: 'localforage',\n        // Default DB size is _JUST UNDER_ 5MB, as it's the highest size\n        // we can use without a prompt.\n        size: 4980736,\n        storeName: 'keyvaluepairs',\n        version: 1.0\n      };\n      function callWhenReady(localForageInstance, libraryMethod) {\n        localForageInstance[libraryMethod] = function () {\n          var _args = arguments;\n          return localForageInstance.ready().then(function () {\n            return localForageInstance[libraryMethod].apply(localForageInstance, _args);\n          });\n        };\n      }\n      function extend() {\n        for (var i = 1; i < arguments.length; i++) {\n          var arg = arguments[i];\n          if (arg) {\n            for (var _key in arg) {\n              if (arg.hasOwnProperty(_key)) {\n                if (isArray(arg[_key])) {\n                  arguments[0][_key] = arg[_key].slice();\n                } else {\n                  arguments[0][_key] = arg[_key];\n                }\n              }\n            }\n          }\n        }\n        return arguments[0];\n      }\n      var LocalForage = function () {\n        function LocalForage(options) {\n          _classCallCheck(this, LocalForage);\n          for (var driverTypeKey in DefaultDrivers) {\n            if (DefaultDrivers.hasOwnProperty(driverTypeKey)) {\n              var driver = DefaultDrivers[driverTypeKey];\n              var driverName = driver._driver;\n              this[driverTypeKey] = driverName;\n              if (!DefinedDrivers[driverName]) {\n                // we don't need to wait for the promise,\n                // since the default drivers can be defined\n                // in a blocking manner\n                this.defineDriver(driver);\n              }\n            }\n          }\n          this._defaultConfig = extend({}, DefaultConfig);\n          this._config = extend({}, this._defaultConfig, options);\n          this._driverSet = null;\n          this._initDriver = null;\n          this._ready = false;\n          this._dbInfo = null;\n          this._wrapLibraryMethodsWithReady();\n          this.setDriver(this._config.driver)[\"catch\"](function () {});\n        }\n\n        // Set any config values for localForage; can be called anytime before\n        // the first API call (e.g. `getItem`, `setItem`).\n        // We loop through options so we don't overwrite existing config\n        // values.\n\n        LocalForage.prototype.config = function config(options) {\n          // If the options argument is an object, we use it to set values.\n          // Otherwise, we return either a specified config value or all\n          // config values.\n          if ((typeof options === 'undefined' ? 'undefined' : _typeof(options)) === 'object') {\n            // If localforage is ready and fully initialized, we can't set\n            // any new configuration values. Instead, we return an error.\n            if (this._ready) {\n              return new Error(\"Can't call config() after localforage \" + 'has been used.');\n            }\n            for (var i in options) {\n              if (i === 'storeName') {\n                options[i] = options[i].replace(/\\W/g, '_');\n              }\n              if (i === 'version' && typeof options[i] !== 'number') {\n                return new Error('Database version must be a number.');\n              }\n              this._config[i] = options[i];\n            }\n\n            // after all config options are set and\n            // the driver option is used, try setting it\n            if ('driver' in options && options.driver) {\n              return this.setDriver(this._config.driver);\n            }\n            return true;\n          } else if (typeof options === 'string') {\n            return this._config[options];\n          } else {\n            return this._config;\n          }\n        };\n\n        // Used to define a custom driver, shared across all instances of\n        // localForage.\n\n        LocalForage.prototype.defineDriver = function defineDriver(driverObject, callback, errorCallback) {\n          var promise = new Promise$1(function (resolve, reject) {\n            try {\n              var driverName = driverObject._driver;\n              var complianceError = new Error('Custom driver not compliant; see ' + 'https://mozilla.github.io/localForage/#definedriver');\n\n              // A driver name should be defined and not overlap with the\n              // library-defined, default drivers.\n              if (!driverObject._driver) {\n                reject(complianceError);\n                return;\n              }\n              var driverMethods = LibraryMethods.concat('_initStorage');\n              for (var i = 0, len = driverMethods.length; i < len; i++) {\n                var driverMethodName = driverMethods[i];\n\n                // when the property is there,\n                // it should be a method even when optional\n                var isRequired = !includes(OptionalDriverMethods, driverMethodName);\n                if ((isRequired || driverObject[driverMethodName]) && typeof driverObject[driverMethodName] !== 'function') {\n                  reject(complianceError);\n                  return;\n                }\n              }\n              var configureMissingMethods = function configureMissingMethods() {\n                var methodNotImplementedFactory = function methodNotImplementedFactory(methodName) {\n                  return function () {\n                    var error = new Error('Method ' + methodName + ' is not implemented by the current driver');\n                    var promise = Promise$1.reject(error);\n                    executeCallback(promise, arguments[arguments.length - 1]);\n                    return promise;\n                  };\n                };\n                for (var _i = 0, _len = OptionalDriverMethods.length; _i < _len; _i++) {\n                  var optionalDriverMethod = OptionalDriverMethods[_i];\n                  if (!driverObject[optionalDriverMethod]) {\n                    driverObject[optionalDriverMethod] = methodNotImplementedFactory(optionalDriverMethod);\n                  }\n                }\n              };\n              configureMissingMethods();\n              var setDriverSupport = function setDriverSupport(support) {\n                if (DefinedDrivers[driverName]) {\n                  console.info('Redefining LocalForage driver: ' + driverName);\n                }\n                DefinedDrivers[driverName] = driverObject;\n                DriverSupport[driverName] = support;\n                // don't use a then, so that we can define\n                // drivers that have simple _support methods\n                // in a blocking manner\n                resolve();\n              };\n              if ('_support' in driverObject) {\n                if (driverObject._support && typeof driverObject._support === 'function') {\n                  driverObject._support().then(setDriverSupport, reject);\n                } else {\n                  setDriverSupport(!!driverObject._support);\n                }\n              } else {\n                setDriverSupport(true);\n              }\n            } catch (e) {\n              reject(e);\n            }\n          });\n          executeTwoCallbacks(promise, callback, errorCallback);\n          return promise;\n        };\n        LocalForage.prototype.driver = function driver() {\n          return this._driver || null;\n        };\n        LocalForage.prototype.getDriver = function getDriver(driverName, callback, errorCallback) {\n          var getDriverPromise = DefinedDrivers[driverName] ? Promise$1.resolve(DefinedDrivers[driverName]) : Promise$1.reject(new Error('Driver not found.'));\n          executeTwoCallbacks(getDriverPromise, callback, errorCallback);\n          return getDriverPromise;\n        };\n        LocalForage.prototype.getSerializer = function getSerializer(callback) {\n          var serializerPromise = Promise$1.resolve(localforageSerializer);\n          executeTwoCallbacks(serializerPromise, callback);\n          return serializerPromise;\n        };\n        LocalForage.prototype.ready = function ready(callback) {\n          var self = this;\n          var promise = self._driverSet.then(function () {\n            if (self._ready === null) {\n              self._ready = self._initDriver();\n            }\n            return self._ready;\n          });\n          executeTwoCallbacks(promise, callback, callback);\n          return promise;\n        };\n        LocalForage.prototype.setDriver = function setDriver(drivers, callback, errorCallback) {\n          var self = this;\n          if (!isArray(drivers)) {\n            drivers = [drivers];\n          }\n          var supportedDrivers = this._getSupportedDrivers(drivers);\n          function setDriverToConfig() {\n            self._config.driver = self.driver();\n          }\n          function extendSelfWithDriver(driver) {\n            self._extend(driver);\n            setDriverToConfig();\n            self._ready = self._initStorage(self._config);\n            return self._ready;\n          }\n          function initDriver(supportedDrivers) {\n            return function () {\n              var currentDriverIndex = 0;\n              function driverPromiseLoop() {\n                while (currentDriverIndex < supportedDrivers.length) {\n                  var driverName = supportedDrivers[currentDriverIndex];\n                  currentDriverIndex++;\n                  self._dbInfo = null;\n                  self._ready = null;\n                  return self.getDriver(driverName).then(extendSelfWithDriver)[\"catch\"](driverPromiseLoop);\n                }\n                setDriverToConfig();\n                var error = new Error('No available storage method found.');\n                self._driverSet = Promise$1.reject(error);\n                return self._driverSet;\n              }\n              return driverPromiseLoop();\n            };\n          }\n\n          // There might be a driver initialization in progress\n          // so wait for it to finish in order to avoid a possible\n          // race condition to set _dbInfo\n          var oldDriverSetDone = this._driverSet !== null ? this._driverSet[\"catch\"](function () {\n            return Promise$1.resolve();\n          }) : Promise$1.resolve();\n          this._driverSet = oldDriverSetDone.then(function () {\n            var driverName = supportedDrivers[0];\n            self._dbInfo = null;\n            self._ready = null;\n            return self.getDriver(driverName).then(function (driver) {\n              self._driver = driver._driver;\n              setDriverToConfig();\n              self._wrapLibraryMethodsWithReady();\n              self._initDriver = initDriver(supportedDrivers);\n            });\n          })[\"catch\"](function () {\n            setDriverToConfig();\n            var error = new Error('No available storage method found.');\n            self._driverSet = Promise$1.reject(error);\n            return self._driverSet;\n          });\n          executeTwoCallbacks(this._driverSet, callback, errorCallback);\n          return this._driverSet;\n        };\n        LocalForage.prototype.supports = function supports(driverName) {\n          return !!DriverSupport[driverName];\n        };\n        LocalForage.prototype._extend = function _extend(libraryMethodsAndProperties) {\n          extend(this, libraryMethodsAndProperties);\n        };\n        LocalForage.prototype._getSupportedDrivers = function _getSupportedDrivers(drivers) {\n          var supportedDrivers = [];\n          for (var i = 0, len = drivers.length; i < len; i++) {\n            var driverName = drivers[i];\n            if (this.supports(driverName)) {\n              supportedDrivers.push(driverName);\n            }\n          }\n          return supportedDrivers;\n        };\n        LocalForage.prototype._wrapLibraryMethodsWithReady = function _wrapLibraryMethodsWithReady() {\n          // Add a stub for each driver API method that delays the call to the\n          // corresponding driver method until localForage is ready. These stubs\n          // will be replaced by the driver methods as soon as the driver is\n          // loaded, so there is no performance impact.\n          for (var i = 0, len = LibraryMethods.length; i < len; i++) {\n            callWhenReady(this, LibraryMethods[i]);\n          }\n        };\n        LocalForage.prototype.createInstance = function createInstance(options) {\n          return new LocalForage(options);\n        };\n        return LocalForage;\n      }();\n\n      // The actual localForage object that we expose as a module or via a\n      // global. It's extended by pulling in one of our other libraries.\n\n      var localforage_js = new LocalForage();\n      module.exports = localforage_js;\n    }, {\n      \"3\": 3\n    }]\n  }, {}, [4])(4);\n});", "import LocalForage from 'localforage';\n/** @hidden */\nexport const Drivers = {\n  SecureStorage: 'ionicSecureStorage',\n  IndexedDB: LocalForage.INDEXEDDB,\n  LocalStorage: LocalForage.LOCALSTORAGE\n};\nconst defaultConfig = {\n  name: '_ionicstorage',\n  storeName: '_ionickv',\n  dbKey: '_ionickey',\n  driverOrder: [Drivers.SecureStorage, Drivers.IndexedDB, Drivers.LocalStorage]\n};\nexport class Storage {\n  /**\n   * Create a new Storage instance using the order of drivers and any additional config\n   * options to pass to LocalForage.\n   *\n   * Possible default driverOrder options are: ['indexeddb', 'localstorage'] and the\n   * default is that exact ordering.\n   *\n   * When using Ionic Secure Storage (enterprise only), use ['ionicSecureStorage', 'indexeddb', 'localstorage'] to ensure\n   * Secure Storage is used when available, or fall back to IndexedDB or LocalStorage on the web.\n   */\n  constructor(config = defaultConfig) {\n    this._db = null;\n    this._secureStorageDriver = null;\n    const actualConfig = Object.assign({}, defaultConfig, config || {});\n    this._config = actualConfig;\n  }\n  async create() {\n    const db = LocalForage.createInstance(this._config);\n    this._db = db;\n    await db.setDriver(this._config.driverOrder || []);\n    return this;\n  }\n  /**\n   * Define a new Driver. Must be called before\n   * initializing the database. Example:\n   *\n   * await storage.defineDriver(myDriver);\n   * await storage.create();\n   */\n  async defineDriver(driver) {\n    if (driver._driver === Drivers.SecureStorage) {\n      this._secureStorageDriver = driver;\n    }\n    return LocalForage.defineDriver(driver);\n  }\n  /**\n   * Get the name of the driver being used.\n   * @returns Name of the driver\n   */\n  get driver() {\n    var _a;\n    return ((_a = this._db) === null || _a === void 0 ? void 0 : _a.driver()) || null;\n  }\n  assertDb() {\n    if (!this._db) {\n      throw new Error('Database not created. Must call create() first');\n    }\n    return this._db;\n  }\n  /**\n   * Get the value associated with the given key.\n   * @param key the key to identify this value\n   * @returns Returns a promise with the value of the given key\n   */\n  get(key) {\n    const db = this.assertDb();\n    return db.getItem(key);\n  }\n  /**\n   * Set the value for the given key.\n   * @param key the key to identify this value\n   * @param value the value for this key\n   * @returns Returns a promise that resolves when the key and value are set\n   */\n  set(key, value) {\n    const db = this.assertDb();\n    return db.setItem(key, value);\n  }\n  /**\n   * Remove any value associated with this key.\n   * @param key the key to identify this value\n   * @returns Returns a promise that resolves when the value is removed\n   */\n  remove(key) {\n    const db = this.assertDb();\n    return db.removeItem(key);\n  }\n  /**\n   * Clear the entire key value store. WARNING: HOT!\n   * @returns Returns a promise that resolves when the store is cleared\n   */\n  clear() {\n    const db = this.assertDb();\n    return db.clear();\n  }\n  /**\n   * @returns Returns a promise that resolves with the number of keys stored.\n   */\n  length() {\n    const db = this.assertDb();\n    return db.length();\n  }\n  /**\n   * @returns Returns a promise that resolves with the keys in the store.\n   */\n  keys() {\n    const db = this.assertDb();\n    return db.keys();\n  }\n  /**\n   * Iterate through each key,value pair.\n   * @param iteratorCallback a callback of the form (value, key, iterationNumber)\n   * @returns Returns a promise that resolves when the iteration has finished.\n   */\n  forEach(iteratorCallback) {\n    const db = this.assertDb();\n    return db.iterate(iteratorCallback);\n  }\n  setEncryptionKey(key) {\n    var _a;\n    if (!this._secureStorageDriver) {\n      throw new Error('@ionic-enterprise/secure-storage not installed. Encryption support not available');\n    } else {\n      (_a = this._secureStorageDriver) === null || _a === void 0 ? void 0 : _a.setEncryptionKey(key);\n    }\n  }\n}\n", "import { isPlatformServer } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, PLATFORM_ID, NgModule } from '@angular/core';\nimport { Storage } from '@ionic/storage';\nexport { Storage } from '@ionic/storage';\nconst StorageConfigToken = new InjectionToken('STORAGE_CONFIG_TOKEN');\nclass NoopStorage extends Storage {\n  constructor() {\n    super();\n  }\n  async create() {\n    return this;\n  }\n  async defineDriver() {}\n  get driver() {\n    return 'noop';\n  }\n  async get(key) {\n    return null;\n  }\n  async set(key, value) {}\n  async remove(key) {}\n  async clear() {}\n  async length() {\n    return 0;\n  }\n  async keys() {\n    return [];\n  }\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  async forEach(iteratorCallback) {}\n  setEncryptionKey(key) {}\n}\nfunction provideStorage(platformId, storageConfig) {\n  if (isPlatformServer(platformId)) {\n    // When running in a server context return the NoopStorage\n    return new NoopStorage();\n  }\n  return new Storage(storageConfig);\n}\nclass IonicStorageModule {\n  static forRoot(storageConfig = null) {\n    return {\n      ngModule: IonicStorageModule,\n      providers: [{\n        provide: StorageConfigToken,\n        useValue: storageConfig\n      }, {\n        provide: Storage,\n        useFactory: provideStorage,\n        deps: [PLATFORM_ID, StorageConfigToken]\n      }]\n    };\n  }\n}\nIonicStorageModule.ɵfac = function IonicStorageModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || IonicStorageModule)();\n};\nIonicStorageModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: IonicStorageModule\n});\nIonicStorageModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonicStorageModule, [{\n    type: NgModule\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { IonicStorageModule, StorageConfigToken, provideStorage };\n", "import { Injectable } from '@angular/core';\r\nimport { Drivers } from '@ionic/storage';\r\nimport { Storage } from '@ionic/storage-angular';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class SessionService {\r\n  private _storage: Storage | null = null;\r\n\r\n  constructor(private storage: Storage) {\r\n    this.init();\r\n  }\r\n\r\n  async init() : Promise<void>{\r\n    if (!this._storage) {        \r\n        await this.storage.create();\r\n        this._storage = this.storage;\r\n      }\r\n  }\r\n\r\n  // Salvar o token e dados do usuário Good Driver\r\n  async setSession(token: string, userId: string, userName: string, userEmail: string) {\r\n    await this._storage?.set('jwt', token);\r\n    await this._storage?.set('userId', userId);\r\n    await this._storage?.set('userName', userName);\r\n    await this._storage?.set('userEmail', userEmail);\r\n  }\r\n\r\n  // Pegar o token\r\n  async getToken() {\r\n    if (!this._storage) {\r\n      console.warn('Storage was not initialized. Creating now...');\r\n      this._storage = await this.storage.create();\r\n    }\r\n    return await this._storage?.get('jwt');\r\n  }\r\n\r\n\r\n  // Pegar o Id do usuário\r\n  async getUserId() {\r\n    if (!this._storage) {\r\n      console.warn('Storage was not initialized. Creating now...');\r\n      this._storage = await this.storage.create();\r\n    }\r\n    \r\n      const value = await this._storage?.get('userId') ?? null;\r\n      console.log('Fetched userId:', value);\r\n      return value;\r\n  }\r\n\r\n  // Pegar o Nome do usuário\r\n  async getUserName() {\r\n    if (!this._storage) {\r\n      console.warn('Storage was not initialized. Creating now...');\r\n      this._storage = await this.storage.create();\r\n    }\r\n    const value = await this._storage?.get('userName');\r\n    console.log('Fetched userName:', value);\r\n    return value;\r\n  }\r\n\r\n    // Pegar o email do usuário\r\n    async getUserEmail() {\r\n      if (!this._storage) {\r\n        console.warn('Storage was not initialized. Creating now...');\r\n        this._storage = await this.storage.create();\r\n      }\r\n        return await this._storage?.get('userEmail');\r\n      }\r\n\r\n  // Remover sessão\r\n  async clearSession() {\r\n    if (!this._storage) {\r\n      console.warn('Storage was not initialized. Creating now...');\r\n      this._storage = await this.storage.create();\r\n    }\r\n    await this._storage?.remove('jwt');\r\n    await this._storage?.remove('userId');\r\n    await this._storage?.remove('userName');\r\n    await this._storage?.remove('userEmail');\r\n  }\r\n}\r\n", "import { Injectable } from '@angular/core';\r\nimport { ToastController } from '@ionic/angular';\r\n\r\n@Injectable({\r\n  providedIn: 'root' //reutilizavél em qualquer parte do app\r\n})\r\nexport class ToastService {\r\n\r\n  constructor(private toastController: ToastController) {}\r\n\r\n  async showToast(message: string, type: 'success' | 'danger' | 'warning' | 'info' = 'success') {\r\n    const toast = await this.toastController.create({\r\n      message,\r\n      duration: 3000,\r\n      position: 'bottom',\r\n      color: type,\r\n      buttons: [\r\n        {\r\n          side: 'end',\r\n          icon: 'close',\r\n          role: 'cancel'\r\n        }\r\n      ]\r\n    });\r\n    await toast.present();\r\n  }\r\n}\r\n", "/**\r\n * Enum representing the synchronization status of a record\r\n */\r\nexport enum SyncStatus {\r\n    /** Record exists only locally and needs to be created on the server */\r\n    PendingCreate = 'PENDING_CREATE',\r\n  \r\n    /** Record exists locally and on the server but has local changes that need to be sent to the server */\r\n    PendingUpdate = 'PENDING_UPDATE',\r\n  \r\n    /** Record exists locally and on the server but needs to be deleted on the server */\r\n    PendingDelete = 'PENDING_DELETE',\r\n  \r\n    /** Record is synchronized with the server */\r\n    Synced = 'SYNCED',\r\n  \r\n    /** Record failed to synchronize with the server */\r\n    Failed = 'FAILED'\r\n  }", "import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable } from 'rxjs';\nimport { Vehicle } from '../models/vehicle.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class VehicleStateService {\n  // BehaviorSubject to track the primary vehicle\n  private primaryVehicleSubject = new BehaviorSubject<Vehicle | null>(null);\n  \n  // Observable that components can subscribe to\n  public primaryVehicle$: Observable<Vehicle | null> = this.primaryVehicleSubject.asObservable();\n  \n  // BehaviorSubject to track all vehicles\n  private vehiclesSubject = new BehaviorSubject<Vehicle[]>([]);\n  \n  // Observable that components can subscribe to\n  public vehicles$: Observable<Vehicle[]> = this.vehiclesSubject.asObservable();\n  \n  constructor() {}\n  \n  /**\n   * Updates the primary vehicle\n   * @param vehicle The new primary vehicle\n   */\n  updatePrimaryVehicle(vehicle: Vehicle | null): void {\n    this.primaryVehicleSubject.next(vehicle);\n  }\n  \n  /**\n   * Updates the list of vehicles\n   * @param vehicles The new list of vehicles\n   */\n  updateVehicles(vehicles: Vehicle[]): void {\n    this.vehiclesSubject.next(vehicles);\n    \n    // Also update the primary vehicle if it exists in the list\n    const primaryVehicle = vehicles.find(v => v.isPrimary);\n    if (primaryVehicle) {\n      this.updatePrimaryVehicle(primaryVehicle);\n    }\n  }\n  \n  /**\n   * Gets the current primary vehicle\n   * @returns The current primary vehicle or null\n   */\n  getCurrentPrimaryVehicle(): Vehicle | null {\n    return this.primaryVehicleSubject.getValue();\n  }\n  \n  /**\n   * Gets the current list of vehicles\n   * @returns The current list of vehicles\n   */\n  getCurrentVehicles(): Vehicle[] {\n    return this.vehiclesSubject.getValue();\n  }\n}\n", "import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable, fromEvent, merge, of } from 'rxjs';\nimport { map } from 'rxjs/operators';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class NetworkService {\n  private online$: Observable<boolean>;\n  private isOnline = new BehaviorSubject<boolean>(navigator.onLine);\n\n  constructor() {\n    // Create an observable from the window's online and offline events\n    this.online$ = merge(\n      of(navigator.onLine),\n      fromEvent(window, 'online').pipe(map(() => true)),\n      fromEvent(window, 'offline').pipe(map(() => false))\n    );\n\n    // Subscribe to the online$ observable and update the BehaviorSubject\n    this.online$.subscribe(isOnline => {\n      this.isOnline.next(isOnline);\n    });\n  }\n\n  /**\n   * Returns an observable that emits the current online status\n   * and continues to emit when the status changes\n   */\n  public getOnlineStatus(): Observable<boolean> {\n    return this.isOnline.asObservable();\n  }\n\n  /**\n   * Returns the current online status\n   */\n  public isOnlineNow(): boolean {\n    return navigator.onLine;\n  }\n}\n", "// This file can be replaced during build by using the `fileReplacements` array.\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\n// The list of file replacements can be found in `angular.json`.\n\nexport const environment = {\n  production: false,\n  apiUrl: 'http://************:5026'\n  // apiUrl: 'https://************:7226'\n};\n\n/*\n * For easier debugging in development mode, you can import the following file\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\n *\n * This import should be commented out in production mode because it will have a negative impact\n * on performance if an error is thrown.\n */\n// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.\n", "import { Injectable } from '@angular/core';\nimport { environment } from 'src/environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ApiService {\n  private baseUrl = environment.apiUrl;\n  \n  // Mapeamento de endpoints antigos para novos\n  private endpointMap = {\n    // Usuários\n    'create': 'api/user/create',\n    'Authentication': 'api/user/authentication',\n    \n    // Veículos\n    'vehicles/createVehicle': 'api/vehicle/create',\n    'vehicles/list': 'api/vehicle/list',\n    \n    // Marcas\n    'brands': 'api/brand/list',\n    \n    // Modelos\n    'models': 'api/model/list',\n    \n    // Jornadas\n    'journeys/create': 'api/journey/create',\n    'journeys/list': 'api/journey/list',\n    'journeys/details': 'api/journey/details'\n  };\n\n  constructor() { }\n\n  /**\n   * Obtém a URL completa para um endpoint específico\n   * @param endpoint O endpoint antigo ou novo\n   * @param queryParams Parâmetros de consulta opcionais\n   * @returns A URL completa para o endpoint\n   */\n  getUrl(endpoint: string, queryParams?: Record<string, string | number>): string {\n    // Verifica se o endpoint já está no novo formato (começa com 'api/')\n    const mappedEndpoint = endpoint.startsWith('api/') \n      ? endpoint \n      : (this.endpointMap as { [key: string]: string })[endpoint] || endpoint;\n    \n    let url = `${this.baseUrl}/${mappedEndpoint}`;\n    \n    // Adiciona parâmetros de consulta, se houver\n    if (queryParams && Object.keys(queryParams).length > 0) {\n      const params = new URLSearchParams();\n      Object.entries(queryParams).forEach(([key, value]) => {\n        params.append(key, value.toString());\n      });\n      url += `?${params.toString()}`;\n    }\n    \n    return url;\n  }\n}\n\n", "import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { VehicleCreateRequestDto } from '../dtos/vehicle/VehicleCreateRequestDto';\r\nimport { SyncStatus } from '../models/sync.model';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport { SessionService } from './session.service';\r\nimport { DataStorageService } from './data-storage-service';\r\nimport { ApiService } from './api.service';\r\nimport { VehicleStateService } from './vehicle-state.service';\r\nimport { NetworkService } from './network.service';\r\nimport { Vehicle } from '../models/vehicle.model';\r\nimport { VehicleSyncRequestDto } from '../dtos/vehicle/vehicleSyncRequestDto';\r\n\r\n\r\n@Injectable({\r\n    providedIn: 'root'\r\n  })\r\nexport class VehicleService\r\n{\r\n    private readonly tableName = 'vehicles';\r\n    constructor(\r\n      private http: HttpClient,\r\n      private dataStorageService: DataStorageService,\r\n      private sessionService: SessionService,\r\n      private apiService: ApiService,\r\n      private vehicleStateService: VehicleStateService,\r\n      private networkService: NetworkService\r\n    ) {}\r\n\r\n    async create(data: VehicleCreateRequestDto): Promise<any> {\r\n      try {\r\n        const url = this.apiService.getUrl('vehicles/createVehicle');\r\n        const result = await this.http.post<any>(url, data).toPromise();\r\n        return result;\r\n      } catch (error) {\r\n        console.error('Error creating vehicle:', error);\r\n        throw error;\r\n      }\r\n    }\r\n\r\n    async createLocal(data: VehicleCreateRequestDto): Promise<any> {\r\n      try {\r\n        // Create a timestamp for createdOn\r\n        const createdOn = new Date().toISOString();\r\n\r\n        // Check if this is the first vehicle for the user\r\n        const existingVehicles = await this.listAllLocal(data.userId);\r\n        const isPrimary = existingVehicles.length === 0 ? 1 : 0; // Set as primary if it's the first vehicle\r\n\r\n        // Determine sync status based on network connectivity\r\n        const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingCreate : SyncStatus.PendingCreate;\r\nconsole.log('Vai Cadastrar veiculo: ', JSON.stringify(data));\r\n        await this.dataStorageService.insert(this.tableName, {\r\n          id: data.vehicleId,\r\n          userId: data.userId,\r\n          plate: data.plate,\r\n          year: data.year,\r\n          brandId: data.brandId,\r\n          brandName: data.brandName,\r\n          modelId: data.modelId,\r\n          modelName: data.modelName,\r\n          version: data.version,\r\n          policyNumber: data.policyNumber,\r\n          isPrimary: isPrimary,\r\n          createdOn: createdOn,\r\n          syncStatus: syncStatus,\r\n          lastSyncDate: null\r\n        });\r\n        console.log('Cadastrou veiculo: '+ JSON.stringify(data));\r\n        return { id: data.vehicleId, isPrimary: isPrimary === 1, syncStatus: syncStatus };\r\n      } catch (error) {\r\n        console.error('Error creating vehicle:', error);\r\n        throw error;\r\n      }\r\n    }\r\n\r\n    async listAll(userId: string): Promise<Vehicle[]> {\r\n      try {\r\n        const url = this.apiService.getUrl('vehicles/list', { userId });\r\n        const result = await this.http.get<Vehicle[]>(url).toPromise();\r\n\r\n        if (!result) return [];\r\n\r\n        return result;\r\n      } catch (error) {\r\n        console.error('Error fetching vehicles:', error);\r\n        return [];\r\n      }\r\n    }\r\n\r\n    async listAllLocal(userId: string): Promise<Vehicle[]> {\r\n      // Buscar dados do usuário no Local Storage\r\n      let result = await this.dataStorageService.select(this.tableName, '');\r\n\r\n      if (!Array.isArray(result)) {\r\n        return [];\r\n      }\r\n\r\n      result = result.filter((data: any) => data.userId === userId);\r\n      console.log('result.length vehicles for user:', result.length);\r\n      if(!result || result.length === 0)\r\n        return [];\r\n\r\n      const resultVehicles: Vehicle[] = result.map((data: any) => {\r\n        return {\r\n          id: data.id,\r\n          brandId: data.brandId,\r\n          brandName: data.brandName,\r\n          modelId: data.modelId,\r\n          modelName: data.modelName,\r\n          userId: data.userId,\r\n          plate: data.plate,\r\n          year: data.year,\r\n          version: data.version,\r\n          policyNumber: data.policyNumber,\r\n          isPrimary: data.isPrimary === 1 || data.isPrimary === true,\r\n          createdOn: data.createdOn,\r\n          updatedOn: data.updatedOn,\r\n          syncStatus: data.syncStatus || SyncStatus.Synced,\r\n          lastSyncDate: data.lastSyncDate ? new Date(data.lastSyncDate) : undefined\r\n        };\r\n      });\r\n\r\n      // Update the vehicle state service with the latest vehicles\r\n      this.vehicleStateService.updateVehicles(resultVehicles);\r\n\r\n      return resultVehicles;\r\n    }\r\n\r\n    /**\r\n     * Sets a vehicle as the primary vehicle for a user\r\n     * @param vehicleId The ID of the vehicle to set as primary\r\n     * @param userId The user ID\r\n     */\r\n    async setPrimaryVehicle(vehicleId: string, userId: string): Promise<boolean> {\r\n      try {\r\n        // First, unset any existing primary vehicle\r\n        const vehicles = await this.listAllLocal(userId);\r\n\r\n        // Update all vehicles to not be primary\r\n        for (const vehicle of vehicles) {\r\n          if (vehicle.isPrimary) {\r\n            // Get the full vehicle object\r\n            const vehicleToUpdate = { ...vehicle, isPrimary: 0 };\r\n\r\n            // Update the vehicle\r\n            await this.dataStorageService.update(\r\n              this.tableName,\r\n              vehicleToUpdate,\r\n              `id = '${vehicle.id}'`\r\n            );\r\n          }\r\n        }\r\n\r\n        // Get the vehicle to set as primary\r\n        const primaryVehicle = vehicles.find(v => v.id === vehicleId);\r\n\r\n        if (primaryVehicle) {\r\n          // Update the vehicle with isPrimary = 1\r\n          const updatedVehicle = { ...primaryVehicle, isPrimary: true };\r\n\r\n          // Update the vehicle in the database (convert boolean to number for storage)\r\n          const vehicleForDb = { ...updatedVehicle, isPrimary: 1 };\r\n          await this.dataStorageService.update(\r\n            this.tableName,\r\n            vehicleForDb,\r\n            `id = '${vehicleId}'`\r\n          );\r\n\r\n          // Refresh the vehicles list to update the state\r\n          await this.listAllLocal(userId);\r\n\r\n          // Update the primary vehicle in the state service\r\n          this.vehicleStateService.updatePrimaryVehicle(updatedVehicle);\r\n\r\n          return true;\r\n        } else {\r\n          console.error('Vehicle not found:', vehicleId);\r\n          return false;\r\n        }\r\n      } catch (error) {\r\n        console.error('Error setting primary vehicle:', error);\r\n        return false;\r\n      }\r\n    }\r\n\r\n    /**\r\n     * Gets the primary vehicle for a user\r\n     * @param userId The user ID\r\n     * @returns The primary vehicle or null if none exists\r\n     */\r\n    async getPrimaryVehicle(userId: string): Promise<Vehicle | null> {\r\n      try {\r\n        const vehicles = await this.listAllLocal(userId);\r\n\r\n        // Find the primary vehicle\r\n        const primaryVehicle = vehicles.find(v => v.isPrimary);\r\n\r\n        // If no primary vehicle is set but vehicles exist, set the first one as primary\r\n        if (!primaryVehicle && vehicles.length > 0) {\r\n          await this.setPrimaryVehicle(vehicles[0].id, userId);\r\n\r\n          // Get the updated vehicle with isPrimary set to true\r\n          const updatedVehicle = { ...vehicles[0], isPrimary: true };\r\n\r\n          // Update the state service\r\n          this.vehicleStateService.updatePrimaryVehicle(updatedVehicle);\r\n\r\n          return updatedVehicle;\r\n        }\r\n\r\n        // Update the state service with the primary vehicle\r\n        if (primaryVehicle) {\r\n          this.vehicleStateService.updatePrimaryVehicle(primaryVehicle);\r\n        }\r\n\r\n        return primaryVehicle || null;\r\n      } catch (error) {\r\n        console.error('Error getting primary vehicle:', error);\r\n        return null;\r\n      }\r\n    }\r\n\r\n    /**\r\n     * Checks if the user has any vehicles\r\n     * @param userId The user ID\r\n     * @returns True if the user has at least one vehicle\r\n     */\r\n    async hasVehicles(userId: string): Promise<boolean> {\r\n      const vehicles = await this.listAllLocal(userId);\r\n      return vehicles.length > 0;\r\n    }\r\n\r\n    async sendVehicleToSync(vehicle: VehicleSyncRequestDto): Promise<boolean> {\r\n      try {\r\n        const url = this.apiService.getUrl('vehicles/createVehicle');\r\n        const result = await this.http.post<any>(url, vehicle).toPromise();\r\n        return result;\r\n      } catch (error) {\r\n        console.error('Error sending vehicle to sync:', error);\r\n        throw error;\r\n      }\r\n    }\r\n\r\n    /**\r\n     * Updates the sync status of a vehicle\r\n     * @param vehicle The vehicle to update\r\n     * @returns True if the update was successful\r\n     */\r\n    async updateVehicleSync(vehicle: Vehicle): Promise<boolean> {\r\n      try {\r\n        // Convert boolean isPrimary to number for storage\r\n        const vehicleForDb = {\r\n          ...vehicle,\r\n          isPrimary: vehicle.isPrimary ? 1 : 0,\r\n          lastSyncDate: vehicle.lastSyncDate ? vehicle.lastSyncDate.toISOString() : null\r\n        };\r\n\r\n        await this.dataStorageService.update(\r\n          this.tableName,\r\n          vehicleForDb,\r\n          `id = '${vehicle.id}'`\r\n        );\r\n\r\n        // Refresh the vehicles list to update the state\r\n        if (vehicle.userId) {\r\n          await this.listAllLocal(vehicle.userId);\r\n        }\r\n\r\n        return true;\r\n      } catch (error) {\r\n        console.error('Error updating vehicle sync status:', error);\r\n        return false;\r\n      }\r\n    }\r\n\r\n    /**\r\n     * Gets vehicles that need to be synchronized\r\n     * @param userId The user ID\r\n     * @returns Array of vehicles that need to be synchronized\r\n     */\r\n    async getPendingSyncVehicles(userId: string): Promise<Vehicle[]> {\r\n      const vehicles = await this.listAllLocal(userId);\r\n      return vehicles.filter(v =>\r\n        v.syncStatus === SyncStatus.PendingCreate ||\r\n        v.syncStatus === SyncStatus.PendingUpdate ||\r\n        v.syncStatus === SyncStatus.PendingDelete\r\n      );\r\n    }\r\n\r\n    /**\r\n     * Busca um veículo pelo id e userId\r\n     */\r\n    async getById(id: string): Promise<Vehicle | null> {\r\n      const userId = await this.sessionService.getUserId() || '';\r\n      const vehicles = await this.listAllLocal(userId);\r\n      return vehicles.find(v => v.id === id) || null;\r\n    }\r\n\r\n    /**\r\n     * Atualiza um veículo pelo id\r\n     */\r\n    async update(id: string, updatedVehicle: Vehicle): Promise<boolean> {\r\n      try {\r\n        // Convert boolean isPrimary to number for storage\r\n        const vehicleForDb = {\r\n          ...updatedVehicle,\r\n          isPrimary: updatedVehicle.isPrimary ? 1 : 0,\r\n          lastSyncDate: updatedVehicle.lastSyncDate ? (updatedVehicle.lastSyncDate instanceof Date ? updatedVehicle.lastSyncDate.toISOString() : updatedVehicle.lastSyncDate) : null\r\n        };\r\n        await this.dataStorageService.update(\r\n          this.tableName,\r\n          vehicleForDb,\r\n          `id = '${id}'`\r\n        );\r\n        // Atualiza o estado\r\n        if (updatedVehicle.userId) {\r\n          await this.listAllLocal(updatedVehicle.userId);\r\n        }\r\n        return true;\r\n      } catch (error) {\r\n        console.error('Erro ao atualizar veículo:', error);\r\n        return false;\r\n      }\r\n    }\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAMA,KAAC,SAAU,GAAG;AACZ,UAAI,OAAO,YAAY,YAAY,OAAO,WAAW,aAAa;AAChE,eAAO,UAAU,EAAE;AAAA,MACrB,WAAW,OAAO,WAAW,cAAc,OAAO,KAAK;AACrD,eAAO,CAAC,GAAG,CAAC;AAAA,MACd,OAAO;AACL,YAAI;AACJ,YAAI,OAAO,WAAW,aAAa;AACjC,cAAI;AAAA,QACN,WAAW,OAAO,WAAW,aAAa;AACxC,cAAI;AAAA,QACN,WAAW,OAAO,SAAS,aAAa;AACtC,cAAI;AAAA,QACN,OAAO;AACL,cAAI;AAAA,QACN;AACA,UAAE,cAAc,EAAE;AAAA,MACpB;AAAA,IACF,GAAG,WAAY;AACb,UAAIA,SAAQC,SAAQC;AACpB,aAAO,SAAS,EAAE,GAAG,GAAG,GAAG;AACzB,iBAAS,EAAEC,IAAG,GAAG;AACf,cAAI,CAAC,EAAEA,EAAC,GAAG;AACT,gBAAI,CAAC,EAAEA,EAAC,GAAG;AACT,kBAAI,IAAI,OAAO,aAAW,cAAc;AACxC,kBAAI,CAAC,KAAK,EAAG,QAAO,EAAEA,IAAG,IAAE;AAC3B,kBAAI,EAAG,QAAO,EAAEA,IAAG,IAAE;AACrB,kBAAI,IAAI,IAAI,MAAM,yBAAyBA,KAAI,GAAG;AAClD,oBAAM,EAAE,OAAO,oBAAoB;AAAA,YACrC;AACA,gBAAI,IAAI,EAAEA,EAAC,IAAI;AAAA,cACb,SAAS,CAAC;AAAA,YACZ;AACA,cAAEA,EAAC,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,SAAUC,IAAG;AACnC,kBAAIC,KAAI,EAAEF,EAAC,EAAE,CAAC,EAAEC,EAAC;AACjB,qBAAO,EAAEC,KAAIA,KAAID,EAAC;AAAA,YACpB,GAAG,GAAG,EAAE,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,UAC7B;AACA,iBAAO,EAAED,EAAC,EAAE;AAAA,QACd;AACA,YAAI,IAAI,OAAO,aAAW,cAAc;AACxC,iBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAK,GAAE,EAAE,CAAC,CAAC;AACzC,eAAO;AAAA,MACT,EAAE;AAAA,QACA,GAAG,CAAC,SAAU,SAASF,SAAQC,UAAS;AACtC,WAAC,SAAUI,SAAQ;AACjB;AAEA,gBAAI,WAAWA,QAAO,oBAAoBA,QAAO;AACjD,gBAAI;AACJ;AACE,kBAAI,UAAU;AACZ,oBAAI,SAAS;AACb,oBAAI,WAAW,IAAI,SAAS,QAAQ;AACpC,oBAAI,UAAUA,QAAO,SAAS,eAAe,EAAE;AAC/C,yBAAS,QAAQ,SAAS;AAAA,kBACxB,eAAe;AAAA,gBACjB,CAAC;AACD,gCAAgB,WAAY;AAC1B,0BAAQ,OAAO,SAAS,EAAE,SAAS;AAAA,gBACrC;AAAA,cACF,WAAW,CAACA,QAAO,gBAAgB,OAAOA,QAAO,mBAAmB,aAAa;AAC/E,oBAAI,UAAU,IAAIA,QAAO,eAAe;AACxC,wBAAQ,MAAM,YAAY;AAC1B,gCAAgB,WAAY;AAC1B,0BAAQ,MAAM,YAAY,CAAC;AAAA,gBAC7B;AAAA,cACF,WAAW,cAAcA,WAAU,wBAAwBA,QAAO,SAAS,cAAc,QAAQ,GAAG;AAClG,gCAAgB,WAAY;AAG1B,sBAAI,WAAWA,QAAO,SAAS,cAAc,QAAQ;AACrD,2BAAS,qBAAqB,WAAY;AACxC,6BAAS;AACT,6BAAS,qBAAqB;AAC9B,6BAAS,WAAW,YAAY,QAAQ;AACxC,+BAAW;AAAA,kBACb;AACA,kBAAAA,QAAO,SAAS,gBAAgB,YAAY,QAAQ;AAAA,gBACtD;AAAA,cACF,OAAO;AACL,gCAAgB,WAAY;AAC1B,6BAAW,UAAU,CAAC;AAAA,gBACxB;AAAA,cACF;AAAA,YACF;AACA,gBAAI;AACJ,gBAAI,QAAQ,CAAC;AAEb,qBAAS,WAAW;AAClB,yBAAW;AACX,kBAAI,GAAG;AACP,kBAAI,MAAM,MAAM;AAChB,qBAAO,KAAK;AACV,2BAAW;AACX,wBAAQ,CAAC;AACT,oBAAI;AACJ,uBAAO,EAAE,IAAI,KAAK;AAChB,2BAAS,CAAC,EAAE;AAAA,gBACd;AACA,sBAAM,MAAM;AAAA,cACd;AACA,yBAAW;AAAA,YACb;AACA,YAAAL,QAAO,UAAU;AACjB,qBAAS,UAAU,MAAM;AACvB,kBAAI,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,UAAU;AACvC,8BAAc;AAAA,cAChB;AAAA,YACF;AAAA,UACF,GAAG,KAAK,MAAM,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,OAAO,WAAW,cAAc,SAAS,CAAC,CAAC;AAAA,QACzI,GAAG,CAAC,CAAC;AAAA,QACL,GAAG,CAAC,SAAU,SAASA,SAAQC,UAAS;AACtC;AAEA,cAAI,YAAY,QAAQ,CAAC;AAGzB,mBAAS,WAAW;AAAA,UAAC;AACrB,cAAI,WAAW,CAAC;AAChB,cAAI,WAAW,CAAC,UAAU;AAC1B,cAAI,YAAY,CAAC,WAAW;AAC5B,cAAI,UAAU,CAAC,SAAS;AACxB,UAAAD,QAAO,UAAUM;AACjB,mBAASA,SAAQ,UAAU;AACzB,gBAAI,OAAO,aAAa,YAAY;AAClC,oBAAM,IAAI,UAAU,6BAA6B;AAAA,YACnD;AACA,iBAAK,QAAQ;AACb,iBAAK,QAAQ,CAAC;AACd,iBAAK,UAAU;AACf,gBAAI,aAAa,UAAU;AACzB,oCAAsB,MAAM,QAAQ;AAAA,YACtC;AAAA,UACF;AACA,UAAAA,SAAQ,UAAU,OAAO,IAAI,SAAU,YAAY;AACjD,mBAAO,KAAK,KAAK,MAAM,UAAU;AAAA,UACnC;AACA,UAAAA,SAAQ,UAAU,OAAO,SAAU,aAAa,YAAY;AAC1D,gBAAI,OAAO,gBAAgB,cAAc,KAAK,UAAU,aAAa,OAAO,eAAe,cAAc,KAAK,UAAU,UAAU;AAChI,qBAAO;AAAA,YACT;AACA,gBAAI,UAAU,IAAI,KAAK,YAAY,QAAQ;AAC3C,gBAAI,KAAK,UAAU,SAAS;AAC1B,kBAAI,WAAW,KAAK,UAAU,YAAY,cAAc;AACxD,qBAAO,SAAS,UAAU,KAAK,OAAO;AAAA,YACxC,OAAO;AACL,mBAAK,MAAM,KAAK,IAAI,UAAU,SAAS,aAAa,UAAU,CAAC;AAAA,YACjE;AACA,mBAAO;AAAA,UACT;AACA,mBAAS,UAAU,SAAS,aAAa,YAAY;AACnD,iBAAK,UAAU;AACf,gBAAI,OAAO,gBAAgB,YAAY;AACrC,mBAAK,cAAc;AACnB,mBAAK,gBAAgB,KAAK;AAAA,YAC5B;AACA,gBAAI,OAAO,eAAe,YAAY;AACpC,mBAAK,aAAa;AAClB,mBAAK,eAAe,KAAK;AAAA,YAC3B;AAAA,UACF;AACA,oBAAU,UAAU,gBAAgB,SAAU,OAAO;AACnD,qBAAS,QAAQ,KAAK,SAAS,KAAK;AAAA,UACtC;AACA,oBAAU,UAAU,qBAAqB,SAAU,OAAO;AACxD,mBAAO,KAAK,SAAS,KAAK,aAAa,KAAK;AAAA,UAC9C;AACA,oBAAU,UAAU,eAAe,SAAU,OAAO;AAClD,qBAAS,OAAO,KAAK,SAAS,KAAK;AAAA,UACrC;AACA,oBAAU,UAAU,oBAAoB,SAAU,OAAO;AACvD,mBAAO,KAAK,SAAS,KAAK,YAAY,KAAK;AAAA,UAC7C;AACA,mBAAS,OAAO,SAAS,MAAM,OAAO;AACpC,sBAAU,WAAY;AACpB,kBAAI;AACJ,kBAAI;AACF,8BAAc,KAAK,KAAK;AAAA,cAC1B,SAAS,GAAG;AACV,uBAAO,SAAS,OAAO,SAAS,CAAC;AAAA,cACnC;AACA,kBAAI,gBAAgB,SAAS;AAC3B,yBAAS,OAAO,SAAS,IAAI,UAAU,oCAAoC,CAAC;AAAA,cAC9E,OAAO;AACL,yBAAS,QAAQ,SAAS,WAAW;AAAA,cACvC;AAAA,YACF,CAAC;AAAA,UACH;AACA,mBAAS,UAAU,SAAUC,OAAM,OAAO;AACxC,gBAAI,SAAS,SAAS,SAAS,KAAK;AACpC,gBAAI,OAAO,WAAW,SAAS;AAC7B,qBAAO,SAAS,OAAOA,OAAM,OAAO,KAAK;AAAA,YAC3C;AACA,gBAAI,WAAW,OAAO;AACtB,gBAAI,UAAU;AACZ,oCAAsBA,OAAM,QAAQ;AAAA,YACtC,OAAO;AACL,cAAAA,MAAK,QAAQ;AACb,cAAAA,MAAK,UAAU;AACf,kBAAI,IAAI;AACR,kBAAI,MAAMA,MAAK,MAAM;AACrB,qBAAO,EAAE,IAAI,KAAK;AAChB,gBAAAA,MAAK,MAAM,CAAC,EAAE,cAAc,KAAK;AAAA,cACnC;AAAA,YACF;AACA,mBAAOA;AAAA,UACT;AACA,mBAAS,SAAS,SAAUA,OAAM,OAAO;AACvC,YAAAA,MAAK,QAAQ;AACb,YAAAA,MAAK,UAAU;AACf,gBAAI,IAAI;AACR,gBAAI,MAAMA,MAAK,MAAM;AACrB,mBAAO,EAAE,IAAI,KAAK;AAChB,cAAAA,MAAK,MAAM,CAAC,EAAE,aAAa,KAAK;AAAA,YAClC;AACA,mBAAOA;AAAA,UACT;AACA,mBAAS,QAAQ,KAAK;AAEpB,gBAAI,OAAO,OAAO,IAAI;AACtB,gBAAI,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,eAAe,OAAO,SAAS,YAAY;AAC/F,qBAAO,SAAS,WAAW;AACzB,qBAAK,MAAM,KAAK,SAAS;AAAA,cAC3B;AAAA,YACF;AAAA,UACF;AACA,mBAAS,sBAAsBA,OAAM,UAAU;AAE7C,gBAAI,SAAS;AACb,qBAAS,QAAQ,OAAO;AACtB,kBAAI,QAAQ;AACV;AAAA,cACF;AACA,uBAAS;AACT,uBAAS,OAAOA,OAAM,KAAK;AAAA,YAC7B;AACA,qBAAS,UAAU,OAAO;AACxB,kBAAI,QAAQ;AACV;AAAA,cACF;AACA,uBAAS;AACT,uBAAS,QAAQA,OAAM,KAAK;AAAA,YAC9B;AACA,qBAAS,cAAc;AACrB,uBAAS,WAAW,OAAO;AAAA,YAC7B;AACA,gBAAI,SAAS,SAAS,WAAW;AACjC,gBAAI,OAAO,WAAW,SAAS;AAC7B,sBAAQ,OAAO,KAAK;AAAA,YACtB;AAAA,UACF;AACA,mBAAS,SAAS,MAAM,OAAO;AAC7B,gBAAI,MAAM,CAAC;AACX,gBAAI;AACF,kBAAI,QAAQ,KAAK,KAAK;AACtB,kBAAI,SAAS;AAAA,YACf,SAAS,GAAG;AACV,kBAAI,SAAS;AACb,kBAAI,QAAQ;AAAA,YACd;AACA,mBAAO;AAAA,UACT;AACA,UAAAD,SAAQ,UAAU;AAClB,mBAAS,QAAQ,OAAO;AACtB,gBAAI,iBAAiB,MAAM;AACzB,qBAAO;AAAA,YACT;AACA,mBAAO,SAAS,QAAQ,IAAI,KAAK,QAAQ,GAAG,KAAK;AAAA,UACnD;AACA,UAAAA,SAAQ,SAAS;AACjB,mBAAS,OAAO,QAAQ;AACtB,gBAAI,UAAU,IAAI,KAAK,QAAQ;AAC/B,mBAAO,SAAS,OAAO,SAAS,MAAM;AAAA,UACxC;AACA,UAAAA,SAAQ,MAAM;AACd,mBAAS,IAAI,UAAU;AACrB,gBAAIC,QAAO;AACX,gBAAI,OAAO,UAAU,SAAS,KAAK,QAAQ,MAAM,kBAAkB;AACjE,qBAAO,KAAK,OAAO,IAAI,UAAU,kBAAkB,CAAC;AAAA,YACtD;AACA,gBAAI,MAAM,SAAS;AACnB,gBAAI,SAAS;AACb,gBAAI,CAAC,KAAK;AACR,qBAAO,KAAK,QAAQ,CAAC,CAAC;AAAA,YACxB;AACA,gBAAI,SAAS,IAAI,MAAM,GAAG;AAC1B,gBAAI,WAAW;AACf,gBAAI,IAAI;AACR,gBAAI,UAAU,IAAI,KAAK,QAAQ;AAC/B,mBAAO,EAAE,IAAI,KAAK;AAChB,0BAAY,SAAS,CAAC,GAAG,CAAC;AAAA,YAC5B;AACA,mBAAO;AACP,qBAAS,YAAY,OAAOC,IAAG;AAC7B,cAAAD,MAAK,QAAQ,KAAK,EAAE,KAAK,gBAAgB,SAAU,OAAO;AACxD,oBAAI,CAAC,QAAQ;AACX,2BAAS;AACT,2BAAS,OAAO,SAAS,KAAK;AAAA,gBAChC;AAAA,cACF,CAAC;AACD,uBAAS,eAAe,UAAU;AAChC,uBAAOC,EAAC,IAAI;AACZ,oBAAI,EAAE,aAAa,OAAO,CAAC,QAAQ;AACjC,2BAAS;AACT,2BAAS,QAAQ,SAAS,MAAM;AAAA,gBAClC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,UAAAF,SAAQ,OAAO;AACf,mBAAS,KAAK,UAAU;AACtB,gBAAIC,QAAO;AACX,gBAAI,OAAO,UAAU,SAAS,KAAK,QAAQ,MAAM,kBAAkB;AACjE,qBAAO,KAAK,OAAO,IAAI,UAAU,kBAAkB,CAAC;AAAA,YACtD;AACA,gBAAI,MAAM,SAAS;AACnB,gBAAI,SAAS;AACb,gBAAI,CAAC,KAAK;AACR,qBAAO,KAAK,QAAQ,CAAC,CAAC;AAAA,YACxB;AACA,gBAAI,IAAI;AACR,gBAAI,UAAU,IAAI,KAAK,QAAQ;AAC/B,mBAAO,EAAE,IAAI,KAAK;AAChB,uBAAS,SAAS,CAAC,CAAC;AAAA,YACtB;AACA,mBAAO;AACP,qBAAS,SAAS,OAAO;AACvB,cAAAA,MAAK,QAAQ,KAAK,EAAE,KAAK,SAAU,UAAU;AAC3C,oBAAI,CAAC,QAAQ;AACX,2BAAS;AACT,2BAAS,QAAQ,SAAS,QAAQ;AAAA,gBACpC;AAAA,cACF,GAAG,SAAU,OAAO;AAClB,oBAAI,CAAC,QAAQ;AACX,2BAAS;AACT,2BAAS,OAAO,SAAS,KAAK;AAAA,gBAChC;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF,GAAG;AAAA,UACD,KAAK;AAAA,QACP,CAAC;AAAA,QACD,GAAG,CAAC,SAAU,SAASP,SAAQC,UAAS;AACtC,WAAC,SAAUI,SAAQ;AACjB;AAEA,gBAAI,OAAOA,QAAO,YAAY,YAAY;AACxC,cAAAA,QAAO,UAAU,QAAQ,CAAC;AAAA,YAC5B;AAAA,UACF,GAAG,KAAK,MAAM,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,OAAO,WAAW,cAAc,SAAS,CAAC,CAAC;AAAA,QACzI,GAAG;AAAA,UACD,KAAK;AAAA,QACP,CAAC;AAAA,QACD,GAAG,CAAC,SAAU,SAASL,SAAQC,UAAS;AACtC;AAEA,cAAI,UAAU,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,WAAW,SAAU,KAAK;AACjG,mBAAO,OAAO;AAAA,UAChB,IAAI,SAAU,KAAK;AACjB,mBAAO,OAAO,OAAO,WAAW,cAAc,IAAI,gBAAgB,UAAU,QAAQ,OAAO,YAAY,WAAW,OAAO;AAAA,UAC3H;AACA,mBAAS,gBAAgB,UAAU,aAAa;AAC9C,gBAAI,EAAE,oBAAoB,cAAc;AACtC,oBAAM,IAAI,UAAU,mCAAmC;AAAA,YACzD;AAAA,UACF;AACA,mBAAS,SAAS;AAEhB,gBAAI;AACF,kBAAI,OAAO,cAAc,aAAa;AACpC,uBAAO;AAAA,cACT;AACA,kBAAI,OAAO,oBAAoB,aAAa;AAC1C,uBAAO;AAAA,cACT;AACA,kBAAI,OAAO,iBAAiB,aAAa;AACvC,uBAAO;AAAA,cACT;AACA,kBAAI,OAAO,eAAe,aAAa;AACrC,uBAAO;AAAA,cACT;AACA,kBAAI,OAAO,gBAAgB,aAAa;AACtC,uBAAO;AAAA,cACT;AAAA,YACF,SAAS,GAAG;AACV;AAAA,YACF;AAAA,UACF;AACA,cAAI,MAAM,OAAO;AACjB,mBAAS,mBAAmB;AAC1B,gBAAI;AAGF,kBAAI,CAAC,OAAO,CAAC,IAAI,MAAM;AACrB,uBAAO;AAAA,cACT;AAKA,kBAAI,WAAW,OAAO,iBAAiB,eAAe,4BAA4B,KAAK,UAAU,SAAS,KAAK,CAAC,SAAS,KAAK,UAAU,SAAS,KAAK,CAAC,aAAa,KAAK,UAAU,QAAQ;AAC3L,kBAAI,WAAW,OAAO,UAAU,cAAc,MAAM,SAAS,EAAE,QAAQ,cAAc,MAAM;AAQ3F,sBAAQ,CAAC,YAAY,aAAa,OAAO,cAAc;AAAA;AAAA;AAAA;AAAA,cAKvD,OAAO,gBAAgB;AAAA,YACzB,SAAS,GAAG;AACV,qBAAO;AAAA,YACT;AAAA,UACF;AAQA,mBAAS,WAAW,OAAO,YAAY;AAErC,oBAAQ,SAAS,CAAC;AAClB,yBAAa,cAAc,CAAC;AAC5B,gBAAI;AACF,qBAAO,IAAI,KAAK,OAAO,UAAU;AAAA,YACnC,SAAS,GAAG;AACV,kBAAI,EAAE,SAAS,aAAa;AAC1B,sBAAM;AAAA,cACR;AACA,kBAAI,UAAU,OAAO,gBAAgB,cAAc,cAAc,OAAO,kBAAkB,cAAc,gBAAgB,OAAO,mBAAmB,cAAc,iBAAiB;AACjL,kBAAI,UAAU,IAAI,QAAQ;AAC1B,uBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,wBAAQ,OAAO,MAAM,CAAC,CAAC;AAAA,cACzB;AACA,qBAAO,QAAQ,QAAQ,WAAW,IAAI;AAAA,YACxC;AAAA,UACF;AAIA,cAAI,OAAO,YAAY,aAAa;AAGlC,oBAAQ,CAAC;AAAA,UACX;AACA,cAAI,YAAY;AAChB,mBAAS,gBAAgB,SAAS,UAAU;AAC1C,gBAAI,UAAU;AACZ,sBAAQ,KAAK,SAAU,QAAQ;AAC7B,yBAAS,MAAM,MAAM;AAAA,cACvB,GAAG,SAAU,OAAO;AAClB,yBAAS,KAAK;AAAA,cAChB,CAAC;AAAA,YACH;AAAA,UACF;AACA,mBAAS,oBAAoB,SAAS,UAAU,eAAe;AAC7D,gBAAI,OAAO,aAAa,YAAY;AAClC,sBAAQ,KAAK,QAAQ;AAAA,YACvB;AACA,gBAAI,OAAO,kBAAkB,YAAY;AACvC,sBAAQ,OAAO,EAAE,aAAa;AAAA,YAChC;AAAA,UACF;AACA,mBAAS,aAAaQ,MAAK;AAEzB,gBAAI,OAAOA,SAAQ,UAAU;AAC3B,sBAAQ,KAAKA,OAAM,yCAAyC;AAC5D,cAAAA,OAAM,OAAOA,IAAG;AAAA,YAClB;AACA,mBAAOA;AAAA,UACT;AACA,mBAAS,cAAc;AACrB,gBAAI,UAAU,UAAU,OAAO,UAAU,UAAU,SAAS,CAAC,MAAM,YAAY;AAC7E,qBAAO,UAAU,UAAU,SAAS,CAAC;AAAA,YACvC;AAAA,UACF;AAKA,cAAI,4BAA4B;AAChC,cAAI,gBAAgB;AACpB,cAAI,aAAa,CAAC;AAClB,cAAI,WAAW,OAAO,UAAU;AAGhC,cAAI,YAAY;AAChB,cAAI,aAAa;AAOjB,mBAAS,wBAAwB,KAAK;AACpC,gBAAIC,UAAS,IAAI;AACjB,gBAAI,MAAM,IAAI,YAAYA,OAAM;AAChC,gBAAI,MAAM,IAAI,WAAW,GAAG;AAC5B,qBAAS,IAAI,GAAG,IAAIA,SAAQ,KAAK;AAC/B,kBAAI,CAAC,IAAI,IAAI,WAAW,CAAC;AAAA,YAC3B;AACA,mBAAO;AAAA,UACT;AAiBA,mBAAS,gCAAgCC,MAAK;AAC5C,mBAAO,IAAI,UAAU,SAAU,SAAS;AACtC,kBAAI,MAAMA,KAAI,YAAY,2BAA2B,UAAU;AAC/D,kBAAI,OAAO,WAAW,CAAC,EAAE,CAAC;AAC1B,kBAAI,YAAY,yBAAyB,EAAE,IAAI,MAAM,KAAK;AAC1D,kBAAI,UAAU,SAAU,GAAG;AAGzB,kBAAE,eAAe;AACjB,kBAAE,gBAAgB;AAClB,wBAAQ,KAAK;AAAA,cACf;AACA,kBAAI,aAAa,WAAY;AAC3B,oBAAI,gBAAgB,UAAU,UAAU,MAAM,eAAe;AAC7D,oBAAI,cAAc,UAAU,UAAU,MAAM,QAAQ;AAGpD,wBAAQ,eAAe,CAAC,iBAAiB,SAAS,cAAc,CAAC,GAAG,EAAE,KAAK,EAAE;AAAA,cAC/E;AAAA,YACF,CAAC,EAAE,OAAO,EAAE,WAAY;AACtB,qBAAO;AAAA,YACT,CAAC;AAAA,UACH;AACA,mBAAS,kBAAkBA,MAAK;AAC9B,gBAAI,OAAO,kBAAkB,WAAW;AACtC,qBAAO,UAAU,QAAQ,aAAa;AAAA,YACxC;AACA,mBAAO,gCAAgCA,IAAG,EAAE,KAAK,SAAU,OAAO;AAChE,8BAAgB;AAChB,qBAAO;AAAA,YACT,CAAC;AAAA,UACH;AACA,mBAAS,gBAAgB,QAAQ;AAC/B,gBAAI,YAAY,WAAW,OAAO,IAAI;AAGtC,gBAAI,oBAAoB,CAAC;AACzB,8BAAkB,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACnE,gCAAkB,UAAU;AAC5B,gCAAkB,SAAS;AAAA,YAC7B,CAAC;AAGD,sBAAU,mBAAmB,KAAK,iBAAiB;AAGnD,gBAAI,CAAC,UAAU,SAAS;AACtB,wBAAU,UAAU,kBAAkB;AAAA,YACxC,OAAO;AACL,wBAAU,UAAU,UAAU,QAAQ,KAAK,WAAY;AACrD,uBAAO,kBAAkB;AAAA,cAC3B,CAAC;AAAA,YACH;AAAA,UACF;AACA,mBAAS,kBAAkB,QAAQ;AACjC,gBAAI,YAAY,WAAW,OAAO,IAAI;AAGtC,gBAAI,oBAAoB,UAAU,mBAAmB,IAAI;AAIzD,gBAAI,mBAAmB;AACrB,gCAAkB,QAAQ;AAC1B,qBAAO,kBAAkB;AAAA,YAC3B;AAAA,UACF;AACA,mBAAS,iBAAiB,QAAQ,KAAK;AACrC,gBAAI,YAAY,WAAW,OAAO,IAAI;AAGtC,gBAAI,oBAAoB,UAAU,mBAAmB,IAAI;AAIzD,gBAAI,mBAAmB;AACrB,gCAAkB,OAAO,GAAG;AAC5B,qBAAO,kBAAkB;AAAA,YAC3B;AAAA,UACF;AACA,mBAAS,eAAe,QAAQ,eAAe;AAC7C,mBAAO,IAAI,UAAU,SAAU,SAAS,QAAQ;AAC9C,yBAAW,OAAO,IAAI,IAAI,WAAW,OAAO,IAAI,KAAK,gBAAgB;AACrE,kBAAI,OAAO,IAAI;AACb,oBAAI,eAAe;AACjB,kCAAgB,MAAM;AACtB,yBAAO,GAAG,MAAM;AAAA,gBAClB,OAAO;AACL,yBAAO,QAAQ,OAAO,EAAE;AAAA,gBAC1B;AAAA,cACF;AACA,kBAAI,SAAS,CAAC,OAAO,IAAI;AACzB,kBAAI,eAAe;AACjB,uBAAO,KAAK,OAAO,OAAO;AAAA,cAC5B;AACA,kBAAI,UAAU,IAAI,KAAK,MAAM,KAAK,MAAM;AACxC,kBAAI,eAAe;AACjB,wBAAQ,kBAAkB,SAAU,GAAG;AACrC,sBAAI,KAAK,QAAQ;AACjB,sBAAI;AACF,uBAAG,kBAAkB,OAAO,SAAS;AACrC,wBAAI,EAAE,cAAc,GAAG;AAErB,yBAAG,kBAAkB,yBAAyB;AAAA,oBAChD;AAAA,kBACF,SAAS,IAAI;AACX,wBAAI,GAAG,SAAS,mBAAmB;AACjC,8BAAQ,KAAK,mBAAmB,OAAO,OAAO,sCAA2C,EAAE,aAAa,iBAAiB,EAAE,aAAa,wBAAwB,OAAO,YAAY,mBAAmB;AAAA,oBACxM,OAAO;AACL,4BAAM;AAAA,oBACR;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AACA,sBAAQ,UAAU,SAAU,GAAG;AAC7B,kBAAE,eAAe;AACjB,uBAAO,QAAQ,KAAK;AAAA,cACtB;AACA,sBAAQ,YAAY,WAAY;AAC9B,oBAAI,KAAK,QAAQ;AACjB,mBAAG,kBAAkB,SAAU,GAAG;AAMhC,oBAAE,OAAO,MAAM;AAAA,gBACjB;AACA,wBAAQ,EAAE;AACV,kCAAkB,MAAM;AAAA,cAC1B;AAAA,YACF,CAAC;AAAA,UACH;AACA,mBAAS,uBAAuB,QAAQ;AACtC,mBAAO,eAAe,QAAQ,KAAK;AAAA,UACrC;AACA,mBAAS,uBAAuB,QAAQ;AACtC,mBAAO,eAAe,QAAQ,IAAI;AAAA,UACpC;AACA,mBAAS,iBAAiB,QAAQ,gBAAgB;AAChD,gBAAI,CAAC,OAAO,IAAI;AACd,qBAAO;AAAA,YACT;AACA,gBAAI,aAAa,CAAC,OAAO,GAAG,iBAAiB,SAAS,OAAO,SAAS;AACtE,gBAAI,cAAc,OAAO,UAAU,OAAO,GAAG;AAC7C,gBAAI,YAAY,OAAO,UAAU,OAAO,GAAG;AAC3C,gBAAI,aAAa;AAGf,kBAAI,OAAO,YAAY,gBAAgB;AACrC,wBAAQ,KAAK,mBAAmB,OAAO,OAAO,wCAA6C,OAAO,GAAG,UAAU,iBAAiB,OAAO,UAAU,GAAG;AAAA,cACtJ;AAEA,qBAAO,UAAU,OAAO,GAAG;AAAA,YAC7B;AACA,gBAAI,aAAa,YAAY;AAI3B,kBAAI,YAAY;AACd,oBAAI,aAAa,OAAO,GAAG,UAAU;AACrC,oBAAI,aAAa,OAAO,SAAS;AAC/B,yBAAO,UAAU;AAAA,gBACnB;AAAA,cACF;AACA,qBAAO;AAAA,YACT;AACA,mBAAO;AAAA,UACT;AAGA,mBAAS,YAAY,MAAM;AACzB,mBAAO,IAAI,UAAU,SAAU,SAAS,QAAQ;AAC9C,kBAAI,SAAS,IAAI,WAAW;AAC5B,qBAAO,UAAU;AACjB,qBAAO,YAAY,SAAU,GAAG;AAC9B,oBAAI,SAAS,KAAK,EAAE,OAAO,UAAU,EAAE;AACvC,wBAAQ;AAAA,kBACN,6BAA6B;AAAA,kBAC7B,MAAM;AAAA,kBACN,MAAM,KAAK;AAAA,gBACb,CAAC;AAAA,cACH;AACA,qBAAO,mBAAmB,IAAI;AAAA,YAChC,CAAC;AAAA,UACH;AAGA,mBAAS,YAAY,aAAa;AAChC,gBAAI,YAAY,wBAAwB,KAAK,YAAY,IAAI,CAAC;AAC9D,mBAAO,WAAW,CAAC,SAAS,GAAG;AAAA,cAC7B,MAAM,YAAY;AAAA,YACpB,CAAC;AAAA,UACH;AAGA,mBAAS,eAAe,OAAO;AAC7B,mBAAO,SAAS,MAAM;AAAA,UACxB;AAMA,mBAAS,YAAY,UAAU;AAC7B,gBAAIJ,QAAO;AACX,gBAAI,UAAUA,MAAK,WAAW,EAAE,KAAK,WAAY;AAC/C,kBAAI,YAAY,WAAWA,MAAK,QAAQ,IAAI;AAC5C,kBAAI,aAAa,UAAU,SAAS;AAClC,uBAAO,UAAU;AAAA,cACnB;AAAA,YACF,CAAC;AACD,gCAAoB,SAAS,UAAU,QAAQ;AAC/C,mBAAO;AAAA,UACT;AAKA,mBAAS,cAAc,QAAQ;AAC7B,4BAAgB,MAAM;AACtB,gBAAI,YAAY,WAAW,OAAO,IAAI;AACtC,gBAAI,UAAU,UAAU;AACxB,qBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,kBAAI,SAAS,QAAQ,CAAC;AACtB,kBAAI,OAAO,QAAQ,IAAI;AACrB,uBAAO,QAAQ,GAAG,MAAM;AACxB,uBAAO,QAAQ,KAAK;AAAA,cACtB;AAAA,YACF;AACA,mBAAO,KAAK;AACZ,mBAAO,uBAAuB,MAAM,EAAE,KAAK,SAAU,IAAI;AACvD,qBAAO,KAAK;AACZ,kBAAI,iBAAiB,MAAM,GAAG;AAE5B,uBAAO,uBAAuB,MAAM;AAAA,cACtC;AACA,qBAAO;AAAA,YACT,CAAC,EAAE,KAAK,SAAU,IAAI;AAGpB,qBAAO,KAAK,UAAU,KAAK;AAC3B,uBAASC,KAAI,GAAGA,KAAI,QAAQ,QAAQA,MAAK;AACvC,wBAAQA,EAAC,EAAE,QAAQ,KAAK;AAAA,cAC1B;AAAA,YACF,CAAC,EAAE,OAAO,EAAE,SAAU,KAAK;AACzB,+BAAiB,QAAQ,GAAG;AAC5B,oBAAM;AAAA,YACR,CAAC;AAAA,UACH;AAIA,mBAAS,kBAAkB,QAAQ,MAAM,UAAU,SAAS;AAC1D,gBAAI,YAAY,QAAW;AACzB,wBAAU;AAAA,YACZ;AACA,gBAAI;AACF,kBAAI,KAAK,OAAO,GAAG,YAAY,OAAO,WAAW,IAAI;AACrD,uBAAS,MAAM,EAAE;AAAA,YACnB,SAAS,KAAK;AACZ,kBAAI,UAAU,MAAM,CAAC,OAAO,MAAM,IAAI,SAAS,uBAAuB,IAAI,SAAS,kBAAkB;AACnG,uBAAO,UAAU,QAAQ,EAAE,KAAK,WAAY;AAC1C,sBAAI,CAAC,OAAO,MAAM,IAAI,SAAS,mBAAmB,CAAC,OAAO,GAAG,iBAAiB,SAAS,OAAO,SAAS,KAAK,OAAO,WAAW,OAAO,GAAG,SAAS;AAE/I,wBAAI,OAAO,IAAI;AACb,6BAAO,UAAU,OAAO,GAAG,UAAU;AAAA,oBACvC;AAEA,2BAAO,uBAAuB,MAAM;AAAA,kBACtC;AAAA,gBACF,CAAC,EAAE,KAAK,WAAY;AAClB,yBAAO,cAAc,MAAM,EAAE,KAAK,WAAY;AAC5C,sCAAkB,QAAQ,MAAM,UAAU,UAAU,CAAC;AAAA,kBACvD,CAAC;AAAA,gBACH,CAAC,EAAE,OAAO,EAAE,QAAQ;AAAA,cACtB;AACA,uBAAS,GAAG;AAAA,YACd;AAAA,UACF;AACA,mBAAS,kBAAkB;AACzB,mBAAO;AAAA;AAAA,cAEL,SAAS,CAAC;AAAA;AAAA,cAEV,IAAI;AAAA;AAAA,cAEJ,SAAS;AAAA;AAAA,cAET,oBAAoB,CAAC;AAAA,YACvB;AAAA,UACF;AAIA,mBAAS,aAAa,SAAS;AAC7B,gBAAID,QAAO;AACX,gBAAI,SAAS;AAAA,cACX,IAAI;AAAA,YACN;AACA,gBAAI,SAAS;AACX,uBAAS,KAAK,SAAS;AACrB,uBAAO,CAAC,IAAI,QAAQ,CAAC;AAAA,cACvB;AAAA,YACF;AAGA,gBAAI,YAAY,WAAW,OAAO,IAAI;AAGtC,gBAAI,CAAC,WAAW;AACd,0BAAY,gBAAgB;AAE5B,yBAAW,OAAO,IAAI,IAAI;AAAA,YAC5B;AAGA,sBAAU,QAAQ,KAAKA,KAAI;AAG3B,gBAAI,CAACA,MAAK,YAAY;AACpB,cAAAA,MAAK,aAAaA,MAAK;AACvB,cAAAA,MAAK,QAAQ;AAAA,YACf;AAGA,gBAAI,eAAe,CAAC;AACpB,qBAAS,eAAe;AAGtB,qBAAO,UAAU,QAAQ;AAAA,YAC3B;AACA,qBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,QAAQ,KAAK;AACjD,kBAAI,SAAS,UAAU,QAAQ,CAAC;AAChC,kBAAI,WAAWA,OAAM;AAEnB,6BAAa,KAAK,OAAO,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;AAAA,cAC9D;AAAA,YACF;AAGA,gBAAI,UAAU,UAAU,QAAQ,MAAM,CAAC;AAIvC,mBAAO,UAAU,IAAI,YAAY,EAAE,KAAK,WAAY;AAClD,qBAAO,KAAK,UAAU;AAEtB,qBAAO,uBAAuB,MAAM;AAAA,YACtC,CAAC,EAAE,KAAK,SAAU,IAAI;AACpB,qBAAO,KAAK;AACZ,kBAAI,iBAAiB,QAAQA,MAAK,eAAe,OAAO,GAAG;AAEzD,uBAAO,uBAAuB,MAAM;AAAA,cACtC;AACA,qBAAO;AAAA,YACT,CAAC,EAAE,KAAK,SAAU,IAAI;AACpB,qBAAO,KAAK,UAAU,KAAK;AAC3B,cAAAA,MAAK,UAAU;AAEf,uBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,oBAAIK,UAAS,QAAQ,CAAC;AACtB,oBAAIA,YAAWL,OAAM;AAEnB,kBAAAK,QAAO,QAAQ,KAAK,OAAO;AAC3B,kBAAAA,QAAO,QAAQ,UAAU,OAAO;AAAA,gBAClC;AAAA,cACF;AAAA,YACF,CAAC;AAAA,UACH;AACA,mBAAS,QAAQH,MAAK,UAAU;AAC9B,gBAAIF,QAAO;AACX,YAAAE,OAAM,aAAaA,IAAG;AACtB,gBAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACrD,cAAAF,MAAK,MAAM,EAAE,KAAK,WAAY;AAC5B,kCAAkBA,MAAK,SAAS,WAAW,SAAU,KAAK,aAAa;AACrE,sBAAI,KAAK;AACP,2BAAO,OAAO,GAAG;AAAA,kBACnB;AACA,sBAAI;AACF,wBAAI,QAAQ,YAAY,YAAYA,MAAK,QAAQ,SAAS;AAC1D,wBAAI,MAAM,MAAM,IAAIE,IAAG;AACvB,wBAAI,YAAY,WAAY;AAC1B,0BAAI,QAAQ,IAAI;AAChB,0BAAI,UAAU,QAAW;AACvB,gCAAQ;AAAA,sBACV;AACA,0BAAI,eAAe,KAAK,GAAG;AACzB,gCAAQ,YAAY,KAAK;AAAA,sBAC3B;AACA,8BAAQ,KAAK;AAAA,oBACf;AACA,wBAAI,UAAU,WAAY;AACxB,6BAAO,IAAI,KAAK;AAAA,oBAClB;AAAA,kBACF,SAAS,GAAG;AACV,2BAAO,CAAC;AAAA,kBACV;AAAA,gBACF,CAAC;AAAA,cACH,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,YACpB,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AAGA,mBAAS,QAAQ,UAAU,UAAU;AACnC,gBAAIF,QAAO;AACX,gBAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACrD,cAAAA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC5B,kCAAkBA,MAAK,SAAS,WAAW,SAAU,KAAK,aAAa;AACrE,sBAAI,KAAK;AACP,2BAAO,OAAO,GAAG;AAAA,kBACnB;AACA,sBAAI;AACF,wBAAI,QAAQ,YAAY,YAAYA,MAAK,QAAQ,SAAS;AAC1D,wBAAI,MAAM,MAAM,WAAW;AAC3B,wBAAI,kBAAkB;AACtB,wBAAI,YAAY,WAAY;AAC1B,0BAAI,SAAS,IAAI;AACjB,0BAAI,QAAQ;AACV,4BAAI,QAAQ,OAAO;AACnB,4BAAI,eAAe,KAAK,GAAG;AACzB,kCAAQ,YAAY,KAAK;AAAA,wBAC3B;AACA,4BAAI,SAAS,SAAS,OAAO,OAAO,KAAK,iBAAiB;AAK1D,4BAAI,WAAW,QAAQ;AACrB,kCAAQ,MAAM;AAAA,wBAChB,OAAO;AACL,iCAAO,UAAU,EAAE;AAAA,wBACrB;AAAA,sBACF,OAAO;AACL,gCAAQ;AAAA,sBACV;AAAA,oBACF;AACA,wBAAI,UAAU,WAAY;AACxB,6BAAO,IAAI,KAAK;AAAA,oBAClB;AAAA,kBACF,SAAS,GAAG;AACV,2BAAO,CAAC;AAAA,kBACV;AAAA,gBACF,CAAC;AAAA,cACH,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,YACpB,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AACA,mBAAS,QAAQE,MAAK,OAAO,UAAU;AACrC,gBAAIF,QAAO;AACX,YAAAE,OAAM,aAAaA,IAAG;AACtB,gBAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACrD,kBAAI;AACJ,cAAAF,MAAK,MAAM,EAAE,KAAK,WAAY;AAC5B,yBAASA,MAAK;AACd,oBAAI,SAAS,KAAK,KAAK,MAAM,iBAAiB;AAC5C,yBAAO,kBAAkB,OAAO,EAAE,EAAE,KAAK,SAAU,aAAa;AAC9D,wBAAI,aAAa;AACf,6BAAO;AAAA,oBACT;AACA,2BAAO,YAAY,KAAK;AAAA,kBAC1B,CAAC;AAAA,gBACH;AACA,uBAAO;AAAA,cACT,CAAC,EAAE,KAAK,SAAUM,QAAO;AACvB,kCAAkBN,MAAK,SAAS,YAAY,SAAU,KAAK,aAAa;AACtE,sBAAI,KAAK;AACP,2BAAO,OAAO,GAAG;AAAA,kBACnB;AACA,sBAAI;AACF,wBAAI,QAAQ,YAAY,YAAYA,MAAK,QAAQ,SAAS;AAM1D,wBAAIM,WAAU,MAAM;AAClB,sBAAAA,SAAQ;AAAA,oBACV;AACA,wBAAI,MAAM,MAAM,IAAIA,QAAOJ,IAAG;AAC9B,gCAAY,aAAa,WAAY;AAOnC,0BAAII,WAAU,QAAW;AACvB,wBAAAA,SAAQ;AAAA,sBACV;AACA,8BAAQA,MAAK;AAAA,oBACf;AACA,gCAAY,UAAU,YAAY,UAAU,WAAY;AACtD,0BAAIC,OAAM,IAAI,QAAQ,IAAI,QAAQ,IAAI,YAAY;AAClD,6BAAOA,IAAG;AAAA,oBACZ;AAAA,kBACF,SAAS,GAAG;AACV,2BAAO,CAAC;AAAA,kBACV;AAAA,gBACF,CAAC;AAAA,cACH,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,YACpB,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AACA,mBAAS,WAAWL,MAAK,UAAU;AACjC,gBAAIF,QAAO;AACX,YAAAE,OAAM,aAAaA,IAAG;AACtB,gBAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACrD,cAAAF,MAAK,MAAM,EAAE,KAAK,WAAY;AAC5B,kCAAkBA,MAAK,SAAS,YAAY,SAAU,KAAK,aAAa;AACtE,sBAAI,KAAK;AACP,2BAAO,OAAO,GAAG;AAAA,kBACnB;AACA,sBAAI;AACF,wBAAI,QAAQ,YAAY,YAAYA,MAAK,QAAQ,SAAS;AAM1D,wBAAI,MAAM,MAAM,QAAQ,EAAEE,IAAG;AAC7B,gCAAY,aAAa,WAAY;AACnC,8BAAQ;AAAA,oBACV;AACA,gCAAY,UAAU,WAAY;AAChC,6BAAO,IAAI,KAAK;AAAA,oBAClB;AAIA,gCAAY,UAAU,WAAY;AAChC,0BAAIK,OAAM,IAAI,QAAQ,IAAI,QAAQ,IAAI,YAAY;AAClD,6BAAOA,IAAG;AAAA,oBACZ;AAAA,kBACF,SAAS,GAAG;AACV,2BAAO,CAAC;AAAA,kBACV;AAAA,gBACF,CAAC;AAAA,cACH,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,YACpB,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AACA,mBAAS,MAAM,UAAU;AACvB,gBAAIP,QAAO;AACX,gBAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACrD,cAAAA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC5B,kCAAkBA,MAAK,SAAS,YAAY,SAAU,KAAK,aAAa;AACtE,sBAAI,KAAK;AACP,2BAAO,OAAO,GAAG;AAAA,kBACnB;AACA,sBAAI;AACF,wBAAI,QAAQ,YAAY,YAAYA,MAAK,QAAQ,SAAS;AAC1D,wBAAI,MAAM,MAAM,MAAM;AACtB,gCAAY,aAAa,WAAY;AACnC,8BAAQ;AAAA,oBACV;AACA,gCAAY,UAAU,YAAY,UAAU,WAAY;AACtD,0BAAIO,OAAM,IAAI,QAAQ,IAAI,QAAQ,IAAI,YAAY;AAClD,6BAAOA,IAAG;AAAA,oBACZ;AAAA,kBACF,SAAS,GAAG;AACV,2BAAO,CAAC;AAAA,kBACV;AAAA,gBACF,CAAC;AAAA,cACH,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,YACpB,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AACA,mBAAS,OAAO,UAAU;AACxB,gBAAIP,QAAO;AACX,gBAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACrD,cAAAA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC5B,kCAAkBA,MAAK,SAAS,WAAW,SAAU,KAAK,aAAa;AACrE,sBAAI,KAAK;AACP,2BAAO,OAAO,GAAG;AAAA,kBACnB;AACA,sBAAI;AACF,wBAAI,QAAQ,YAAY,YAAYA,MAAK,QAAQ,SAAS;AAC1D,wBAAI,MAAM,MAAM,MAAM;AACtB,wBAAI,YAAY,WAAY;AAC1B,8BAAQ,IAAI,MAAM;AAAA,oBACpB;AACA,wBAAI,UAAU,WAAY;AACxB,6BAAO,IAAI,KAAK;AAAA,oBAClB;AAAA,kBACF,SAAS,GAAG;AACV,2BAAO,CAAC;AAAA,kBACV;AAAA,gBACF,CAAC;AAAA,cACH,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,YACpB,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AACA,mBAAS,IAAI,GAAG,UAAU;AACxB,gBAAIA,QAAO;AACX,gBAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACrD,kBAAI,IAAI,GAAG;AACT,wBAAQ,IAAI;AACZ;AAAA,cACF;AACA,cAAAA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC5B,kCAAkBA,MAAK,SAAS,WAAW,SAAU,KAAK,aAAa;AACrE,sBAAI,KAAK;AACP,2BAAO,OAAO,GAAG;AAAA,kBACnB;AACA,sBAAI;AACF,wBAAI,QAAQ,YAAY,YAAYA,MAAK,QAAQ,SAAS;AAC1D,wBAAI,WAAW;AACf,wBAAI,MAAM,MAAM,cAAc;AAC9B,wBAAI,YAAY,WAAY;AAC1B,0BAAI,SAAS,IAAI;AACjB,0BAAI,CAAC,QAAQ;AAEX,gCAAQ,IAAI;AACZ;AAAA,sBACF;AACA,0BAAI,MAAM,GAAG;AAGX,gCAAQ,OAAO,GAAG;AAAA,sBACpB,OAAO;AACL,4BAAI,CAAC,UAAU;AAGb,qCAAW;AACX,iCAAO,QAAQ,CAAC;AAAA,wBAClB,OAAO;AAEL,kCAAQ,OAAO,GAAG;AAAA,wBACpB;AAAA,sBACF;AAAA,oBACF;AACA,wBAAI,UAAU,WAAY;AACxB,6BAAO,IAAI,KAAK;AAAA,oBAClB;AAAA,kBACF,SAAS,GAAG;AACV,2BAAO,CAAC;AAAA,kBACV;AAAA,gBACF,CAAC;AAAA,cACH,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,YACpB,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AACA,mBAAS,KAAK,UAAU;AACtB,gBAAIA,QAAO;AACX,gBAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACrD,cAAAA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC5B,kCAAkBA,MAAK,SAAS,WAAW,SAAU,KAAK,aAAa;AACrE,sBAAI,KAAK;AACP,2BAAO,OAAO,GAAG;AAAA,kBACnB;AACA,sBAAI;AACF,wBAAI,QAAQ,YAAY,YAAYA,MAAK,QAAQ,SAAS;AAC1D,wBAAI,MAAM,MAAM,cAAc;AAC9B,wBAAIQ,QAAO,CAAC;AACZ,wBAAI,YAAY,WAAY;AAC1B,0BAAI,SAAS,IAAI;AACjB,0BAAI,CAAC,QAAQ;AACX,gCAAQA,KAAI;AACZ;AAAA,sBACF;AACA,sBAAAA,MAAK,KAAK,OAAO,GAAG;AACpB,6BAAO,UAAU,EAAE;AAAA,oBACrB;AACA,wBAAI,UAAU,WAAY;AACxB,6BAAO,IAAI,KAAK;AAAA,oBAClB;AAAA,kBACF,SAAS,GAAG;AACV,2BAAO,CAAC;AAAA,kBACV;AAAA,gBACF,CAAC;AAAA,cACH,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,YACpB,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AACA,mBAAS,aAAa,SAAS,UAAU;AACvC,uBAAW,YAAY,MAAM,MAAM,SAAS;AAC5C,gBAAI,gBAAgB,KAAK,OAAO;AAChC,sBAAU,OAAO,YAAY,cAAc,WAAW,CAAC;AACvD,gBAAI,CAAC,QAAQ,MAAM;AACjB,sBAAQ,OAAO,QAAQ,QAAQ,cAAc;AAC7C,sBAAQ,YAAY,QAAQ,aAAa,cAAc;AAAA,YACzD;AACA,gBAAIR,QAAO;AACX,gBAAI;AACJ,gBAAI,CAAC,QAAQ,MAAM;AACjB,wBAAU,UAAU,OAAO,mBAAmB;AAAA,YAChD,OAAO;AACL,kBAAI,cAAc,QAAQ,SAAS,cAAc,QAAQA,MAAK,QAAQ;AACtE,kBAAI,YAAY,cAAc,UAAU,QAAQA,MAAK,QAAQ,EAAE,IAAI,uBAAuB,OAAO,EAAE,KAAK,SAAU,IAAI;AACpH,oBAAI,YAAY,WAAW,QAAQ,IAAI;AACvC,oBAAI,UAAU,UAAU;AACxB,0BAAU,KAAK;AACf,yBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,0BAAQ,CAAC,EAAE,QAAQ,KAAK;AAAA,gBAC1B;AACA,uBAAO;AAAA,cACT,CAAC;AACD,kBAAI,CAAC,QAAQ,WAAW;AACtB,0BAAU,UAAU,KAAK,SAAU,IAAI;AACrC,kCAAgB,OAAO;AACvB,sBAAI,YAAY,WAAW,QAAQ,IAAI;AACvC,sBAAI,UAAU,UAAU;AACxB,qBAAG,MAAM;AACT,2BAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,wBAAI,SAAS,QAAQ,CAAC;AACtB,2BAAO,QAAQ,KAAK;AAAA,kBACtB;AACA,sBAAI,gBAAgB,IAAI,UAAU,SAAU,SAAS,QAAQ;AAC3D,wBAAI,MAAM,IAAI,eAAe,QAAQ,IAAI;AACzC,wBAAI,UAAU,WAAY;AACxB,0BAAIS,MAAK,IAAI;AACb,0BAAIA,KAAI;AACN,wBAAAA,IAAG,MAAM;AAAA,sBACX;AACA,6BAAO,IAAI,KAAK;AAAA,oBAClB;AACA,wBAAI,YAAY,WAAY;AAG1B,8BAAQ,KAAK,wCAAwC,QAAQ,OAAO,yCAAyC;AAAA,oBAC/G;AACA,wBAAI,YAAY,WAAY;AAC1B,0BAAIA,MAAK,IAAI;AACb,0BAAIA,KAAI;AACN,wBAAAA,IAAG,MAAM;AAAA,sBACX;AACA,8BAAQA,GAAE;AAAA,oBACZ;AAAA,kBACF,CAAC;AACD,yBAAO,cAAc,KAAK,SAAUA,KAAI;AACtC,8BAAU,KAAKA;AACf,6BAASR,KAAI,GAAGA,KAAI,QAAQ,QAAQA,MAAK;AACvC,0BAAI,UAAU,QAAQA,EAAC;AACvB,wCAAkB,QAAQ,OAAO;AAAA,oBACnC;AAAA,kBACF,CAAC,EAAE,OAAO,EAAE,SAAU,KAAK;AACzB,qBAAC,iBAAiB,SAAS,GAAG,KAAK,UAAU,QAAQ,GAAG,OAAO,EAAE,WAAY;AAAA,oBAAC,CAAC;AAC/E,0BAAM;AAAA,kBACR,CAAC;AAAA,gBACH,CAAC;AAAA,cACH,OAAO;AACL,0BAAU,UAAU,KAAK,SAAU,IAAI;AACrC,sBAAI,CAAC,GAAG,iBAAiB,SAAS,QAAQ,SAAS,GAAG;AACpD;AAAA,kBACF;AACA,sBAAI,aAAa,GAAG,UAAU;AAC9B,kCAAgB,OAAO;AACvB,sBAAI,YAAY,WAAW,QAAQ,IAAI;AACvC,sBAAI,UAAU,UAAU;AACxB,qBAAG,MAAM;AACT,2BAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,wBAAI,SAAS,QAAQ,CAAC;AACtB,2BAAO,QAAQ,KAAK;AACpB,2BAAO,QAAQ,UAAU;AAAA,kBAC3B;AACA,sBAAI,oBAAoB,IAAI,UAAU,SAAU,SAAS,QAAQ;AAC/D,wBAAI,MAAM,IAAI,KAAK,QAAQ,MAAM,UAAU;AAC3C,wBAAI,UAAU,SAAU,KAAK;AAC3B,0BAAIQ,MAAK,IAAI;AACb,sBAAAA,IAAG,MAAM;AACT,6BAAO,GAAG;AAAA,oBACZ;AACA,wBAAI,kBAAkB,WAAY;AAChC,0BAAIA,MAAK,IAAI;AACb,sBAAAA,IAAG,kBAAkB,QAAQ,SAAS;AAAA,oBACxC;AACA,wBAAI,YAAY,WAAY;AAC1B,0BAAIA,MAAK,IAAI;AACb,sBAAAA,IAAG,MAAM;AACT,8BAAQA,GAAE;AAAA,oBACZ;AAAA,kBACF,CAAC;AACD,yBAAO,kBAAkB,KAAK,SAAUA,KAAI;AAC1C,8BAAU,KAAKA;AACf,6BAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,0BAAI,WAAW,QAAQ,CAAC;AACxB,+BAAS,QAAQ,KAAKA;AACtB,wCAAkB,SAAS,OAAO;AAAA,oBACpC;AAAA,kBACF,CAAC,EAAE,OAAO,EAAE,SAAU,KAAK;AACzB,qBAAC,iBAAiB,SAAS,GAAG,KAAK,UAAU,QAAQ,GAAG,OAAO,EAAE,WAAY;AAAA,oBAAC,CAAC;AAC/E,0BAAM;AAAA,kBACR,CAAC;AAAA,gBACH,CAAC;AAAA,cACH;AAAA,YACF;AACA,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AACA,cAAI,eAAe;AAAA,YACjB,SAAS;AAAA,YACT;AAAA,YACA,UAAU,iBAAiB;AAAA,YAC3B;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,mBAAS,gBAAgB;AACvB,mBAAO,OAAO,iBAAiB;AAAA,UACjC;AAKA,cAAI,aAAa;AACjB,cAAI,mBAAmB;AACvB,cAAI,yBAAyB;AAC7B,cAAI,oBAAoB;AACxB,cAAI,2BAA2B,kBAAkB;AAGjD,cAAI,mBAAmB;AACvB,cAAI,YAAY;AAChB,cAAI,iBAAiB;AACrB,cAAI,kBAAkB;AACtB,cAAI,yBAAyB;AAC7B,cAAI,kBAAkB;AACtB,cAAI,kBAAkB;AACtB,cAAI,mBAAmB;AACvB,cAAI,mBAAmB;AACvB,cAAI,oBAAoB;AACxB,cAAI,oBAAoB;AACxB,cAAI,gCAAgC,2BAA2B,iBAAiB;AAChF,cAAI,aAAa,OAAO,UAAU;AAClC,mBAAS,eAAe,kBAAkB;AAExC,gBAAI,eAAe,iBAAiB,SAAS;AAC7C,gBAAI,MAAM,iBAAiB;AAC3B,gBAAI;AACJ,gBAAI,IAAI;AACR,gBAAI,UAAU,UAAU,UAAU;AAClC,gBAAI,iBAAiB,iBAAiB,SAAS,CAAC,MAAM,KAAK;AACzD;AACA,kBAAI,iBAAiB,iBAAiB,SAAS,CAAC,MAAM,KAAK;AACzD;AAAA,cACF;AAAA,YACF;AACA,gBAAI,SAAS,IAAI,YAAY,YAAY;AACzC,gBAAI,QAAQ,IAAI,WAAW,MAAM;AACjC,iBAAK,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC3B,yBAAW,WAAW,QAAQ,iBAAiB,CAAC,CAAC;AACjD,yBAAW,WAAW,QAAQ,iBAAiB,IAAI,CAAC,CAAC;AACrD,yBAAW,WAAW,QAAQ,iBAAiB,IAAI,CAAC,CAAC;AACrD,yBAAW,WAAW,QAAQ,iBAAiB,IAAI,CAAC,CAAC;AAGrD,oBAAM,GAAG,IAAI,YAAY,IAAI,YAAY;AACzC,oBAAM,GAAG,KAAK,WAAW,OAAO,IAAI,YAAY;AAChD,oBAAM,GAAG,KAAK,WAAW,MAAM,IAAI,WAAW;AAAA,YAChD;AACA,mBAAO;AAAA,UACT;AAIA,mBAAS,eAAe,QAAQ;AAE9B,gBAAI,QAAQ,IAAI,WAAW,MAAM;AACjC,gBAAI,eAAe;AACnB,gBAAI;AACJ,iBAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AAEpC,8BAAgB,WAAW,MAAM,CAAC,KAAK,CAAC;AACxC,8BAAgB,YAAY,MAAM,CAAC,IAAI,MAAM,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC;AAClE,8BAAgB,YAAY,MAAM,IAAI,CAAC,IAAI,OAAO,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC;AACvE,8BAAgB,WAAW,MAAM,IAAI,CAAC,IAAI,EAAE;AAAA,YAC9C;AACA,gBAAI,MAAM,SAAS,MAAM,GAAG;AAC1B,6BAAe,aAAa,UAAU,GAAG,aAAa,SAAS,CAAC,IAAI;AAAA,YACtE,WAAW,MAAM,SAAS,MAAM,GAAG;AACjC,6BAAe,aAAa,UAAU,GAAG,aAAa,SAAS,CAAC,IAAI;AAAA,YACtE;AACA,mBAAO;AAAA,UACT;AAKA,mBAAS,UAAU,OAAO,UAAU;AAClC,gBAAI,YAAY;AAChB,gBAAI,OAAO;AACT,0BAAY,WAAW,KAAK,KAAK;AAAA,YACnC;AAMA,gBAAI,UAAU,cAAc,0BAA0B,MAAM,UAAU,WAAW,KAAK,MAAM,MAAM,MAAM,yBAAyB;AAG/H,kBAAI;AACJ,kBAAI,SAAS;AACb,kBAAI,iBAAiB,aAAa;AAChC,yBAAS;AACT,0BAAU;AAAA,cACZ,OAAO;AACL,yBAAS,MAAM;AACf,oBAAI,cAAc,sBAAsB;AACtC,4BAAU;AAAA,gBACZ,WAAW,cAAc,uBAAuB;AAC9C,4BAAU;AAAA,gBACZ,WAAW,cAAc,8BAA8B;AACrD,4BAAU;AAAA,gBACZ,WAAW,cAAc,uBAAuB;AAC9C,4BAAU;AAAA,gBACZ,WAAW,cAAc,wBAAwB;AAC/C,4BAAU;AAAA,gBACZ,WAAW,cAAc,uBAAuB;AAC9C,4BAAU;AAAA,gBACZ,WAAW,cAAc,wBAAwB;AAC/C,4BAAU;AAAA,gBACZ,WAAW,cAAc,yBAAyB;AAChD,4BAAU;AAAA,gBACZ,WAAW,cAAc,yBAAyB;AAChD,4BAAU;AAAA,gBACZ,OAAO;AACL,2BAAS,IAAI,MAAM,oCAAoC,CAAC;AAAA,gBAC1D;AAAA,cACF;AACA,uBAAS,SAAS,eAAe,MAAM,CAAC;AAAA,YAC1C,WAAW,cAAc,iBAAiB;AAExC,kBAAI,aAAa,IAAI,WAAW;AAChC,yBAAW,SAAS,WAAY;AAE9B,oBAAI,MAAM,mBAAmB,MAAM,OAAO,MAAM,eAAe,KAAK,MAAM;AAC1E,yBAAS,oBAAoB,YAAY,GAAG;AAAA,cAC9C;AACA,yBAAW,kBAAkB,KAAK;AAAA,YACpC,OAAO;AACL,kBAAI;AACF,yBAAS,KAAK,UAAU,KAAK,CAAC;AAAA,cAChC,SAAS,GAAG;AACV,wBAAQ,MAAM,+CAA+C,KAAK;AAClE,yBAAS,MAAM,CAAC;AAAA,cAClB;AAAA,YACF;AAAA,UACF;AAUA,mBAAS,YAAY,OAAO;AAI1B,gBAAI,MAAM,UAAU,GAAG,wBAAwB,MAAM,mBAAmB;AACtE,qBAAO,KAAK,MAAM,KAAK;AAAA,YACzB;AAKA,gBAAI,mBAAmB,MAAM,UAAU,6BAA6B;AACpE,gBAAI,OAAO,MAAM,UAAU,0BAA0B,6BAA6B;AAClF,gBAAI;AAGJ,gBAAI,SAAS,aAAa,uBAAuB,KAAK,gBAAgB,GAAG;AACvE,kBAAI,UAAU,iBAAiB,MAAM,sBAAsB;AAC3D,yBAAW,QAAQ,CAAC;AACpB,iCAAmB,iBAAiB,UAAU,QAAQ,CAAC,EAAE,MAAM;AAAA,YACjE;AACA,gBAAI,SAAS,eAAe,gBAAgB;AAI5C,oBAAQ,MAAM;AAAA,cACZ,KAAK;AACH,uBAAO;AAAA,cACT,KAAK;AACH,uBAAO,WAAW,CAAC,MAAM,GAAG;AAAA,kBAC1B,MAAM;AAAA,gBACR,CAAC;AAAA,cACH,KAAK;AACH,uBAAO,IAAI,UAAU,MAAM;AAAA,cAC7B,KAAK;AACH,uBAAO,IAAI,WAAW,MAAM;AAAA,cAC9B,KAAK;AACH,uBAAO,IAAI,kBAAkB,MAAM;AAAA,cACrC,KAAK;AACH,uBAAO,IAAI,WAAW,MAAM;AAAA,cAC9B,KAAK;AACH,uBAAO,IAAI,YAAY,MAAM;AAAA,cAC/B,KAAK;AACH,uBAAO,IAAI,WAAW,MAAM;AAAA,cAC9B,KAAK;AACH,uBAAO,IAAI,YAAY,MAAM;AAAA,cAC/B,KAAK;AACH,uBAAO,IAAI,aAAa,MAAM;AAAA,cAChC,KAAK;AACH,uBAAO,IAAI,aAAa,MAAM;AAAA,cAChC;AACE,sBAAM,IAAI,MAAM,kBAAkB,IAAI;AAAA,YAC1C;AAAA,UACF;AACA,cAAI,wBAAwB;AAAA,YAC1B;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAYA,mBAAS,cAAc,GAAG,QAAQ,UAAU,eAAe;AACzD,cAAE,WAAW,gCAAgC,OAAO,YAAY,gDAAqD,CAAC,GAAG,UAAU,aAAa;AAAA,UAClJ;AAIA,mBAAS,eAAe,SAAS;AAC/B,gBAAIT,QAAO;AACX,gBAAI,SAAS;AAAA,cACX,IAAI;AAAA,YACN;AACA,gBAAI,SAAS;AACX,uBAAS,KAAK,SAAS;AACrB,uBAAO,CAAC,IAAI,OAAO,QAAQ,CAAC,MAAM,WAAW,QAAQ,CAAC,EAAE,SAAS,IAAI,QAAQ,CAAC;AAAA,cAChF;AAAA,YACF;AACA,gBAAI,gBAAgB,IAAI,UAAU,SAAU,SAAS,QAAQ;AAG3D,kBAAI;AACF,uBAAO,KAAK,aAAa,OAAO,MAAM,OAAO,OAAO,OAAO,GAAG,OAAO,aAAa,OAAO,IAAI;AAAA,cAC/F,SAAS,GAAG;AACV,uBAAO,OAAO,CAAC;AAAA,cACjB;AAGA,qBAAO,GAAG,YAAY,SAAU,GAAG;AACjC,8BAAc,GAAG,QAAQ,WAAY;AACnC,kBAAAA,MAAK,UAAU;AACf,0BAAQ;AAAA,gBACV,GAAG,SAAUU,IAAG,OAAO;AACrB,yBAAO,KAAK;AAAA,gBACd,CAAC;AAAA,cACH,GAAG,MAAM;AAAA,YACX,CAAC;AACD,mBAAO,aAAa;AACpB,mBAAO;AAAA,UACT;AACA,mBAAS,cAAc,GAAG,QAAQ,cAAc,MAAM,UAAU,eAAe;AAC7E,cAAE,WAAW,cAAc,MAAM,UAAU,SAAUA,IAAG,OAAO;AAC7D,kBAAI,MAAM,SAAS,MAAM,YAAY;AACnC,gBAAAA,GAAE,WAAW,kEAAuE,CAAC,OAAO,SAAS,GAAG,SAAUA,IAAG,SAAS;AAC5H,sBAAI,CAAC,QAAQ,KAAK,QAAQ;AAGxB,kCAAcA,IAAG,QAAQ,WAAY;AACnC,sBAAAA,GAAE,WAAW,cAAc,MAAM,UAAU,aAAa;AAAA,oBAC1D,GAAG,aAAa;AAAA,kBAClB,OAAO;AACL,kCAAcA,IAAG,KAAK;AAAA,kBACxB;AAAA,gBACF,GAAG,aAAa;AAAA,cAClB,OAAO;AACL,8BAAcA,IAAG,KAAK;AAAA,cACxB;AAAA,YACF,GAAG,aAAa;AAAA,UAClB;AACA,mBAAS,UAAUR,MAAK,UAAU;AAChC,gBAAIF,QAAO;AACX,YAAAE,OAAM,aAAaA,IAAG;AACtB,gBAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACrD,cAAAF,MAAK,MAAM,EAAE,KAAK,WAAY;AAC5B,oBAAI,SAASA,MAAK;AAClB,uBAAO,GAAG,YAAY,SAAU,GAAG;AACjC,gCAAc,GAAG,QAAQ,mBAAmB,OAAO,YAAY,0BAA0B,CAACE,IAAG,GAAG,SAAUQ,IAAG,SAAS;AACpH,wBAAI,SAAS,QAAQ,KAAK,SAAS,QAAQ,KAAK,KAAK,CAAC,EAAE,QAAQ;AAIhE,wBAAI,QAAQ;AACV,+BAAS,OAAO,WAAW,YAAY,MAAM;AAAA,oBAC/C;AACA,4BAAQ,MAAM;AAAA,kBAChB,GAAG,SAAUA,IAAG,OAAO;AACrB,2BAAO,KAAK;AAAA,kBACd,CAAC;AAAA,gBACH,CAAC;AAAA,cACH,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,YACpB,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AACA,mBAAS,UAAU,UAAU,UAAU;AACrC,gBAAIV,QAAO;AACX,gBAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACrD,cAAAA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC5B,oBAAI,SAASA,MAAK;AAClB,uBAAO,GAAG,YAAY,SAAU,GAAG;AACjC,gCAAc,GAAG,QAAQ,mBAAmB,OAAO,WAAW,CAAC,GAAG,SAAUU,IAAG,SAAS;AACtF,wBAAI,OAAO,QAAQ;AACnB,wBAAIP,UAAS,KAAK;AAClB,6BAAS,IAAI,GAAG,IAAIA,SAAQ,KAAK;AAC/B,0BAAI,OAAO,KAAK,KAAK,CAAC;AACtB,0BAAI,SAAS,KAAK;AAIlB,0BAAI,QAAQ;AACV,iCAAS,OAAO,WAAW,YAAY,MAAM;AAAA,sBAC/C;AACA,+BAAS,SAAS,QAAQ,KAAK,KAAK,IAAI,CAAC;AAIzC,0BAAI,WAAW,QAAQ;AACrB,gCAAQ,MAAM;AACd;AAAA,sBACF;AAAA,oBACF;AACA,4BAAQ;AAAA,kBACV,GAAG,SAAUO,IAAG,OAAO;AACrB,2BAAO,KAAK;AAAA,kBACd,CAAC;AAAA,gBACH,CAAC;AAAA,cACH,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,YACpB,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AACA,mBAAS,SAASR,MAAK,OAAO,UAAU,aAAa;AACnD,gBAAIF,QAAO;AACX,YAAAE,OAAM,aAAaA,IAAG;AACtB,gBAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACrD,cAAAF,MAAK,MAAM,EAAE,KAAK,WAAY;AAI5B,oBAAI,UAAU,QAAW;AACvB,0BAAQ;AAAA,gBACV;AAGA,oBAAI,gBAAgB;AACpB,oBAAI,SAASA,MAAK;AAClB,uBAAO,WAAW,UAAU,OAAO,SAAUM,QAAO,OAAO;AACzD,sBAAI,OAAO;AACT,2BAAO,KAAK;AAAA,kBACd,OAAO;AACL,2BAAO,GAAG,YAAY,SAAU,GAAG;AACjC,oCAAc,GAAG,QAAQ,4BAA4B,OAAO,YAAY,+BAAoC,CAACJ,MAAKI,MAAK,GAAG,WAAY;AACpI,gCAAQ,aAAa;AAAA,sBACvB,GAAG,SAAUI,IAAGC,QAAO;AACrB,+BAAOA,MAAK;AAAA,sBACd,CAAC;AAAA,oBACH,GAAG,SAAU,UAAU;AAGrB,0BAAI,SAAS,SAAS,SAAS,WAAW;AAQxC,4BAAI,cAAc,GAAG;AACnB,kCAAQ,SAAS,MAAMX,OAAM,CAACE,MAAK,eAAe,UAAU,cAAc,CAAC,CAAC,CAAC;AAC7E;AAAA,wBACF;AACA,+BAAO,QAAQ;AAAA,sBACjB;AAAA,oBACF,CAAC;AAAA,kBACH;AAAA,gBACF,CAAC;AAAA,cACH,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,YACpB,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AACA,mBAAS,UAAUA,MAAK,OAAO,UAAU;AACvC,mBAAO,SAAS,MAAM,MAAM,CAACA,MAAK,OAAO,UAAU,CAAC,CAAC;AAAA,UACvD;AACA,mBAAS,aAAaA,MAAK,UAAU;AACnC,gBAAIF,QAAO;AACX,YAAAE,OAAM,aAAaA,IAAG;AACtB,gBAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACrD,cAAAF,MAAK,MAAM,EAAE,KAAK,WAAY;AAC5B,oBAAI,SAASA,MAAK;AAClB,uBAAO,GAAG,YAAY,SAAU,GAAG;AACjC,gCAAc,GAAG,QAAQ,iBAAiB,OAAO,YAAY,kBAAkB,CAACE,IAAG,GAAG,WAAY;AAChG,4BAAQ;AAAA,kBACV,GAAG,SAAUQ,IAAG,OAAO;AACrB,2BAAO,KAAK;AAAA,kBACd,CAAC;AAAA,gBACH,CAAC;AAAA,cACH,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,YACpB,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AAIA,mBAAS,QAAQ,UAAU;AACzB,gBAAIV,QAAO;AACX,gBAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACrD,cAAAA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC5B,oBAAI,SAASA,MAAK;AAClB,uBAAO,GAAG,YAAY,SAAU,GAAG;AACjC,gCAAc,GAAG,QAAQ,iBAAiB,OAAO,WAAW,CAAC,GAAG,WAAY;AAC1E,4BAAQ;AAAA,kBACV,GAAG,SAAUU,IAAG,OAAO;AACrB,2BAAO,KAAK;AAAA,kBACd,CAAC;AAAA,gBACH,CAAC;AAAA,cACH,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,YACpB,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AAIA,mBAAS,SAAS,UAAU;AAC1B,gBAAIV,QAAO;AACX,gBAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACrD,cAAAA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC5B,oBAAI,SAASA,MAAK;AAClB,uBAAO,GAAG,YAAY,SAAU,GAAG;AAEjC,gCAAc,GAAG,QAAQ,iCAAiC,OAAO,WAAW,CAAC,GAAG,SAAUU,IAAG,SAAS;AACpG,wBAAI,SAAS,QAAQ,KAAK,KAAK,CAAC,EAAE;AAClC,4BAAQ,MAAM;AAAA,kBAChB,GAAG,SAAUA,IAAG,OAAO;AACrB,2BAAO,KAAK;AAAA,kBACd,CAAC;AAAA,gBACH,CAAC;AAAA,cACH,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,YACpB,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AASA,mBAAS,MAAM,GAAG,UAAU;AAC1B,gBAAIV,QAAO;AACX,gBAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACrD,cAAAA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC5B,oBAAI,SAASA,MAAK;AAClB,uBAAO,GAAG,YAAY,SAAU,GAAG;AACjC,gCAAc,GAAG,QAAQ,qBAAqB,OAAO,YAAY,yBAAyB,CAAC,IAAI,CAAC,GAAG,SAAUU,IAAG,SAAS;AACvH,wBAAI,SAAS,QAAQ,KAAK,SAAS,QAAQ,KAAK,KAAK,CAAC,EAAE,MAAM;AAC9D,4BAAQ,MAAM;AAAA,kBAChB,GAAG,SAAUA,IAAG,OAAO;AACrB,2BAAO,KAAK;AAAA,kBACd,CAAC;AAAA,gBACH,CAAC;AAAA,cACH,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,YACpB,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AACA,mBAAS,OAAO,UAAU;AACxB,gBAAIV,QAAO;AACX,gBAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACrD,cAAAA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC5B,oBAAI,SAASA,MAAK;AAClB,uBAAO,GAAG,YAAY,SAAU,GAAG;AACjC,gCAAc,GAAG,QAAQ,qBAAqB,OAAO,WAAW,CAAC,GAAG,SAAUU,IAAG,SAAS;AACxF,wBAAIF,QAAO,CAAC;AACZ,6BAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,QAAQ,KAAK;AAC5C,sBAAAA,MAAK,KAAK,QAAQ,KAAK,KAAK,CAAC,EAAE,GAAG;AAAA,oBACpC;AACA,4BAAQA,KAAI;AAAA,kBACd,GAAG,SAAUE,IAAG,OAAO;AACrB,2BAAO,KAAK;AAAA,kBACd,CAAC;AAAA,gBACH,CAAC;AAAA,cACH,CAAC,EAAE,OAAO,EAAE,MAAM;AAAA,YACpB,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AAIA,mBAAS,iBAAiB,IAAI;AAC5B,mBAAO,IAAI,UAAU,SAAU,SAAS,QAAQ;AAC9C,iBAAG,YAAY,SAAU,GAAG;AAC1B,kBAAE,WAAW,+FAAoG,CAAC,GAAG,SAAUA,IAAG,SAAS;AACzI,sBAAI,aAAa,CAAC;AAClB,2BAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,QAAQ,KAAK;AAC5C,+BAAW,KAAK,QAAQ,KAAK,KAAK,CAAC,EAAE,IAAI;AAAA,kBAC3C;AACA,0BAAQ;AAAA,oBACN;AAAA,oBACA;AAAA,kBACF,CAAC;AAAA,gBACH,GAAG,SAAUA,IAAG,OAAO;AACrB,yBAAO,KAAK;AAAA,gBACd,CAAC;AAAA,cACH,GAAG,SAAU,UAAU;AACrB,uBAAO,QAAQ;AAAA,cACjB,CAAC;AAAA,YACH,CAAC;AAAA,UACH;AACA,mBAAS,eAAe,SAAS,UAAU;AACzC,uBAAW,YAAY,MAAM,MAAM,SAAS;AAC5C,gBAAI,gBAAgB,KAAK,OAAO;AAChC,sBAAU,OAAO,YAAY,cAAc,WAAW,CAAC;AACvD,gBAAI,CAAC,QAAQ,MAAM;AACjB,sBAAQ,OAAO,QAAQ,QAAQ,cAAc;AAC7C,sBAAQ,YAAY,QAAQ,aAAa,cAAc;AAAA,YACzD;AACA,gBAAIV,QAAO;AACX,gBAAI;AACJ,gBAAI,CAAC,QAAQ,MAAM;AACjB,wBAAU,UAAU,OAAO,mBAAmB;AAAA,YAChD,OAAO;AACL,wBAAU,IAAI,UAAU,SAAU,SAAS;AACzC,oBAAI;AACJ,oBAAI,QAAQ,SAAS,cAAc,MAAM;AAEvC,uBAAKA,MAAK,QAAQ;AAAA,gBACpB,OAAO;AACL,uBAAK,aAAa,QAAQ,MAAM,IAAI,IAAI,CAAC;AAAA,gBAC3C;AACA,oBAAI,CAAC,QAAQ,WAAW;AAEtB,0BAAQ,iBAAiB,EAAE,CAAC;AAAA,gBAC9B,OAAO;AACL,0BAAQ;AAAA,oBACN;AAAA,oBACA,YAAY,CAAC,QAAQ,SAAS;AAAA,kBAChC,CAAC;AAAA,gBACH;AAAA,cACF,CAAC,EAAE,KAAK,SAAU,eAAe;AAC/B,uBAAO,IAAI,UAAU,SAAU,SAAS,QAAQ;AAC9C,gCAAc,GAAG,YAAY,SAAU,GAAG;AACxC,6BAAS,UAAU,WAAW;AAC5B,6BAAO,IAAI,UAAU,SAAUY,UAASC,SAAQ;AAC9C,0BAAE,WAAW,0BAA0B,WAAW,CAAC,GAAG,WAAY;AAChE,0BAAAD,SAAQ;AAAA,wBACV,GAAG,SAAUF,IAAG,OAAO;AACrB,0BAAAG,QAAO,KAAK;AAAA,wBACd,CAAC;AAAA,sBACH,CAAC;AAAA,oBACH;AACA,wBAAI,aAAa,CAAC;AAClB,6BAAS,IAAI,GAAG,MAAM,cAAc,WAAW,QAAQ,IAAI,KAAK,KAAK;AACnE,iCAAW,KAAK,UAAU,cAAc,WAAW,CAAC,CAAC,CAAC;AAAA,oBACxD;AACA,8BAAU,IAAI,UAAU,EAAE,KAAK,WAAY;AACzC,8BAAQ;AAAA,oBACV,CAAC,EAAE,OAAO,EAAE,SAAU,GAAG;AACvB,6BAAO,CAAC;AAAA,oBACV,CAAC;AAAA,kBACH,GAAG,SAAU,UAAU;AACrB,2BAAO,QAAQ;AAAA,kBACjB,CAAC;AAAA,gBACH,CAAC;AAAA,cACH,CAAC;AAAA,YACH;AACA,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AACA,cAAI,gBAAgB;AAAA,YAClB,SAAS;AAAA,YACT,cAAc;AAAA,YACd,UAAU,cAAc;AAAA,YACxB,SAAS;AAAA,YACT,SAAS;AAAA,YACT,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,MAAM;AAAA,YACN,cAAc;AAAA,UAChB;AACA,mBAAS,sBAAsB;AAC7B,gBAAI;AACF,qBAAO,OAAO,iBAAiB,eAAe,aAAa;AAAA,cAE3D,CAAC,CAAC,aAAa;AAAA,YACjB,SAAS,GAAG;AACV,qBAAO;AAAA,YACT;AAAA,UACF;AACA,mBAAS,cAAc,SAASC,gBAAe;AAC7C,gBAAI,YAAY,QAAQ,OAAO;AAC/B,gBAAI,QAAQ,cAAcA,eAAc,WAAW;AACjD,2BAAa,QAAQ,YAAY;AAAA,YACnC;AACA,mBAAO;AAAA,UACT;AAGA,mBAAS,4BAA4B;AACnC,gBAAI,sBAAsB;AAC1B,gBAAI;AACF,2BAAa,QAAQ,qBAAqB,IAAI;AAC9C,2BAAa,WAAW,mBAAmB;AAC3C,qBAAO;AAAA,YACT,SAAS,GAAG;AACV,qBAAO;AAAA,YACT;AAAA,UACF;AAMA,mBAAS,wBAAwB;AAC/B,mBAAO,CAAC,0BAA0B,KAAK,aAAa,SAAS;AAAA,UAC/D;AAGA,mBAAS,eAAe,SAAS;AAC/B,gBAAId,QAAO;AACX,gBAAI,SAAS,CAAC;AACd,gBAAI,SAAS;AACX,uBAAS,KAAK,SAAS;AACrB,uBAAO,CAAC,IAAI,QAAQ,CAAC;AAAA,cACvB;AAAA,YACF;AACA,mBAAO,YAAY,cAAc,SAASA,MAAK,cAAc;AAC7D,gBAAI,CAAC,sBAAsB,GAAG;AAC5B,qBAAO,UAAU,OAAO;AAAA,YAC1B;AACA,YAAAA,MAAK,UAAU;AACf,mBAAO,aAAa;AACpB,mBAAO,UAAU,QAAQ;AAAA,UAC3B;AAIA,mBAAS,QAAQ,UAAU;AACzB,gBAAIA,QAAO;AACX,gBAAI,UAAUA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1C,kBAAI,YAAYA,MAAK,QAAQ;AAC7B,uBAAS,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,KAAK;AACjD,oBAAIE,OAAM,aAAa,IAAI,CAAC;AAC5B,oBAAIA,KAAI,QAAQ,SAAS,MAAM,GAAG;AAChC,+BAAa,WAAWA,IAAG;AAAA,gBAC7B;AAAA,cACF;AAAA,YACF,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AAKA,mBAAS,UAAUA,MAAK,UAAU;AAChC,gBAAIF,QAAO;AACX,YAAAE,OAAM,aAAaA,IAAG;AACtB,gBAAI,UAAUF,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1C,kBAAI,SAASA,MAAK;AAClB,kBAAI,SAAS,aAAa,QAAQ,OAAO,YAAYE,IAAG;AAMxD,kBAAI,QAAQ;AACV,yBAAS,OAAO,WAAW,YAAY,MAAM;AAAA,cAC/C;AACA,qBAAO;AAAA,YACT,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AAGA,mBAAS,UAAU,UAAU,UAAU;AACrC,gBAAIF,QAAO;AACX,gBAAI,UAAUA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1C,kBAAI,SAASA,MAAK;AAClB,kBAAI,YAAY,OAAO;AACvB,kBAAI,kBAAkB,UAAU;AAChC,kBAAIG,UAAS,aAAa;AAQ1B,kBAAI,kBAAkB;AACtB,uBAAS,IAAI,GAAG,IAAIA,SAAQ,KAAK;AAC/B,oBAAID,OAAM,aAAa,IAAI,CAAC;AAC5B,oBAAIA,KAAI,QAAQ,SAAS,MAAM,GAAG;AAChC;AAAA,gBACF;AACA,oBAAI,QAAQ,aAAa,QAAQA,IAAG;AAMpC,oBAAI,OAAO;AACT,0BAAQ,OAAO,WAAW,YAAY,KAAK;AAAA,gBAC7C;AACA,wBAAQ,SAAS,OAAOA,KAAI,UAAU,eAAe,GAAG,iBAAiB;AACzE,oBAAI,UAAU,QAAQ;AACpB,yBAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AAGA,mBAAS,MAAM,GAAG,UAAU;AAC1B,gBAAIF,QAAO;AACX,gBAAI,UAAUA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1C,kBAAI,SAASA,MAAK;AAClB,kBAAI;AACJ,kBAAI;AACF,yBAAS,aAAa,IAAI,CAAC;AAAA,cAC7B,SAAS,OAAO;AACd,yBAAS;AAAA,cACX;AAGA,kBAAI,QAAQ;AACV,yBAAS,OAAO,UAAU,OAAO,UAAU,MAAM;AAAA,cACnD;AACA,qBAAO;AAAA,YACT,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AACA,mBAAS,OAAO,UAAU;AACxB,gBAAIA,QAAO;AACX,gBAAI,UAAUA,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1C,kBAAI,SAASA,MAAK;AAClB,kBAAIG,UAAS,aAAa;AAC1B,kBAAIK,QAAO,CAAC;AACZ,uBAAS,IAAI,GAAG,IAAIL,SAAQ,KAAK;AAC/B,oBAAI,UAAU,aAAa,IAAI,CAAC;AAChC,oBAAI,QAAQ,QAAQ,OAAO,SAAS,MAAM,GAAG;AAC3C,kBAAAK,MAAK,KAAK,QAAQ,UAAU,OAAO,UAAU,MAAM,CAAC;AAAA,gBACtD;AAAA,cACF;AACA,qBAAOA;AAAA,YACT,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AAGA,mBAAS,SAAS,UAAU;AAC1B,gBAAIR,QAAO;AACX,gBAAI,UAAUA,MAAK,KAAK,EAAE,KAAK,SAAUQ,OAAM;AAC7C,qBAAOA,MAAK;AAAA,YACd,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AAGA,mBAAS,aAAaN,MAAK,UAAU;AACnC,gBAAIF,QAAO;AACX,YAAAE,OAAM,aAAaA,IAAG;AACtB,gBAAI,UAAUF,MAAK,MAAM,EAAE,KAAK,WAAY;AAC1C,kBAAI,SAASA,MAAK;AAClB,2BAAa,WAAW,OAAO,YAAYE,IAAG;AAAA,YAChD,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AAMA,mBAAS,UAAUA,MAAK,OAAO,UAAU;AACvC,gBAAIF,QAAO;AACX,YAAAE,OAAM,aAAaA,IAAG;AACtB,gBAAI,UAAUF,MAAK,MAAM,EAAE,KAAK,WAAY;AAG1C,kBAAI,UAAU,QAAW;AACvB,wBAAQ;AAAA,cACV;AAGA,kBAAI,gBAAgB;AACpB,qBAAO,IAAI,UAAU,SAAU,SAAS,QAAQ;AAC9C,oBAAI,SAASA,MAAK;AAClB,uBAAO,WAAW,UAAU,OAAO,SAAUM,QAAO,OAAO;AACzD,sBAAI,OAAO;AACT,2BAAO,KAAK;AAAA,kBACd,OAAO;AACL,wBAAI;AACF,mCAAa,QAAQ,OAAO,YAAYJ,MAAKI,MAAK;AAClD,8BAAQ,aAAa;AAAA,oBACvB,SAAS,GAAG;AAGV,0BAAI,EAAE,SAAS,wBAAwB,EAAE,SAAS,8BAA8B;AAC9E,+BAAO,CAAC;AAAA,sBACV;AACA,6BAAO,CAAC;AAAA,oBACV;AAAA,kBACF;AAAA,gBACF,CAAC;AAAA,cACH,CAAC;AAAA,YACH,CAAC;AACD,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AACA,mBAAS,eAAe,SAAS,UAAU;AACzC,uBAAW,YAAY,MAAM,MAAM,SAAS;AAC5C,sBAAU,OAAO,YAAY,cAAc,WAAW,CAAC;AACvD,gBAAI,CAAC,QAAQ,MAAM;AACjB,kBAAI,gBAAgB,KAAK,OAAO;AAChC,sBAAQ,OAAO,QAAQ,QAAQ,cAAc;AAC7C,sBAAQ,YAAY,QAAQ,aAAa,cAAc;AAAA,YACzD;AACA,gBAAIN,QAAO;AACX,gBAAI;AACJ,gBAAI,CAAC,QAAQ,MAAM;AACjB,wBAAU,UAAU,OAAO,mBAAmB;AAAA,YAChD,OAAO;AACL,wBAAU,IAAI,UAAU,SAAU,SAAS;AACzC,oBAAI,CAAC,QAAQ,WAAW;AACtB,0BAAQ,QAAQ,OAAO,GAAG;AAAA,gBAC5B,OAAO;AACL,0BAAQ,cAAc,SAASA,MAAK,cAAc,CAAC;AAAA,gBACrD;AAAA,cACF,CAAC,EAAE,KAAK,SAAU,WAAW;AAC3B,yBAAS,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,KAAK;AACjD,sBAAIE,OAAM,aAAa,IAAI,CAAC;AAC5B,sBAAIA,KAAI,QAAQ,SAAS,MAAM,GAAG;AAChC,iCAAa,WAAWA,IAAG;AAAA,kBAC7B;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AACA,4BAAgB,SAAS,QAAQ;AACjC,mBAAO;AAAA,UACT;AACA,cAAI,sBAAsB;AAAA,YACxB,SAAS;AAAA,YACT,cAAc;AAAA,YACd,UAAU,oBAAoB;AAAA,YAC9B,SAAS;AAAA,YACT,SAAS;AAAA,YACT,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,KAAK;AAAA,YACL,MAAM;AAAA,YACN,cAAc;AAAA,UAChB;AACA,cAAI,YAAY,SAASa,WAAU,GAAG,GAAG;AACvC,mBAAO,MAAM,KAAK,OAAO,MAAM,YAAY,OAAO,MAAM,YAAY,MAAM,CAAC,KAAK,MAAM,CAAC;AAAA,UACzF;AACA,cAAI,WAAW,SAASC,UAAS,OAAO,eAAe;AACrD,gBAAI,MAAM,MAAM;AAChB,gBAAI,IAAI;AACR,mBAAO,IAAI,KAAK;AACd,kBAAI,UAAU,MAAM,CAAC,GAAG,aAAa,GAAG;AACtC,uBAAO;AAAA,cACT;AACA;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AACA,cAAI,UAAU,MAAM,WAAW,SAAU,KAAK;AAC5C,mBAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AAAA,UACjD;AAIA,cAAI,iBAAiB,CAAC;AACtB,cAAI,gBAAgB,CAAC;AACrB,cAAI,iBAAiB;AAAA,YACnB,WAAW;AAAA,YACX,QAAQ;AAAA,YACR,cAAc;AAAA,UAChB;AACA,cAAI,qBAAqB,CAAC,eAAe,UAAU,SAAS,eAAe,OAAO,SAAS,eAAe,aAAa,OAAO;AAC9H,cAAI,wBAAwB,CAAC,cAAc;AAC3C,cAAI,iBAAiB,CAAC,SAAS,WAAW,WAAW,OAAO,QAAQ,UAAU,cAAc,SAAS,EAAE,OAAO,qBAAqB;AACnI,cAAI,gBAAgB;AAAA,YAClB,aAAa;AAAA,YACb,QAAQ,mBAAmB,MAAM;AAAA,YACjC,MAAM;AAAA;AAAA;AAAA,YAGN,MAAM;AAAA,YACN,WAAW;AAAA,YACX,SAAS;AAAA,UACX;AACA,mBAAS,cAAc,qBAAqB,eAAe;AACzD,gCAAoB,aAAa,IAAI,WAAY;AAC/C,kBAAI,QAAQ;AACZ,qBAAO,oBAAoB,MAAM,EAAE,KAAK,WAAY;AAClD,uBAAO,oBAAoB,aAAa,EAAE,MAAM,qBAAqB,KAAK;AAAA,cAC5E,CAAC;AAAA,YACH;AAAA,UACF;AACA,mBAAS,SAAS;AAChB,qBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,kBAAI,MAAM,UAAU,CAAC;AACrB,kBAAI,KAAK;AACP,yBAAS,QAAQ,KAAK;AACpB,sBAAI,IAAI,eAAe,IAAI,GAAG;AAC5B,wBAAI,QAAQ,IAAI,IAAI,CAAC,GAAG;AACtB,gCAAU,CAAC,EAAE,IAAI,IAAI,IAAI,IAAI,EAAE,MAAM;AAAA,oBACvC,OAAO;AACL,gCAAU,CAAC,EAAE,IAAI,IAAI,IAAI,IAAI;AAAA,oBAC/B;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AACA,mBAAO,UAAU,CAAC;AAAA,UACpB;AACA,cAAIC,eAAc,WAAY;AAC5B,qBAASA,aAAY,SAAS;AAC5B,8BAAgB,MAAMA,YAAW;AACjC,uBAAS,iBAAiB,gBAAgB;AACxC,oBAAI,eAAe,eAAe,aAAa,GAAG;AAChD,sBAAI,SAAS,eAAe,aAAa;AACzC,sBAAI,aAAa,OAAO;AACxB,uBAAK,aAAa,IAAI;AACtB,sBAAI,CAAC,eAAe,UAAU,GAAG;AAI/B,yBAAK,aAAa,MAAM;AAAA,kBAC1B;AAAA,gBACF;AAAA,cACF;AACA,mBAAK,iBAAiB,OAAO,CAAC,GAAG,aAAa;AAC9C,mBAAK,UAAU,OAAO,CAAC,GAAG,KAAK,gBAAgB,OAAO;AACtD,mBAAK,aAAa;AAClB,mBAAK,cAAc;AACnB,mBAAK,SAAS;AACd,mBAAK,UAAU;AACf,mBAAK,6BAA6B;AAClC,mBAAK,UAAU,KAAK,QAAQ,MAAM,EAAE,OAAO,EAAE,WAAY;AAAA,cAAC,CAAC;AAAA,YAC7D;AAOA,YAAAA,aAAY,UAAU,SAAS,SAAS,OAAO,SAAS;AAItD,mBAAK,OAAO,YAAY,cAAc,cAAc,QAAQ,OAAO,OAAO,UAAU;AAGlF,oBAAI,KAAK,QAAQ;AACf,yBAAO,IAAI,MAAM,sDAA2D;AAAA,gBAC9E;AACA,yBAAS,KAAK,SAAS;AACrB,sBAAI,MAAM,aAAa;AACrB,4BAAQ,CAAC,IAAI,QAAQ,CAAC,EAAE,QAAQ,OAAO,GAAG;AAAA,kBAC5C;AACA,sBAAI,MAAM,aAAa,OAAO,QAAQ,CAAC,MAAM,UAAU;AACrD,2BAAO,IAAI,MAAM,oCAAoC;AAAA,kBACvD;AACA,uBAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,gBAC7B;AAIA,oBAAI,YAAY,WAAW,QAAQ,QAAQ;AACzC,yBAAO,KAAK,UAAU,KAAK,QAAQ,MAAM;AAAA,gBAC3C;AACA,uBAAO;AAAA,cACT,WAAW,OAAO,YAAY,UAAU;AACtC,uBAAO,KAAK,QAAQ,OAAO;AAAA,cAC7B,OAAO;AACL,uBAAO,KAAK;AAAA,cACd;AAAA,YACF;AAKA,YAAAA,aAAY,UAAU,eAAe,SAAS,aAAa,cAAc,UAAU,eAAe;AAChG,kBAAI,UAAU,IAAI,UAAU,SAAU,SAAS,QAAQ;AACrD,oBAAI;AACF,sBAAI,aAAa,aAAa;AAC9B,sBAAI,kBAAkB,IAAI,MAAM,sFAA2F;AAI3H,sBAAI,CAAC,aAAa,SAAS;AACzB,2BAAO,eAAe;AACtB;AAAA,kBACF;AACA,sBAAI,gBAAgB,eAAe,OAAO,cAAc;AACxD,2BAAS,IAAI,GAAG,MAAM,cAAc,QAAQ,IAAI,KAAK,KAAK;AACxD,wBAAI,mBAAmB,cAAc,CAAC;AAItC,wBAAI,aAAa,CAAC,SAAS,uBAAuB,gBAAgB;AAClE,yBAAK,cAAc,aAAa,gBAAgB,MAAM,OAAO,aAAa,gBAAgB,MAAM,YAAY;AAC1G,6BAAO,eAAe;AACtB;AAAA,oBACF;AAAA,kBACF;AACA,sBAAI,0BAA0B,SAASC,2BAA0B;AAC/D,wBAAI,8BAA8B,SAASC,6BAA4B,YAAY;AACjF,6BAAO,WAAY;AACjB,4BAAI,QAAQ,IAAI,MAAM,YAAY,aAAa,2CAA2C;AAC1F,4BAAIC,WAAU,UAAU,OAAO,KAAK;AACpC,wCAAgBA,UAAS,UAAU,UAAU,SAAS,CAAC,CAAC;AACxD,+BAAOA;AAAA,sBACT;AAAA,oBACF;AACA,6BAAS,KAAK,GAAG,OAAO,sBAAsB,QAAQ,KAAK,MAAM,MAAM;AACrE,0BAAI,uBAAuB,sBAAsB,EAAE;AACnD,0BAAI,CAAC,aAAa,oBAAoB,GAAG;AACvC,qCAAa,oBAAoB,IAAI,4BAA4B,oBAAoB;AAAA,sBACvF;AAAA,oBACF;AAAA,kBACF;AACA,0CAAwB;AACxB,sBAAI,mBAAmB,SAASC,kBAAiB,SAAS;AACxD,wBAAI,eAAe,UAAU,GAAG;AAC9B,8BAAQ,KAAK,oCAAoC,UAAU;AAAA,oBAC7D;AACA,mCAAe,UAAU,IAAI;AAC7B,kCAAc,UAAU,IAAI;AAI5B,4BAAQ;AAAA,kBACV;AACA,sBAAI,cAAc,cAAc;AAC9B,wBAAI,aAAa,YAAY,OAAO,aAAa,aAAa,YAAY;AACxE,mCAAa,SAAS,EAAE,KAAK,kBAAkB,MAAM;AAAA,oBACvD,OAAO;AACL,uCAAiB,CAAC,CAAC,aAAa,QAAQ;AAAA,oBAC1C;AAAA,kBACF,OAAO;AACL,qCAAiB,IAAI;AAAA,kBACvB;AAAA,gBACF,SAAS,GAAG;AACV,yBAAO,CAAC;AAAA,gBACV;AAAA,cACF,CAAC;AACD,kCAAoB,SAAS,UAAU,aAAa;AACpD,qBAAO;AAAA,YACT;AACA,YAAAJ,aAAY,UAAU,SAAS,SAAS,SAAS;AAC/C,qBAAO,KAAK,WAAW;AAAA,YACzB;AACA,YAAAA,aAAY,UAAU,YAAY,SAAS,UAAU,YAAY,UAAU,eAAe;AACxF,kBAAI,mBAAmB,eAAe,UAAU,IAAI,UAAU,QAAQ,eAAe,UAAU,CAAC,IAAI,UAAU,OAAO,IAAI,MAAM,mBAAmB,CAAC;AACnJ,kCAAoB,kBAAkB,UAAU,aAAa;AAC7D,qBAAO;AAAA,YACT;AACA,YAAAA,aAAY,UAAU,gBAAgB,SAAS,cAAc,UAAU;AACrE,kBAAI,oBAAoB,UAAU,QAAQ,qBAAqB;AAC/D,kCAAoB,mBAAmB,QAAQ;AAC/C,qBAAO;AAAA,YACT;AACA,YAAAA,aAAY,UAAU,QAAQ,SAAS,MAAM,UAAU;AACrD,kBAAIjB,QAAO;AACX,kBAAI,UAAUA,MAAK,WAAW,KAAK,WAAY;AAC7C,oBAAIA,MAAK,WAAW,MAAM;AACxB,kBAAAA,MAAK,SAASA,MAAK,YAAY;AAAA,gBACjC;AACA,uBAAOA,MAAK;AAAA,cACd,CAAC;AACD,kCAAoB,SAAS,UAAU,QAAQ;AAC/C,qBAAO;AAAA,YACT;AACA,YAAAiB,aAAY,UAAU,YAAY,SAAS,UAAU,SAAS,UAAU,eAAe;AACrF,kBAAIjB,QAAO;AACX,kBAAI,CAAC,QAAQ,OAAO,GAAG;AACrB,0BAAU,CAAC,OAAO;AAAA,cACpB;AACA,kBAAI,mBAAmB,KAAK,qBAAqB,OAAO;AACxD,uBAAS,oBAAoB;AAC3B,gBAAAA,MAAK,QAAQ,SAASA,MAAK,OAAO;AAAA,cACpC;AACA,uBAAS,qBAAqB,QAAQ;AACpC,gBAAAA,MAAK,QAAQ,MAAM;AACnB,kCAAkB;AAClB,gBAAAA,MAAK,SAASA,MAAK,aAAaA,MAAK,OAAO;AAC5C,uBAAOA,MAAK;AAAA,cACd;AACA,uBAAS,WAAWsB,mBAAkB;AACpC,uBAAO,WAAY;AACjB,sBAAI,qBAAqB;AACzB,2BAAS,oBAAoB;AAC3B,2BAAO,qBAAqBA,kBAAiB,QAAQ;AACnD,0BAAI,aAAaA,kBAAiB,kBAAkB;AACpD;AACA,sBAAAtB,MAAK,UAAU;AACf,sBAAAA,MAAK,SAAS;AACd,6BAAOA,MAAK,UAAU,UAAU,EAAE,KAAK,oBAAoB,EAAE,OAAO,EAAE,iBAAiB;AAAA,oBACzF;AACA,sCAAkB;AAClB,wBAAI,QAAQ,IAAI,MAAM,oCAAoC;AAC1D,oBAAAA,MAAK,aAAa,UAAU,OAAO,KAAK;AACxC,2BAAOA,MAAK;AAAA,kBACd;AACA,yBAAO,kBAAkB;AAAA,gBAC3B;AAAA,cACF;AAKA,kBAAI,mBAAmB,KAAK,eAAe,OAAO,KAAK,WAAW,OAAO,EAAE,WAAY;AACrF,uBAAO,UAAU,QAAQ;AAAA,cAC3B,CAAC,IAAI,UAAU,QAAQ;AACvB,mBAAK,aAAa,iBAAiB,KAAK,WAAY;AAClD,oBAAI,aAAa,iBAAiB,CAAC;AACnC,gBAAAA,MAAK,UAAU;AACf,gBAAAA,MAAK,SAAS;AACd,uBAAOA,MAAK,UAAU,UAAU,EAAE,KAAK,SAAU,QAAQ;AACvD,kBAAAA,MAAK,UAAU,OAAO;AACtB,oCAAkB;AAClB,kBAAAA,MAAK,6BAA6B;AAClC,kBAAAA,MAAK,cAAc,WAAW,gBAAgB;AAAA,gBAChD,CAAC;AAAA,cACH,CAAC,EAAE,OAAO,EAAE,WAAY;AACtB,kCAAkB;AAClB,oBAAI,QAAQ,IAAI,MAAM,oCAAoC;AAC1D,gBAAAA,MAAK,aAAa,UAAU,OAAO,KAAK;AACxC,uBAAOA,MAAK;AAAA,cACd,CAAC;AACD,kCAAoB,KAAK,YAAY,UAAU,aAAa;AAC5D,qBAAO,KAAK;AAAA,YACd;AACA,YAAAiB,aAAY,UAAU,WAAW,SAAS,SAAS,YAAY;AAC7D,qBAAO,CAAC,CAAC,cAAc,UAAU;AAAA,YACnC;AACA,YAAAA,aAAY,UAAU,UAAU,SAAS,QAAQ,6BAA6B;AAC5E,qBAAO,MAAM,2BAA2B;AAAA,YAC1C;AACA,YAAAA,aAAY,UAAU,uBAAuB,SAAS,qBAAqB,SAAS;AAClF,kBAAI,mBAAmB,CAAC;AACxB,uBAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAClD,oBAAI,aAAa,QAAQ,CAAC;AAC1B,oBAAI,KAAK,SAAS,UAAU,GAAG;AAC7B,mCAAiB,KAAK,UAAU;AAAA,gBAClC;AAAA,cACF;AACA,qBAAO;AAAA,YACT;AACA,YAAAA,aAAY,UAAU,+BAA+B,SAAS,+BAA+B;AAK3F,uBAAS,IAAI,GAAG,MAAM,eAAe,QAAQ,IAAI,KAAK,KAAK;AACzD,8BAAc,MAAM,eAAe,CAAC,CAAC;AAAA,cACvC;AAAA,YACF;AACA,YAAAA,aAAY,UAAU,iBAAiB,SAAS,eAAe,SAAS;AACtE,qBAAO,IAAIA,aAAY,OAAO;AAAA,YAChC;AACA,mBAAOA;AAAA,UACT,EAAE;AAKF,cAAI,iBAAiB,IAAIA,aAAY;AACrC,UAAAxB,QAAO,UAAU;AAAA,QACnB,GAAG;AAAA,UACD,KAAK;AAAA,QACP,CAAC;AAAA,MACH,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;AAAA,IACf,CAAC;AAAA;AAAA;;;ACt/ED,wBAEa,SAKP,eAMO;AAbb,IAAA8B,YAAA;AAAA;AAAA;AAAA,yBAAwB;AAEjB,IAAM,UAAU;AAAA,MACrB,eAAe;AAAA,MACf,WAAW,mBAAAC,QAAY;AAAA,MACvB,cAAc,mBAAAA,QAAY;AAAA,IAC5B;AACA,IAAM,gBAAgB;AAAA,MACpB,MAAM;AAAA,MACN,WAAW;AAAA,MACX,OAAO;AAAA,MACP,aAAa,CAAC,QAAQ,eAAe,QAAQ,WAAW,QAAQ,YAAY;AAAA,IAC9E;AACO,IAAM,UAAN,MAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWnB,YAAY,SAAS,eAAe;AAClC,aAAK,MAAM;AACX,aAAK,uBAAuB;AAC5B,cAAM,eAAe,OAAO,OAAO,CAAC,GAAG,eAAe,UAAU,CAAC,CAAC;AAClE,aAAK,UAAU;AAAA,MACjB;AAAA,MACM,SAAS;AAAA;AACb,gBAAM,KAAK,mBAAAA,QAAY,eAAe,KAAK,OAAO;AAClD,eAAK,MAAM;AACX,gBAAM,GAAG,UAAU,KAAK,QAAQ,eAAe,CAAC,CAAC;AACjD,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQM,aAAa,QAAQ;AAAA;AACzB,cAAI,OAAO,YAAY,QAAQ,eAAe;AAC5C,iBAAK,uBAAuB;AAAA,UAC9B;AACA,iBAAO,mBAAAA,QAAY,aAAa,MAAM;AAAA,QACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,IAAI,SAAS;AACX,YAAI;AACJ,iBAAS,KAAK,KAAK,SAAS,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO,MAAM;AAAA,MAC/E;AAAA,MACA,WAAW;AACT,YAAI,CAAC,KAAK,KAAK;AACb,gBAAM,IAAI,MAAM,gDAAgD;AAAA,QAClE;AACA,eAAO,KAAK;AAAA,MACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,IAAI,KAAK;AACP,cAAM,KAAK,KAAK,SAAS;AACzB,eAAO,GAAG,QAAQ,GAAG;AAAA,MACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,IAAI,KAAK,OAAO;AACd,cAAM,KAAK,KAAK,SAAS;AACzB,eAAO,GAAG,QAAQ,KAAK,KAAK;AAAA,MAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,OAAO,KAAK;AACV,cAAM,KAAK,KAAK,SAAS;AACzB,eAAO,GAAG,WAAW,GAAG;AAAA,MAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,QAAQ;AACN,cAAM,KAAK,KAAK,SAAS;AACzB,eAAO,GAAG,MAAM;AAAA,MAClB;AAAA;AAAA;AAAA;AAAA,MAIA,SAAS;AACP,cAAM,KAAK,KAAK,SAAS;AACzB,eAAO,GAAG,OAAO;AAAA,MACnB;AAAA;AAAA;AAAA;AAAA,MAIA,OAAO;AACL,cAAM,KAAK,KAAK,SAAS;AACzB,eAAO,GAAG,KAAK;AAAA,MACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,QAAQ,kBAAkB;AACxB,cAAM,KAAK,KAAK,SAAS;AACzB,eAAO,GAAG,QAAQ,gBAAgB;AAAA,MACpC;AAAA,MACA,iBAAiB,KAAK;AACpB,YAAI;AACJ,YAAI,CAAC,KAAK,sBAAsB;AAC9B,gBAAM,IAAI,MAAM,kFAAkF;AAAA,QACpG,OAAO;AACL,WAAC,KAAK,KAAK,0BAA0B,QAAQ,OAAO,SAAS,SAAS,GAAG,iBAAiB,GAAG;AAAA,QAC/F;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACjGA,SAAS,eAAe,YAAY,eAAe;AACjD,MAAI,iBAAiB,UAAU,GAAG;AAEhC,WAAO,IAAI,YAAY;AAAA,EACzB;AACA,SAAO,IAAI,QAAQ,aAAa;AAClC;AAvCA,IAKM,oBACA,aAkCA;AAxCN;AAAA;AAAA;AAAA;AACA;AACA;AACA,IAAAC;AACA,IAAAA;AACA,IAAM,qBAAqB,IAAI,eAAe,sBAAsB;AACpE,IAAM,cAAN,cAA0B,QAAQ;AAAA,MAChC,cAAc;AACZ,cAAM;AAAA,MACR;AAAA,MACM,SAAS;AAAA;AACb,iBAAO;AAAA,QACT;AAAA;AAAA,MACM,eAAe;AAAA;AAAA,QAAC;AAAA;AAAA,MACtB,IAAI,SAAS;AACX,eAAO;AAAA,MACT;AAAA,MACM,IAAI,KAAK;AAAA;AACb,iBAAO;AAAA,QACT;AAAA;AAAA,MACM,IAAI,KAAK,OAAO;AAAA;AAAA,QAAC;AAAA;AAAA,MACjB,OAAO,KAAK;AAAA;AAAA,QAAC;AAAA;AAAA,MACb,QAAQ;AAAA;AAAA,QAAC;AAAA;AAAA,MACT,SAAS;AAAA;AACb,iBAAO;AAAA,QACT;AAAA;AAAA,MACM,OAAO;AAAA;AACX,iBAAO,CAAC;AAAA,QACV;AAAA;AAAA;AAAA,MAEM,QAAQ,kBAAkB;AAAA;AAAA,QAAC;AAAA;AAAA,MACjC,iBAAiB,KAAK;AAAA,MAAC;AAAA,IACzB;AAQA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,MACvB,OAAO,QAAQ,gBAAgB,MAAM;AACnC,eAAO;AAAA,UACL,UAAU;AAAA,UACV,WAAW,CAAC;AAAA,YACV,SAAS;AAAA,YACT,UAAU;AAAA,UACZ,GAAG;AAAA,YACD,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,MAAM,CAAC,aAAa,kBAAkB;AAAA,UACxC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AACA,uBAAmB,YAAO,SAAS,2BAA2B,mBAAmB;AAC/E,aAAO,KAAK,qBAAqB,oBAAoB;AAAA,IACvD;AACA,uBAAmB,YAAsB,gBAAG,2BAAiB;AAAA,MAC3D,MAAM;AAAA,IACR,CAAC;AACD,uBAAmB,YAAsB,gBAAG,2BAAiB,CAAC,CAAC;AAC/D,KAAC,MAAM;AACL,OAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,QAC3F,MAAM;AAAA,MACR,CAAC,GAAG,MAAM,IAAI;AAAA,IAChB,GAAG;AAAA;AAAA;;;AClEH,IAOa;AAPb;;;;;;AAOM,IAAO,kBAAP,MAAO,gBAAc;MAGzB,YAAoB,SAAgB;AAAhB,aAAA,UAAA;AAFZ,aAAA,WAA2B;AAGjC,aAAK,KAAI;MACX;MAEM,OAAI;;AACR,cAAI,CAAC,KAAK,UAAU;AAChB,kBAAM,KAAK,QAAQ,OAAM;AACzB,iBAAK,WAAW,KAAK;UACvB;QACJ;;;MAGM,WAAW,OAAe,QAAgB,UAAkB,WAAiB;;AAtBrF;AAuBI,iBAAM,UAAK,aAAL,mBAAe,IAAI,OAAO;AAChC,iBAAM,UAAK,aAAL,mBAAe,IAAI,UAAU;AACnC,iBAAM,UAAK,aAAL,mBAAe,IAAI,YAAY;AACrC,iBAAM,UAAK,aAAL,mBAAe,IAAI,aAAa;QACxC;;;MAGM,WAAQ;;AA9BhB;AA+BI,cAAI,CAAC,KAAK,UAAU;AAClB,oBAAQ,KAAK,8CAA8C;AAC3D,iBAAK,WAAW,MAAM,KAAK,QAAQ,OAAM;UAC3C;AACA,iBAAO,OAAM,UAAK,aAAL,mBAAe,IAAI;QAClC;;;MAIM,YAAS;;AAxCjB;AAyCI,cAAI,CAAC,KAAK,UAAU;AAClB,oBAAQ,KAAK,8CAA8C;AAC3D,iBAAK,WAAW,MAAM,KAAK,QAAQ,OAAM;UAC3C;AAEE,gBAAM,SAAQ,YAAM,UAAK,aAAL,mBAAe,IAAI,cAAzB,YAAsC;AACpD,kBAAQ,IAAI,mBAAmB,KAAK;AACpC,iBAAO;QACX;;;MAGM,cAAW;;AApDnB;AAqDI,cAAI,CAAC,KAAK,UAAU;AAClB,oBAAQ,KAAK,8CAA8C;AAC3D,iBAAK,WAAW,MAAM,KAAK,QAAQ,OAAM;UAC3C;AACA,gBAAM,QAAQ,OAAM,UAAK,aAAL,mBAAe,IAAI;AACvC,kBAAQ,IAAI,qBAAqB,KAAK;AACtC,iBAAO;QACT;;;MAGQ,eAAY;;AA/DtB;AAgEM,cAAI,CAAC,KAAK,UAAU;AAClB,oBAAQ,KAAK,8CAA8C;AAC3D,iBAAK,WAAW,MAAM,KAAK,QAAQ,OAAM;UAC3C;AACE,iBAAO,OAAM,UAAK,aAAL,mBAAe,IAAI;QAClC;;;MAGE,eAAY;;AAxEpB;AAyEI,cAAI,CAAC,KAAK,UAAU;AAClB,oBAAQ,KAAK,8CAA8C;AAC3D,iBAAK,WAAW,MAAM,KAAK,QAAQ,OAAM;UAC3C;AACA,iBAAM,UAAK,aAAL,mBAAe,OAAO;AAC5B,iBAAM,UAAK,aAAL,mBAAe,OAAO;AAC5B,iBAAM,UAAK,aAAL,mBAAe,OAAO;AAC5B,iBAAM,UAAK,aAAL,mBAAe,OAAO;QAC9B;;;;uCA1EW,iBAAc,mBAAA,OAAA,CAAA;IAAA;uFAAd,iBAAc,SAAd,gBAAc,WAAA,YAFb,OAAM,CAAA;AAEd,IAAO,iBAAP;;0EAAO,gBAAc,CAAA;cAH1B;eAAW;UACV,YAAY;SACb;;;;;;;ACND,IAMa;AANb;;;;;;AAMM,IAAO,gBAAP,MAAO,cAAY;MAEvB,YAAoB,iBAAgC;AAAhC,aAAA,kBAAA;MAAmC;MAEjD,UAAU,SAAiB,OAAkD,WAAS;;AAC1F,gBAAM,QAAQ,MAAM,KAAK,gBAAgB,OAAO;YAC9C;YACA,UAAU;YACV,UAAU;YACV,OAAO;YACP,SAAS;cACP;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;;;WAGX;AACD,gBAAM,MAAM,QAAO;QACrB;;;;uCAnBW,eAAY,mBAAA,eAAA,CAAA;IAAA;;aAAZ;MAAY,SAAZ,cAAY;MAAA,YAFX;;;AAER,IAAO,eAAP;;0EAAO,cAAY,CAAA;cAHxB;eAAW;UACV,YAAY;;SACb;;;;;;;ACLD,IAGY;AAHZ;;;AAGA,KAAA,SAAYC,aAAU;AAElB,MAAAA,YAAA,eAAA,IAAA;AAGA,MAAAA,YAAA,eAAA,IAAA;AAGA,MAAAA,YAAA,eAAA,IAAA;AAGA,MAAAA,YAAA,QAAA,IAAA;AAGA,MAAAA,YAAA,QAAA,IAAA;IACF,GAfU,eAAA,aAAU,CAAA,EAAA;;;;;ACHtB,IAOa;AAPb;;;;AACA;;AAMM,IAAO,uBAAP,MAAO,qBAAmB;MAa9B,cAAA;AAXQ,aAAA,wBAAwB,IAAI,gBAAgC,IAAI;AAGjE,aAAA,kBAA8C,KAAK,sBAAsB,aAAY;AAGpF,aAAA,kBAAkB,IAAI,gBAA2B,CAAA,CAAE;AAGpD,aAAA,YAAmC,KAAK,gBAAgB,aAAY;MAE5D;;;;;MAMf,qBAAqB,SAAuB;AAC1C,aAAK,sBAAsB,KAAK,OAAO;MACzC;;;;;MAMA,eAAe,UAAmB;AAChC,aAAK,gBAAgB,KAAK,QAAQ;AAGlC,cAAM,iBAAiB,SAAS,KAAK,OAAK,EAAE,SAAS;AACrD,YAAI,gBAAgB;AAClB,eAAK,qBAAqB,cAAc;QAC1C;MACF;;;;;MAMA,2BAAwB;AACtB,eAAO,KAAK,sBAAsB,SAAQ;MAC5C;;;;;MAMA,qBAAkB;AAChB,eAAO,KAAK,gBAAgB,SAAQ;MACtC;;;uCAnDW,sBAAmB;IAAA;4FAAnB,sBAAmB,SAAnB,qBAAmB,WAAA,YAFlB,OAAM,CAAA;AAEd,IAAO,sBAAP;;0EAAO,qBAAmB,CAAA;cAH/B;eAAW;UACV,YAAY;SACb;;;;;;;ACND,IAOa;AAPb;;;;AACA;AACA;;AAKM,IAAO,kBAAP,MAAO,gBAAc;MAIzB,cAAA;AAFQ,aAAA,WAAW,IAAI,gBAAyB,UAAU,MAAM;AAI9D,aAAK,UAAU,MACb,GAAG,UAAU,MAAM,GACnB,UAAU,QAAQ,QAAQ,EAAE,KAAK,IAAI,MAAM,IAAI,CAAC,GAChD,UAAU,QAAQ,SAAS,EAAE,KAAK,IAAI,MAAM,KAAK,CAAC,CAAC;AAIrD,aAAK,QAAQ,UAAU,cAAW;AAChC,eAAK,SAAS,KAAK,QAAQ;QAC7B,CAAC;MACH;;;;;MAMO,kBAAe;AACpB,eAAO,KAAK,SAAS,aAAY;MACnC;;;;MAKO,cAAW;AAChB,eAAO,UAAU;MACnB;;;uCA/BW,iBAAc;IAAA;uFAAd,iBAAc,SAAd,gBAAc,WAAA,YAFb,OAAM,CAAA;AAEd,IAAO,iBAAP;;0EAAO,gBAAc,CAAA;cAH1B;eAAW;UACV,YAAY;SACb;;;;;;;ACND,IAIa;AAJb;;;AAIO,IAAM,cAAc;MACzB,YAAY;MACZ,QAAQ;;;;;;;ACNV,IAMa;AANb;;;;AACA;;AAKM,IAAO,cAAP,MAAO,YAAU;MAyBrB,cAAA;AAxBQ,aAAA,UAAU,YAAY;AAGtB,aAAA,cAAc;;UAEpB,UAAU;UACV,kBAAkB;;UAGlB,0BAA0B;UAC1B,iBAAiB;;UAGjB,UAAU;;UAGV,UAAU;;UAGV,mBAAmB;UACnB,iBAAiB;UACjB,oBAAoB;;MAGN;;;;;;;MAQhB,OAAO,UAAkB,aAA6C;AAEpE,cAAM,iBAAiB,SAAS,WAAW,MAAM,IAC7C,WACC,KAAK,YAA0C,QAAQ,KAAK;AAEjE,YAAI,MAAM,GAAG,KAAK,OAAO,IAAI,cAAc;AAG3C,YAAI,eAAe,OAAO,KAAK,WAAW,EAAE,SAAS,GAAG;AACtD,gBAAM,SAAS,IAAI,gBAAe;AAClC,iBAAO,QAAQ,WAAW,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAK;AACnD,mBAAO,OAAO,KAAK,MAAM,SAAQ,CAAE;UACrC,CAAC;AACD,iBAAO,IAAI,OAAO,SAAQ,CAAE;QAC9B;AAEA,eAAO;MACT;;;uCAnDW,aAAU;IAAA;mFAAV,aAAU,SAAV,YAAU,WAAA,YAFT,OAAM,CAAA;AAEd,IAAO,aAAP;;0EAAO,YAAU,CAAA;cAHtB;eAAW;UACV,YAAY;SACb;;;;;;;ACJD,IAgBa;AAhBb;;;;AAEA;;;;;;;;AAcM,IAAO,kBAAP,MAAO,gBAAc;MAGvB,YACU,MACA,oBACA,gBACA,YACA,qBACA,gBAA8B;AAL9B,aAAA,OAAA;AACA,aAAA,qBAAA;AACA,aAAA,iBAAA;AACA,aAAA,aAAA;AACA,aAAA,sBAAA;AACA,aAAA,iBAAA;AAPO,aAAA,YAAY;MAQ1B;MAEG,OAAO,MAA6B;;AACxC,cAAI;AACF,kBAAM,MAAM,KAAK,WAAW,OAAO,wBAAwB;AAC3D,kBAAM,SAAS,MAAM,KAAK,KAAK,KAAU,KAAK,IAAI,EAAE,UAAS;AAC7D,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,2BAA2B,KAAK;AAC9C,kBAAM;UACR;QACF;;MAEM,YAAY,MAA6B;;AAC7C,cAAI;AAEF,kBAAM,aAAY,oBAAI,KAAI,GAAG,YAAW;AAGxC,kBAAM,mBAAmB,MAAM,KAAK,aAAa,KAAK,MAAM;AAC5D,kBAAM,YAAY,iBAAiB,WAAW,IAAI,IAAI;AAGtD,kBAAM,aAAa,KAAK,eAAe,YAAW,IAAK,WAAW,gBAAgB,WAAW;AACrG,oBAAQ,IAAI,2BAA2B,KAAK,UAAU,IAAI,CAAC;AACnD,kBAAM,KAAK,mBAAmB,OAAO,KAAK,WAAW;cACnD,IAAI,KAAK;cACT,QAAQ,KAAK;cACb,OAAO,KAAK;cACZ,MAAM,KAAK;cACX,SAAS,KAAK;cACd,WAAW,KAAK;cAChB,SAAS,KAAK;cACd,WAAW,KAAK;cAChB,SAAS,KAAK;cACd,cAAc,KAAK;cACnB;cACA;cACA;cACA,cAAc;aACf;AACD,oBAAQ,IAAI,wBAAuB,KAAK,UAAU,IAAI,CAAC;AACvD,mBAAO,EAAE,IAAI,KAAK,WAAW,WAAW,cAAc,GAAG,WAAsB;UACjF,SAAS,OAAO;AACd,oBAAQ,MAAM,2BAA2B,KAAK;AAC9C,kBAAM;UACR;QACF;;MAEM,QAAQ,QAAc;;AAC1B,cAAI;AACF,kBAAM,MAAM,KAAK,WAAW,OAAO,iBAAiB,EAAE,OAAM,CAAE;AAC9D,kBAAM,SAAS,MAAM,KAAK,KAAK,IAAe,GAAG,EAAE,UAAS;AAE5D,gBAAI,CAAC;AAAQ,qBAAO,CAAA;AAEpB,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,4BAA4B,KAAK;AAC/C,mBAAO,CAAA;UACT;QACF;;MAEM,aAAa,QAAc;;AAE/B,cAAI,SAAS,MAAM,KAAK,mBAAmB,OAAO,KAAK,WAAW,EAAE;AAEpE,cAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,mBAAO,CAAA;UACT;AAEA,mBAAS,OAAO,OAAO,CAAC,SAAc,KAAK,WAAW,MAAM;AAC5D,kBAAQ,IAAI,oCAAoC,OAAO,MAAM;AAC7D,cAAG,CAAC,UAAU,OAAO,WAAW;AAC9B,mBAAO,CAAA;AAET,gBAAM,iBAA4B,OAAO,IAAI,CAAC,SAAa;AACzD,mBAAO;cACL,IAAI,KAAK;cACT,SAAS,KAAK;cACd,WAAW,KAAK;cAChB,SAAS,KAAK;cACd,WAAW,KAAK;cAChB,QAAQ,KAAK;cACb,OAAO,KAAK;cACZ,MAAM,KAAK;cACX,SAAS,KAAK;cACd,cAAc,KAAK;cACnB,WAAW,KAAK,cAAc,KAAK,KAAK,cAAc;cACtD,WAAW,KAAK;cAChB,WAAW,KAAK;cAChB,YAAY,KAAK,cAAc,WAAW;cAC1C,cAAc,KAAK,eAAe,IAAI,KAAK,KAAK,YAAY,IAAI;;UAEpE,CAAC;AAGD,eAAK,oBAAoB,eAAe,cAAc;AAEtD,iBAAO;QACT;;;;;;;MAOM,kBAAkB,WAAmB,QAAc;;AACvD,cAAI;AAEF,kBAAM,WAAW,MAAM,KAAK,aAAa,MAAM;AAG/C,uBAAW,WAAW,UAAU;AAC9B,kBAAI,QAAQ,WAAW;AAErB,sBAAM,kBAAkB,iCAAK,UAAL,EAAc,WAAW,EAAC;AAGlD,sBAAM,KAAK,mBAAmB,OAC5B,KAAK,WACL,iBACA,SAAS,QAAQ,EAAE,GAAG;cAE1B;YACF;AAGA,kBAAM,iBAAiB,SAAS,KAAK,OAAK,EAAE,OAAO,SAAS;AAE5D,gBAAI,gBAAgB;AAElB,oBAAM,iBAAiB,iCAAK,iBAAL,EAAqB,WAAW,KAAI;AAG3D,oBAAM,eAAe,iCAAK,iBAAL,EAAqB,WAAW,EAAC;AACtD,oBAAM,KAAK,mBAAmB,OAC5B,KAAK,WACL,cACA,SAAS,SAAS,GAAG;AAIvB,oBAAM,KAAK,aAAa,MAAM;AAG9B,mBAAK,oBAAoB,qBAAqB,cAAc;AAE5D,qBAAO;YACT,OAAO;AACL,sBAAQ,MAAM,sBAAsB,SAAS;AAC7C,qBAAO;YACT;UACF,SAAS,OAAO;AACd,oBAAQ,MAAM,kCAAkC,KAAK;AACrD,mBAAO;UACT;QACF;;;;;;;MAOM,kBAAkB,QAAc;;AACpC,cAAI;AACF,kBAAM,WAAW,MAAM,KAAK,aAAa,MAAM;AAG/C,kBAAM,iBAAiB,SAAS,KAAK,OAAK,EAAE,SAAS;AAGrD,gBAAI,CAAC,kBAAkB,SAAS,SAAS,GAAG;AAC1C,oBAAM,KAAK,kBAAkB,SAAS,CAAC,EAAE,IAAI,MAAM;AAGnD,oBAAM,iBAAiB,iCAAK,SAAS,CAAC,IAAf,EAAkB,WAAW,KAAI;AAGxD,mBAAK,oBAAoB,qBAAqB,cAAc;AAE5D,qBAAO;YACT;AAGA,gBAAI,gBAAgB;AAClB,mBAAK,oBAAoB,qBAAqB,cAAc;YAC9D;AAEA,mBAAO,kBAAkB;UAC3B,SAAS,OAAO;AACd,oBAAQ,MAAM,kCAAkC,KAAK;AACrD,mBAAO;UACT;QACF;;;;;;;MAOM,YAAY,QAAc;;AAC9B,gBAAM,WAAW,MAAM,KAAK,aAAa,MAAM;AAC/C,iBAAO,SAAS,SAAS;QAC3B;;MAEM,kBAAkB,SAA8B;;AACpD,cAAI;AACF,kBAAM,MAAM,KAAK,WAAW,OAAO,wBAAwB;AAC3D,kBAAM,SAAS,MAAM,KAAK,KAAK,KAAU,KAAK,OAAO,EAAE,UAAS;AAChE,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,kCAAkC,KAAK;AACrD,kBAAM;UACR;QACF;;;;;;;MAOM,kBAAkB,SAAgB;;AACtC,cAAI;AAEF,kBAAM,eAAe,iCAChB,UADgB;cAEnB,WAAW,QAAQ,YAAY,IAAI;cACnC,cAAc,QAAQ,eAAe,QAAQ,aAAa,YAAW,IAAK;;AAG5E,kBAAM,KAAK,mBAAmB,OAC5B,KAAK,WACL,cACA,SAAS,QAAQ,EAAE,GAAG;AAIxB,gBAAI,QAAQ,QAAQ;AAClB,oBAAM,KAAK,aAAa,QAAQ,MAAM;YACxC;AAEA,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,uCAAuC,KAAK;AAC1D,mBAAO;UACT;QACF;;;;;;;MAOM,uBAAuB,QAAc;;AACzC,gBAAM,WAAW,MAAM,KAAK,aAAa,MAAM;AAC/C,iBAAO,SAAS,OAAO,OACrB,EAAE,eAAe,WAAW,iBAC5B,EAAE,eAAe,WAAW,iBAC5B,EAAE,eAAe,WAAW,aAAa;QAE7C;;;;;MAKM,QAAQ,IAAU;;AACtB,gBAAM,UAAS,MAAM,KAAK,eAAe,UAAS,MAAM;AACxD,gBAAM,WAAW,MAAM,KAAK,aAAa,MAAM;AAC/C,iBAAO,SAAS,KAAK,OAAK,EAAE,OAAO,EAAE,KAAK;QAC5C;;;;;MAKM,OAAO,IAAY,gBAAuB;;AAC9C,cAAI;AAEF,kBAAM,eAAe,iCAChB,iBADgB;cAEnB,WAAW,eAAe,YAAY,IAAI;cAC1C,cAAc,eAAe,eAAgB,eAAe,wBAAwB,OAAO,eAAe,aAAa,YAAW,IAAK,eAAe,eAAgB;;AAExK,kBAAM,KAAK,mBAAmB,OAC5B,KAAK,WACL,cACA,SAAS,EAAE,GAAG;AAGhB,gBAAI,eAAe,QAAQ;AACzB,oBAAM,KAAK,aAAa,eAAe,MAAM;YAC/C;AACA,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,iCAA8B,KAAK;AACjD,mBAAO;UACT;QACF;;;;uCAnTS,iBAAc,mBAAA,UAAA,GAAA,mBAAA,kBAAA,GAAA,mBAAA,cAAA,GAAA,mBAAA,UAAA,GAAA,mBAAA,mBAAA,GAAA,mBAAA,cAAA,CAAA;IAAA;uFAAd,iBAAc,SAAd,gBAAc,WAAA,YAFX,OAAM,CAAA;AAEhB,IAAO,iBAAP;;0EAAO,gBAAc,CAAA;cAH1B;eAAW;UACR,YAAY;SACb;;;;;", "names": ["define", "module", "exports", "o", "e", "n", "global", "Promise", "self", "i", "key", "length", "idb", "forage", "value", "err", "keys", "db", "t", "error", "resolve", "reject", "defaultConfig", "sameValue", "includes", "LocalForage", "configureMissingMethods", "methodNotImplementedFactory", "promise", "setDriverSupport", "supportedDrivers", "init_esm", "LocalForage", "init_esm", "SyncStatus"], "x_google_ignoreList": [0, 1, 2]}