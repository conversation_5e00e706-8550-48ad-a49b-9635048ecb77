{"version": 3, "sources": ["node_modules/@capacitor/app/dist/esm/web.js"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\nexport class AppWeb extends WebPlugin {\n  constructor() {\n    super();\n    this.handleVisibilityChange = () => {\n      const data = {\n        isActive: document.hidden !== true\n      };\n      this.notifyListeners('appStateChange', data);\n      if (document.hidden) {\n        this.notifyListeners('pause', null);\n      } else {\n        this.notifyListeners('resume', null);\n      }\n    };\n    document.addEventListener('visibilitychange', this.handleVisibilityChange, false);\n  }\n  exitApp() {\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async getInfo() {\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async getLaunchUrl() {\n    return {\n      url: ''\n    };\n  }\n  async getState() {\n    return {\n      isActive: document.hidden !== true\n    };\n  }\n  async minimizeApp() {\n    throw this.unimplemented('Not implemented on web.');\n  }\n}\n"], "mappings": ";;;;;;;;;;AAAA,IACa;AADb;AAAA;AAAA;AACO,IAAM,SAAN,cAAqB,UAAU;AAAA,MACpC,cAAc;AACZ,cAAM;AACN,aAAK,yBAAyB,MAAM;AAClC,gBAAM,OAAO;AAAA,YACX,UAAU,SAAS,WAAW;AAAA,UAChC;AACA,eAAK,gBAAgB,kBAAkB,IAAI;AAC3C,cAAI,SAAS,QAAQ;AACnB,iBAAK,gBAAgB,SAAS,IAAI;AAAA,UACpC,OAAO;AACL,iBAAK,gBAAgB,UAAU,IAAI;AAAA,UACrC;AAAA,QACF;AACA,iBAAS,iBAAiB,oBAAoB,KAAK,wBAAwB,KAAK;AAAA,MAClF;AAAA,MACA,UAAU;AACR,cAAM,KAAK,cAAc,yBAAyB;AAAA,MACpD;AAAA,MACM,UAAU;AAAA;AACd,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,MACM,eAAe;AAAA;AACnB,iBAAO;AAAA,YACL,KAAK;AAAA,UACP;AAAA,QACF;AAAA;AAAA,MACM,WAAW;AAAA;AACf,iBAAO;AAAA,YACL,UAAU,SAAS,WAAW;AAAA,UAChC;AAAA,QACF;AAAA;AAAA,MACM,cAAc;AAAA;AAClB,gBAAM,KAAK,cAAc,yBAAyB;AAAA,QACpD;AAAA;AAAA,IACF;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}