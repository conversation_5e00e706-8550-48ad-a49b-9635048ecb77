﻿using GoodDriver.CommandHandlers.Users;
using GoodDriver.CommandHandlers.Vehicles;
using GoodDriver.Contracts.Users.Commands;
using GoodDriver.Contracts.Users.Requests;
using GoodDriver.Contracts.Users.Responses;
using GoodDriver.Contracts.Vehicles.Commands;
using Microsoft.Extensions.DependencyInjection;
using Rogerio.Cqrs.Bus;
using Rogerio.Cqrs.Commands;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Infrastructure.Installers
{
    public static class CommandInstaller
    {
        public static void Install(IServiceCollection services)
        {
            services.AddSingleton<UserCommandHandler>();
            services.AddSingleton<VehicleCommandHandler>();

            services.AddSingleton<ICommandBus>(a =>
            {
				var container = a.GetService<MemoryContainerBus>();


                //User
                var userCommandHandler = a.GetService<UserCommandHandler>();
                container.Register<UserCreateCommand>(userCommandHandler);
                container.Register<UserLogonRequest, UserLogonResponse>(userCommandHandler);


                //Vehicle
                var vehicleCommandHandler = a.GetService<VehicleCommandHandler>();
                container.Register<VehicleCreateCommand>(vehicleCommandHandler);

                return container;
			});
        }
    }
}
