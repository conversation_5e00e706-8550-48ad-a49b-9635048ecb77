﻿using GoodDriver.API.Controllers.Users;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Rogerio.Cqrs.Commands;
using Rogerio.Security.Domain;
using Rogerio.Security;
using GoodDriver.Contracts.Users.Commands;
using Rogerio.Commom.Exceptions;
using GoodDriver.Contracts.Vehicles.Commands;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using Rogerio.Cqrs.Requests;
using GoodDriver.ReadModels.Brands.Queries;
using GoodDriver.ReadModels.Brands.Result;
using GoodDriver.ReadModels.Vehicles._2_Result;
using GoodDriver.ReadModels.Vehicles._1_Query;

namespace GoodDriver.API.Controllers.Vehicles
{
    [ApiController]
    [Route("api/vehicle")]
    public class VehicleController : BaseControllerGoodDriver
    {
        private readonly ICommandBus commandBus;
        private readonly IConfiguration _config;
        private readonly IOptions<SecurityOptions> secutiryOption;
        private readonly IRequestBus requestBus;
        public VehicleController(IConfiguration config, ILogger<UserController> logger, ISecurityManager securityManager, ICommandBus _commandBus, IOptions<SecurityOptions> secutiryOption, IRequestBus requestBus)
            : base(securityManager)
        {
            _config = config;
            commandBus = _commandBus;
            this.secutiryOption = secutiryOption;
            this.requestBus = requestBus;
        }

        /// <summary>
        /// Cria um novo veículo para o usuário.
        /// </summary>
        [HttpPost("create")]
        [Authorize]
        public async Task<IActionResult> CreateVehicle([FromBody] VehicleCreateCommand createcommand)
        {
            if (!ModelState.IsValid)
                return new JsonResult(new { Success = false, Message = "Dados inválidos para criação de um veículo." });

            try
            {
                await commandBus.SendAsync(createcommand);
                return Ok(new { Success = true, Message = "Veículo criado com sucesso." });
            }
            catch (BusinessException ex)
            {
                return BadRequest(new { message = ex.Message, errorCode = ex.Key });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }

        }


        /// <summary>
        /// Lista os veículos do usuário.
        /// </summary>
        [HttpGet("list")]
        [Authorize]
        public async Task<IActionResult> ListVehicles([FromQuery] string userId)
        {
            try
            {
                if (string.IsNullOrEmpty(userId))
                {
                    userId = User.Id;
                }

                var vehicles = await requestBus.RequestAsync<ListVehiclesByUserIdQuery, ListVehiclesByUserIdResult>(new ListVehiclesByUserIdQuery(userId));
                return Ok(vehicles);
            }
            catch (BusinessException ex)
            {
                return BadRequest(new { message = ex.Message, errorCode = ex.Key });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }
    }
}
