{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/index-cfd9c1f2.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n// TODO(FW-2832): types\nclass Config {\n  constructor() {\n    this.m = new Map();\n  }\n  reset(configObj) {\n    this.m = new Map(Object.entries(configObj));\n  }\n  get(key, fallback) {\n    const value = this.m.get(key);\n    return value !== undefined ? value : fallback;\n  }\n  getBoolean(key, fallback = false) {\n    const val = this.m.get(key);\n    if (val === undefined) {\n      return fallback;\n    }\n    if (typeof val === 'string') {\n      return val === 'true';\n    }\n    return !!val;\n  }\n  getNumber(key, fallback) {\n    const val = parseFloat(this.m.get(key));\n    return isNaN(val) ? fallback !== undefined ? fallback : NaN : val;\n  }\n  set(key, value) {\n    this.m.set(key, value);\n  }\n}\nconst config = /*@__PURE__*/new Config();\nconst configFromSession = win => {\n  try {\n    const configStr = win.sessionStorage.getItem(IONIC_SESSION_KEY);\n    return configStr !== null ? JSON.parse(configStr) : {};\n  } catch (e) {\n    return {};\n  }\n};\nconst saveConfig = (win, c) => {\n  try {\n    win.sessionStorage.setItem(IONIC_SESSION_KEY, JSON.stringify(c));\n  } catch (e) {\n    return;\n  }\n};\nconst configFromURL = win => {\n  const configObj = {};\n  win.location.search.slice(1).split('&').map(entry => entry.split('=')).map(([key, value]) => {\n    try {\n      return [decodeURIComponent(key), decodeURIComponent(value)];\n    } catch (e) {\n      return ['', ''];\n    }\n  }).filter(([key]) => startsWith(key, IONIC_PREFIX)).map(([key, value]) => [key.slice(IONIC_PREFIX.length), value]).forEach(([key, value]) => {\n    configObj[key] = value;\n  });\n  return configObj;\n};\nconst startsWith = (input, search) => {\n  return input.substr(0, search.length) === search;\n};\nconst IONIC_PREFIX = 'ionic:';\nconst IONIC_SESSION_KEY = 'ionic-persist-config';\nvar LogLevel;\n(function (LogLevel) {\n  LogLevel[\"OFF\"] = \"OFF\";\n  LogLevel[\"ERROR\"] = \"ERROR\";\n  LogLevel[\"WARN\"] = \"WARN\";\n})(LogLevel || (LogLevel = {}));\n/**\n * Logs a warning to the console with an Ionic prefix\n * to indicate the library that is warning the developer.\n *\n * @param message - The string message to be logged to the console.\n */\nconst printIonWarning = (message, ...params) => {\n  const logLevel = config.get('logLevel', LogLevel.WARN);\n  if ([LogLevel.WARN].includes(logLevel)) {\n    return console.warn(`[Ionic Warning]: ${message}`, ...params);\n  }\n};\n/**\n * Logs an error to the console with an Ionic prefix\n * to indicate the library that is warning the developer.\n *\n * @param message - The string message to be logged to the console.\n * @param params - Additional arguments to supply to the console.error.\n */\nconst printIonError = (message, ...params) => {\n  const logLevel = config.get('logLevel', LogLevel.ERROR);\n  if ([LogLevel.ERROR, LogLevel.WARN].includes(logLevel)) {\n    return console.error(`[Ionic Error]: ${message}`, ...params);\n  }\n};\n/**\n * Prints an error informing developers that an implementation requires an element to be used\n * within a specific selector.\n *\n * @param el The web component element this is requiring the element.\n * @param targetSelectors The selector or selectors that were not found.\n */\nconst printRequiredElementError = (el, ...targetSelectors) => {\n  return console.error(`<${el.tagName.toLowerCase()}> must be used inside ${targetSelectors.join(' or ')}.`);\n};\nexport { LogLevel as L, configFromSession as a, configFromURL as b, config as c, printIonError as d, printRequiredElementError as e, printIonWarning as p, saveConfig as s };"], "mappings": ";;;;;AAAA,IAIM,QA6BA,QACA,mBAQA,YAOA,eAaA,YAGA,cACA,mBACF,UAYE,iBAaA,eAaA;AAzGN;AAAA;AAAA;AAIA,IAAM,SAAN,MAAa;AAAA,MACX,cAAc;AACZ,aAAK,IAAI,oBAAI,IAAI;AAAA,MACnB;AAAA,MACA,MAAM,WAAW;AACf,aAAK,IAAI,IAAI,IAAI,OAAO,QAAQ,SAAS,CAAC;AAAA,MAC5C;AAAA,MACA,IAAI,KAAK,UAAU;AACjB,cAAM,QAAQ,KAAK,EAAE,IAAI,GAAG;AAC5B,eAAO,UAAU,SAAY,QAAQ;AAAA,MACvC;AAAA,MACA,WAAW,KAAK,WAAW,OAAO;AAChC,cAAM,MAAM,KAAK,EAAE,IAAI,GAAG;AAC1B,YAAI,QAAQ,QAAW;AACrB,iBAAO;AAAA,QACT;AACA,YAAI,OAAO,QAAQ,UAAU;AAC3B,iBAAO,QAAQ;AAAA,QACjB;AACA,eAAO,CAAC,CAAC;AAAA,MACX;AAAA,MACA,UAAU,KAAK,UAAU;AACvB,cAAM,MAAM,WAAW,KAAK,EAAE,IAAI,GAAG,CAAC;AACtC,eAAO,MAAM,GAAG,IAAI,aAAa,SAAY,WAAW,MAAM;AAAA,MAChE;AAAA,MACA,IAAI,KAAK,OAAO;AACd,aAAK,EAAE,IAAI,KAAK,KAAK;AAAA,MACvB;AAAA,IACF;AACA,IAAM,SAAsB,oBAAI,OAAO;AACvC,IAAM,oBAAoB,SAAO;AAC/B,UAAI;AACF,cAAM,YAAY,IAAI,eAAe,QAAQ,iBAAiB;AAC9D,eAAO,cAAc,OAAO,KAAK,MAAM,SAAS,IAAI,CAAC;AAAA,MACvD,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,IAAM,aAAa,CAAC,KAAK,MAAM;AAC7B,UAAI;AACF,YAAI,eAAe,QAAQ,mBAAmB,KAAK,UAAU,CAAC,CAAC;AAAA,MACjE,SAAS,GAAG;AACV;AAAA,MACF;AAAA,IACF;AACA,IAAM,gBAAgB,SAAO;AAC3B,YAAM,YAAY,CAAC;AACnB,UAAI,SAAS,OAAO,MAAM,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI,WAAS,MAAM,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AAC3F,YAAI;AACF,iBAAO,CAAC,mBAAmB,GAAG,GAAG,mBAAmB,KAAK,CAAC;AAAA,QAC5D,SAAS,GAAG;AACV,iBAAO,CAAC,IAAI,EAAE;AAAA,QAChB;AAAA,MACF,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,MAAM,WAAW,KAAK,YAAY,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,IAAI,MAAM,aAAa,MAAM,GAAG,KAAK,CAAC,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC3I,kBAAU,GAAG,IAAI;AAAA,MACnB,CAAC;AACD,aAAO;AAAA,IACT;AACA,IAAM,aAAa,CAAC,OAAO,WAAW;AACpC,aAAO,MAAM,OAAO,GAAG,OAAO,MAAM,MAAM;AAAA,IAC5C;AACA,IAAM,eAAe;AACrB,IAAM,oBAAoB;AAE1B,KAAC,SAAUA,WAAU;AACnB,MAAAA,UAAS,KAAK,IAAI;AAClB,MAAAA,UAAS,OAAO,IAAI;AACpB,MAAAA,UAAS,MAAM,IAAI;AAAA,IACrB,GAAG,aAAa,WAAW,CAAC,EAAE;AAO9B,IAAM,kBAAkB,CAAC,YAAY,WAAW;AAC9C,YAAM,WAAW,OAAO,IAAI,YAAY,SAAS,IAAI;AACrD,UAAI,CAAC,SAAS,IAAI,EAAE,SAAS,QAAQ,GAAG;AACtC,eAAO,QAAQ,KAAK,oBAAoB,OAAO,IAAI,GAAG,MAAM;AAAA,MAC9D;AAAA,IACF;AAQA,IAAM,gBAAgB,CAAC,YAAY,WAAW;AAC5C,YAAM,WAAW,OAAO,IAAI,YAAY,SAAS,KAAK;AACtD,UAAI,CAAC,SAAS,OAAO,SAAS,IAAI,EAAE,SAAS,QAAQ,GAAG;AACtD,eAAO,QAAQ,MAAM,kBAAkB,OAAO,IAAI,GAAG,MAAM;AAAA,MAC7D;AAAA,IACF;AAQA,IAAM,4BAA4B,CAAC,OAAO,oBAAoB;AAC5D,aAAO,QAAQ,MAAM,IAAI,GAAG,QAAQ,YAAY,CAAC,yBAAyB,gBAAgB,KAAK,MAAM,CAAC,GAAG;AAAA,IAC3G;AAAA;AAAA;", "names": ["LogLevel"], "x_google_ignoreList": [0]}