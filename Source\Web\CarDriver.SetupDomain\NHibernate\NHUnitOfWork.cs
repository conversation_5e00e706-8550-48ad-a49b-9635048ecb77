﻿using GoodDriver.SetupDomain.UnitOfWork;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.SetupDomain.NHibernate
{
    public class NHUnitOfWork : IUnitOfWork<ISession>, IUnitOfWork, IDisposable
    {
        private ISession session;

        private ITransaction transaction;

        private int transactionCreatedUsage;

        internal int Usages { get; set; }

        public ISession Context => session;

        internal event EventHandler Closed;

        public NHUnitOfWork(ISession session, ITransaction transaction = null)
        {
            if (session == null)
            {
                throw new ArgumentNullException("session");
            }

            this.session = session;
            this.transaction = transaction;
            Usages = 0;
        }

        public void Complete()
        {
            lock (this)
            {
                if (Usages == transactionCreatedUsage && transaction != null)
                {
                    transaction.Commit();
                    DisposeTransaction();
                }

                if (transaction == null)
                {
                    session.Flush();
                }
            }
        }

        public void Dispose()
        {
            lock (this)
            {
                if (transactionCreatedUsage >= Usages && transaction != null)
                {
                    transaction.Rollback();
                    DisposeTransaction();
                }

                if (Usages == 1)
                {
                    session.Close();
                    if (this.Closed != null)
                    {
                        this.Closed(this, new EventArgs());
                    }
                }

                Usages--;
            }
        }

        internal bool WithTransaction()
        {
            return transaction != null;
        }

        internal void CreateTransactionIfNotExists(IsolationLevel? il)
        {
            if (transaction == null)
            {
                transactionCreatedUsage = Usages;
                if (il.HasValue)
                {
                    transaction = session.BeginTransaction(il.Value);
                }
                else
                {
                    transaction = session.BeginTransaction();
                }
            }
        }

        private void DisposeTransaction()
        {
            transaction.Dispose();
            transaction = null;
            transactionCreatedUsage = 0;
        }
    }
}
