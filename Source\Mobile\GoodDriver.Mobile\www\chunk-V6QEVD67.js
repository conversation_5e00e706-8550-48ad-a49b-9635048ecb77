import {
  __async,
  __esm,
  __glob,
  __spreadValues
} from "./chunk-4W6HR7MY.js";

// import("./**/*.entry.js") in node_modules/@ionic/core/dist/esm/index-527b9e34.js
var globImport_entry_js;
var init_ = __esm({
  'import("./**/*.entry.js") in node_modules/@ionic/core/dist/esm/index-527b9e34.js'() {
    globImport_entry_js = __glob({
      "./ion-accordion_2.entry.js": () => import("./ion-accordion_2.entry-B5T7L7ZF.js"),
      "./ion-action-sheet.entry.js": () => import("./ion-action-sheet.entry-YYSEW2OJ.js"),
      "./ion-alert.entry.js": () => import("./ion-alert.entry-BFJGM52E.js"),
      "./ion-app_8.entry.js": () => import("./ion-app_8.entry-GCSCJXPF.js"),
      "./ion-avatar_3.entry.js": () => import("./ion-avatar_3.entry-TOCYCWH4.js"),
      "./ion-back-button.entry.js": () => import("./ion-back-button.entry-NM7WTAE7.js"),
      "./ion-backdrop.entry.js": () => import("./ion-backdrop.entry-ZKU7Q6DQ.js"),
      "./ion-breadcrumb_2.entry.js": () => import("./ion-breadcrumb_2.entry-EH7AWXWR.js"),
      "./ion-button_2.entry.js": () => import("./ion-button_2.entry-3PHKQ4TO.js"),
      "./ion-card_5.entry.js": () => import("./ion-card_5.entry-CB4NY36V.js"),
      "./ion-checkbox.entry.js": () => import("./ion-checkbox.entry-GJXG5D34.js"),
      "./ion-chip.entry.js": () => import("./ion-chip.entry-RJL2JSHZ.js"),
      "./ion-col_3.entry.js": () => import("./ion-col_3.entry-76PSK4UI.js"),
      "./ion-datetime-button.entry.js": () => import("./ion-datetime-button.entry-GWGBE5ZN.js"),
      "./ion-datetime_3.entry.js": () => import("./ion-datetime_3.entry-X5K2PJLN.js"),
      "./ion-fab_3.entry.js": () => import("./ion-fab_3.entry-UHQACBLL.js"),
      "./ion-img.entry.js": () => import("./ion-img.entry-WBEVO2XI.js"),
      "./ion-infinite-scroll_2.entry.js": () => import("./ion-infinite-scroll_2.entry-3EYCYWBI.js"),
      "./ion-input-password-toggle.entry.js": () => import("./ion-input-password-toggle.entry-OMEPZUYL.js"),
      "./ion-input.entry.js": () => import("./ion-input.entry-LPL7NQXK.js"),
      "./ion-item-option_3.entry.js": () => import("./ion-item-option_3.entry-OY3ZB53I.js"),
      "./ion-item_8.entry.js": () => import("./ion-item_8.entry-6QJRYZX4.js"),
      "./ion-loading.entry.js": () => import("./ion-loading.entry-LNPJNN7Q.js"),
      "./ion-menu_3.entry.js": () => import("./ion-menu_3.entry-CYQRNRYE.js"),
      "./ion-modal.entry.js": () => import("./ion-modal.entry-M6XTAGM7.js"),
      "./ion-nav_2.entry.js": () => import("./ion-nav_2.entry-I6E4GPIK.js"),
      "./ion-picker-column-option.entry.js": () => import("./ion-picker-column-option.entry-UXCEI6DQ.js"),
      "./ion-picker-column.entry.js": () => import("./ion-picker-column.entry-5IDP2QFO.js"),
      "./ion-picker.entry.js": () => import("./ion-picker.entry-6BWPKWNK.js"),
      "./ion-popover.entry.js": () => import("./ion-popover.entry-4HJIJ67V.js"),
      "./ion-progress-bar.entry.js": () => import("./ion-progress-bar.entry-4PFUYQMJ.js"),
      "./ion-radio_2.entry.js": () => import("./ion-radio_2.entry-HKL4YHH7.js"),
      "./ion-range.entry.js": () => import("./ion-range.entry-MSGO63X5.js"),
      "./ion-refresher_2.entry.js": () => import("./ion-refresher_2.entry-FQINRL63.js"),
      "./ion-reorder_2.entry.js": () => import("./ion-reorder_2.entry-GKZANI4U.js"),
      "./ion-ripple-effect.entry.js": () => import("./ion-ripple-effect.entry-AQCHNP5R.js"),
      "./ion-route_4.entry.js": () => import("./ion-route_4.entry-FCS2AJCN.js"),
      "./ion-searchbar.entry.js": () => import("./ion-searchbar.entry-KJZL5XOT.js"),
      "./ion-segment-content.entry.js": () => import("./ion-segment-content.entry-3MFLXOCP.js"),
      "./ion-segment-view.entry.js": () => import("./ion-segment-view.entry-LDF4HD3A.js"),
      "./ion-segment_2.entry.js": () => import("./ion-segment_2.entry-2TATTTWN.js"),
      "./ion-select-modal.entry.js": () => import("./ion-select-modal.entry-NMEOA6BI.js"),
      "./ion-select_3.entry.js": () => import("./ion-select_3.entry-2LPPWP6U.js"),
      "./ion-spinner.entry.js": () => import("./ion-spinner.entry-7C22LPI2.js"),
      "./ion-split-pane.entry.js": () => import("./ion-split-pane.entry-F3TWKY4Q.js"),
      "./ion-tab-bar_2.entry.js": () => import("./ion-tab-bar_2.entry-LY7YQE2N.js"),
      "./ion-tab_2.entry.js": () => import("./ion-tab_2.entry-RH4GTA2N.js"),
      "./ion-text.entry.js": () => import("./ion-text.entry-URN2N2RD.js"),
      "./ion-textarea.entry.js": () => import("./ion-textarea.entry-5M4PN7KG.js"),
      "./ion-toast.entry.js": () => import("./ion-toast.entry-4SRO7YET.js"),
      "./ion-toggle.entry.js": () => import("./ion-toggle.entry-2YUEVBC5.js")
    });
  }
});

// node_modules/@ionic/core/dist/esm/index-527b9e34.js
function queryNonceMetaTagContent(doc2) {
  var _a, _b, _c;
  return (_c = (_b = (_a = doc2.head) == null ? void 0 : _a.querySelector('meta[name="csp-nonce"]')) == null ? void 0 : _b.getAttribute("content")) != null ? _c : void 0;
}
function map(result, fn) {
  if (result.isOk) {
    const val = fn(result.value);
    if (val instanceof Promise) {
      return val.then((newVal) => ok(newVal));
    } else {
      return ok(val);
    }
  }
  if (result.isErr) {
    const value = result.value;
    return err(value);
  }
  throw "should never get here";
}
function sortedAttrNames(attrNames) {
  return attrNames.includes("ref") ? (
    // we need to sort these to ensure that `'ref'` is the last attr
    [...attrNames.filter((attr) => attr !== "ref"), "ref"]
  ) : (
    // no need to sort, return the original array
    attrNames
  );
}
var NAMESPACE, BUILD, __defProp, __export, Build, hostRefs, getHostRef, registerInstance, registerHost, isMemberInElement, consoleError, cmpModules, loadModule, styles, modeResolutionChain, CONTENT_REF_ID, ORG_LOCATION_ID, SLOT_NODE_ID, TEXT_NODE_ID, HYDRATE_ID, HYDRATED_STYLE_ID, HYDRATE_CHILD_ID, HYDRATED_CSS, SLOT_FB_CSS, XLINK_NS, win, doc, H, plt, supportsShadow, supportsListenerOptions, promiseResolve, supportsConstructableStylesheets, queuePending, queueDomReads, queueDomWrites, queueTask, consume, flush, nextTick, readTask, writeTask, getAssetPath, EMPTY_OBJ, SVG_NS, HTML_NS, isDef, isComplexType, result_exports, ok, err, unwrap, unwrapErr, createTime, uniqueTime, h, newVNode, Host, isHost, vdomFnUtils, convertToPublic, convertToPrivate, initializeClientHydrate, clientHydrate, initializeDocumentHydrate, computeMode, setMode, getMode, parsePropertyValue, getElement, createEvent, emitEvent, rootAppliedStyles, registerStyle, addStyle, attachStyles, getScopeId, setAccessor, parseClassListRegex, parseClassList, CAPTURE_EVENT_SUFFIX, CAPTURE_EVENT_REGEX, updateElement, scopeId, contentRef, hostTagName, useNativeShadowDom, checkSlotFallbackVisibility, checkSlotRelocate, isSvgMode, createElm, relocateToHostRoot, putBackInOriginalLocation, addVnodes, removeVnodes, updateChildren, isSameVnode, referenceNode, parentReferenceNode, patch, updateFallbackSlotVisibility, relocateNodes, markSlotContentForRelocation, isNodeLocatedInSlot, nullifyVNodeRefs, insertBefore, findScopeIds, updateElementScopeIds, renderVdom, attachToAncestor, scheduleUpdate, dispatchHooks, enqueue, isPromisey, updateComponent, callRender, postUpdateComponent, forceUpdate, appDidLoad, safeCall, addHydratedFlag, getValue, setValue, proxyComponent, initializeComponent, fireConnectedCallback, connectedCallback, setContentReference, disconnectInstance, disconnectedCallback, patchPseudoShadowDom, patchCloneNode, patchSlotAppendChild, patchSlotRemoveChild, patchSlotPrepend, patchSlotAppend, patchSlotInsertAdjacentHTML, patchSlotInsertAdjacentText, patchSlotInsertAdjacentElement, patchTextContent, patchChildSlotNodes, getAllChildSlotNodes, getSlotName, getHostSlotNode, getHostSlotChildNodes, bootstrapLazy, addHostEventListeners, hostListenerProxy, getHostListenerTarget, hostListenerOpts;
var init_index_527b9e34 = __esm({
  "node_modules/@ionic/core/dist/esm/index-527b9e34.js"() {
    "use strict";
    /* @vite-ignore */
    /* webpackInclude: /\.entry\.js$/ */
    /* webpackExclude: /\.system\.entry\.js$/ */
    /* webpackMode: "lazy" */
    init_();
    NAMESPACE = "ionic";
    BUILD = /* ionic */
    {
      allRenderFn: false,
      appendChildSlotFix: true,
      asyncLoading: true,
      asyncQueue: false,
      attachStyles: true,
      cloneNodeFix: true,
      cmpDidLoad: true,
      cmpDidRender: true,
      cmpDidUnload: false,
      cmpDidUpdate: true,
      cmpShouldUpdate: false,
      cmpWillLoad: true,
      cmpWillRender: true,
      cmpWillUpdate: false,
      connectedCallback: true,
      constructableCSS: true,
      cssAnnotations: true,
      devTools: false,
      disconnectedCallback: true,
      element: false,
      event: true,
      experimentalScopedSlotChanges: true,
      experimentalSlotFixes: true,
      formAssociated: false,
      hasRenderFn: true,
      hostListener: true,
      hostListenerTarget: true,
      hostListenerTargetBody: true,
      hostListenerTargetDocument: true,
      hostListenerTargetParent: false,
      hostListenerTargetWindow: true,
      hotModuleReplacement: false,
      hydrateClientSide: true,
      hydrateServerSide: false,
      hydratedAttribute: false,
      hydratedClass: true,
      hydratedSelectorName: "hydrated",
      initializeNextTick: false,
      invisiblePrehydration: true,
      isDebug: false,
      isDev: false,
      isTesting: false,
      lazyLoad: true,
      lifecycle: true,
      lifecycleDOMEvents: false,
      member: true,
      method: true,
      mode: true,
      observeAttribute: true,
      profile: false,
      prop: true,
      propBoolean: true,
      propMutable: true,
      propNumber: true,
      propString: true,
      reflect: true,
      scoped: true,
      scopedSlotTextContentFix: true,
      scriptDataOpts: false,
      shadowDelegatesFocus: true,
      shadowDom: true,
      slot: true,
      slotChildNodesFix: true,
      slotRelocation: true,
      state: true,
      style: true,
      svg: true,
      taskQueue: true,
      transformTagName: false,
      updatable: true,
      vdomAttribute: true,
      vdomClass: true,
      vdomFunctional: true,
      vdomKey: true,
      vdomListener: true,
      vdomPropOrAttr: true,
      vdomRef: true,
      vdomRender: true,
      vdomStyle: true,
      vdomText: true,
      vdomXlink: true,
      watchCallback: true
    };
    __defProp = Object.defineProperty;
    __export = (target, all) => {
      for (var name in all) __defProp(target, name, {
        get: all[name],
        enumerable: true
      });
    };
    Build = {
      isDev: false,
      isBrowser: true,
      isServer: false,
      isTesting: false
    };
    hostRefs = /* @__PURE__ */ new WeakMap();
    getHostRef = (ref) => hostRefs.get(ref);
    registerInstance = (lazyInstance, hostRef) => hostRefs.set(hostRef.$lazyInstance$ = lazyInstance, hostRef);
    registerHost = (hostElement, cmpMeta) => {
      const hostRef = {
        $flags$: 0,
        $hostElement$: hostElement,
        $cmpMeta$: cmpMeta,
        $instanceValues$: /* @__PURE__ */ new Map()
      };
      {
        hostRef.$onInstancePromise$ = new Promise((r) => hostRef.$onInstanceResolve$ = r);
      }
      {
        hostRef.$onReadyPromise$ = new Promise((r) => hostRef.$onReadyResolve$ = r);
        hostElement["s-p"] = [];
        hostElement["s-rc"] = [];
      }
      return hostRefs.set(hostElement, hostRef);
    };
    isMemberInElement = (elm, memberName) => memberName in elm;
    consoleError = (e, el) => (0, console.error)(e, el);
    cmpModules = /* @__PURE__ */ new Map();
    loadModule = (cmpMeta, hostRef, hmrVersionId) => {
      const exportName = cmpMeta.$tagName$.replace(/-/g, "_");
      const bundleId = cmpMeta.$lazyBundleId$;
      if (!bundleId) {
        return void 0;
      }
      const module = cmpModules.get(bundleId);
      if (module) {
        return module[exportName];
      }
      return globImport_entry_js(`./${bundleId}.entry.js${""}`).then((importedModule) => {
        {
          cmpModules.set(bundleId, importedModule);
        }
        return importedModule[exportName];
      }, consoleError);
    };
    styles = /* @__PURE__ */ new Map();
    modeResolutionChain = [];
    CONTENT_REF_ID = "r";
    ORG_LOCATION_ID = "o";
    SLOT_NODE_ID = "s";
    TEXT_NODE_ID = "t";
    HYDRATE_ID = "s-id";
    HYDRATED_STYLE_ID = "sty-id";
    HYDRATE_CHILD_ID = "c-id";
    HYDRATED_CSS = "{visibility:hidden}.hydrated{visibility:inherit}";
    SLOT_FB_CSS = "slot-fb{display:contents}slot-fb[hidden]{display:none}";
    XLINK_NS = "http://www.w3.org/1999/xlink";
    win = typeof window !== "undefined" ? window : {};
    doc = win.document || {
      head: {}
    };
    H = win.HTMLElement || class {
    };
    plt = {
      $flags$: 0,
      $resourcesUrl$: "",
      jmp: (h2) => h2(),
      raf: (h2) => requestAnimationFrame(h2),
      ael: (el, eventName, listener, opts) => el.addEventListener(eventName, listener, opts),
      rel: (el, eventName, listener, opts) => el.removeEventListener(eventName, listener, opts),
      ce: (eventName, opts) => new CustomEvent(eventName, opts)
    };
    supportsShadow = BUILD.shadowDom;
    supportsListenerOptions = /* @__PURE__ */ (() => {
      let supportsListenerOptions2 = false;
      try {
        doc.addEventListener("e", null, Object.defineProperty({}, "passive", {
          get() {
            supportsListenerOptions2 = true;
          }
        }));
      } catch (e) {
      }
      return supportsListenerOptions2;
    })();
    promiseResolve = (v) => Promise.resolve(v);
    supportsConstructableStylesheets = /* @__PURE__ */ (() => {
      try {
        new CSSStyleSheet();
        return typeof new CSSStyleSheet().replaceSync === "function";
      } catch (e) {
      }
      return false;
    })();
    queuePending = false;
    queueDomReads = [];
    queueDomWrites = [];
    queueTask = (queue, write) => (cb) => {
      queue.push(cb);
      if (!queuePending) {
        queuePending = true;
        if (write && plt.$flags$ & 4) {
          nextTick(flush);
        } else {
          plt.raf(flush);
        }
      }
    };
    consume = (queue) => {
      for (let i2 = 0; i2 < queue.length; i2++) {
        try {
          queue[i2](performance.now());
        } catch (e) {
          consoleError(e);
        }
      }
      queue.length = 0;
    };
    flush = () => {
      consume(queueDomReads);
      {
        consume(queueDomWrites);
        if (queuePending = queueDomReads.length > 0) {
          plt.raf(flush);
        }
      }
    };
    nextTick = (cb) => promiseResolve().then(cb);
    readTask = /* @__PURE__ */ queueTask(queueDomReads, false);
    writeTask = /* @__PURE__ */ queueTask(queueDomWrites, true);
    getAssetPath = (path) => {
      const assetUrl = new URL(path, plt.$resourcesUrl$);
      return assetUrl.origin !== win.location.origin ? assetUrl.href : assetUrl.pathname;
    };
    EMPTY_OBJ = {};
    SVG_NS = "http://www.w3.org/2000/svg";
    HTML_NS = "http://www.w3.org/1999/xhtml";
    isDef = (v) => v != null;
    isComplexType = (o) => {
      o = typeof o;
      return o === "object" || o === "function";
    };
    result_exports = {};
    __export(result_exports, {
      err: () => err,
      map: () => map,
      ok: () => ok,
      unwrap: () => unwrap,
      unwrapErr: () => unwrapErr
    });
    ok = (value) => ({
      isOk: true,
      isErr: false,
      value
    });
    err = (value) => ({
      isOk: false,
      isErr: true,
      value
    });
    unwrap = (result) => {
      if (result.isOk) {
        return result.value;
      } else {
        throw result.value;
      }
    };
    unwrapErr = (result) => {
      if (result.isErr) {
        return result.value;
      } else {
        throw result.value;
      }
    };
    createTime = (fnName, tagName = "") => {
      {
        return () => {
          return;
        };
      }
    };
    uniqueTime = (key, measureText) => {
      {
        return () => {
          return;
        };
      }
    };
    h = (nodeName, vnodeData, ...children) => {
      let child = null;
      let key = null;
      let slotName = null;
      let simple = false;
      let lastSimple = false;
      const vNodeChildren = [];
      const walk = (c) => {
        for (let i2 = 0; i2 < c.length; i2++) {
          child = c[i2];
          if (Array.isArray(child)) {
            walk(child);
          } else if (child != null && typeof child !== "boolean") {
            if (simple = typeof nodeName !== "function" && !isComplexType(child)) {
              child = String(child);
            }
            if (simple && lastSimple) {
              vNodeChildren[vNodeChildren.length - 1].$text$ += child;
            } else {
              vNodeChildren.push(simple ? newVNode(null, child) : child);
            }
            lastSimple = simple;
          }
        }
      };
      walk(children);
      if (vnodeData) {
        if (vnodeData.key) {
          key = vnodeData.key;
        }
        if (vnodeData.name) {
          slotName = vnodeData.name;
        }
        {
          const classData = vnodeData.className || vnodeData.class;
          if (classData) {
            vnodeData.class = typeof classData !== "object" ? classData : Object.keys(classData).filter((k) => classData[k]).join(" ");
          }
        }
      }
      if (typeof nodeName === "function") {
        return nodeName(vnodeData === null ? {} : vnodeData, vNodeChildren, vdomFnUtils);
      }
      const vnode = newVNode(nodeName, null);
      vnode.$attrs$ = vnodeData;
      if (vNodeChildren.length > 0) {
        vnode.$children$ = vNodeChildren;
      }
      {
        vnode.$key$ = key;
      }
      {
        vnode.$name$ = slotName;
      }
      return vnode;
    };
    newVNode = (tag, text) => {
      const vnode = {
        $flags$: 0,
        $tag$: tag,
        $text$: text,
        $elm$: null,
        $children$: null
      };
      {
        vnode.$attrs$ = null;
      }
      {
        vnode.$key$ = null;
      }
      {
        vnode.$name$ = null;
      }
      return vnode;
    };
    Host = {};
    isHost = (node) => node && node.$tag$ === Host;
    vdomFnUtils = {
      forEach: (children, cb) => children.map(convertToPublic).forEach(cb),
      map: (children, cb) => children.map(convertToPublic).map(cb).map(convertToPrivate)
    };
    convertToPublic = (node) => ({
      vattrs: node.$attrs$,
      vchildren: node.$children$,
      vkey: node.$key$,
      vname: node.$name$,
      vtag: node.$tag$,
      vtext: node.$text$
    });
    convertToPrivate = (node) => {
      if (typeof node.vtag === "function") {
        const vnodeData = __spreadValues({}, node.vattrs);
        if (node.vkey) {
          vnodeData.key = node.vkey;
        }
        if (node.vname) {
          vnodeData.name = node.vname;
        }
        return h(node.vtag, vnodeData, ...node.vchildren || []);
      }
      const vnode = newVNode(node.vtag, node.vtext);
      vnode.$attrs$ = node.vattrs;
      vnode.$children$ = node.vchildren;
      vnode.$key$ = node.vkey;
      vnode.$name$ = node.vname;
      return vnode;
    };
    initializeClientHydrate = (hostElm, tagName, hostId, hostRef) => {
      const endHydrate = createTime("hydrateClient", tagName);
      const shadowRoot = hostElm.shadowRoot;
      const childRenderNodes = [];
      const slotNodes = [];
      const shadowRootNodes = shadowRoot ? [] : null;
      const vnode = hostRef.$vnode$ = newVNode(tagName, null);
      if (!plt.$orgLocNodes$) {
        initializeDocumentHydrate(doc.body, plt.$orgLocNodes$ = /* @__PURE__ */ new Map());
      }
      hostElm[HYDRATE_ID] = hostId;
      hostElm.removeAttribute(HYDRATE_ID);
      clientHydrate(vnode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, hostElm, hostId);
      childRenderNodes.map((c) => {
        const orgLocationId = c.$hostId$ + "." + c.$nodeId$;
        const orgLocationNode = plt.$orgLocNodes$.get(orgLocationId);
        const node = c.$elm$;
        if (orgLocationNode && supportsShadow && orgLocationNode["s-en"] === "") {
          orgLocationNode.parentNode.insertBefore(node, orgLocationNode.nextSibling);
        }
        if (!shadowRoot) {
          node["s-hn"] = tagName;
          if (orgLocationNode) {
            node["s-ol"] = orgLocationNode;
            node["s-ol"]["s-nr"] = node;
          }
        }
        plt.$orgLocNodes$.delete(orgLocationId);
      });
      if (shadowRoot) {
        shadowRootNodes.map((shadowRootNode) => {
          if (shadowRootNode) {
            shadowRoot.appendChild(shadowRootNode);
          }
        });
      }
      endHydrate();
    };
    clientHydrate = (parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node, hostId) => {
      let childNodeType;
      let childIdSplt;
      let childVNode;
      let i2;
      if (node.nodeType === 1) {
        childNodeType = node.getAttribute(HYDRATE_CHILD_ID);
        if (childNodeType) {
          childIdSplt = childNodeType.split(".");
          if (childIdSplt[0] === hostId || childIdSplt[0] === "0") {
            childVNode = {
              $flags$: 0,
              $hostId$: childIdSplt[0],
              $nodeId$: childIdSplt[1],
              $depth$: childIdSplt[2],
              $index$: childIdSplt[3],
              $tag$: node.tagName.toLowerCase(),
              $elm$: node,
              $attrs$: null,
              $children$: null,
              $key$: null,
              $name$: null,
              $text$: null
            };
            childRenderNodes.push(childVNode);
            node.removeAttribute(HYDRATE_CHILD_ID);
            if (!parentVNode.$children$) {
              parentVNode.$children$ = [];
            }
            parentVNode.$children$[childVNode.$index$] = childVNode;
            parentVNode = childVNode;
            if (shadowRootNodes && childVNode.$depth$ === "0") {
              shadowRootNodes[childVNode.$index$] = childVNode.$elm$;
            }
          }
        }
        if (node.shadowRoot) {
          for (i2 = node.shadowRoot.childNodes.length - 1; i2 >= 0; i2--) {
            clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node.shadowRoot.childNodes[i2], hostId);
          }
        }
        for (i2 = node.childNodes.length - 1; i2 >= 0; i2--) {
          clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node.childNodes[i2], hostId);
        }
      } else if (node.nodeType === 8) {
        childIdSplt = node.nodeValue.split(".");
        if (childIdSplt[1] === hostId || childIdSplt[1] === "0") {
          childNodeType = childIdSplt[0];
          childVNode = {
            $flags$: 0,
            $hostId$: childIdSplt[1],
            $nodeId$: childIdSplt[2],
            $depth$: childIdSplt[3],
            $index$: childIdSplt[4],
            $elm$: node,
            $attrs$: null,
            $children$: null,
            $key$: null,
            $name$: null,
            $tag$: null,
            $text$: null
          };
          if (childNodeType === TEXT_NODE_ID) {
            childVNode.$elm$ = node.nextSibling;
            if (childVNode.$elm$ && childVNode.$elm$.nodeType === 3) {
              childVNode.$text$ = childVNode.$elm$.textContent;
              childRenderNodes.push(childVNode);
              node.remove();
              if (!parentVNode.$children$) {
                parentVNode.$children$ = [];
              }
              parentVNode.$children$[childVNode.$index$] = childVNode;
              if (shadowRootNodes && childVNode.$depth$ === "0") {
                shadowRootNodes[childVNode.$index$] = childVNode.$elm$;
              }
            }
          } else if (childVNode.$hostId$ === hostId) {
            if (childNodeType === SLOT_NODE_ID) {
              childVNode.$tag$ = "slot";
              if (childIdSplt[5]) {
                node["s-sn"] = childVNode.$name$ = childIdSplt[5];
              } else {
                node["s-sn"] = "";
              }
              node["s-sr"] = true;
              if (shadowRootNodes) {
                childVNode.$elm$ = doc.createElement(childVNode.$tag$);
                if (childVNode.$name$) {
                  childVNode.$elm$.setAttribute("name", childVNode.$name$);
                }
                node.parentNode.insertBefore(childVNode.$elm$, node);
                node.remove();
                if (childVNode.$depth$ === "0") {
                  shadowRootNodes[childVNode.$index$] = childVNode.$elm$;
                }
              }
              slotNodes.push(childVNode);
              if (!parentVNode.$children$) {
                parentVNode.$children$ = [];
              }
              parentVNode.$children$[childVNode.$index$] = childVNode;
            } else if (childNodeType === CONTENT_REF_ID) {
              if (shadowRootNodes) {
                node.remove();
              } else {
                hostElm["s-cr"] = node;
                node["s-cn"] = true;
              }
            }
          }
        }
      } else if (parentVNode && parentVNode.$tag$ === "style") {
        const vnode = newVNode(null, node.textContent);
        vnode.$elm$ = node;
        vnode.$index$ = "0";
        parentVNode.$children$ = [vnode];
      }
    };
    initializeDocumentHydrate = (node, orgLocNodes) => {
      if (node.nodeType === 1) {
        let i2 = 0;
        if (node.shadowRoot) {
          for (; i2 < node.shadowRoot.childNodes.length; i2++) {
            initializeDocumentHydrate(node.shadowRoot.childNodes[i2], orgLocNodes);
          }
        }
        for (i2 = 0; i2 < node.childNodes.length; i2++) {
          initializeDocumentHydrate(node.childNodes[i2], orgLocNodes);
        }
      } else if (node.nodeType === 8) {
        const childIdSplt = node.nodeValue.split(".");
        if (childIdSplt[0] === ORG_LOCATION_ID) {
          orgLocNodes.set(childIdSplt[1] + "." + childIdSplt[2], node);
          node.nodeValue = "";
          node["s-en"] = childIdSplt[3];
        }
      }
    };
    computeMode = (elm) => modeResolutionChain.map((h2) => h2(elm)).find((m) => !!m);
    setMode = (handler) => modeResolutionChain.push(handler);
    getMode = (ref) => getHostRef(ref).$modeName$;
    parsePropertyValue = (propValue, propType) => {
      if (propValue != null && !isComplexType(propValue)) {
        if (propType & 4) {
          return propValue === "false" ? false : propValue === "" || !!propValue;
        }
        if (propType & 2) {
          return parseFloat(propValue);
        }
        if (propType & 1) {
          return String(propValue);
        }
        return propValue;
      }
      return propValue;
    };
    getElement = (ref) => getHostRef(ref).$hostElement$;
    createEvent = (ref, name, flags) => {
      const elm = getElement(ref);
      return {
        emit: (detail) => {
          return emitEvent(elm, name, {
            bubbles: !!(flags & 4),
            composed: !!(flags & 2),
            cancelable: !!(flags & 1),
            detail
          });
        }
      };
    };
    emitEvent = (elm, name, opts) => {
      const ev = plt.ce(name, opts);
      elm.dispatchEvent(ev);
      return ev;
    };
    rootAppliedStyles = /* @__PURE__ */ new WeakMap();
    registerStyle = (scopeId2, cssText, allowCS) => {
      let style = styles.get(scopeId2);
      if (supportsConstructableStylesheets && allowCS) {
        style = style || new CSSStyleSheet();
        if (typeof style === "string") {
          style = cssText;
        } else {
          style.replaceSync(cssText);
        }
      } else {
        style = cssText;
      }
      styles.set(scopeId2, style);
    };
    addStyle = (styleContainerNode, cmpMeta, mode) => {
      var _a;
      const scopeId2 = getScopeId(cmpMeta, mode);
      const style = styles.get(scopeId2);
      styleContainerNode = styleContainerNode.nodeType === 11 ? styleContainerNode : doc;
      if (style) {
        if (typeof style === "string") {
          styleContainerNode = styleContainerNode.head || styleContainerNode;
          let appliedStyles = rootAppliedStyles.get(styleContainerNode);
          let styleElm;
          if (!appliedStyles) {
            rootAppliedStyles.set(styleContainerNode, appliedStyles = /* @__PURE__ */ new Set());
          }
          if (!appliedStyles.has(scopeId2)) {
            if (styleContainerNode.host && (styleElm = styleContainerNode.querySelector(`[${HYDRATED_STYLE_ID}="${scopeId2}"]`))) {
              styleElm.innerHTML = style;
            } else {
              styleElm = doc.createElement("style");
              styleElm.innerHTML = style;
              const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(doc);
              if (nonce != null) {
                styleElm.setAttribute("nonce", nonce);
              }
              const injectStyle = (
                /**
                 * we render a scoped component
                 */
                !(cmpMeta.$flags$ & 1) || /**
                * we are using shadow dom and render the style tag within the shadowRoot
                */
                cmpMeta.$flags$ & 1 && styleContainerNode.nodeName !== "HEAD"
              );
              if (injectStyle) {
                styleContainerNode.insertBefore(styleElm, styleContainerNode.querySelector("link"));
              }
            }
            if (cmpMeta.$flags$ & 4) {
              styleElm.innerHTML += SLOT_FB_CSS;
            }
            if (appliedStyles) {
              appliedStyles.add(scopeId2);
            }
          }
        } else if (!styleContainerNode.adoptedStyleSheets.includes(style)) {
          styleContainerNode.adoptedStyleSheets = [...styleContainerNode.adoptedStyleSheets, style];
        }
      }
      return scopeId2;
    };
    attachStyles = (hostRef) => {
      const cmpMeta = hostRef.$cmpMeta$;
      const elm = hostRef.$hostElement$;
      const flags = cmpMeta.$flags$;
      const endAttachStyles = createTime("attachStyles", cmpMeta.$tagName$);
      const scopeId2 = addStyle(elm.shadowRoot ? elm.shadowRoot : elm.getRootNode(), cmpMeta, hostRef.$modeName$);
      if (flags & 10 && flags & 2) {
        elm["s-sc"] = scopeId2;
        elm.classList.add(scopeId2 + "-h");
        if (flags & 2) {
          elm.classList.add(scopeId2 + "-s");
        }
      }
      endAttachStyles();
    };
    getScopeId = (cmp, mode) => "sc-" + (mode && cmp.$flags$ & 32 ? cmp.$tagName$ + "-" + mode : cmp.$tagName$);
    setAccessor = (elm, memberName, oldValue, newValue, isSvg, flags) => {
      if (oldValue !== newValue) {
        let isProp = isMemberInElement(elm, memberName);
        let ln = memberName.toLowerCase();
        if (memberName === "class") {
          const classList = elm.classList;
          const oldClasses = parseClassList(oldValue);
          const newClasses = parseClassList(newValue);
          classList.remove(...oldClasses.filter((c) => c && !newClasses.includes(c)));
          classList.add(...newClasses.filter((c) => c && !oldClasses.includes(c)));
        } else if (memberName === "style") {
          {
            for (const prop in oldValue) {
              if (!newValue || newValue[prop] == null) {
                if (prop.includes("-")) {
                  elm.style.removeProperty(prop);
                } else {
                  elm.style[prop] = "";
                }
              }
            }
          }
          for (const prop in newValue) {
            if (!oldValue || newValue[prop] !== oldValue[prop]) {
              if (prop.includes("-")) {
                elm.style.setProperty(prop, newValue[prop]);
              } else {
                elm.style[prop] = newValue[prop];
              }
            }
          }
        } else if (memberName === "key") ;
        else if (memberName === "ref") {
          if (newValue) {
            newValue(elm);
          }
        } else if (!isProp && memberName[0] === "o" && memberName[1] === "n") {
          if (memberName[2] === "-") {
            memberName = memberName.slice(3);
          } else if (isMemberInElement(win, ln)) {
            memberName = ln.slice(2);
          } else {
            memberName = ln[2] + memberName.slice(3);
          }
          if (oldValue || newValue) {
            const capture = memberName.endsWith(CAPTURE_EVENT_SUFFIX);
            memberName = memberName.replace(CAPTURE_EVENT_REGEX, "");
            if (oldValue) {
              plt.rel(elm, memberName, oldValue, capture);
            }
            if (newValue) {
              plt.ael(elm, memberName, newValue, capture);
            }
          }
        } else {
          const isComplex = isComplexType(newValue);
          if ((isProp || isComplex && newValue !== null) && !isSvg) {
            try {
              if (!elm.tagName.includes("-")) {
                const n = newValue == null ? "" : newValue;
                if (memberName === "list") {
                  isProp = false;
                } else if (oldValue == null || elm[memberName] != n) {
                  elm[memberName] = n;
                }
              } else {
                elm[memberName] = newValue;
              }
            } catch (e) {
            }
          }
          let xlink = false;
          {
            if (ln !== (ln = ln.replace(/^xlink\:?/, ""))) {
              memberName = ln;
              xlink = true;
            }
          }
          if (newValue == null || newValue === false) {
            if (newValue !== false || elm.getAttribute(memberName) === "") {
              if (xlink) {
                elm.removeAttributeNS(XLINK_NS, memberName);
              } else {
                elm.removeAttribute(memberName);
              }
            }
          } else if ((!isProp || flags & 4 || isSvg) && !isComplex) {
            newValue = newValue === true ? "" : newValue;
            if (xlink) {
              elm.setAttributeNS(XLINK_NS, memberName, newValue);
            } else {
              elm.setAttribute(memberName, newValue);
            }
          }
        }
      }
    };
    parseClassListRegex = /\s/;
    parseClassList = (value) => !value ? [] : value.split(parseClassListRegex);
    CAPTURE_EVENT_SUFFIX = "Capture";
    CAPTURE_EVENT_REGEX = new RegExp(CAPTURE_EVENT_SUFFIX + "$");
    updateElement = (oldVnode, newVnode, isSvgMode2) => {
      const elm = newVnode.$elm$.nodeType === 11 && newVnode.$elm$.host ? newVnode.$elm$.host : newVnode.$elm$;
      const oldVnodeAttrs = oldVnode && oldVnode.$attrs$ || EMPTY_OBJ;
      const newVnodeAttrs = newVnode.$attrs$ || EMPTY_OBJ;
      {
        for (const memberName of sortedAttrNames(Object.keys(oldVnodeAttrs))) {
          if (!(memberName in newVnodeAttrs)) {
            setAccessor(elm, memberName, oldVnodeAttrs[memberName], void 0, isSvgMode2, newVnode.$flags$);
          }
        }
      }
      for (const memberName of sortedAttrNames(Object.keys(newVnodeAttrs))) {
        setAccessor(elm, memberName, oldVnodeAttrs[memberName], newVnodeAttrs[memberName], isSvgMode2, newVnode.$flags$);
      }
    };
    useNativeShadowDom = false;
    checkSlotFallbackVisibility = false;
    checkSlotRelocate = false;
    isSvgMode = false;
    createElm = (oldParentVNode, newParentVNode, childIndex, parentElm) => {
      var _a;
      const newVNode2 = newParentVNode.$children$[childIndex];
      let i2 = 0;
      let elm;
      let childNode;
      let oldVNode;
      if (!useNativeShadowDom) {
        checkSlotRelocate = true;
        if (newVNode2.$tag$ === "slot") {
          if (scopeId) {
            parentElm.classList.add(scopeId + "-s");
          }
          newVNode2.$flags$ |= newVNode2.$children$ ? (
            // slot element has fallback content
            // still create an element that "mocks" the slot element
            2
          ) : (
            // slot element does not have fallback content
            // create an html comment we'll use to always reference
            // where actual slot content should sit next to
            1
          );
        }
      }
      if (newVNode2.$text$ !== null) {
        elm = newVNode2.$elm$ = doc.createTextNode(newVNode2.$text$);
      } else if (newVNode2.$flags$ & 1) {
        elm = newVNode2.$elm$ = doc.createTextNode("");
      } else {
        if (!isSvgMode) {
          isSvgMode = newVNode2.$tag$ === "svg";
        }
        elm = newVNode2.$elm$ = doc.createElementNS(isSvgMode ? SVG_NS : HTML_NS, !useNativeShadowDom && BUILD.slotRelocation && newVNode2.$flags$ & 2 ? "slot-fb" : newVNode2.$tag$);
        if (isSvgMode && newVNode2.$tag$ === "foreignObject") {
          isSvgMode = false;
        }
        {
          updateElement(null, newVNode2, isSvgMode);
        }
        const rootNode = elm.getRootNode();
        const isElementWithinShadowRoot = !rootNode.querySelector("body");
        if (!isElementWithinShadowRoot && BUILD.scoped && isDef(scopeId) && elm["s-si"] !== scopeId) {
          elm.classList.add(elm["s-si"] = scopeId);
        }
        {
          updateElementScopeIds(elm, parentElm);
        }
        if (newVNode2.$children$) {
          for (i2 = 0; i2 < newVNode2.$children$.length; ++i2) {
            childNode = createElm(oldParentVNode, newVNode2, i2, elm);
            if (childNode) {
              elm.appendChild(childNode);
            }
          }
        }
        {
          if (newVNode2.$tag$ === "svg") {
            isSvgMode = false;
          } else if (elm.tagName === "foreignObject") {
            isSvgMode = true;
          }
        }
      }
      elm["s-hn"] = hostTagName;
      {
        if (newVNode2.$flags$ & (2 | 1)) {
          elm["s-sr"] = true;
          elm["s-cr"] = contentRef;
          elm["s-sn"] = newVNode2.$name$ || "";
          elm["s-rf"] = (_a = newVNode2.$attrs$) == null ? void 0 : _a.ref;
          oldVNode = oldParentVNode && oldParentVNode.$children$ && oldParentVNode.$children$[childIndex];
          if (oldVNode && oldVNode.$tag$ === newVNode2.$tag$ && oldParentVNode.$elm$) {
            {
              relocateToHostRoot(oldParentVNode.$elm$);
            }
          }
        }
      }
      return elm;
    };
    relocateToHostRoot = (parentElm) => {
      plt.$flags$ |= 1;
      const host = parentElm.closest(hostTagName.toLowerCase());
      if (host != null) {
        const contentRefNode = Array.from(host.childNodes).find((ref) => ref["s-cr"]);
        const childNodeArray = Array.from(parentElm.childNodes);
        for (const childNode of contentRefNode ? childNodeArray.reverse() : childNodeArray) {
          if (childNode["s-sh"] != null) {
            insertBefore(host, childNode, contentRefNode != null ? contentRefNode : null);
            childNode["s-sh"] = void 0;
            checkSlotRelocate = true;
          }
        }
      }
      plt.$flags$ &= ~1;
    };
    putBackInOriginalLocation = (parentElm, recursive) => {
      plt.$flags$ |= 1;
      const oldSlotChildNodes = Array.from(parentElm.childNodes);
      if (parentElm["s-sr"] && BUILD.experimentalSlotFixes) {
        let node = parentElm;
        while (node = node.nextSibling) {
          if (node && node["s-sn"] === parentElm["s-sn"] && node["s-sh"] === hostTagName) {
            oldSlotChildNodes.push(node);
          }
        }
      }
      for (let i2 = oldSlotChildNodes.length - 1; i2 >= 0; i2--) {
        const childNode = oldSlotChildNodes[i2];
        if (childNode["s-hn"] !== hostTagName && childNode["s-ol"]) {
          insertBefore(parentReferenceNode(childNode), childNode, referenceNode(childNode));
          childNode["s-ol"].remove();
          childNode["s-ol"] = void 0;
          childNode["s-sh"] = void 0;
          checkSlotRelocate = true;
        }
        if (recursive) {
          putBackInOriginalLocation(childNode, recursive);
        }
      }
      plt.$flags$ &= ~1;
    };
    addVnodes = (parentElm, before, parentVNode, vnodes, startIdx, endIdx) => {
      let containerElm = parentElm["s-cr"] && parentElm["s-cr"].parentNode || parentElm;
      let childNode;
      if (containerElm.shadowRoot && containerElm.tagName === hostTagName) {
        containerElm = containerElm.shadowRoot;
      }
      for (; startIdx <= endIdx; ++startIdx) {
        if (vnodes[startIdx]) {
          childNode = createElm(null, parentVNode, startIdx, parentElm);
          if (childNode) {
            vnodes[startIdx].$elm$ = childNode;
            insertBefore(containerElm, childNode, referenceNode(before));
          }
        }
      }
    };
    removeVnodes = (vnodes, startIdx, endIdx) => {
      for (let index = startIdx; index <= endIdx; ++index) {
        const vnode = vnodes[index];
        if (vnode) {
          const elm = vnode.$elm$;
          nullifyVNodeRefs(vnode);
          if (elm) {
            {
              checkSlotFallbackVisibility = true;
              if (elm["s-ol"]) {
                elm["s-ol"].remove();
              } else {
                putBackInOriginalLocation(elm, true);
              }
            }
            elm.remove();
          }
        }
      }
    };
    updateChildren = (parentElm, oldCh, newVNode2, newCh, isInitialRender = false) => {
      let oldStartIdx = 0;
      let newStartIdx = 0;
      let idxInOld = 0;
      let i2 = 0;
      let oldEndIdx = oldCh.length - 1;
      let oldStartVnode = oldCh[0];
      let oldEndVnode = oldCh[oldEndIdx];
      let newEndIdx = newCh.length - 1;
      let newStartVnode = newCh[0];
      let newEndVnode = newCh[newEndIdx];
      let node;
      let elmToMove;
      while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {
        if (oldStartVnode == null) {
          oldStartVnode = oldCh[++oldStartIdx];
        } else if (oldEndVnode == null) {
          oldEndVnode = oldCh[--oldEndIdx];
        } else if (newStartVnode == null) {
          newStartVnode = newCh[++newStartIdx];
        } else if (newEndVnode == null) {
          newEndVnode = newCh[--newEndIdx];
        } else if (isSameVnode(oldStartVnode, newStartVnode, isInitialRender)) {
          patch(oldStartVnode, newStartVnode, isInitialRender);
          oldStartVnode = oldCh[++oldStartIdx];
          newStartVnode = newCh[++newStartIdx];
        } else if (isSameVnode(oldEndVnode, newEndVnode, isInitialRender)) {
          patch(oldEndVnode, newEndVnode, isInitialRender);
          oldEndVnode = oldCh[--oldEndIdx];
          newEndVnode = newCh[--newEndIdx];
        } else if (isSameVnode(oldStartVnode, newEndVnode, isInitialRender)) {
          if (oldStartVnode.$tag$ === "slot" || newEndVnode.$tag$ === "slot") {
            putBackInOriginalLocation(oldStartVnode.$elm$.parentNode, false);
          }
          patch(oldStartVnode, newEndVnode, isInitialRender);
          insertBefore(parentElm, oldStartVnode.$elm$, oldEndVnode.$elm$.nextSibling);
          oldStartVnode = oldCh[++oldStartIdx];
          newEndVnode = newCh[--newEndIdx];
        } else if (isSameVnode(oldEndVnode, newStartVnode, isInitialRender)) {
          if (oldStartVnode.$tag$ === "slot" || newEndVnode.$tag$ === "slot") {
            putBackInOriginalLocation(oldEndVnode.$elm$.parentNode, false);
          }
          patch(oldEndVnode, newStartVnode, isInitialRender);
          insertBefore(parentElm, oldEndVnode.$elm$, oldStartVnode.$elm$);
          oldEndVnode = oldCh[--oldEndIdx];
          newStartVnode = newCh[++newStartIdx];
        } else {
          idxInOld = -1;
          {
            for (i2 = oldStartIdx; i2 <= oldEndIdx; ++i2) {
              if (oldCh[i2] && oldCh[i2].$key$ !== null && oldCh[i2].$key$ === newStartVnode.$key$) {
                idxInOld = i2;
                break;
              }
            }
          }
          if (idxInOld >= 0) {
            elmToMove = oldCh[idxInOld];
            if (elmToMove.$tag$ !== newStartVnode.$tag$) {
              node = createElm(oldCh && oldCh[newStartIdx], newVNode2, idxInOld, parentElm);
            } else {
              patch(elmToMove, newStartVnode, isInitialRender);
              oldCh[idxInOld] = void 0;
              node = elmToMove.$elm$;
            }
            newStartVnode = newCh[++newStartIdx];
          } else {
            node = createElm(oldCh && oldCh[newStartIdx], newVNode2, newStartIdx, parentElm);
            newStartVnode = newCh[++newStartIdx];
          }
          if (node) {
            {
              insertBefore(parentReferenceNode(oldStartVnode.$elm$), node, referenceNode(oldStartVnode.$elm$));
            }
          }
        }
      }
      if (oldStartIdx > oldEndIdx) {
        addVnodes(parentElm, newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].$elm$, newVNode2, newCh, newStartIdx, newEndIdx);
      } else if (newStartIdx > newEndIdx) {
        removeVnodes(oldCh, oldStartIdx, oldEndIdx);
      }
    };
    isSameVnode = (leftVNode, rightVNode, isInitialRender = false) => {
      if (leftVNode.$tag$ === rightVNode.$tag$) {
        if (leftVNode.$tag$ === "slot") {
          if (
            // The component gets hydrated and no VDOM has been initialized.
            // Here the comparison can't happen as $name$ property is not set for `leftNode`.
            "$nodeId$" in leftVNode && isInitialRender && // `leftNode` is not from type HTMLComment which would cause many
            // hydration comments to be removed
            leftVNode.$elm$.nodeType !== 8
          ) {
            return false;
          }
          return leftVNode.$name$ === rightVNode.$name$;
        }
        if (!isInitialRender) {
          return leftVNode.$key$ === rightVNode.$key$;
        }
        return true;
      }
      return false;
    };
    referenceNode = (node) => {
      return node && node["s-ol"] || node;
    };
    parentReferenceNode = (node) => (node["s-ol"] ? node["s-ol"] : node).parentNode;
    patch = (oldVNode, newVNode2, isInitialRender = false) => {
      const elm = newVNode2.$elm$ = oldVNode.$elm$;
      const oldChildren = oldVNode.$children$;
      const newChildren = newVNode2.$children$;
      const tag = newVNode2.$tag$;
      const text = newVNode2.$text$;
      let defaultHolder;
      if (text === null) {
        {
          isSvgMode = tag === "svg" ? true : tag === "foreignObject" ? false : isSvgMode;
        }
        {
          if (tag === "slot" && !useNativeShadowDom) {
            if (oldVNode.$name$ !== newVNode2.$name$) {
              newVNode2.$elm$["s-sn"] = newVNode2.$name$ || "";
              relocateToHostRoot(newVNode2.$elm$.parentElement);
            }
          } else {
            updateElement(oldVNode, newVNode2, isSvgMode);
          }
        }
        if (oldChildren !== null && newChildren !== null) {
          updateChildren(elm, oldChildren, newVNode2, newChildren, isInitialRender);
        } else if (newChildren !== null) {
          if (oldVNode.$text$ !== null) {
            elm.textContent = "";
          }
          addVnodes(elm, null, newVNode2, newChildren, 0, newChildren.length - 1);
        } else if (
          // don't do this on initial render as it can cause non-hydrated content to be removed
          !isInitialRender && BUILD.updatable && oldChildren !== null
        ) {
          removeVnodes(oldChildren, 0, oldChildren.length - 1);
        }
        if (isSvgMode && tag === "svg") {
          isSvgMode = false;
        }
      } else if (defaultHolder = elm["s-cr"]) {
        defaultHolder.parentNode.textContent = text;
      } else if (oldVNode.$text$ !== text) {
        elm.data = text;
      }
    };
    updateFallbackSlotVisibility = (elm) => {
      const childNodes = elm.childNodes;
      for (const childNode of childNodes) {
        if (childNode.nodeType === 1) {
          if (childNode["s-sr"]) {
            const slotName = childNode["s-sn"];
            childNode.hidden = false;
            for (const siblingNode of childNodes) {
              if (siblingNode !== childNode) {
                if (siblingNode["s-hn"] !== childNode["s-hn"] || slotName !== "") {
                  if (siblingNode.nodeType === 1 && (slotName === siblingNode.getAttribute("slot") || slotName === siblingNode["s-sn"]) || siblingNode.nodeType === 3 && slotName === siblingNode["s-sn"]) {
                    childNode.hidden = true;
                    break;
                  }
                } else {
                  if (siblingNode.nodeType === 1 || siblingNode.nodeType === 3 && siblingNode.textContent.trim() !== "") {
                    childNode.hidden = true;
                    break;
                  }
                }
              }
            }
          }
          updateFallbackSlotVisibility(childNode);
        }
      }
    };
    relocateNodes = [];
    markSlotContentForRelocation = (elm) => {
      let node;
      let hostContentNodes;
      let j;
      for (const childNode of elm.childNodes) {
        if (childNode["s-sr"] && (node = childNode["s-cr"]) && node.parentNode) {
          hostContentNodes = node.parentNode.childNodes;
          const slotName = childNode["s-sn"];
          for (j = hostContentNodes.length - 1; j >= 0; j--) {
            node = hostContentNodes[j];
            if (!node["s-cn"] && !node["s-nr"] && node["s-hn"] !== childNode["s-hn"] && (!node["s-sh"] || node["s-sh"] !== childNode["s-hn"])) {
              if (isNodeLocatedInSlot(node, slotName)) {
                let relocateNodeData = relocateNodes.find((r) => r.$nodeToRelocate$ === node);
                checkSlotFallbackVisibility = true;
                node["s-sn"] = node["s-sn"] || slotName;
                if (relocateNodeData) {
                  relocateNodeData.$nodeToRelocate$["s-sh"] = childNode["s-hn"];
                  relocateNodeData.$slotRefNode$ = childNode;
                } else {
                  node["s-sh"] = childNode["s-hn"];
                  relocateNodes.push({
                    $slotRefNode$: childNode,
                    $nodeToRelocate$: node
                  });
                }
                if (node["s-sr"]) {
                  relocateNodes.map((relocateNode) => {
                    if (isNodeLocatedInSlot(relocateNode.$nodeToRelocate$, node["s-sn"])) {
                      relocateNodeData = relocateNodes.find((r) => r.$nodeToRelocate$ === node);
                      if (relocateNodeData && !relocateNode.$slotRefNode$) {
                        relocateNode.$slotRefNode$ = relocateNodeData.$slotRefNode$;
                      }
                    }
                  });
                }
              } else if (!relocateNodes.some((r) => r.$nodeToRelocate$ === node)) {
                relocateNodes.push({
                  $nodeToRelocate$: node
                });
              }
            }
          }
        }
        if (childNode.nodeType === 1) {
          markSlotContentForRelocation(childNode);
        }
      }
    };
    isNodeLocatedInSlot = (nodeToRelocate, slotName) => {
      if (nodeToRelocate.nodeType === 1) {
        if (nodeToRelocate.getAttribute("slot") === null && slotName === "") {
          return true;
        }
        if (nodeToRelocate.getAttribute("slot") === slotName) {
          return true;
        }
        return false;
      }
      if (nodeToRelocate["s-sn"] === slotName) {
        return true;
      }
      return slotName === "";
    };
    nullifyVNodeRefs = (vNode) => {
      {
        vNode.$attrs$ && vNode.$attrs$.ref && vNode.$attrs$.ref(null);
        vNode.$children$ && vNode.$children$.map(nullifyVNodeRefs);
      }
    };
    insertBefore = (parent, newNode, reference) => {
      const inserted = parent == null ? void 0 : parent.insertBefore(newNode, reference);
      {
        updateElementScopeIds(newNode, parent);
      }
      return inserted;
    };
    findScopeIds = (element) => {
      const scopeIds = [];
      if (element) {
        scopeIds.push(...element["s-scs"] || [], element["s-si"], element["s-sc"], ...findScopeIds(element.parentElement));
      }
      return scopeIds;
    };
    updateElementScopeIds = (element, parent, iterateChildNodes = false) => {
      var _a;
      if (element && parent && element.nodeType === 1) {
        const scopeIds = new Set(findScopeIds(parent).filter(Boolean));
        if (scopeIds.size) {
          (_a = element.classList) == null ? void 0 : _a.add(...element["s-scs"] = [...scopeIds]);
          if (element["s-ol"] || iterateChildNodes) {
            for (const childNode of Array.from(element.childNodes)) {
              updateElementScopeIds(childNode, element, true);
            }
          }
        }
      }
    };
    renderVdom = (hostRef, renderFnResults, isInitialLoad = false) => {
      var _a, _b, _c, _d, _e;
      const hostElm = hostRef.$hostElement$;
      const cmpMeta = hostRef.$cmpMeta$;
      const oldVNode = hostRef.$vnode$ || newVNode(null, null);
      const rootVnode = isHost(renderFnResults) ? renderFnResults : h(null, null, renderFnResults);
      hostTagName = hostElm.tagName;
      if (cmpMeta.$attrsToReflect$) {
        rootVnode.$attrs$ = rootVnode.$attrs$ || {};
        cmpMeta.$attrsToReflect$.map(([propName, attribute]) => rootVnode.$attrs$[attribute] = hostElm[propName]);
      }
      if (isInitialLoad && rootVnode.$attrs$) {
        for (const key of Object.keys(rootVnode.$attrs$)) {
          if (hostElm.hasAttribute(key) && !["key", "ref", "style", "class"].includes(key)) {
            rootVnode.$attrs$[key] = hostElm[key];
          }
        }
      }
      rootVnode.$tag$ = null;
      rootVnode.$flags$ |= 4;
      hostRef.$vnode$ = rootVnode;
      rootVnode.$elm$ = oldVNode.$elm$ = hostElm.shadowRoot || hostElm;
      {
        scopeId = hostElm["s-sc"];
      }
      useNativeShadowDom = (cmpMeta.$flags$ & 1) !== 0;
      {
        contentRef = hostElm["s-cr"];
        checkSlotFallbackVisibility = false;
      }
      patch(oldVNode, rootVnode, isInitialLoad);
      {
        plt.$flags$ |= 1;
        if (checkSlotRelocate) {
          markSlotContentForRelocation(rootVnode.$elm$);
          for (const relocateData of relocateNodes) {
            const nodeToRelocate = relocateData.$nodeToRelocate$;
            if (!nodeToRelocate["s-ol"]) {
              const orgLocationNode = doc.createTextNode("");
              orgLocationNode["s-nr"] = nodeToRelocate;
              insertBefore(nodeToRelocate.parentNode, nodeToRelocate["s-ol"] = orgLocationNode, nodeToRelocate);
            }
          }
          for (const relocateData of relocateNodes) {
            const nodeToRelocate = relocateData.$nodeToRelocate$;
            const slotRefNode = relocateData.$slotRefNode$;
            if (slotRefNode) {
              const parentNodeRef = slotRefNode.parentNode;
              let insertBeforeNode = slotRefNode.nextSibling;
              if (insertBeforeNode && insertBeforeNode.nodeType === 1) {
                let orgLocationNode = (_a = nodeToRelocate["s-ol"]) == null ? void 0 : _a.previousSibling;
                while (orgLocationNode) {
                  let refNode = (_b = orgLocationNode["s-nr"]) != null ? _b : null;
                  if (refNode && refNode["s-sn"] === nodeToRelocate["s-sn"] && parentNodeRef === refNode.parentNode) {
                    refNode = refNode.nextSibling;
                    while (refNode === nodeToRelocate || (refNode == null ? void 0 : refNode["s-sr"])) {
                      refNode = refNode == null ? void 0 : refNode.nextSibling;
                    }
                    if (!refNode || !refNode["s-nr"]) {
                      insertBeforeNode = refNode;
                      break;
                    }
                  }
                  orgLocationNode = orgLocationNode.previousSibling;
                }
              }
              if (!insertBeforeNode && parentNodeRef !== nodeToRelocate.parentNode || nodeToRelocate.nextSibling !== insertBeforeNode) {
                if (nodeToRelocate !== insertBeforeNode) {
                  insertBefore(parentNodeRef, nodeToRelocate, insertBeforeNode);
                  if (nodeToRelocate.nodeType === 1) {
                    nodeToRelocate.hidden = (_c = nodeToRelocate["s-ih"]) != null ? _c : false;
                  }
                }
              }
              nodeToRelocate && typeof slotRefNode["s-rf"] === "function" && slotRefNode["s-rf"](nodeToRelocate);
            } else {
              if (nodeToRelocate.nodeType === 1) {
                if (isInitialLoad) {
                  nodeToRelocate["s-ih"] = (_d = nodeToRelocate.hidden) != null ? _d : false;
                }
                nodeToRelocate.hidden = true;
              }
            }
          }
        }
        if (checkSlotFallbackVisibility) {
          updateFallbackSlotVisibility(rootVnode.$elm$);
        }
        plt.$flags$ &= ~1;
        relocateNodes.length = 0;
      }
      if (cmpMeta.$flags$ & 2) {
        for (const childNode of rootVnode.$elm$.childNodes) {
          if (childNode["s-hn"] !== hostTagName && !childNode["s-sh"]) {
            if (isInitialLoad && childNode["s-ih"] == null) {
              childNode["s-ih"] = (_e = childNode.hidden) != null ? _e : false;
            }
            childNode.hidden = true;
          }
        }
      }
      contentRef = void 0;
    };
    attachToAncestor = (hostRef, ancestorComponent) => {
      if (ancestorComponent && !hostRef.$onRenderResolve$ && ancestorComponent["s-p"]) {
        ancestorComponent["s-p"].push(new Promise((r) => hostRef.$onRenderResolve$ = r));
      }
    };
    scheduleUpdate = (hostRef, isInitialLoad) => {
      {
        hostRef.$flags$ |= 16;
      }
      if (hostRef.$flags$ & 4) {
        hostRef.$flags$ |= 512;
        return;
      }
      attachToAncestor(hostRef, hostRef.$ancestorComponent$);
      const dispatch = () => dispatchHooks(hostRef, isInitialLoad);
      return writeTask(dispatch);
    };
    dispatchHooks = (hostRef, isInitialLoad) => {
      const elm = hostRef.$hostElement$;
      const endSchedule = createTime("scheduleUpdate", hostRef.$cmpMeta$.$tagName$);
      const instance = hostRef.$lazyInstance$;
      if (!instance) {
        throw new Error(`Can't render component <${elm.tagName.toLowerCase()} /> with invalid Stencil runtime! Make sure this imported component is compiled with a \`externalRuntime: true\` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime`);
      }
      let maybePromise;
      if (isInitialLoad) {
        {
          hostRef.$flags$ |= 256;
          if (hostRef.$queuedListeners$) {
            hostRef.$queuedListeners$.map(([methodName, event]) => safeCall(instance, methodName, event));
            hostRef.$queuedListeners$ = void 0;
          }
        }
        {
          maybePromise = safeCall(instance, "componentWillLoad");
        }
      }
      {
        maybePromise = enqueue(maybePromise, () => safeCall(instance, "componentWillRender"));
      }
      endSchedule();
      return enqueue(maybePromise, () => updateComponent(hostRef, instance, isInitialLoad));
    };
    enqueue = (maybePromise, fn) => isPromisey(maybePromise) ? maybePromise.then(fn).catch((err2) => {
      console.error(err2);
      fn();
    }) : fn();
    isPromisey = (maybePromise) => maybePromise instanceof Promise || maybePromise && maybePromise.then && typeof maybePromise.then === "function";
    updateComponent = (hostRef, instance, isInitialLoad) => __async(null, null, function* () {
      var _a;
      const elm = hostRef.$hostElement$;
      const endUpdate = createTime("update", hostRef.$cmpMeta$.$tagName$);
      const rc = elm["s-rc"];
      if (isInitialLoad) {
        attachStyles(hostRef);
      }
      const endRender = createTime("render", hostRef.$cmpMeta$.$tagName$);
      {
        callRender(hostRef, instance, elm, isInitialLoad);
      }
      if (rc) {
        rc.map((cb) => cb());
        elm["s-rc"] = void 0;
      }
      endRender();
      endUpdate();
      {
        const childrenPromises = (_a = elm["s-p"]) != null ? _a : [];
        const postUpdate = () => postUpdateComponent(hostRef);
        if (childrenPromises.length === 0) {
          postUpdate();
        } else {
          Promise.all(childrenPromises).then(postUpdate);
          hostRef.$flags$ |= 4;
          childrenPromises.length = 0;
        }
      }
    });
    callRender = (hostRef, instance, elm, isInitialLoad) => {
      try {
        instance = instance.render && instance.render();
        {
          hostRef.$flags$ &= ~16;
        }
        {
          hostRef.$flags$ |= 2;
        }
        {
          {
            {
              renderVdom(hostRef, instance, isInitialLoad);
            }
          }
        }
      } catch (e) {
        consoleError(e, hostRef.$hostElement$);
      }
      return null;
    };
    postUpdateComponent = (hostRef) => {
      const tagName = hostRef.$cmpMeta$.$tagName$;
      const elm = hostRef.$hostElement$;
      const endPostUpdate = createTime("postUpdate", tagName);
      const instance = hostRef.$lazyInstance$;
      const ancestorComponent = hostRef.$ancestorComponent$;
      {
        safeCall(instance, "componentDidRender");
      }
      if (!(hostRef.$flags$ & 64)) {
        hostRef.$flags$ |= 64;
        {
          addHydratedFlag(elm);
        }
        {
          safeCall(instance, "componentDidLoad");
        }
        endPostUpdate();
        {
          hostRef.$onReadyResolve$(elm);
          if (!ancestorComponent) {
            appDidLoad();
          }
        }
      } else {
        {
          safeCall(instance, "componentDidUpdate");
        }
        endPostUpdate();
      }
      {
        hostRef.$onInstanceResolve$(elm);
      }
      {
        if (hostRef.$onRenderResolve$) {
          hostRef.$onRenderResolve$();
          hostRef.$onRenderResolve$ = void 0;
        }
        if (hostRef.$flags$ & 512) {
          nextTick(() => scheduleUpdate(hostRef, false));
        }
        hostRef.$flags$ &= ~(4 | 512);
      }
    };
    forceUpdate = (ref) => {
      {
        const hostRef = getHostRef(ref);
        const isConnected = hostRef.$hostElement$.isConnected;
        if (isConnected && (hostRef.$flags$ & (2 | 16)) === 2) {
          scheduleUpdate(hostRef, false);
        }
        return isConnected;
      }
    };
    appDidLoad = (who) => {
      {
        addHydratedFlag(doc.documentElement);
      }
      nextTick(() => emitEvent(win, "appload", {
        detail: {
          namespace: NAMESPACE
        }
      }));
    };
    safeCall = (instance, method, arg) => {
      if (instance && instance[method]) {
        try {
          return instance[method](arg);
        } catch (e) {
          consoleError(e);
        }
      }
      return void 0;
    };
    addHydratedFlag = (elm) => {
      var _a;
      return elm.classList.add((_a = BUILD.hydratedSelectorName) != null ? _a : "hydrated");
    };
    getValue = (ref, propName) => getHostRef(ref).$instanceValues$.get(propName);
    setValue = (ref, propName, newVal, cmpMeta) => {
      const hostRef = getHostRef(ref);
      if (!hostRef) {
        throw new Error(`Couldn't find host element for "${cmpMeta.$tagName$}" as it is unknown to this Stencil runtime. This usually happens when integrating a 3rd party Stencil component with another Stencil component or application. Please reach out to the maintainers of the 3rd party Stencil component or report this on the Stencil Discord server (https://chat.stenciljs.com) or comment on this similar [GitHub issue](https://github.com/ionic-team/stencil/issues/5457).`);
      }
      const elm = hostRef.$hostElement$;
      const oldVal = hostRef.$instanceValues$.get(propName);
      const flags = hostRef.$flags$;
      const instance = hostRef.$lazyInstance$;
      newVal = parsePropertyValue(newVal, cmpMeta.$members$[propName][0]);
      const areBothNaN = Number.isNaN(oldVal) && Number.isNaN(newVal);
      const didValueChange = newVal !== oldVal && !areBothNaN;
      if ((!(flags & 8) || oldVal === void 0) && didValueChange) {
        hostRef.$instanceValues$.set(propName, newVal);
        if (instance) {
          if (cmpMeta.$watchers$ && flags & 128) {
            const watchMethods = cmpMeta.$watchers$[propName];
            if (watchMethods) {
              watchMethods.map((watchMethodName) => {
                try {
                  instance[watchMethodName](newVal, oldVal, propName);
                } catch (e) {
                  consoleError(e, elm);
                }
              });
            }
          }
          if ((flags & (2 | 16)) === 2) {
            scheduleUpdate(hostRef, false);
          }
        }
      }
    };
    proxyComponent = (Cstr, cmpMeta, flags) => {
      var _a, _b;
      const prototype = Cstr.prototype;
      if (cmpMeta.$members$ || cmpMeta.$watchers$ || Cstr.watchers) {
        if (Cstr.watchers && !cmpMeta.$watchers$) {
          cmpMeta.$watchers$ = Cstr.watchers;
        }
        const members = Object.entries((_a = cmpMeta.$members$) != null ? _a : {});
        members.map(([memberName, [memberFlags]]) => {
          if (memberFlags & 31 || flags & 2 && memberFlags & 32) {
            Object.defineProperty(prototype, memberName, {
              get() {
                return getValue(this, memberName);
              },
              set(newValue) {
                setValue(this, memberName, newValue, cmpMeta);
              },
              configurable: true,
              enumerable: true
            });
          } else if (flags & 1 && memberFlags & 64) {
            Object.defineProperty(prototype, memberName, {
              value(...args) {
                var _a2;
                const ref = getHostRef(this);
                return (_a2 = ref == null ? void 0 : ref.$onInstancePromise$) == null ? void 0 : _a2.then(() => {
                  var _a3;
                  return (_a3 = ref.$lazyInstance$) == null ? void 0 : _a3[memberName](...args);
                });
              }
            });
          }
        });
        if (flags & 1) {
          const attrNameToPropName = /* @__PURE__ */ new Map();
          prototype.attributeChangedCallback = function(attrName, oldValue, newValue) {
            plt.jmp(() => {
              var _a2;
              const propName = attrNameToPropName.get(attrName);
              if (this.hasOwnProperty(propName)) {
                newValue = this[propName];
                delete this[propName];
              } else if (prototype.hasOwnProperty(propName) && typeof this[propName] === "number" && // cast type to number to avoid TS compiler issues
              this[propName] == newValue) {
                return;
              } else if (propName == null) {
                const hostRef = getHostRef(this);
                const flags2 = hostRef == null ? void 0 : hostRef.$flags$;
                if (flags2 && !(flags2 & 8) && flags2 & 128 && newValue !== oldValue) {
                  const instance = hostRef.$lazyInstance$;
                  const entry = (_a2 = cmpMeta.$watchers$) == null ? void 0 : _a2[attrName];
                  entry == null ? void 0 : entry.forEach((callbackName) => {
                    if (instance[callbackName] != null) {
                      instance[callbackName].call(instance, newValue, oldValue, attrName);
                    }
                  });
                }
                return;
              }
              this[propName] = newValue === null && typeof this[propName] === "boolean" ? false : newValue;
            });
          };
          Cstr.observedAttributes = Array.from(/* @__PURE__ */ new Set([...Object.keys((_b = cmpMeta.$watchers$) != null ? _b : {}), ...members.filter(
            ([_, m]) => m[0] & 15
            /* HasAttribute */
          ).map(([propName, m]) => {
            var _a2;
            const attrName = m[1] || propName;
            attrNameToPropName.set(attrName, propName);
            if (m[0] & 512) {
              (_a2 = cmpMeta.$attrsToReflect$) == null ? void 0 : _a2.push([propName, attrName]);
            }
            return attrName;
          })]));
        }
      }
      return Cstr;
    };
    initializeComponent = (elm, hostRef, cmpMeta, hmrVersionId) => __async(null, null, function* () {
      let Cstr;
      if ((hostRef.$flags$ & 32) === 0) {
        hostRef.$flags$ |= 32;
        const bundleId = cmpMeta.$lazyBundleId$;
        if (bundleId) {
          const CstrImport = loadModule(cmpMeta);
          if (CstrImport && "then" in CstrImport) {
            const endLoad = uniqueTime();
            Cstr = yield CstrImport;
            endLoad();
          } else {
            Cstr = CstrImport;
          }
          if (!Cstr) {
            throw new Error(`Constructor for "${cmpMeta.$tagName$}#${hostRef.$modeName$}" was not found`);
          }
          if (!Cstr.isProxied) {
            {
              cmpMeta.$watchers$ = Cstr.watchers;
            }
            proxyComponent(
              Cstr,
              cmpMeta,
              2
              /* proxyState */
            );
            Cstr.isProxied = true;
          }
          const endNewInstance = createTime("createInstance", cmpMeta.$tagName$);
          {
            hostRef.$flags$ |= 8;
          }
          try {
            new Cstr(hostRef);
          } catch (e) {
            consoleError(e);
          }
          {
            hostRef.$flags$ &= ~8;
          }
          {
            hostRef.$flags$ |= 128;
          }
          endNewInstance();
          fireConnectedCallback(hostRef.$lazyInstance$);
        } else {
          Cstr = elm.constructor;
          const cmpTag = elm.localName;
          customElements.whenDefined(cmpTag).then(
            () => hostRef.$flags$ |= 128
            /* isWatchReady */
          );
        }
        if (Cstr && Cstr.style) {
          let style;
          if (typeof Cstr.style === "string") {
            style = Cstr.style;
          } else if (typeof Cstr.style !== "string") {
            hostRef.$modeName$ = computeMode(elm);
            if (hostRef.$modeName$) {
              style = Cstr.style[hostRef.$modeName$];
            }
          }
          const scopeId2 = getScopeId(cmpMeta, hostRef.$modeName$);
          if (!styles.has(scopeId2)) {
            const endRegisterStyles = createTime("registerStyles", cmpMeta.$tagName$);
            registerStyle(scopeId2, style, !!(cmpMeta.$flags$ & 1));
            endRegisterStyles();
          }
        }
      }
      const ancestorComponent = hostRef.$ancestorComponent$;
      const schedule = () => scheduleUpdate(hostRef, true);
      if (ancestorComponent && ancestorComponent["s-rc"]) {
        ancestorComponent["s-rc"].push(schedule);
      } else {
        schedule();
      }
    });
    fireConnectedCallback = (instance) => {
      {
        safeCall(instance, "connectedCallback");
      }
    };
    connectedCallback = (elm) => {
      if ((plt.$flags$ & 1) === 0) {
        const hostRef = getHostRef(elm);
        const cmpMeta = hostRef.$cmpMeta$;
        const endConnected = createTime("connectedCallback", cmpMeta.$tagName$);
        if (!(hostRef.$flags$ & 1)) {
          hostRef.$flags$ |= 1;
          let hostId;
          {
            hostId = elm.getAttribute(HYDRATE_ID);
            if (hostId) {
              if (cmpMeta.$flags$ & 1) {
                const scopeId2 = addStyle(elm.shadowRoot, cmpMeta, elm.getAttribute("s-mode"));
                elm.classList.remove(scopeId2 + "-h", scopeId2 + "-s");
              }
              initializeClientHydrate(elm, cmpMeta.$tagName$, hostId, hostRef);
            }
          }
          if (!hostId) {
            if (
              // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field
              cmpMeta.$flags$ & (4 | 8)
            ) {
              setContentReference(elm);
            }
          }
          {
            let ancestorComponent = elm;
            while (ancestorComponent = ancestorComponent.parentNode || ancestorComponent.host) {
              if (ancestorComponent.nodeType === 1 && ancestorComponent.hasAttribute("s-id") && ancestorComponent["s-p"] || ancestorComponent["s-p"]) {
                attachToAncestor(hostRef, hostRef.$ancestorComponent$ = ancestorComponent);
                break;
              }
            }
          }
          if (cmpMeta.$members$) {
            Object.entries(cmpMeta.$members$).map(([memberName, [memberFlags]]) => {
              if (memberFlags & 31 && elm.hasOwnProperty(memberName)) {
                const value = elm[memberName];
                delete elm[memberName];
                elm[memberName] = value;
              }
            });
          }
          {
            initializeComponent(elm, hostRef, cmpMeta);
          }
        } else {
          addHostEventListeners(elm, hostRef, cmpMeta.$listeners$);
          if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {
            fireConnectedCallback(hostRef.$lazyInstance$);
          } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {
            hostRef.$onReadyPromise$.then(() => fireConnectedCallback(hostRef.$lazyInstance$));
          }
        }
        endConnected();
      }
    };
    setContentReference = (elm) => {
      const contentRefElm = elm["s-cr"] = doc.createComment("");
      contentRefElm["s-cn"] = true;
      insertBefore(elm, contentRefElm, elm.firstChild);
    };
    disconnectInstance = (instance) => {
      {
        safeCall(instance, "disconnectedCallback");
      }
    };
    disconnectedCallback = (elm) => __async(null, null, function* () {
      if ((plt.$flags$ & 1) === 0) {
        const hostRef = getHostRef(elm);
        {
          if (hostRef.$rmListeners$) {
            hostRef.$rmListeners$.map((rmListener) => rmListener());
            hostRef.$rmListeners$ = void 0;
          }
        }
        if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {
          disconnectInstance(hostRef.$lazyInstance$);
        } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {
          hostRef.$onReadyPromise$.then(() => disconnectInstance(hostRef.$lazyInstance$));
        }
      }
    });
    patchPseudoShadowDom = (hostElementPrototype, descriptorPrototype) => {
      patchCloneNode(hostElementPrototype);
      patchSlotAppendChild(hostElementPrototype);
      patchSlotAppend(hostElementPrototype);
      patchSlotPrepend(hostElementPrototype);
      patchSlotInsertAdjacentElement(hostElementPrototype);
      patchSlotInsertAdjacentHTML(hostElementPrototype);
      patchSlotInsertAdjacentText(hostElementPrototype);
      patchTextContent(hostElementPrototype);
      patchChildSlotNodes(hostElementPrototype, descriptorPrototype);
      patchSlotRemoveChild(hostElementPrototype);
    };
    patchCloneNode = (HostElementPrototype) => {
      const orgCloneNode = HostElementPrototype.cloneNode;
      HostElementPrototype.cloneNode = function(deep) {
        const srcNode = this;
        const isShadowDom = srcNode.shadowRoot && supportsShadow;
        const clonedNode = orgCloneNode.call(srcNode, isShadowDom ? deep : false);
        if (!isShadowDom && deep) {
          let i2 = 0;
          let slotted, nonStencilNode;
          const stencilPrivates = ["s-id", "s-cr", "s-lr", "s-rc", "s-sc", "s-p", "s-cn", "s-sr", "s-sn", "s-hn", "s-ol", "s-nr", "s-si", "s-rf", "s-scs"];
          for (; i2 < srcNode.childNodes.length; i2++) {
            slotted = srcNode.childNodes[i2]["s-nr"];
            nonStencilNode = stencilPrivates.every((privateField) => !srcNode.childNodes[i2][privateField]);
            if (slotted) {
              if (clonedNode.__appendChild) {
                clonedNode.__appendChild(slotted.cloneNode(true));
              } else {
                clonedNode.appendChild(slotted.cloneNode(true));
              }
            }
            if (nonStencilNode) {
              clonedNode.appendChild(srcNode.childNodes[i2].cloneNode(true));
            }
          }
        }
        return clonedNode;
      };
    };
    patchSlotAppendChild = (HostElementPrototype) => {
      HostElementPrototype.__appendChild = HostElementPrototype.appendChild;
      HostElementPrototype.appendChild = function(newChild) {
        const slotName = newChild["s-sn"] = getSlotName(newChild);
        const slotNode = getHostSlotNode(this.childNodes, slotName, this.tagName);
        if (slotNode) {
          const slotChildNodes = getHostSlotChildNodes(slotNode, slotName);
          const appendAfter = slotChildNodes[slotChildNodes.length - 1];
          const insertedNode = insertBefore(appendAfter.parentNode, newChild, appendAfter.nextSibling);
          updateFallbackSlotVisibility(this);
          return insertedNode;
        }
        return this.__appendChild(newChild);
      };
    };
    patchSlotRemoveChild = (ElementPrototype) => {
      ElementPrototype.__removeChild = ElementPrototype.removeChild;
      ElementPrototype.removeChild = function(toRemove) {
        if (toRemove && typeof toRemove["s-sn"] !== "undefined") {
          const slotNode = getHostSlotNode(this.childNodes, toRemove["s-sn"], this.tagName);
          if (slotNode) {
            const slotChildNodes = getHostSlotChildNodes(slotNode, toRemove["s-sn"]);
            const existingNode = slotChildNodes.find((n) => n === toRemove);
            if (existingNode) {
              existingNode.remove();
              updateFallbackSlotVisibility(this);
              return;
            }
          }
        }
        return this.__removeChild(toRemove);
      };
    };
    patchSlotPrepend = (HostElementPrototype) => {
      const originalPrepend = HostElementPrototype.prepend;
      HostElementPrototype.prepend = function(...newChildren) {
        newChildren.forEach((newChild) => {
          if (typeof newChild === "string") {
            newChild = this.ownerDocument.createTextNode(newChild);
          }
          const slotName = newChild["s-sn"] = getSlotName(newChild);
          const slotNode = getHostSlotNode(this.childNodes, slotName, this.tagName);
          if (slotNode) {
            const slotPlaceholder = document.createTextNode("");
            slotPlaceholder["s-nr"] = newChild;
            slotNode["s-cr"].parentNode.__appendChild(slotPlaceholder);
            newChild["s-ol"] = slotPlaceholder;
            const slotChildNodes = getHostSlotChildNodes(slotNode, slotName);
            const appendAfter = slotChildNodes[0];
            return insertBefore(appendAfter.parentNode, newChild, appendAfter.nextSibling);
          }
          if (newChild.nodeType === 1 && !!newChild.getAttribute("slot")) {
            newChild.hidden = true;
          }
          return originalPrepend.call(this, newChild);
        });
      };
    };
    patchSlotAppend = (HostElementPrototype) => {
      HostElementPrototype.append = function(...newChildren) {
        newChildren.forEach((newChild) => {
          if (typeof newChild === "string") {
            newChild = this.ownerDocument.createTextNode(newChild);
          }
          this.appendChild(newChild);
        });
      };
    };
    patchSlotInsertAdjacentHTML = (HostElementPrototype) => {
      const originalInsertAdjacentHtml = HostElementPrototype.insertAdjacentHTML;
      HostElementPrototype.insertAdjacentHTML = function(position, text) {
        if (position !== "afterbegin" && position !== "beforeend") {
          return originalInsertAdjacentHtml.call(this, position, text);
        }
        const container = this.ownerDocument.createElement("_");
        let node;
        container.innerHTML = text;
        if (position === "afterbegin") {
          while (node = container.firstChild) {
            this.prepend(node);
          }
        } else if (position === "beforeend") {
          while (node = container.firstChild) {
            this.append(node);
          }
        }
      };
    };
    patchSlotInsertAdjacentText = (HostElementPrototype) => {
      HostElementPrototype.insertAdjacentText = function(position, text) {
        this.insertAdjacentHTML(position, text);
      };
    };
    patchSlotInsertAdjacentElement = (HostElementPrototype) => {
      const originalInsertAdjacentElement = HostElementPrototype.insertAdjacentElement;
      HostElementPrototype.insertAdjacentElement = function(position, element) {
        if (position !== "afterbegin" && position !== "beforeend") {
          return originalInsertAdjacentElement.call(this, position, element);
        }
        if (position === "afterbegin") {
          this.prepend(element);
          return element;
        } else if (position === "beforeend") {
          this.append(element);
          return element;
        }
        return element;
      };
    };
    patchTextContent = (hostElementPrototype) => {
      const descriptor = Object.getOwnPropertyDescriptor(Node.prototype, "textContent");
      Object.defineProperty(hostElementPrototype, "__textContent", descriptor);
      {
        Object.defineProperty(hostElementPrototype, "textContent", {
          // To mimic shadow root behavior, we need to return the text content of all
          // nodes in a slot reference node
          get() {
            const slotRefNodes = getAllChildSlotNodes(this.childNodes);
            const textContent = slotRefNodes.map((node) => {
              var _a, _b;
              const text = [];
              let slotContent = node.nextSibling;
              while (slotContent && slotContent["s-sn"] === node["s-sn"]) {
                if (slotContent.nodeType === 3 || slotContent.nodeType === 1) {
                  text.push((_b = (_a = slotContent.textContent) == null ? void 0 : _a.trim()) != null ? _b : "");
                }
                slotContent = slotContent.nextSibling;
              }
              return text.filter((ref) => ref !== "").join(" ");
            }).filter((text) => text !== "").join(" ");
            return " " + textContent + " ";
          },
          // To mimic shadow root behavior, we need to overwrite all nodes in a slot
          // reference node. If a default slot reference node exists, the text content will be
          // placed there. Otherwise, the new text node will be hidden
          set(value) {
            const slotRefNodes = getAllChildSlotNodes(this.childNodes);
            slotRefNodes.forEach((node) => {
              let slotContent = node.nextSibling;
              while (slotContent && slotContent["s-sn"] === node["s-sn"]) {
                const tmp = slotContent;
                slotContent = slotContent.nextSibling;
                tmp.remove();
              }
              if (node["s-sn"] === "") {
                const textNode = this.ownerDocument.createTextNode(value);
                textNode["s-sn"] = "";
                insertBefore(node.parentElement, textNode, node.nextSibling);
              } else {
                node.remove();
              }
            });
          }
        });
      }
    };
    patchChildSlotNodes = (elm, cmpMeta) => {
      class FakeNodeList extends Array {
        item(n) {
          return this[n];
        }
      }
      if (cmpMeta.$flags$ & 8) {
        const childNodesFn = elm.__lookupGetter__("childNodes");
        Object.defineProperty(elm, "children", {
          get() {
            return this.childNodes.map((n) => n.nodeType === 1);
          }
        });
        Object.defineProperty(elm, "childElementCount", {
          get() {
            return elm.children.length;
          }
        });
        Object.defineProperty(elm, "childNodes", {
          get() {
            const childNodes = childNodesFn.call(this);
            if ((plt.$flags$ & 1) === 0 && getHostRef(this).$flags$ & 2) {
              const result = new FakeNodeList();
              for (let i2 = 0; i2 < childNodes.length; i2++) {
                const slot = childNodes[i2]["s-nr"];
                if (slot) {
                  result.push(slot);
                }
              }
              return result;
            }
            return FakeNodeList.from(childNodes);
          }
        });
      }
    };
    getAllChildSlotNodes = (childNodes) => {
      const slotRefNodes = [];
      for (const childNode of Array.from(childNodes)) {
        if (childNode["s-sr"]) {
          slotRefNodes.push(childNode);
        }
        slotRefNodes.push(...getAllChildSlotNodes(childNode.childNodes));
      }
      return slotRefNodes;
    };
    getSlotName = (node) => node["s-sn"] || node.nodeType === 1 && node.getAttribute("slot") || "";
    getHostSlotNode = (childNodes, slotName, hostName) => {
      let i2 = 0;
      let childNode;
      for (; i2 < childNodes.length; i2++) {
        childNode = childNodes[i2];
        if (childNode["s-sr"] && childNode["s-sn"] === slotName && childNode["s-hn"] === hostName) {
          return childNode;
        }
        childNode = getHostSlotNode(childNode.childNodes, slotName, hostName);
        if (childNode) {
          return childNode;
        }
      }
      return null;
    };
    getHostSlotChildNodes = (n, slotName) => {
      const childNodes = [n];
      while ((n = n.nextSibling) && n["s-sn"] === slotName) {
        childNodes.push(n);
      }
      return childNodes;
    };
    bootstrapLazy = (lazyBundles, options = {}) => {
      var _a;
      const endBootstrap = createTime();
      const cmpTags = [];
      const exclude = options.exclude || [];
      const customElements2 = win.customElements;
      const head = doc.head;
      const metaCharset = /* @__PURE__ */ head.querySelector("meta[charset]");
      const dataStyles = /* @__PURE__ */ doc.createElement("style");
      const deferredConnectedCallbacks = [];
      let appLoadFallback;
      let isBootstrapping = true;
      Object.assign(plt, options);
      plt.$resourcesUrl$ = new URL(options.resourcesUrl || "./", doc.baseURI).href;
      {
        plt.$flags$ |= 2;
      }
      let hasSlotRelocation = false;
      lazyBundles.map((lazyBundle) => {
        lazyBundle[1].map((compactMeta) => {
          var _a2;
          const cmpMeta = {
            $flags$: compactMeta[0],
            $tagName$: compactMeta[1],
            $members$: compactMeta[2],
            $listeners$: compactMeta[3]
          };
          if (cmpMeta.$flags$ & 4) {
            hasSlotRelocation = true;
          }
          {
            cmpMeta.$members$ = compactMeta[2];
          }
          {
            cmpMeta.$listeners$ = compactMeta[3];
          }
          {
            cmpMeta.$attrsToReflect$ = [];
          }
          {
            cmpMeta.$watchers$ = (_a2 = compactMeta[4]) != null ? _a2 : {};
          }
          const tagName = cmpMeta.$tagName$;
          const HostElement = class extends HTMLElement {
            // StencilLazyHost
            constructor(self) {
              super(self);
              this.hasRegisteredEventListeners = false;
              self = this;
              registerHost(self, cmpMeta);
              if (cmpMeta.$flags$ & 1) {
                {
                  if (!self.shadowRoot) {
                    {
                      self.attachShadow({
                        mode: "open",
                        delegatesFocus: !!(cmpMeta.$flags$ & 16)
                      });
                    }
                  } else {
                    if (self.shadowRoot.mode !== "open") {
                      throw new Error(`Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${self.shadowRoot.mode} but Stencil only supports open shadow roots.`);
                    }
                  }
                }
              }
            }
            connectedCallback() {
              const hostRef = getHostRef(this);
              if (!this.hasRegisteredEventListeners) {
                this.hasRegisteredEventListeners = true;
                addHostEventListeners(this, hostRef, cmpMeta.$listeners$);
              }
              if (appLoadFallback) {
                clearTimeout(appLoadFallback);
                appLoadFallback = null;
              }
              if (isBootstrapping) {
                deferredConnectedCallbacks.push(this);
              } else {
                plt.jmp(() => connectedCallback(this));
              }
            }
            disconnectedCallback() {
              plt.jmp(() => disconnectedCallback(this));
            }
            componentOnReady() {
              return getHostRef(this).$onReadyPromise$;
            }
          };
          {
            if (cmpMeta.$flags$ & 2) {
              patchPseudoShadowDom(HostElement.prototype, cmpMeta);
            }
          }
          cmpMeta.$lazyBundleId$ = lazyBundle[0];
          if (!exclude.includes(tagName) && !customElements2.get(tagName)) {
            cmpTags.push(tagName);
            customElements2.define(tagName, proxyComponent(
              HostElement,
              cmpMeta,
              1
              /* isElementConstructor */
            ));
          }
        });
      });
      if (cmpTags.length > 0) {
        if (hasSlotRelocation) {
          dataStyles.textContent += SLOT_FB_CSS;
        }
        {
          dataStyles.textContent += cmpTags.sort() + HYDRATED_CSS;
        }
        if (dataStyles.innerHTML.length) {
          dataStyles.setAttribute("data-styles", "");
          const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(doc);
          if (nonce != null) {
            dataStyles.setAttribute("nonce", nonce);
          }
          head.insertBefore(dataStyles, metaCharset ? metaCharset.nextSibling : head.firstChild);
        }
      }
      isBootstrapping = false;
      if (deferredConnectedCallbacks.length) {
        deferredConnectedCallbacks.map((host) => host.connectedCallback());
      } else {
        {
          plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30));
        }
      }
      endBootstrap();
    };
    addHostEventListeners = (elm, hostRef, listeners, attachParentListeners) => {
      if (listeners) {
        listeners.map(([flags, name, method]) => {
          const target = getHostListenerTarget(elm, flags);
          const handler = hostListenerProxy(hostRef, method);
          const opts = hostListenerOpts(flags);
          plt.ael(target, name, handler, opts);
          (hostRef.$rmListeners$ = hostRef.$rmListeners$ || []).push(() => plt.rel(target, name, handler, opts));
        });
      }
    };
    hostListenerProxy = (hostRef, methodName) => (ev) => {
      var _a;
      try {
        {
          if (hostRef.$flags$ & 256) {
            (_a = hostRef.$lazyInstance$) == null ? void 0 : _a[methodName](ev);
          } else {
            (hostRef.$queuedListeners$ = hostRef.$queuedListeners$ || []).push([methodName, ev]);
          }
        }
      } catch (e) {
        consoleError(e);
      }
    };
    getHostListenerTarget = (elm, flags) => {
      if (flags & 4) return doc;
      if (flags & 8) return win;
      if (flags & 16) return doc.body;
      return elm;
    };
    hostListenerOpts = (flags) => supportsListenerOptions ? {
      passive: (flags & 1) !== 0,
      capture: (flags & 2) !== 0
    } : (flags & 2) !== 0;
  }
});

export {
  Build,
  registerInstance,
  readTask,
  writeTask,
  getAssetPath,
  h,
  Host,
  setMode,
  getMode,
  getElement,
  createEvent,
  forceUpdate,
  bootstrapLazy,
  init_index_527b9e34
};
/*! Bundled license information:

@ionic/core/dist/esm/index-527b9e34.js:
  (*!
   * (C) Ionic http://ionicframework.com - MIT License
   *)
  (*!__STENCIL_STATIC_IMPORT_SWITCH__*)
*/
//# sourceMappingURL=chunk-V6QEVD67.js.map
