import {
  ApiService,
  HttpClient,
  Injectable,
  firstValueFrom,
  init_api_service,
  init_core,
  init_esm,
  init_http,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-EC6CHFTM.js";
import {
  __async,
  __esm
} from "./chunk-4W6HR7MY.js";

// src/app/core/services/brand.service.ts
var _BrandService, BrandService;
var init_brand_service = __esm({
  "src/app/core/services/brand.service.ts"() {
    "use strict";
    init_core();
    init_core();
    init_http();
    init_api_service();
    _BrandService = class _BrandService {
      constructor(http, apiService) {
        this.http = http;
        this.apiService = apiService;
      }
      listAll() {
        return __async(this, null, function* () {
          try {
            const url = this.apiService.getUrl("brands");
            const result = yield this.http.get(url).toPromise();
            if (!result)
              return [];
            const brands = result.map((data) => {
              return {
                id: data.id,
                name: data.name,
                ico: data.ico
              };
            });
            return brands;
          } catch (error) {
            console.error("Error fetching brands:", error);
            return [];
          }
        });
      }
    };
    _BrandService.\u0275fac = function BrandService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _BrandService)(\u0275\u0275inject(HttpClient), \u0275\u0275inject(ApiService));
    };
    _BrandService.\u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _BrandService, factory: _BrandService.\u0275fac, providedIn: "root" });
    BrandService = _BrandService;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(BrandService, [{
        type: Injectable,
        args: [{
          providedIn: "root"
        }]
      }], () => [{ type: HttpClient }, { type: ApiService }], null);
    })();
  }
});

// src/app/core/services/model.service.ts
var _ModelService, ModelService;
var init_model_service = __esm({
  "src/app/core/services/model.service.ts"() {
    "use strict";
    init_core();
    init_esm();
    init_core();
    init_http();
    init_api_service();
    _ModelService = class _ModelService {
      constructor(http, apiService) {
        this.http = http;
        this.apiService = apiService;
      }
      getByBranch(brandId) {
        return __async(this, null, function* () {
          try {
            console.log("ModelService.getByBranch called with:", brandId);
            const url = this.apiService.getUrl("models", { brandId: brandId.brandId.toString() });
            console.log("Generated URL:", url);
            const result = yield firstValueFrom(this.http.get(url));
            if (!result)
              return [];
            const models = result.map((data) => {
              return {
                id: data.id,
                name: data.name,
                referenceCode: data.referenceCode,
                referenceMonth: data.referenceMonth,
                shortName: data.shortName,
                brandId: data.brandId
              };
            });
            return models;
          } catch (error) {
            console.error("Error fetching models:", error);
            return [];
          }
        });
      }
    };
    _ModelService.\u0275fac = function ModelService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ModelService)(\u0275\u0275inject(HttpClient), \u0275\u0275inject(ApiService));
    };
    _ModelService.\u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _ModelService, factory: _ModelService.\u0275fac, providedIn: "root" });
    ModelService = _ModelService;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ModelService, [{
        type: Injectable,
        args: [{
          providedIn: "root"
        }]
      }], () => [{ type: HttpClient }, { type: ApiService }], null);
    })();
  }
});

export {
  BrandService,
  init_brand_service,
  ModelService,
  init_model_service
};
//# sourceMappingURL=chunk-35CQJKXP.js.map
