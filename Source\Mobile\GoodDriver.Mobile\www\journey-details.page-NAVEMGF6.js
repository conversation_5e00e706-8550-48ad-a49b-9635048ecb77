import {
  ActivatedRoute,
  CommonModule,
  Component,
  DataStorageService,
  DatePipe,
  DecimalPipe,
  IonBackButton2 as IonBackButton,
  IonButtons,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonContent,
  IonHeader,
  IonItem,
  IonLabel,
  IonList,
  IonListHeader,
  IonTitle,
  IonToolbar,
  IonicModule,
  NgForOf,
  NgIf,
  init_common,
  init_core,
  init_data_storage_service,
  init_ionic_angular,
  init_router,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2
} from "./chunk-L3SP4JFN.js";
import "./chunk-GJIVKQBF.js";
import "./chunk-WY354WYL.js";
import "./chunk-3TAP6XVY.js";
import "./chunk-BQEXAUCY.js";
import "./chunk-EUHNYIV3.js";
import "./chunk-AY6ZGCP5.js";
import "./chunk-I3UCYTIQ.js";
import "./chunk-4JI7VCBZ.js";
import "./chunk-YY7NCLAA.js";
import "./chunk-GUQ63TTB.js";
import "./chunk-CHFZ6R2P.js";
import "./chunk-XPKADKFH.js";
import "./chunk-V6QEVD67.js";
import "./chunk-WNPNB2PX.js";
import "./chunk-NRG74LSH.js";
import "./chunk-YSMEPS6E.js";
import "./chunk-NHEUXSZ2.js";
import "./chunk-WX4TOLMF.js";
import "./chunk-6SXGDLUH.js";
import "./chunk-NNIHVF7T.js";
import "./chunk-ZNALOMZJ.js";
import "./chunk-GHA2GP6C.js";
import "./chunk-H2CU6QHT.js";
import "./chunk-6PMAHZVS.js";
import "./chunk-DOEZTQY3.js";
import "./chunk-O4BCOVEV.js";
import "./chunk-TD4SIQNM.js";
import "./chunk-VU5YACRM.js";
import {
  __async,
  __esm
} from "./chunk-4W6HR7MY.js";

// src/app/pages/tabs/journeys/journey-details/journey-details.page.ts
function JourneyDetailsPage_ion_card_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-card")(1, "ion-card-header")(2, "ion-card-title");
    \u0275\u0275text(3, "Resumo");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(4, "ion-card-content")(5, "p")(6, "strong");
    \u0275\u0275text(7, "In\xEDcio:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(8);
    \u0275\u0275pipe(9, "date");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "p")(11, "strong");
    \u0275\u0275text(12, "Fim:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(13);
    \u0275\u0275pipe(14, "date");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(15, "p")(16, "strong");
    \u0275\u0275text(17, "Dist\xE2ncia:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(18);
    \u0275\u0275pipe(19, "number");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(8);
    \u0275\u0275textInterpolate1(" ", \u0275\u0275pipeBind2(9, 3, ctx_r0.journey.startDate, "short"), "");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1(" ", ctx_r0.journey.endDate ? \u0275\u0275pipeBind2(14, 6, ctx_r0.journey.endDate, "short") : "Em andamento", "");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1(" ", \u0275\u0275pipeBind2(19, 9, ctx_r0.journey.distance, "1.2-2"), " km");
  }
}
function JourneyDetailsPage_div_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 8)(1, "p");
    \u0275\u0275text(2, "Nenhuma localiza\xE7\xE3o registrada para esta viagem.");
    \u0275\u0275elementEnd()();
  }
}
function JourneyDetailsPage_ion_item_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-item")(1, "ion-label")(2, "h3");
    \u0275\u0275text(3);
    \u0275\u0275pipe(4, "date");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "p")(8, "strong");
    \u0275\u0275text(9, "Endere\xE7o:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(10);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const info_r2 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(4, 4, info_r2.timestamp, "short"));
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate2("Latitude: ", info_r2.latitude, ", Longitude: ", info_r2.longitude, "");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", info_r2.address, "");
  }
}
var _JourneyDetailsPage, JourneyDetailsPage;
var init_journey_details_page = __esm({
  "src/app/pages/tabs/journeys/journey-details/journey-details.page.ts"() {
    init_core();
    init_ionic_angular();
    init_common();
    init_core();
    init_router();
    init_data_storage_service();
    init_ionic_angular();
    init_common();
    _JourneyDetailsPage = class _JourneyDetailsPage {
      constructor(route, dataStorageService) {
        this.route = route;
        this.dataStorageService = dataStorageService;
      }
      ngOnInit() {
        return __async(this, null, function* () {
          var _a;
          const journeyId = this.route.snapshot.paramMap.get("id");
          const journeyStr = this.route.snapshot.queryParamMap.get("data");
          if (journeyStr) {
            this.journey = JSON.parse(journeyStr);
            (_a = this.journey.infosJourney) == null ? void 0 : _a.map((info) => __async(this, null, function* () {
              info.address = yield this.reverseGeocode(info.latitude, info.longitude);
              return info;
            }));
          }
        });
      }
      reverseGeocode(lat, lon) {
        return __async(this, null, function* () {
          const url = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lon}`;
          const response = yield fetch(url);
          const data = yield response.json();
          return data.display_name || "Endere\xE7o n\xE3o encontrado";
        });
      }
    };
    _JourneyDetailsPage.\u0275fac = function JourneyDetailsPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _JourneyDetailsPage)(\u0275\u0275directiveInject(ActivatedRoute), \u0275\u0275directiveInject(DataStorageService));
    };
    _JourneyDetailsPage.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _JourneyDetailsPage, selectors: [["app-journey-details"]], decls: 13, vars: 4, consts: [[3, "translucent"], ["color", "primary"], ["slot", "start"], ["defaultHref", "/tabs/journeys"], [1, "ion-padding"], [4, "ngIf"], ["class", "ion-text-center ion-padding", 4, "ngIf"], [4, "ngFor", "ngForOf"], [1, "ion-text-center", "ion-padding"]], template: function JourneyDetailsPage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header", 0)(1, "ion-toolbar", 1)(2, "ion-title");
        \u0275\u0275text(3, "Detalhes da Viagem");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(4, "ion-buttons", 2);
        \u0275\u0275element(5, "ion-back-button", 3);
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(6, "ion-content", 4);
        \u0275\u0275template(7, JourneyDetailsPage_ion_card_7_Template, 20, 12, "ion-card", 5);
        \u0275\u0275elementStart(8, "ion-list")(9, "ion-list-header");
        \u0275\u0275text(10, " Localiza\xE7\xF5es Registradas ");
        \u0275\u0275elementEnd();
        \u0275\u0275template(11, JourneyDetailsPage_div_11_Template, 3, 0, "div", 6)(12, JourneyDetailsPage_ion_item_12_Template, 11, 7, "ion-item", 7);
        \u0275\u0275elementEnd()();
      }
      if (rf & 2) {
        \u0275\u0275property("translucent", true);
        \u0275\u0275advance(7);
        \u0275\u0275property("ngIf", ctx.journey);
        \u0275\u0275advance(4);
        \u0275\u0275property("ngIf", (ctx.journey.infosJourney == null ? null : ctx.journey.infosJourney.length) === 0);
        \u0275\u0275advance();
        \u0275\u0275property("ngForOf", ctx.journey.infosJourney);
      }
    }, dependencies: [IonicModule, IonButtons, IonCard, IonCardContent, IonCardHeader, IonCardTitle, IonContent, IonHeader, IonItem, IonLabel, IonList, IonListHeader, IonTitle, IonToolbar, IonBackButton, CommonModule, NgForOf, NgIf, DecimalPipe, DatePipe], encapsulation: 2 });
    JourneyDetailsPage = _JourneyDetailsPage;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(JourneyDetailsPage, [{
        type: Component,
        args: [{ selector: "app-journey-details", standalone: true, imports: [
          IonicModule,
          CommonModule
        ], template: `<ion-header [translucent]="true">
  <ion-toolbar color="primary">
    <ion-title>Detalhes da Viagem</ion-title>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/tabs/journeys"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">

  <ion-card *ngIf="journey">
    <ion-card-header>
      <ion-card-title>Resumo</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <p><strong>In\xEDcio:</strong> {{ journey.startDate | date:'short' }}</p>
      <p><strong>Fim:</strong> {{ journey.endDate ? (journey.endDate | date:'short') : 'Em andamento' }}</p>
      <p><strong>Dist\xE2ncia:</strong> {{ journey.distance | number:'1.2-2' }} km</p>
    </ion-card-content>
  </ion-card>

  <ion-list>
    <ion-list-header>
      Localiza\xE7\xF5es Registradas
    </ion-list-header>

    <div *ngIf="journey.infosJourney?.length === 0" class="ion-text-center ion-padding">
      <p>Nenhuma localiza\xE7\xE3o registrada para esta viagem.</p>
    </div>

    <ion-item *ngFor="let info of journey.infosJourney">
      <ion-label>
        <h3>{{ info.timestamp | date:'short' }}</h3>
        <p>Latitude: {{ info.latitude }}, Longitude: {{ info.longitude }}</p>
        <p><strong>Endere\xE7o:</strong> {{ info.address }}</p>
      </ion-label>
    </ion-item>
  </ion-list>

</ion-content>
` }]
      }], () => [{ type: ActivatedRoute }, { type: DataStorageService }], null);
    })();
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(JourneyDetailsPage, { className: "JourneyDetailsPage", filePath: "src/app/pages/tabs/journeys/journey-details/journey-details.page.ts", lineNumber: 20 });
    })();
  }
});
init_journey_details_page();
export {
  JourneyDetailsPage
};
//# sourceMappingURL=journey-details.page-NAVEMGF6.js.map
