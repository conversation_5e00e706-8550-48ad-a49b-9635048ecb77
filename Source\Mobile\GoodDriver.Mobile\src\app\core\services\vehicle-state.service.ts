import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Vehicle } from '../models/vehicle.model';

@Injectable({
  providedIn: 'root'
})
export class VehicleStateService {
  // BehaviorSubject to track the primary vehicle
  private primaryVehicleSubject = new BehaviorSubject<Vehicle | null>(null);
  
  // Observable that components can subscribe to
  public primaryVehicle$: Observable<Vehicle | null> = this.primaryVehicleSubject.asObservable();
  
  // BehaviorSubject to track all vehicles
  private vehiclesSubject = new BehaviorSubject<Vehicle[]>([]);
  
  // Observable that components can subscribe to
  public vehicles$: Observable<Vehicle[]> = this.vehiclesSubject.asObservable();
  
  constructor() {}
  
  /**
   * Updates the primary vehicle
   * @param vehicle The new primary vehicle
   */
  updatePrimaryVehicle(vehicle: Vehicle | null): void {
    this.primaryVehicleSubject.next(vehicle);
  }
  
  /**
   * Updates the list of vehicles
   * @param vehicles The new list of vehicles
   */
  updateVehicles(vehicles: Vehicle[]): void {
    this.vehiclesSubject.next(vehicles);
    
    // Also update the primary vehicle if it exists in the list
    const primaryVehicle = vehicles.find(v => v.isPrimary);
    if (primaryVehicle) {
      this.updatePrimaryVehicle(primaryVehicle);
    }
  }
  
  /**
   * Gets the current primary vehicle
   * @returns The current primary vehicle or null
   */
  getCurrentPrimaryVehicle(): Vehicle | null {
    return this.primaryVehicleSubject.getValue();
  }
  
  /**
   * Gets the current list of vehicles
   * @returns The current list of vehicles
   */
  getCurrentVehicles(): Vehicle[] {
    return this.vehiclesSubject.getValue();
  }
}
