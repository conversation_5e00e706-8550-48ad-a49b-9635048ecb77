/* node_modules/@ionic/angular/css/core.css */
:root {
  --ion-color-primary: #0054e9;
  --ion-color-primary-rgb:
    0,
    84,
    233;
  --ion-color-primary-contrast: #fff;
  --ion-color-primary-contrast-rgb:
    255,
    255,
    255;
  --ion-color-primary-shade: #004acd;
  --ion-color-primary-tint: #1a65eb;
  --ion-color-secondary: #0163aa;
  --ion-color-secondary-rgb:
    1,
    99,
    170;
  --ion-color-secondary-contrast: #fff;
  --ion-color-secondary-contrast-rgb:
    255,
    255,
    255;
  --ion-color-secondary-shade: #015796;
  --ion-color-secondary-tint: #1a73b3;
  --ion-color-tertiary: #6030ff;
  --ion-color-tertiary-rgb:
    96,
    48,
    255;
  --ion-color-tertiary-contrast: #fff;
  --ion-color-tertiary-contrast-rgb:
    255,
    255,
    255;
  --ion-color-tertiary-shade: #542ae0;
  --ion-color-tertiary-tint: #7045ff;
  --ion-color-success: #2dd55b;
  --ion-color-success-rgb:
    45,
    213,
    91;
  --ion-color-success-contrast: #000;
  --ion-color-success-contrast-rgb:
    0,
    0,
    0;
  --ion-color-success-shade: #28bb50;
  --ion-color-success-tint: #42d96b;
  --ion-color-warning: #ffc409;
  --ion-color-warning-rgb:
    255,
    196,
    9;
  --ion-color-warning-contrast: #000;
  --ion-color-warning-contrast-rgb:
    0,
    0,
    0;
  --ion-color-warning-shade: #e0ac08;
  --ion-color-warning-tint: #ffca22;
  --ion-color-danger: #c5000f;
  --ion-color-danger-rgb:
    197,
    0,
    15;
  --ion-color-danger-contrast: #fff;
  --ion-color-danger-contrast-rgb:
    255,
    255,
    255;
  --ion-color-danger-shade: #ad000d;
  --ion-color-danger-tint: #cb1a27;
  --ion-color-light: #f4f5f8;
  --ion-color-light-rgb:
    244,
    245,
    248;
  --ion-color-light-contrast: #000;
  --ion-color-light-contrast-rgb:
    0,
    0,
    0;
  --ion-color-light-shade: #d7d8da;
  --ion-color-light-tint: #f5f6f9;
  --ion-color-medium: #636469;
  --ion-color-medium-rgb:
    99,
    100,
    105;
  --ion-color-medium-contrast: #fff;
  --ion-color-medium-contrast-rgb:
    255,
    255,
    255;
  --ion-color-medium-shade: #57585c;
  --ion-color-medium-tint: #737478;
  --ion-color-dark: #222428;
  --ion-color-dark-rgb:
    34,
    36,
    40;
  --ion-color-dark-contrast: #fff;
  --ion-color-dark-contrast-rgb:
    255,
    255,
    255;
  --ion-color-dark-shade: #1e2023;
  --ion-color-dark-tint: #383a3e;
}
html.ios {
  --ion-default-font:
    -apple-system,
    BlinkMacSystemFont,
    "Helvetica Neue",
    "Roboto",
    sans-serif;
}
html.md {
  --ion-default-font:
    "Roboto",
    "Helvetica Neue",
    sans-serif;
}
html {
  --ion-dynamic-font: -apple-system-body;
  --ion-font-family: var(--ion-default-font);
}
body {
  background: var(--ion-background-color);
  color: var(--ion-text-color);
}
body.backdrop-no-scroll {
  overflow: hidden;
}
html.ios ion-modal.modal-card ion-header ion-toolbar:first-of-type,
html.ios ion-modal.modal-sheet ion-header ion-toolbar:first-of-type,
html.ios ion-modal ion-footer ion-toolbar:first-of-type {
  padding-top: 6px;
}
html.ios ion-modal.modal-card ion-header ion-toolbar:last-of-type,
html.ios ion-modal.modal-sheet ion-header ion-toolbar:last-of-type {
  padding-bottom: 6px;
}
html.ios ion-modal ion-toolbar {
  padding-right: calc(var(--ion-safe-area-right) + 8px);
  padding-left: calc(var(--ion-safe-area-left) + 8px);
}
@media screen and (min-width: 768px) {
  html.ios ion-modal.modal-card:first-of-type {
    --backdrop-opacity: 0.18;
  }
}
ion-modal.modal-default.show-modal ~ ion-modal.modal-default {
  --backdrop-opacity: 0;
  --box-shadow: none;
}
html.ios ion-modal.modal-card .ion-page {
  border-top-left-radius: var(--border-radius);
}
.ion-color-primary {
  --ion-color-base: var(--ion-color-primary, #0054e9) !important;
  --ion-color-base-rgb: var(--ion-color-primary-rgb, 0, 84, 233) !important;
  --ion-color-contrast: var(--ion-color-primary-contrast, #fff) !important;
  --ion-color-contrast-rgb: var(--ion-color-primary-contrast-rgb, 255, 255, 255) !important;
  --ion-color-shade: var(--ion-color-primary-shade, #004acd) !important;
  --ion-color-tint: var(--ion-color-primary-tint, #1a65eb) !important;
}
.ion-color-secondary {
  --ion-color-base: var(--ion-color-secondary, #0163aa) !important;
  --ion-color-base-rgb: var(--ion-color-secondary-rgb, 1, 99, 170) !important;
  --ion-color-contrast: var(--ion-color-secondary-contrast, #fff) !important;
  --ion-color-contrast-rgb: var(--ion-color-secondary-contrast-rgb, 255, 255, 255) !important;
  --ion-color-shade: var(--ion-color-secondary-shade, #015796) !important;
  --ion-color-tint: var(--ion-color-secondary-tint, #1a73b3) !important;
}
.ion-color-tertiary {
  --ion-color-base: var(--ion-color-tertiary, #6030ff) !important;
  --ion-color-base-rgb: var(--ion-color-tertiary-rgb, 96, 48, 255) !important;
  --ion-color-contrast: var(--ion-color-tertiary-contrast, #fff) !important;
  --ion-color-contrast-rgb: var(--ion-color-tertiary-contrast-rgb, 255, 255, 255) !important;
  --ion-color-shade: var(--ion-color-tertiary-shade, #542ae0) !important;
  --ion-color-tint: var(--ion-color-tertiary-tint, #7045ff) !important;
}
.ion-color-success {
  --ion-color-base: var(--ion-color-success, #2dd55b) !important;
  --ion-color-base-rgb: var(--ion-color-success-rgb, 45, 213, 91) !important;
  --ion-color-contrast: var(--ion-color-success-contrast, #000) !important;
  --ion-color-contrast-rgb: var(--ion-color-success-contrast-rgb, 0, 0, 0) !important;
  --ion-color-shade: var(--ion-color-success-shade, #28bb50) !important;
  --ion-color-tint: var(--ion-color-success-tint, #42d96b) !important;
}
.ion-color-warning {
  --ion-color-base: var(--ion-color-warning, #ffc409) !important;
  --ion-color-base-rgb: var(--ion-color-warning-rgb, 255, 196, 9) !important;
  --ion-color-contrast: var(--ion-color-warning-contrast, #000) !important;
  --ion-color-contrast-rgb: var(--ion-color-warning-contrast-rgb, 0, 0, 0) !important;
  --ion-color-shade: var(--ion-color-warning-shade, #e0ac08) !important;
  --ion-color-tint: var(--ion-color-warning-tint, #ffca22) !important;
}
.ion-color-danger {
  --ion-color-base: var(--ion-color-danger, #c5000f) !important;
  --ion-color-base-rgb: var(--ion-color-danger-rgb, 197, 0, 15) !important;
  --ion-color-contrast: var(--ion-color-danger-contrast, #fff) !important;
  --ion-color-contrast-rgb: var(--ion-color-danger-contrast-rgb, 255, 255, 255) !important;
  --ion-color-shade: var(--ion-color-danger-shade, #ad000d) !important;
  --ion-color-tint: var(--ion-color-danger-tint, #cb1a27) !important;
}
.ion-color-light {
  --ion-color-base: var(--ion-color-light, #f4f5f8) !important;
  --ion-color-base-rgb: var(--ion-color-light-rgb, 244, 245, 248) !important;
  --ion-color-contrast: var(--ion-color-light-contrast, #000) !important;
  --ion-color-contrast-rgb: var(--ion-color-light-contrast-rgb, 0, 0, 0) !important;
  --ion-color-shade: var(--ion-color-light-shade, #d7d8da) !important;
  --ion-color-tint: var(--ion-color-light-tint, #f5f6f9) !important;
}
.ion-color-medium {
  --ion-color-base: var(--ion-color-medium, #636469) !important;
  --ion-color-base-rgb: var(--ion-color-medium-rgb, 99, 100, 105) !important;
  --ion-color-contrast: var(--ion-color-medium-contrast, #fff) !important;
  --ion-color-contrast-rgb: var(--ion-color-medium-contrast-rgb, 255, 255, 255) !important;
  --ion-color-shade: var(--ion-color-medium-shade, #57585c) !important;
  --ion-color-tint: var(--ion-color-medium-tint, #737478) !important;
}
.ion-color-dark {
  --ion-color-base: var(--ion-color-dark, #222428) !important;
  --ion-color-base-rgb: var(--ion-color-dark-rgb, 34, 36, 40) !important;
  --ion-color-contrast: var(--ion-color-dark-contrast, #fff) !important;
  --ion-color-contrast-rgb: var(--ion-color-dark-contrast-rgb, 255, 255, 255) !important;
  --ion-color-shade: var(--ion-color-dark-shade, #1e2023) !important;
  --ion-color-tint: var(--ion-color-dark-tint, #383a3e) !important;
}
.ion-page {
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  position: absolute;
  flex-direction: column;
  justify-content: space-between;
  contain: layout size style;
  z-index: 0;
}
ion-modal > .ion-page {
  position: relative;
  contain: layout style;
  height: 100%;
}
.split-pane-visible > .ion-page.split-pane-main {
  position: relative;
}
ion-route,
ion-route-redirect,
ion-router,
ion-select-option,
ion-nav-controller,
ion-menu-controller,
ion-action-sheet-controller,
ion-alert-controller,
ion-loading-controller,
ion-modal-controller,
ion-picker-controller,
ion-popover-controller,
ion-toast-controller,
.ion-page-hidden {
  display: none !important;
}
.ion-page-invisible {
  opacity: 0;
}
.can-go-back > ion-header ion-back-button {
  display: block;
}
html.plt-ios.plt-hybrid,
html.plt-ios.plt-pwa {
  --ion-statusbar-padding: 20px;
}
@supports (padding-top: 20px) {
  html {
    --ion-safe-area-top: var(--ion-statusbar-padding);
  }
}
@supports (padding-top: env(safe-area-inset-top)) {
  html {
    --ion-safe-area-top: env(safe-area-inset-top);
    --ion-safe-area-bottom: env(safe-area-inset-bottom);
    --ion-safe-area-left: env(safe-area-inset-left);
    --ion-safe-area-right: env(safe-area-inset-right);
  }
}
ion-card.ion-color .ion-inherit-color,
ion-card-header.ion-color .ion-inherit-color {
  color: inherit;
}
.menu-content {
  transform: translate3d(0, 0, 0);
}
.menu-content-open {
  cursor: pointer;
  touch-action: manipulation;
  pointer-events: none;
  overflow-y: hidden;
}
.menu-content-open ion-content {
  --overflow: hidden;
}
.menu-content-open .ion-content-scroll-host {
  overflow: hidden;
}
.ios .menu-content-reveal {
  box-shadow: -8px 0 42px rgba(0, 0, 0, .08);
}
[dir=rtl].ios .menu-content-reveal {
  box-shadow: 8px 0 42px rgba(0, 0, 0, .08);
}
.md .menu-content-reveal {
  box-shadow: 4px 0px 16px rgba(0, 0, 0, .18);
}
.md .menu-content-push {
  box-shadow: 4px 0px 16px rgba(0, 0, 0, .18);
}
ion-accordion-group.accordion-group-expand-inset > ion-accordion:first-of-type {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
ion-accordion-group.accordion-group-expand-inset > ion-accordion:last-of-type {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
ion-accordion-group > ion-accordion:last-of-type ion-item[slot=header] {
  --border-width: 0px;
}
ion-accordion.accordion-animated > [slot=header] .ion-accordion-toggle-icon {
  transition: 300ms transform cubic-bezier(0.25, 0.8, 0.5, 1);
}
@media (prefers-reduced-motion: reduce) {
  ion-accordion .ion-accordion-toggle-icon {
    transition: none !important;
  }
}
ion-accordion.accordion-expanding > [slot=header] .ion-accordion-toggle-icon,
ion-accordion.accordion-expanded > [slot=header] .ion-accordion-toggle-icon {
  transform: rotate(180deg);
}
ion-accordion-group.accordion-group-expand-inset.md > ion-accordion.accordion-previous ion-item[slot=header] {
  --border-width: 0px;
  --inner-border-width: 0px;
}
ion-accordion-group.accordion-group-expand-inset.md > ion-accordion.accordion-expanding:first-of-type,
ion-accordion-group.accordion-group-expand-inset.md > ion-accordion.accordion-expanded:first-of-type {
  margin-top: 0;
}
ion-input input::-webkit-date-and-time-value {
  text-align: start;
}
.ion-datetime-button-overlay {
  --width: fit-content;
  --height: fit-content;
}
.ion-datetime-button-overlay ion-datetime.datetime-grid {
  width: 320px;
  min-height: 320px;
}
[ion-last-focus],
header[tabindex="-1"]:focus,
[role=banner][tabindex="-1"]:focus,
main[tabindex="-1"]:focus,
[role=main][tabindex="-1"]:focus,
h1[tabindex="-1"]:focus,
[role=heading][aria-level="1"][tabindex="-1"]:focus {
  outline: none;
}
.popover-viewport:has(> ion-content) {
  overflow: hidden;
}
@supports not selector(:has(> ion-content)) {
  .popover-viewport {
    overflow: hidden;
  }
}

/* node_modules/@ionic/angular/css/normalize.css */
audio,
canvas,
progress,
video {
  vertical-align: baseline;
}
audio:not([controls]) {
  display: none;
  height: 0;
}
b,
strong {
  font-weight: bold;
}
img {
  max-width: 100%;
}
hr {
  height: 1px;
  border-width: 0;
  box-sizing: content-box;
}
pre {
  overflow: auto;
}
code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}
label,
input,
select,
textarea {
  font-family: inherit;
  line-height: normal;
}
textarea {
  overflow: auto;
  height: auto;
  font: inherit;
  color: inherit;
}
textarea::placeholder {
  padding-left: 2px;
}
form,
input,
optgroup,
select {
  margin: 0;
  font: inherit;
  color: inherit;
}
html input[type=button],
input[type=reset],
input[type=submit] {
  cursor: pointer;
  -webkit-appearance: button;
}
a,
a div,
a span,
a ion-icon,
a ion-label,
button,
button div,
button span,
button ion-icon,
button ion-label,
.ion-tappable,
[tappable],
[tappable] div,
[tappable] span,
[tappable] ion-icon,
[tappable] ion-label,
input,
textarea {
  touch-action: manipulation;
}
a ion-label,
button ion-label {
  pointer-events: none;
}
button {
  padding: 0;
  border: 0;
  border-radius: 0;
  font-family: inherit;
  font-style: inherit;
  font-variant: inherit;
  line-height: 1;
  text-transform: none;
  cursor: pointer;
  -webkit-appearance: button;
}
[tappable] {
  cursor: pointer;
}
a[disabled],
button[disabled],
html input[disabled] {
  cursor: default;
}
button::-moz-focus-inner,
input::-moz-focus-inner {
  padding: 0;
  border: 0;
}
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  height: auto;
}
input[type=search]::-webkit-search-cancel-button,
input[type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
td,
th {
  padding: 0;
}

/* node_modules/@ionic/angular/css/structure.css */
* {
  box-sizing: border-box;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}
html {
  width: 100%;
  height: 100%;
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
}
html:not(.hydrated) body {
  display: none;
}
html.ion-ce body {
  display: block;
}
html.plt-pwa {
  height: 100vh;
}
body {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  margin-left: 0;
  margin-right: 0;
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 0;
  padding-right: 0;
  padding-top: 0;
  padding-bottom: 0;
  position: fixed;
  width: 100%;
  max-width: 100%;
  height: 100%;
  max-height: 100%;
  transform: translateZ(0);
  text-rendering: optimizeLegibility;
  overflow: hidden;
  touch-action: manipulation;
  -webkit-user-drag: none;
  -ms-content-zooming: none;
  word-wrap: break-word;
  overscroll-behavior-y: none;
  -webkit-text-size-adjust: none;
  text-size-adjust: none;
}

/* node_modules/@ionic/angular/css/typography.css */
html {
  font-family: var(--ion-font-family);
}
@supports (-webkit-touch-callout: none) {
  html {
    font: var(--ion-dynamic-font, 16px var(--ion-font-family));
  }
}
a {
  background-color: transparent;
  color: var(--ion-color-primary, #0054e9);
}
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 16px;
  margin-bottom: 10px;
  font-weight: 500;
  line-height: 1.2;
}
h1 {
  margin-top: 20px;
  font-size: 1.625rem;
}
h2 {
  margin-top: 18px;
  font-size: 1.5rem;
}
h3 {
  font-size: 1.375rem;
}
h4 {
  font-size: 1.25rem;
}
h5 {
  font-size: 1.125rem;
}
h6 {
  font-size: 1rem;
}
small {
  font-size: 75%;
}
sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}
sup {
  top: -0.5em;
}
sub {
  bottom: -0.25em;
}

/* node_modules/@ionic/angular/css/display.css */
.ion-hide {
  display: none !important;
}
.ion-hide-up {
  display: none !important;
}
.ion-hide-down {
  display: none !important;
}
@media (min-width: 576px) {
  .ion-hide-sm-up {
    display: none !important;
  }
}
@media (max-width: 575.98px) {
  .ion-hide-sm-down {
    display: none !important;
  }
}
@media (min-width: 768px) {
  .ion-hide-md-up {
    display: none !important;
  }
}
@media (max-width: 767.98px) {
  .ion-hide-md-down {
    display: none !important;
  }
}
@media (min-width: 992px) {
  .ion-hide-lg-up {
    display: none !important;
  }
}
@media (max-width: 991.98px) {
  .ion-hide-lg-down {
    display: none !important;
  }
}
@media (min-width: 1200px) {
  .ion-hide-xl-up {
    display: none !important;
  }
}
@media (max-width: 1199.98px) {
  .ion-hide-xl-down {
    display: none !important;
  }
}

/* node_modules/@ionic/angular/css/padding.css */
.ion-no-padding {
  --padding-start: 0;
  --padding-end: 0;
  --padding-top: 0;
  --padding-bottom: 0;
  padding-left: 0;
  padding-right: 0;
  padding-top: 0;
  padding-bottom: 0;
}
.ion-padding {
  --padding-start: var(--ion-padding, 16px);
  --padding-end: var(--ion-padding, 16px);
  --padding-top: var(--ion-padding, 16px);
  --padding-bottom: var(--ion-padding, 16px);
  -webkit-padding-start: var(--ion-padding, 16px);
  padding-inline-start: var(--ion-padding, 16px);
  -webkit-padding-end: var(--ion-padding, 16px);
  padding-inline-end: var(--ion-padding, 16px);
  padding-top: var(--ion-padding, 16px);
  padding-bottom: var(--ion-padding, 16px);
}
.ion-padding-top {
  --padding-top: var(--ion-padding, 16px);
  padding-top: var(--ion-padding, 16px);
}
.ion-padding-start {
  --padding-start: var(--ion-padding, 16px);
  -webkit-padding-start: var(--ion-padding, 16px);
  padding-inline-start: var(--ion-padding, 16px);
}
.ion-padding-end {
  --padding-end: var(--ion-padding, 16px);
  -webkit-padding-end: var(--ion-padding, 16px);
  padding-inline-end: var(--ion-padding, 16px);
}
.ion-padding-bottom {
  --padding-bottom: var(--ion-padding, 16px);
  padding-bottom: var(--ion-padding, 16px);
}
.ion-padding-vertical {
  --padding-top: var(--ion-padding, 16px);
  --padding-bottom: var(--ion-padding, 16px);
  padding-top: var(--ion-padding, 16px);
  padding-bottom: var(--ion-padding, 16px);
}
.ion-padding-horizontal {
  --padding-start: var(--ion-padding, 16px);
  --padding-end: var(--ion-padding, 16px);
  -webkit-padding-start: var(--ion-padding, 16px);
  padding-inline-start: var(--ion-padding, 16px);
  -webkit-padding-end: var(--ion-padding, 16px);
  padding-inline-end: var(--ion-padding, 16px);
}
.ion-no-margin {
  --margin-start: 0;
  --margin-end: 0;
  --margin-top: 0;
  --margin-bottom: 0;
  margin-left: 0;
  margin-right: 0;
  margin-top: 0;
  margin-bottom: 0;
}
.ion-margin {
  --margin-start: var(--ion-margin, 16px);
  --margin-end: var(--ion-margin, 16px);
  --margin-top: var(--ion-margin, 16px);
  --margin-bottom: var(--ion-margin, 16px);
  -webkit-margin-start: var(--ion-margin, 16px);
  margin-inline-start: var(--ion-margin, 16px);
  -webkit-margin-end: var(--ion-margin, 16px);
  margin-inline-end: var(--ion-margin, 16px);
  margin-top: var(--ion-margin, 16px);
  margin-bottom: var(--ion-margin, 16px);
}
.ion-margin-top {
  --margin-top: var(--ion-margin, 16px);
  margin-top: var(--ion-margin, 16px);
}
.ion-margin-start {
  --margin-start: var(--ion-margin, 16px);
  -webkit-margin-start: var(--ion-margin, 16px);
  margin-inline-start: var(--ion-margin, 16px);
}
.ion-margin-end {
  --margin-end: var(--ion-margin, 16px);
  -webkit-margin-end: var(--ion-margin, 16px);
  margin-inline-end: var(--ion-margin, 16px);
}
.ion-margin-bottom {
  --margin-bottom: var(--ion-margin, 16px);
  margin-bottom: var(--ion-margin, 16px);
}
.ion-margin-vertical {
  --margin-top: var(--ion-margin, 16px);
  --margin-bottom: var(--ion-margin, 16px);
  margin-top: var(--ion-margin, 16px);
  margin-bottom: var(--ion-margin, 16px);
}
.ion-margin-horizontal {
  --margin-start: var(--ion-margin, 16px);
  --margin-end: var(--ion-margin, 16px);
  -webkit-margin-start: var(--ion-margin, 16px);
  margin-inline-start: var(--ion-margin, 16px);
  -webkit-margin-end: var(--ion-margin, 16px);
  margin-inline-end: var(--ion-margin, 16px);
}

/* node_modules/@ionic/angular/css/float-elements.css */
.ion-float-left {
  float: left !important;
}
.ion-float-right {
  float: right !important;
}
.ion-float-start {
  float: left !important;
}
:host-context([dir=rtl]) .ion-float-start {
  float: right !important;
}
[dir=rtl] .ion-float-start {
  float: right !important;
}
@supports selector(:dir(rtl)) {
  .ion-float-start:dir(rtl) {
    float: right !important;
  }
}
.ion-float-end {
  float: right !important;
}
:host-context([dir=rtl]) .ion-float-end {
  float: left !important;
}
[dir=rtl] .ion-float-end {
  float: left !important;
}
@supports selector(:dir(rtl)) {
  .ion-float-end:dir(rtl) {
    float: left !important;
  }
}
@media (min-width: 576px) {
  .ion-float-sm-left {
    float: left !important;
  }
  .ion-float-sm-right {
    float: right !important;
  }
  .ion-float-sm-start {
    float: left !important;
  }
  :host-context([dir=rtl]) .ion-float-sm-start {
    float: right !important;
  }
  [dir=rtl] .ion-float-sm-start {
    float: right !important;
  }
  @supports selector(:dir(rtl)) {
    .ion-float-sm-start:dir(rtl) {
      float: right !important;
    }
  }
  .ion-float-sm-end {
    float: right !important;
  }
  :host-context([dir=rtl]) .ion-float-sm-end {
    float: left !important;
  }
  [dir=rtl] .ion-float-sm-end {
    float: left !important;
  }
  @supports selector(:dir(rtl)) {
    .ion-float-sm-end:dir(rtl) {
      float: left !important;
    }
  }
}
@media (min-width: 768px) {
  .ion-float-md-left {
    float: left !important;
  }
  .ion-float-md-right {
    float: right !important;
  }
  .ion-float-md-start {
    float: left !important;
  }
  :host-context([dir=rtl]) .ion-float-md-start {
    float: right !important;
  }
  [dir=rtl] .ion-float-md-start {
    float: right !important;
  }
  @supports selector(:dir(rtl)) {
    .ion-float-md-start:dir(rtl) {
      float: right !important;
    }
  }
  .ion-float-md-end {
    float: right !important;
  }
  :host-context([dir=rtl]) .ion-float-md-end {
    float: left !important;
  }
  [dir=rtl] .ion-float-md-end {
    float: left !important;
  }
  @supports selector(:dir(rtl)) {
    .ion-float-md-end:dir(rtl) {
      float: left !important;
    }
  }
}
@media (min-width: 992px) {
  .ion-float-lg-left {
    float: left !important;
  }
  .ion-float-lg-right {
    float: right !important;
  }
  .ion-float-lg-start {
    float: left !important;
  }
  :host-context([dir=rtl]) .ion-float-lg-start {
    float: right !important;
  }
  [dir=rtl] .ion-float-lg-start {
    float: right !important;
  }
  @supports selector(:dir(rtl)) {
    .ion-float-lg-start:dir(rtl) {
      float: right !important;
    }
  }
  .ion-float-lg-end {
    float: right !important;
  }
  :host-context([dir=rtl]) .ion-float-lg-end {
    float: left !important;
  }
  [dir=rtl] .ion-float-lg-end {
    float: left !important;
  }
  @supports selector(:dir(rtl)) {
    .ion-float-lg-end:dir(rtl) {
      float: left !important;
    }
  }
}
@media (min-width: 1200px) {
  .ion-float-xl-left {
    float: left !important;
  }
  .ion-float-xl-right {
    float: right !important;
  }
  .ion-float-xl-start {
    float: left !important;
  }
  :host-context([dir=rtl]) .ion-float-xl-start {
    float: right !important;
  }
  [dir=rtl] .ion-float-xl-start {
    float: right !important;
  }
  @supports selector(:dir(rtl)) {
    .ion-float-xl-start:dir(rtl) {
      float: right !important;
    }
  }
  .ion-float-xl-end {
    float: right !important;
  }
  :host-context([dir=rtl]) .ion-float-xl-end {
    float: left !important;
  }
  [dir=rtl] .ion-float-xl-end {
    float: left !important;
  }
  @supports selector(:dir(rtl)) {
    .ion-float-xl-end:dir(rtl) {
      float: left !important;
    }
  }
}

/* node_modules/@ionic/angular/css/text-alignment.css */
.ion-text-center {
  text-align: center !important;
}
.ion-text-justify {
  text-align: justify !important;
}
.ion-text-start {
  text-align: start !important;
}
.ion-text-end {
  text-align: end !important;
}
.ion-text-left {
  text-align: left !important;
}
.ion-text-right {
  text-align: right !important;
}
.ion-text-nowrap {
  white-space: nowrap !important;
}
.ion-text-wrap {
  white-space: normal !important;
}
@media (min-width: 576px) {
  .ion-text-sm-center {
    text-align: center !important;
  }
  .ion-text-sm-justify {
    text-align: justify !important;
  }
  .ion-text-sm-start {
    text-align: start !important;
  }
  .ion-text-sm-end {
    text-align: end !important;
  }
  .ion-text-sm-left {
    text-align: left !important;
  }
  .ion-text-sm-right {
    text-align: right !important;
  }
  .ion-text-sm-nowrap {
    white-space: nowrap !important;
  }
  .ion-text-sm-wrap {
    white-space: normal !important;
  }
}
@media (min-width: 768px) {
  .ion-text-md-center {
    text-align: center !important;
  }
  .ion-text-md-justify {
    text-align: justify !important;
  }
  .ion-text-md-start {
    text-align: start !important;
  }
  .ion-text-md-end {
    text-align: end !important;
  }
  .ion-text-md-left {
    text-align: left !important;
  }
  .ion-text-md-right {
    text-align: right !important;
  }
  .ion-text-md-nowrap {
    white-space: nowrap !important;
  }
  .ion-text-md-wrap {
    white-space: normal !important;
  }
}
@media (min-width: 992px) {
  .ion-text-lg-center {
    text-align: center !important;
  }
  .ion-text-lg-justify {
    text-align: justify !important;
  }
  .ion-text-lg-start {
    text-align: start !important;
  }
  .ion-text-lg-end {
    text-align: end !important;
  }
  .ion-text-lg-left {
    text-align: left !important;
  }
  .ion-text-lg-right {
    text-align: right !important;
  }
  .ion-text-lg-nowrap {
    white-space: nowrap !important;
  }
  .ion-text-lg-wrap {
    white-space: normal !important;
  }
}
@media (min-width: 1200px) {
  .ion-text-xl-center {
    text-align: center !important;
  }
  .ion-text-xl-justify {
    text-align: justify !important;
  }
  .ion-text-xl-start {
    text-align: start !important;
  }
  .ion-text-xl-end {
    text-align: end !important;
  }
  .ion-text-xl-left {
    text-align: left !important;
  }
  .ion-text-xl-right {
    text-align: right !important;
  }
  .ion-text-xl-nowrap {
    white-space: nowrap !important;
  }
  .ion-text-xl-wrap {
    white-space: normal !important;
  }
}

/* node_modules/@ionic/angular/css/text-transformation.css */
.ion-text-uppercase {
  text-transform: uppercase !important;
}
.ion-text-lowercase {
  text-transform: lowercase !important;
}
.ion-text-capitalize {
  text-transform: capitalize !important;
}
@media (min-width: 576px) {
  .ion-text-sm-uppercase {
    text-transform: uppercase !important;
  }
  .ion-text-sm-lowercase {
    text-transform: lowercase !important;
  }
  .ion-text-sm-capitalize {
    text-transform: capitalize !important;
  }
}
@media (min-width: 768px) {
  .ion-text-md-uppercase {
    text-transform: uppercase !important;
  }
  .ion-text-md-lowercase {
    text-transform: lowercase !important;
  }
  .ion-text-md-capitalize {
    text-transform: capitalize !important;
  }
}
@media (min-width: 992px) {
  .ion-text-lg-uppercase {
    text-transform: uppercase !important;
  }
  .ion-text-lg-lowercase {
    text-transform: lowercase !important;
  }
  .ion-text-lg-capitalize {
    text-transform: capitalize !important;
  }
}
@media (min-width: 1200px) {
  .ion-text-xl-uppercase {
    text-transform: uppercase !important;
  }
  .ion-text-xl-lowercase {
    text-transform: lowercase !important;
  }
  .ion-text-xl-capitalize {
    text-transform: capitalize !important;
  }
}

/* node_modules/@ionic/angular/css/flex-utils.css */
.ion-align-self-start {
  align-self: flex-start !important;
}
.ion-align-self-end {
  align-self: flex-end !important;
}
.ion-align-self-center {
  align-self: center !important;
}
.ion-align-self-stretch {
  align-self: stretch !important;
}
.ion-align-self-baseline {
  align-self: baseline !important;
}
.ion-align-self-auto {
  align-self: auto !important;
}
.ion-wrap {
  flex-wrap: wrap !important;
}
.ion-nowrap {
  flex-wrap: nowrap !important;
}
.ion-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}
.ion-justify-content-start {
  justify-content: flex-start !important;
}
.ion-justify-content-center {
  justify-content: center !important;
}
.ion-justify-content-end {
  justify-content: flex-end !important;
}
.ion-justify-content-around {
  justify-content: space-around !important;
}
.ion-justify-content-between {
  justify-content: space-between !important;
}
.ion-justify-content-evenly {
  justify-content: space-evenly !important;
}
.ion-align-items-start {
  align-items: flex-start !important;
}
.ion-align-items-center {
  align-items: center !important;
}
.ion-align-items-end {
  align-items: flex-end !important;
}
.ion-align-items-stretch {
  align-items: stretch !important;
}
.ion-align-items-baseline {
  align-items: baseline !important;
}

/* node_modules/@ionic/angular/css/palettes/dark.system.css */
@media (prefers-color-scheme: dark) {
  :root {
    --ion-color-primary: #4d8dff;
    --ion-color-primary-rgb:
      77,
      141,
      255;
    --ion-color-primary-contrast: #000;
    --ion-color-primary-contrast-rgb:
      0,
      0,
      0;
    --ion-color-primary-shade: #447ce0;
    --ion-color-primary-tint: #5f98ff;
    --ion-color-secondary: #46b1ff;
    --ion-color-secondary-rgb:
      70,
      177,
      255;
    --ion-color-secondary-contrast: #000;
    --ion-color-secondary-contrast-rgb:
      0,
      0,
      0;
    --ion-color-secondary-shade: #3e9ce0;
    --ion-color-secondary-tint: #59b9ff;
    --ion-color-tertiary: #8482fb;
    --ion-color-tertiary-rgb:
      132,
      130,
      251;
    --ion-color-tertiary-contrast: #000;
    --ion-color-tertiary-contrast-rgb:
      0,
      0,
      0;
    --ion-color-tertiary-shade: #7472dd;
    --ion-color-tertiary-tint: #908ffb;
    --ion-color-success: #2dd55b;
    --ion-color-success-rgb:
      45,
      213,
      91;
    --ion-color-success-contrast: #000;
    --ion-color-success-contrast-rgb:
      0,
      0,
      0;
    --ion-color-success-shade: #28bb50;
    --ion-color-success-tint: #42d96b;
    --ion-color-warning: #ffce31;
    --ion-color-warning-rgb:
      255,
      206,
      49;
    --ion-color-warning-contrast: #000;
    --ion-color-warning-contrast-rgb:
      0,
      0,
      0;
    --ion-color-warning-shade: #e0b52b;
    --ion-color-warning-tint: #ffd346;
    --ion-color-danger: #f24c58;
    --ion-color-danger-rgb:
      242,
      76,
      88;
    --ion-color-danger-contrast: #000;
    --ion-color-danger-contrast-rgb:
      0,
      0,
      0;
    --ion-color-danger-shade: #d5434d;
    --ion-color-danger-tint: #f35e69;
    --ion-color-light: #222428;
    --ion-color-light-rgb:
      34,
      36,
      40;
    --ion-color-light-contrast: #fff;
    --ion-color-light-contrast-rgb:
      255,
      255,
      255;
    --ion-color-light-shade: #1e2023;
    --ion-color-light-tint: #383a3e;
    --ion-color-medium: #989aa2;
    --ion-color-medium-rgb:
      152,
      154,
      162;
    --ion-color-medium-contrast: #000;
    --ion-color-medium-contrast-rgb:
      0,
      0,
      0;
    --ion-color-medium-shade: #86888f;
    --ion-color-medium-tint: #a2a4ab;
    --ion-color-dark: #f4f5f8;
    --ion-color-dark-rgb:
      244,
      245,
      248;
    --ion-color-dark-contrast: #000;
    --ion-color-dark-contrast-rgb:
      0,
      0,
      0;
    --ion-color-dark-shade: #d7d8da;
    --ion-color-dark-tint: #f5f6f9;
  }
  :root.ios {
    --ion-background-color: #000000;
    --ion-background-color-rgb:
      0,
      0,
      0;
    --ion-text-color: #ffffff;
    --ion-text-color-rgb:
      255,
      255,
      255;
    --ion-background-color-step-50: #0d0d0d;
    --ion-background-color-step-100: #1a1a1a;
    --ion-background-color-step-150: #262626;
    --ion-background-color-step-200: #333333;
    --ion-background-color-step-250: #404040;
    --ion-background-color-step-300: #4d4d4d;
    --ion-background-color-step-350: #595959;
    --ion-background-color-step-400: #666666;
    --ion-background-color-step-450: #737373;
    --ion-background-color-step-500: #808080;
    --ion-background-color-step-550: #8c8c8c;
    --ion-background-color-step-600: #999999;
    --ion-background-color-step-650: #a6a6a6;
    --ion-background-color-step-700: #b3b3b3;
    --ion-background-color-step-750: #bfbfbf;
    --ion-background-color-step-800: #cccccc;
    --ion-background-color-step-850: #d9d9d9;
    --ion-background-color-step-900: #e6e6e6;
    --ion-background-color-step-950: #f2f2f2;
    --ion-text-color-step-50: #f2f2f2;
    --ion-text-color-step-100: #e6e6e6;
    --ion-text-color-step-150: #d9d9d9;
    --ion-text-color-step-200: #cccccc;
    --ion-text-color-step-250: #bfbfbf;
    --ion-text-color-step-300: #b3b3b3;
    --ion-text-color-step-350: #a6a6a6;
    --ion-text-color-step-400: #999999;
    --ion-text-color-step-450: #8c8c8c;
    --ion-text-color-step-500: #808080;
    --ion-text-color-step-550: #737373;
    --ion-text-color-step-600: #666666;
    --ion-text-color-step-650: #595959;
    --ion-text-color-step-700: #4d4d4d;
    --ion-text-color-step-750: #404040;
    --ion-text-color-step-800: #333333;
    --ion-text-color-step-850: #262626;
    --ion-text-color-step-900: #1a1a1a;
    --ion-text-color-step-950: #0d0d0d;
    --ion-item-background: #000000;
    --ion-card-background: #1c1c1d;
  }
  :root.ios ion-modal {
    --ion-background-color: var(--ion-color-step-100, var(--ion-background-color-step-100));
    --ion-toolbar-background: var(--ion-color-step-150, var(--ion-background-color-step-150));
    --ion-toolbar-border-color: var(--ion-color-step-250, var(--ion-background-color-step-250));
  }
  :root.md {
    --ion-background-color: #121212;
    --ion-background-color-rgb:
      18,
      18,
      18;
    --ion-text-color: #ffffff;
    --ion-text-color-rgb:
      255,
      255,
      255;
    --ion-background-color-step-50: #1e1e1e;
    --ion-background-color-step-100: #2a2a2a;
    --ion-background-color-step-150: #363636;
    --ion-background-color-step-200: #414141;
    --ion-background-color-step-250: #4d4d4d;
    --ion-background-color-step-300: #595959;
    --ion-background-color-step-350: #656565;
    --ion-background-color-step-400: #717171;
    --ion-background-color-step-450: #7d7d7d;
    --ion-background-color-step-500: #898989;
    --ion-background-color-step-550: #949494;
    --ion-background-color-step-600: #a0a0a0;
    --ion-background-color-step-650: #acacac;
    --ion-background-color-step-700: #b8b8b8;
    --ion-background-color-step-750: #c4c4c4;
    --ion-background-color-step-800: #d0d0d0;
    --ion-background-color-step-850: #dbdbdb;
    --ion-background-color-step-900: #e7e7e7;
    --ion-background-color-step-950: #f3f3f3;
    --ion-text-color-step-50: #f3f3f3;
    --ion-text-color-step-100: #e7e7e7;
    --ion-text-color-step-150: #dbdbdb;
    --ion-text-color-step-200: #d0d0d0;
    --ion-text-color-step-250: #c4c4c4;
    --ion-text-color-step-300: #b8b8b8;
    --ion-text-color-step-350: #acacac;
    --ion-text-color-step-400: #a0a0a0;
    --ion-text-color-step-450: #949494;
    --ion-text-color-step-500: #898989;
    --ion-text-color-step-550: #7d7d7d;
    --ion-text-color-step-600: #717171;
    --ion-text-color-step-650: #656565;
    --ion-text-color-step-700: #595959;
    --ion-text-color-step-750: #4d4d4d;
    --ion-text-color-step-800: #414141;
    --ion-text-color-step-850: #363636;
    --ion-text-color-step-900: #2a2a2a;
    --ion-text-color-step-950: #1e1e1e;
    --ion-item-background: #1e1e1e;
    --ion-toolbar-background: #1f1f1f;
    --ion-tab-bar-background: #1f1f1f;
    --ion-card-background: #1e1e1e;
  }
}

/* src/global.scss */
ion-card {
  border-radius: var(--app-border-radius-md);
  box-shadow: var(--app-card-shadow) !important;
  margin: var(--app-spacing-md);
}
ion-card ion-card-header {
  padding: var(--app-spacing-md);
}
ion-card ion-card-header ion-card-title {
  font-size: 1.25rem;
  font-weight: var(--app-heading-font-weight);
  color: var(--app-text-color);
}
ion-card ion-card-header ion-card-subtitle {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
  margin-top: var(--app-spacing-xs);
}
ion-card ion-card-content {
  padding: var(--app-spacing-md);
  color: var(--app-text-color);
}
ion-button {
  --border-radius: var(--app-border-radius-md);
  --box-shadow: none;
  --padding-top: var(--app-spacing-sm);
  --padding-bottom: var(--app-spacing-sm);
  --padding-start: var(--app-spacing-lg);
  --padding-end: var(--app-spacing-lg);
  margin: var(--app-spacing-sm) 0;
  font-weight: 500;
  letter-spacing: 0.03em;
  text-transform: none;
}
ion-button.button-solid {
  --background: var(--ion-color-primary);
  --color: var(--ion-color-primary-contrast);
}
ion-button.button-solid:hover {
  --background: var(--ion-color-primary-shade);
}
ion-button.button-outline {
  --border-color: var(--ion-color-primary);
  --background: transparent;
  --color: var(--ion-color-primary);
}
ion-button.button-outline:hover {
  --background: rgba(var(--ion-color-primary-rgb), 0.05);
}
ion-button.button-clear {
  --color: var(--ion-color-primary);
}
ion-button.button-clear:hover {
  --color: var(--ion-color-primary-shade);
}
ion-button ion-icon {
  margin-right: var(--app-spacing-xs);
}
ion-item {
  --padding-start: var(--app-spacing-md);
  --padding-end: var(--app-spacing-md);
  --padding-top: var(--app-spacing-sm);
  --padding-bottom: var(--app-spacing-sm);
  --inner-padding-end: 0;
  --border-color: var(--app-border-color);
  --background: var(--app-background-color);
  --background-hover: rgba(var(--ion-color-primary-rgb), 0.05);
}
ion-item.item-interactive {
  --background-activated: rgba(var(--ion-color-primary-rgb), 0.1);
}
ion-input,
ion-textarea,
ion-select {
  --padding-start: var(--app-spacing-md);
  --padding-end: var(--app-spacing-md);
  --padding-top: var(--app-spacing-md);
  --padding-bottom: var(--app-spacing-md);
  --background: var(--app-input-background);
  --color: var(--app-text-color);
  --placeholder-color: var(--ion-color-medium);
  --placeholder-opacity: 0.7;
  border-radius: var(--app-border-radius-sm);
  margin: var(--app-spacing-xs) 0;
}
ion-input.has-focus,
ion-textarea.has-focus,
ion-select.has-focus {
  --background: var(--app-input-background);
  --border-color: var(--ion-color-primary);
}
ion-label {
  color: var(--app-text-color);
  font-weight: 500;
  margin-bottom: var(--app-spacing-xs);
}
ion-list {
  background: transparent;
  padding: var(--app-spacing-sm) 0;
}
ion-list ion-list-header {
  padding-left: var(--app-spacing-md);
  padding-right: var(--app-spacing-md);
  font-weight: 600;
  letter-spacing: 0.03em;
  text-transform: uppercase;
  font-size: 0.8rem;
  color: var(--ion-color-medium);
}
ion-list ion-item {
  --padding-start: var(--app-spacing-md);
  --padding-end: var(--app-spacing-md);
  --inner-padding-end: var(--app-spacing-md);
  margin-bottom: var(--app-spacing-xs);
}
ion-list ion-item:last-child {
  --border-color: transparent;
}
ion-list ion-item h2 {
  font-weight: 500;
  font-size: 1rem;
  color: var(--app-text-color);
}
ion-list ion-item p {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}
ion-toolbar {
  --background: var(--ion-color-primary);
  --color: var(--ion-color-primary-contrast);
  --border-color: transparent;
  --padding-top: var(--app-spacing-sm);
  --padding-bottom: var(--app-spacing-sm);
  --padding-start: var(--app-spacing-md);
  --padding-end: var(--app-spacing-md);
}
ion-toolbar ion-title {
  font-size: 1.1rem;
  font-weight: 500;
}
ion-toolbar ion-buttons ion-button {
  --padding-start: var(--app-spacing-sm);
  --padding-end: var(--app-spacing-sm);
}
ion-tab-bar {
  --background: var(--app-background-color);
  --border-color: var(--app-border-color);
  padding: var(--app-spacing-xs) 0;
}
ion-tab-bar ion-tab-button {
  --color: var(--ion-color-medium);
  --color-selected: var(--ion-color-primary);
}
ion-tab-bar ion-tab-button ion-icon {
  font-size: 1.4rem;
}
ion-tab-bar ion-tab-button ion-label {
  font-size: 0.7rem;
  font-weight: 500;
  margin-top: var(--app-spacing-xs);
}
ion-badge {
  padding: var(--app-spacing-xs) var(--app-spacing-sm);
  border-radius: var(--app-border-radius-sm);
  font-weight: 500;
  font-size: 0.75rem;
}
ion-segment {
  --background: transparent;
  padding: var(--app-spacing-sm);
}
ion-segment ion-segment-button {
  --background: transparent;
  --background-checked: var(--ion-color-primary);
  --background-hover: rgba(var(--ion-color-primary-rgb), 0.05);
  --border-radius: var(--app-border-radius-md);
  --border-color: var(--app-border-color);
  --border-style: solid;
  --border-width: 1px;
  --color: var(--ion-color-medium);
  --color-checked: var(--ion-color-primary-contrast);
  --indicator-color: transparent;
  min-height: 40px;
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: none;
}
ion-segment ion-segment-button::part(indicator) {
  display: none;
}
.m-0 {
  margin: 0 !important;
}
.m-xs {
  margin: var(--app-spacing-xs) !important;
}
.m-sm {
  margin: var(--app-spacing-sm) !important;
}
.m-md {
  margin: var(--app-spacing-md) !important;
}
.m-lg {
  margin: var(--app-spacing-lg) !important;
}
.m-xl {
  margin: var(--app-spacing-xl) !important;
}
.mt-0 {
  margin-top: 0 !important;
}
.mt-xs {
  margin-top: var(--app-spacing-xs) !important;
}
.mt-sm {
  margin-top: var(--app-spacing-sm) !important;
}
.mt-md {
  margin-top: var(--app-spacing-md) !important;
}
.mt-lg {
  margin-top: var(--app-spacing-lg) !important;
}
.mt-xl {
  margin-top: var(--app-spacing-xl) !important;
}
.mr-0 {
  margin-right: 0 !important;
}
.mr-xs {
  margin-right: var(--app-spacing-xs) !important;
}
.mr-sm {
  margin-right: var(--app-spacing-sm) !important;
}
.mr-md {
  margin-right: var(--app-spacing-md) !important;
}
.mr-lg {
  margin-right: var(--app-spacing-lg) !important;
}
.mr-xl {
  margin-right: var(--app-spacing-xl) !important;
}
.mb-0 {
  margin-bottom: 0 !important;
}
.mb-xs {
  margin-bottom: var(--app-spacing-xs) !important;
}
.mb-sm {
  margin-bottom: var(--app-spacing-sm) !important;
}
.mb-md {
  margin-bottom: var(--app-spacing-md) !important;
}
.mb-lg {
  margin-bottom: var(--app-spacing-lg) !important;
}
.mb-xl {
  margin-bottom: var(--app-spacing-xl) !important;
}
.ml-0 {
  margin-left: 0 !important;
}
.ml-xs {
  margin-left: var(--app-spacing-xs) !important;
}
.ml-sm {
  margin-left: var(--app-spacing-sm) !important;
}
.ml-md {
  margin-left: var(--app-spacing-md) !important;
}
.ml-lg {
  margin-left: var(--app-spacing-lg) !important;
}
.ml-xl {
  margin-left: var(--app-spacing-xl) !important;
}
.mx-0 {
  margin-left: 0 !important;
  margin-right: 0 !important;
}
.mx-xs {
  margin-left: var(--app-spacing-xs) !important;
  margin-right: var(--app-spacing-xs) !important;
}
.mx-sm {
  margin-left: var(--app-spacing-sm) !important;
  margin-right: var(--app-spacing-sm) !important;
}
.mx-md {
  margin-left: var(--app-spacing-md) !important;
  margin-right: var(--app-spacing-md) !important;
}
.mx-lg {
  margin-left: var(--app-spacing-lg) !important;
  margin-right: var(--app-spacing-lg) !important;
}
.mx-xl {
  margin-left: var(--app-spacing-xl) !important;
  margin-right: var(--app-spacing-xl) !important;
}
.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}
.my-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}
.my-xs {
  margin-top: var(--app-spacing-xs) !important;
  margin-bottom: var(--app-spacing-xs) !important;
}
.my-sm {
  margin-top: var(--app-spacing-sm) !important;
  margin-bottom: var(--app-spacing-sm) !important;
}
.my-md {
  margin-top: var(--app-spacing-md) !important;
  margin-bottom: var(--app-spacing-md) !important;
}
.my-lg {
  margin-top: var(--app-spacing-lg) !important;
  margin-bottom: var(--app-spacing-lg) !important;
}
.my-xl {
  margin-top: var(--app-spacing-xl) !important;
  margin-bottom: var(--app-spacing-xl) !important;
}
.p-0 {
  padding: 0 !important;
}
.p-xs {
  padding: var(--app-spacing-xs) !important;
}
.p-sm {
  padding: var(--app-spacing-sm) !important;
}
.p-md {
  padding: var(--app-spacing-md) !important;
}
.p-lg {
  padding: var(--app-spacing-lg) !important;
}
.p-xl {
  padding: var(--app-spacing-xl) !important;
}
.pt-0 {
  padding-top: 0 !important;
}
.pt-xs {
  padding-top: var(--app-spacing-xs) !important;
}
.pt-sm {
  padding-top: var(--app-spacing-sm) !important;
}
.pt-md {
  padding-top: var(--app-spacing-md) !important;
}
.pt-lg {
  padding-top: var(--app-spacing-lg) !important;
}
.pt-xl {
  padding-top: var(--app-spacing-xl) !important;
}
.pr-0 {
  padding-right: 0 !important;
}
.pr-xs {
  padding-right: var(--app-spacing-xs) !important;
}
.pr-sm {
  padding-right: var(--app-spacing-sm) !important;
}
.pr-md {
  padding-right: var(--app-spacing-md) !important;
}
.pr-lg {
  padding-right: var(--app-spacing-lg) !important;
}
.pr-xl {
  padding-right: var(--app-spacing-xl) !important;
}
.pb-0 {
  padding-bottom: 0 !important;
}
.pb-xs {
  padding-bottom: var(--app-spacing-xs) !important;
}
.pb-sm {
  padding-bottom: var(--app-spacing-sm) !important;
}
.pb-md {
  padding-bottom: var(--app-spacing-md) !important;
}
.pb-lg {
  padding-bottom: var(--app-spacing-lg) !important;
}
.pb-xl {
  padding-bottom: var(--app-spacing-xl) !important;
}
.pl-0 {
  padding-left: 0 !important;
}
.pl-xs {
  padding-left: var(--app-spacing-xs) !important;
}
.pl-sm {
  padding-left: var(--app-spacing-sm) !important;
}
.pl-md {
  padding-left: var(--app-spacing-md) !important;
}
.pl-lg {
  padding-left: var(--app-spacing-lg) !important;
}
.pl-xl {
  padding-left: var(--app-spacing-xl) !important;
}
.px-0 {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.px-xs {
  padding-left: var(--app-spacing-xs) !important;
  padding-right: var(--app-spacing-xs) !important;
}
.px-sm {
  padding-left: var(--app-spacing-sm) !important;
  padding-right: var(--app-spacing-sm) !important;
}
.px-md {
  padding-left: var(--app-spacing-md) !important;
  padding-right: var(--app-spacing-md) !important;
}
.px-lg {
  padding-left: var(--app-spacing-lg) !important;
  padding-right: var(--app-spacing-lg) !important;
}
.px-xl {
  padding-left: var(--app-spacing-xl) !important;
  padding-right: var(--app-spacing-xl) !important;
}
.py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
.py-xs {
  padding-top: var(--app-spacing-xs) !important;
  padding-bottom: var(--app-spacing-xs) !important;
}
.py-sm {
  padding-top: var(--app-spacing-sm) !important;
  padding-bottom: var(--app-spacing-sm) !important;
}
.py-md {
  padding-top: var(--app-spacing-md) !important;
  padding-bottom: var(--app-spacing-md) !important;
}
.py-lg {
  padding-top: var(--app-spacing-lg) !important;
  padding-bottom: var(--app-spacing-lg) !important;
}
.py-xl {
  padding-top: var(--app-spacing-xl) !important;
  padding-bottom: var(--app-spacing-xl) !important;
}
.text-left {
  text-align: left !important;
}
.text-center {
  text-align: center !important;
}
.text-right {
  text-align: right !important;
}
.text-justify {
  text-align: justify !important;
}
.text-lowercase {
  text-transform: lowercase !important;
}
.text-uppercase {
  text-transform: uppercase !important;
}
.text-capitalize {
  text-transform: capitalize !important;
}
.font-light {
  font-weight: 300 !important;
}
.font-normal {
  font-weight: 400 !important;
}
.font-medium {
  font-weight: 500 !important;
}
.font-semibold {
  font-weight: 600 !important;
}
.font-bold {
  font-weight: 700 !important;
}
.text-xs {
  font-size: 0.75rem !important;
}
.text-sm {
  font-size: 0.875rem !important;
}
.text-md {
  font-size: 1rem !important;
}
.text-lg {
  font-size: 1.125rem !important;
}
.text-xl {
  font-size: 1.25rem !important;
}
.text-2xl {
  font-size: 1.5rem !important;
}
.text-3xl {
  font-size: 1.875rem !important;
}
.text-4xl {
  font-size: 2.25rem !important;
}
.text-primary {
  color: var(--ion-color-primary) !important;
}
.text-secondary {
  color: var(--ion-color-secondary) !important;
}
.text-tertiary {
  color: var(--ion-color-tertiary) !important;
}
.text-success {
  color: var(--ion-color-success) !important;
}
.text-warning {
  color: var(--ion-color-warning) !important;
}
.text-danger {
  color: var(--ion-color-danger) !important;
}
.text-dark {
  color: var(--ion-color-dark) !important;
}
.text-medium {
  color: var(--ion-color-medium) !important;
}
.text-light {
  color: var(--ion-color-light) !important;
}
.d-none {
  display: none !important;
}
.d-inline {
  display: inline !important;
}
.d-inline-block {
  display: inline-block !important;
}
.d-block {
  display: block !important;
}
.d-flex {
  display: flex !important;
}
.d-inline-flex {
  display: inline-flex !important;
}
.flex-row {
  flex-direction: row !important;
}
.flex-column {
  flex-direction: column !important;
}
.flex-row-reverse {
  flex-direction: row-reverse !important;
}
.flex-column-reverse {
  flex-direction: column-reverse !important;
}
.flex-wrap {
  flex-wrap: wrap !important;
}
.flex-nowrap {
  flex-wrap: nowrap !important;
}
.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}
.justify-content-start {
  justify-content: flex-start !important;
}
.justify-content-end {
  justify-content: flex-end !important;
}
.justify-content-center {
  justify-content: center !important;
}
.justify-content-between {
  justify-content: space-between !important;
}
.justify-content-around {
  justify-content: space-around !important;
}
.justify-content-evenly {
  justify-content: space-evenly !important;
}
.align-items-start {
  align-items: flex-start !important;
}
.align-items-end {
  align-items: flex-end !important;
}
.align-items-center {
  align-items: center !important;
}
.align-items-baseline {
  align-items: baseline !important;
}
.align-items-stretch {
  align-items: stretch !important;
}
.align-self-start {
  align-self: flex-start !important;
}
.align-self-end {
  align-self: flex-end !important;
}
.align-self-center {
  align-self: center !important;
}
.align-self-baseline {
  align-self: baseline !important;
}
.align-self-stretch {
  align-self: stretch !important;
}
.flex-grow-0 {
  flex-grow: 0 !important;
}
.flex-grow-1 {
  flex-grow: 1 !important;
}
.flex-shrink-0 {
  flex-shrink: 0 !important;
}
.flex-shrink-1 {
  flex-shrink: 1 !important;
}
.position-relative {
  position: relative !important;
}
.position-absolute {
  position: absolute !important;
}
.position-fixed {
  position: fixed !important;
}
.position-sticky {
  position: sticky !important;
}
.border {
  border: 1px solid var(--app-border-color) !important;
}
.border-top {
  border-top: 1px solid var(--app-border-color) !important;
}
.border-right {
  border-right: 1px solid var(--app-border-color) !important;
}
.border-bottom {
  border-bottom: 1px solid var(--app-border-color) !important;
}
.border-left {
  border-left: 1px solid var(--app-border-color) !important;
}
.border-0 {
  border: 0 !important;
}
.rounded {
  border-radius: var(--app-border-radius-md) !important;
}
.rounded-sm {
  border-radius: var(--app-border-radius-sm) !important;
}
.rounded-lg {
  border-radius: var(--app-border-radius-lg) !important;
}
.rounded-circle {
  border-radius: 50% !important;
}
.rounded-0 {
  border-radius: 0 !important;
}
.bg-primary {
  background-color: var(--ion-color-primary) !important;
}
.bg-secondary {
  background-color: var(--ion-color-secondary) !important;
}
.bg-tertiary {
  background-color: var(--ion-color-tertiary) !important;
}
.bg-success {
  background-color: var(--ion-color-success) !important;
}
.bg-warning {
  background-color: var(--ion-color-warning) !important;
}
.bg-danger {
  background-color: var(--ion-color-danger) !important;
}
.bg-dark {
  background-color: var(--ion-color-dark) !important;
}
.bg-medium {
  background-color: var(--ion-color-medium) !important;
}
.bg-light {
  background-color: var(--ion-color-light) !important;
}
.bg-transparent {
  background-color: transparent !important;
}
.bg-primary-light {
  background-color: var(--app-primary-light) !important;
}
.bg-success-light {
  background-color: var(--app-success-light) !important;
}
.bg-warning-light {
  background-color: var(--app-warning-light) !important;
}
.bg-danger-light {
  background-color: var(--app-danger-light) !important;
}
* {
  font-family: var(--app-font-family);
}
body,
ion-content {
  color: var(--app-text-color);
}
ion-content {
  --background: var(--app-background-color);
}
:focus {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}
html {
  scroll-behavior: smooth;
}
* {
  -webkit-tap-highlight-color: rgba(var(--ion-color-primary-rgb), 0.2);
}
ion-input,
ion-textarea,
ion-select {
  margin-bottom: var(--app-spacing-md);
}
ion-button {
  text-transform: none;
}
ion-card {
  overflow: hidden;
  border-radius: var(--app-border-radius-md);
  box-shadow: var(--app-card-shadow);
  margin: var(--app-spacing-md);
}
ion-card ion-card-header {
  padding-bottom: var(--app-spacing-sm);
}
ion-card ion-card-header ion-card-title {
  font-size: 18px;
  font-weight: var(--app-heading-font-weight);
  color: var(--ion-color-primary);
}
ion-card ion-card-header ion-card-subtitle {
  font-size: 14px;
  color: var(--ion-color-medium);
}
ion-card ion-card-content {
  padding: var(--app-spacing-md);
}
ion-list {
  background: transparent;
}
ion-list ion-item {
  --padding-start: var(--app-spacing-md);
  --padding-end: var(--app-spacing-md);
  --padding-top: var(--app-spacing-sm);
  --padding-bottom: var(--app-spacing-sm);
  --background: transparent;
}
ion-list ion-item:last-child {
  --border-width: 0;
}
ion-item.custom-input {
  --background: var(--app-input-background);
  --border-radius: var(--app-border-radius-md);
  --padding-start: var(--app-spacing-md);
  margin-bottom: var(--app-spacing-md);
}
ion-item.custom-input ion-input,
ion-item.custom-input ion-textarea,
ion-item.custom-input ion-select {
  --padding-start: 0;
}
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* src/theme/variables.scss */
:root {
  --ion-color-primary: #2E7D32;
  --ion-color-primary-rgb:
    46,
    125,
    50;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb:
    255,
    255,
    255;
  --ion-color-primary-shade: #286e2c;
  --ion-color-primary-tint: #438a47;
  --ion-color-secondary: #1B5E20;
  --ion-color-secondary-rgb:
    27,
    94,
    32;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb:
    255,
    255,
    255;
  --ion-color-secondary-shade: #18531c;
  --ion-color-secondary-tint: #326e36;
  --ion-color-tertiary: #5260ff;
  --ion-color-tertiary-rgb:
    82,
    96,
    255;
  --ion-color-tertiary-contrast: #ffffff;
  --ion-color-tertiary-contrast-rgb:
    255,
    255,
    255;
  --ion-color-tertiary-shade: #4854e0;
  --ion-color-tertiary-tint: #6370ff;
  --ion-color-success: #2dd36f;
  --ion-color-success-rgb:
    45,
    211,
    111;
  --ion-color-success-contrast: #ffffff;
  --ion-color-success-contrast-rgb:
    255,
    255,
    255;
  --ion-color-success-shade: #28ba62;
  --ion-color-success-tint: #42d77d;
  --ion-color-warning: #ffc409;
  --ion-color-warning-rgb:
    255,
    196,
    9;
  --ion-color-warning-contrast: #000000;
  --ion-color-warning-contrast-rgb:
    0,
    0,
    0;
  --ion-color-warning-shade: #e0ac08;
  --ion-color-warning-tint: #ffca22;
  --ion-color-danger: #eb445a;
  --ion-color-danger-rgb:
    235,
    68,
    90;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb:
    255,
    255,
    255;
  --ion-color-danger-shade: #cf3c4f;
  --ion-color-danger-tint: #ed576b;
  --ion-color-dark: #222428;
  --ion-color-dark-rgb:
    34,
    36,
    40;
  --ion-color-dark-contrast: #ffffff;
  --ion-color-dark-contrast-rgb:
    255,
    255,
    255;
  --ion-color-dark-shade: #1e2023;
  --ion-color-dark-tint: #383a3e;
  --ion-color-medium: #92949c;
  --ion-color-medium-rgb:
    146,
    148,
    156;
  --ion-color-medium-contrast: #ffffff;
  --ion-color-medium-contrast-rgb:
    255,
    255,
    255;
  --ion-color-medium-shade: #808289;
  --ion-color-medium-tint: #9d9fa6;
  --ion-color-light: #f4f5f8;
  --ion-color-light-rgb:
    244,
    245,
    248;
  --ion-color-light-contrast: #000000;
  --ion-color-light-contrast-rgb:
    0,
    0,
    0;
  --ion-color-light-shade: #d7d8da;
  --ion-color-light-tint: #f5f6f9;
  --app-background-color: #ffffff;
  --app-text-color: #333333;
  --app-border-color: #e0e0e0;
  --app-card-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  --app-input-background: #f9f9f9;
  --app-success-light: #e6f7ee;
  --app-warning-light: #fff8e6;
  --app-danger-light: #fde8eb;
  --app-primary-light: #e6f3ff;
}
:root {
  --app-font-family:
    "Roboto",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Helvetica,
    Arial,
    sans-serif;
  --app-heading-font-weight: 500;
  --app-body-font-weight: 400;
  --app-spacing-xs: 4px;
  --app-spacing-sm: 8px;
  --app-spacing-md: 16px;
  --app-spacing-lg: 24px;
  --app-spacing-xl: 32px;
  --app-border-radius-sm: 4px;
  --app-border-radius-md: 8px;
  --app-border-radius-lg: 12px;
  --app-transition-fast: 0.15s ease;
  --app-transition-normal: 0.25s ease;
  --app-transition-slow: 0.4s ease;
}
@media (prefers-color-scheme: dark) {
  :root {
    --ion-color-primary: #4CAF50;
    --ion-color-primary-rgb:
      76,
      175,
      80;
    --ion-color-primary-contrast: #ffffff;
    --ion-color-primary-contrast-rgb:
      255,
      255,
      255;
    --ion-color-primary-shade: #439a46;
    --ion-color-primary-tint: #5eb762;
    --ion-color-secondary: #388E3C;
    --ion-color-secondary-rgb:
      56,
      142,
      60;
    --ion-color-secondary-contrast: #ffffff;
    --ion-color-secondary-contrast-rgb:
      255,
      255,
      255;
    --ion-color-secondary-shade: #317d35;
    --ion-color-secondary-tint: #4c9a50;
    --ion-color-tertiary: #6a75ff;
    --ion-color-tertiary-rgb:
      106,
      117,
      255;
    --ion-color-tertiary-contrast: #ffffff;
    --ion-color-tertiary-contrast-rgb:
      255,
      255,
      255;
    --ion-color-tertiary-shade: #5d67e0;
    --ion-color-tertiary-tint: #7983ff;
    --ion-color-success: #2fdf75;
    --ion-color-success-rgb:
      47,
      223,
      117;
    --ion-color-success-contrast: #000000;
    --ion-color-success-contrast-rgb:
      0,
      0,
      0;
    --ion-color-success-shade: #29c467;
    --ion-color-success-tint: #44e283;
    --ion-color-warning: #ffd534;
    --ion-color-warning-rgb:
      255,
      213,
      52;
    --ion-color-warning-contrast: #000000;
    --ion-color-warning-contrast-rgb:
      0,
      0,
      0;
    --ion-color-warning-shade: #e0bb2e;
    --ion-color-warning-tint: #ffd948;
    --ion-color-danger: #ff4961;
    --ion-color-danger-rgb:
      255,
      73,
      97;
    --ion-color-danger-contrast: #ffffff;
    --ion-color-danger-contrast-rgb:
      255,
      255,
      255;
    --ion-color-danger-shade: #e04055;
    --ion-color-danger-tint: #ff5b71;
    --ion-color-dark: #f4f5f8;
    --ion-color-dark-rgb:
      244,
      245,
      248;
    --ion-color-dark-contrast: #000000;
    --ion-color-dark-contrast-rgb:
      0,
      0,
      0;
    --ion-color-dark-shade: #d7d8da;
    --ion-color-dark-tint: #f5f6f9;
    --ion-color-medium: #989aa2;
    --ion-color-medium-rgb:
      152,
      154,
      162;
    --ion-color-medium-contrast: #000000;
    --ion-color-medium-contrast-rgb:
      0,
      0,
      0;
    --ion-color-medium-shade: #86888f;
    --ion-color-medium-tint: #a2a4ab;
    --ion-color-light: #222428;
    --ion-color-light-rgb:
      34,
      36,
      40;
    --ion-color-light-contrast: #ffffff;
    --ion-color-light-contrast-rgb:
      255,
      255,
      255;
    --ion-color-light-shade: #1e2023;
    --ion-color-light-tint: #383a3e;
    --app-background-color: #121212;
    --app-text-color: #f4f4f4;
    --app-border-color: #333333;
    --app-card-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    --app-input-background: #1e1e1e;
    --app-success-light: #1a3328;
    --app-warning-light: #332b1a;
    --app-danger-light: #331a22;
    --app-primary-light: #1a2733;
  }
}

/* angular:styles/global:styles */
/*# sourceMappingURL=styles.css.map */
