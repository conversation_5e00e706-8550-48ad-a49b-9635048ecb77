import {
  JourneyStorageService,
  init_journey_storage_service
} from "./chunk-WC2VXUXG.js";
import "./chunk-ZCUEDWU7.js";
import {
  ActivatedRoute,
  CommonModule,
  Component,
  DataStorageService,
  DatePipe,
  DecimalPipe,
  IonAvatar,
  IonBackButton2 as IonBackButton,
  IonBadge,
  IonButton,
  IonButtons,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonContent,
  IonHeader,
  IonIcon,
  IonItem,
  IonLabel,
  IonList,
  IonSpinner,
  IonTitle,
  IonToolbar,
  IonicModule,
  NgForOf,
  NgIf,
  RouterLinkDelegateDirective2 as RouterLinkDelegateDirective,
  ToastService,
  init_common,
  init_core,
  init_data_storage_service,
  init_ionic_angular,
  init_router,
  init_toast_service,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2
} from "./chunk-2TGB3XS3.js";
import "./chunk-GJIVKQBF.js";
import "./chunk-WY354WYL.js";
import "./chunk-3TAP6XVY.js";
import "./chunk-BQEXAUCY.js";
import "./chunk-EUHNYIV3.js";
import "./chunk-AY6ZGCP5.js";
import "./chunk-I3UCYTIQ.js";
import "./chunk-4JI7VCBZ.js";
import "./chunk-YY7NCLAA.js";
import "./chunk-GUQ63TTB.js";
import "./chunk-CHFZ6R2P.js";
import "./chunk-XPKADKFH.js";
import "./chunk-V6QEVD67.js";
import "./chunk-WNPNB2PX.js";
import "./chunk-NRG74LSH.js";
import "./chunk-YSMEPS6E.js";
import "./chunk-NHEUXSZ2.js";
import "./chunk-WX4TOLMF.js";
import "./chunk-6SXGDLUH.js";
import "./chunk-NNIHVF7T.js";
import "./chunk-ZNALOMZJ.js";
import "./chunk-GHA2GP6C.js";
import "./chunk-H2CU6QHT.js";
import "./chunk-6PMAHZVS.js";
import "./chunk-DOEZTQY3.js";
import "./chunk-O4BCOVEV.js";
import "./chunk-TD4SIQNM.js";
import "./chunk-VU5YACRM.js";
import {
  __async,
  __esm
} from "./chunk-4W6HR7MY.js";

// src/app/pages/tabs/journeys/journey-details/journey-details.page.ts
function JourneyDetailsPage_div_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 8);
    \u0275\u0275element(1, "ion-spinner", 9);
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Carregando detalhes da viagem...");
    \u0275\u0275elementEnd()();
  }
}
function JourneyDetailsPage_div_8_p_21_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p")(1, "strong");
    \u0275\u0275text(2, "Ve\xEDculo:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", ctx_r0.journey.vehicleId, "");
  }
}
function JourneyDetailsPage_div_8_ion_badge_26_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-badge", 1);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.journey.infosJourney == null ? null : ctx_r0.journey.infosJourney.length, " ");
  }
}
function JourneyDetailsPage_div_8_div_28_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 11);
    \u0275\u0275element(1, "ion-icon", 12);
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Nenhuma localiza\xE7\xE3o registrada para esta viagem.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "p")(5, "small");
    \u0275\u0275text(6, "As localiza\xE7\xF5es s\xE3o registradas automaticamente durante a viagem.");
    \u0275\u0275elementEnd()()();
  }
}
function JourneyDetailsPage_div_8_ion_list_29_ion_item_1_p_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p");
    \u0275\u0275element(1, "ion-icon", 17);
    \u0275\u0275elementStart(2, "strong");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const info_r2 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(info_r2.address);
  }
}
function JourneyDetailsPage_div_8_ion_list_29_ion_item_1_p_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p", 18);
    \u0275\u0275element(1, "ion-spinner", 19);
    \u0275\u0275text(2, " Carregando endere\xE7o... ");
    \u0275\u0275elementEnd();
  }
}
function JourneyDetailsPage_div_8_ion_list_29_ion_item_1_p_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p");
    \u0275\u0275element(1, "ion-icon", 20);
    \u0275\u0275elementStart(2, "span", 21);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const info_r2 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(info_r2.occurrenceType);
  }
}
function JourneyDetailsPage_div_8_ion_list_29_ion_item_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-item")(1, "ion-avatar", 2)(2, "div", 14);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(4, "ion-label")(5, "h3");
    \u0275\u0275text(6);
    \u0275\u0275pipe(7, "date");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "p");
    \u0275\u0275element(9, "ion-icon", 15);
    \u0275\u0275text(10);
    \u0275\u0275pipe(11, "number");
    \u0275\u0275pipe(12, "number");
    \u0275\u0275elementEnd();
    \u0275\u0275template(13, JourneyDetailsPage_div_8_ion_list_29_ion_item_1_p_13_Template, 4, 1, "p", 6)(14, JourneyDetailsPage_div_8_ion_list_29_ion_item_1_p_14_Template, 3, 0, "p", 16)(15, JourneyDetailsPage_div_8_ion_list_29_ion_item_1_p_15_Template, 4, 1, "p", 6);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const info_r2 = ctx.$implicit;
    const i_r3 = ctx.index;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(i_r3 + 1);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(7, 7, info_r2.timestamp, "short"));
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate2(" ", \u0275\u0275pipeBind2(11, 10, info_r2.latitude, "1.6-6"), ", ", \u0275\u0275pipeBind2(12, 13, info_r2.longitude, "1.6-6"), " ");
    \u0275\u0275advance(3);
    \u0275\u0275property("ngIf", info_r2.address);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !info_r2.address);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", info_r2.occurrenceType);
  }
}
function JourneyDetailsPage_div_8_ion_list_29_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-list");
    \u0275\u0275template(1, JourneyDetailsPage_div_8_ion_list_29_ion_item_1_Template, 16, 16, "ion-item", 13);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r0.journey.infosJourney);
  }
}
function JourneyDetailsPage_div_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div")(1, "ion-card")(2, "ion-card-header")(3, "ion-card-title");
    \u0275\u0275text(4, "Resumo da Viagem");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(5, "ion-card-content")(6, "p")(7, "strong");
    \u0275\u0275text(8, "In\xEDcio:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(9);
    \u0275\u0275pipe(10, "date");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "p")(12, "strong");
    \u0275\u0275text(13, "Fim:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(14);
    \u0275\u0275pipe(15, "date");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(16, "p")(17, "strong");
    \u0275\u0275text(18, "Dist\xE2ncia:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(19);
    \u0275\u0275pipe(20, "number");
    \u0275\u0275elementEnd();
    \u0275\u0275template(21, JourneyDetailsPage_div_8_p_21_Template, 4, 1, "p", 6);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(22, "ion-card")(23, "ion-card-header")(24, "ion-card-title");
    \u0275\u0275text(25, " Localiza\xE7\xF5es Registradas ");
    \u0275\u0275template(26, JourneyDetailsPage_div_8_ion_badge_26_Template, 2, 1, "ion-badge", 10);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(27, "ion-card-content");
    \u0275\u0275template(28, JourneyDetailsPage_div_8_div_28_Template, 7, 0, "div", 7)(29, JourneyDetailsPage_div_8_ion_list_29_Template, 2, 1, "ion-list", 6);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(9);
    \u0275\u0275textInterpolate1(" ", \u0275\u0275pipeBind2(10, 7, ctx_r0.journey.startDate, "short"), "");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1(" ", ctx_r0.journey.endDate ? \u0275\u0275pipeBind2(15, 10, ctx_r0.journey.endDate, "short") : "Em andamento", "");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1(" ", \u0275\u0275pipeBind2(20, 13, ctx_r0.journey.distance, "1.2-2"), " km");
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r0.journey.vehicleId);
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", ctx_r0.journey.infosJourney == null ? null : ctx_r0.journey.infosJourney.length);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", !ctx_r0.journey.infosJourney || ctx_r0.journey.infosJourney.length === 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.journey.infosJourney && ctx_r0.journey.infosJourney.length > 0);
  }
}
function JourneyDetailsPage_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 11);
    \u0275\u0275element(1, "ion-icon", 22);
    \u0275\u0275elementStart(2, "h2");
    \u0275\u0275text(3, "Viagem n\xE3o encontrada");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "p");
    \u0275\u0275text(5, "N\xE3o foi poss\xEDvel carregar os detalhes desta viagem.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "ion-button", 23);
    \u0275\u0275text(7, " Voltar \xE0s Viagens ");
    \u0275\u0275elementEnd()();
  }
}
var _JourneyDetailsPage, JourneyDetailsPage;
var init_journey_details_page = __esm({
  "src/app/pages/tabs/journeys/journey-details/journey-details.page.ts"() {
    init_core();
    init_ionic_angular();
    init_common();
    init_core();
    init_router();
    init_data_storage_service();
    init_journey_storage_service();
    init_toast_service();
    init_ionic_angular();
    init_common();
    _JourneyDetailsPage = class _JourneyDetailsPage {
      constructor(route, dataStorageService, journeyStorageService, toastService) {
        this.route = route;
        this.dataStorageService = dataStorageService;
        this.journeyStorageService = journeyStorageService;
        this.toastService = toastService;
        this.isLoading = true;
      }
      ngOnInit() {
        return __async(this, null, function* () {
          var _a;
          const journeyId = this.route.snapshot.paramMap.get("id");
          const journeyStr = this.route.snapshot.queryParamMap.get("data");
          console.log("Journey Details - ID:", journeyId);
          console.log("Journey Details - Data from query:", journeyStr);
          if (journeyStr) {
            try {
              this.journey = JSON.parse(journeyStr);
              console.log("Parsed journey:", this.journey);
              console.log("Journey infosJourney:", this.journey.infosJourney);
              console.log("Journey infosJourney length:", (_a = this.journey.infosJourney) == null ? void 0 : _a.length);
              console.log("Loading journey locations from database...");
              yield this.loadJourneyLocations(this.journey.id);
              if (this.journey.infosJourney && this.journey.infosJourney.length > 0) {
                console.log("Loading addresses for", this.journey.infosJourney.length, "locations");
                yield this.loadAddresses();
              } else {
                console.log("No locations found for this journey");
              }
            } catch (error) {
              console.error("Error parsing journey data:", error);
              this.toastService.showToast("Erro ao carregar dados da viagem", "danger");
            }
          } else if (journeyId) {
            console.log("No data in query params, loading from database...");
            yield this.loadJourneyFromDatabase(journeyId);
          }
          yield this.debugJourneyInfoTable();
          this.isLoading = false;
        });
      }
      /**
       * Load journey locations from database
       */
      loadJourneyLocations(journeyId) {
        return __async(this, null, function* () {
          try {
            console.log("Querying journeyInfo table for journeyId:", journeyId);
            const locations = yield this.dataStorageService.select("journeyInfo", `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);
            console.log("Raw locations from database:", locations);
            console.log("Locations type:", typeof locations);
            console.log("Is array:", Array.isArray(locations));
            if (Array.isArray(locations) && locations.length > 0) {
              this.journey.infosJourney = locations.map((location) => ({
                id: location.id,
                journeyId: location.journeyId,
                latitude: location.latitude,
                longitude: location.longitude,
                timestamp: location.timestamp,
                address: location.address,
                occurrenceType: location.occurrenceType,
                syncStatus: location.syncStatus,
                lastSyncDate: location.lastSyncDate ? new Date(location.lastSyncDate) : void 0
              }));
              console.log("Loaded", this.journey.infosJourney.length, "locations from database");
              console.log("Mapped locations:", this.journey.infosJourney);
            } else {
              console.log("No locations found in database for journey:", journeyId);
              console.log("Locations result:", locations);
              this.journey.infosJourney = [];
            }
          } catch (error) {
            console.error("Error loading journey locations:", error);
            this.journey.infosJourney = [];
          }
        });
      }
      /**
       * Load journey from database by ID
       */
      loadJourneyFromDatabase(journeyId) {
        return __async(this, null, function* () {
          try {
            const journeyData = yield this.dataStorageService.select("journeys", `WHERE id = '${journeyId}'`);
            if (Array.isArray(journeyData) && journeyData.length > 0) {
              const data = journeyData[0];
              this.journey = {
                id: data.id,
                startDate: data.startDate,
                endDate: data.endDate,
                distance: data.distance,
                userId: data.userId,
                vehicleId: data.vehicleId,
                infosJourney: [],
                syncStatus: data.syncStatus,
                lastSyncDate: data.lastSyncDate ? new Date(data.lastSyncDate) : void 0
              };
              yield this.loadJourneyLocations(journeyId);
              if (this.journey.infosJourney && this.journey.infosJourney.length > 0) {
                yield this.loadAddresses();
              }
            } else {
              console.error("Journey not found in database:", journeyId);
              this.toastService.showToast("Viagem n\xE3o encontrada", "danger");
            }
          } catch (error) {
            console.error("Error loading journey from database:", error);
            this.toastService.showToast("Erro ao carregar viagem", "danger");
          }
        });
      }
      /**
       * Load addresses for all journey locations
       */
      loadAddresses() {
        return __async(this, null, function* () {
          if (!this.journey.infosJourney)
            return;
          console.log("Loading addresses for journey locations...");
          const batchSize = 5;
          for (let i = 0; i < this.journey.infosJourney.length; i += batchSize) {
            const batch = this.journey.infosJourney.slice(i, i + batchSize);
            yield Promise.all(batch.map((info) => __async(this, null, function* () {
              if (!info.address) {
                try {
                  info.address = yield this.reverseGeocode(info.latitude, info.longitude);
                } catch (error) {
                  console.error("Error loading address for location:", error);
                  info.address = "Endere\xE7o n\xE3o dispon\xEDvel";
                }
              }
            })));
            if (i + batchSize < this.journey.infosJourney.length) {
              yield new Promise((resolve) => setTimeout(resolve, 1e3));
            }
          }
          console.log("Finished loading addresses");
        });
      }
      /**
       * Debug method to check journeyInfo table contents
       */
      debugJourneyInfoTable() {
        return __async(this, null, function* () {
          try {
            console.log("=== DEBUG: JourneyInfo Table ===");
            const allJourneyInfo = yield this.dataStorageService.select("journeyInfo", "");
            console.log("All journeyInfo records:", allJourneyInfo);
            console.log("Total journeyInfo records:", Array.isArray(allJourneyInfo) ? allJourneyInfo.length : 0);
            const allJourneys = yield this.dataStorageService.select("journeys", "");
            console.log("All journey records:", allJourneys);
            console.log("Total journey records:", Array.isArray(allJourneys) ? allJourneys.length : 0);
            console.log("=== END DEBUG ===");
          } catch (error) {
            console.error("Error in debug method:", error);
          }
        });
      }
      reverseGeocode(lat, lon) {
        return __async(this, null, function* () {
          try {
            const url = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lon}`;
            const response = yield fetch(url);
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = yield response.json();
            return data.display_name || "Endere\xE7o n\xE3o encontrado";
          } catch (error) {
            console.error("Error in reverse geocoding:", error);
            return "Endere\xE7o n\xE3o dispon\xEDvel";
          }
        });
      }
    };
    _JourneyDetailsPage.\u0275fac = function JourneyDetailsPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _JourneyDetailsPage)(\u0275\u0275directiveInject(ActivatedRoute), \u0275\u0275directiveInject(DataStorageService), \u0275\u0275directiveInject(JourneyStorageService), \u0275\u0275directiveInject(ToastService));
    };
    _JourneyDetailsPage.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _JourneyDetailsPage, selectors: [["app-journey-details"]], decls: 10, vars: 4, consts: [[3, "translucent"], ["color", "primary"], ["slot", "start"], ["defaultHref", "/tabs/journeys"], [1, "ion-padding"], ["class", "loading-container", 4, "ngIf"], [4, "ngIf"], ["class", "ion-text-center ion-padding", 4, "ngIf"], [1, "loading-container"], ["name", "crescent"], ["color", "primary", 4, "ngIf"], [1, "ion-text-center", "ion-padding"], ["name", "location-outline", "size", "large", "color", "medium"], [4, "ngFor", "ngForOf"], [1, "location-number"], ["name", "location-outline"], ["class", "loading-address", 4, "ngIf"], ["name", "home-outline"], [1, "loading-address"], ["name", "dots"], ["name", "warning-outline", "color", "warning"], [1, "occurrence-type"], ["name", "alert-circle-outline", "size", "large", "color", "danger"], ["routerLink", "/tabs/journeys", "color", "primary"]], template: function JourneyDetailsPage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header", 0)(1, "ion-toolbar", 1)(2, "ion-title");
        \u0275\u0275text(3, "Detalhes da Viagem");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(4, "ion-buttons", 2);
        \u0275\u0275element(5, "ion-back-button", 3);
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(6, "ion-content", 4);
        \u0275\u0275template(7, JourneyDetailsPage_div_7_Template, 4, 0, "div", 5)(8, JourneyDetailsPage_div_8_Template, 30, 16, "div", 6)(9, JourneyDetailsPage_div_9_Template, 8, 0, "div", 7);
        \u0275\u0275elementEnd();
      }
      if (rf & 2) {
        \u0275\u0275property("translucent", true);
        \u0275\u0275advance(7);
        \u0275\u0275property("ngIf", ctx.isLoading);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", !ctx.isLoading && ctx.journey);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", !ctx.isLoading && !ctx.journey);
      }
    }, dependencies: [IonicModule, IonAvatar, IonBadge, IonButton, IonButtons, IonCard, IonCardContent, IonCardHeader, IonCardTitle, IonContent, IonHeader, IonIcon, IonItem, IonLabel, IonList, IonSpinner, IonTitle, IonToolbar, IonBackButton, RouterLinkDelegateDirective, CommonModule, NgForOf, NgIf, DecimalPipe, DatePipe], styles: ["\n\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 50vh;\n}\n.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n}\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: var(--ion-color-medium);\n  font-size: 0.9rem;\n}\n.location-number[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  background-color: var(--ion-color-primary);\n  color: var(--ion-color-primary-contrast);\n  font-weight: bold;\n  font-size: 0.9rem;\n  border-radius: 50%;\n}\n.loading-address[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: var(--ion-color-medium);\n  font-style: italic;\n}\n.loading-address[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%] {\n  --color: var(--ion-color-medium);\n}\n.occurrence-type[_ngcontent-%COMP%] {\n  color: var(--ion-color-warning);\n  font-weight: 500;\n  text-transform: capitalize;\n}\nion-item[_ngcontent-%COMP%] {\n  --padding-start: 16px;\n  --inner-padding-end: 16px;\n}\nion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%] {\n  margin: 8px 0;\n}\nion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  color: var(--ion-color-dark);\n  font-weight: 600;\n  margin-bottom: 4px;\n}\nion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin: 4px 0;\n  font-size: 0.9rem;\n}\nion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  color: var(--ion-color-medium);\n}\nion-card[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n}\nion-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\nion-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]   ion-badge[_ngcontent-%COMP%] {\n  margin-left: 8px;\n}\nion-list[_ngcontent-%COMP%] {\n  background: transparent;\n}\nion-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%] {\n  --background: var(--ion-color-light);\n  margin-bottom: 8px;\n  border-radius: 8px;\n}\nion-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:last-child {\n  margin-bottom: 0;\n}\n/*# sourceMappingURL=journey-details.page.css.map */"] });
    JourneyDetailsPage = _JourneyDetailsPage;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(JourneyDetailsPage, [{
        type: Component,
        args: [{ selector: "app-journey-details", standalone: true, imports: [
          IonicModule,
          CommonModule
        ], template: `<ion-header [translucent]="true">
  <ion-toolbar color="primary">
    <ion-title>Detalhes da Viagem</ion-title>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/tabs/journeys"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Carregando detalhes da viagem...</p>
  </div>

  <!-- Journey content -->
  <div *ngIf="!isLoading && journey">
    <ion-card>
      <ion-card-header>
        <ion-card-title>Resumo da Viagem</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <p><strong>In\xEDcio:</strong> {{ journey.startDate | date:'short' }}</p>
        <p><strong>Fim:</strong> {{ journey.endDate ? (journey.endDate | date:'short') : 'Em andamento' }}</p>
        <p><strong>Dist\xE2ncia:</strong> {{ journey.distance | number:'1.2-2' }} km</p>
        <p *ngIf="journey.vehicleId"><strong>Ve\xEDculo:</strong> {{ journey.vehicleId }}</p>
      </ion-card-content>
    </ion-card>

    <ion-card>
      <ion-card-header>
        <ion-card-title>
          Localiza\xE7\xF5es Registradas
          <ion-badge color="primary" *ngIf="journey.infosJourney?.length">
            {{ journey.infosJourney?.length }}
          </ion-badge>
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <!-- No locations message -->
        <div *ngIf="!journey.infosJourney || journey.infosJourney.length === 0" class="ion-text-center ion-padding">
          <ion-icon name="location-outline" size="large" color="medium"></ion-icon>
          <p>Nenhuma localiza\xE7\xE3o registrada para esta viagem.</p>
          <p><small>As localiza\xE7\xF5es s\xE3o registradas automaticamente durante a viagem.</small></p>
        </div>

        <!-- Locations list -->
        <ion-list *ngIf="journey.infosJourney && journey.infosJourney.length > 0">
          <ion-item *ngFor="let info of journey.infosJourney; let i = index">
            <ion-avatar slot="start">
              <div class="location-number">{{ i + 1 }}</div>
            </ion-avatar>
            <ion-label>
              <h3>{{ info.timestamp | date:'short' }}</h3>
              <p>
                <ion-icon name="location-outline"></ion-icon>
                {{ info.latitude | number:'1.6-6' }}, {{ info.longitude | number:'1.6-6' }}
              </p>
              <p *ngIf="info.address">
                <ion-icon name="home-outline"></ion-icon>
                <strong>{{ info.address }}</strong>
              </p>
              <p *ngIf="!info.address" class="loading-address">
                <ion-spinner name="dots"></ion-spinner>
                Carregando endere\xE7o...
              </p>
              <p *ngIf="info.occurrenceType">
                <ion-icon name="warning-outline" color="warning"></ion-icon>
                <span class="occurrence-type">{{ info.occurrenceType }}</span>
              </p>
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Error state -->
  <div *ngIf="!isLoading && !journey" class="ion-text-center ion-padding">
    <ion-icon name="alert-circle-outline" size="large" color="danger"></ion-icon>
    <h2>Viagem n\xE3o encontrada</h2>
    <p>N\xE3o foi poss\xEDvel carregar os detalhes desta viagem.</p>
    <ion-button routerLink="/tabs/journeys" color="primary">
      Voltar \xE0s Viagens
    </ion-button>
  </div>

</ion-content>
`, styles: ["/* src/app/pages/tabs/journeys/journey-details/journey-details.page.scss */\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 50vh;\n}\n.loading-container ion-spinner {\n  margin-bottom: 16px;\n}\n.loading-container p {\n  color: var(--ion-color-medium);\n  font-size: 0.9rem;\n}\n.location-number {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  background-color: var(--ion-color-primary);\n  color: var(--ion-color-primary-contrast);\n  font-weight: bold;\n  font-size: 0.9rem;\n  border-radius: 50%;\n}\n.loading-address {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: var(--ion-color-medium);\n  font-style: italic;\n}\n.loading-address ion-spinner {\n  --color: var(--ion-color-medium);\n}\n.occurrence-type {\n  color: var(--ion-color-warning);\n  font-weight: 500;\n  text-transform: capitalize;\n}\nion-item {\n  --padding-start: 16px;\n  --inner-padding-end: 16px;\n}\nion-item ion-label {\n  margin: 8px 0;\n}\nion-item ion-label h3 {\n  color: var(--ion-color-dark);\n  font-weight: 600;\n  margin-bottom: 4px;\n}\nion-item ion-label p {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin: 4px 0;\n  font-size: 0.9rem;\n}\nion-item ion-label p ion-icon {\n  font-size: 1rem;\n  color: var(--ion-color-medium);\n}\nion-card {\n  margin-bottom: 16px;\n}\nion-card ion-card-title {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\nion-card ion-card-title ion-badge {\n  margin-left: 8px;\n}\nion-list {\n  background: transparent;\n}\nion-list ion-item {\n  --background: var(--ion-color-light);\n  margin-bottom: 8px;\n  border-radius: 8px;\n}\nion-list ion-item:last-child {\n  margin-bottom: 0;\n}\n/*# sourceMappingURL=journey-details.page.css.map */\n"] }]
      }], () => [{ type: ActivatedRoute }, { type: DataStorageService }, { type: JourneyStorageService }, { type: ToastService }], null);
    })();
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(JourneyDetailsPage, { className: "JourneyDetailsPage", filePath: "src/app/pages/tabs/journeys/journey-details/journey-details.page.ts", lineNumber: 22 });
    })();
  }
});
init_journey_details_page();
export {
  JourneyDetailsPage
};
//# sourceMappingURL=journey-details.page-ONDSAKCI.js.map
