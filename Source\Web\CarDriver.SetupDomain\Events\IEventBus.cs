﻿using GoodDriver.SetupDomain.Cqrs.Domain;
using GoodDriver.SetupDomain.Cqrs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.SetupDomain.Events
{
    public interface IEventBus : IBus, IDisposable
    {
        //void AddObserver(IEventObserver eventObserver);

        //Task PublishAsync<T>(T eventMessage, string topicOrQueue = null) where T : class, IEvent;

        //void Register<T>(IEventHandler<T> eventHandler, string topicOrQueue = null) where T : class, IEvent;

        //void Unregister<T>(IEventHandler<T> eventHandler, string topicOrQueue = null) where T : class, IEvent;

        //bool HasRegistered<T>() where T : class, IEvent;
    }
}
