{"version": 3, "sources": ["node_modules/@ionic/angular/src/css/core.scss", "node_modules/@ionic/angular/src/components/modal/modal.vars.scss", "node_modules/@ionic/angular/src/themes/ionic.mixins.scss", "node_modules/@ionic/angular/src/themes/ionic.globals.scss", "node_modules/@ionic/angular/src/components/menu/menu.ios.vars.scss", "node_modules/@ionic/angular/src/components/menu/menu.md.vars.scss", "node_modules/@ionic/angular/src/css/normalize.scss", "node_modules/@ionic/angular/src/css/structure.scss", "node_modules/@ionic/angular/src/themes/ionic.mixins.scss", "node_modules/@ionic/angular/src/css/typography.scss", "node_modules/@ionic/angular/src/themes/ionic.mixins.scss", "node_modules/@ionic/angular/src/css/display.scss", "node_modules/@ionic/angular/src/themes/ionic.mixins.scss", "node_modules/@ionic/angular/src/css/padding.scss", "node_modules/@ionic/angular/src/themes/ionic.mixins.scss", "node_modules/@ionic/angular/src/css/float-elements.scss", "node_modules/@ionic/angular/src/themes/ionic.mixins.scss", "node_modules/@ionic/angular/src/css/text-alignment.scss", "node_modules/@ionic/angular/src/themes/ionic.mixins.scss", "node_modules/@ionic/angular/src/css/text-transformation.scss", "node_modules/@ionic/angular/src/themes/ionic.mixins.scss", "node_modules/@ionic/angular/src/css/flex-utils.scss", "node_modules/@ionic/angular/src/css/palettes/dark.system.scss", "node_modules/@ionic/angular/src/css/palettes/dark.scss", "src/global.scss", "src/theme/components.scss", "src/theme/utilities.scss", "src/theme/variables.scss"], "sourcesContent": ["@use \"sass:map\";\n@import \"../themes/ionic.globals\";\n@import \"../components/menu/menu.ios.vars\";\n@import \"../components/menu/menu.md.vars\";\n@import \"../components/modal/modal.vars\";\n\n:root {\n  /**\n   * Loop through each color object from the\n   * `ionic.theme.default.scss` file\n   * and generate CSS Variables for each color.\n   */\n  @each $color-name, $value in $colors {\n    --ion-color-#{$color-name}: #{map.get($value, base)};\n    --ion-color-#{$color-name}-rgb: #{color-to-rgb-list(map.get($value, base))};\n    --ion-color-#{$color-name}-contrast: #{map.get($value, contrast)};\n    --ion-color-#{$color-name}-contrast-rgb: #{color-to-rgb-list(map.get($value, contrast))};\n    --ion-color-#{$color-name}-shade: #{map.get($value, shade)};\n    --ion-color-#{$color-name}-tint: #{map.get($value, tint)};\n  }\n}\n\n// Ionic Font Family\n// --------------------------------------------------\n\nhtml.ios {\n  --ion-default-font: -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Roboto\", sans-serif;\n}\nhtml.md {\n  --ion-default-font: \"Roboto\", \"Helvetica Neue\", sans-serif;\n}\n\nhtml {\n  --ion-dynamic-font: -apple-system-body;\n  --ion-font-family: var(--ion-default-font);\n}\n\nbody {\n  background: var(--ion-background-color);\n  color: var(--ion-text-color);\n}\n\nbody.backdrop-no-scroll {\n  overflow: hidden;\n}\n\n// Modal - Card Style\n// --------------------------------------------------\n/**\n * Card style modal needs additional padding on the\n * top of the header. We accomplish this by targeting\n * the first toolbar in the header.\n * Footer also needs this. We do not adjust the bottom\n * padding though because of the safe area.\n */\nhtml.ios ion-modal.modal-card ion-header ion-toolbar:first-of-type,\nhtml.ios ion-modal.modal-sheet ion-header ion-toolbar:first-of-type,\nhtml.ios ion-modal ion-footer ion-toolbar:first-of-type {\n  padding-top: $modal-sheet-padding-top;\n}\n\n/**\n* Card style modal needs additional padding on the\n* bottom of the header. We accomplish this by targeting\n* the last toolbar in the header.\n*/\nhtml.ios ion-modal.modal-card ion-header ion-toolbar:last-of-type,\nhtml.ios ion-modal.modal-sheet ion-header ion-toolbar:last-of-type {\n  padding-bottom: $modal-sheet-padding-bottom;\n}\n\n/**\n* Add padding on the left and right\n* of toolbars while accounting for\n* safe area values when in landscape.\n*/\nhtml.ios ion-modal ion-toolbar {\n  padding-right: calc(var(--ion-safe-area-right) + 8px);\n  padding-left: calc(var(--ion-safe-area-left) + 8px);\n}\n\n/**\n * Card style modal on iPadOS\n * should only have backdrop on first instance.\n */\n@media screen and (min-width: 768px) {\n  html.ios ion-modal.modal-card:first-of-type {\n    --backdrop-opacity: 0.18;\n  }\n}\n\n/**\n * Subsequent modals should not have a backdrop/box shadow\n * as it will cause the screen to appear to get progressively\n * darker. With Ionic 6, declarative modals made it\n * possible to have multiple non-presented modals in the DOM,\n * so we could no longer rely on ion-modal:first-of-type.\n * Here we disable the opacity/box-shadow for every modal\n * that comes after the first presented modal.\n *\n * Note: ion-modal:not(.overlay-hidden):first-of-type\n * does not match the first modal to not have\n * the .overlay-hidden class, it will match the\n * first modal in general only if it does not\n * have the .overlay-hidden class.\n * The :nth-child() pseudo-class has support\n * for selectors which would help us here. At the\n * time of writing it does not have great cross browser\n * support.\n *\n * Note 2: This should only apply to non-card and\n * non-sheet modals. Card and sheet modals have their\n * own criteria for displaying backdrops/box shadows.\n *\n * Do not use :not(.overlay-hidden) in place of\n * .show-modal because that triggers a memory\n * leak in Blink: https://bugs.chromium.org/p/chromium/issues/detail?id=1418768\n */\nion-modal.modal-default.show-modal ~ ion-modal.modal-default {\n  --backdrop-opacity: 0;\n  --box-shadow: none;\n}\n\n/**\n * This works around a bug in WebKit where the\n * content will overflow outside of the bottom border\n * radius when re-painting. As long as a single\n * border radius value is set on .ion-page, this\n * issue does not happen. We set the top left radius\n * here because the top left corner will always have a\n * radius no matter the platform.\n * This behavior only applies to card modals.\n */\nhtml.ios ion-modal.modal-card .ion-page {\n  border-top-left-radius: var(--border-radius);\n}\n\n// Ionic Colors\n// --------------------------------------------------\n// Generates the color classes and variables based on the\n// colors map\n\n@mixin generate-color($color-name) {\n  $value: map-get($colors, $color-name);\n\n  $base: map-get($value, base);\n  $contrast: map-get($value, contrast);\n  $shade: map-get($value, shade);\n  $tint: map-get($value, tint);\n\n  --ion-color-base: var(--ion-color-#{$color-name}, #{$base}) !important;\n  --ion-color-base-rgb: var(--ion-color-#{$color-name}-rgb, #{color-to-rgb-list($base)}) !important;\n  --ion-color-contrast: var(--ion-color-#{$color-name}-contrast, #{$contrast}) !important;\n  --ion-color-contrast-rgb: var(--ion-color-#{$color-name}-contrast-rgb, #{color-to-rgb-list($contrast)}) !important;\n  --ion-color-shade: var(--ion-color-#{$color-name}-shade, #{$shade}) !important;\n  --ion-color-tint: var(--ion-color-#{$color-name}-tint, #{$tint}) !important;\n}\n\n@each $color-name, $value in $colors {\n  .ion-color-#{$color-name} {\n    @include generate-color($color-name);\n  }\n}\n\n\n// Page Container Structure\n// --------------------------------------------------\n\n.ion-page {\n  @include position(0, 0, 0, 0);\n\n  display: flex;\n  position: absolute;\n\n  flex-direction: column;\n  justify-content: space-between;\n\n  contain: layout size style;\n  z-index: $z-index-page-container;\n}\n\n/**\n * When making custom dialogs, using\n * ion-content is not required. As a result,\n * some developers may wish to have dialogs\n * that are automatically sized by the browser.\n * These changes allow certain dimension values\n * such as fit-content to work correctly.\n */\nion-modal > .ion-page {\n  position: relative;\n\n  contain: layout style;\n\n  height: 100%;\n}\n\n.split-pane-visible > .ion-page.split-pane-main {\n  position: relative;\n}\n\nion-route,\nion-route-redirect,\nion-router,\nion-select-option,\nion-nav-controller,\nion-menu-controller,\nion-action-sheet-controller,\nion-alert-controller,\nion-loading-controller,\nion-modal-controller,\nion-picker-controller,\nion-popover-controller,\nion-toast-controller,\n.ion-page-hidden {\n  /* stylelint-disable-next-line declaration-no-important */\n  display: none !important;\n}\n\n.ion-page-invisible {\n  opacity: 0;\n}\n\n.can-go-back > ion-header ion-back-button {\n  display: block;\n}\n\n\n// Ionic Safe Margins\n// --------------------------------------------------\n\nhtml.plt-ios.plt-hybrid, html.plt-ios.plt-pwa {\n  --ion-statusbar-padding: 20px;\n}\n\n@supports (padding-top: 20px) {\n  html {\n    --ion-safe-area-top: var(--ion-statusbar-padding);\n  }\n}\n\n@supports (padding-top: env(safe-area-inset-top)) {\n  html {\n    --ion-safe-area-top: env(safe-area-inset-top);\n    --ion-safe-area-bottom: env(safe-area-inset-bottom);\n    --ion-safe-area-left: env(safe-area-inset-left);\n    --ion-safe-area-right: env(safe-area-inset-right);\n  }\n}\n\n\n// Global Card Styles\n// --------------------------------------------------\n\nion-card.ion-color .ion-inherit-color,\nion-card-header.ion-color .ion-inherit-color {\n  color: inherit;\n}\n\n\n// Menu Styles\n// --------------------------------------------------\n\n.menu-content {\n  @include transform(translate3d(0, 0, 0));\n}\n\n.menu-content-open {\n  cursor: pointer;\n  touch-action: manipulation;\n\n  /**\n   * The containing element itself should be clickable but\n   * everything inside of it should not clickable when menu is open\n   *\n   * Setting pointer-events after scrolling has already started\n   * will not cancel scrolling which is why we also set\n   * overflow-y below.\n   */\n  pointer-events: none;\n\n  /**\n   * This accounts for scenarios where the main content itself\n   * is scrollable.\n   */\n  overflow-y: hidden;\n}\n\n/**\n * Setting overflow cancels any in-progress scrolling\n * when the menu opens. This prevents users from accidentally\n * scrolling the main content while also dragging the menu open.\n * The code below accounts for both ion-content and then custom\n * scroll containers within ion-content (such as virtual scroll)\n */\n.menu-content-open ion-content {\n  --overflow: hidden;\n}\n\n.menu-content-open .ion-content-scroll-host {\n  overflow: hidden;\n}\n\n.ios .menu-content-reveal {\n  box-shadow: $menu-ios-box-shadow-reveal;\n}\n\n[dir=rtl].ios .menu-content-reveal {\n  box-shadow: $menu-ios-box-shadow-reveal-rtl;\n}\n\n.ios .menu-content-push {\n  box-shadow: $menu-ios-box-shadow-push;\n}\n\n.md .menu-content-reveal {\n  box-shadow: $menu-md-box-shadow;\n}\n\n.md .menu-content-push {\n  box-shadow: $menu-md-box-shadow;\n}\n\n// Accordion Styles\nion-accordion-group.accordion-group-expand-inset > ion-accordion:first-of-type {\n  border-top-left-radius: 8px;\n  border-top-right-radius: 8px;\n}\nion-accordion-group.accordion-group-expand-inset > ion-accordion:last-of-type {\n  border-bottom-left-radius: 8px;\n  border-bottom-right-radius: 8px;\n}\nion-accordion-group > ion-accordion:last-of-type ion-item[slot=\"header\"] {\n  --border-width: 0px;\n}\n\nion-accordion.accordion-animated > [slot=\"header\"] .ion-accordion-toggle-icon {\n  transition: 300ms transform cubic-bezier(0.25, 0.8, 0.5, 1);\n}\n\n@media (prefers-reduced-motion: reduce) {\n  ion-accordion .ion-accordion-toggle-icon {\n    /* stylelint-disable declaration-no-important */\n    transition: none !important;\n  }\n}\n/**\n * The > [slot=\"header\"] selector ensures that we do\n * not modify toggle icons for any nested accordions. The state\n * of one accordion should not affect any accordions inside\n * of a nested accordion group.\n */\nion-accordion.accordion-expanding > [slot=\"header\"] .ion-accordion-toggle-icon,\nion-accordion.accordion-expanded > [slot=\"header\"] .ion-accordion-toggle-icon {\n  transform: rotate(180deg);\n}\n\nion-accordion-group.accordion-group-expand-inset.md > ion-accordion.accordion-previous ion-item[slot=\"header\"] {\n  --border-width: 0px;\n  --inner-border-width: 0px;\n}\n\nion-accordion-group.accordion-group-expand-inset.md > ion-accordion.accordion-expanding:first-of-type,\nion-accordion-group.accordion-group-expand-inset.md > ion-accordion.accordion-expanded:first-of-type {\n  margin-top: 0;\n}\n\n// Safari/iOS 15 changes the appearance of input[type=\"date\"].\n// For backwards compatibility from Ionic 5/Safari 14 designs,\n// we override the appearance only when using within an ion-input.\nion-input input::-webkit-date-and-time-value {\n  text-align: start;\n}\n\n/**\n * The .ion-datetime-button-overlay class contains\n * styles that allow any modal/popover to be\n * sized according to the dimensions of the datetime\n * when used with ion-datetime-button.\n */\n.ion-datetime-button-overlay {\n  --width: fit-content;\n  --height: fit-content;\n}\n\n/**\n * The grid variant can scale down when inline.\n * When used in a `fit-content` overlay, this causes\n * the overlay to shrink when the month/year picker is open.\n * Explicitly setting the dimensions lets us have a consistently\n * sized grid interface.\n */\n.ion-datetime-button-overlay ion-datetime.datetime-grid {\n  width: 320px;\n  min-height: 320px;\n}\n\n/**\n * When moving focus on page transitions we call .focus() on an element which can\n * add an undesired outline ring. This CSS removes the outline ring.\n * We also remove the outline ring from elements that are actively being focused\n * by the focus manager. We are intentionally selective about which elements this\n * applies to so we do not accidentally override outlines set by the developer.\n */\n[ion-last-focus],\nheader[tabindex=\"-1\"]:focus,\n[role=\"banner\"][tabindex=\"-1\"]:focus,\nmain[tabindex=\"-1\"]:focus,\n[role=\"main\"][tabindex=\"-1\"]:focus,\nh1[tabindex=\"-1\"]:focus,\n[role=\"heading\"][aria-level=\"1\"][tabindex=\"-1\"]:focus {\n  outline: none;\n}\n\n/*\n * If a popover has a child ion-content (or class equivalent) then the .popover-viewport element\n * should not be scrollable to ensure the inner content does scroll. However, if the popover\n * does not have a child ion-content (or class equivalent) then the .popover-viewport element\n * should remain scrollable. This code exists globally because popover targets\n * .popover-viewport using ::slotted which only supports simple selectors.\n *\n * Note that we do not need to account for .ion-content-scroll-host here because that\n * class should always be placed within ion-content even if ion-content is not scrollable.\n */\n.popover-viewport:has(> ion-content) {\n  overflow: hidden;\n}\n\n/**\n * :has has cross-browser support, but it is still relatively new. As a result,\n * we should fallback to the old behavior for environments that do not support :has.\n * Developers can explicitly enable this behavior by setting overflow: visible\n * on .popover-viewport if they know they are not going to use an ion-content.\n * TODO FW-6106 Remove this\n */\n@supports not selector(:has(> ion-content)) {\n  .popover-viewport {\n    overflow: hidden;\n  }\n}\n", "@import \"../../themes/ionic.globals\";\n\n// Modals\n// --------------------------------------------------\n\n/// @prop - Min width of the modal inset\n$modal-inset-min-width:         768px;\n\n/// @prop - Minimum height of the small modal inset\n$modal-inset-min-height-small:  600px;\n\n/// @prop - Minimum height of the large modal inset\n$modal-inset-min-height-large:  768px;\n\n/// @prop - Width of the large modal inset\n$modal-inset-width:             600px;\n\n/// @prop - Height of the small modal inset\n$modal-inset-height-small:      500px;\n\n/// @prop - Height of the large modal inset\n$modal-inset-height-large:      600px;\n\n/// @prop - Text color of the modal content\n$modal-text-color:              $text-color;\n\n/// @prop - Padding top of the sheet modal\n$modal-sheet-padding-top:        6px;\n\n/// @prop - Padding bottom of the sheet modal\n$modal-sheet-padding-bottom:     6px;\n", "\n/**\n * A heuristic that applies CSS to tablet\n * viewports.\n *\n * Usage:\n * @include tablet-viewport() {\n *   :host {\n *     background-color: green;\n *   }\n * }\n */\n@mixin tablet-viewport() {\n  @media screen and (min-width: 768px) {\n    @content;\n  }\n}\n\n/**\n * A heuristic that applies CSS to mobile\n * viewports (i.e. phones, not tablets).\n *\n * Usage:\n * @include mobile-viewport() {\n *   :host {\n *     background-color: blue;\n *   }\n * }\n */\n@mixin mobile-viewport() {\n  @media screen and (max-width: 767px) {\n    @content;\n  }\n}\n\n@mixin input-cover() {\n  @include position(0, null, null, 0);\n  @include margin(0);\n\n  position: absolute;\n\n  width: 100%;\n  height: 100%;\n\n  border: 0;\n  background: transparent;\n  cursor: pointer;\n\n  appearance: none;\n  outline: none;\n\n  &::-moz-focus-inner {\n    border: 0;\n  }\n}\n\n@mixin visually-hidden() {\n  position: absolute;\n\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n\n  width: 100%;\n  height: 100%;\n\n  margin: 0;\n  padding: 0;\n\n  border: 0;\n  outline: 0;\n  clip: rect(0 0 0 0);\n\n  opacity: 0;\n  overflow: hidden;\n\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n@mixin text-inherit() {\n  font-family: inherit;\n  font-size: inherit;\n  font-style: inherit;\n  font-weight: inherit;\n  letter-spacing: inherit;\n  text-decoration: inherit;\n  text-indent: inherit;\n  text-overflow: inherit;\n  text-transform: inherit;\n  text-align: inherit;\n  white-space: inherit;\n  color: inherit;\n}\n\n@mixin button-state() {\n  @include position(0, 0, 0, 0);\n\n  position: absolute;\n\n  content: \"\";\n\n  opacity: 0;\n}\n\n// Font smoothing\n// --------------------------------------------------\n\n@mixin font-smoothing() {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n}\n\n// Get the key from a map based on the index\n@function index-to-key($map, $index) {\n  $keys: map-keys($map);\n\n  @return nth($keys, $index);\n}\n\n\n// Breakpoint Mixins\n// ---------------------------------------------------------------------------------\n\n// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$screen-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// ---------------------------------------------------------------------------------\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $screen-breakpoints) {\n  $min: map-get($breakpoints, $name);\n\n  @return if($name != index-to-key($breakpoints, 1), $min, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash infront.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $screen-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $screen-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $screen-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Maximum breakpoint width. Null for the smallest (first) breakpoint.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n//\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\t// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\t// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $screen-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $screen-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n\n// Text Direction - ltr / rtl\n//\n// CSS defaults to use the ltr css, and adds [dir=rtl] selectors\n// to override ltr defaults.\n// ----------------------------------------------------------\n\n@mixin multi-dir() {\n  @content;\n\n  // $root: #{&};\n  // @at-root [dir] {\n  //   #{$root} {\n  //     @content;\n  //   }\n  // }\n}\n\n@mixin rtl() {\n  $root: #{&};\n\n  $rootSplit: str-split($root, \",\");\n  $selectors: #{add-root-selector($root, \"[dir=rtl]\")};\n  $selectorsSplit: str-split($selectors, \",\");\n\n  $hostContextSelectors: ();\n  $restSelectors: ();\n  $dirSelectors: ();\n\n  // Selectors must be split into individual selectors in case the browser\n  // doesn't support a specific selector.\n  // For example, Firefox and Safari doesn't support `:host-context()`.\n  // If an invalid selector is used, then the entire group of selectors\n  // will be ignored.\n  // @link https://www.w3.org/TR/selectors-3/#grouping\n  @each $selector in $selectorsSplit {\n    // Group the selectors back into a single selector to optimize the output.\n    @if str-index($selector, \":host-context\") {\n      $hostContextSelectors: append($hostContextSelectors, $selector, comma);\n    } @else {\n      // Group the selectors back into a single selector to optimize the output.\n      $restSelectors: append($restSelectors, $selector, comma);\n    }\n  }\n\n  // Supported by Chrome.\n  @if length($hostContextSelectors) > 0 {\n    @at-root #{$hostContextSelectors} {\n      @content;\n    }\n  }\n\n  // Supported by all browsers.\n  @if length($restSelectors) > 0 {\n    @at-root #{$restSelectors} {\n      @content;\n    }\n  }\n\n  // If browser can support `:dir()`, then add the `:dir()` selectors.\n  @supports selector(:dir(rtl)) {\n    // Adding :dir() in case the browser doesn't support `:host-context()` and does support `:dir()`.\n    // `:host-context()` is added:\n    // - through the `add-root-selector()` function.\n    // - first so that it takes precedence over `:dir()`.\n    // For example,\n    // - Firefox doesn't support `:host-context()`, but does support `:dir()`.\n    // - Safari doesn't support `:host-context()`, but Safari 16.4+ supports `:dir()`\n    // @link https://webkit.org/blog/13966/webkit-features-in-safari-16-4/\n    // -- However, there is a Webkit bug on v16 that prevents `:dir()` from working when\n    // -- the app direction is changed dynamically. v17+ works fine.\n    // -- @link https://bugs.webkit.org/show_bug.cgi?id=257133\n\n    // Supported by Firefox.\n    @at-root #{add-root-selector($root, \":dir(rtl)\", false)} {\n      @content;\n    }\n  }\n}\n\n@mixin ltr() {\n  @content;\n}\n\n\n// SVG Background Image Mixin\n// @param {string} $svg\n// ----------------------------------------------------------\n@mixin svg-background-image($svg, $flip-rtl: false) {\n  $url: url-encode($svg);\n  $viewBox: str-split(str-extract($svg, \"viewBox='\", \"'\"), \" \");\n\n  @if $flip-rtl != true or $viewBox == null {\n    @include multi-dir() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n  } @else {\n    $transform: \"transform='translate(#{nth($viewBox, 3)}, 0) scale(-1, 1)'\";\n    $flipped-url: $svg;\n    $flipped-url: str-replace($flipped-url, \"<path\", \"<path #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<line\", \"<line #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<polygon\", \"<polygon #{$transform}\");\n    $flipped-url: url-encode($flipped-url);\n\n    @include ltr () {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n    @include rtl() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$flipped-url}\");\n    }\n  }\n}\n\n// Add property horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin property-horizontal($prop, $start, $end: $start) {\n  @if $start == 0 and $end == 0 {\n    #{$prop}-left: $start;\n    #{$prop}-right: $end;\n\n  } @else {\n    -webkit-#{$prop}-start: $start;\n    #{$prop}-inline-start: $start;\n    -webkit-#{$prop}-end: $end;\n    #{$prop}-inline-end: $end;\n  }\n}\n\n// Add property for all directions\n// @param {string} $prop\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// @param {boolean} $content include content or use default\n// ----------------------------------------------------------\n@mixin property($prop, $top, $end: $top, $bottom: $top, $start: $end) {\n  @include property-horizontal($prop, $start, $end);\n  #{$prop}-top: $top;\n  #{$prop}-bottom: $bottom;\n}\n\n// Add padding horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin padding-horizontal($start, $end: $start) {\n  @include property-horizontal(padding, $start, $end);\n}\n\n// Add padding for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin padding($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(padding, $top, $end, $bottom, $start);\n}\n\n// Add margin horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin margin-horizontal($start, $end: $start) {\n  @include property-horizontal(margin, $start, $end);\n}\n\n// Add margin for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin margin($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(margin, $top, $end, $bottom, $start);\n}\n\n// Add position horizontal\n// @param {string} $start - amount to position start\n// @param {string} $end - amount to left: 0; end\n// ----------------------------------------------------------\n@mixin position-horizontal($start: null, $end: null) {\n  @if $start == $end {\n    @include multi-dir() {\n      left: $start;\n      right: $end;\n    }\n  } @else {\n    @at-root {\n      & {\n        inset-inline-start: $start;\n        inset-inline-end: $end;\n      }\n    }\n  }\n}\n\n// Add position for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin position($top: null, $end: null, $bottom: null, $start: null) {\n  @include position-horizontal($start, $end);\n  top: $top;\n  bottom: $bottom;\n}\n\n// Add border for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin border($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(border, $top, $end, $bottom, $start);\n}\n\n// Add border radius for all directions\n// @param {string} $top-start\n// @param {string} $top-end\n// @param {string} $bottom-end\n// @param {string} $bottom-start\n// ----------------------------------------------------------\n@mixin border-radius($top-start, $top-end: $top-start, $bottom-end: $top-start, $bottom-start: $top-end) {\n  @if $top-start == $top-end and $top-start == $bottom-end and $top-start == $bottom-start {\n    border-radius: $top-start;\n  } @else {\n    border-start-start-radius: $top-start;\n    border-start-end-radius: $top-end;\n    border-end-end-radius: $bottom-end;\n    border-end-start-radius: $bottom-start;\n  }\n}\n\n// Add direction for all directions\n// @param {string} $dir - Direction on LTR\n@mixin direction($dir) {\n  $other-dir: null;\n\n  @if $dir == ltr {\n    $other-dir: rtl;\n  } @else {\n    $other-dir: ltr;\n  }\n\n  @include ltr() {\n    direction: $dir;\n  }\n  @include rtl() {\n    direction: $other-dir;\n  }\n}\n\n// Add float for all directions\n// @param {string} $side\n// @param {string} $decorator - !important\n@mixin float($side, $decorator: null) {\n  @if $side == start {\n    @include ltr() {\n      float: left $decorator;\n    }\n    @include rtl() {\n      float: right $decorator;\n    }\n  } @else if $side == end {\n    @include ltr() {\n      float: right $decorator;\n    }\n    @include rtl() {\n      float: left $decorator;\n    }\n  } @else {\n    @include multi-dir() {\n      float: $side $decorator;\n    }\n  }\n}\n\n@mixin background-position($horizontal, $horizontal-amount: null, $vertical: null, $vertical-amount: null) {\n  @if $horizontal == start or $horizontal == end {\n    $horizontal-ltr: null;\n    $horizontal-rtl: null;\n    @if $horizontal == start {\n      $horizontal-ltr: left;\n      $horizontal-rtl: right;\n    } @else {\n      $horizontal-ltr: right;\n      $horizontal-rtl: left;\n    }\n\n    @include ltr() {\n      background-position: $horizontal-ltr $horizontal-amount $vertical $vertical-amount;\n    }\n    @include rtl() {\n      background-position: $horizontal-rtl $horizontal-amount $vertical $vertical-amount;\n    }\n  } @else {\n    @include multi-dir() {\n      background-position: $horizontal $horizontal-amount $vertical $vertical-amount;\n    }\n  }\n}\n\n@mixin transform-origin($x-axis, $y-axis: null) {\n  @if $x-axis == start {\n    @include ltr() {\n      transform-origin: left $y-axis;\n    }\n    @include rtl() {\n      transform-origin: right $y-axis;\n    }\n  } @else if $x-axis == end {\n    @include ltr() {\n      transform-origin: right $y-axis;\n    }\n    @include rtl() {\n      transform-origin: left $y-axis;\n    }\n  } @else if $x-axis == left or $x-axis == right {\n    @include multi-dir() {\n      transform-origin: $x-axis $y-axis;\n    }\n  } @else {\n    @include ltr() {\n      transform-origin: $x-axis $y-axis;\n    }\n    @include rtl() {\n      transform-origin: calc(100% - #{$x-axis}) $y-axis;\n    }\n  }\n}\n\n// Add transform for all directions\n// @param {string} $transforms - comma separated list of transforms\n@mixin transform($transforms...) {\n  $extra: null;\n\n  $x: null;\n  $ltr-translate: null;\n  $rtl-translate: null;\n\n  @each $transform in $transforms {\n    @if (str-index($transform, translate3d)) {\n      $transform: str-replace($transform, 'translate3d(');\n      $transform: str-replace($transform, ')');\n\n      $coordinates: str-split($transform, ',');\n\n      $x: nth($coordinates, 1);\n      $y: nth($coordinates, 2);\n      $z: nth($coordinates, 3);\n\n      $ltr-translate: translate3d($x, $y, $z);\n      $rtl-translate: translate3d(calc(-1 * #{$x}), $y, $z);\n    } @else {\n      @if $extra == null {\n        $extra: $transform;\n      } @else {\n        $extra: $extra $transform;\n      }\n    }\n  }\n\n  @if $x == '0' or $x == null {\n    @include multi-dir() {\n      transform: $ltr-translate $extra;\n    }\n  } @else {\n    @include ltr() {\n      transform: $ltr-translate $extra;\n    }\n\n    @include rtl() {\n      transform: $rtl-translate $extra;\n    }\n  }\n}\n", "\n// Global Utility Functions\n@import \"./ionic.functions.string\";\n\n// Global Color Functions\n@import \"./ionic.functions.color\";\n\n// Global Font Functions\n@import \"./ionic.functions.font\";\n\n// Global Mixins\n@import \"./ionic.mixins\";\n\n// Default Theme\n@import \"./ionic.theme.default\";\n\n\n// Default General\n// --------------------------------------------------\n$font-family-base:                  var(--ion-font-family, inherit);\n\n// Hairlines width\n$hairlines-width: .55px;\n\n// The minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries\n$screen-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px\n);\n\n// Input placeholder opacity\n// Ensures that the placeholder has the\n// correct color contrast against the background.\n$placeholder-opacity: var(--ion-placeholder-opacity, 0.6);\n\n$form-control-label-margin: 16px;\n\n// How much the stacked labels should be scaled by\n/// The value 0.75 is used to match the MD spec.\n/// iOS does not have a floating label design spec, so we standardize on 0.75.\n$form-control-label-stacked-scale: 0.75;\n\n\n// Z-Index\n// --------------------------------------------------\n// Grouped by elements which would be siblings\n\n$z-index-menu-overlay:           1000;\n$z-index-overlay:                1001;\n\n$z-index-fixed-content:          999;\n$z-index-refresher:              -1;\n\n$z-index-page-container:         0;\n$z-index-toolbar:                10;\n$z-index-toolbar-background:     -1;\n$z-index-toolbar-buttons:        99;\n\n$z-index-backdrop:               2;\n$z-index-overlay-wrapper:        10;\n\n$z-index-item-options:           1;\n$z-index-item-input:             2;\n$z-index-item-divider:           100;\n\n$z-index-reorder-selected:       100;\n", "@import \"../../themes/ionic.globals.ios\";\n\n// iOS Menu\n// --------------------------------------------------\n\n/// @prop - Box shadow color of the menu\n$menu-ios-box-shadow-color:      rgba(0, 0, 0, .08);\n\n/// @prop - Box shadow of the menu\n$menu-ios-box-shadow:            -8px 0 42px $menu-ios-box-shadow-color;\n\n/// @prop - Box shadow of the menu in rtl mode\n$menu-ios-box-shadow-rtl:        8px 0 42px $menu-ios-box-shadow-color;\n\n/// @prop - Box shadow of the reveal menu\n$menu-ios-box-shadow-reveal:     $menu-ios-box-shadow;\n\n/// @prop - Box shadow of the reveal menu\n$menu-ios-box-shadow-reveal-rtl: $menu-ios-box-shadow-rtl;\n\n/// @prop - Box shadow of the push menu\n$menu-ios-box-shadow-push:       null;\n\n/// @prop - Box shadow of the overlay menu\n$menu-ios-box-shadow-overlay:    null;\n", "@import \"../../themes/ionic.globals.md\";\n\n// Material Design Menu\n// --------------------------------------------------\n\n/// @prop - Box shadow of the menu\n$menu-md-box-shadow:            4px 0px 16px rgba(0, 0, 0, 0.18);\n", "// ! normalize.css v3.0.2 | MIT License | github.com/necolas/normalize.css\n\n\n// HTML5 display definitions\n// ==========================================================================\n\n// 1. Normalize vertical alignment of `progress` in Chrome, Firefox, and Opera.\naudio,\ncanvas,\nprogress,\nvideo {\n  vertical-align: baseline; // 1\n}\n\n// Prevent modern browsers from displaying `audio` without controls.\n// Remove excess height in iOS 5 devices.\naudio:not([controls]) {\n  display: none;\n\n  height: 0;\n}\n\n\n// Text-level semantics\n// ==========================================================================\n\n// Address style set to `bolder` in Firefox 4+, Safari, and Chrome.\nb,\nstrong {\n  font-weight: bold;\n}\n\n// Embedded content\n// ==========================================================================\n\n// Makes it so the img does not flow outside container\nimg {\n  max-width: 100%;\n}\n\n// Grouping content\n// ==========================================================================\n\nhr {\n  height: 1px;\n\n  border-width: 0;\n\n  box-sizing: content-box;\n}\n\n// Contain overflow in all browsers.\npre {\n  overflow: auto;\n}\n\n// Address odd `em`-unit font size rendering in all browsers.\ncode,\nkbd,\npre,\nsamp {\n  font-family: monospace, monospace;\n  font-size: 1em;\n}\n\n\n// Forms\n// ==========================================================================\n\n// Known limitation: by default, Chrome and Safari on OS X allow very limited\n// styling of `select`, unless a `border` property is set.\n\n// 1. Correct color not being inherited.\n//    Known issue: affects color of disabled elements.\n// 2. Correct font properties not being inherited.\n// 3. Address margins set differently in Firefox 4+, Safari, and Chrome.\n//\n\nlabel,\ninput,\nselect,\ntextarea {\n  font-family: inherit;\n  line-height: normal;\n}\n\ntextarea {\n  overflow: auto;\n\n  height: auto;\n\n  font: inherit;\n  color: inherit;\n}\n\ntextarea::placeholder {\n  padding-left: 2px;\n}\n\nform,\ninput,\noptgroup,\nselect {\n  margin: 0; // 3\n\n  font: inherit; // 2\n  color: inherit; // 1\n}\n\n// 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`\n//    and `video` controls.\n// 2. Correct inability to style clickable `input` types in iOS.\n// 3. Improve usability and consistency of cursor style between image-type\n//    `input` and others.\nhtml input[type=\"button\"], // 1\ninput[type=\"reset\"],\ninput[type=\"submit\"] {\n  cursor: pointer; // 3\n\n  -webkit-appearance: button; // 2\n}\n\n// remove 300ms delay\na,\na div,\na span,\na ion-icon,\na ion-label,\nbutton,\nbutton div,\nbutton span,\nbutton ion-icon,\nbutton ion-label,\n.ion-tappable,\n[tappable],\n[tappable] div,\n[tappable] span,\n[tappable] ion-icon,\n[tappable] ion-label,\ninput,\ntextarea {\n  touch-action: manipulation;\n}\n\na ion-label,\nbutton ion-label {\n  pointer-events: none;\n}\n\nbutton {\n  padding: 0;\n  border: 0;\n  border-radius: 0;\n  font-family: inherit;\n  font-style: inherit;\n  font-variant: inherit;\n  line-height: 1;\n  text-transform: none;\n  cursor: pointer;\n\n  -webkit-appearance: button;\n}\n\n[tappable] {\n  cursor: pointer;\n}\n\n// Re-set default cursor for disabled elements.\na[disabled],\nbutton[disabled],\nhtml input[disabled] {\n  cursor: default;\n}\n\n// Remove inner padding and border in Firefox 4+.\nbutton::-moz-focus-inner,\ninput::-moz-focus-inner {\n  padding: 0;\n\n  border: 0;\n}\n\n// Fix the cursor style for Chrome's increment/decrement buttons. For certain\n// `font-size` values of the `input`, it causes the cursor style of the\n// decrement button to change from `default` to `text`.\ninput[type=\"number\"]::-webkit-inner-spin-button,\ninput[type=\"number\"]::-webkit-outer-spin-button {\n  height: auto;\n}\n\n// Remove inner padding and search cancel button in Safari and Chrome on OS X.\n// Safari (but not Chrome) clips the cancel button when the search input has\n// padding (and `textfield` appearance).\ninput[type=\"search\"]::-webkit-search-cancel-button,\ninput[type=\"search\"]::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n\n// Tables\n// ==========================================================================//\n\n// Remove most spacing between table cells.\ntable {\n  border-collapse: collapse;\n  border-spacing: 0;\n}\n\ntd,\nth {\n  padding: 0;\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n\n// Structure\n// --------------------------------------------------\n// Adds structural css to the native html elements\n\n* {\n  box-sizing: border-box;\n\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n  -webkit-tap-highlight-color: transparent;\n  -webkit-touch-callout: none;\n}\n\nhtml {\n  width: 100%;\n  height: 100%;\n  -webkit-text-size-adjust: 100%;\n\n  text-size-adjust: 100%;\n}\n\nhtml:not(.hydrated) body {\n  display: none;\n}\n\nhtml.ion-ce body {\n  display: block;\n}\n\nhtml.plt-pwa {\n  height: 100vh;\n}\n\nbody {\n  @include font-smoothing();\n  @include margin(0);\n  @include padding(0);\n\n  position: fixed;\n\n  width: 100%;\n  max-width: 100%;\n  height: 100%;\n  max-height: 100%;\n\n  /**\n   * Because body has position: fixed,\n   * it should be promoted to its own\n   * layer.\n   *\n   * WebKit does not always promote\n   * the body to its own layer on page\n   * load in Ionic apps. Once scrolling on\n   * ion-content starts, WebKit will promote\n   * body. Unfortunately, this causes a re-paint\n   * which results in scrolling being halted\n   * until the next user gesture.\n   *\n   * This impacts the Custom Elements build.\n   * The lazy loaded build causes the browser to\n   * re-paint during hydration which causes WebKit\n   * to promote body to its own layer.\n   * In the CE Build, this hydration does not\n   * happen, so the additional re-paint does not occur.\n   */\n  transform: translateZ(0);\n\n  text-rendering: optimizeLegibility;\n\n  overflow: hidden;\n\n  touch-action: manipulation;\n\n  -webkit-user-drag: none;\n\n  -ms-content-zooming: none;\n\n  word-wrap: break-word;\n\n  overscroll-behavior-y: none;\n  -webkit-text-size-adjust: none;\n\n  text-size-adjust: none;\n}\n", "\n/**\n * A heuristic that applies CSS to tablet\n * viewports.\n *\n * Usage:\n * @include tablet-viewport() {\n *   :host {\n *     background-color: green;\n *   }\n * }\n */\n@mixin tablet-viewport() {\n  @media screen and (min-width: 768px) {\n    @content;\n  }\n}\n\n/**\n * A heuristic that applies CSS to mobile\n * viewports (i.e. phones, not tablets).\n *\n * Usage:\n * @include mobile-viewport() {\n *   :host {\n *     background-color: blue;\n *   }\n * }\n */\n@mixin mobile-viewport() {\n  @media screen and (max-width: 767px) {\n    @content;\n  }\n}\n\n@mixin input-cover() {\n  @include position(0, null, null, 0);\n  @include margin(0);\n\n  position: absolute;\n\n  width: 100%;\n  height: 100%;\n\n  border: 0;\n  background: transparent;\n  cursor: pointer;\n\n  appearance: none;\n  outline: none;\n\n  &::-moz-focus-inner {\n    border: 0;\n  }\n}\n\n@mixin visually-hidden() {\n  position: absolute;\n\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n\n  width: 100%;\n  height: 100%;\n\n  margin: 0;\n  padding: 0;\n\n  border: 0;\n  outline: 0;\n  clip: rect(0 0 0 0);\n\n  opacity: 0;\n  overflow: hidden;\n\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n@mixin text-inherit() {\n  font-family: inherit;\n  font-size: inherit;\n  font-style: inherit;\n  font-weight: inherit;\n  letter-spacing: inherit;\n  text-decoration: inherit;\n  text-indent: inherit;\n  text-overflow: inherit;\n  text-transform: inherit;\n  text-align: inherit;\n  white-space: inherit;\n  color: inherit;\n}\n\n@mixin button-state() {\n  @include position(0, 0, 0, 0);\n\n  position: absolute;\n\n  content: \"\";\n\n  opacity: 0;\n}\n\n// Font smoothing\n// --------------------------------------------------\n\n@mixin font-smoothing() {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n}\n\n// Get the key from a map based on the index\n@function index-to-key($map, $index) {\n  $keys: map-keys($map);\n\n  @return nth($keys, $index);\n}\n\n\n// Breakpoint Mixins\n// ---------------------------------------------------------------------------------\n\n// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$screen-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// ---------------------------------------------------------------------------------\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $screen-breakpoints) {\n  $min: map-get($breakpoints, $name);\n\n  @return if($name != index-to-key($breakpoints, 1), $min, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash infront.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $screen-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $screen-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $screen-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Maximum breakpoint width. Null for the smallest (first) breakpoint.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n//\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\t// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\t// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $screen-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $screen-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n\n// Text Direction - ltr / rtl\n//\n// CSS defaults to use the ltr css, and adds [dir=rtl] selectors\n// to override ltr defaults.\n// ----------------------------------------------------------\n\n@mixin multi-dir() {\n  @content;\n\n  // $root: #{&};\n  // @at-root [dir] {\n  //   #{$root} {\n  //     @content;\n  //   }\n  // }\n}\n\n@mixin rtl() {\n  $root: #{&};\n\n  $rootSplit: str-split($root, \",\");\n  $selectors: #{add-root-selector($root, \"[dir=rtl]\")};\n  $selectorsSplit: str-split($selectors, \",\");\n\n  $hostContextSelectors: ();\n  $restSelectors: ();\n  $dirSelectors: ();\n\n  // Selectors must be split into individual selectors in case the browser\n  // doesn't support a specific selector.\n  // For example, Firefox and Safari doesn't support `:host-context()`.\n  // If an invalid selector is used, then the entire group of selectors\n  // will be ignored.\n  // @link https://www.w3.org/TR/selectors-3/#grouping\n  @each $selector in $selectorsSplit {\n    // Group the selectors back into a single selector to optimize the output.\n    @if str-index($selector, \":host-context\") {\n      $hostContextSelectors: append($hostContextSelectors, $selector, comma);\n    } @else {\n      // Group the selectors back into a single selector to optimize the output.\n      $restSelectors: append($restSelectors, $selector, comma);\n    }\n  }\n\n  // Supported by Chrome.\n  @if length($hostContextSelectors) > 0 {\n    @at-root #{$hostContextSelectors} {\n      @content;\n    }\n  }\n\n  // Supported by all browsers.\n  @if length($restSelectors) > 0 {\n    @at-root #{$restSelectors} {\n      @content;\n    }\n  }\n\n  // If browser can support `:dir()`, then add the `:dir()` selectors.\n  @supports selector(:dir(rtl)) {\n    // Adding :dir() in case the browser doesn't support `:host-context()` and does support `:dir()`.\n    // `:host-context()` is added:\n    // - through the `add-root-selector()` function.\n    // - first so that it takes precedence over `:dir()`.\n    // For example,\n    // - Firefox doesn't support `:host-context()`, but does support `:dir()`.\n    // - Safari doesn't support `:host-context()`, but Safari 16.4+ supports `:dir()`\n    // @link https://webkit.org/blog/13966/webkit-features-in-safari-16-4/\n    // -- However, there is a Webkit bug on v16 that prevents `:dir()` from working when\n    // -- the app direction is changed dynamically. v17+ works fine.\n    // -- @link https://bugs.webkit.org/show_bug.cgi?id=257133\n\n    // Supported by Firefox.\n    @at-root #{add-root-selector($root, \":dir(rtl)\", false)} {\n      @content;\n    }\n  }\n}\n\n@mixin ltr() {\n  @content;\n}\n\n\n// SVG Background Image Mixin\n// @param {string} $svg\n// ----------------------------------------------------------\n@mixin svg-background-image($svg, $flip-rtl: false) {\n  $url: url-encode($svg);\n  $viewBox: str-split(str-extract($svg, \"viewBox='\", \"'\"), \" \");\n\n  @if $flip-rtl != true or $viewBox == null {\n    @include multi-dir() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n  } @else {\n    $transform: \"transform='translate(#{nth($viewBox, 3)}, 0) scale(-1, 1)'\";\n    $flipped-url: $svg;\n    $flipped-url: str-replace($flipped-url, \"<path\", \"<path #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<line\", \"<line #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<polygon\", \"<polygon #{$transform}\");\n    $flipped-url: url-encode($flipped-url);\n\n    @include ltr () {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n    @include rtl() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$flipped-url}\");\n    }\n  }\n}\n\n// Add property horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin property-horizontal($prop, $start, $end: $start) {\n  @if $start == 0 and $end == 0 {\n    #{$prop}-left: $start;\n    #{$prop}-right: $end;\n\n  } @else {\n    -webkit-#{$prop}-start: $start;\n    #{$prop}-inline-start: $start;\n    -webkit-#{$prop}-end: $end;\n    #{$prop}-inline-end: $end;\n  }\n}\n\n// Add property for all directions\n// @param {string} $prop\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// @param {boolean} $content include content or use default\n// ----------------------------------------------------------\n@mixin property($prop, $top, $end: $top, $bottom: $top, $start: $end) {\n  @include property-horizontal($prop, $start, $end);\n  #{$prop}-top: $top;\n  #{$prop}-bottom: $bottom;\n}\n\n// Add padding horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin padding-horizontal($start, $end: $start) {\n  @include property-horizontal(padding, $start, $end);\n}\n\n// Add padding for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin padding($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(padding, $top, $end, $bottom, $start);\n}\n\n// Add margin horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin margin-horizontal($start, $end: $start) {\n  @include property-horizontal(margin, $start, $end);\n}\n\n// Add margin for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin margin($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(margin, $top, $end, $bottom, $start);\n}\n\n// Add position horizontal\n// @param {string} $start - amount to position start\n// @param {string} $end - amount to left: 0; end\n// ----------------------------------------------------------\n@mixin position-horizontal($start: null, $end: null) {\n  @if $start == $end {\n    @include multi-dir() {\n      left: $start;\n      right: $end;\n    }\n  } @else {\n    @at-root {\n      & {\n        inset-inline-start: $start;\n        inset-inline-end: $end;\n      }\n    }\n  }\n}\n\n// Add position for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin position($top: null, $end: null, $bottom: null, $start: null) {\n  @include position-horizontal($start, $end);\n  top: $top;\n  bottom: $bottom;\n}\n\n// Add border for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin border($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(border, $top, $end, $bottom, $start);\n}\n\n// Add border radius for all directions\n// @param {string} $top-start\n// @param {string} $top-end\n// @param {string} $bottom-end\n// @param {string} $bottom-start\n// ----------------------------------------------------------\n@mixin border-radius($top-start, $top-end: $top-start, $bottom-end: $top-start, $bottom-start: $top-end) {\n  @if $top-start == $top-end and $top-start == $bottom-end and $top-start == $bottom-start {\n    border-radius: $top-start;\n  } @else {\n    border-start-start-radius: $top-start;\n    border-start-end-radius: $top-end;\n    border-end-end-radius: $bottom-end;\n    border-end-start-radius: $bottom-start;\n  }\n}\n\n// Add direction for all directions\n// @param {string} $dir - Direction on LTR\n@mixin direction($dir) {\n  $other-dir: null;\n\n  @if $dir == ltr {\n    $other-dir: rtl;\n  } @else {\n    $other-dir: ltr;\n  }\n\n  @include ltr() {\n    direction: $dir;\n  }\n  @include rtl() {\n    direction: $other-dir;\n  }\n}\n\n// Add float for all directions\n// @param {string} $side\n// @param {string} $decorator - !important\n@mixin float($side, $decorator: null) {\n  @if $side == start {\n    @include ltr() {\n      float: left $decorator;\n    }\n    @include rtl() {\n      float: right $decorator;\n    }\n  } @else if $side == end {\n    @include ltr() {\n      float: right $decorator;\n    }\n    @include rtl() {\n      float: left $decorator;\n    }\n  } @else {\n    @include multi-dir() {\n      float: $side $decorator;\n    }\n  }\n}\n\n@mixin background-position($horizontal, $horizontal-amount: null, $vertical: null, $vertical-amount: null) {\n  @if $horizontal == start or $horizontal == end {\n    $horizontal-ltr: null;\n    $horizontal-rtl: null;\n    @if $horizontal == start {\n      $horizontal-ltr: left;\n      $horizontal-rtl: right;\n    } @else {\n      $horizontal-ltr: right;\n      $horizontal-rtl: left;\n    }\n\n    @include ltr() {\n      background-position: $horizontal-ltr $horizontal-amount $vertical $vertical-amount;\n    }\n    @include rtl() {\n      background-position: $horizontal-rtl $horizontal-amount $vertical $vertical-amount;\n    }\n  } @else {\n    @include multi-dir() {\n      background-position: $horizontal $horizontal-amount $vertical $vertical-amount;\n    }\n  }\n}\n\n@mixin transform-origin($x-axis, $y-axis: null) {\n  @if $x-axis == start {\n    @include ltr() {\n      transform-origin: left $y-axis;\n    }\n    @include rtl() {\n      transform-origin: right $y-axis;\n    }\n  } @else if $x-axis == end {\n    @include ltr() {\n      transform-origin: right $y-axis;\n    }\n    @include rtl() {\n      transform-origin: left $y-axis;\n    }\n  } @else if $x-axis == left or $x-axis == right {\n    @include multi-dir() {\n      transform-origin: $x-axis $y-axis;\n    }\n  } @else {\n    @include ltr() {\n      transform-origin: $x-axis $y-axis;\n    }\n    @include rtl() {\n      transform-origin: calc(100% - #{$x-axis}) $y-axis;\n    }\n  }\n}\n\n// Add transform for all directions\n// @param {string} $transforms - comma separated list of transforms\n@mixin transform($transforms...) {\n  $extra: null;\n\n  $x: null;\n  $ltr-translate: null;\n  $rtl-translate: null;\n\n  @each $transform in $transforms {\n    @if (str-index($transform, translate3d)) {\n      $transform: str-replace($transform, 'translate3d(');\n      $transform: str-replace($transform, ')');\n\n      $coordinates: str-split($transform, ',');\n\n      $x: nth($coordinates, 1);\n      $y: nth($coordinates, 2);\n      $z: nth($coordinates, 3);\n\n      $ltr-translate: translate3d($x, $y, $z);\n      $rtl-translate: translate3d(calc(-1 * #{$x}), $y, $z);\n    } @else {\n      @if $extra == null {\n        $extra: $transform;\n      } @else {\n        $extra: $extra $transform;\n      }\n    }\n  }\n\n  @if $x == '0' or $x == null {\n    @include multi-dir() {\n      transform: $ltr-translate $extra;\n    }\n  } @else {\n    @include ltr() {\n      transform: $ltr-translate $extra;\n    }\n\n    @include rtl() {\n      transform: $rtl-translate $extra;\n    }\n  }\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n// Typography\n// --------------------------------------------------\n\n/// @prop - Font weight of all headings\n$headings-font-weight:         500;\n\n/// @prop - Line height of all headings\n$headings-line-height:         1.2;\n\n/// @prop - Font size of heading level 1\n$h1-font-size:                 dynamic-font(26px);\n\n/// @prop - Font size of heading level 2\n$h2-font-size:                 dynamic-font(24px);\n\n/// @prop - Font size of heading level 3\n$h3-font-size:                 dynamic-font(22px);\n\n/// @prop - Font size of heading level 4\n$h4-font-size:                 dynamic-font(20px);\n\n/// @prop - Font size of heading level 5\n$h5-font-size:                 dynamic-font(18px);\n\n/// @prop - Font size of heading level 6\n$h6-font-size:                 dynamic-font(16px);\n\nhtml {\n  font-family: var(--ion-font-family);\n}\n\n/**\n * Dynamic Type is an iOS-only feature, so\n * this should only be enabled on iOS devices.\n */\n@supports (-webkit-touch-callout: none) {\n  html {\n    /**\n     * Includes fallback if Dynamic Type is not enabled.\n     */\n    font: var(--ion-dynamic-font, 16px var(--ion-font-family));\n  }\n}\n\na {\n  background-color: transparent;\n  color: ion-color(primary, base);\n}\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  @include margin(16px, null, 10px, null);\n\n  font-weight: $headings-font-weight;\n\n  line-height: $headings-line-height;\n}\n\nh1 {\n  @include margin(20px, null, null, null);\n\n  font-size: $h1-font-size;\n}\n\nh2 {\n  @include margin(18px, null, null, null);\n\n  font-size: $h2-font-size;\n}\n\nh3 {\n  font-size: $h3-font-size;\n}\n\nh4 {\n  font-size: $h4-font-size;\n}\n\nh5 {\n  font-size: $h5-font-size;\n}\n\nh6 {\n  font-size: $h6-font-size;\n}\n\nsmall {\n  font-size: 75%;\n}\n\nsub,\nsup {\n  position: relative;\n\n  font-size: 75%;\n\n  line-height: 0;\n\n  vertical-align: baseline;\n}\n\nsup {\n  top: -.5em;\n}\n\nsub {\n  bottom: -.25em;\n}\n", "\n/**\n * A heuristic that applies CSS to tablet\n * viewports.\n *\n * Usage:\n * @include tablet-viewport() {\n *   :host {\n *     background-color: green;\n *   }\n * }\n */\n@mixin tablet-viewport() {\n  @media screen and (min-width: 768px) {\n    @content;\n  }\n}\n\n/**\n * A heuristic that applies CSS to mobile\n * viewports (i.e. phones, not tablets).\n *\n * Usage:\n * @include mobile-viewport() {\n *   :host {\n *     background-color: blue;\n *   }\n * }\n */\n@mixin mobile-viewport() {\n  @media screen and (max-width: 767px) {\n    @content;\n  }\n}\n\n@mixin input-cover() {\n  @include position(0, null, null, 0);\n  @include margin(0);\n\n  position: absolute;\n\n  width: 100%;\n  height: 100%;\n\n  border: 0;\n  background: transparent;\n  cursor: pointer;\n\n  appearance: none;\n  outline: none;\n\n  &::-moz-focus-inner {\n    border: 0;\n  }\n}\n\n@mixin visually-hidden() {\n  position: absolute;\n\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n\n  width: 100%;\n  height: 100%;\n\n  margin: 0;\n  padding: 0;\n\n  border: 0;\n  outline: 0;\n  clip: rect(0 0 0 0);\n\n  opacity: 0;\n  overflow: hidden;\n\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n@mixin text-inherit() {\n  font-family: inherit;\n  font-size: inherit;\n  font-style: inherit;\n  font-weight: inherit;\n  letter-spacing: inherit;\n  text-decoration: inherit;\n  text-indent: inherit;\n  text-overflow: inherit;\n  text-transform: inherit;\n  text-align: inherit;\n  white-space: inherit;\n  color: inherit;\n}\n\n@mixin button-state() {\n  @include position(0, 0, 0, 0);\n\n  position: absolute;\n\n  content: \"\";\n\n  opacity: 0;\n}\n\n// Font smoothing\n// --------------------------------------------------\n\n@mixin font-smoothing() {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n}\n\n// Get the key from a map based on the index\n@function index-to-key($map, $index) {\n  $keys: map-keys($map);\n\n  @return nth($keys, $index);\n}\n\n\n// Breakpoint Mixins\n// ---------------------------------------------------------------------------------\n\n// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$screen-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// ---------------------------------------------------------------------------------\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $screen-breakpoints) {\n  $min: map-get($breakpoints, $name);\n\n  @return if($name != index-to-key($breakpoints, 1), $min, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash infront.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $screen-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $screen-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $screen-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Maximum breakpoint width. Null for the smallest (first) breakpoint.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n//\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\t// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\t// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $screen-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $screen-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n\n// Text Direction - ltr / rtl\n//\n// CSS defaults to use the ltr css, and adds [dir=rtl] selectors\n// to override ltr defaults.\n// ----------------------------------------------------------\n\n@mixin multi-dir() {\n  @content;\n\n  // $root: #{&};\n  // @at-root [dir] {\n  //   #{$root} {\n  //     @content;\n  //   }\n  // }\n}\n\n@mixin rtl() {\n  $root: #{&};\n\n  $rootSplit: str-split($root, \",\");\n  $selectors: #{add-root-selector($root, \"[dir=rtl]\")};\n  $selectorsSplit: str-split($selectors, \",\");\n\n  $hostContextSelectors: ();\n  $restSelectors: ();\n  $dirSelectors: ();\n\n  // Selectors must be split into individual selectors in case the browser\n  // doesn't support a specific selector.\n  // For example, Firefox and Safari doesn't support `:host-context()`.\n  // If an invalid selector is used, then the entire group of selectors\n  // will be ignored.\n  // @link https://www.w3.org/TR/selectors-3/#grouping\n  @each $selector in $selectorsSplit {\n    // Group the selectors back into a single selector to optimize the output.\n    @if str-index($selector, \":host-context\") {\n      $hostContextSelectors: append($hostContextSelectors, $selector, comma);\n    } @else {\n      // Group the selectors back into a single selector to optimize the output.\n      $restSelectors: append($restSelectors, $selector, comma);\n    }\n  }\n\n  // Supported by Chrome.\n  @if length($hostContextSelectors) > 0 {\n    @at-root #{$hostContextSelectors} {\n      @content;\n    }\n  }\n\n  // Supported by all browsers.\n  @if length($restSelectors) > 0 {\n    @at-root #{$restSelectors} {\n      @content;\n    }\n  }\n\n  // If browser can support `:dir()`, then add the `:dir()` selectors.\n  @supports selector(:dir(rtl)) {\n    // Adding :dir() in case the browser doesn't support `:host-context()` and does support `:dir()`.\n    // `:host-context()` is added:\n    // - through the `add-root-selector()` function.\n    // - first so that it takes precedence over `:dir()`.\n    // For example,\n    // - Firefox doesn't support `:host-context()`, but does support `:dir()`.\n    // - Safari doesn't support `:host-context()`, but Safari 16.4+ supports `:dir()`\n    // @link https://webkit.org/blog/13966/webkit-features-in-safari-16-4/\n    // -- However, there is a Webkit bug on v16 that prevents `:dir()` from working when\n    // -- the app direction is changed dynamically. v17+ works fine.\n    // -- @link https://bugs.webkit.org/show_bug.cgi?id=257133\n\n    // Supported by Firefox.\n    @at-root #{add-root-selector($root, \":dir(rtl)\", false)} {\n      @content;\n    }\n  }\n}\n\n@mixin ltr() {\n  @content;\n}\n\n\n// SVG Background Image Mixin\n// @param {string} $svg\n// ----------------------------------------------------------\n@mixin svg-background-image($svg, $flip-rtl: false) {\n  $url: url-encode($svg);\n  $viewBox: str-split(str-extract($svg, \"viewBox='\", \"'\"), \" \");\n\n  @if $flip-rtl != true or $viewBox == null {\n    @include multi-dir() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n  } @else {\n    $transform: \"transform='translate(#{nth($viewBox, 3)}, 0) scale(-1, 1)'\";\n    $flipped-url: $svg;\n    $flipped-url: str-replace($flipped-url, \"<path\", \"<path #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<line\", \"<line #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<polygon\", \"<polygon #{$transform}\");\n    $flipped-url: url-encode($flipped-url);\n\n    @include ltr () {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n    @include rtl() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$flipped-url}\");\n    }\n  }\n}\n\n// Add property horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin property-horizontal($prop, $start, $end: $start) {\n  @if $start == 0 and $end == 0 {\n    #{$prop}-left: $start;\n    #{$prop}-right: $end;\n\n  } @else {\n    -webkit-#{$prop}-start: $start;\n    #{$prop}-inline-start: $start;\n    -webkit-#{$prop}-end: $end;\n    #{$prop}-inline-end: $end;\n  }\n}\n\n// Add property for all directions\n// @param {string} $prop\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// @param {boolean} $content include content or use default\n// ----------------------------------------------------------\n@mixin property($prop, $top, $end: $top, $bottom: $top, $start: $end) {\n  @include property-horizontal($prop, $start, $end);\n  #{$prop}-top: $top;\n  #{$prop}-bottom: $bottom;\n}\n\n// Add padding horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin padding-horizontal($start, $end: $start) {\n  @include property-horizontal(padding, $start, $end);\n}\n\n// Add padding for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin padding($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(padding, $top, $end, $bottom, $start);\n}\n\n// Add margin horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin margin-horizontal($start, $end: $start) {\n  @include property-horizontal(margin, $start, $end);\n}\n\n// Add margin for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin margin($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(margin, $top, $end, $bottom, $start);\n}\n\n// Add position horizontal\n// @param {string} $start - amount to position start\n// @param {string} $end - amount to left: 0; end\n// ----------------------------------------------------------\n@mixin position-horizontal($start: null, $end: null) {\n  @if $start == $end {\n    @include multi-dir() {\n      left: $start;\n      right: $end;\n    }\n  } @else {\n    @at-root {\n      & {\n        inset-inline-start: $start;\n        inset-inline-end: $end;\n      }\n    }\n  }\n}\n\n// Add position for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin position($top: null, $end: null, $bottom: null, $start: null) {\n  @include position-horizontal($start, $end);\n  top: $top;\n  bottom: $bottom;\n}\n\n// Add border for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin border($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(border, $top, $end, $bottom, $start);\n}\n\n// Add border radius for all directions\n// @param {string} $top-start\n// @param {string} $top-end\n// @param {string} $bottom-end\n// @param {string} $bottom-start\n// ----------------------------------------------------------\n@mixin border-radius($top-start, $top-end: $top-start, $bottom-end: $top-start, $bottom-start: $top-end) {\n  @if $top-start == $top-end and $top-start == $bottom-end and $top-start == $bottom-start {\n    border-radius: $top-start;\n  } @else {\n    border-start-start-radius: $top-start;\n    border-start-end-radius: $top-end;\n    border-end-end-radius: $bottom-end;\n    border-end-start-radius: $bottom-start;\n  }\n}\n\n// Add direction for all directions\n// @param {string} $dir - Direction on LTR\n@mixin direction($dir) {\n  $other-dir: null;\n\n  @if $dir == ltr {\n    $other-dir: rtl;\n  } @else {\n    $other-dir: ltr;\n  }\n\n  @include ltr() {\n    direction: $dir;\n  }\n  @include rtl() {\n    direction: $other-dir;\n  }\n}\n\n// Add float for all directions\n// @param {string} $side\n// @param {string} $decorator - !important\n@mixin float($side, $decorator: null) {\n  @if $side == start {\n    @include ltr() {\n      float: left $decorator;\n    }\n    @include rtl() {\n      float: right $decorator;\n    }\n  } @else if $side == end {\n    @include ltr() {\n      float: right $decorator;\n    }\n    @include rtl() {\n      float: left $decorator;\n    }\n  } @else {\n    @include multi-dir() {\n      float: $side $decorator;\n    }\n  }\n}\n\n@mixin background-position($horizontal, $horizontal-amount: null, $vertical: null, $vertical-amount: null) {\n  @if $horizontal == start or $horizontal == end {\n    $horizontal-ltr: null;\n    $horizontal-rtl: null;\n    @if $horizontal == start {\n      $horizontal-ltr: left;\n      $horizontal-rtl: right;\n    } @else {\n      $horizontal-ltr: right;\n      $horizontal-rtl: left;\n    }\n\n    @include ltr() {\n      background-position: $horizontal-ltr $horizontal-amount $vertical $vertical-amount;\n    }\n    @include rtl() {\n      background-position: $horizontal-rtl $horizontal-amount $vertical $vertical-amount;\n    }\n  } @else {\n    @include multi-dir() {\n      background-position: $horizontal $horizontal-amount $vertical $vertical-amount;\n    }\n  }\n}\n\n@mixin transform-origin($x-axis, $y-axis: null) {\n  @if $x-axis == start {\n    @include ltr() {\n      transform-origin: left $y-axis;\n    }\n    @include rtl() {\n      transform-origin: right $y-axis;\n    }\n  } @else if $x-axis == end {\n    @include ltr() {\n      transform-origin: right $y-axis;\n    }\n    @include rtl() {\n      transform-origin: left $y-axis;\n    }\n  } @else if $x-axis == left or $x-axis == right {\n    @include multi-dir() {\n      transform-origin: $x-axis $y-axis;\n    }\n  } @else {\n    @include ltr() {\n      transform-origin: $x-axis $y-axis;\n    }\n    @include rtl() {\n      transform-origin: calc(100% - #{$x-axis}) $y-axis;\n    }\n  }\n}\n\n// Add transform for all directions\n// @param {string} $transforms - comma separated list of transforms\n@mixin transform($transforms...) {\n  $extra: null;\n\n  $x: null;\n  $ltr-translate: null;\n  $rtl-translate: null;\n\n  @each $transform in $transforms {\n    @if (str-index($transform, translate3d)) {\n      $transform: str-replace($transform, 'translate3d(');\n      $transform: str-replace($transform, ')');\n\n      $coordinates: str-split($transform, ',');\n\n      $x: nth($coordinates, 1);\n      $y: nth($coordinates, 2);\n      $z: nth($coordinates, 3);\n\n      $ltr-translate: translate3d($x, $y, $z);\n      $rtl-translate: translate3d(calc(-1 * #{$x}), $y, $z);\n    } @else {\n      @if $extra == null {\n        $extra: $transform;\n      } @else {\n        $extra: $extra $transform;\n      }\n    }\n  }\n\n  @if $x == '0' or $x == null {\n    @include multi-dir() {\n      transform: $ltr-translate $extra;\n    }\n  } @else {\n    @include ltr() {\n      transform: $ltr-translate $extra;\n    }\n\n    @include rtl() {\n      transform: $rtl-translate $extra;\n    }\n  }\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n// Display\n// --------------------------------------------------\n// Modifies display of a particular element based on the given classes\n\n.ion-hide {\n  display: none !important;\n}\n\n// Adds hidden classes\n@each $breakpoint in map-keys($screen-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $screen-breakpoints);\n\n  @include media-breakpoint-up($breakpoint, $screen-breakpoints) {\n    // Provide `ion-hide-{bp}-up` classes for hiding the element based\n    // on the breakpoint\n    .ion-hide#{$infix}-up {\n      display: none !important;\n    }\n  }\n\n  @include media-breakpoint-down($breakpoint, $screen-breakpoints) {\n    // Provide `ion-hide-{bp}-down` classes for hiding the element based\n    // on the breakpoint\n    .ion-hide#{$infix}-down {\n      display: none !important;\n    }\n  }\n}\n", "\n/**\n * A heuristic that applies CSS to tablet\n * viewports.\n *\n * Usage:\n * @include tablet-viewport() {\n *   :host {\n *     background-color: green;\n *   }\n * }\n */\n@mixin tablet-viewport() {\n  @media screen and (min-width: 768px) {\n    @content;\n  }\n}\n\n/**\n * A heuristic that applies CSS to mobile\n * viewports (i.e. phones, not tablets).\n *\n * Usage:\n * @include mobile-viewport() {\n *   :host {\n *     background-color: blue;\n *   }\n * }\n */\n@mixin mobile-viewport() {\n  @media screen and (max-width: 767px) {\n    @content;\n  }\n}\n\n@mixin input-cover() {\n  @include position(0, null, null, 0);\n  @include margin(0);\n\n  position: absolute;\n\n  width: 100%;\n  height: 100%;\n\n  border: 0;\n  background: transparent;\n  cursor: pointer;\n\n  appearance: none;\n  outline: none;\n\n  &::-moz-focus-inner {\n    border: 0;\n  }\n}\n\n@mixin visually-hidden() {\n  position: absolute;\n\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n\n  width: 100%;\n  height: 100%;\n\n  margin: 0;\n  padding: 0;\n\n  border: 0;\n  outline: 0;\n  clip: rect(0 0 0 0);\n\n  opacity: 0;\n  overflow: hidden;\n\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n@mixin text-inherit() {\n  font-family: inherit;\n  font-size: inherit;\n  font-style: inherit;\n  font-weight: inherit;\n  letter-spacing: inherit;\n  text-decoration: inherit;\n  text-indent: inherit;\n  text-overflow: inherit;\n  text-transform: inherit;\n  text-align: inherit;\n  white-space: inherit;\n  color: inherit;\n}\n\n@mixin button-state() {\n  @include position(0, 0, 0, 0);\n\n  position: absolute;\n\n  content: \"\";\n\n  opacity: 0;\n}\n\n// Font smoothing\n// --------------------------------------------------\n\n@mixin font-smoothing() {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n}\n\n// Get the key from a map based on the index\n@function index-to-key($map, $index) {\n  $keys: map-keys($map);\n\n  @return nth($keys, $index);\n}\n\n\n// Breakpoint Mixins\n// ---------------------------------------------------------------------------------\n\n// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$screen-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// ---------------------------------------------------------------------------------\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $screen-breakpoints) {\n  $min: map-get($breakpoints, $name);\n\n  @return if($name != index-to-key($breakpoints, 1), $min, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash infront.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $screen-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $screen-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $screen-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Maximum breakpoint width. Null for the smallest (first) breakpoint.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n//\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\t// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\t// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $screen-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $screen-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n\n// Text Direction - ltr / rtl\n//\n// CSS defaults to use the ltr css, and adds [dir=rtl] selectors\n// to override ltr defaults.\n// ----------------------------------------------------------\n\n@mixin multi-dir() {\n  @content;\n\n  // $root: #{&};\n  // @at-root [dir] {\n  //   #{$root} {\n  //     @content;\n  //   }\n  // }\n}\n\n@mixin rtl() {\n  $root: #{&};\n\n  $rootSplit: str-split($root, \",\");\n  $selectors: #{add-root-selector($root, \"[dir=rtl]\")};\n  $selectorsSplit: str-split($selectors, \",\");\n\n  $hostContextSelectors: ();\n  $restSelectors: ();\n  $dirSelectors: ();\n\n  // Selectors must be split into individual selectors in case the browser\n  // doesn't support a specific selector.\n  // For example, Firefox and Safari doesn't support `:host-context()`.\n  // If an invalid selector is used, then the entire group of selectors\n  // will be ignored.\n  // @link https://www.w3.org/TR/selectors-3/#grouping\n  @each $selector in $selectorsSplit {\n    // Group the selectors back into a single selector to optimize the output.\n    @if str-index($selector, \":host-context\") {\n      $hostContextSelectors: append($hostContextSelectors, $selector, comma);\n    } @else {\n      // Group the selectors back into a single selector to optimize the output.\n      $restSelectors: append($restSelectors, $selector, comma);\n    }\n  }\n\n  // Supported by Chrome.\n  @if length($hostContextSelectors) > 0 {\n    @at-root #{$hostContextSelectors} {\n      @content;\n    }\n  }\n\n  // Supported by all browsers.\n  @if length($restSelectors) > 0 {\n    @at-root #{$restSelectors} {\n      @content;\n    }\n  }\n\n  // If browser can support `:dir()`, then add the `:dir()` selectors.\n  @supports selector(:dir(rtl)) {\n    // Adding :dir() in case the browser doesn't support `:host-context()` and does support `:dir()`.\n    // `:host-context()` is added:\n    // - through the `add-root-selector()` function.\n    // - first so that it takes precedence over `:dir()`.\n    // For example,\n    // - Firefox doesn't support `:host-context()`, but does support `:dir()`.\n    // - Safari doesn't support `:host-context()`, but Safari 16.4+ supports `:dir()`\n    // @link https://webkit.org/blog/13966/webkit-features-in-safari-16-4/\n    // -- However, there is a Webkit bug on v16 that prevents `:dir()` from working when\n    // -- the app direction is changed dynamically. v17+ works fine.\n    // -- @link https://bugs.webkit.org/show_bug.cgi?id=257133\n\n    // Supported by Firefox.\n    @at-root #{add-root-selector($root, \":dir(rtl)\", false)} {\n      @content;\n    }\n  }\n}\n\n@mixin ltr() {\n  @content;\n}\n\n\n// SVG Background Image Mixin\n// @param {string} $svg\n// ----------------------------------------------------------\n@mixin svg-background-image($svg, $flip-rtl: false) {\n  $url: url-encode($svg);\n  $viewBox: str-split(str-extract($svg, \"viewBox='\", \"'\"), \" \");\n\n  @if $flip-rtl != true or $viewBox == null {\n    @include multi-dir() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n  } @else {\n    $transform: \"transform='translate(#{nth($viewBox, 3)}, 0) scale(-1, 1)'\";\n    $flipped-url: $svg;\n    $flipped-url: str-replace($flipped-url, \"<path\", \"<path #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<line\", \"<line #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<polygon\", \"<polygon #{$transform}\");\n    $flipped-url: url-encode($flipped-url);\n\n    @include ltr () {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n    @include rtl() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$flipped-url}\");\n    }\n  }\n}\n\n// Add property horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin property-horizontal($prop, $start, $end: $start) {\n  @if $start == 0 and $end == 0 {\n    #{$prop}-left: $start;\n    #{$prop}-right: $end;\n\n  } @else {\n    -webkit-#{$prop}-start: $start;\n    #{$prop}-inline-start: $start;\n    -webkit-#{$prop}-end: $end;\n    #{$prop}-inline-end: $end;\n  }\n}\n\n// Add property for all directions\n// @param {string} $prop\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// @param {boolean} $content include content or use default\n// ----------------------------------------------------------\n@mixin property($prop, $top, $end: $top, $bottom: $top, $start: $end) {\n  @include property-horizontal($prop, $start, $end);\n  #{$prop}-top: $top;\n  #{$prop}-bottom: $bottom;\n}\n\n// Add padding horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin padding-horizontal($start, $end: $start) {\n  @include property-horizontal(padding, $start, $end);\n}\n\n// Add padding for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin padding($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(padding, $top, $end, $bottom, $start);\n}\n\n// Add margin horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin margin-horizontal($start, $end: $start) {\n  @include property-horizontal(margin, $start, $end);\n}\n\n// Add margin for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin margin($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(margin, $top, $end, $bottom, $start);\n}\n\n// Add position horizontal\n// @param {string} $start - amount to position start\n// @param {string} $end - amount to left: 0; end\n// ----------------------------------------------------------\n@mixin position-horizontal($start: null, $end: null) {\n  @if $start == $end {\n    @include multi-dir() {\n      left: $start;\n      right: $end;\n    }\n  } @else {\n    @at-root {\n      & {\n        inset-inline-start: $start;\n        inset-inline-end: $end;\n      }\n    }\n  }\n}\n\n// Add position for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin position($top: null, $end: null, $bottom: null, $start: null) {\n  @include position-horizontal($start, $end);\n  top: $top;\n  bottom: $bottom;\n}\n\n// Add border for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin border($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(border, $top, $end, $bottom, $start);\n}\n\n// Add border radius for all directions\n// @param {string} $top-start\n// @param {string} $top-end\n// @param {string} $bottom-end\n// @param {string} $bottom-start\n// ----------------------------------------------------------\n@mixin border-radius($top-start, $top-end: $top-start, $bottom-end: $top-start, $bottom-start: $top-end) {\n  @if $top-start == $top-end and $top-start == $bottom-end and $top-start == $bottom-start {\n    border-radius: $top-start;\n  } @else {\n    border-start-start-radius: $top-start;\n    border-start-end-radius: $top-end;\n    border-end-end-radius: $bottom-end;\n    border-end-start-radius: $bottom-start;\n  }\n}\n\n// Add direction for all directions\n// @param {string} $dir - Direction on LTR\n@mixin direction($dir) {\n  $other-dir: null;\n\n  @if $dir == ltr {\n    $other-dir: rtl;\n  } @else {\n    $other-dir: ltr;\n  }\n\n  @include ltr() {\n    direction: $dir;\n  }\n  @include rtl() {\n    direction: $other-dir;\n  }\n}\n\n// Add float for all directions\n// @param {string} $side\n// @param {string} $decorator - !important\n@mixin float($side, $decorator: null) {\n  @if $side == start {\n    @include ltr() {\n      float: left $decorator;\n    }\n    @include rtl() {\n      float: right $decorator;\n    }\n  } @else if $side == end {\n    @include ltr() {\n      float: right $decorator;\n    }\n    @include rtl() {\n      float: left $decorator;\n    }\n  } @else {\n    @include multi-dir() {\n      float: $side $decorator;\n    }\n  }\n}\n\n@mixin background-position($horizontal, $horizontal-amount: null, $vertical: null, $vertical-amount: null) {\n  @if $horizontal == start or $horizontal == end {\n    $horizontal-ltr: null;\n    $horizontal-rtl: null;\n    @if $horizontal == start {\n      $horizontal-ltr: left;\n      $horizontal-rtl: right;\n    } @else {\n      $horizontal-ltr: right;\n      $horizontal-rtl: left;\n    }\n\n    @include ltr() {\n      background-position: $horizontal-ltr $horizontal-amount $vertical $vertical-amount;\n    }\n    @include rtl() {\n      background-position: $horizontal-rtl $horizontal-amount $vertical $vertical-amount;\n    }\n  } @else {\n    @include multi-dir() {\n      background-position: $horizontal $horizontal-amount $vertical $vertical-amount;\n    }\n  }\n}\n\n@mixin transform-origin($x-axis, $y-axis: null) {\n  @if $x-axis == start {\n    @include ltr() {\n      transform-origin: left $y-axis;\n    }\n    @include rtl() {\n      transform-origin: right $y-axis;\n    }\n  } @else if $x-axis == end {\n    @include ltr() {\n      transform-origin: right $y-axis;\n    }\n    @include rtl() {\n      transform-origin: left $y-axis;\n    }\n  } @else if $x-axis == left or $x-axis == right {\n    @include multi-dir() {\n      transform-origin: $x-axis $y-axis;\n    }\n  } @else {\n    @include ltr() {\n      transform-origin: $x-axis $y-axis;\n    }\n    @include rtl() {\n      transform-origin: calc(100% - #{$x-axis}) $y-axis;\n    }\n  }\n}\n\n// Add transform for all directions\n// @param {string} $transforms - comma separated list of transforms\n@mixin transform($transforms...) {\n  $extra: null;\n\n  $x: null;\n  $ltr-translate: null;\n  $rtl-translate: null;\n\n  @each $transform in $transforms {\n    @if (str-index($transform, translate3d)) {\n      $transform: str-replace($transform, 'translate3d(');\n      $transform: str-replace($transform, ')');\n\n      $coordinates: str-split($transform, ',');\n\n      $x: nth($coordinates, 1);\n      $y: nth($coordinates, 2);\n      $z: nth($coordinates, 3);\n\n      $ltr-translate: translate3d($x, $y, $z);\n      $rtl-translate: translate3d(calc(-1 * #{$x}), $y, $z);\n    } @else {\n      @if $extra == null {\n        $extra: $transform;\n      } @else {\n        $extra: $extra $transform;\n      }\n    }\n  }\n\n  @if $x == '0' or $x == null {\n    @include multi-dir() {\n      transform: $ltr-translate $extra;\n    }\n  } @else {\n    @include ltr() {\n      transform: $ltr-translate $extra;\n    }\n\n    @include rtl() {\n      transform: $rtl-translate $extra;\n    }\n  }\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n\n// Element Space\n// --------------------------------------------------\n// Creates padding and margin attributes to be used on\n// any element\n\n$padding: var(--ion-padding, 16px);\n$margin: var(--ion-margin, 16px);\n\n// Padding\n// --------------------------------------------------\n\n.ion-no-padding {\n  --padding-start: 0;\n  --padding-end: 0;\n  --padding-top: 0;\n  --padding-bottom: 0;\n\n  @include padding(0);\n}\n\n.ion-padding {\n  --padding-start: #{$padding};\n  --padding-end: #{$padding};\n  --padding-top: #{$padding};\n  --padding-bottom: #{$padding};\n\n  @include padding($padding);\n}\n\n.ion-padding-top {\n  --padding-top: #{$padding};\n\n  @include padding($padding, null, null, null);\n}\n\n.ion-padding-start {\n  --padding-start: #{$padding};\n\n  @include padding-horizontal($padding, null);\n}\n\n.ion-padding-end {\n  --padding-end: #{$padding};\n\n  @include padding-horizontal(null, $padding);\n}\n\n.ion-padding-bottom {\n  --padding-bottom: #{$padding};\n\n  @include padding(null, null, $padding, null);\n}\n\n.ion-padding-vertical {\n  --padding-top: #{$padding};\n  --padding-bottom: #{$padding};\n\n  @include padding($padding, null, $padding, null);\n}\n\n.ion-padding-horizontal {\n  --padding-start: #{$padding};\n  --padding-end: #{$padding};\n\n  @include padding-horizontal($padding);\n}\n\n\n// Margin\n// --------------------------------------------------\n\n.ion-no-margin {\n  --margin-start: 0;\n  --margin-end: 0;\n  --margin-top: 0;\n  --margin-bottom: 0;\n\n  @include margin(0);\n}\n\n.ion-margin {\n  --margin-start: #{$margin};\n  --margin-end: #{$margin};\n  --margin-top: #{$margin};\n  --margin-bottom: #{$margin};\n\n  @include margin($margin);\n}\n\n.ion-margin-top {\n  --margin-top: #{$margin};\n\n  @include margin($margin, null, null, null);\n}\n\n.ion-margin-start {\n  --margin-start: #{$margin};\n\n  @include margin-horizontal($margin, null);\n}\n\n.ion-margin-end {\n  --margin-end: #{$margin};\n\n  @include margin-horizontal(null, $margin);\n}\n\n.ion-margin-bottom {\n  --margin-bottom: #{$margin};\n\n  @include margin(null, null, $margin, null);\n}\n\n.ion-margin-vertical {\n  --margin-top: #{$margin};\n  --margin-bottom: #{$margin};\n\n  @include margin($margin, null, $margin, null);\n}\n\n.ion-margin-horizontal {\n  --margin-start: #{$margin};\n  --margin-end: #{$margin};\n\n  @include margin-horizontal($margin);\n}\n", "\n/**\n * A heuristic that applies CSS to tablet\n * viewports.\n *\n * Usage:\n * @include tablet-viewport() {\n *   :host {\n *     background-color: green;\n *   }\n * }\n */\n@mixin tablet-viewport() {\n  @media screen and (min-width: 768px) {\n    @content;\n  }\n}\n\n/**\n * A heuristic that applies CSS to mobile\n * viewports (i.e. phones, not tablets).\n *\n * Usage:\n * @include mobile-viewport() {\n *   :host {\n *     background-color: blue;\n *   }\n * }\n */\n@mixin mobile-viewport() {\n  @media screen and (max-width: 767px) {\n    @content;\n  }\n}\n\n@mixin input-cover() {\n  @include position(0, null, null, 0);\n  @include margin(0);\n\n  position: absolute;\n\n  width: 100%;\n  height: 100%;\n\n  border: 0;\n  background: transparent;\n  cursor: pointer;\n\n  appearance: none;\n  outline: none;\n\n  &::-moz-focus-inner {\n    border: 0;\n  }\n}\n\n@mixin visually-hidden() {\n  position: absolute;\n\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n\n  width: 100%;\n  height: 100%;\n\n  margin: 0;\n  padding: 0;\n\n  border: 0;\n  outline: 0;\n  clip: rect(0 0 0 0);\n\n  opacity: 0;\n  overflow: hidden;\n\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n@mixin text-inherit() {\n  font-family: inherit;\n  font-size: inherit;\n  font-style: inherit;\n  font-weight: inherit;\n  letter-spacing: inherit;\n  text-decoration: inherit;\n  text-indent: inherit;\n  text-overflow: inherit;\n  text-transform: inherit;\n  text-align: inherit;\n  white-space: inherit;\n  color: inherit;\n}\n\n@mixin button-state() {\n  @include position(0, 0, 0, 0);\n\n  position: absolute;\n\n  content: \"\";\n\n  opacity: 0;\n}\n\n// Font smoothing\n// --------------------------------------------------\n\n@mixin font-smoothing() {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n}\n\n// Get the key from a map based on the index\n@function index-to-key($map, $index) {\n  $keys: map-keys($map);\n\n  @return nth($keys, $index);\n}\n\n\n// Breakpoint Mixins\n// ---------------------------------------------------------------------------------\n\n// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$screen-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// ---------------------------------------------------------------------------------\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $screen-breakpoints) {\n  $min: map-get($breakpoints, $name);\n\n  @return if($name != index-to-key($breakpoints, 1), $min, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash infront.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $screen-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $screen-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $screen-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Maximum breakpoint width. Null for the smallest (first) breakpoint.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n//\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\t// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\t// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $screen-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $screen-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n\n// Text Direction - ltr / rtl\n//\n// CSS defaults to use the ltr css, and adds [dir=rtl] selectors\n// to override ltr defaults.\n// ----------------------------------------------------------\n\n@mixin multi-dir() {\n  @content;\n\n  // $root: #{&};\n  // @at-root [dir] {\n  //   #{$root} {\n  //     @content;\n  //   }\n  // }\n}\n\n@mixin rtl() {\n  $root: #{&};\n\n  $rootSplit: str-split($root, \",\");\n  $selectors: #{add-root-selector($root, \"[dir=rtl]\")};\n  $selectorsSplit: str-split($selectors, \",\");\n\n  $hostContextSelectors: ();\n  $restSelectors: ();\n  $dirSelectors: ();\n\n  // Selectors must be split into individual selectors in case the browser\n  // doesn't support a specific selector.\n  // For example, Firefox and Safari doesn't support `:host-context()`.\n  // If an invalid selector is used, then the entire group of selectors\n  // will be ignored.\n  // @link https://www.w3.org/TR/selectors-3/#grouping\n  @each $selector in $selectorsSplit {\n    // Group the selectors back into a single selector to optimize the output.\n    @if str-index($selector, \":host-context\") {\n      $hostContextSelectors: append($hostContextSelectors, $selector, comma);\n    } @else {\n      // Group the selectors back into a single selector to optimize the output.\n      $restSelectors: append($restSelectors, $selector, comma);\n    }\n  }\n\n  // Supported by Chrome.\n  @if length($hostContextSelectors) > 0 {\n    @at-root #{$hostContextSelectors} {\n      @content;\n    }\n  }\n\n  // Supported by all browsers.\n  @if length($restSelectors) > 0 {\n    @at-root #{$restSelectors} {\n      @content;\n    }\n  }\n\n  // If browser can support `:dir()`, then add the `:dir()` selectors.\n  @supports selector(:dir(rtl)) {\n    // Adding :dir() in case the browser doesn't support `:host-context()` and does support `:dir()`.\n    // `:host-context()` is added:\n    // - through the `add-root-selector()` function.\n    // - first so that it takes precedence over `:dir()`.\n    // For example,\n    // - Firefox doesn't support `:host-context()`, but does support `:dir()`.\n    // - Safari doesn't support `:host-context()`, but Safari 16.4+ supports `:dir()`\n    // @link https://webkit.org/blog/13966/webkit-features-in-safari-16-4/\n    // -- However, there is a Webkit bug on v16 that prevents `:dir()` from working when\n    // -- the app direction is changed dynamically. v17+ works fine.\n    // -- @link https://bugs.webkit.org/show_bug.cgi?id=257133\n\n    // Supported by Firefox.\n    @at-root #{add-root-selector($root, \":dir(rtl)\", false)} {\n      @content;\n    }\n  }\n}\n\n@mixin ltr() {\n  @content;\n}\n\n\n// SVG Background Image Mixin\n// @param {string} $svg\n// ----------------------------------------------------------\n@mixin svg-background-image($svg, $flip-rtl: false) {\n  $url: url-encode($svg);\n  $viewBox: str-split(str-extract($svg, \"viewBox='\", \"'\"), \" \");\n\n  @if $flip-rtl != true or $viewBox == null {\n    @include multi-dir() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n  } @else {\n    $transform: \"transform='translate(#{nth($viewBox, 3)}, 0) scale(-1, 1)'\";\n    $flipped-url: $svg;\n    $flipped-url: str-replace($flipped-url, \"<path\", \"<path #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<line\", \"<line #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<polygon\", \"<polygon #{$transform}\");\n    $flipped-url: url-encode($flipped-url);\n\n    @include ltr () {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n    @include rtl() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$flipped-url}\");\n    }\n  }\n}\n\n// Add property horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin property-horizontal($prop, $start, $end: $start) {\n  @if $start == 0 and $end == 0 {\n    #{$prop}-left: $start;\n    #{$prop}-right: $end;\n\n  } @else {\n    -webkit-#{$prop}-start: $start;\n    #{$prop}-inline-start: $start;\n    -webkit-#{$prop}-end: $end;\n    #{$prop}-inline-end: $end;\n  }\n}\n\n// Add property for all directions\n// @param {string} $prop\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// @param {boolean} $content include content or use default\n// ----------------------------------------------------------\n@mixin property($prop, $top, $end: $top, $bottom: $top, $start: $end) {\n  @include property-horizontal($prop, $start, $end);\n  #{$prop}-top: $top;\n  #{$prop}-bottom: $bottom;\n}\n\n// Add padding horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin padding-horizontal($start, $end: $start) {\n  @include property-horizontal(padding, $start, $end);\n}\n\n// Add padding for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin padding($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(padding, $top, $end, $bottom, $start);\n}\n\n// Add margin horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin margin-horizontal($start, $end: $start) {\n  @include property-horizontal(margin, $start, $end);\n}\n\n// Add margin for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin margin($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(margin, $top, $end, $bottom, $start);\n}\n\n// Add position horizontal\n// @param {string} $start - amount to position start\n// @param {string} $end - amount to left: 0; end\n// ----------------------------------------------------------\n@mixin position-horizontal($start: null, $end: null) {\n  @if $start == $end {\n    @include multi-dir() {\n      left: $start;\n      right: $end;\n    }\n  } @else {\n    @at-root {\n      & {\n        inset-inline-start: $start;\n        inset-inline-end: $end;\n      }\n    }\n  }\n}\n\n// Add position for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin position($top: null, $end: null, $bottom: null, $start: null) {\n  @include position-horizontal($start, $end);\n  top: $top;\n  bottom: $bottom;\n}\n\n// Add border for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin border($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(border, $top, $end, $bottom, $start);\n}\n\n// Add border radius for all directions\n// @param {string} $top-start\n// @param {string} $top-end\n// @param {string} $bottom-end\n// @param {string} $bottom-start\n// ----------------------------------------------------------\n@mixin border-radius($top-start, $top-end: $top-start, $bottom-end: $top-start, $bottom-start: $top-end) {\n  @if $top-start == $top-end and $top-start == $bottom-end and $top-start == $bottom-start {\n    border-radius: $top-start;\n  } @else {\n    border-start-start-radius: $top-start;\n    border-start-end-radius: $top-end;\n    border-end-end-radius: $bottom-end;\n    border-end-start-radius: $bottom-start;\n  }\n}\n\n// Add direction for all directions\n// @param {string} $dir - Direction on LTR\n@mixin direction($dir) {\n  $other-dir: null;\n\n  @if $dir == ltr {\n    $other-dir: rtl;\n  } @else {\n    $other-dir: ltr;\n  }\n\n  @include ltr() {\n    direction: $dir;\n  }\n  @include rtl() {\n    direction: $other-dir;\n  }\n}\n\n// Add float for all directions\n// @param {string} $side\n// @param {string} $decorator - !important\n@mixin float($side, $decorator: null) {\n  @if $side == start {\n    @include ltr() {\n      float: left $decorator;\n    }\n    @include rtl() {\n      float: right $decorator;\n    }\n  } @else if $side == end {\n    @include ltr() {\n      float: right $decorator;\n    }\n    @include rtl() {\n      float: left $decorator;\n    }\n  } @else {\n    @include multi-dir() {\n      float: $side $decorator;\n    }\n  }\n}\n\n@mixin background-position($horizontal, $horizontal-amount: null, $vertical: null, $vertical-amount: null) {\n  @if $horizontal == start or $horizontal == end {\n    $horizontal-ltr: null;\n    $horizontal-rtl: null;\n    @if $horizontal == start {\n      $horizontal-ltr: left;\n      $horizontal-rtl: right;\n    } @else {\n      $horizontal-ltr: right;\n      $horizontal-rtl: left;\n    }\n\n    @include ltr() {\n      background-position: $horizontal-ltr $horizontal-amount $vertical $vertical-amount;\n    }\n    @include rtl() {\n      background-position: $horizontal-rtl $horizontal-amount $vertical $vertical-amount;\n    }\n  } @else {\n    @include multi-dir() {\n      background-position: $horizontal $horizontal-amount $vertical $vertical-amount;\n    }\n  }\n}\n\n@mixin transform-origin($x-axis, $y-axis: null) {\n  @if $x-axis == start {\n    @include ltr() {\n      transform-origin: left $y-axis;\n    }\n    @include rtl() {\n      transform-origin: right $y-axis;\n    }\n  } @else if $x-axis == end {\n    @include ltr() {\n      transform-origin: right $y-axis;\n    }\n    @include rtl() {\n      transform-origin: left $y-axis;\n    }\n  } @else if $x-axis == left or $x-axis == right {\n    @include multi-dir() {\n      transform-origin: $x-axis $y-axis;\n    }\n  } @else {\n    @include ltr() {\n      transform-origin: $x-axis $y-axis;\n    }\n    @include rtl() {\n      transform-origin: calc(100% - #{$x-axis}) $y-axis;\n    }\n  }\n}\n\n// Add transform for all directions\n// @param {string} $transforms - comma separated list of transforms\n@mixin transform($transforms...) {\n  $extra: null;\n\n  $x: null;\n  $ltr-translate: null;\n  $rtl-translate: null;\n\n  @each $transform in $transforms {\n    @if (str-index($transform, translate3d)) {\n      $transform: str-replace($transform, 'translate3d(');\n      $transform: str-replace($transform, ')');\n\n      $coordinates: str-split($transform, ',');\n\n      $x: nth($coordinates, 1);\n      $y: nth($coordinates, 2);\n      $z: nth($coordinates, 3);\n\n      $ltr-translate: translate3d($x, $y, $z);\n      $rtl-translate: translate3d(calc(-1 * #{$x}), $y, $z);\n    } @else {\n      @if $extra == null {\n        $extra: $transform;\n      } @else {\n        $extra: $extra $transform;\n      }\n    }\n  }\n\n  @if $x == '0' or $x == null {\n    @include multi-dir() {\n      transform: $ltr-translate $extra;\n    }\n  } @else {\n    @include ltr() {\n      transform: $ltr-translate $extra;\n    }\n\n    @include rtl() {\n      transform: $rtl-translate $extra;\n    }\n  }\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n// Float Elements\n// --------------------------------------------------\n// Creates float classes based on screen size\n\n@each $breakpoint in map-keys($screen-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $screen-breakpoints);\n\n  @include media-breakpoint-up($breakpoint, $screen-breakpoints) {\n    // Provide `.ion-float-{bp}-{side}` classes for floating the element based\n    // on the breakpoint and side\n    .ion-float#{$infix}-left {\n      @include float(left, !important);\n    }\n\n    .ion-float#{$infix}-right {\n      @include float(right, !important);\n    }\n\n    .ion-float#{$infix}-start {\n      @include float(start, !important);\n    }\n\n    .ion-float#{$infix}-end {\n      @include float(end, !important);\n    }\n  }\n}\n", "\n/**\n * A heuristic that applies CSS to tablet\n * viewports.\n *\n * Usage:\n * @include tablet-viewport() {\n *   :host {\n *     background-color: green;\n *   }\n * }\n */\n@mixin tablet-viewport() {\n  @media screen and (min-width: 768px) {\n    @content;\n  }\n}\n\n/**\n * A heuristic that applies CSS to mobile\n * viewports (i.e. phones, not tablets).\n *\n * Usage:\n * @include mobile-viewport() {\n *   :host {\n *     background-color: blue;\n *   }\n * }\n */\n@mixin mobile-viewport() {\n  @media screen and (max-width: 767px) {\n    @content;\n  }\n}\n\n@mixin input-cover() {\n  @include position(0, null, null, 0);\n  @include margin(0);\n\n  position: absolute;\n\n  width: 100%;\n  height: 100%;\n\n  border: 0;\n  background: transparent;\n  cursor: pointer;\n\n  appearance: none;\n  outline: none;\n\n  &::-moz-focus-inner {\n    border: 0;\n  }\n}\n\n@mixin visually-hidden() {\n  position: absolute;\n\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n\n  width: 100%;\n  height: 100%;\n\n  margin: 0;\n  padding: 0;\n\n  border: 0;\n  outline: 0;\n  clip: rect(0 0 0 0);\n\n  opacity: 0;\n  overflow: hidden;\n\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n@mixin text-inherit() {\n  font-family: inherit;\n  font-size: inherit;\n  font-style: inherit;\n  font-weight: inherit;\n  letter-spacing: inherit;\n  text-decoration: inherit;\n  text-indent: inherit;\n  text-overflow: inherit;\n  text-transform: inherit;\n  text-align: inherit;\n  white-space: inherit;\n  color: inherit;\n}\n\n@mixin button-state() {\n  @include position(0, 0, 0, 0);\n\n  position: absolute;\n\n  content: \"\";\n\n  opacity: 0;\n}\n\n// Font smoothing\n// --------------------------------------------------\n\n@mixin font-smoothing() {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n}\n\n// Get the key from a map based on the index\n@function index-to-key($map, $index) {\n  $keys: map-keys($map);\n\n  @return nth($keys, $index);\n}\n\n\n// Breakpoint Mixins\n// ---------------------------------------------------------------------------------\n\n// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$screen-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// ---------------------------------------------------------------------------------\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $screen-breakpoints) {\n  $min: map-get($breakpoints, $name);\n\n  @return if($name != index-to-key($breakpoints, 1), $min, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash infront.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $screen-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $screen-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $screen-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Maximum breakpoint width. Null for the smallest (first) breakpoint.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n//\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\t// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\t// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $screen-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $screen-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n\n// Text Direction - ltr / rtl\n//\n// CSS defaults to use the ltr css, and adds [dir=rtl] selectors\n// to override ltr defaults.\n// ----------------------------------------------------------\n\n@mixin multi-dir() {\n  @content;\n\n  // $root: #{&};\n  // @at-root [dir] {\n  //   #{$root} {\n  //     @content;\n  //   }\n  // }\n}\n\n@mixin rtl() {\n  $root: #{&};\n\n  $rootSplit: str-split($root, \",\");\n  $selectors: #{add-root-selector($root, \"[dir=rtl]\")};\n  $selectorsSplit: str-split($selectors, \",\");\n\n  $hostContextSelectors: ();\n  $restSelectors: ();\n  $dirSelectors: ();\n\n  // Selectors must be split into individual selectors in case the browser\n  // doesn't support a specific selector.\n  // For example, Firefox and Safari doesn't support `:host-context()`.\n  // If an invalid selector is used, then the entire group of selectors\n  // will be ignored.\n  // @link https://www.w3.org/TR/selectors-3/#grouping\n  @each $selector in $selectorsSplit {\n    // Group the selectors back into a single selector to optimize the output.\n    @if str-index($selector, \":host-context\") {\n      $hostContextSelectors: append($hostContextSelectors, $selector, comma);\n    } @else {\n      // Group the selectors back into a single selector to optimize the output.\n      $restSelectors: append($restSelectors, $selector, comma);\n    }\n  }\n\n  // Supported by Chrome.\n  @if length($hostContextSelectors) > 0 {\n    @at-root #{$hostContextSelectors} {\n      @content;\n    }\n  }\n\n  // Supported by all browsers.\n  @if length($restSelectors) > 0 {\n    @at-root #{$restSelectors} {\n      @content;\n    }\n  }\n\n  // If browser can support `:dir()`, then add the `:dir()` selectors.\n  @supports selector(:dir(rtl)) {\n    // Adding :dir() in case the browser doesn't support `:host-context()` and does support `:dir()`.\n    // `:host-context()` is added:\n    // - through the `add-root-selector()` function.\n    // - first so that it takes precedence over `:dir()`.\n    // For example,\n    // - Firefox doesn't support `:host-context()`, but does support `:dir()`.\n    // - Safari doesn't support `:host-context()`, but Safari 16.4+ supports `:dir()`\n    // @link https://webkit.org/blog/13966/webkit-features-in-safari-16-4/\n    // -- However, there is a Webkit bug on v16 that prevents `:dir()` from working when\n    // -- the app direction is changed dynamically. v17+ works fine.\n    // -- @link https://bugs.webkit.org/show_bug.cgi?id=257133\n\n    // Supported by Firefox.\n    @at-root #{add-root-selector($root, \":dir(rtl)\", false)} {\n      @content;\n    }\n  }\n}\n\n@mixin ltr() {\n  @content;\n}\n\n\n// SVG Background Image Mixin\n// @param {string} $svg\n// ----------------------------------------------------------\n@mixin svg-background-image($svg, $flip-rtl: false) {\n  $url: url-encode($svg);\n  $viewBox: str-split(str-extract($svg, \"viewBox='\", \"'\"), \" \");\n\n  @if $flip-rtl != true or $viewBox == null {\n    @include multi-dir() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n  } @else {\n    $transform: \"transform='translate(#{nth($viewBox, 3)}, 0) scale(-1, 1)'\";\n    $flipped-url: $svg;\n    $flipped-url: str-replace($flipped-url, \"<path\", \"<path #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<line\", \"<line #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<polygon\", \"<polygon #{$transform}\");\n    $flipped-url: url-encode($flipped-url);\n\n    @include ltr () {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n    @include rtl() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$flipped-url}\");\n    }\n  }\n}\n\n// Add property horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin property-horizontal($prop, $start, $end: $start) {\n  @if $start == 0 and $end == 0 {\n    #{$prop}-left: $start;\n    #{$prop}-right: $end;\n\n  } @else {\n    -webkit-#{$prop}-start: $start;\n    #{$prop}-inline-start: $start;\n    -webkit-#{$prop}-end: $end;\n    #{$prop}-inline-end: $end;\n  }\n}\n\n// Add property for all directions\n// @param {string} $prop\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// @param {boolean} $content include content or use default\n// ----------------------------------------------------------\n@mixin property($prop, $top, $end: $top, $bottom: $top, $start: $end) {\n  @include property-horizontal($prop, $start, $end);\n  #{$prop}-top: $top;\n  #{$prop}-bottom: $bottom;\n}\n\n// Add padding horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin padding-horizontal($start, $end: $start) {\n  @include property-horizontal(padding, $start, $end);\n}\n\n// Add padding for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin padding($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(padding, $top, $end, $bottom, $start);\n}\n\n// Add margin horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin margin-horizontal($start, $end: $start) {\n  @include property-horizontal(margin, $start, $end);\n}\n\n// Add margin for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin margin($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(margin, $top, $end, $bottom, $start);\n}\n\n// Add position horizontal\n// @param {string} $start - amount to position start\n// @param {string} $end - amount to left: 0; end\n// ----------------------------------------------------------\n@mixin position-horizontal($start: null, $end: null) {\n  @if $start == $end {\n    @include multi-dir() {\n      left: $start;\n      right: $end;\n    }\n  } @else {\n    @at-root {\n      & {\n        inset-inline-start: $start;\n        inset-inline-end: $end;\n      }\n    }\n  }\n}\n\n// Add position for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin position($top: null, $end: null, $bottom: null, $start: null) {\n  @include position-horizontal($start, $end);\n  top: $top;\n  bottom: $bottom;\n}\n\n// Add border for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin border($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(border, $top, $end, $bottom, $start);\n}\n\n// Add border radius for all directions\n// @param {string} $top-start\n// @param {string} $top-end\n// @param {string} $bottom-end\n// @param {string} $bottom-start\n// ----------------------------------------------------------\n@mixin border-radius($top-start, $top-end: $top-start, $bottom-end: $top-start, $bottom-start: $top-end) {\n  @if $top-start == $top-end and $top-start == $bottom-end and $top-start == $bottom-start {\n    border-radius: $top-start;\n  } @else {\n    border-start-start-radius: $top-start;\n    border-start-end-radius: $top-end;\n    border-end-end-radius: $bottom-end;\n    border-end-start-radius: $bottom-start;\n  }\n}\n\n// Add direction for all directions\n// @param {string} $dir - Direction on LTR\n@mixin direction($dir) {\n  $other-dir: null;\n\n  @if $dir == ltr {\n    $other-dir: rtl;\n  } @else {\n    $other-dir: ltr;\n  }\n\n  @include ltr() {\n    direction: $dir;\n  }\n  @include rtl() {\n    direction: $other-dir;\n  }\n}\n\n// Add float for all directions\n// @param {string} $side\n// @param {string} $decorator - !important\n@mixin float($side, $decorator: null) {\n  @if $side == start {\n    @include ltr() {\n      float: left $decorator;\n    }\n    @include rtl() {\n      float: right $decorator;\n    }\n  } @else if $side == end {\n    @include ltr() {\n      float: right $decorator;\n    }\n    @include rtl() {\n      float: left $decorator;\n    }\n  } @else {\n    @include multi-dir() {\n      float: $side $decorator;\n    }\n  }\n}\n\n@mixin background-position($horizontal, $horizontal-amount: null, $vertical: null, $vertical-amount: null) {\n  @if $horizontal == start or $horizontal == end {\n    $horizontal-ltr: null;\n    $horizontal-rtl: null;\n    @if $horizontal == start {\n      $horizontal-ltr: left;\n      $horizontal-rtl: right;\n    } @else {\n      $horizontal-ltr: right;\n      $horizontal-rtl: left;\n    }\n\n    @include ltr() {\n      background-position: $horizontal-ltr $horizontal-amount $vertical $vertical-amount;\n    }\n    @include rtl() {\n      background-position: $horizontal-rtl $horizontal-amount $vertical $vertical-amount;\n    }\n  } @else {\n    @include multi-dir() {\n      background-position: $horizontal $horizontal-amount $vertical $vertical-amount;\n    }\n  }\n}\n\n@mixin transform-origin($x-axis, $y-axis: null) {\n  @if $x-axis == start {\n    @include ltr() {\n      transform-origin: left $y-axis;\n    }\n    @include rtl() {\n      transform-origin: right $y-axis;\n    }\n  } @else if $x-axis == end {\n    @include ltr() {\n      transform-origin: right $y-axis;\n    }\n    @include rtl() {\n      transform-origin: left $y-axis;\n    }\n  } @else if $x-axis == left or $x-axis == right {\n    @include multi-dir() {\n      transform-origin: $x-axis $y-axis;\n    }\n  } @else {\n    @include ltr() {\n      transform-origin: $x-axis $y-axis;\n    }\n    @include rtl() {\n      transform-origin: calc(100% - #{$x-axis}) $y-axis;\n    }\n  }\n}\n\n// Add transform for all directions\n// @param {string} $transforms - comma separated list of transforms\n@mixin transform($transforms...) {\n  $extra: null;\n\n  $x: null;\n  $ltr-translate: null;\n  $rtl-translate: null;\n\n  @each $transform in $transforms {\n    @if (str-index($transform, translate3d)) {\n      $transform: str-replace($transform, 'translate3d(');\n      $transform: str-replace($transform, ')');\n\n      $coordinates: str-split($transform, ',');\n\n      $x: nth($coordinates, 1);\n      $y: nth($coordinates, 2);\n      $z: nth($coordinates, 3);\n\n      $ltr-translate: translate3d($x, $y, $z);\n      $rtl-translate: translate3d(calc(-1 * #{$x}), $y, $z);\n    } @else {\n      @if $extra == null {\n        $extra: $transform;\n      } @else {\n        $extra: $extra $transform;\n      }\n    }\n  }\n\n  @if $x == '0' or $x == null {\n    @include multi-dir() {\n      transform: $ltr-translate $extra;\n    }\n  } @else {\n    @include ltr() {\n      transform: $ltr-translate $extra;\n    }\n\n    @include rtl() {\n      transform: $rtl-translate $extra;\n    }\n  }\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n// Text Alignment\n// --------------------------------------------------\n// Creates text alignment attributes based on screen size\n\n@each $breakpoint in map-keys($screen-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $screen-breakpoints);\n\n  @include media-breakpoint-up($breakpoint, $screen-breakpoints) {\n    // Provide `.ion-text-{bp}` classes for aligning the text based\n    // on the breakpoint\n    .ion-text#{$infix}-center {\n      text-align: center !important;\n    }\n\n    .ion-text#{$infix}-justify {\n      text-align: justify !important;\n    }\n\n    .ion-text#{$infix}-start {\n      text-align: start !important;\n    }\n\n    .ion-text#{$infix}-end {\n      text-align: end !important;\n    }\n\n    .ion-text#{$infix}-left {\n      text-align: left !important;\n    }\n\n    .ion-text#{$infix}-right {\n      text-align: right !important;\n    }\n\n    .ion-text#{$infix}-nowrap {\n      white-space: nowrap !important;\n    }\n\n    .ion-text#{$infix}-wrap {\n      white-space: normal !important;\n    }\n  }\n}\n", "\n/**\n * A heuristic that applies CSS to tablet\n * viewports.\n *\n * Usage:\n * @include tablet-viewport() {\n *   :host {\n *     background-color: green;\n *   }\n * }\n */\n@mixin tablet-viewport() {\n  @media screen and (min-width: 768px) {\n    @content;\n  }\n}\n\n/**\n * A heuristic that applies CSS to mobile\n * viewports (i.e. phones, not tablets).\n *\n * Usage:\n * @include mobile-viewport() {\n *   :host {\n *     background-color: blue;\n *   }\n * }\n */\n@mixin mobile-viewport() {\n  @media screen and (max-width: 767px) {\n    @content;\n  }\n}\n\n@mixin input-cover() {\n  @include position(0, null, null, 0);\n  @include margin(0);\n\n  position: absolute;\n\n  width: 100%;\n  height: 100%;\n\n  border: 0;\n  background: transparent;\n  cursor: pointer;\n\n  appearance: none;\n  outline: none;\n\n  &::-moz-focus-inner {\n    border: 0;\n  }\n}\n\n@mixin visually-hidden() {\n  position: absolute;\n\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n\n  width: 100%;\n  height: 100%;\n\n  margin: 0;\n  padding: 0;\n\n  border: 0;\n  outline: 0;\n  clip: rect(0 0 0 0);\n\n  opacity: 0;\n  overflow: hidden;\n\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n@mixin text-inherit() {\n  font-family: inherit;\n  font-size: inherit;\n  font-style: inherit;\n  font-weight: inherit;\n  letter-spacing: inherit;\n  text-decoration: inherit;\n  text-indent: inherit;\n  text-overflow: inherit;\n  text-transform: inherit;\n  text-align: inherit;\n  white-space: inherit;\n  color: inherit;\n}\n\n@mixin button-state() {\n  @include position(0, 0, 0, 0);\n\n  position: absolute;\n\n  content: \"\";\n\n  opacity: 0;\n}\n\n// Font smoothing\n// --------------------------------------------------\n\n@mixin font-smoothing() {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n}\n\n// Get the key from a map based on the index\n@function index-to-key($map, $index) {\n  $keys: map-keys($map);\n\n  @return nth($keys, $index);\n}\n\n\n// Breakpoint Mixins\n// ---------------------------------------------------------------------------------\n\n// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$screen-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// ---------------------------------------------------------------------------------\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $screen-breakpoints) {\n  $min: map-get($breakpoints, $name);\n\n  @return if($name != index-to-key($breakpoints, 1), $min, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash infront.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $screen-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $screen-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $screen-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Maximum breakpoint width. Null for the smallest (first) breakpoint.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n//\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\t// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\t// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $screen-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $screen-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n\n// Text Direction - ltr / rtl\n//\n// CSS defaults to use the ltr css, and adds [dir=rtl] selectors\n// to override ltr defaults.\n// ----------------------------------------------------------\n\n@mixin multi-dir() {\n  @content;\n\n  // $root: #{&};\n  // @at-root [dir] {\n  //   #{$root} {\n  //     @content;\n  //   }\n  // }\n}\n\n@mixin rtl() {\n  $root: #{&};\n\n  $rootSplit: str-split($root, \",\");\n  $selectors: #{add-root-selector($root, \"[dir=rtl]\")};\n  $selectorsSplit: str-split($selectors, \",\");\n\n  $hostContextSelectors: ();\n  $restSelectors: ();\n  $dirSelectors: ();\n\n  // Selectors must be split into individual selectors in case the browser\n  // doesn't support a specific selector.\n  // For example, Firefox and Safari doesn't support `:host-context()`.\n  // If an invalid selector is used, then the entire group of selectors\n  // will be ignored.\n  // @link https://www.w3.org/TR/selectors-3/#grouping\n  @each $selector in $selectorsSplit {\n    // Group the selectors back into a single selector to optimize the output.\n    @if str-index($selector, \":host-context\") {\n      $hostContextSelectors: append($hostContextSelectors, $selector, comma);\n    } @else {\n      // Group the selectors back into a single selector to optimize the output.\n      $restSelectors: append($restSelectors, $selector, comma);\n    }\n  }\n\n  // Supported by Chrome.\n  @if length($hostContextSelectors) > 0 {\n    @at-root #{$hostContextSelectors} {\n      @content;\n    }\n  }\n\n  // Supported by all browsers.\n  @if length($restSelectors) > 0 {\n    @at-root #{$restSelectors} {\n      @content;\n    }\n  }\n\n  // If browser can support `:dir()`, then add the `:dir()` selectors.\n  @supports selector(:dir(rtl)) {\n    // Adding :dir() in case the browser doesn't support `:host-context()` and does support `:dir()`.\n    // `:host-context()` is added:\n    // - through the `add-root-selector()` function.\n    // - first so that it takes precedence over `:dir()`.\n    // For example,\n    // - Firefox doesn't support `:host-context()`, but does support `:dir()`.\n    // - Safari doesn't support `:host-context()`, but Safari 16.4+ supports `:dir()`\n    // @link https://webkit.org/blog/13966/webkit-features-in-safari-16-4/\n    // -- However, there is a Webkit bug on v16 that prevents `:dir()` from working when\n    // -- the app direction is changed dynamically. v17+ works fine.\n    // -- @link https://bugs.webkit.org/show_bug.cgi?id=257133\n\n    // Supported by Firefox.\n    @at-root #{add-root-selector($root, \":dir(rtl)\", false)} {\n      @content;\n    }\n  }\n}\n\n@mixin ltr() {\n  @content;\n}\n\n\n// SVG Background Image Mixin\n// @param {string} $svg\n// ----------------------------------------------------------\n@mixin svg-background-image($svg, $flip-rtl: false) {\n  $url: url-encode($svg);\n  $viewBox: str-split(str-extract($svg, \"viewBox='\", \"'\"), \" \");\n\n  @if $flip-rtl != true or $viewBox == null {\n    @include multi-dir() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n  } @else {\n    $transform: \"transform='translate(#{nth($viewBox, 3)}, 0) scale(-1, 1)'\";\n    $flipped-url: $svg;\n    $flipped-url: str-replace($flipped-url, \"<path\", \"<path #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<line\", \"<line #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<polygon\", \"<polygon #{$transform}\");\n    $flipped-url: url-encode($flipped-url);\n\n    @include ltr () {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n    @include rtl() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$flipped-url}\");\n    }\n  }\n}\n\n// Add property horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin property-horizontal($prop, $start, $end: $start) {\n  @if $start == 0 and $end == 0 {\n    #{$prop}-left: $start;\n    #{$prop}-right: $end;\n\n  } @else {\n    -webkit-#{$prop}-start: $start;\n    #{$prop}-inline-start: $start;\n    -webkit-#{$prop}-end: $end;\n    #{$prop}-inline-end: $end;\n  }\n}\n\n// Add property for all directions\n// @param {string} $prop\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// @param {boolean} $content include content or use default\n// ----------------------------------------------------------\n@mixin property($prop, $top, $end: $top, $bottom: $top, $start: $end) {\n  @include property-horizontal($prop, $start, $end);\n  #{$prop}-top: $top;\n  #{$prop}-bottom: $bottom;\n}\n\n// Add padding horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin padding-horizontal($start, $end: $start) {\n  @include property-horizontal(padding, $start, $end);\n}\n\n// Add padding for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin padding($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(padding, $top, $end, $bottom, $start);\n}\n\n// Add margin horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin margin-horizontal($start, $end: $start) {\n  @include property-horizontal(margin, $start, $end);\n}\n\n// Add margin for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin margin($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(margin, $top, $end, $bottom, $start);\n}\n\n// Add position horizontal\n// @param {string} $start - amount to position start\n// @param {string} $end - amount to left: 0; end\n// ----------------------------------------------------------\n@mixin position-horizontal($start: null, $end: null) {\n  @if $start == $end {\n    @include multi-dir() {\n      left: $start;\n      right: $end;\n    }\n  } @else {\n    @at-root {\n      & {\n        inset-inline-start: $start;\n        inset-inline-end: $end;\n      }\n    }\n  }\n}\n\n// Add position for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin position($top: null, $end: null, $bottom: null, $start: null) {\n  @include position-horizontal($start, $end);\n  top: $top;\n  bottom: $bottom;\n}\n\n// Add border for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin border($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(border, $top, $end, $bottom, $start);\n}\n\n// Add border radius for all directions\n// @param {string} $top-start\n// @param {string} $top-end\n// @param {string} $bottom-end\n// @param {string} $bottom-start\n// ----------------------------------------------------------\n@mixin border-radius($top-start, $top-end: $top-start, $bottom-end: $top-start, $bottom-start: $top-end) {\n  @if $top-start == $top-end and $top-start == $bottom-end and $top-start == $bottom-start {\n    border-radius: $top-start;\n  } @else {\n    border-start-start-radius: $top-start;\n    border-start-end-radius: $top-end;\n    border-end-end-radius: $bottom-end;\n    border-end-start-radius: $bottom-start;\n  }\n}\n\n// Add direction for all directions\n// @param {string} $dir - Direction on LTR\n@mixin direction($dir) {\n  $other-dir: null;\n\n  @if $dir == ltr {\n    $other-dir: rtl;\n  } @else {\n    $other-dir: ltr;\n  }\n\n  @include ltr() {\n    direction: $dir;\n  }\n  @include rtl() {\n    direction: $other-dir;\n  }\n}\n\n// Add float for all directions\n// @param {string} $side\n// @param {string} $decorator - !important\n@mixin float($side, $decorator: null) {\n  @if $side == start {\n    @include ltr() {\n      float: left $decorator;\n    }\n    @include rtl() {\n      float: right $decorator;\n    }\n  } @else if $side == end {\n    @include ltr() {\n      float: right $decorator;\n    }\n    @include rtl() {\n      float: left $decorator;\n    }\n  } @else {\n    @include multi-dir() {\n      float: $side $decorator;\n    }\n  }\n}\n\n@mixin background-position($horizontal, $horizontal-amount: null, $vertical: null, $vertical-amount: null) {\n  @if $horizontal == start or $horizontal == end {\n    $horizontal-ltr: null;\n    $horizontal-rtl: null;\n    @if $horizontal == start {\n      $horizontal-ltr: left;\n      $horizontal-rtl: right;\n    } @else {\n      $horizontal-ltr: right;\n      $horizontal-rtl: left;\n    }\n\n    @include ltr() {\n      background-position: $horizontal-ltr $horizontal-amount $vertical $vertical-amount;\n    }\n    @include rtl() {\n      background-position: $horizontal-rtl $horizontal-amount $vertical $vertical-amount;\n    }\n  } @else {\n    @include multi-dir() {\n      background-position: $horizontal $horizontal-amount $vertical $vertical-amount;\n    }\n  }\n}\n\n@mixin transform-origin($x-axis, $y-axis: null) {\n  @if $x-axis == start {\n    @include ltr() {\n      transform-origin: left $y-axis;\n    }\n    @include rtl() {\n      transform-origin: right $y-axis;\n    }\n  } @else if $x-axis == end {\n    @include ltr() {\n      transform-origin: right $y-axis;\n    }\n    @include rtl() {\n      transform-origin: left $y-axis;\n    }\n  } @else if $x-axis == left or $x-axis == right {\n    @include multi-dir() {\n      transform-origin: $x-axis $y-axis;\n    }\n  } @else {\n    @include ltr() {\n      transform-origin: $x-axis $y-axis;\n    }\n    @include rtl() {\n      transform-origin: calc(100% - #{$x-axis}) $y-axis;\n    }\n  }\n}\n\n// Add transform for all directions\n// @param {string} $transforms - comma separated list of transforms\n@mixin transform($transforms...) {\n  $extra: null;\n\n  $x: null;\n  $ltr-translate: null;\n  $rtl-translate: null;\n\n  @each $transform in $transforms {\n    @if (str-index($transform, translate3d)) {\n      $transform: str-replace($transform, 'translate3d(');\n      $transform: str-replace($transform, ')');\n\n      $coordinates: str-split($transform, ',');\n\n      $x: nth($coordinates, 1);\n      $y: nth($coordinates, 2);\n      $z: nth($coordinates, 3);\n\n      $ltr-translate: translate3d($x, $y, $z);\n      $rtl-translate: translate3d(calc(-1 * #{$x}), $y, $z);\n    } @else {\n      @if $extra == null {\n        $extra: $transform;\n      } @else {\n        $extra: $extra $transform;\n      }\n    }\n  }\n\n  @if $x == '0' or $x == null {\n    @include multi-dir() {\n      transform: $ltr-translate $extra;\n    }\n  } @else {\n    @include ltr() {\n      transform: $ltr-translate $extra;\n    }\n\n    @include rtl() {\n      transform: $rtl-translate $extra;\n    }\n  }\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n// Text Transformation\n// --------------------------------------------------\n// Creates text transform attributes based on screen size\n\n@each $breakpoint in map-keys($screen-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $screen-breakpoints);\n\n  @include media-breakpoint-up($breakpoint, $screen-breakpoints) {\n    // Provide `.ion-text-{bp}` classes for transforming the text based\n    // on the breakpoint\n    .ion-text#{$infix}-uppercase {\n      /* stylelint-disable-next-line declaration-no-important */\n      text-transform: uppercase !important;\n    }\n\n    .ion-text#{$infix}-lowercase {\n      /* stylelint-disable-next-line declaration-no-important */\n      text-transform: lowercase !important;\n    }\n\n    .ion-text#{$infix}-capitalize {\n      /* stylelint-disable-next-line declaration-no-important */\n      text-transform: capitalize !important;\n    }\n  }\n}\n", "\n/**\n * A heuristic that applies CSS to tablet\n * viewports.\n *\n * Usage:\n * @include tablet-viewport() {\n *   :host {\n *     background-color: green;\n *   }\n * }\n */\n@mixin tablet-viewport() {\n  @media screen and (min-width: 768px) {\n    @content;\n  }\n}\n\n/**\n * A heuristic that applies CSS to mobile\n * viewports (i.e. phones, not tablets).\n *\n * Usage:\n * @include mobile-viewport() {\n *   :host {\n *     background-color: blue;\n *   }\n * }\n */\n@mixin mobile-viewport() {\n  @media screen and (max-width: 767px) {\n    @content;\n  }\n}\n\n@mixin input-cover() {\n  @include position(0, null, null, 0);\n  @include margin(0);\n\n  position: absolute;\n\n  width: 100%;\n  height: 100%;\n\n  border: 0;\n  background: transparent;\n  cursor: pointer;\n\n  appearance: none;\n  outline: none;\n\n  &::-moz-focus-inner {\n    border: 0;\n  }\n}\n\n@mixin visually-hidden() {\n  position: absolute;\n\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n\n  width: 100%;\n  height: 100%;\n\n  margin: 0;\n  padding: 0;\n\n  border: 0;\n  outline: 0;\n  clip: rect(0 0 0 0);\n\n  opacity: 0;\n  overflow: hidden;\n\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n@mixin text-inherit() {\n  font-family: inherit;\n  font-size: inherit;\n  font-style: inherit;\n  font-weight: inherit;\n  letter-spacing: inherit;\n  text-decoration: inherit;\n  text-indent: inherit;\n  text-overflow: inherit;\n  text-transform: inherit;\n  text-align: inherit;\n  white-space: inherit;\n  color: inherit;\n}\n\n@mixin button-state() {\n  @include position(0, 0, 0, 0);\n\n  position: absolute;\n\n  content: \"\";\n\n  opacity: 0;\n}\n\n// Font smoothing\n// --------------------------------------------------\n\n@mixin font-smoothing() {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n}\n\n// Get the key from a map based on the index\n@function index-to-key($map, $index) {\n  $keys: map-keys($map);\n\n  @return nth($keys, $index);\n}\n\n\n// Breakpoint Mixins\n// ---------------------------------------------------------------------------------\n\n// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$screen-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// ---------------------------------------------------------------------------------\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $screen-breakpoints) {\n  $min: map-get($breakpoints, $name);\n\n  @return if($name != index-to-key($breakpoints, 1), $min, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash infront.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $screen-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $screen-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $screen-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Maximum breakpoint width. Null for the smallest (first) breakpoint.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n//\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\t// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\t// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $screen-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $screen-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n\n// Text Direction - ltr / rtl\n//\n// CSS defaults to use the ltr css, and adds [dir=rtl] selectors\n// to override ltr defaults.\n// ----------------------------------------------------------\n\n@mixin multi-dir() {\n  @content;\n\n  // $root: #{&};\n  // @at-root [dir] {\n  //   #{$root} {\n  //     @content;\n  //   }\n  // }\n}\n\n@mixin rtl() {\n  $root: #{&};\n\n  $rootSplit: str-split($root, \",\");\n  $selectors: #{add-root-selector($root, \"[dir=rtl]\")};\n  $selectorsSplit: str-split($selectors, \",\");\n\n  $hostContextSelectors: ();\n  $restSelectors: ();\n  $dirSelectors: ();\n\n  // Selectors must be split into individual selectors in case the browser\n  // doesn't support a specific selector.\n  // For example, Firefox and Safari doesn't support `:host-context()`.\n  // If an invalid selector is used, then the entire group of selectors\n  // will be ignored.\n  // @link https://www.w3.org/TR/selectors-3/#grouping\n  @each $selector in $selectorsSplit {\n    // Group the selectors back into a single selector to optimize the output.\n    @if str-index($selector, \":host-context\") {\n      $hostContextSelectors: append($hostContextSelectors, $selector, comma);\n    } @else {\n      // Group the selectors back into a single selector to optimize the output.\n      $restSelectors: append($restSelectors, $selector, comma);\n    }\n  }\n\n  // Supported by Chrome.\n  @if length($hostContextSelectors) > 0 {\n    @at-root #{$hostContextSelectors} {\n      @content;\n    }\n  }\n\n  // Supported by all browsers.\n  @if length($restSelectors) > 0 {\n    @at-root #{$restSelectors} {\n      @content;\n    }\n  }\n\n  // If browser can support `:dir()`, then add the `:dir()` selectors.\n  @supports selector(:dir(rtl)) {\n    // Adding :dir() in case the browser doesn't support `:host-context()` and does support `:dir()`.\n    // `:host-context()` is added:\n    // - through the `add-root-selector()` function.\n    // - first so that it takes precedence over `:dir()`.\n    // For example,\n    // - Firefox doesn't support `:host-context()`, but does support `:dir()`.\n    // - Safari doesn't support `:host-context()`, but Safari 16.4+ supports `:dir()`\n    // @link https://webkit.org/blog/13966/webkit-features-in-safari-16-4/\n    // -- However, there is a Webkit bug on v16 that prevents `:dir()` from working when\n    // -- the app direction is changed dynamically. v17+ works fine.\n    // -- @link https://bugs.webkit.org/show_bug.cgi?id=257133\n\n    // Supported by Firefox.\n    @at-root #{add-root-selector($root, \":dir(rtl)\", false)} {\n      @content;\n    }\n  }\n}\n\n@mixin ltr() {\n  @content;\n}\n\n\n// SVG Background Image Mixin\n// @param {string} $svg\n// ----------------------------------------------------------\n@mixin svg-background-image($svg, $flip-rtl: false) {\n  $url: url-encode($svg);\n  $viewBox: str-split(str-extract($svg, \"viewBox='\", \"'\"), \" \");\n\n  @if $flip-rtl != true or $viewBox == null {\n    @include multi-dir() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n  } @else {\n    $transform: \"transform='translate(#{nth($viewBox, 3)}, 0) scale(-1, 1)'\";\n    $flipped-url: $svg;\n    $flipped-url: str-replace($flipped-url, \"<path\", \"<path #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<line\", \"<line #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<polygon\", \"<polygon #{$transform}\");\n    $flipped-url: url-encode($flipped-url);\n\n    @include ltr () {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n    @include rtl() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$flipped-url}\");\n    }\n  }\n}\n\n// Add property horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin property-horizontal($prop, $start, $end: $start) {\n  @if $start == 0 and $end == 0 {\n    #{$prop}-left: $start;\n    #{$prop}-right: $end;\n\n  } @else {\n    -webkit-#{$prop}-start: $start;\n    #{$prop}-inline-start: $start;\n    -webkit-#{$prop}-end: $end;\n    #{$prop}-inline-end: $end;\n  }\n}\n\n// Add property for all directions\n// @param {string} $prop\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// @param {boolean} $content include content or use default\n// ----------------------------------------------------------\n@mixin property($prop, $top, $end: $top, $bottom: $top, $start: $end) {\n  @include property-horizontal($prop, $start, $end);\n  #{$prop}-top: $top;\n  #{$prop}-bottom: $bottom;\n}\n\n// Add padding horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin padding-horizontal($start, $end: $start) {\n  @include property-horizontal(padding, $start, $end);\n}\n\n// Add padding for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin padding($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(padding, $top, $end, $bottom, $start);\n}\n\n// Add margin horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin margin-horizontal($start, $end: $start) {\n  @include property-horizontal(margin, $start, $end);\n}\n\n// Add margin for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin margin($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(margin, $top, $end, $bottom, $start);\n}\n\n// Add position horizontal\n// @param {string} $start - amount to position start\n// @param {string} $end - amount to left: 0; end\n// ----------------------------------------------------------\n@mixin position-horizontal($start: null, $end: null) {\n  @if $start == $end {\n    @include multi-dir() {\n      left: $start;\n      right: $end;\n    }\n  } @else {\n    @at-root {\n      & {\n        inset-inline-start: $start;\n        inset-inline-end: $end;\n      }\n    }\n  }\n}\n\n// Add position for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin position($top: null, $end: null, $bottom: null, $start: null) {\n  @include position-horizontal($start, $end);\n  top: $top;\n  bottom: $bottom;\n}\n\n// Add border for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin border($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(border, $top, $end, $bottom, $start);\n}\n\n// Add border radius for all directions\n// @param {string} $top-start\n// @param {string} $top-end\n// @param {string} $bottom-end\n// @param {string} $bottom-start\n// ----------------------------------------------------------\n@mixin border-radius($top-start, $top-end: $top-start, $bottom-end: $top-start, $bottom-start: $top-end) {\n  @if $top-start == $top-end and $top-start == $bottom-end and $top-start == $bottom-start {\n    border-radius: $top-start;\n  } @else {\n    border-start-start-radius: $top-start;\n    border-start-end-radius: $top-end;\n    border-end-end-radius: $bottom-end;\n    border-end-start-radius: $bottom-start;\n  }\n}\n\n// Add direction for all directions\n// @param {string} $dir - Direction on LTR\n@mixin direction($dir) {\n  $other-dir: null;\n\n  @if $dir == ltr {\n    $other-dir: rtl;\n  } @else {\n    $other-dir: ltr;\n  }\n\n  @include ltr() {\n    direction: $dir;\n  }\n  @include rtl() {\n    direction: $other-dir;\n  }\n}\n\n// Add float for all directions\n// @param {string} $side\n// @param {string} $decorator - !important\n@mixin float($side, $decorator: null) {\n  @if $side == start {\n    @include ltr() {\n      float: left $decorator;\n    }\n    @include rtl() {\n      float: right $decorator;\n    }\n  } @else if $side == end {\n    @include ltr() {\n      float: right $decorator;\n    }\n    @include rtl() {\n      float: left $decorator;\n    }\n  } @else {\n    @include multi-dir() {\n      float: $side $decorator;\n    }\n  }\n}\n\n@mixin background-position($horizontal, $horizontal-amount: null, $vertical: null, $vertical-amount: null) {\n  @if $horizontal == start or $horizontal == end {\n    $horizontal-ltr: null;\n    $horizontal-rtl: null;\n    @if $horizontal == start {\n      $horizontal-ltr: left;\n      $horizontal-rtl: right;\n    } @else {\n      $horizontal-ltr: right;\n      $horizontal-rtl: left;\n    }\n\n    @include ltr() {\n      background-position: $horizontal-ltr $horizontal-amount $vertical $vertical-amount;\n    }\n    @include rtl() {\n      background-position: $horizontal-rtl $horizontal-amount $vertical $vertical-amount;\n    }\n  } @else {\n    @include multi-dir() {\n      background-position: $horizontal $horizontal-amount $vertical $vertical-amount;\n    }\n  }\n}\n\n@mixin transform-origin($x-axis, $y-axis: null) {\n  @if $x-axis == start {\n    @include ltr() {\n      transform-origin: left $y-axis;\n    }\n    @include rtl() {\n      transform-origin: right $y-axis;\n    }\n  } @else if $x-axis == end {\n    @include ltr() {\n      transform-origin: right $y-axis;\n    }\n    @include rtl() {\n      transform-origin: left $y-axis;\n    }\n  } @else if $x-axis == left or $x-axis == right {\n    @include multi-dir() {\n      transform-origin: $x-axis $y-axis;\n    }\n  } @else {\n    @include ltr() {\n      transform-origin: $x-axis $y-axis;\n    }\n    @include rtl() {\n      transform-origin: calc(100% - #{$x-axis}) $y-axis;\n    }\n  }\n}\n\n// Add transform for all directions\n// @param {string} $transforms - comma separated list of transforms\n@mixin transform($transforms...) {\n  $extra: null;\n\n  $x: null;\n  $ltr-translate: null;\n  $rtl-translate: null;\n\n  @each $transform in $transforms {\n    @if (str-index($transform, translate3d)) {\n      $transform: str-replace($transform, 'translate3d(');\n      $transform: str-replace($transform, ')');\n\n      $coordinates: str-split($transform, ',');\n\n      $x: nth($coordinates, 1);\n      $y: nth($coordinates, 2);\n      $z: nth($coordinates, 3);\n\n      $ltr-translate: translate3d($x, $y, $z);\n      $rtl-translate: translate3d(calc(-1 * #{$x}), $y, $z);\n    } @else {\n      @if $extra == null {\n        $extra: $transform;\n      } @else {\n        $extra: $extra $transform;\n      }\n    }\n  }\n\n  @if $x == '0' or $x == null {\n    @include multi-dir() {\n      transform: $ltr-translate $extra;\n    }\n  } @else {\n    @include ltr() {\n      transform: $ltr-translate $extra;\n    }\n\n    @include rtl() {\n      transform: $rtl-translate $extra;\n    }\n  }\n}\n", "// Flex Utilities\n// --------------------------------------------------\n// Creates flex classes to align flex containers\n// and items\n\n// Align Self\n// --------------------------------------------------\n\n.ion-align-self-start {\n  align-self: flex-start !important;\n}\n\n.ion-align-self-end {\n  align-self: flex-end !important;\n}\n\n.ion-align-self-center {\n  align-self: center !important;\n}\n\n.ion-align-self-stretch {\n  align-self: stretch !important;\n}\n\n.ion-align-self-baseline {\n  align-self: baseline !important;\n}\n\n.ion-align-self-auto {\n  align-self: auto !important;\n}\n\n\n// Flex Wrap\n// --------------------------------------------------\n\n.ion-wrap {\n  flex-wrap: wrap !important;\n}\n\n.ion-nowrap {\n  flex-wrap: nowrap !important;\n}\n\n.ion-wrap-reverse {\n  flex-wrap: wrap-reverse !important;\n}\n\n\n// Justify Content\n// --------------------------------------------------\n\n.ion-justify-content-start {\n  justify-content: flex-start !important;\n}\n\n.ion-justify-content-center {\n  justify-content: center !important;\n}\n\n.ion-justify-content-end {\n  justify-content: flex-end !important;\n}\n\n.ion-justify-content-around {\n  justify-content: space-around !important;\n}\n\n.ion-justify-content-between {\n  justify-content: space-between !important;\n}\n\n.ion-justify-content-evenly {\n  justify-content: space-evenly !important;\n}\n\n\n// Align Items\n// --------------------------------------------------\n\n.ion-align-items-start {\n  align-items: flex-start !important;\n}\n\n.ion-align-items-center {\n  align-items: center !important;\n}\n\n.ion-align-items-end {\n  align-items: flex-end !important;\n}\n\n.ion-align-items-stretch {\n  align-items: stretch !important;\n}\n\n.ion-align-items-baseline {\n  align-items: baseline !important;\n}\n", "@import \"./dark\";\n\n@media (prefers-color-scheme: dark) {\n  :root {\n    @include dark-base-palette();\n  }\n\n  :root.ios {\n    @include dark-ios-palette();\n  }\n\n  :root.md {\n    @include dark-md-palette();\n  }\n}\n", "@use \"sass:map\";\n@import \"../../themes/ionic.functions.color\";\n\n$primary: #4d8dff;\n$secondary: #46b1ff;\n$tertiary: #8482fb;\n$success: #2dd55b;\n$warning: #ffce31;\n$danger: #f24c58;\n$light: #222428;\n$medium: #989aa2;\n$dark: #f4f5f8;\n\n$colors:  (\n  primary: (\n    base:             $primary,\n    contrast:         #000,\n    shade:            get-color-shade($primary),\n    tint:             get-color-tint($primary)\n  ),\n  secondary: (\n    base:             $secondary,\n    contrast:         #000,\n    shade:            get-color-shade($secondary),\n    tint:             get-color-tint($secondary)\n  ),\n  tertiary: (\n    base:             $tertiary,\n    contrast:         #000,\n    shade:            get-color-shade($tertiary),\n    tint:             get-color-tint($tertiary)\n  ),\n  success: (\n    base:             $success,\n    contrast:         #000,\n    shade:            get-color-shade($success),\n    tint:             get-color-tint($success)\n  ),\n  warning: (\n    base:             $warning,\n    contrast:         #000,\n    shade:            get-color-shade($warning),\n    tint:             get-color-tint($warning)\n  ),\n  danger: (\n    base:             $danger,\n    contrast:         #000,\n    shade:            get-color-shade($danger),\n    tint:             get-color-tint($danger)\n  ),\n  light: (\n    base:             $light,\n    contrast:         #fff,\n    shade:            get-color-shade($light),\n    tint:             get-color-tint($light)\n  ),\n  medium: (\n    base:             $medium,\n    contrast:         #000,\n    shade:            get-color-shade($medium),\n    tint:             get-color-tint($medium)\n  ),\n  dark: (\n    base:             $dark,\n    contrast:         #000,\n    shade:            get-color-shade($dark),\n    tint:             get-color-tint($dark)\n  )\n);\n\n@mixin dark-base-palette() {\n  & {\n    @each $color-name, $value in $colors {\n      --ion-color-#{$color-name}: #{map.get($value, base)};\n      --ion-color-#{$color-name}-rgb: #{color-to-rgb-list(map.get($value, base))};\n      --ion-color-#{$color-name}-contrast: #{map.get($value, contrast)};\n      --ion-color-#{$color-name}-contrast-rgb: #{color-to-rgb-list(map.get($value, contrast))};\n      --ion-color-#{$color-name}-shade: #{map.get($value, shade)};\n      --ion-color-#{$color-name}-tint: #{map.get($value, tint)};\n    }\n  }\n}\n\n@mixin dark-ios-palette() {\n  & {\n    --ion-background-color: #000000;\n    --ion-background-color-rgb: 0, 0, 0;\n    --ion-text-color: #ffffff;\n    --ion-text-color-rgb: 255, 255, 255;\n    --ion-background-color-step-50: #0d0d0d;\n    --ion-background-color-step-100: #1a1a1a;\n    --ion-background-color-step-150: #262626;\n    --ion-background-color-step-200: #333333;\n    --ion-background-color-step-250: #404040;\n    --ion-background-color-step-300: #4d4d4d;\n    --ion-background-color-step-350: #595959;\n    --ion-background-color-step-400: #666666;\n    --ion-background-color-step-450: #737373;\n    --ion-background-color-step-500: #808080;\n    --ion-background-color-step-550: #8c8c8c;\n    --ion-background-color-step-600: #999999;\n    --ion-background-color-step-650: #a6a6a6;\n    --ion-background-color-step-700: #b3b3b3;\n    --ion-background-color-step-750: #bfbfbf;\n    --ion-background-color-step-800: #cccccc;\n    --ion-background-color-step-850: #d9d9d9;\n    --ion-background-color-step-900: #e6e6e6;\n    --ion-background-color-step-950: #f2f2f2;\n    --ion-text-color-step-50: #f2f2f2;\n    --ion-text-color-step-100: #e6e6e6;\n    --ion-text-color-step-150: #d9d9d9;\n    --ion-text-color-step-200: #cccccc;\n    --ion-text-color-step-250: #bfbfbf;\n    --ion-text-color-step-300: #b3b3b3;\n    --ion-text-color-step-350: #a6a6a6;\n    --ion-text-color-step-400: #999999;\n    --ion-text-color-step-450: #8c8c8c;\n    --ion-text-color-step-500: #808080;\n    --ion-text-color-step-550: #737373;\n    --ion-text-color-step-600: #666666;\n    --ion-text-color-step-650: #595959;\n    --ion-text-color-step-700: #4d4d4d;\n    --ion-text-color-step-750: #404040;\n    --ion-text-color-step-800: #333333;\n    --ion-text-color-step-850: #262626;\n    --ion-text-color-step-900: #1a1a1a;\n    --ion-text-color-step-950: #0d0d0d;\n    --ion-item-background: #000000;\n    --ion-card-background: #1c1c1d;\n  }\n\n  & ion-modal {\n    --ion-background-color: var(--ion-color-step-100, var(--ion-background-color-step-100));\n    --ion-toolbar-background: var(--ion-color-step-150, var(--ion-background-color-step-150));\n    --ion-toolbar-border-color: var(--ion-color-step-250, var(--ion-background-color-step-250));\n  }\n}\n\n@mixin dark-md-palette() {\n  & {\n    --ion-background-color: #121212;\n    --ion-background-color-rgb: 18, 18, 18;\n    --ion-text-color: #ffffff;\n    --ion-text-color-rgb: 255, 255, 255;\n    --ion-background-color-step-50: #1e1e1e;\n    --ion-background-color-step-100: #2a2a2a;\n    --ion-background-color-step-150: #363636;\n    --ion-background-color-step-200: #414141;\n    --ion-background-color-step-250: #4d4d4d;\n    --ion-background-color-step-300: #595959;\n    --ion-background-color-step-350: #656565;\n    --ion-background-color-step-400: #717171;\n    --ion-background-color-step-450: #7d7d7d;\n    --ion-background-color-step-500: #898989;\n    --ion-background-color-step-550: #949494;\n    --ion-background-color-step-600: #a0a0a0;\n    --ion-background-color-step-650: #acacac;\n    --ion-background-color-step-700: #b8b8b8;\n    --ion-background-color-step-750: #c4c4c4;\n    --ion-background-color-step-800: #d0d0d0;\n    --ion-background-color-step-850: #dbdbdb;\n    --ion-background-color-step-900: #e7e7e7;\n    --ion-background-color-step-950: #f3f3f3;\n    --ion-text-color-step-50: #f3f3f3;\n    --ion-text-color-step-100: #e7e7e7;\n    --ion-text-color-step-150: #dbdbdb;\n    --ion-text-color-step-200: #d0d0d0;\n    --ion-text-color-step-250: #c4c4c4;\n    --ion-text-color-step-300: #b8b8b8;\n    --ion-text-color-step-350: #acacac;\n    --ion-text-color-step-400: #a0a0a0;\n    --ion-text-color-step-450: #949494;\n    --ion-text-color-step-500: #898989;\n    --ion-text-color-step-550: #7d7d7d;\n    --ion-text-color-step-600: #717171;\n    --ion-text-color-step-650: #656565;\n    --ion-text-color-step-700: #595959;\n    --ion-text-color-step-750: #4d4d4d;\n    --ion-text-color-step-800: #414141;\n    --ion-text-color-step-850: #363636;\n    --ion-text-color-step-900: #2a2a2a;\n    --ion-text-color-step-950: #1e1e1e;\n    --ion-item-background: #1e1e1e;\n    --ion-toolbar-background: #1f1f1f;\n    --ion-tab-bar-background: #1f1f1f;\n    --ion-card-background: #1e1e1e;\n  }\n}\n", "/**\n * Custom App Styles\n * -----------------------------------------------------\n * Import custom theme files\n */\n @use './theme/mixins.scss';\n @use './theme/components.scss';\n @use './theme/utilities.scss';\n\n/*\n * App Global CSS\n * ----------------------------------------------------------------------------\n * Put style rules here that you want to apply globally. These styles are for\n * the entire app and not just one component. Additionally, this file can be\n * used as an entry point to import other CSS/Sass files to be included in the\n * output CSS.\n * For more information on global stylesheets, visit the documentation:\n * https://ionicframework.com/docs/layout/global-stylesheets\n */\n\n/* Core CSS required for Ionic components to work properly */\n@import \"@ionic/angular/css/core.css\";\n\n/* Basic CSS for apps built with Ionic */\n@import \"@ionic/angular/css/normalize.css\";\n@import \"@ionic/angular/css/structure.css\";\n@import \"@ionic/angular/css/typography.css\";\n@import \"@ionic/angular/css/display.css\";\n\n/* Optional CSS utils that can be commented out */\n@import \"@ionic/angular/css/padding.css\";\n@import \"@ionic/angular/css/float-elements.css\";\n@import \"@ionic/angular/css/text-alignment.css\";\n@import \"@ionic/angular/css/text-transformation.css\";\n@import \"@ionic/angular/css/flex-utils.css\";\n\n/**\n * Ionic Dark Mode\n * -----------------------------------------------------\n * For more info, please see:\n * https://ionicframework.com/docs/theming/dark-mode\n */\n\n/* @import \"@ionic/angular/css/palettes/dark.always.css\"; */\n/* @import \"@ionic/angular/css/palettes/dark.class.css\"; */\n@import '@ionic/angular/css/palettes/dark.system.css';\n\n\n/**\n * Global App Styles\n * -----------------------------------------------------\n * These styles apply to the entire app\n */\n\n// Apply base font family to the entire app\n* {\n  font-family: var(--app-font-family);\n}\n\n// Set default text color\nbody, ion-content {\n  color: var(--app-text-color);\n}\n\n// Set default background color\nion-content {\n  --background: var(--app-background-color);\n}\n\n// Improve focus styles for accessibility\n:focus {\n  outline: 2px solid var(--ion-color-primary);\n  outline-offset: 2px;\n}\n\n// Smooth scrolling\nhtml {\n  scroll-behavior: smooth;\n}\n\n// Improve tap highlight on mobile\n* {\n  -webkit-tap-highlight-color: rgba(var(--ion-color-primary-rgb), 0.2);\n}\n\n// Improve form elements\nion-input, ion-textarea, ion-select {\n  margin-bottom: var(--app-spacing-md);\n}\n\n// Improve buttons\nion-button {\n  text-transform: none;\n}\n\n// Improve card styles\nion-card {\n  overflow: hidden;\n  border-radius: var(--app-border-radius-md);\n  box-shadow: var(--app-card-shadow);\n  margin: var(--app-spacing-md);\n\n  ion-card-header {\n    padding-bottom: var(--app-spacing-sm);\n\n    ion-card-title {\n      font-size: 18px;\n      font-weight: var(--app-heading-font-weight);\n      color: var(--ion-color-primary);\n    }\n\n    ion-card-subtitle {\n      font-size: 14px;\n      color: var(--ion-color-medium);\n    }\n  }\n\n  ion-card-content {\n    padding: var(--app-spacing-md);\n  }\n}\n\n// Custom list styles\nion-list {\n  background: transparent;\n\n  ion-item {\n    --padding-start: var(--app-spacing-md);\n    --padding-end: var(--app-spacing-md);\n    --padding-top: var(--app-spacing-sm);\n    --padding-bottom: var(--app-spacing-sm);\n    --background: transparent;\n\n    &:last-child {\n      --border-width: 0;\n    }\n  }\n}\n\n// Custom input styles\nion-item.custom-input {\n  --background: var(--app-input-background);\n  --border-radius: var(--app-border-radius-md);\n  --padding-start: var(--app-spacing-md);\n  margin-bottom: var(--app-spacing-md);\n\n  ion-input, ion-textarea, ion-select {\n    --padding-start: 0;\n  }\n}\n\n// Logo animation\n@keyframes float {\n  0% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-5px);\n  }\n  100% {\n    transform: translateY(0px);\n  }\n}\n", "// Component Styles\n// --------------------------------------------------\n// Global styles for common components\n\n@use './mixins.scss';\n\n// Cards\n// --------------------------------------------------\nion-card {\n  border-radius: var(--app-border-radius-md);\n  box-shadow: var(--app-card-shadow) !important;\n  margin: var(--app-spacing-md);\n\n  ion-card-header {\n    padding: var(--app-spacing-md);\n\n    ion-card-title {\n      font-size: 1.25rem;\n      font-weight: var(--app-heading-font-weight);\n      color: var(--app-text-color);\n    }\n\n    ion-card-subtitle {\n      font-size: 0.9rem;\n      color: var(--ion-color-medium);\n      margin-top: var(--app-spacing-xs);\n    }\n  }\n\n  ion-card-content {\n    padding: var(--app-spacing-md);\n    color: var(--app-text-color);\n  }\n}\n\n// Buttons\n// --------------------------------------------------\nion-button {\n  --border-radius: var(--app-border-radius-md);\n  --box-shadow: none;\n  --padding-top: var(--app-spacing-sm);\n  --padding-bottom: var(--app-spacing-sm);\n  --padding-start: var(--app-spacing-lg);\n  --padding-end: var(--app-spacing-lg);\n  margin: var(--app-spacing-sm) 0;\n  font-weight: 500;\n  letter-spacing: 0.03em;\n  text-transform: none;\n\n  &.button-solid {\n    --background: var(--ion-color-primary);\n    --color: var(--ion-color-primary-contrast);\n\n    &:hover {\n      --background: var(--ion-color-primary-shade);\n    }\n  }\n\n  &.button-outline {\n    --border-color: var(--ion-color-primary);\n    --background: transparent;\n    --color: var(--ion-color-primary);\n\n    &:hover {\n      --background: rgba(var(--ion-color-primary-rgb), 0.05);\n    }\n  }\n\n  &.button-clear {\n    --color: var(--ion-color-primary);\n\n    &:hover {\n      --color: var(--ion-color-primary-shade);\n    }\n  }\n\n  ion-icon {\n    margin-right: var(--app-spacing-xs);\n  }\n}\n\n// Form elements\n// --------------------------------------------------\nion-item {\n  --padding-start: var(--app-spacing-md);\n  --padding-end: var(--app-spacing-md);\n  --padding-top: var(--app-spacing-sm);\n  --padding-bottom: var(--app-spacing-sm);\n  --inner-padding-end: 0;\n  --border-color: var(--app-border-color);\n  --background: var(--app-background-color);\n  --background-hover: rgba(var(--ion-color-primary-rgb), 0.05);\n\n  &.item-interactive {\n    --background-activated: rgba(var(--ion-color-primary-rgb), 0.1);\n  }\n}\n\nion-input, ion-textarea, ion-select {\n  --padding-start: var(--app-spacing-md);\n  --padding-end: var(--app-spacing-md);\n  --padding-top: var(--app-spacing-md);\n  --padding-bottom: var(--app-spacing-md);\n  --background: var(--app-input-background);\n  --color: var(--app-text-color);\n  --placeholder-color: var(--ion-color-medium);\n  --placeholder-opacity: 0.7;\n  border-radius: var(--app-border-radius-sm);\n  margin: var(--app-spacing-xs) 0;\n\n  &.has-focus {\n    --background: var(--app-input-background);\n    --border-color: var(--ion-color-primary);\n  }\n}\n\nion-label {\n  color: var(--app-text-color);\n  font-weight: 500;\n  margin-bottom: var(--app-spacing-xs);\n}\n\n// Lists\n// --------------------------------------------------\nion-list {\n  background: transparent;\n  padding: var(--app-spacing-sm) 0;\n\n  ion-list-header {\n    padding-left: var(--app-spacing-md);\n    padding-right: var(--app-spacing-md);\n    font-weight: 600;\n    letter-spacing: 0.03em;\n    text-transform: uppercase;\n    font-size: 0.8rem;\n    color: var(--ion-color-medium);\n  }\n\n  ion-item {\n    --padding-start: var(--app-spacing-md);\n    --padding-end: var(--app-spacing-md);\n    --inner-padding-end: var(--app-spacing-md);\n    margin-bottom: var(--app-spacing-xs);\n\n    &:last-child {\n      --border-color: transparent;\n    }\n\n    h2 {\n      font-weight: 500;\n      font-size: 1rem;\n      color: var(--app-text-color);\n    }\n\n    p {\n      font-size: 0.9rem;\n      color: var(--ion-color-medium);\n    }\n  }\n}\n\n// Toolbars\n// --------------------------------------------------\nion-toolbar {\n  --background: var(--ion-color-primary);\n  --color: var(--ion-color-primary-contrast);\n  --border-color: transparent;\n  --padding-top: var(--app-spacing-sm);\n  --padding-bottom: var(--app-spacing-sm);\n  --padding-start: var(--app-spacing-md);\n  --padding-end: var(--app-spacing-md);\n\n  ion-title {\n    font-size: 1.1rem;\n    font-weight: 500;\n  }\n\n  ion-buttons {\n    ion-button {\n      --padding-start: var(--app-spacing-sm);\n      --padding-end: var(--app-spacing-sm);\n    }\n  }\n}\n\n// Tabs\n// --------------------------------------------------\nion-tab-bar {\n  --background: var(--app-background-color);\n  --border-color: var(--app-border-color);\n  padding: var(--app-spacing-xs) 0;\n\n  ion-tab-button {\n    --color: var(--ion-color-medium);\n    --color-selected: var(--ion-color-primary);\n\n    ion-icon {\n      font-size: 1.4rem;\n    }\n\n    ion-label {\n      font-size: 0.7rem;\n      font-weight: 500;\n      margin-top: var(--app-spacing-xs);\n    }\n  }\n}\n\n// Badges\n// --------------------------------------------------\nion-badge {\n  padding: var(--app-spacing-xs) var(--app-spacing-sm);\n  border-radius: var(--app-border-radius-sm);\n  font-weight: 500;\n  font-size: 0.75rem;\n}\n\n// Segment\n// --------------------------------------------------\nion-segment {\n  --background: transparent;\n  padding: var(--app-spacing-sm);\n\n  ion-segment-button {\n    --background: transparent;\n    --background-checked: var(--ion-color-primary);\n    --background-hover: rgba(var(--ion-color-primary-rgb), 0.05);\n    --border-radius: var(--app-border-radius-md);\n    --border-color: var(--app-border-color);\n    --border-style: solid;\n    --border-width: 1px;\n    --color: var(--ion-color-medium);\n    --color-checked: var(--ion-color-primary-contrast);\n    --indicator-color: transparent;\n    min-height: 40px;\n    font-size: 0.9rem;\n    font-weight: 500;\n    text-transform: none;\n\n    &::part(indicator) {\n      display: none;\n    }\n  }\n}\n", "// Utility Classes\n// --------------------------------------------------\n// Reusable utility classes for common styling needs\n\n@use './mixins.scss';\n\n// Spacing utilities\n// --------------------------------------------------\n\n// Margin\n.m-0 { margin: 0 !important; }\n.m-xs { margin: var(--app-spacing-xs) !important; }\n.m-sm { margin: var(--app-spacing-sm) !important; }\n.m-md { margin: var(--app-spacing-md) !important; }\n.m-lg { margin: var(--app-spacing-lg) !important; }\n.m-xl { margin: var(--app-spacing-xl) !important; }\n\n// Margin top\n.mt-0 { margin-top: 0 !important; }\n.mt-xs { margin-top: var(--app-spacing-xs) !important; }\n.mt-sm { margin-top: var(--app-spacing-sm) !important; }\n.mt-md { margin-top: var(--app-spacing-md) !important; }\n.mt-lg { margin-top: var(--app-spacing-lg) !important; }\n.mt-xl { margin-top: var(--app-spacing-xl) !important; }\n\n// Margin right\n.mr-0 { margin-right: 0 !important; }\n.mr-xs { margin-right: var(--app-spacing-xs) !important; }\n.mr-sm { margin-right: var(--app-spacing-sm) !important; }\n.mr-md { margin-right: var(--app-spacing-md) !important; }\n.mr-lg { margin-right: var(--app-spacing-lg) !important; }\n.mr-xl { margin-right: var(--app-spacing-xl) !important; }\n\n// Margin bottom\n.mb-0 { margin-bottom: 0 !important; }\n.mb-xs { margin-bottom: var(--app-spacing-xs) !important; }\n.mb-sm { margin-bottom: var(--app-spacing-sm) !important; }\n.mb-md { margin-bottom: var(--app-spacing-md) !important; }\n.mb-lg { margin-bottom: var(--app-spacing-lg) !important; }\n.mb-xl { margin-bottom: var(--app-spacing-xl) !important; }\n\n// Margin left\n.ml-0 { margin-left: 0 !important; }\n.ml-xs { margin-left: var(--app-spacing-xs) !important; }\n.ml-sm { margin-left: var(--app-spacing-sm) !important; }\n.ml-md { margin-left: var(--app-spacing-md) !important; }\n.ml-lg { margin-left: var(--app-spacing-lg) !important; }\n.ml-xl { margin-left: var(--app-spacing-xl) !important; }\n\n// Margin x-axis (left and right)\n.mx-0 { margin-left: 0 !important; margin-right: 0 !important; }\n.mx-xs { margin-left: var(--app-spacing-xs) !important; margin-right: var(--app-spacing-xs) !important; }\n.mx-sm { margin-left: var(--app-spacing-sm) !important; margin-right: var(--app-spacing-sm) !important; }\n.mx-md { margin-left: var(--app-spacing-md) !important; margin-right: var(--app-spacing-md) !important; }\n.mx-lg { margin-left: var(--app-spacing-lg) !important; margin-right: var(--app-spacing-lg) !important; }\n.mx-xl { margin-left: var(--app-spacing-xl) !important; margin-right: var(--app-spacing-xl) !important; }\n.mx-auto { margin-left: auto !important; margin-right: auto !important; }\n\n// Margin y-axis (top and bottom)\n.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }\n.my-xs { margin-top: var(--app-spacing-xs) !important; margin-bottom: var(--app-spacing-xs) !important; }\n.my-sm { margin-top: var(--app-spacing-sm) !important; margin-bottom: var(--app-spacing-sm) !important; }\n.my-md { margin-top: var(--app-spacing-md) !important; margin-bottom: var(--app-spacing-md) !important; }\n.my-lg { margin-top: var(--app-spacing-lg) !important; margin-bottom: var(--app-spacing-lg) !important; }\n.my-xl { margin-top: var(--app-spacing-xl) !important; margin-bottom: var(--app-spacing-xl) !important; }\n\n// Padding\n.p-0 { padding: 0 !important; }\n.p-xs { padding: var(--app-spacing-xs) !important; }\n.p-sm { padding: var(--app-spacing-sm) !important; }\n.p-md { padding: var(--app-spacing-md) !important; }\n.p-lg { padding: var(--app-spacing-lg) !important; }\n.p-xl { padding: var(--app-spacing-xl) !important; }\n\n// Padding top\n.pt-0 { padding-top: 0 !important; }\n.pt-xs { padding-top: var(--app-spacing-xs) !important; }\n.pt-sm { padding-top: var(--app-spacing-sm) !important; }\n.pt-md { padding-top: var(--app-spacing-md) !important; }\n.pt-lg { padding-top: var(--app-spacing-lg) !important; }\n.pt-xl { padding-top: var(--app-spacing-xl) !important; }\n\n// Padding right\n.pr-0 { padding-right: 0 !important; }\n.pr-xs { padding-right: var(--app-spacing-xs) !important; }\n.pr-sm { padding-right: var(--app-spacing-sm) !important; }\n.pr-md { padding-right: var(--app-spacing-md) !important; }\n.pr-lg { padding-right: var(--app-spacing-lg) !important; }\n.pr-xl { padding-right: var(--app-spacing-xl) !important; }\n\n// Padding bottom\n.pb-0 { padding-bottom: 0 !important; }\n.pb-xs { padding-bottom: var(--app-spacing-xs) !important; }\n.pb-sm { padding-bottom: var(--app-spacing-sm) !important; }\n.pb-md { padding-bottom: var(--app-spacing-md) !important; }\n.pb-lg { padding-bottom: var(--app-spacing-lg) !important; }\n.pb-xl { padding-bottom: var(--app-spacing-xl) !important; }\n\n// Padding left\n.pl-0 { padding-left: 0 !important; }\n.pl-xs { padding-left: var(--app-spacing-xs) !important; }\n.pl-sm { padding-left: var(--app-spacing-sm) !important; }\n.pl-md { padding-left: var(--app-spacing-md) !important; }\n.pl-lg { padding-left: var(--app-spacing-lg) !important; }\n.pl-xl { padding-left: var(--app-spacing-xl) !important; }\n\n// Padding x-axis (left and right)\n.px-0 { padding-left: 0 !important; padding-right: 0 !important; }\n.px-xs { padding-left: var(--app-spacing-xs) !important; padding-right: var(--app-spacing-xs) !important; }\n.px-sm { padding-left: var(--app-spacing-sm) !important; padding-right: var(--app-spacing-sm) !important; }\n.px-md { padding-left: var(--app-spacing-md) !important; padding-right: var(--app-spacing-md) !important; }\n.px-lg { padding-left: var(--app-spacing-lg) !important; padding-right: var(--app-spacing-lg) !important; }\n.px-xl { padding-left: var(--app-spacing-xl) !important; padding-right: var(--app-spacing-xl) !important; }\n\n// Padding y-axis (top and bottom)\n.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }\n.py-xs { padding-top: var(--app-spacing-xs) !important; padding-bottom: var(--app-spacing-xs) !important; }\n.py-sm { padding-top: var(--app-spacing-sm) !important; padding-bottom: var(--app-spacing-sm) !important; }\n.py-md { padding-top: var(--app-spacing-md) !important; padding-bottom: var(--app-spacing-md) !important; }\n.py-lg { padding-top: var(--app-spacing-lg) !important; padding-bottom: var(--app-spacing-lg) !important; }\n.py-xl { padding-top: var(--app-spacing-xl) !important; padding-bottom: var(--app-spacing-xl) !important; }\n\n// Typography utilities\n// --------------------------------------------------\n\n// Text alignment\n.text-left { text-align: left !important; }\n.text-center { text-align: center !important; }\n.text-right { text-align: right !important; }\n.text-justify { text-align: justify !important; }\n\n// Text transformation\n.text-lowercase { text-transform: lowercase !important; }\n.text-uppercase { text-transform: uppercase !important; }\n.text-capitalize { text-transform: capitalize !important; }\n\n// Font weight\n.font-light { font-weight: 300 !important; }\n.font-normal { font-weight: 400 !important; }\n.font-medium { font-weight: 500 !important; }\n.font-semibold { font-weight: 600 !important; }\n.font-bold { font-weight: 700 !important; }\n\n// Font size\n.text-xs { font-size: 0.75rem !important; }\n.text-sm { font-size: 0.875rem !important; }\n.text-md { font-size: 1rem !important; }\n.text-lg { font-size: 1.125rem !important; }\n.text-xl { font-size: 1.25rem !important; }\n.text-2xl { font-size: 1.5rem !important; }\n.text-3xl { font-size: 1.875rem !important; }\n.text-4xl { font-size: 2.25rem !important; }\n\n// Text colors\n.text-primary { color: var(--ion-color-primary) !important; }\n.text-secondary { color: var(--ion-color-secondary) !important; }\n.text-tertiary { color: var(--ion-color-tertiary) !important; }\n.text-success { color: var(--ion-color-success) !important; }\n.text-warning { color: var(--ion-color-warning) !important; }\n.text-danger { color: var(--ion-color-danger) !important; }\n.text-dark { color: var(--ion-color-dark) !important; }\n.text-medium { color: var(--ion-color-medium) !important; }\n.text-light { color: var(--ion-color-light) !important; }\n\n// Display utilities\n// --------------------------------------------------\n.d-none { display: none !important; }\n.d-inline { display: inline !important; }\n.d-inline-block { display: inline-block !important; }\n.d-block { display: block !important; }\n.d-flex { display: flex !important; }\n.d-inline-flex { display: inline-flex !important; }\n\n// Flex utilities\n// --------------------------------------------------\n.flex-row { flex-direction: row !important; }\n.flex-column { flex-direction: column !important; }\n.flex-row-reverse { flex-direction: row-reverse !important; }\n.flex-column-reverse { flex-direction: column-reverse !important; }\n\n.flex-wrap { flex-wrap: wrap !important; }\n.flex-nowrap { flex-wrap: nowrap !important; }\n.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }\n\n.justify-content-start { justify-content: flex-start !important; }\n.justify-content-end { justify-content: flex-end !important; }\n.justify-content-center { justify-content: center !important; }\n.justify-content-between { justify-content: space-between !important; }\n.justify-content-around { justify-content: space-around !important; }\n.justify-content-evenly { justify-content: space-evenly !important; }\n\n.align-items-start { align-items: flex-start !important; }\n.align-items-end { align-items: flex-end !important; }\n.align-items-center { align-items: center !important; }\n.align-items-baseline { align-items: baseline !important; }\n.align-items-stretch { align-items: stretch !important; }\n\n.align-self-start { align-self: flex-start !important; }\n.align-self-end { align-self: flex-end !important; }\n.align-self-center { align-self: center !important; }\n.align-self-baseline { align-self: baseline !important; }\n.align-self-stretch { align-self: stretch !important; }\n\n.flex-grow-0 { flex-grow: 0 !important; }\n.flex-grow-1 { flex-grow: 1 !important; }\n.flex-shrink-0 { flex-shrink: 0 !important; }\n.flex-shrink-1 { flex-shrink: 1 !important; }\n\n// Position utilities\n// --------------------------------------------------\n.position-relative { position: relative !important; }\n.position-absolute { position: absolute !important; }\n.position-fixed { position: fixed !important; }\n.position-sticky { position: sticky !important; }\n\n// Border utilities\n// --------------------------------------------------\n.border { border: 1px solid var(--app-border-color) !important; }\n.border-top { border-top: 1px solid var(--app-border-color) !important; }\n.border-right { border-right: 1px solid var(--app-border-color) !important; }\n.border-bottom { border-bottom: 1px solid var(--app-border-color) !important; }\n.border-left { border-left: 1px solid var(--app-border-color) !important; }\n.border-0 { border: 0 !important; }\n\n.rounded { border-radius: var(--app-border-radius-md) !important; }\n.rounded-sm { border-radius: var(--app-border-radius-sm) !important; }\n.rounded-lg { border-radius: var(--app-border-radius-lg) !important; }\n.rounded-circle { border-radius: 50% !important; }\n.rounded-0 { border-radius: 0 !important; }\n\n// Background utilities\n// --------------------------------------------------\n.bg-primary { background-color: var(--ion-color-primary) !important; }\n.bg-secondary { background-color: var(--ion-color-secondary) !important; }\n.bg-tertiary { background-color: var(--ion-color-tertiary) !important; }\n.bg-success { background-color: var(--ion-color-success) !important; }\n.bg-warning { background-color: var(--ion-color-warning) !important; }\n.bg-danger { background-color: var(--ion-color-danger) !important; }\n.bg-dark { background-color: var(--ion-color-dark) !important; }\n.bg-medium { background-color: var(--ion-color-medium) !important; }\n.bg-light { background-color: var(--ion-color-light) !important; }\n.bg-transparent { background-color: transparent !important; }\n\n// Custom background colors\n.bg-primary-light { background-color: var(--app-primary-light) !important; }\n.bg-success-light { background-color: var(--app-success-light) !important; }\n.bg-warning-light { background-color: var(--app-warning-light) !important; }\n.bg-danger-light { background-color: var(--app-danger-light) !important; }\n", "// Ionic Variables and Theming\n// --------------------------------------------------\n// For more information on theming, visit:\n// http://ionicframework.com/docs/theming/\n\n// Ionic Colors\n// --------------------------------------------------\n// Named Color Variables\n// --------------------------------------------------\n// Named colors makes it easy to reuse colors across your app.\n// It's highly recommended to change the default colors\n// to match your app's branding.\n\n:root {\n  // Primary brand color - Green for Good Driver\n  --ion-color-primary: #2E7D32;\n  --ion-color-primary-rgb: 46, 125, 50;\n  --ion-color-primary-contrast: #ffffff;\n  --ion-color-primary-contrast-rgb: 255, 255, 255;\n  --ion-color-primary-shade: #286e2c;\n  --ion-color-primary-tint: #438a47;\n\n  // Secondary color - Darker green for contrast\n  --ion-color-secondary: #1B5E20;\n  --ion-color-secondary-rgb: 27, 94, 32;\n  --ion-color-secondary-contrast: #ffffff;\n  --ion-color-secondary-contrast-rgb: 255, 255, 255;\n  --ion-color-secondary-shade: #18531c;\n  --ion-color-secondary-tint: #326e36;\n\n  // Tertiary color\n  --ion-color-tertiary: #5260ff;\n  --ion-color-tertiary-rgb: 82, 96, 255;\n  --ion-color-tertiary-contrast: #ffffff;\n  --ion-color-tertiary-contrast-rgb: 255, 255, 255;\n  --ion-color-tertiary-shade: #4854e0;\n  --ion-color-tertiary-tint: #6370ff;\n\n  // Success color\n  --ion-color-success: #2dd36f;\n  --ion-color-success-rgb: 45, 211, 111;\n  --ion-color-success-contrast: #ffffff;\n  --ion-color-success-contrast-rgb: 255, 255, 255;\n  --ion-color-success-shade: #28ba62;\n  --ion-color-success-tint: #42d77d;\n\n  // Warning color\n  --ion-color-warning: #ffc409;\n  --ion-color-warning-rgb: 255, 196, 9;\n  --ion-color-warning-contrast: #000000;\n  --ion-color-warning-contrast-rgb: 0, 0, 0;\n  --ion-color-warning-shade: #e0ac08;\n  --ion-color-warning-tint: #ffca22;\n\n  // Danger color\n  --ion-color-danger: #eb445a;\n  --ion-color-danger-rgb: 235, 68, 90;\n  --ion-color-danger-contrast: #ffffff;\n  --ion-color-danger-contrast-rgb: 255, 255, 255;\n  --ion-color-danger-shade: #cf3c4f;\n  --ion-color-danger-tint: #ed576b;\n\n  // Dark color\n  --ion-color-dark: #222428;\n  --ion-color-dark-rgb: 34, 36, 40;\n  --ion-color-dark-contrast: #ffffff;\n  --ion-color-dark-contrast-rgb: 255, 255, 255;\n  --ion-color-dark-shade: #1e2023;\n  --ion-color-dark-tint: #383a3e;\n\n  // Medium color\n  --ion-color-medium: #92949c;\n  --ion-color-medium-rgb: 146, 148, 156;\n  --ion-color-medium-contrast: #ffffff;\n  --ion-color-medium-contrast-rgb: 255, 255, 255;\n  --ion-color-medium-shade: #808289;\n  --ion-color-medium-tint: #9d9fa6;\n\n  // Light color\n  --ion-color-light: #f4f5f8;\n  --ion-color-light-rgb: 244, 245, 248;\n  --ion-color-light-contrast: #000000;\n  --ion-color-light-contrast-rgb: 0, 0, 0;\n  --ion-color-light-shade: #d7d8da;\n  --ion-color-light-tint: #f5f6f9;\n\n  // Custom app colors\n  --app-background-color: #ffffff;\n  --app-text-color: #333333;\n  --app-border-color: #e0e0e0;\n  --app-card-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n  --app-input-background: #f9f9f9;\n  --app-success-light: #e6f7ee;\n  --app-warning-light: #fff8e6;\n  --app-danger-light: #fde8eb;\n  --app-primary-light: #e6f3ff;\n}\n\n// App specific variables\n// --------------------------------------------------\n:root {\n  // Typography\n  --app-font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;\n  --app-heading-font-weight: 500;\n  --app-body-font-weight: 400;\n\n  // Spacing\n  --app-spacing-xs: 4px;\n  --app-spacing-sm: 8px;\n  --app-spacing-md: 16px;\n  --app-spacing-lg: 24px;\n  --app-spacing-xl: 32px;\n\n  // Border radius\n  --app-border-radius-sm: 4px;\n  --app-border-radius-md: 8px;\n  --app-border-radius-lg: 12px;\n\n  // Transitions\n  --app-transition-fast: 0.15s ease;\n  --app-transition-normal: 0.25s ease;\n  --app-transition-slow: 0.4s ease;\n}\n\n// Dark theme overrides\n// --------------------------------------------------\n@media (prefers-color-scheme: dark) {\n  :root {\n    --ion-color-primary: #4CAF50;\n    --ion-color-primary-rgb: 76, 175, 80;\n    --ion-color-primary-contrast: #ffffff;\n    --ion-color-primary-contrast-rgb: 255, 255, 255;\n    --ion-color-primary-shade: #439a46;\n    --ion-color-primary-tint: #5eb762;\n\n    --ion-color-secondary: #388E3C;\n    --ion-color-secondary-rgb: 56, 142, 60;\n    --ion-color-secondary-contrast: #ffffff;\n    --ion-color-secondary-contrast-rgb: 255, 255, 255;\n    --ion-color-secondary-shade: #317d35;\n    --ion-color-secondary-tint: #4c9a50;\n\n    --ion-color-tertiary: #6a75ff;\n    --ion-color-tertiary-rgb: 106, 117, 255;\n    --ion-color-tertiary-contrast: #ffffff;\n    --ion-color-tertiary-contrast-rgb: 255, 255, 255;\n    --ion-color-tertiary-shade: #5d67e0;\n    --ion-color-tertiary-tint: #7983ff;\n\n    --ion-color-success: #2fdf75;\n    --ion-color-success-rgb: 47, 223, 117;\n    --ion-color-success-contrast: #000000;\n    --ion-color-success-contrast-rgb: 0, 0, 0;\n    --ion-color-success-shade: #29c467;\n    --ion-color-success-tint: #44e283;\n\n    --ion-color-warning: #ffd534;\n    --ion-color-warning-rgb: 255, 213, 52;\n    --ion-color-warning-contrast: #000000;\n    --ion-color-warning-contrast-rgb: 0, 0, 0;\n    --ion-color-warning-shade: #e0bb2e;\n    --ion-color-warning-tint: #ffd948;\n\n    --ion-color-danger: #ff4961;\n    --ion-color-danger-rgb: 255, 73, 97;\n    --ion-color-danger-contrast: #ffffff;\n    --ion-color-danger-contrast-rgb: 255, 255, 255;\n    --ion-color-danger-shade: #e04055;\n    --ion-color-danger-tint: #ff5b71;\n\n    --ion-color-dark: #f4f5f8;\n    --ion-color-dark-rgb: 244, 245, 248;\n    --ion-color-dark-contrast: #000000;\n    --ion-color-dark-contrast-rgb: 0, 0, 0;\n    --ion-color-dark-shade: #d7d8da;\n    --ion-color-dark-tint: #f5f6f9;\n\n    --ion-color-medium: #989aa2;\n    --ion-color-medium-rgb: 152, 154, 162;\n    --ion-color-medium-contrast: #000000;\n    --ion-color-medium-contrast-rgb: 0, 0, 0;\n    --ion-color-medium-shade: #86888f;\n    --ion-color-medium-tint: #a2a4ab;\n\n    --ion-color-light: #222428;\n    --ion-color-light-rgb: 34, 36, 40;\n    --ion-color-light-contrast: #ffffff;\n    --ion-color-light-contrast-rgb: 255, 255, 255;\n    --ion-color-light-shade: #1e2023;\n    --ion-color-light-tint: #383a3e;\n\n    // Custom app colors for dark mode\n    --app-background-color: #121212;\n    --app-text-color: #f4f4f4;\n    --app-border-color: #333333;\n    --app-card-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\n    --app-input-background: #1e1e1e;\n    --app-success-light: #1a3328;\n    --app-warning-light: #332b1a;\n    --app-danger-light: #331a22;\n    --app-primary-light: #1a2733;\n  }\n}\n"], "mappings": ";AAMA;AAOI,uBAAA;AACA;IAAA,CAAA;IAAA,EAAA;IAAA;AACA,gCAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,6BAAA;AACA,4BAAA;AALA,yBAAA;AACA;IAAA,CAAA;IAAA,EAAA;IAAA;AACA,kCAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,+BAAA;AACA,8BAAA;AALA,wBAAA;AACA;IAAA,EAAA;IAAA,EAAA;IAAA;AACA,iCAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,8BAAA;AACA,6BAAA;AALA,uBAAA;AACA;IAAA,EAAA;IAAA,GAAA;IAAA;AACA,gCAAA;AACA;IAAA,CAAA;IAAA,CAAA;IAAA;AACA,6BAAA;AACA,4BAAA;AALA,uBAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,gCAAA;AACA;IAAA,CAAA;IAAA,CAAA;IAAA;AACA,6BAAA;AACA,4BAAA;AALA,sBAAA;AACA;IAAA,GAAA;IAAA,CAAA;IAAA;AACA,+BAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,4BAAA;AACA,2BAAA;AALA,qBAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,8BAAA;AACA;IAAA,CAAA;IAAA,CAAA;IAAA;AACA,2BAAA;AACA,0BAAA;AALA,sBAAA;AACA;IAAA,EAAA;IAAA,GAAA;IAAA;AACA,+BAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,4BAAA;AACA,2BAAA;AALA,oBAAA;AACA;IAAA,EAAA;IAAA,EAAA;IAAA;AACA,6BAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,0BAAA;AACA,yBAAA;AAAA;AAOJ,IAAA,CAAA;AACE;IAAA,aAAA;IAAA,kBAAA;IAAA,gBAAA;IAAA,QAAA;IAAA;AAAA;AAEF,IAAA,CAAA;AACE;IAAA,QAAA;IAAA,gBAAA;IAAA;AAAA;AAGF;AACE,sBAAA;AACA,qBAAA,IAAA;AAAA;AAGF;AACE,cAAA,IAAA;AACA,SAAA,IAAA;AAAA;AAGF,IAAA,CAAA;AACE,YAAA;AAAA;AAYF,IAAA,CA9BA,IA8BA,SAAA,CAAA,WAAA,WAAA,WAAA;AAAA,IAAA,CA9BA,IA8BA,SAAA,CAAA,YAAA,WAAA,WAAA;AAAA,IAAA,CA9BA,IA8BA,UAAA,WAAA,WAAA;AAGE,eC/B+B;AAAA;ADuCjC,IAAA,CAzCA,IAyCA,SAAA,CAXA,WAWA,WAAA,WAAA;AAAA,IAAA,CAzCA,IAyCA,SAAA,CAXA,YAWA,WAAA,WAAA;AAEE,kBCtC+B;AAAA;AD8CjC,IAAA,CAnDA,IAmDA,UAAA;AACE,iBAAA,KAAA,IAAA,uBAAA,EAAA;AACA,gBAAA,KAAA,IAAA,sBAAA,EAAA;AAAA;AAOF,OAAA,OAAA,IAAA,CAAA,SAAA,EAAA;AACE,MAAA,CA7DF,IA6DE,SAAA,CA/BF,UA+BE;AACE,wBAAA;AAAA;AAAA;AA+BJ,SAAA,CAAA,aAAA,CAAA,WAAA,EAAA,SAAA,CAAA;AACE,sBAAA;AACA,gBAAA;AAAA;AAaF,IAAA,CA5GA,IA4GA,SAAA,CA9EA,WA8EA,CAAA;AACE,0BAAA,IAAA;AAAA;AAyBA,CAAA;AATA,oBAAA,IAAA,mBAAA,EAAA;AACA,wBAAA,IAAA,uBAAA,EAAA,CAAA,EAAA,EAAA,EAAA;AACA,wBAAA,IAAA,4BAAA,EAAA;AACA,4BAAA,IAAA,gCAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,qBAAA,IAAA,yBAAA,EAAA;AACA,oBAAA,IAAA,wBAAA,EAAA;AAAA;AAIA,CAAA;AATA,oBAAA,IAAA,qBAAA,EAAA;AACA,wBAAA,IAAA,yBAAA,EAAA,CAAA,EAAA,EAAA,EAAA;AACA,wBAAA,IAAA,8BAAA,EAAA;AACA,4BAAA,IAAA,kCAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,qBAAA,IAAA,2BAAA,EAAA;AACA,oBAAA,IAAA,0BAAA,EAAA;AAAA;AAIA,CAAA;AATA,oBAAA,IAAA,oBAAA,EAAA;AACA,wBAAA,IAAA,wBAAA,EAAA,EAAA,EAAA,EAAA,EAAA;AACA,wBAAA,IAAA,6BAAA,EAAA;AACA,4BAAA,IAAA,iCAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,qBAAA,IAAA,0BAAA,EAAA;AACA,oBAAA,IAAA,yBAAA,EAAA;AAAA;AAIA,CAAA;AATA,oBAAA,IAAA,mBAAA,EAAA;AACA,wBAAA,IAAA,uBAAA,EAAA,EAAA,EAAA,GAAA,EAAA;AACA,wBAAA,IAAA,4BAAA,EAAA;AACA,4BAAA,IAAA,gCAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,qBAAA,IAAA,yBAAA,EAAA;AACA,oBAAA,IAAA,wBAAA,EAAA;AAAA;AAIA,CAAA;AATA,oBAAA,IAAA,mBAAA,EAAA;AACA,wBAAA,IAAA,uBAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,wBAAA,IAAA,4BAAA,EAAA;AACA,4BAAA,IAAA,gCAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,qBAAA,IAAA,yBAAA,EAAA;AACA,oBAAA,IAAA,wBAAA,EAAA;AAAA;AAIA,CAAA;AATA,oBAAA,IAAA,kBAAA,EAAA;AACA,wBAAA,IAAA,sBAAA,EAAA,GAAA,EAAA,CAAA,EAAA;AACA,wBAAA,IAAA,2BAAA,EAAA;AACA,4BAAA,IAAA,+BAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,qBAAA,IAAA,wBAAA,EAAA;AACA,oBAAA,IAAA,uBAAA,EAAA;AAAA;AAIA,CAAA;AATA,oBAAA,IAAA,iBAAA,EAAA;AACA,wBAAA,IAAA,qBAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,wBAAA,IAAA,0BAAA,EAAA;AACA,4BAAA,IAAA,8BAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,qBAAA,IAAA,uBAAA,EAAA;AACA,oBAAA,IAAA,sBAAA,EAAA;AAAA;AAIA,CAAA;AATA,oBAAA,IAAA,kBAAA,EAAA;AACA,wBAAA,IAAA,sBAAA,EAAA,EAAA,EAAA,GAAA,EAAA;AACA,wBAAA,IAAA,2BAAA,EAAA;AACA,4BAAA,IAAA,+BAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,qBAAA,IAAA,wBAAA,EAAA;AACA,oBAAA,IAAA,uBAAA,EAAA;AAAA;AAIA,CAAA;AATA,oBAAA,IAAA,gBAAA,EAAA;AACA,wBAAA,IAAA,oBAAA,EAAA,EAAA,EAAA,EAAA,EAAA;AACA,wBAAA,IAAA,yBAAA,EAAA;AACA,4BAAA,IAAA,6BAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,qBAAA,IAAA,sBAAA,EAAA;AACA,oBAAA,IAAA,qBAAA,EAAA;AAAA;AAaF,CAnCA;AEwQM,QFpOuB;AEqOvB,SFrOiB;AEyPrB,OFzPkB;AE0PlB,UF1PwB;AAExB,WAAA;AACA,YAAA;AAEA,kBAAA;AACA,mBAAA;AAEA,WAAA,OAAA,KAAA;AACA,WGzH+B;AAAA;AHoIjC,UAAA,EAAA,CAxDA;AAyDE,YAAA;AAEA,WAAA,OAAA;AAEA,UAAA;AAAA;AAGF,CAAA,mBAAA,EAAA,CAhEA,QAgEA,CAAA;AACE,YAAA;AAAA;AAGF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,CAAA;AAeE,WAAA;AAAA;AAGF,CAAA;AACE,WAAA;AAAA;AAGF,CAAA,YAAA,EAAA,WAAA;AACE,WAAA;AAAA;AAOF,IAAA,CAAA,OAAA,CAAA;AAAA,IAAA,CAAA,OAAA,CAAA;AACE,2BAAA;AAAA;AAGF,UAAA,CAAA,WAAA,EAAA;AACE;AACE,yBAAA,IAAA;AAAA;AAAA;AAIJ,UAAA,CAAA,WAAA,EAAA,IAAA;AACE;AACE,yBAAA,IAAA;AACA,4BAAA,IAAA;AACA,0BAAA,IAAA;AACA,2BAAA,IAAA;AAAA;AAAA;AAQJ,QAAA,CAAA,UAAA,CAAA;AAAA,eAAA,CAAA,UAAA,CAAA;AAEE,SAAA;AAAA;AAOF,CAAA;AE6TM,aAAA,YAAA,CAAA,EAAA,CAAA,EAAA;AAAA;AFzTN,CAAA;AACE,UAAA;AACA,gBAAA;AAUA,kBAAA;AAMA,cAAA;AAAA;AAUF,CA5BA,kBA4BA;AACE,cAAA;AAAA;AAGF,CAhCA,kBAgCA,CAAA;AACE,YAAA;AAAA;AAGF,CAtRA,IAsRA,CAAA;AACE,cIvS+B,KAAA,EAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAAA;AJ0SjC,CAAA,QAAA,CA1RA,IA0RA,CAJA;AAKE,cIxS+B,IAAA,EAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAAA;AJ+SjC,CA/RA,GA+RA,CAZA;AAaE,cKtT8B,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAAA;ALyThC,CAnSA,GAmSA,CAAA;AACE,cK1T8B,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAAA;AL8ThC,mBAAA,CAAA,6BAAA,EAAA,aAAA;AACE,0BAAA;AACA,2BAAA;AAAA;AAEF,mBAAA,CAJA,6BAIA,EAAA,aAAA;AACE,6BAAA;AACA,8BAAA;AAAA;AAEF,oBAAA,EAAA,aAAA,cAAA,QAAA,CAAA;AACE,kBAAA;AAAA;AAGF,aAAA,CAAA,mBAAA,EAAA,CAAA,aAAA,CAAA;AACE,cAAA,MAAA,UAAA,aAAA,IAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AAAA;AAGF,OAAA,CAAA,sBAAA,EAAA;AACE,gBAAA,CALF;AAOI,gBAAA;AAAA;AAAA;AASJ,aAAA,CAAA,oBAAA,EAAA,CAAA,aAAA,CAhBA;AAgBA,aAAA,CAAA,mBAAA,EAAA,CAAA,aAAA,CAhBA;AAkBE,aAAA,OAAA;AAAA;AAGF,mBAAA,CAjCA,4BAiCA,CAzUA,GAyUA,EAAA,aAAA,CAAA,mBAAA,QAAA,CAAA;AACE,kBAAA;AACA,wBAAA;AAAA;AAGF,mBAAA,CAtCA,4BAsCA,CA9UA,GA8UA,EAAA,aAAA,CAVA,mBAUA;AAAA,mBAAA,CAtCA,4BAsCA,CA9UA,GA8UA,EAAA,aAAA,CAVA,kBAUA;AAEE,cAAA;AAAA;AAMF,UAAA,KAAA;AACE,cAAA;AAAA;AASF,CAAA;AACE,WAAA;AACA,YAAA;AAAA;AAUF,CAZA,4BAYA,YAAA,CAAA;AACE,SAAA;AACA,cAAA;AAAA;AAUF,CAAA;AAAA,MAAA,CAAA,cAAA;AAAA,CAAA,YAAA,CAAA,cAAA;AAAA,IAAA,CAAA,cAAA;AAAA,CAAA,UAAA,CAAA,cAAA;AAAA,EAAA,CAAA,cAAA;AAAA,CAAA,aAAA,CAAA,eAAA,CAAA,cAAA;AAOE,WAAA;AAAA;AAaF,CAAA,gBAAA,KAAA,EAAA;AACE,YAAA;AAAA;AAUF,UAAA,IAAA,SAAA,CAAA,IAAA,EAAA;AACE,GAZF;AAaI,cAAA;AAAA;AAAA;;;AM9aJ;AAAA;AAAA;AAAA;AAIE,kBAAA;AAAA;AAKF,KAAA,KAAA,CAAA;AACE,WAAA;AAEA,UAAA;AAAA;AAQF;AAAA;AAEE,eAAA;AAAA;AAOF;AACE,aAAA;AAAA;AAMF;AACE,UAAA;AAEA,gBAAA;AAEA,cAAA;AAAA;AAIF;AACE,YAAA;AAAA;AAIF;AAAA;AAAA;AAAA;AAIE,eAAA,SAAA,EAAA;AACA,aAAA;AAAA;AAgBF;AAAA;AAAA;AAAA;AAIE,eAAA;AACA,eAAA;AAAA;AAGF;AACE,YAAA;AAEA,UAAA;AAEA,QAAA;AACA,SAAA;AAAA;AAGF,QAAA;AACE,gBAAA;AAAA;AAGF;AAAA;AAAA;AAAA;AAIE,UAAA;AAEA,QAAA;AACA,SAAA;AAAA;AAQF,KAAA,KAAA,CAAA;AAAA,KAAA,CAAA;AAAA,KAAA,CAAA;AAGE,UAAA;AAEA,sBAAA;AAAA;AAIF;AAAA,EAAA;AAAA,EAAA;AAAA,EAAA;AAAA,EAAA;AAAA;AAAA,OAAA;AAAA,OAAA;AAAA,OAAA;AAAA,OAAA;AAAA,CAAA;AAAA,CAAA;AAAA,CAAA,UAAA;AAAA,CAAA,UAAA;AAAA,CAAA,UAAA;AAAA,CAAA,UAAA;AAAA;AAAA;AAkBE,gBAAA;AAAA;AAGF,EAAA;AAAA,OAAA;AAEE,kBAAA;AAAA;AAGF;AACE,WAAA;AACA,UAAA;AACA,iBAAA;AACA,eAAA;AACA,cAAA;AACA,gBAAA;AACA,eAAA;AACA,kBAAA;AACA,UAAA;AAEA,sBAAA;AAAA;AAGF,CAAA;AACE,UAAA;AAAA;AAIF,CAAA,CAAA;AAAA,MAAA,CAAA;AAAA,KAAA,KAAA,CAAA;AAGE,UAAA;AAAA;AAIF,MAAA;AAAA,KAAA;AAEE,WAAA;AAEA,UAAA;AAAA;AAMF,KAAA,CAAA,YAAA;AAAA,KAAA,CAAA,YAAA;AAEE,UAAA;AAAA;AAMF,KAAA,CAAA,YAAA;AAAA,KAAA,CAAA,YAAA;AAEE,sBAAA;AAAA;AAQF;AACE,mBAAA;AACA,kBAAA;AAAA;AAGF;AAAA;AAEE,WAAA;AAAA;;;AC1MF;AACE,cAAA;AAEA,+BAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,+BAAA;AACA,yBAAA;AAAA;AAGF;AACE,SAAA;AACA,UAAA;AACA,4BAAA;AAEA,oBAAA;AAAA;AAGF,IAAA,KAAA,CAAA,UAAA;AACE,WAAA;AAAA;AAGF,IAAA,CAAA,OAAA;AACE,WAAA;AAAA;AAGF,IAAA,CAAA;AACE,UAAA;AAAA;AAGF;AC0EE,2BAAA;AACA,0BAAA;AA0NE,eDnSc;ACoSd,gBDpSc;ACwThB,cDxTgB;ACyThB,iBDzTgB;ACmSd,gBDlSe;ACmSf,iBDnSe;ACuTjB,eDvTiB;ACwTjB,kBDxTiB;AAEjB,YAAA;AAEA,SAAA;AACA,aAAA;AACA,UAAA;AACA,cAAA;AAsBA,aAAA,WAAA;AAEA,kBAAA;AAEA,YAAA;AAEA,gBAAA;AAEA,qBAAA;AAEA,uBAAA;AAEA,aAAA;AAEA,yBAAA;AACA,4BAAA;AAEA,oBAAA;AAAA;;;AEvDF;AACE,eAAA,IAAA;AAAA;AAOF,UAAA,CAAA,qBAAA,EAAA;AACE;AAIE,UAAA,IAAA,kBAAA,EAAA,KAAA,IAAA;AAAA;AAAA;AAIJ;AACE,oBAAA;AACA,SAAA,IAAA,mBAAA,EAAA;AAAA;AAGF;AAAA;AAAA;AAAA;AAAA;AAAA;AC0SE,cDpSgB;ACqShB,iBDrS4B;AAE5B,eArD6B;AAuD7B,eApD6B;AAAA;AAuD/B;AC6RE,cD5RgB;AAEhB,aAvD6B;AAAA;AA0D/B;ACuRE,cDtRgB;AAEhB,aA1D6B;AAAA;AA6D/B;AACE,aA3D6B;AAAA;AA8D/B;AACE,aA5D6B;AAAA;AA+D/B;AACE,aA7D6B;AAAA;AAgE/B;AACE,aA9D6B;AAAA;AAiE/B;AACE,aAAA;AAAA;AAGF;AAAA;AAEE,YAAA;AAEA,aAAA;AAEA,eAAA;AAEA,kBAAA;AAAA;AAGF;AACE,OAAA;AAAA;AAGF;AACE,UAAA;AAAA;;;AE1GF,CAAA;AACE,WAAA;AAAA;AAUE,CAAA;AACE,WAAA;AAAA;AAOF,CAAA;AACE,WAAA;AAAA;ACsIF,OAAA,CAAA,SAAA,EAAA;AD/IA,GAAA;AACE,aAAA;AAAA;AAAA;ACuLF,OAAA,CAAA,SAAA,EAAA;ADhLA,GAAA;AACE,aAAA;AAAA;AAAA;ACsIF,OAAA,CAAA,SAAA,EAAA;AD/IA,GAAA;AACE,aAAA;AAAA;AAAA;ACuLF,OAAA,CAAA,SAAA,EAAA;ADhLA,GAAA;AACE,aAAA;AAAA;AAAA;ACsIF,OAAA,CAAA,SAAA,EAAA;AD/IA,GAAA;AACE,aAAA;AAAA;AAAA;ACuLF,OAAA,CAAA,SAAA,EAAA;ADhLA,GAAA;AACE,aAAA;AAAA;AAAA;ACsIF,OAAA,CAAA,SAAA,EAAA;AD/IA,GAAA;AACE,aAAA;AAAA;AAAA;ACuLF,OAAA,CAAA,SAAA,EAAA;ADhLA,GAAA;AACE,aAAA;AAAA;AAAA;;;AEZN,CAAA;AACE,mBAAA;AACA,iBAAA;AACA,iBAAA;AACA,oBAAA;ACsTE,gBDpTe;ACqTf,iBDrTe;ACyUjB,eDzUiB;AC0UjB,kBD1UiB;AAAA;AAGnB,CAAA;AACE,mBAAA,IAAA,aAAA,EAAA;AACA,iBAAA,IAAA,aAAA,EAAA;AACA,iBAAA,IAAA,aAAA,EAAA;AACA,oBAAA,IAAA,aAAA,EAAA;ACiTE,yBDpUM,IAAA,aAAA,EAAA;ACqUN,wBDrUM,IAAA,aAAA,EAAA;ACsUN,uBDtUM,IAAA,aAAA,EAAA;ACuUN,sBDvUM,IAAA,aAAA,EAAA;ACqVR,eDrVQ,IAAA,aAAA,EAAA;ACsVR,kBDtVQ,IAAA,aAAA,EAAA;AAAA;AAwBV,CAAA;AACE,iBAAA,IAAA,aAAA,EAAA;AC4TA,eDrVQ,IAAA,aAAA,EAAA;AAAA;AA8BV,CAAA;AACE,mBAAA,IAAA,aAAA,EAAA;ACqSE,yBDpUM,IAAA,aAAA,EAAA;ACqUN,wBDrUM,IAAA,aAAA,EAAA;AAAA;AAoCV,CAAA;AACE,iBAAA,IAAA,aAAA,EAAA;ACiSE,uBDtUM,IAAA,aAAA,EAAA;ACuUN,sBDvUM,IAAA,aAAA,EAAA;AAAA;AA0CV,CAAA;AACE,oBAAA,IAAA,aAAA,EAAA;AC2SA,kBDtVQ,IAAA,aAAA,EAAA;AAAA;AAgDV,CAAA;AACE,iBAAA,IAAA,aAAA,EAAA;AACA,oBAAA,IAAA,aAAA,EAAA;ACmSA,eDrVQ,IAAA,aAAA,EAAA;ACsVR,kBDtVQ,IAAA,aAAA,EAAA;AAAA;AAuDV,CAAA;AACE,mBAAA,IAAA,aAAA,EAAA;AACA,iBAAA,IAAA,aAAA,EAAA;AC2QE,yBDpUM,IAAA,aAAA,EAAA;ACqUN,wBDrUM,IAAA,aAAA,EAAA;ACsUN,uBDtUM,IAAA,aAAA,EAAA;ACuUN,sBDvUM,IAAA,aAAA,EAAA;AAAA;AAkEV,CAAA;AACE,kBAAA;AACA,gBAAA;AACA,gBAAA;AACA,mBAAA;AC0PE,eDxPc;ACyPd,gBDzPc;AC6QhB,cD7QgB;AC8QhB,iBD9QgB;AAAA;AAGlB,CAAA;AACE,kBAAA,IAAA,YAAA,EAAA;AACA,gBAAA,IAAA,YAAA,EAAA;AACA,gBAAA,IAAA,YAAA,EAAA;AACA,mBAAA,IAAA,YAAA,EAAA;ACqPE,wBDnUK,IAAA,YAAA,EAAA;ACoUL,uBDpUK,IAAA,YAAA,EAAA;ACqUL,sBDrUK,IAAA,YAAA,EAAA;ACsUL,qBDtUK,IAAA,YAAA,EAAA;ACoVP,cDpVO,IAAA,YAAA,EAAA;ACqVP,iBDrVO,IAAA,YAAA,EAAA;AAAA;AAmFT,CAAA;AACE,gBAAA,IAAA,YAAA,EAAA;ACgQA,cDpVO,IAAA,YAAA,EAAA;AAAA;AAyFT,CAAA;AACE,kBAAA,IAAA,YAAA,EAAA;ACyOE,wBDnUK,IAAA,YAAA,EAAA;ACoUL,uBDpUK,IAAA,YAAA,EAAA;AAAA;AA+FT,CAAA;AACE,gBAAA,IAAA,YAAA,EAAA;ACqOE,sBDrUK,IAAA,YAAA,EAAA;ACsUL,qBDtUK,IAAA,YAAA,EAAA;AAAA;AAqGT,CAAA;AACE,mBAAA,IAAA,YAAA,EAAA;AC+OA,iBDrVO,IAAA,YAAA,EAAA;AAAA;AA2GT,CAAA;AACE,gBAAA,IAAA,YAAA,EAAA;AACA,mBAAA,IAAA,YAAA,EAAA;ACuOA,cDpVO,IAAA,YAAA,EAAA;ACqVP,iBDrVO,IAAA,YAAA,EAAA;AAAA;AAkHT,CAAA;AACE,kBAAA,IAAA,YAAA,EAAA;AACA,gBAAA,IAAA,YAAA,EAAA;AC+ME,wBDnUK,IAAA,YAAA,EAAA;ACoUL,uBDpUK,IAAA,YAAA,EAAA;ACqUL,sBDrUK,IAAA,YAAA,EAAA;ACsUL,qBDtUK,IAAA,YAAA,EAAA;AAAA;;;AEGL,CAAA;AC2dE,SAAA;AAAA;ADvdF,CAAA;ACudE,SAAA;AAAA;ADndF,CAAA;ACqcE,SAAA;AAAA;AAzNO,cAAA,CAAA,GAAA,CAAA,MAAA,CD5OT;ACwcE,SAAA;AAAA;AArNO,CAAA,SAAA,CDnPT;ACwcE,SAAA;AAAA;AA/MJ,UAAA,SAAA,CAAA,IAAA;AAcW,GDvQT,eCuQS,KAAA;AAiMP,WAAA;AAAA;AAAA;ADpcF,CAAA;ACwcE,SAAA;AAAA;AAhOO,cAAA,CAAA,GAAA,CAAA,MAAA,CDxOT;AC2cE,SAAA;AAAA;AA5NO,CAAA,SAAA,CD/OT;AC2cE,SAAA;AAAA;AAtNJ,UAAA,SAAA,CAAA,IAAA;AAcW,GDnQT,aCmQS,KAAA;AAwMP,WAAA;AAAA;AAAA;AAnUF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AC2dE,WAAA;AAAA;ADvdF,GAAA;ACudE,WAAA;AAAA;ADndF,GAAA;ACqcE,WAAA;AAAA;AAzNO,gBAAA,CAAA,GAAA,CAAA,MAAA,CD5OT;ACwcE,WAAA;AAAA;AArNO,GAAA,SAAA,CDnPT;ACwcE,WAAA;AAAA;AA/MJ,YAAA,SAAA,CAAA,IAAA;AAcW,KDvQT,kBCuQS,KAAA;AAiMP,aAAA;AAAA;AAAA;ADpcF,GAAA;ACwcE,WAAA;AAAA;AAhOO,gBAAA,CAAA,GAAA,CAAA,MAAA,CDxOT;AC2cE,WAAA;AAAA;AA5NO,GAAA,SAAA,CD/OT;AC2cE,WAAA;AAAA;AAtNJ,YAAA,SAAA,CAAA,IAAA;AAcW,KDnQT,gBCmQS,KAAA;AAwMP,aAAA;AAAA;AAAA;AAAA;AAnUF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AC2dE,WAAA;AAAA;ADvdF,GAAA;ACudE,WAAA;AAAA;ADndF,GAAA;ACqcE,WAAA;AAAA;AAzNO,gBAAA,CAAA,GAAA,CAAA,MAAA,CD5OT;ACwcE,WAAA;AAAA;AArNO,GAAA,SAAA,CDnPT;ACwcE,WAAA;AAAA;AA/MJ,YAAA,SAAA,CAAA,IAAA;AAcW,KDvQT,kBCuQS,KAAA;AAiMP,aAAA;AAAA;AAAA;ADpcF,GAAA;ACwcE,WAAA;AAAA;AAhOO,gBAAA,CAAA,GAAA,CAAA,MAAA,CDxOT;AC2cE,WAAA;AAAA;AA5NO,GAAA,SAAA,CD/OT;AC2cE,WAAA;AAAA;AAtNJ,YAAA,SAAA,CAAA,IAAA;AAcW,KDnQT,gBCmQS,KAAA;AAwMP,aAAA;AAAA;AAAA;AAAA;AAnUF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AC2dE,WAAA;AAAA;ADvdF,GAAA;ACudE,WAAA;AAAA;ADndF,GAAA;ACqcE,WAAA;AAAA;AAzNO,gBAAA,CAAA,GAAA,CAAA,MAAA,CD5OT;ACwcE,WAAA;AAAA;AArNO,GAAA,SAAA,CDnPT;ACwcE,WAAA;AAAA;AA/MJ,YAAA,SAAA,CAAA,IAAA;AAcW,KDvQT,kBCuQS,KAAA;AAiMP,aAAA;AAAA;AAAA;ADpcF,GAAA;ACwcE,WAAA;AAAA;AAhOO,gBAAA,CAAA,GAAA,CAAA,MAAA,CDxOT;AC2cE,WAAA;AAAA;AA5NO,GAAA,SAAA,CD/OT;AC2cE,WAAA;AAAA;AAtNJ,YAAA,SAAA,CAAA,IAAA;AAcW,KDnQT,gBCmQS,KAAA;AAwMP,aAAA;AAAA;AAAA;AAAA;AAnUF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AC2dE,WAAA;AAAA;ADvdF,GAAA;ACudE,WAAA;AAAA;ADndF,GAAA;ACqcE,WAAA;AAAA;AAzNO,gBAAA,CAAA,GAAA,CAAA,MAAA,CD5OT;ACwcE,WAAA;AAAA;AArNO,GAAA,SAAA,CDnPT;ACwcE,WAAA;AAAA;AA/MJ,YAAA,SAAA,CAAA,IAAA;AAcW,KDvQT,kBCuQS,KAAA;AAiMP,aAAA;AAAA;AAAA;ADpcF,GAAA;ACwcE,WAAA;AAAA;AAhOO,gBAAA,CAAA,GAAA,CAAA,MAAA,CDxOT;AC2cE,WAAA;AAAA;AA5NO,GAAA,SAAA,CD/OT;AC2cE,WAAA;AAAA;AAtNJ,YAAA,SAAA,CAAA,IAAA;AAcW,KDnQT,gBCmQS,KAAA;AAwMP,aAAA;AAAA;AAAA;AAAA;;;ACvdF,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,eAAA;AAAA;AAGF,CAAA;AACE,eAAA;AAAA;ACuHF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,iBAAA;AAAA;AAGF,GAAA;AACE,iBAAA;AAAA;AAAA;ACuHF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,iBAAA;AAAA;AAGF,GAAA;AACE,iBAAA;AAAA;AAAA;ACuHF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,iBAAA;AAAA;AAGF,GAAA;AACE,iBAAA;AAAA;AAAA;ACuHF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,iBAAA;AAAA;AAGF,GAAA;AACE,iBAAA;AAAA;AAAA;;;AE7BF,CAAA;AAEE,kBAAA;AAAA;AAGF,CAAA;AAEE,kBAAA;AAAA;AAGF,CAAA;AAEE,kBAAA;AAAA;ACwIF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AAEE,oBAAA;AAAA;AAGF,GAAA;AAEE,oBAAA;AAAA;AAGF,GAAA;AAEE,oBAAA;AAAA;AAAA;ACwIF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AAEE,oBAAA;AAAA;AAGF,GAAA;AAEE,oBAAA;AAAA;AAGF,GAAA;AAEE,oBAAA;AAAA;AAAA;ACwIF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AAEE,oBAAA;AAAA;AAGF,GAAA;AAEE,oBAAA;AAAA;AAGF,GAAA;AAEE,oBAAA;AAAA;AAAA;ACwIF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AAEE,oBAAA;AAAA;AAGF,GAAA;AAEE,oBAAA;AAAA;AAGF,GAAA;AAEE,oBAAA;AAAA;AAAA;;;AEjBN,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,cAAA;AAAA;AAOF,CAAA;AACE,aAAA;AAAA;AAGF,CAAA;AACE,aAAA;AAAA;AAGF,CAAA;AACE,aAAA;AAAA;AAOF,CAAA;AACE,mBAAA;AAAA;AAGF,CAAA;AACE,mBAAA;AAAA;AAGF,CAAA;AACE,mBAAA;AAAA;AAGF,CAAA;AACE,mBAAA;AAAA;AAGF,CAAA;AACE,mBAAA;AAAA;AAGF,CAAA;AACE,mBAAA;AAAA;AAOF,CAAA;AACE,eAAA;AAAA;AAGF,CAAA;AACE,eAAA;AAAA;AAGF,CAAA;AACE,eAAA;AAAA;AAGF,CAAA;AACE,eAAA;AAAA;AAGF,CAAA;AACE,eAAA;AAAA;;;AC/FF,OAAA,CAAA,oBAAA,EAAA;ACqEE;AAEI,yBAAA;AACA;MAAA,EAAA;MAAA,GAAA;MAAA;AACA,kCAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,+BAAA;AACA,8BAAA;AALA,2BAAA;AACA;MAAA,EAAA;MAAA,GAAA;MAAA;AACA,oCAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,iCAAA;AACA,gCAAA;AALA,0BAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,mCAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,gCAAA;AACA,+BAAA;AALA,yBAAA;AACA;MAAA,EAAA;MAAA,GAAA;MAAA;AACA,kCAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,+BAAA;AACA,8BAAA;AALA,yBAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,kCAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,+BAAA;AACA,8BAAA;AALA,wBAAA;AACA;MAAA,GAAA;MAAA,EAAA;MAAA;AACA,iCAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,8BAAA;AACA,6BAAA;AALA,uBAAA;AACA;MAAA,EAAA;MAAA,EAAA;MAAA;AACA,gCAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,6BAAA;AACA,4BAAA;AALA,wBAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,iCAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,8BAAA;AACA,6BAAA;AALA,sBAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,+BAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,4BAAA;AACA,2BAAA;AAAA;AAMJ,OAAA,CAAA;AACE,4BAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,sBAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,oCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,8BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,2BAAA;AACA,2BAAA;AAAA;AAGF,OAAA,CA/CA,IA+CA;AACE,4BAAA,IAAA,oBAAA,EAAA,IAAA;AACA,8BAAA,IAAA,oBAAA,EAAA,IAAA;AACA,gCAAA,IAAA,oBAAA,EAAA,IAAA;AAAA;AAKF,OAAA,CAAA;AACE,4BAAA;AACA;MAAA,EAAA;MAAA,EAAA;MAAA;AACA,sBAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,oCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,8BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,2BAAA;AACA,8BAAA;AACA,8BAAA;AACA,2BAAA;AAAA;AAAA;;;AEjLJ;AACE,iBAAA,IAAA;AACA,cAAA,IAAA;AACA,UAAA,IAAA;;AAEA,SAAA;AACE,WAAA,IAAA;;AAEA,SAAA,gBAAA;AACE,aAAA;AACA,eAAA,IAAA;AACA,SAAA,IAAA;;AAGF,SAAA,gBAAA;AACE,aAAA;AACA,SAAA,IAAA;AACA,cAAA,IAAA;;AAIJ,SAAA;AACE,WAAA,IAAA;AACA,SAAA,IAAA;;AAMJ;AACE,mBAAA,IAAA;AACA,gBAAA;AACA,iBAAA,IAAA;AACA,oBAAA,IAAA;AACA,mBAAA,IAAA;AACA,iBAAA,IAAA;AACA,UAAA,IAAA,kBAAA;AACA,eAAA;AACA,kBAAA;AACA,kBAAA;;AAEA,UAAA,CAAA;AACE,gBAAA,IAAA;AACA,WAAA,IAAA;;AAEA,UAAA,CAJF,YAIE;AACE,gBAAA,IAAA;;AAIJ,UAAA,CAAA;AACE,kBAAA,IAAA;AACA,gBAAA;AACA,WAAA,IAAA;;AAEA,UAAA,CALF,cAKE;AACE,gBAAA,KAAA,IAAA,wBAAA,EAAA;;AAIJ,UAAA,CAAA;AACE,WAAA,IAAA;;AAEA,UAAA,CAHF,YAGE;AACE,WAAA,IAAA;;AAIJ,WAAA;AACE,gBAAA,IAAA;;AAMJ;AACE,mBAAA,IAAA;AACA,iBAAA,IAAA;AACA,iBAAA,IAAA;AACA,oBAAA,IAAA;AACA,uBAAA;AACA,kBAAA,IAAA;AACA,gBAAA,IAAA;AACA,sBAAA,KAAA,IAAA,wBAAA,EAAA;;AAEA,QAAA,CAAA;AACE,0BAAA,KAAA,IAAA,wBAAA,EAAA;;AAIJ;AAAA;AAAA;AACE,mBAAA,IAAA;AACA,iBAAA,IAAA;AACA,iBAAA,IAAA;AACA,oBAAA,IAAA;AACA,gBAAA,IAAA;AACA,WAAA,IAAA;AACA,uBAAA,IAAA;AACA,yBAAA;AACA,iBAAA,IAAA;AACA,UAAA,IAAA,kBAAA;;AAEA,SAAA,CAAA;AAAA,YAAA,CAAA;AAAA,UAAA,CAAA;AACE,gBAAA,IAAA;AACA,kBAAA,IAAA;;AAIJ;AACE,SAAA,IAAA;AACA,eAAA;AACA,iBAAA,IAAA;;AAKF;AACE,cAAA;AACA,WAAA,IAAA,kBAAA;;AAEA,SAAA;AACE,gBAAA,IAAA;AACA,iBAAA,IAAA;AACA,eAAA;AACA,kBAAA;AACA,kBAAA;AACA,aAAA;AACA,SAAA,IAAA;;AAGF,SAAA;AACE,mBAAA,IAAA;AACA,iBAAA,IAAA;AACA,uBAAA,IAAA;AACA,iBAAA,IAAA;;AAEA,SAAA,QAAA;AACE,kBAAA;;AAGF,SAAA,SAAA;AACE,eAAA;AACA,aAAA;AACA,SAAA,IAAA;;AAGF,SAAA,SAAA;AACE,aAAA;AACA,SAAA,IAAA;;AAON;AACE,gBAAA,IAAA;AACA,WAAA,IAAA;AACA,kBAAA;AACA,iBAAA,IAAA;AACA,oBAAA,IAAA;AACA,mBAAA,IAAA;AACA,iBAAA,IAAA;;AAEA,YAAA;AACE,aAAA;AACA,eAAA;;AAIA,YAAA,YAAA;AACE,mBAAA,IAAA;AACA,iBAAA,IAAA;;AAON;AACE,gBAAA,IAAA;AACA,kBAAA,IAAA;AACA,WAAA,IAAA,kBAAA;;AAEA,YAAA;AACE,WAAA,IAAA;AACA,oBAAA,IAAA;;AAEA,YAAA,eAAA;AACE,aAAA;;AAGF,YAAA,eAAA;AACE,aAAA;AACA,eAAA;AACA,cAAA,IAAA;;AAON;AACE,WAAA,IAAA,kBAAA,IAAA;AACA,iBAAA,IAAA;AACA,eAAA;AACA,aAAA;;AAKF;AACE,gBAAA;AACA,WAAA,IAAA;;AAEA,YAAA;AACE,gBAAA;AACA,wBAAA,IAAA;AACA,sBAAA,KAAA,IAAA,wBAAA,EAAA;AACA,mBAAA,IAAA;AACA,kBAAA,IAAA;AACA,kBAAA;AACA,kBAAA;AACA,WAAA,IAAA;AACA,mBAAA,IAAA;AACA,qBAAA;AACA,cAAA;AACA,aAAA;AACA,eAAA;AACA,kBAAA;;AAEA,YAAA,kBAAA,OAAA;AACE,WAAA;;ACtON,CAAA;AAAO,UAAA;;AACP,CAAA;AAAQ,UAAA,IAAA;;AACR,CAAA;AAAQ,UAAA,IAAA;;AACR,CAAA;AAAQ,UAAA,IAAA;;AACR,CAAA;AAAQ,UAAA,IAAA;;AACR,CAAA;AAAQ,UAAA,IAAA;;AAGR,CAAA;AAAQ,cAAA;;AACR,CAAA;AAAS,cAAA,IAAA;;AACT,CAAA;AAAS,cAAA,IAAA;;AACT,CAAA;AAAS,cAAA,IAAA;;AACT,CAAA;AAAS,cAAA,IAAA;;AACT,CAAA;AAAS,cAAA,IAAA;;AAGT,CAAA;AAAQ,gBAAA;;AACR,CAAA;AAAS,gBAAA,IAAA;;AACT,CAAA;AAAS,gBAAA,IAAA;;AACT,CAAA;AAAS,gBAAA,IAAA;;AACT,CAAA;AAAS,gBAAA,IAAA;;AACT,CAAA;AAAS,gBAAA,IAAA;;AAGT,CAAA;AAAQ,iBAAA;;AACR,CAAA;AAAS,iBAAA,IAAA;;AACT,CAAA;AAAS,iBAAA,IAAA;;AACT,CAAA;AAAS,iBAAA,IAAA;;AACT,CAAA;AAAS,iBAAA,IAAA;;AACT,CAAA;AAAS,iBAAA,IAAA;;AAGT,CAAA;AAAQ,eAAA;;AACR,CAAA;AAAS,eAAA,IAAA;;AACT,CAAA;AAAS,eAAA,IAAA;;AACT,CAAA;AAAS,eAAA,IAAA;;AACT,CAAA;AAAS,eAAA,IAAA;;AACT,CAAA;AAAS,eAAA,IAAA;;AAGT,CAAA;AAAQ,eAAA;AAA2B,gBAAA;;AACnC,CAAA;AAAS,eAAA,IAAA;AAA+C,gBAAA,IAAA;;AACxD,CAAA;AAAS,eAAA,IAAA;AAA+C,gBAAA,IAAA;;AACxD,CAAA;AAAS,eAAA,IAAA;AAA+C,gBAAA,IAAA;;AACxD,CAAA;AAAS,eAAA,IAAA;AAA+C,gBAAA,IAAA;;AACxD,CAAA;AAAS,eAAA,IAAA;AAA+C,gBAAA,IAAA;;AACxD,CAAA;AAAW,eAAA;AAA8B,gBAAA;;AAGzC,CAAA;AAAQ,cAAA;AAA0B,iBAAA;;AAClC,CAAA;AAAS,cAAA,IAAA;AAA8C,iBAAA,IAAA;;AACvD,CAAA;AAAS,cAAA,IAAA;AAA8C,iBAAA,IAAA;;AACvD,CAAA;AAAS,cAAA,IAAA;AAA8C,iBAAA,IAAA;;AACvD,CAAA;AAAS,cAAA,IAAA;AAA8C,iBAAA,IAAA;;AACvD,CAAA;AAAS,cAAA,IAAA;AAA8C,iBAAA,IAAA;;AAGvD,CAAA;AAAO,WAAA;;AACP,CAAA;AAAQ,WAAA,IAAA;;AACR,CAAA;AAAQ,WAAA,IAAA;;AACR,CAAA;AAAQ,WAAA,IAAA;;AACR,CAAA;AAAQ,WAAA,IAAA;;AACR,CAAA;AAAQ,WAAA,IAAA;;AAGR,CAAA;AAAQ,eAAA;;AACR,CAAA;AAAS,eAAA,IAAA;;AACT,CAAA;AAAS,eAAA,IAAA;;AACT,CAAA;AAAS,eAAA,IAAA;;AACT,CAAA;AAAS,eAAA,IAAA;;AACT,CAAA;AAAS,eAAA,IAAA;;AAGT,CAAA;AAAQ,iBAAA;;AACR,CAAA;AAAS,iBAAA,IAAA;;AACT,CAAA;AAAS,iBAAA,IAAA;;AACT,CAAA;AAAS,iBAAA,IAAA;;AACT,CAAA;AAAS,iBAAA,IAAA;;AACT,CAAA;AAAS,iBAAA,IAAA;;AAGT,CAAA;AAAQ,kBAAA;;AACR,CAAA;AAAS,kBAAA,IAAA;;AACT,CAAA;AAAS,kBAAA,IAAA;;AACT,CAAA;AAAS,kBAAA,IAAA;;AACT,CAAA;AAAS,kBAAA,IAAA;;AACT,CAAA;AAAS,kBAAA,IAAA;;AAGT,CAAA;AAAQ,gBAAA;;AACR,CAAA;AAAS,gBAAA,IAAA;;AACT,CAAA;AAAS,gBAAA,IAAA;;AACT,CAAA;AAAS,gBAAA,IAAA;;AACT,CAAA;AAAS,gBAAA,IAAA;;AACT,CAAA;AAAS,gBAAA,IAAA;;AAGT,CAAA;AAAQ,gBAAA;AAA4B,iBAAA;;AACpC,CAAA;AAAS,gBAAA,IAAA;AAAgD,iBAAA,IAAA;;AACzD,CAAA;AAAS,gBAAA,IAAA;AAAgD,iBAAA,IAAA;;AACzD,CAAA;AAAS,gBAAA,IAAA;AAAgD,iBAAA,IAAA;;AACzD,CAAA;AAAS,gBAAA,IAAA;AAAgD,iBAAA,IAAA;;AACzD,CAAA;AAAS,gBAAA,IAAA;AAAgD,iBAAA,IAAA;;AAGzD,CAAA;AAAQ,eAAA;AAA2B,kBAAA;;AACnC,CAAA;AAAS,eAAA,IAAA;AAA+C,kBAAA,IAAA;;AACxD,CAAA;AAAS,eAAA,IAAA;AAA+C,kBAAA,IAAA;;AACxD,CAAA;AAAS,eAAA,IAAA;AAA+C,kBAAA,IAAA;;AACxD,CAAA;AAAS,eAAA,IAAA;AAA+C,kBAAA,IAAA;;AACxD,CAAA;AAAS,eAAA,IAAA;AAA+C,kBAAA,IAAA;;AAMxD,CAAA;AAAa,cAAA;;AACb,CAAA;AAAe,cAAA;;AACf,CAAA;AAAc,cAAA;;AACd,CAAA;AAAgB,cAAA;;AAGhB,CAAA;AAAkB,kBAAA;;AAClB,CAAA;AAAkB,kBAAA;;AAClB,CAAA;AAAmB,kBAAA;;AAGnB,CAAA;AAAc,eAAA;;AACd,CAAA;AAAe,eAAA;;AACf,CAAA;AAAe,eAAA;;AACf,CAAA;AAAiB,eAAA;;AACjB,CAAA;AAAa,eAAA;;AAGb,CAAA;AAAW,aAAA;;AACX,CAAA;AAAW,aAAA;;AACX,CAAA;AAAW,aAAA;;AACX,CAAA;AAAW,aAAA;;AACX,CAAA;AAAW,aAAA;;AACX,CAAA;AAAY,aAAA;;AACZ,CAAA;AAAY,aAAA;;AACZ,CAAA;AAAY,aAAA;;AAGZ,CAAA;AAAgB,SAAA,IAAA;;AAChB,CAAA;AAAkB,SAAA,IAAA;;AAClB,CAAA;AAAiB,SAAA,IAAA;;AACjB,CAAA;AAAgB,SAAA,IAAA;;AAChB,CAAA;AAAgB,SAAA,IAAA;;AAChB,CAAA;AAAe,SAAA,IAAA;;AACf,CAAA;AAAa,SAAA,IAAA;;AACb,CAAA;AAAe,SAAA,IAAA;;AACf,CAAA;AAAc,SAAA,IAAA;;AAId,CAAA;AAAU,WAAA;;AACV,CAAA;AAAY,WAAA;;AACZ,CAAA;AAAkB,WAAA;;AAClB,CAAA;AAAW,WAAA;;AACX,CAAA;AAAU,WAAA;;AACV,CAAA;AAAiB,WAAA;;AAIjB,CAAA;AAAY,kBAAA;;AACZ,CAAA;AAAe,kBAAA;;AACf,CAAA;AAAoB,kBAAA;;AACpB,CAAA;AAAuB,kBAAA;;AAEvB,CAAA;AAAa,aAAA;;AACb,CAAA;AAAe,aAAA;;AACf,CAAA;AAAqB,aAAA;;AAErB,CAAA;AAAyB,mBAAA;;AACzB,CAAA;AAAuB,mBAAA;;AACvB,CAAA;AAA0B,mBAAA;;AAC1B,CAAA;AAA2B,mBAAA;;AAC3B,CAAA;AAA0B,mBAAA;;AAC1B,CAAA;AAA0B,mBAAA;;AAE1B,CAAA;AAAqB,eAAA;;AACrB,CAAA;AAAmB,eAAA;;AACnB,CAAA;AAAsB,eAAA;;AACtB,CAAA;AAAwB,eAAA;;AACxB,CAAA;AAAuB,eAAA;;AAEvB,CAAA;AAAoB,cAAA;;AACpB,CAAA;AAAkB,cAAA;;AAClB,CAAA;AAAqB,cAAA;;AACrB,CAAA;AAAuB,cAAA;;AACvB,CAAA;AAAsB,cAAA;;AAEtB,CAAA;AAAe,aAAA;;AACf,CAAA;AAAe,aAAA;;AACf,CAAA;AAAiB,eAAA;;AACjB,CAAA;AAAiB,eAAA;;AAIjB,CAAA;AAAqB,YAAA;;AACrB,CAAA;AAAqB,YAAA;;AACrB,CAAA;AAAkB,YAAA;;AAClB,CAAA;AAAmB,YAAA;;AAInB,CAAA;AAAU,UAAA,IAAA,MAAA,IAAA;;AACV,CAAA;AAAc,cAAA,IAAA,MAAA,IAAA;;AACd,CAAA;AAAgB,gBAAA,IAAA,MAAA,IAAA;;AAChB,CAAA;AAAiB,iBAAA,IAAA,MAAA,IAAA;;AACjB,CAAA;AAAe,eAAA,IAAA,MAAA,IAAA;;AACf,CAAA;AAAY,UAAA;;AAEZ,CAAA;AAAW,iBAAA,IAAA;;AACX,CAAA;AAAc,iBAAA,IAAA;;AACd,CAAA;AAAc,iBAAA,IAAA;;AACd,CAAA;AAAkB,iBAAA;;AAClB,CAAA;AAAa,iBAAA;;AAIb,CAAA;AAAc,oBAAA,IAAA;;AACd,CAAA;AAAgB,oBAAA,IAAA;;AAChB,CAAA;AAAe,oBAAA,IAAA;;AACf,CAAA;AAAc,oBAAA,IAAA;;AACd,CAAA;AAAc,oBAAA,IAAA;;AACd,CAAA;AAAa,oBAAA,IAAA;;AACb,CAAA;AAAW,oBAAA,IAAA;;AACX,CAAA;AAAa,oBAAA,IAAA;;AACb,CAAA;AAAY,oBAAA,IAAA;;AACZ,CAAA;AAAkB,oBAAA;;AAGlB,CAAA;AAAoB,oBAAA,IAAA;;AACpB,CAAA;AAAoB,oBAAA,IAAA;;AACpB,CAAA;AAAoB,oBAAA,IAAA;;AACpB,CAAA;AAAmB,oBAAA,IAAA;;AFhMnB;AACE,eAAA,IAAA;;AAIF;AAAA;AACE,SAAA,IAAA;;AAIF;AACE,gBAAA,IAAA;;AAIF;AACE,WAAA,IAAA,MAAA,IAAA;AACA,kBAAA;;AAIF;AACE,mBAAA;;AAIF;AACE,+BAAA,KAAA,IAAA,wBAAA,EAAA;;AAIF;AAAA;AAAA;AACE,iBAAA,IAAA;;AAIF;AACE,kBAAA;;AAIF;AACE,YAAA;AACA,iBAAA,IAAA;AACA,cAAA,IAAA;AACA,UAAA,IAAA;;AAEA,SAAA;AACE,kBAAA,IAAA;;AAEA,SAAA,gBAAA;AACE,aAAA;AACA,eAAA,IAAA;AACA,SAAA,IAAA;;AAGF,SAAA,gBAAA;AACE,aAAA;AACA,SAAA,IAAA;;AAIJ,SAAA;AACE,WAAA,IAAA;;AAKJ;AACE,cAAA;;AAEA,SAAA;AACE,mBAAA,IAAA;AACA,iBAAA,IAAA;AACA,iBAAA,IAAA;AACA,oBAAA,IAAA;AACA,gBAAA;;AAEA,SAAA,QAAA;AACE,kBAAA;;AAMN,QAAA,CAAA;AACE,gBAAA,IAAA;AACA,mBAAA,IAAA;AACA,mBAAA,IAAA;AACA,iBAAA,IAAA;;AAEA,QAAA,CANF,aAME;AAAA,QAAA,CANF,aAME;AAAA,QAAA,CANF,aAME;AACE,mBAAA;;AAKJ,WAAA;AACE;AACE,eAAA,WAAA;;AAEF;AACE,eAAA,WAAA;;AAEF;AACE,eAAA,WAAA;;;;;AGnJJ;AAEE,uBAAA;AACA;IAAA,EAAA;IAAA,GAAA;IAAA;AACA,gCAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,6BAAA;AACA,4BAAA;AAGA,yBAAA;AACA;IAAA,EAAA;IAAA,EAAA;IAAA;AACA,kCAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,+BAAA;AACA,8BAAA;AAGA,wBAAA;AACA;IAAA,EAAA;IAAA,EAAA;IAAA;AACA,iCAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,8BAAA;AACA,6BAAA;AAGA,uBAAA;AACA;IAAA,EAAA;IAAA,GAAA;IAAA;AACA,gCAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,6BAAA;AACA,4BAAA;AAGA,uBAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,gCAAA;AACA;IAAA,CAAA;IAAA,CAAA;IAAA;AACA,6BAAA;AACA,4BAAA;AAGA,sBAAA;AACA;IAAA,GAAA;IAAA,EAAA;IAAA;AACA,+BAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,4BAAA;AACA,2BAAA;AAGA,oBAAA;AACA;IAAA,EAAA;IAAA,EAAA;IAAA;AACA,6BAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,0BAAA;AACA,yBAAA;AAGA,sBAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,+BAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,4BAAA;AACA,2BAAA;AAGA,qBAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,8BAAA;AACA;IAAA,CAAA;IAAA,CAAA;IAAA;AACA,2BAAA;AACA,0BAAA;AAGA,0BAAA;AACA,oBAAA;AACA,sBAAA;AACA,qBAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,0BAAA;AACA,uBAAA;AACA,uBAAA;AACA,sBAAA;AACA,uBAAA;;AAKF;AAEE;IAAA,QAAA;IAAA,aAAA;IAAA,kBAAA;IAAA,UAAA;IAAA,SAAA;IAAA,KAAA;IAAA;AACA,6BAAA;AACA,0BAAA;AAGA,oBAAA;AACA,oBAAA;AACA,oBAAA;AACA,oBAAA;AACA,oBAAA;AAGA,0BAAA;AACA,0BAAA;AACA,0BAAA;AAGA,yBAAA,MAAA;AACA,2BAAA,MAAA;AACA,yBAAA,KAAA;;AAKF,OAAA,CAAA,oBAAA,EAAA;AACE;AACE,yBAAA;AACA;MAAA,EAAA;MAAA,GAAA;MAAA;AACA,kCAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,+BAAA;AACA,8BAAA;AAEA,2BAAA;AACA;MAAA,EAAA;MAAA,GAAA;MAAA;AACA,oCAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,iCAAA;AACA,gCAAA;AAEA,0BAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,mCAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,gCAAA;AACA,+BAAA;AAEA,yBAAA;AACA;MAAA,EAAA;MAAA,GAAA;MAAA;AACA,kCAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,+BAAA;AACA,8BAAA;AAEA,yBAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,kCAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,+BAAA;AACA,8BAAA;AAEA,wBAAA;AACA;MAAA,GAAA;MAAA,EAAA;MAAA;AACA,iCAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,8BAAA;AACA,6BAAA;AAEA,sBAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,+BAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,4BAAA;AACA,2BAAA;AAEA,wBAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,iCAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,8BAAA;AACA,6BAAA;AAEA,uBAAA;AACA;MAAA,EAAA;MAAA,EAAA;MAAA;AACA,gCAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,6BAAA;AACA,4BAAA;AAGA,4BAAA;AACA,sBAAA;AACA,wBAAA;AACA,uBAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,4BAAA;AACA,yBAAA;AACA,yBAAA;AACA,wBAAA;AACA,yBAAA;;;", "names": []}