{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-reorder_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as Host, f as getElement, c as createEvent } from './index-527b9e34.js';\nimport { j as reorderThreeOutline, k as reorderTwoSharp } from './index-e2cf2ceb.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport { f as findClosestIonContent, g as getScrollElement } from './index-9a17db3d.js';\nimport { r as raf } from './helpers-d94bc8ad.js';\nimport { a as hapticSelectionStart, b as hapticSelectionChanged, h as hapticSelectionEnd } from './haptic-ac164e4c.js';\nimport './index-cfd9c1f2.js';\nimport './capacitor-59395cbd.js';\nimport './index-a5d50daf.js';\nconst reorderIosCss = \":host([slot]){display:none;line-height:0;z-index:100}.reorder-icon{display:block}::slotted(ion-icon){font-size:dynamic-font(16px)}.reorder-icon{font-size:2.125rem;opacity:0.4}\";\nconst IonReorderIosStyle0 = reorderIosCss;\nconst reorderMdCss = \":host([slot]){display:none;line-height:0;z-index:100}.reorder-icon{display:block}::slotted(ion-icon){font-size:dynamic-font(16px)}.reorder-icon{font-size:1.9375rem;opacity:0.3}\";\nconst IonReorderMdStyle0 = reorderMdCss;\nconst Reorder = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n  }\n  onClick(ev) {\n    const reorderGroup = this.el.closest('ion-reorder-group');\n    ev.preventDefault();\n    // Only stop event propagation if the reorder is inside of an enabled\n    // reorder group. This allows interaction with clickable children components.\n    if (!reorderGroup || !reorderGroup.disabled) {\n      ev.stopImmediatePropagation();\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    const reorderIcon = mode === 'ios' ? reorderThreeOutline : reorderTwoSharp;\n    return h(Host, {\n      key: '17adf3165f4e09283d5d6434d7cd47bd23519048',\n      class: mode\n    }, h(\"slot\", {\n      key: 'd00d1cd97c689fc5c7b7175a2051cf697fe22871'\n    }, h(\"ion-icon\", {\n      key: 'eec219aebde6083de98358be3e75965c5a5dc3d0',\n      icon: reorderIcon,\n      lazy: false,\n      class: \"reorder-icon\",\n      part: \"icon\",\n      \"aria-hidden\": \"true\"\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nReorder.style = {\n  ios: IonReorderIosStyle0,\n  md: IonReorderMdStyle0\n};\nconst reorderGroupCss = \".reorder-list-active>*{display:block;-webkit-transition:-webkit-transform 300ms;transition:-webkit-transform 300ms;transition:transform 300ms;transition:transform 300ms, -webkit-transform 300ms;will-change:transform}.reorder-enabled{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.reorder-enabled ion-reorder{display:block;cursor:-webkit-grab;cursor:grab;pointer-events:all;-ms-touch-action:none;touch-action:none}.reorder-selected,.reorder-selected ion-reorder{cursor:-webkit-grabbing;cursor:grabbing}.reorder-selected{position:relative;-webkit-transition:none !important;transition:none !important;-webkit-box-shadow:0 0 10px rgba(0, 0, 0, 0.4);box-shadow:0 0 10px rgba(0, 0, 0, 0.4);opacity:0.8;z-index:100}.reorder-visible ion-reorder .reorder-icon{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}\";\nconst IonReorderGroupStyle0 = reorderGroupCss;\nconst ReorderGroup = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionItemReorder = createEvent(this, \"ionItemReorder\", 7);\n    this.lastToIndex = -1;\n    this.cachedHeights = [];\n    this.scrollElTop = 0;\n    this.scrollElBottom = 0;\n    this.scrollElInitial = 0;\n    this.containerTop = 0;\n    this.containerBottom = 0;\n    this.state = 0 /* ReorderGroupState.Idle */;\n    this.disabled = true;\n  }\n  disabledChanged() {\n    if (this.gesture) {\n      this.gesture.enable(!this.disabled);\n    }\n  }\n  async connectedCallback() {\n    const contentEl = findClosestIonContent(this.el);\n    if (contentEl) {\n      this.scrollEl = await getScrollElement(contentEl);\n    }\n    this.gesture = (await import('./index-39782642.js')).createGesture({\n      el: this.el,\n      gestureName: 'reorder',\n      gesturePriority: 110,\n      threshold: 0,\n      direction: 'y',\n      passive: false,\n      canStart: detail => this.canStart(detail),\n      onStart: ev => this.onStart(ev),\n      onMove: ev => this.onMove(ev),\n      onEnd: () => this.onEnd()\n    });\n    this.disabledChanged();\n  }\n  disconnectedCallback() {\n    this.onEnd();\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n  }\n  /**\n   * Completes the reorder operation. Must be called by the `ionItemReorder` event.\n   *\n   * If a list of items is passed, the list will be reordered and returned in the\n   * proper order.\n   *\n   * If no parameters are passed or if `true` is passed in, the reorder will complete\n   * and the item will remain in the position it was dragged to. If `false` is passed,\n   * the reorder will complete and the item will bounce back to its original position.\n   *\n   * @param listOrReorder A list of items to be sorted and returned in the new order or a\n   * boolean of whether or not the reorder should reposition the item.\n   */\n  complete(listOrReorder) {\n    return Promise.resolve(this.completeReorder(listOrReorder));\n  }\n  canStart(ev) {\n    if (this.selectedItemEl || this.state !== 0 /* ReorderGroupState.Idle */) {\n      return false;\n    }\n    const target = ev.event.target;\n    const reorderEl = target.closest('ion-reorder');\n    if (!reorderEl) {\n      return false;\n    }\n    const item = findReorderItem(reorderEl, this.el);\n    if (!item) {\n      return false;\n    }\n    ev.data = item;\n    return true;\n  }\n  onStart(ev) {\n    ev.event.preventDefault();\n    const item = this.selectedItemEl = ev.data;\n    const heights = this.cachedHeights;\n    heights.length = 0;\n    const el = this.el;\n    const children = el.children;\n    if (!children || children.length === 0) {\n      return;\n    }\n    let sum = 0;\n    for (let i = 0; i < children.length; i++) {\n      const child = children[i];\n      sum += child.offsetHeight;\n      heights.push(sum);\n      child.$ionIndex = i;\n    }\n    const box = el.getBoundingClientRect();\n    this.containerTop = box.top;\n    this.containerBottom = box.bottom;\n    if (this.scrollEl) {\n      const scrollBox = this.scrollEl.getBoundingClientRect();\n      this.scrollElInitial = this.scrollEl.scrollTop;\n      this.scrollElTop = scrollBox.top + AUTO_SCROLL_MARGIN;\n      this.scrollElBottom = scrollBox.bottom - AUTO_SCROLL_MARGIN;\n    } else {\n      this.scrollElInitial = 0;\n      this.scrollElTop = 0;\n      this.scrollElBottom = 0;\n    }\n    this.lastToIndex = indexForItem(item);\n    this.selectedItemHeight = item.offsetHeight;\n    this.state = 1 /* ReorderGroupState.Active */;\n    item.classList.add(ITEM_REORDER_SELECTED);\n    hapticSelectionStart();\n  }\n  onMove(ev) {\n    const selectedItem = this.selectedItemEl;\n    if (!selectedItem) {\n      return;\n    }\n    // Scroll if we reach the scroll margins\n    const scroll = this.autoscroll(ev.currentY);\n    // // Get coordinate\n    const top = this.containerTop - scroll;\n    const bottom = this.containerBottom - scroll;\n    const currentY = Math.max(top, Math.min(ev.currentY, bottom));\n    const deltaY = scroll + currentY - ev.startY;\n    const normalizedY = currentY - top;\n    const toIndex = this.itemIndexForTop(normalizedY);\n    if (toIndex !== this.lastToIndex) {\n      const fromIndex = indexForItem(selectedItem);\n      this.lastToIndex = toIndex;\n      hapticSelectionChanged();\n      this.reorderMove(fromIndex, toIndex);\n    }\n    // Update selected item position\n    selectedItem.style.transform = `translateY(${deltaY}px)`;\n  }\n  onEnd() {\n    const selectedItemEl = this.selectedItemEl;\n    this.state = 2 /* ReorderGroupState.Complete */;\n    if (!selectedItemEl) {\n      this.state = 0 /* ReorderGroupState.Idle */;\n      return;\n    }\n    const toIndex = this.lastToIndex;\n    const fromIndex = indexForItem(selectedItemEl);\n    if (toIndex === fromIndex) {\n      this.completeReorder();\n    } else {\n      this.ionItemReorder.emit({\n        from: fromIndex,\n        to: toIndex,\n        complete: this.completeReorder.bind(this)\n      });\n    }\n    hapticSelectionEnd();\n  }\n  completeReorder(listOrReorder) {\n    const selectedItemEl = this.selectedItemEl;\n    if (selectedItemEl && this.state === 2 /* ReorderGroupState.Complete */) {\n      const children = this.el.children;\n      const len = children.length;\n      const toIndex = this.lastToIndex;\n      const fromIndex = indexForItem(selectedItemEl);\n      /**\n       * insertBefore and setting the transform\n       * needs to happen in the same frame otherwise\n       * there will be a duplicate transition. This primarily\n       * impacts Firefox where insertBefore and transform operations\n       * are happening in two separate frames.\n       */\n      raf(() => {\n        if (toIndex !== fromIndex && (listOrReorder === undefined || listOrReorder === true)) {\n          const ref = fromIndex < toIndex ? children[toIndex + 1] : children[toIndex];\n          this.el.insertBefore(selectedItemEl, ref);\n        }\n        for (let i = 0; i < len; i++) {\n          children[i].style['transform'] = '';\n        }\n      });\n      if (Array.isArray(listOrReorder)) {\n        listOrReorder = reorderArray(listOrReorder, fromIndex, toIndex);\n      }\n      selectedItemEl.style.transition = '';\n      selectedItemEl.classList.remove(ITEM_REORDER_SELECTED);\n      this.selectedItemEl = undefined;\n      this.state = 0 /* ReorderGroupState.Idle */;\n    }\n    return listOrReorder;\n  }\n  itemIndexForTop(deltaY) {\n    const heights = this.cachedHeights;\n    for (let i = 0; i < heights.length; i++) {\n      if (heights[i] > deltaY) {\n        return i;\n      }\n    }\n    return heights.length - 1;\n  }\n  /********* DOM WRITE ********* */\n  reorderMove(fromIndex, toIndex) {\n    const itemHeight = this.selectedItemHeight;\n    const children = this.el.children;\n    for (let i = 0; i < children.length; i++) {\n      const style = children[i].style;\n      let value = '';\n      if (i > fromIndex && i <= toIndex) {\n        value = `translateY(${-itemHeight}px)`;\n      } else if (i < fromIndex && i >= toIndex) {\n        value = `translateY(${itemHeight}px)`;\n      }\n      style['transform'] = value;\n    }\n  }\n  autoscroll(posY) {\n    if (!this.scrollEl) {\n      return 0;\n    }\n    let amount = 0;\n    if (posY < this.scrollElTop) {\n      amount = -SCROLL_JUMP;\n    } else if (posY > this.scrollElBottom) {\n      amount = SCROLL_JUMP;\n    }\n    if (amount !== 0) {\n      this.scrollEl.scrollBy(0, amount);\n    }\n    return this.scrollEl.scrollTop - this.scrollElInitial;\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '6ca009dd65302a914d459aec638e62977440db20',\n      class: {\n        [mode]: true,\n        'reorder-enabled': !this.disabled,\n        'reorder-list-active': this.state !== 0 /* ReorderGroupState.Idle */\n      }\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"disabled\": [\"disabledChanged\"]\n    };\n  }\n};\nconst indexForItem = element => {\n  return element['$ionIndex'];\n};\nconst findReorderItem = (node, container) => {\n  let parent;\n  while (node) {\n    parent = node.parentElement;\n    if (parent === container) {\n      return node;\n    }\n    node = parent;\n  }\n  return undefined;\n};\nconst AUTO_SCROLL_MARGIN = 60;\nconst SCROLL_JUMP = 10;\nconst ITEM_REORDER_SELECTED = 'reorder-selected';\nconst reorderArray = (array, from, to) => {\n  const element = array[from];\n  array.splice(from, 1);\n  array.splice(to, 0, element);\n  return array.slice();\n};\nReorderGroup.style = IonReorderGroupStyle0;\nexport { Reorder as ion_reorder, ReorderGroup as ion_reorder_group };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAYM,eACA,qBACA,cACA,oBACA,SAsCA,iBACA,uBACA,cAwPA,cAGA,iBAWA,oBACA,aACA,uBACA;AAjUN;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAM,gBAAgB;AACtB,IAAM,sBAAsB;AAC5B,IAAM,eAAe;AACrB,IAAM,qBAAqB;AAC3B,IAAM,UAAU,MAAM;AAAA,MACpB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAAA,MAChC;AAAA,MACA,QAAQ,IAAI;AACV,cAAM,eAAe,KAAK,GAAG,QAAQ,mBAAmB;AACxD,WAAG,eAAe;AAGlB,YAAI,CAAC,gBAAgB,CAAC,aAAa,UAAU;AAC3C,aAAG,yBAAyB;AAAA,QAC9B;AAAA,MACF;AAAA,MACA,SAAS;AACP,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM,cAAc,SAAS,QAAQ,sBAAsB;AAC3D,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,OAAO;AAAA,QACT,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,QACP,GAAG,EAAE,YAAY;AAAA,UACf,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,eAAe;AAAA,QACjB,CAAC,CAAC,CAAC;AAAA,MACL;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,IACF;AACA,YAAQ,QAAQ;AAAA,MACd,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AACA,IAAM,kBAAkB;AACxB,IAAM,wBAAwB;AAC9B,IAAM,eAAe,MAAM;AAAA,MACzB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,aAAK,cAAc;AACnB,aAAK,gBAAgB,CAAC;AACtB,aAAK,cAAc;AACnB,aAAK,iBAAiB;AACtB,aAAK,kBAAkB;AACvB,aAAK,eAAe;AACpB,aAAK,kBAAkB;AACvB,aAAK,QAAQ;AACb,aAAK,WAAW;AAAA,MAClB;AAAA,MACA,kBAAkB;AAChB,YAAI,KAAK,SAAS;AAChB,eAAK,QAAQ,OAAO,CAAC,KAAK,QAAQ;AAAA,QACpC;AAAA,MACF;AAAA,MACM,oBAAoB;AAAA;AACxB,gBAAM,YAAY,sBAAsB,KAAK,EAAE;AAC/C,cAAI,WAAW;AACb,iBAAK,WAAW,MAAM,iBAAiB,SAAS;AAAA,UAClD;AACA,eAAK,WAAW,MAAM,OAAO,8BAAqB,GAAG,cAAc;AAAA,YACjE,IAAI,KAAK;AAAA,YACT,aAAa;AAAA,YACb,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,WAAW;AAAA,YACX,SAAS;AAAA,YACT,UAAU,YAAU,KAAK,SAAS,MAAM;AAAA,YACxC,SAAS,QAAM,KAAK,QAAQ,EAAE;AAAA,YAC9B,QAAQ,QAAM,KAAK,OAAO,EAAE;AAAA,YAC5B,OAAO,MAAM,KAAK,MAAM;AAAA,UAC1B,CAAC;AACD,eAAK,gBAAgB;AAAA,QACvB;AAAA;AAAA,MACA,uBAAuB;AACrB,aAAK,MAAM;AACX,YAAI,KAAK,SAAS;AAChB,eAAK,QAAQ,QAAQ;AACrB,eAAK,UAAU;AAAA,QACjB;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAcA,SAAS,eAAe;AACtB,eAAO,QAAQ,QAAQ,KAAK,gBAAgB,aAAa,CAAC;AAAA,MAC5D;AAAA,MACA,SAAS,IAAI;AACX,YAAI,KAAK,kBAAkB,KAAK,UAAU,GAAgC;AACxE,iBAAO;AAAA,QACT;AACA,cAAM,SAAS,GAAG,MAAM;AACxB,cAAM,YAAY,OAAO,QAAQ,aAAa;AAC9C,YAAI,CAAC,WAAW;AACd,iBAAO;AAAA,QACT;AACA,cAAM,OAAO,gBAAgB,WAAW,KAAK,EAAE;AAC/C,YAAI,CAAC,MAAM;AACT,iBAAO;AAAA,QACT;AACA,WAAG,OAAO;AACV,eAAO;AAAA,MACT;AAAA,MACA,QAAQ,IAAI;AACV,WAAG,MAAM,eAAe;AACxB,cAAM,OAAO,KAAK,iBAAiB,GAAG;AACtC,cAAM,UAAU,KAAK;AACrB,gBAAQ,SAAS;AACjB,cAAM,KAAK,KAAK;AAChB,cAAM,WAAW,GAAG;AACpB,YAAI,CAAC,YAAY,SAAS,WAAW,GAAG;AACtC;AAAA,QACF;AACA,YAAI,MAAM;AACV,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,gBAAM,QAAQ,SAAS,CAAC;AACxB,iBAAO,MAAM;AACb,kBAAQ,KAAK,GAAG;AAChB,gBAAM,YAAY;AAAA,QACpB;AACA,cAAM,MAAM,GAAG,sBAAsB;AACrC,aAAK,eAAe,IAAI;AACxB,aAAK,kBAAkB,IAAI;AAC3B,YAAI,KAAK,UAAU;AACjB,gBAAM,YAAY,KAAK,SAAS,sBAAsB;AACtD,eAAK,kBAAkB,KAAK,SAAS;AACrC,eAAK,cAAc,UAAU,MAAM;AACnC,eAAK,iBAAiB,UAAU,SAAS;AAAA,QAC3C,OAAO;AACL,eAAK,kBAAkB;AACvB,eAAK,cAAc;AACnB,eAAK,iBAAiB;AAAA,QACxB;AACA,aAAK,cAAc,aAAa,IAAI;AACpC,aAAK,qBAAqB,KAAK;AAC/B,aAAK,QAAQ;AACb,aAAK,UAAU,IAAI,qBAAqB;AACxC,6BAAqB;AAAA,MACvB;AAAA,MACA,OAAO,IAAI;AACT,cAAM,eAAe,KAAK;AAC1B,YAAI,CAAC,cAAc;AACjB;AAAA,QACF;AAEA,cAAM,SAAS,KAAK,WAAW,GAAG,QAAQ;AAE1C,cAAM,MAAM,KAAK,eAAe;AAChC,cAAM,SAAS,KAAK,kBAAkB;AACtC,cAAM,WAAW,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG,UAAU,MAAM,CAAC;AAC5D,cAAM,SAAS,SAAS,WAAW,GAAG;AACtC,cAAM,cAAc,WAAW;AAC/B,cAAM,UAAU,KAAK,gBAAgB,WAAW;AAChD,YAAI,YAAY,KAAK,aAAa;AAChC,gBAAM,YAAY,aAAa,YAAY;AAC3C,eAAK,cAAc;AACnB,iCAAuB;AACvB,eAAK,YAAY,WAAW,OAAO;AAAA,QACrC;AAEA,qBAAa,MAAM,YAAY,cAAc,MAAM;AAAA,MACrD;AAAA,MACA,QAAQ;AACN,cAAM,iBAAiB,KAAK;AAC5B,aAAK,QAAQ;AACb,YAAI,CAAC,gBAAgB;AACnB,eAAK,QAAQ;AACb;AAAA,QACF;AACA,cAAM,UAAU,KAAK;AACrB,cAAM,YAAY,aAAa,cAAc;AAC7C,YAAI,YAAY,WAAW;AACzB,eAAK,gBAAgB;AAAA,QACvB,OAAO;AACL,eAAK,eAAe,KAAK;AAAA,YACvB,MAAM;AAAA,YACN,IAAI;AAAA,YACJ,UAAU,KAAK,gBAAgB,KAAK,IAAI;AAAA,UAC1C,CAAC;AAAA,QACH;AACA,2BAAmB;AAAA,MACrB;AAAA,MACA,gBAAgB,eAAe;AAC7B,cAAM,iBAAiB,KAAK;AAC5B,YAAI,kBAAkB,KAAK,UAAU,GAAoC;AACvE,gBAAM,WAAW,KAAK,GAAG;AACzB,gBAAM,MAAM,SAAS;AACrB,gBAAM,UAAU,KAAK;AACrB,gBAAM,YAAY,aAAa,cAAc;AAQ7C,cAAI,MAAM;AACR,gBAAI,YAAY,cAAc,kBAAkB,UAAa,kBAAkB,OAAO;AACpF,oBAAM,MAAM,YAAY,UAAU,SAAS,UAAU,CAAC,IAAI,SAAS,OAAO;AAC1E,mBAAK,GAAG,aAAa,gBAAgB,GAAG;AAAA,YAC1C;AACA,qBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,uBAAS,CAAC,EAAE,MAAM,WAAW,IAAI;AAAA,YACnC;AAAA,UACF,CAAC;AACD,cAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,4BAAgB,aAAa,eAAe,WAAW,OAAO;AAAA,UAChE;AACA,yBAAe,MAAM,aAAa;AAClC,yBAAe,UAAU,OAAO,qBAAqB;AACrD,eAAK,iBAAiB;AACtB,eAAK,QAAQ;AAAA,QACf;AACA,eAAO;AAAA,MACT;AAAA,MACA,gBAAgB,QAAQ;AACtB,cAAM,UAAU,KAAK;AACrB,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAI,QAAQ,CAAC,IAAI,QAAQ;AACvB,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO,QAAQ,SAAS;AAAA,MAC1B;AAAA;AAAA,MAEA,YAAY,WAAW,SAAS;AAC9B,cAAM,aAAa,KAAK;AACxB,cAAM,WAAW,KAAK,GAAG;AACzB,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,gBAAM,QAAQ,SAAS,CAAC,EAAE;AAC1B,cAAI,QAAQ;AACZ,cAAI,IAAI,aAAa,KAAK,SAAS;AACjC,oBAAQ,cAAc,CAAC,UAAU;AAAA,UACnC,WAAW,IAAI,aAAa,KAAK,SAAS;AACxC,oBAAQ,cAAc,UAAU;AAAA,UAClC;AACA,gBAAM,WAAW,IAAI;AAAA,QACvB;AAAA,MACF;AAAA,MACA,WAAW,MAAM;AACf,YAAI,CAAC,KAAK,UAAU;AAClB,iBAAO;AAAA,QACT;AACA,YAAI,SAAS;AACb,YAAI,OAAO,KAAK,aAAa;AAC3B,mBAAS,CAAC;AAAA,QACZ,WAAW,OAAO,KAAK,gBAAgB;AACrC,mBAAS;AAAA,QACX;AACA,YAAI,WAAW,GAAG;AAChB,eAAK,SAAS,SAAS,GAAG,MAAM;AAAA,QAClC;AACA,eAAO,KAAK,SAAS,YAAY,KAAK;AAAA,MACxC;AAAA,MACA,SAAS;AACP,cAAM,OAAO,WAAW,IAAI;AAC5B,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,OAAO;AAAA,YACL,CAAC,IAAI,GAAG;AAAA,YACR,mBAAmB,CAAC,KAAK;AAAA,YACzB,uBAAuB,KAAK,UAAU;AAAA;AAAA,UACxC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,YAAY,CAAC,iBAAiB;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AACA,IAAM,eAAe,aAAW;AAC9B,aAAO,QAAQ,WAAW;AAAA,IAC5B;AACA,IAAM,kBAAkB,CAAC,MAAM,cAAc;AAC3C,UAAI;AACJ,aAAO,MAAM;AACX,iBAAS,KAAK;AACd,YAAI,WAAW,WAAW;AACxB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,IAAM,qBAAqB;AAC3B,IAAM,cAAc;AACpB,IAAM,wBAAwB;AAC9B,IAAM,eAAe,CAAC,OAAO,MAAM,OAAO;AACxC,YAAM,UAAU,MAAM,IAAI;AAC1B,YAAM,OAAO,MAAM,CAAC;AACpB,YAAM,OAAO,IAAI,GAAG,OAAO;AAC3B,aAAO,MAAM,MAAM;AAAA,IACrB;AACA,iBAAa,QAAQ;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}