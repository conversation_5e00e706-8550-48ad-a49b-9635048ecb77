import { Injectable } from '@angular/core';
import { v4 as uuidv4 } from 'uuid';
import { SessionService } from './session.service';
import { Journey } from '../models/journey.model';
import { DataStorageService } from './data-storage-service';
import { Platform } from '@ionic/angular'; // Para detecção da plataforma (dispositivo ou navegador)
import { JourneyInfo } from '../models/journeyInfo.model';
import { JourneyInfoService } from './journeyinfo.service';
import { VehicleService } from './vehicle.service';
import { NetworkService } from './network.service';
import { SyncStatus } from '../models/sync.model';
import { JourneySyncRequestDto } from '../dtos/journey/journeySyncRequestDto';
import { firstValueFrom } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class JourneyStorageService
{
    constructor(
      private sessionService: SessionService,
      private dataStorageService: DataStorageService,
      private platform: Platform,
      private journeyInfoService: JourneyInfoService,
      private vehicleService: VehicleService,
      private networkService: NetworkService,
      private http: HttpClient,
      private apiService: ApiService
    ) {
      this.isNative = this.platform.is('capacitor') || this.platform.is('cordova');
    }

  private readonly tableName = 'journeys';
  private db: any; // Referência ao banco de dados (SQLite ou IndexedDB)
  private isNative: boolean; // Variável para verificar se é dispositivo físico

  async init()
  {
    await this.dataStorageService.init();
  }

  /**
   * Starts a new journey if the user has at least one vehicle
   * @returns The journey ID if successful, null if no vehicles exist
   */
  async startJourney(): Promise<string | null> {
    const userId = await this.sessionService.getUserId();

    // Check if the user has any vehicles
    const hasVehicles = await this.vehicleService.hasVehicles(userId);
    if (!hasVehicles) {
      console.error('Cannot start journey: No vehicles registered for this user');
      return null;
    }

    // Get the primary vehicle
    const primaryVehicle = await this.vehicleService.getPrimaryVehicle(userId);
    if (!primaryVehicle) {
      console.error('Cannot start journey: No primary vehicle set');
      return null;
    }

    const id = uuidv4();
    const startDate = new Date().toISOString();

    // Determine sync status based on network connectivity
    const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingCreate : SyncStatus.PendingCreate;

    // Create the journey with the primary vehicle ID
    await this.dataStorageService.insert(this.tableName, {
      id: id,
      startDate: startDate,
      userId: userId,
      vehicleId: primaryVehicle.id,
      syncStatus: syncStatus,
      lastSyncDate: null
    });

    // Start tracking the journey
    this.journeyInfoService.startTracking(id);
    return id;
  }

  async endJourney(journeyId: string) {
    this.journeyInfoService.stopTracking(journeyId);
    const endDate = new Date().toISOString();

    // Calcular a distância total da viagem
    // Use the correct table name 'journeyInfo' instead of 'location'
    const result = await this.dataStorageService.select('journeyInfo', `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);

    // Handle the result properly for both IndexedDB and SQLite
    let infosJourney: JourneyInfo[] = [];
    if (Array.isArray(result)) {
      infosJourney = result;
    } else if (result) {
      // If result is not an array but exists, try to convert it to an array
      try {
        infosJourney = Array.isArray(result) ? result : [];
      } catch (error) {
        console.error('Error converting journey info to array:', error);
      }
    }

    let totalDistance = 0;

    if (infosJourney && infosJourney.length > 1) {
      for (let i = 0; i < infosJourney.length - 1; i++) {
        const start = infosJourney[i];
        const end = infosJourney[i + 1];
        totalDistance += this.calculateDistance(
          start.latitude, start.longitude,
          end.latitude, end.longitude
        );
      }
    }

    // Determine sync status based on network connectivity
    const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingUpdate : SyncStatus.PendingUpdate;

    // Update the journey with the calculated distance
    await this.dataStorageService.update(
      this.tableName,
      {
        endDate: endDate,
        distance: totalDistance,
        syncStatus: syncStatus,
        lastSyncDate: null
      },
      `id = '${journeyId}'`
    );
  }

  async addLocation(journeyId: string, latitude: number, longitude: number) {
    const id = uuidv4();
    const timestamp = new Date().toISOString();

    // Determine sync status based on network connectivity
    const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingCreate : SyncStatus.PendingCreate;

    // Use the correct table name 'journeyInfo' instead of 'location'
    await this.dataStorageService.insert('journeyInfo', {
      id: id,
      journeyId: journeyId,
      latitude: latitude,
      longitude: longitude,
      timestamp: timestamp,
      syncStatus: syncStatus,
      lastSyncDate: null
    });
  }

  async closeConnection() {
    // Fechar a conexão com o banco de dados se estiver usando SQLite
    if (this.db) {
      this.db.close();
      this.db = null;
     }
  }

  async getAllJourneys(): Promise<Journey[]>
  {
    let result = await this.dataStorageService.select(this.tableName, 'ORDER BY startDate DESC');

    if(!result || result.length === 0)
      return [];

    for (let i = 0; i < result.length; i++) {
      const journeyId = result[i].id;
      // Use the correct table name 'journeyInfo' instead of 'location'
      const infosJourney = await this.dataStorageService.select('journeyInfo', `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);

      // Ensure we have an array of journey info objects
      if (Array.isArray(infosJourney)) {
        result[i].infos = infosJourney.filter((info: JourneyInfo) => info.journeyId === journeyId);
      } else {
        result[i].infos = [];
      }
    }

    const journeys: Journey[] = result.map((data: any) => {
      return {
        id: data.id,
        startDate: data.startDate,
        endDate: data.endDate,
        distance: data.distance,
        userId: data.userId,
        vehicleId: data.vehicleId,
        infosJourney: data.infos,
        syncStatus: data.syncStatus || SyncStatus.Synced,
        lastSyncDate: data.lastSyncDate ? new Date(data.lastSyncDate) : undefined
      };
    });
    return journeys;
  }

  async getAllJourneysByUser(userId: string): Promise<Journey[]>
  {
    // 1. Buscar dados do usuário no Local Storage
    let result = await this.dataStorageService.select(this.tableName, ' ORDER BY startDate DESC');

    // Filter results by userId
    if (Array.isArray(result)) {
      result = result.filter((data: any) => data.userId === userId);
    } else {
      return [];
    }

    if(!result || result.length === 0)
      return [];

    for (let i = 0; i < result.length; i++) {
      const journeyId = result[i].id;
      // Use the correct table name 'journeyInfo' instead of 'location'
      const infosJourney = await this.dataStorageService.select('journeyInfo', `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);

      // Ensure we have an array of journey info objects
      if (Array.isArray(infosJourney)) {
        result[i].infos = infosJourney.filter((info: JourneyInfo) => info.journeyId === journeyId);
      } else {
        result[i].infos = [];
      }
    }

    const journeys: Journey[] = result.map((data: any) => {
      return {
        id: data.id,
        startDate: data.startDate,
        endDate: data.endDate,
        distance: data.distance,
        userId: data.userId,
        vehicleId: data.vehicleId,
        infosJourney: data.infos,
        syncStatus: data.syncStatus || SyncStatus.Synced,
        lastSyncDate: data.lastSyncDate ? new Date(data.lastSyncDate) : undefined
      };
    });
    return journeys;
  }

  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const toRad = (value: number) => (value * Math.PI) / 180;

    const R = 6371000; // Raio da Terra em metros
    const dLat = toRad(lat2 - lat1);
    const dLon = toRad(lon2 - lon1);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c; // Distância em metros
  }

  /**
   * Updates the sync status of a journey
   * @param journey The journey to update
   * @returns True if the update was successful
   */
  async updateJourneySync(journey: Journey): Promise<boolean> {
    try {
      // Convert journey to database format
      const journeyForDb = {
        ...journey,
        lastSyncDate: journey.lastSyncDate ? journey.lastSyncDate.toISOString() : null
      };

      // Remove infosJourney property as it's not stored in the journey table
      if (journeyForDb.infosJourney) {
        delete journeyForDb.infosJourney;
      }

      await this.dataStorageService.update(
        this.tableName,
        journeyForDb,
        `id = '${journey.id}'`
      );

      return true;
    } catch (error) {
      console.error('Error updating journey sync status:', error);
      return false;
    }
  }

  /**
   * Gets journeys that need to be synchronized
   * @param userId The user ID
   * @returns Array of journeys that need to be synchronized
   */
  async getPendingSyncJourneys(userId: string): Promise<Journey[]> {
    const journeys = await this.getAllJourneysByUser(userId);
    return journeys.filter(j =>
      j.syncStatus === SyncStatus.PendingCreate ||
      j.syncStatus === SyncStatus.PendingUpdate ||
      j.syncStatus === SyncStatus.PendingDelete
    );
  }

  /**
   * Sends a journey to the server for synchronization
   * @param journey The journey to send
   * @returns True if the journey was sent successfully
   */
  async sendJourneyToSync(journey: JourneySyncRequestDto): Promise<boolean> {
    try {
      const url = this.apiService.getUrl('journey/SyncJourney');
      const result = await this.http.post(url, journey).toPromise();
      return true; // Se chegou até aqui, a requisição foi bem-sucedida
    } catch (error) {
      console.error('Error sending journey to sync:', error);
      throw error;
    }
  }
}
