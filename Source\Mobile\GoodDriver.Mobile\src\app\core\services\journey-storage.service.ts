import { Injectable } from '@angular/core';
import { v4 as uuidv4 } from 'uuid';
import { SessionService } from './session.service';
import { Journey } from '../models/journey.model';
import { DataStorageService } from './data-storage-service';
import { Platform } from '@ionic/angular'; // Para detecção da plataforma (dispositivo ou navegador)
import { JourneyInfo } from '../models/journeyInfo.model';
import { JourneyInfoService } from './journeyinfo.service';
import { VehicleService } from './vehicle.service';
import { NetworkService } from './network.service';
import { SyncStatus } from '../models/sync.model';
import { JourneySyncRequestDto } from '../dtos/journey/journeySyncRequestDto';
import { firstValueFrom } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ApiService } from './api.service';
import { GeocodingService } from './geocoding.service';

@Injectable({
  providedIn: 'root'
})
export class JourneyStorageService
{
    constructor(
      private sessionService: SessionService,
      private dataStorageService: DataStorageService,
      private platform: Platform,
      private journeyInfoService: JourneyInfoService,
      private vehicleService: VehicleService,
      private networkService: NetworkService,
      private http: HttpClient,
      private apiService: ApiService,
      private geocodingService: GeocodingService
    ) {
      this.isNative = this.platform.is('capacitor') || this.platform.is('cordova');
    }

  private readonly tableName = 'journeys';
  private db: any; // Referência ao banco de dados (SQLite ou IndexedDB)
  private isNative: boolean; // Variável para verificar se é dispositivo físico

  async init()
  {
    await this.dataStorageService.init();
  }

  /**
   * Starts a new journey if the user has at least one vehicle
   * @returns The journey ID if successful, null if no vehicles exist
   */
  async startJourney(): Promise<string | null> {
    const userId = await this.sessionService.getUserId();

    // Check if the user has any vehicles
    const hasVehicles = await this.vehicleService.hasVehicles(userId);
    if (!hasVehicles) {
      console.error('Cannot start journey: No vehicles registered for this user');
      return null;
    }

    // Get the primary vehicle
    const primaryVehicle = await this.vehicleService.getPrimaryVehicle(userId);
    if (!primaryVehicle) {
      console.error('Cannot start journey: No primary vehicle set');
      return null;
    }

    const id = uuidv4();
    const startDate = new Date().toISOString();

    // Determine sync status based on network connectivity
    const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingCreate : SyncStatus.PendingCreate;

    // Create the journey with the primary vehicle ID
    await this.dataStorageService.insert(this.tableName, {
      id: id,
      startDate: startDate,
      userId: userId,
      vehicleId: primaryVehicle.id,
      syncStatus: syncStatus,
      lastSyncDate: null
    });

    // Start tracking the journey
    this.journeyInfoService.startTracking(id);
    return id;
  }

  async endJourney(journeyId: string) {
    this.journeyInfoService.stopTracking(journeyId);
    const endDate = new Date().toISOString();

    console.log(`Finalizando viagem ${journeyId}...`);

    // Calcular a distância total da viagem com filtros de precisão
    const result = await this.dataStorageService.select('journeyInfo', `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);

    // Handle the result properly for both IndexedDB and SQLite
    let infosJourney: JourneyInfo[] = [];
    if (Array.isArray(result)) {
      infosJourney = result;
    } else if (result) {
      try {
        infosJourney = Array.isArray(result) ? result : [];
      } catch (error) {
        console.error('Error converting journey info to array:', error);
      }
    }

    console.log(`Total de pontos coletados: ${infosJourney.length}`);

    // Buscar endereços para pontos que não possuem endereço
    await this.loadAndSaveAddressesForJourney(infosJourney);

    let totalDistance = 0;

    if (infosJourney && infosJourney.length > 1) {
      // Aplicar filtros para melhorar a precisão
      const filteredLocations = this.filterLocationsByAccuracy(infosJourney);
      console.log(`Pontos após filtragem: ${filteredLocations.length}`);

      // Calcular distância usando múltiplos métodos
      totalDistance = this.calculateJourneyDistance(filteredLocations);

      console.log(`Distância calculada: ${(totalDistance / 1000).toFixed(3)} km`);
    } else {
      console.log('Viagem sem pontos suficientes para calcular distância');
    }

    // Determine sync status based on network connectivity
    const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingUpdate : SyncStatus.PendingUpdate;

    // Update the journey with the calculated distance (convert to km)
    await this.dataStorageService.update(
      this.tableName,
      {
        endDate: endDate,
        distance: totalDistance / 1000, // Convert meters to kilometers
        syncStatus: syncStatus,
        lastSyncDate: null
      },
      `id = '${journeyId}'`
    );

    console.log(`Viagem ${journeyId} finalizada com ${(totalDistance / 1000).toFixed(3)} km`);
  }

  async addLocation(journeyId: string, latitude: number, longitude: number) {
    const id = uuidv4();
    const timestamp = new Date().toISOString();

    // Determine sync status based on network connectivity
    const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingCreate : SyncStatus.PendingCreate;

    // Use the correct table name 'journeyInfo' instead of 'location'
    await this.dataStorageService.insert('journeyInfo', {
      id: id,
      journeyId: journeyId,
      latitude: latitude,
      longitude: longitude,
      timestamp: timestamp,
      syncStatus: syncStatus,
      lastSyncDate: null
    });
  }

  async closeConnection() {
    // Fechar a conexão com o banco de dados se estiver usando SQLite
    if (this.db) {
      this.db.close();
      this.db = null;
     }
  }

  async getAllJourneys(): Promise<Journey[]>
  {
    let result = await this.dataStorageService.select(this.tableName, 'ORDER BY startDate DESC');

    if(!result || result.length === 0)
      return [];

    for (let i = 0; i < result.length; i++) {
      const journeyId = result[i].id;
      // Use the correct table name 'journeyInfo' instead of 'location'
      const infosJourney = await this.dataStorageService.select('journeyInfo', `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);

      // Ensure we have an array of journey info objects
      if (Array.isArray(infosJourney)) {
        result[i].infos = infosJourney.filter((info: JourneyInfo) => info.journeyId === journeyId);
      } else {
        result[i].infos = [];
      }
    }

    const journeys: Journey[] = result.map((data: any) => {
      return {
        id: data.id,
        startDate: data.startDate,
        endDate: data.endDate,
        distance: data.distance,
        userId: data.userId,
        vehicleId: data.vehicleId,
        infosJourney: data.infos,
        syncStatus: data.syncStatus || SyncStatus.Synced,
        lastSyncDate: data.lastSyncDate ? new Date(data.lastSyncDate) : undefined
      };
    });
    return journeys;
  }

  async getAllJourneysByUser(userId: string): Promise<Journey[]>
  {
    // 1. Buscar dados do usuário no Local Storage
    let result = await this.dataStorageService.select(this.tableName, ' ORDER BY startDate DESC');

    // Filter results by userId
    if (Array.isArray(result)) {
      result = result.filter((data: any) => data.userId === userId);
    } else {
      return [];
    }

    if(!result || result.length === 0)
      return [];

    for (let i = 0; i < result.length; i++) {
      const journeyId = result[i].id;
      // Use the correct table name 'journeyInfo' instead of 'location'
      const infosJourney = await this.dataStorageService.select('journeyInfo', `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);

      // Ensure we have an array of journey info objects
      if (Array.isArray(infosJourney)) {
        result[i].infos = infosJourney.filter((info: JourneyInfo) => info.journeyId === journeyId);
      } else {
        result[i].infos = [];
      }
    }

    const journeys: Journey[] = result.map((data: any) => {
      return {
        id: data.id,
        startDate: data.startDate,
        endDate: data.endDate,
        distance: data.distance,
        userId: data.userId,
        vehicleId: data.vehicleId,
        infosJourney: data.infos,
        syncStatus: data.syncStatus || SyncStatus.Synced,
        lastSyncDate: data.lastSyncDate ? new Date(data.lastSyncDate) : undefined
      };
    });
    return journeys;
  }

  /**
   * Filtra localizações por precisão e remove pontos muito próximos
   */
  private filterLocationsByAccuracy(locations: JourneyInfo[]): JourneyInfo[] {
    if (locations.length <= 2) return locations;

    const filtered: JourneyInfo[] = [];
    const MIN_DISTANCE_THRESHOLD = 10; // Mínimo de 10 metros entre pontos
    const MAX_SPEED_THRESHOLD = 200; // Máximo de 200 km/h (velocidade impossível para carros normais)

    // Sempre incluir o primeiro ponto
    filtered.push(locations[0]);

    for (let i = 1; i < locations.length; i++) {
      const current = locations[i];
      const previous = filtered[filtered.length - 1];

      // Calcular distância entre pontos
      const distance = this.calculateDistance(
        previous.latitude, previous.longitude,
        current.latitude, current.longitude
      );

      // Calcular tempo entre pontos (em horas)
      const timeDiff = (new Date(current.timestamp).getTime() - new Date(previous.timestamp).getTime()) / (1000 * 60 * 60);

      // Calcular velocidade (km/h)
      const speed = timeDiff > 0 ? (distance / 1000) / timeDiff : 0;

      // Filtros de qualidade:
      // 1. Distância mínima entre pontos (evita ruído GPS)
      // 2. Velocidade máxima realística
      // 3. Não incluir pontos com coordenadas inválidas
      // 4. Precisão GPS aceitável (se disponível)
      const hasGoodAccuracy = !current.accuracy || current.accuracy <= 30; // 30m de precisão máxima

      if (distance >= MIN_DISTANCE_THRESHOLD &&
          speed <= MAX_SPEED_THRESHOLD &&
          this.isValidCoordinate(current.latitude, current.longitude) &&
          hasGoodAccuracy) {
        filtered.push(current);
      } else {
        const reason = [];
        if (distance < MIN_DISTANCE_THRESHOLD) reason.push(`dist=${distance.toFixed(1)}m`);
        if (speed > MAX_SPEED_THRESHOLD) reason.push(`speed=${speed.toFixed(1)}km/h`);
        if (!this.isValidCoordinate(current.latitude, current.longitude)) reason.push('coords_invalid');
        if (!hasGoodAccuracy) reason.push(`accuracy=${current.accuracy}m`);
        console.log(`Ponto filtrado: ${reason.join(', ')}`);
      }
    }

    // Sempre incluir o último ponto se não foi incluído
    const lastOriginal = locations[locations.length - 1];
    const lastFiltered = filtered[filtered.length - 1];
    if (lastOriginal.timestamp !== lastFiltered.timestamp) {
      filtered.push(lastOriginal);
    }

    return filtered;
  }

  /**
   * Verifica se as coordenadas são válidas
   */
  private isValidCoordinate(lat: number, lon: number): boolean {
    return lat >= -90 && lat <= 90 && lon >= -180 && lon <= 180 && lat !== 0 && lon !== 0;
  }

  /**
   * Calcula a distância total da viagem usando múltiplos métodos
   */
  private calculateJourneyDistance(locations: JourneyInfo[]): number {
    if (locations.length < 2) return 0;

    // Método 1: Distância direta (linha reta do início ao fim)
    const directDistance = this.calculateDistance(
      locations[0].latitude, locations[0].longitude,
      locations[locations.length - 1].latitude, locations[locations.length - 1].longitude
    );

    // Método 2: Distância cumulativa (soma de todos os segmentos)
    let cumulativeDistance = 0;
    for (let i = 0; i < locations.length - 1; i++) {
      const segmentDistance = this.calculateDistance(
        locations[i].latitude, locations[i].longitude,
        locations[i + 1].latitude, locations[i + 1].longitude
      );
      cumulativeDistance += segmentDistance;
    }

    // Método 3: Distância suavizada (remove outliers)
    const smoothedDistance = this.calculateSmoothedDistance(locations);

    console.log(`Distâncias calculadas:`);
    console.log(`- Direta: ${(directDistance / 1000).toFixed(3)} km`);
    console.log(`- Cumulativa: ${(cumulativeDistance / 1000).toFixed(3)} km`);
    console.log(`- Suavizada: ${(smoothedDistance / 1000).toFixed(3)} km`);

    // Usar a distância suavizada como resultado final
    // Se a diferença entre direta e cumulativa for muito grande, usar a direta
    const ratio = cumulativeDistance / directDistance;

    if (ratio > 3.0) {
      console.log(`Ratio muito alto (${ratio.toFixed(2)}), usando distância direta`);
      return directDistance;
    } else {
      console.log(`Usando distância suavizada`);
      return smoothedDistance;
    }
  }

  /**
   * Calcula distância suavizada removendo segmentos com velocidades irreais
   */
  private calculateSmoothedDistance(locations: JourneyInfo[]): number {
    if (locations.length < 2) return 0;

    let totalDistance = 0;
    const MAX_SEGMENT_SPEED = 150; // km/h

    for (let i = 0; i < locations.length - 1; i++) {
      const current = locations[i];
      const next = locations[i + 1];

      const segmentDistance = this.calculateDistance(
        current.latitude, current.longitude,
        next.latitude, next.longitude
      );

      // Calcular tempo entre pontos (em horas)
      const timeDiff = (new Date(next.timestamp).getTime() - new Date(current.timestamp).getTime()) / (1000 * 60 * 60);
      const speed = timeDiff > 0 ? (segmentDistance / 1000) / timeDiff : 0;

      // Incluir apenas segmentos com velocidade realística
      if (speed <= MAX_SEGMENT_SPEED) {
        totalDistance += segmentDistance;
      } else {
        console.log(`Segmento ignorado: ${speed.toFixed(1)} km/h`);
      }
    }

    return totalDistance;
  }

  /**
   * Calcula distância entre dois pontos usando fórmula de Haversine
   */
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const toRad = (value: number) => (value * Math.PI) / 180;

    const R = 6371000; // Raio da Terra em metros
    const dLat = toRad(lat2 - lat1);
    const dLon = toRad(lon2 - lon1);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c; // Distância em metros
  }

  /**
   * Carrega e salva endereços para pontos da viagem que não possuem endereço
   */
  private async loadAndSaveAddressesForJourney(infosJourney: JourneyInfo[]): Promise<void> {
    if (!infosJourney || infosJourney.length === 0) return;

    console.log('Carregando endereços para pontos da viagem...');

    // Identificar pontos que precisam de endereço
    const pointsNeedingAddress = infosJourney.filter(info => !info.address);

    if (pointsNeedingAddress.length === 0) {
      console.log('Todos os pontos já possuem endereço');
      return;
    }

    console.log(`${pointsNeedingAddress.length} pontos precisam de endereço`);

    // Priorizar início e fim da viagem
    const priorityPoints: { info: JourneyInfo, index: number, priority: string }[] = [];
    const regularPoints: JourneyInfo[] = [];

    pointsNeedingAddress.forEach((info) => {
      const actualIndex = infosJourney.indexOf(info);

      if (actualIndex === 0) {
        priorityPoints.push({ info, index: actualIndex, priority: 'start' });
      } else if (actualIndex === infosJourney.length - 1 && infosJourney.length > 1) {
        priorityPoints.push({ info, index: actualIndex, priority: 'end' });
      } else {
        regularPoints.push(info);
      }
    });

    // Carregar endereços prioritários primeiro (início e fim)
    if (priorityPoints.length > 0) {
      console.log(`Carregando endereços prioritários (${priorityPoints.length} pontos)...`);

      await Promise.all(
        priorityPoints.map(async ({ info, priority }) => {
          try {
            console.log(`Carregando endereço para ${priority === 'start' ? 'início' : 'fim'} da viagem...`);
            const address = await this.geocodingService.getReverseGeocodeWithCache(info.latitude, info.longitude);

            // Atualizar o objeto na memória
            info.address = address;

            // Salvar no banco de dados
            await this.updateJourneyInfoAddress(info.id, address);

            console.log(`Endereço do ${priority === 'start' ? 'início' : 'fim'} salvo: ${address}`);
          } catch (error) {
            console.error(`Erro ao carregar endereço para ${priority}:`, error);
            info.address = 'Endereço não disponível';
            await this.updateJourneyInfoAddress(info.id, 'Endereço não disponível');
          }
        })
      );

      // Pequena pausa antes de carregar endereços intermediários
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Carregar endereços intermediários em lotes menores
    if (regularPoints.length > 0) {
      console.log(`Carregando endereços intermediários (${regularPoints.length} pontos)...`);

      const batchSize = 3; // Lotes pequenos para não sobrecarregar a API

      for (let i = 0; i < regularPoints.length; i += batchSize) {
        const batch = regularPoints.slice(i, i + batchSize);

        await Promise.all(
          batch.map(async (info) => {
            try {
              const address = await this.geocodingService.getReverseGeocodeWithCache(info.latitude, info.longitude);

              // Atualizar o objeto na memória
              info.address = address;

              // Salvar no banco de dados
              await this.updateJourneyInfoAddress(info.id, address);

              console.log(`Endereço intermediário salvo: ${address}`);
            } catch (error) {
              console.error('Erro ao carregar endereço intermediário:', error);
              info.address = 'Endereço não disponível';
              await this.updateJourneyInfoAddress(info.id, 'Endereço não disponível');
            }
          })
        );

        // Pausa entre lotes para ser respeitoso com a API
        if (i + batchSize < regularPoints.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }

    console.log('Carregamento de endereços concluído');
  }

  /**
   * Atualiza apenas o campo address de um JourneyInfo
   */
  private async updateJourneyInfoAddress(journeyInfoId: string, address: string): Promise<void> {
    try {
      await this.dataStorageService.update(
        'journeyInfo',
        { address: address },
        `id = '${journeyInfoId}'`
      );
    } catch (error) {
      console.error(`Erro ao atualizar endereço para journeyInfo ${journeyInfoId}:`, error);
    }
  }

  /**
   * Updates the sync status of a journey
   * @param journey The journey to update
   * @returns True if the update was successful
   */
  async updateJourneySync(journey: Journey): Promise<boolean> {
    try {
      // Convert journey to database format
      const journeyForDb = {
        ...journey,
        lastSyncDate: journey.lastSyncDate ? journey.lastSyncDate.toISOString() : null
      };

      // Remove infosJourney property as it's not stored in the journey table
      if (journeyForDb.infosJourney) {
        delete journeyForDb.infosJourney;
      }

      await this.dataStorageService.update(
        this.tableName,
        journeyForDb,
        `id = '${journey.id}'`
      );

      return true;
    } catch (error) {
      console.error('Error updating journey sync status:', error);
      return false;
    }
  }

  /**
   * Gets journeys that need to be synchronized
   * @param userId The user ID
   * @returns Array of journeys that need to be synchronized
   */
  async getPendingSyncJourneys(userId: string): Promise<Journey[]> {
    const journeys = await this.getAllJourneysByUser(userId);
    return journeys.filter(j =>
      j.syncStatus === SyncStatus.PendingCreate ||
      j.syncStatus === SyncStatus.PendingUpdate ||
      j.syncStatus === SyncStatus.PendingDelete
    );
  }

  /**
   * Sends a journey to the server for synchronization
   * @param journey The journey to send
   * @returns True if the journey was sent successfully
   */
  async sendJourneyToSync(journey: JourneySyncRequestDto): Promise<boolean> {
    try {
      const url = this.apiService.getUrl('journey/SyncJourney');
      await firstValueFrom(this.http.post(url, journey));
      return true; // Se chegou até aqui, a requisição foi bem-sucedida
    } catch (error) {
      console.error('Error sending journey to sync:', error);
      throw error;
    }
  }
}
