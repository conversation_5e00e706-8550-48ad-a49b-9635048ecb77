{"name": "@capacitor-community/sqlite", "version": "7.0.0", "description": "Community plugin for native & electron SQLite databases", "main": "dist/plugin.cjs.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "unpkg": "dist/plugin.js", "files": ["android/src/main/", "android/build.gradle", "dist/", "src/", "ios/Plugin/", "electron/", "CapacitorCommunitySqlite.podspec"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/capacitor-community/sqlite.git"}, "bugs": {"url": "https://github.com/capacitor-community/sqlite/issues"}, "keywords": ["capacitor", "plugin", "native", "electron", "browser", "database", "sqlite"], "scripts": {"verify": "npm run verify:ios && npm run verify:android && npm run verify:web && npm run verify:electron", "verify:ios": "cd ios && pod install && xcodebuild -workspace Plugin.xcworkspace -scheme Plugin OTHER_CFLAGS='-DHAVE_GETHOSTUUID=0' && cd ..", "verify:android": "cd android && ./gradlew clean build test && cd ..", "verify:web": "npm run build", "verify:electron": "npm run build-electron", "lint": "npm run eslint && npm run prettier -- --check && npm run swiftlint -- lint", "fmt": "npm run eslint -- --fix && npm run prettier -- --write && npm run swiftlint -- --fix --format", "eslint": "eslint . --ext ts", "prettier": "prettier \"**/*.{css,html,ts,js,java}\" --plugin=prettier-plugin-java", "swiftlint": "node-swiftlint", "docgen": "npm run docgenPlugin && npm run docgenConnection && npm run docgenDBConnection", "docgenPlugin": "docgen --api CapacitorSQLitePlugin --output-readme docs/API.md", "docgenConnection": "docgen --api ISQLiteConnection --output-readme docs/APIConnection.md", "docgenDBConnection": "docgen --api ISQLiteDBConnection --output-readme docs/APIDBConnection.md", "build": "npm run clean && npm run docgen && tsc && rollup -c rollup.config.mjs", "build-electron": "tsc --project electron/tsconfig.json && rollup -c electron/rollup.config.mjs && rimraf ./electron/build", "clean": "rimraf ./dist", "watch": "tsc --watch", "test": "echo \"No test specified\"", "prepublishOnly": "npm run build && npm run build-electron && npm run docgen", "release": "standard-version"}, "devDependencies": {"@capacitor/android": "7.0.0", "@capacitor/cli": "7.0.0", "@capacitor/core": "7.0.0", "@capacitor/docgen": "0.3.0", "@capacitor/ios": "7.0.0", "@ionic/eslint-config": "0.4.0", "@ionic/prettier-config": "4.0.0", "@ionic/swiftlint-config": "2.0.0", "@rollup/plugin-commonjs": "28.0.2", "@rollup/plugin-node-resolve": "16.0.0", "eslint": "8.57.0", "prettier": "3.4.2", "prettier-plugin-java": "2.6.6", "rimraf": "6.0.1", "rollup": "4.30.1", "standard-version": "9.5.0", "swiftlint": "2.0.0", "typescript": "4.1.5"}, "peerDependencies": {"@capacitor/core": ">=7.0.0"}, "prettier": "@ionic/prettier-config", "swiftlint": "@ionic/swiftlint-config", "eslintConfig": {"extends": "@ionic/eslint-config/recommended"}, "capacitor": {"ios": {"src": "ios"}, "android": {"src": "android"}, "electron": {"src": "electron"}}, "dependencies": {"jeep-sqlite": "^2.7.2"}}