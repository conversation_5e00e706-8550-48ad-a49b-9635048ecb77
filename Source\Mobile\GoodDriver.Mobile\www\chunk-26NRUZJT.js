import {
  __esm
} from "./chunk-4W6HR7MY.js";

// node_modules/@ionic/core/dist/esm/dir-babeabeb.js
var isRTL;
var init_dir_babeabeb = __esm({
  "node_modules/@ionic/core/dist/esm/dir-babeabeb.js"() {
    "use strict";
    isRTL = (hostEl) => {
      if (hostEl) {
        if (hostEl.dir !== "") {
          return hostEl.dir.toLowerCase() === "rtl";
        }
      }
      return (document === null || document === void 0 ? void 0 : document.dir.toLowerCase()) === "rtl";
    };
  }
});

export {
  isRTL,
  init_dir_babeabeb
};
/*! Bundled license information:

@ionic/core/dist/esm/dir-babeabeb.js:
  (*!
   * (C) Ionic http://ionicframework.com - MIT License
   *)
*/
//# sourceMappingURL=chunk-26NRUZJT.js.map
