﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Migration.Schema
{
	[FluentMigrator.Migration(20250409212100)]
	public class CreateTableDevice : FluentMigrator.Migration
	{
		public override void Down()
		{
			if (Schema.Table("Device").Exists())
			{
				Delete.Table("Device");
			}
		}

		public override void Up()
		{
			if (!Schema.Table("Device").Exists())
			{
				Create.Table("Device")
					.WithColumn("Id").AsGuid().PrimaryKey().NotNullable()
					.WithColumn("UserId").AsGuid().NotNullable().ForeignKey("FK_Device_User", "User", "Id")
					.WithColumn("Brand").AsString(50).Nullable()
					.WithColumn("Model").AsString(50).Nullable()
					.WithColumn("SystemOperational").AsString(50).Nullable()					
					.WithColumn("CreatedOn").AsDateTime().NotNullable()
					.WithColumn("UpdatedOn").AsDateTime().Nullable();
			}
		}	
	}
}
