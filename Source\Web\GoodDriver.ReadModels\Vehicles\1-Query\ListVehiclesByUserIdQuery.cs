﻿using GoodDriver.ReadModels.Brands.Result;
using GoodDriver.ReadModels.Vehicles._2_Result;
using Rogerio.Cqrs.Requests;

namespace GoodDriver.ReadModels.Vehicles._1_Query
{
    public class ListVehiclesByUserIdQuery : IRequest<ListVehiclesByUserIdResult>
    {
        public ListVehiclesByUserIdQuery(string userId)
        {
            this.UserId = userId;
        }

        public string UserId { get; set; }
    }
}
