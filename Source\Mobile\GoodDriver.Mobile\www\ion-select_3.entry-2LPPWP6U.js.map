{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-select_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, h, e as Host, f as getElement, i as forceUpdate } from './index-527b9e34.js';\nimport { c as createNotchController } from './notch-controller-1a1f7183.js';\nimport { i as isOptionSelected, c as compareOptions } from './compare-with-utils-a96ff2ea.js';\nimport { h as inheritAttributes, d as renderHiddenInput, f as focusVisibleElement } from './helpers-d94bc8ad.js';\nimport { p as printIonWarning } from './index-cfd9c1f2.js';\nimport { c as popoverController, b as actionSheetController, a as alertController, m as modalController, s as safeCall } from './overlays-d99dcb0a.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { h as hostContext, c as createColorClasses, g as getClassMap } from './theme-01f3f29c.js';\nimport { w as watchForOptions } from './watch-options-c2911ace.js';\nimport { w as chevronExpand, q as caretDownSharp } from './index-e2cf2ceb.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport './index-a5d50daf.js';\nimport './hardware-back-button-a7eb8233.js';\nimport './framework-delegate-56b467ad.js';\nimport './gesture-controller-314a54f6.js';\nconst selectIosCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0}:host(.select-disabled){pointer-events:none}:host(.has-focus) button{border:2px solid #5e9ed6}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.select-bottom{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}:host(.has-focus.ion-valid),:host(.select-expanded.ion-valid),:host(.ion-touched.ion-invalid),:host(.select-expanded.ion-touched.ion-invalid){--border-color:var(--highlight-color)}.select-bottom .error-text{display:none;color:var(--highlight-color-invalid)}.select-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .select-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .select-bottom .helper-text{display:none}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-focus.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]:last-of-type){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]:first-of-type){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host{--border-width:0.55px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--highlight-height:0px}.select-icon{width:1.125rem;height:1.125rem;color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959))}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 1.125rem - 4px)}:host(.select-disabled){opacity:0.3}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\nconst IonSelectIosStyle0 = selectIosCss;\nconst selectMdCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0}:host(.select-disabled){pointer-events:none}:host(.has-focus) button{border:2px solid #5e9ed6}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.select-bottom{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}:host(.has-focus.ion-valid),:host(.select-expanded.ion-valid),:host(.ion-touched.ion-invalid),:host(.select-expanded.ion-touched.ion-invalid){--border-color:var(--highlight-color)}.select-bottom .error-text{display:none;color:var(--highlight-color-invalid)}.select-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .select-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .select-bottom .helper-text{display:none}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-focus.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]:last-of-type){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]:first-of-type){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.select-fill-solid){--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-500, var(--ion-background-color-step-500, gray));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-solid) .select-wrapper{border-bottom:var(--border-width) var(--border-style) var(--border-color)}:host(.select-expanded.select-fill-solid.ion-valid),:host(.has-focus.select-fill-solid.ion-valid),:host(.select-fill-solid.ion-touched.ion-invalid){--border-color:var(--highlight-color)}:host(.select-fill-solid) .select-bottom{border-top:none}@media (any-hover: hover){:host(.select-fill-solid:hover){--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}:host(.select-fill-solid.select-expanded),:host(.select-fill-solid.has-focus){--background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--border-color:var(--highlight-color)}:host(.select-fill-solid) .select-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0px;border-end-start-radius:0px}:host(.label-floating.select-fill-solid) .label-text-wrapper{max-width:calc(100% / 0.75)}:host(.in-item.select-expanded.select-fill-solid) .select-wrapper .select-icon,:host(.in-item.has-focus.select-fill-solid) .select-wrapper .select-icon,:host(.in-item.has-focus.ion-valid.select-fill-solid) .select-wrapper .select-icon,:host(.in-item.ion-touched.ion-invalid.select-fill-solid) .select-wrapper .select-icon{color:var(--highlight-color)}:host(.select-fill-outline){--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-outline.select-shape-round){--border-radius:28px;--padding-start:32px;--padding-end:32px}:host(.has-focus.select-fill-outline.ion-valid),:host(.select-fill-outline.ion-touched.ion-invalid){--border-color:var(--highlight-color)}@media (any-hover: hover){:host(.select-fill-outline:hover){--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}:host(.select-fill-outline.select-expanded),:host(.select-fill-outline.has-focus){--border-width:var(--highlight-height);--border-color:var(--highlight-color)}:host(.select-fill-outline) .select-bottom{border-top:none}:host(.select-fill-outline) .select-wrapper{border-bottom:none}:host(.select-ltr.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-fill-outline.select-label-placement-floating) .label-text-wrapper{position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .label-text-wrapper{position:relative;z-index:1}:host(.label-floating.select-fill-outline) .label-text-wrapper{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}:host(.select-fill-outline.select-label-placement-stacked) select,:host(.select-fill-outline.select-label-placement-floating) select{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}:host(.select-fill-outline) .select-outline-container{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-end{pointer-events:none}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-notch,:host(.select-fill-outline) .select-outline-end{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.select-fill-outline) .select-outline-notch{max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .notch-spacer{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none}:host(.select-fill-outline) .select-outline-start{-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color)}:host(.select-fill-outline) .select-outline-start{border-start-start-radius:var(--border-radius);border-start-end-radius:0px;border-end-end-radius:0px;border-end-start-radius:var(--border-radius)}:host(.select-fill-outline) .select-outline-start{width:calc(var(--padding-start) - 4px)}:host(.select-fill-outline) .select-outline-end{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color)}:host(.select-fill-outline) .select-outline-end{border-start-start-radius:0px;border-start-end-radius:var(--border-radius);border-end-end-radius:var(--border-radius);border-end-start-radius:0px}:host(.select-fill-outline) .select-outline-end{-ms-flex-positive:1;flex-grow:1}:host(.label-floating.select-fill-outline) .select-outline-notch{border-top:none}:host(.in-item.select-expanded.select-fill-outline) .select-wrapper .select-icon,:host(.in-item.has-focus.select-fill-outline) .select-wrapper .select-icon,:host(.in-item.has-focus.ion-valid.select-fill-outline) .select-wrapper .select-icon,:host(.in-item.ion-touched.ion-invalid.select-fill-outline) .select-wrapper .select-icon{color:var(--highlight-color)}:host{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--highlight-height:2px}:host(.select-label-placement-floating.select-expanded) .label-text-wrapper,:host(.select-label-placement-floating.has-focus) .label-text-wrapper,:host(.select-label-placement-stacked.select-expanded) .label-text-wrapper,:host(.select-label-placement-stacked.has-focus) .label-text-wrapper{color:var(--highlight-color)}:host(.has-focus.select-label-placement-floating.ion-valid) .label-text-wrapper,:host(.select-label-placement-floating.ion-touched.ion-invalid) .label-text-wrapper,:host(.has-focus.select-label-placement-stacked.ion-valid) .label-text-wrapper,:host(.select-label-placement-stacked.ion-touched.ion-invalid) .label-text-wrapper{color:var(--highlight-color)}.select-highlight{bottom:-1px;position:absolute;width:100%;height:var(--highlight-height);-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}.select-highlight{inset-inline-start:0}:host(.select-expanded) .select-highlight,:host(.has-focus) .select-highlight{-webkit-transform:scale(1);transform:scale(1)}:host(.in-item) .select-highlight{bottom:0}:host(.in-item) .select-highlight{inset-inline-start:0}.select-icon{width:0.8125rem;-webkit-transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray))}:host(.select-expanded:not(.has-expanded-icon)) .select-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.in-item.select-expanded) .select-wrapper .select-icon,:host(.in-item.has-focus) .select-wrapper .select-icon,:host(.in-item.has-focus.ion-valid) .select-wrapper .select-icon,:host(.in-item.ion-touched.ion-invalid) .select-wrapper .select-icon{color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray))}:host(.select-expanded) .select-wrapper .select-icon,:host(.has-focus.ion-valid) .select-wrapper .select-icon,:host(.ion-touched.ion-invalid) .select-wrapper .select-icon,:host(.has-focus) .select-wrapper .select-icon{color:var(--highlight-color)}:host(.select-shape-round){--border-radius:16px}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 0.8125rem - 4px)}:host(.select-disabled){opacity:0.38}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\nconst IonSelectMdStyle0 = selectMdCss;\nconst Select = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionCancel = createEvent(this, \"ionCancel\", 7);\n    this.ionDismiss = createEvent(this, \"ionDismiss\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.inputId = `ion-sel-${selectIds++}`;\n    this.helperTextId = `${this.inputId}-helper-text`;\n    this.errorTextId = `${this.inputId}-error-text`;\n    this.inheritedAttributes = {};\n    this.onClick = ev => {\n      const target = ev.target;\n      const closestSlot = target.closest('[slot=\"start\"], [slot=\"end\"]');\n      if (target === this.el || closestSlot === null) {\n        this.setFocus();\n        this.open(ev);\n      } else {\n        /**\n         * Prevent clicks to the start/end slots from opening the select.\n         * We ensure the target isn't this element in case the select is slotted\n         * in, for example, an item. This would prevent the select from ever\n         * being opened since the element itself has slot=\"start\"/\"end\".\n         *\n         * Clicking a slotted element also causes a click\n         * on the <label> element (since it wraps the slots).\n         * Clicking <label> dispatches another click event on\n         * the native form control that then bubbles up to this\n         * listener. This additional event targets the host\n         * element, so the select overlay is opened.\n         *\n         * When the slotted elements are clicked (and therefore\n         * the ancestor <label> element) we want to prevent the label\n         * from dispatching another click event.\n         *\n         * Do not call stopPropagation() because this will cause\n         * click handlers on the slotted elements to never fire in React.\n         * When developers do onClick in React a native \"click\" listener\n         * is added on the root element, not the slotted element. When that\n         * native click listener fires, React then dispatches the synthetic\n         * click event on the slotted element. However, if stopPropagation\n         * is called then the native click event will never bubble up\n         * to the root element.\n         */\n        ev.preventDefault();\n      }\n    };\n    this.onFocus = () => {\n      this.hasFocus = true;\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.hasFocus = false;\n      this.ionBlur.emit();\n    };\n    /**\n     * Stops propagation when the label is clicked,\n     * otherwise, two clicks will be triggered.\n     */\n    this.onLabelClick = ev => {\n      // Only stop propagation if the click was directly on the label\n      // and not on the input or other child elements\n      if (ev.target === ev.currentTarget) {\n        ev.stopPropagation();\n      }\n    };\n    this.isExpanded = false;\n    this.hasFocus = false;\n    this.cancelText = 'Cancel';\n    this.color = undefined;\n    this.compareWith = undefined;\n    this.disabled = false;\n    this.fill = undefined;\n    this.errorText = undefined;\n    this.helperText = undefined;\n    this.interface = 'alert';\n    this.interfaceOptions = {};\n    this.justify = undefined;\n    this.label = undefined;\n    this.labelPlacement = 'start';\n    this.multiple = false;\n    this.name = this.inputId;\n    this.okText = 'OK';\n    this.placeholder = undefined;\n    this.selectedText = undefined;\n    this.toggleIcon = undefined;\n    this.expandedIcon = undefined;\n    this.shape = undefined;\n    this.value = undefined;\n    this.required = false;\n  }\n  styleChanged() {\n    this.emitStyle();\n  }\n  setValue(value) {\n    this.value = value;\n    this.ionChange.emit({\n      value\n    });\n  }\n  async connectedCallback() {\n    const {\n      el\n    } = this;\n    this.notchController = createNotchController(el, () => this.notchSpacerEl, () => this.labelSlot);\n    this.updateOverlayOptions();\n    this.emitStyle();\n    this.mutationO = watchForOptions(this.el, 'ion-select-option', async () => {\n      this.updateOverlayOptions();\n      /**\n       * We need to re-render the component\n       * because one of the new ion-select-option\n       * elements may match the value. In this case,\n       * the rendered selected text should be updated.\n       */\n      forceUpdate(this);\n    });\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAttributes(this.el, ['aria-label']);\n  }\n  componentDidLoad() {\n    /**\n     * If any of the conditions that trigger the styleChanged callback\n     * are met on component load, it is possible the event emitted\n     * prior to a parent web component registering an event listener.\n     *\n     * To ensure the parent web component receives the event, we\n     * emit the style event again after the component has loaded.\n     *\n     * This is often seen in Angular with the `dist` output target.\n     */\n    this.emitStyle();\n  }\n  disconnectedCallback() {\n    if (this.mutationO) {\n      this.mutationO.disconnect();\n      this.mutationO = undefined;\n    }\n    if (this.notchController) {\n      this.notchController.destroy();\n      this.notchController = undefined;\n    }\n  }\n  /**\n   * Open the select overlay. The overlay is either an alert, action sheet, or popover,\n   * depending on the `interface` property on the `ion-select`.\n   *\n   * @param event The user interface event that called the open.\n   */\n  async open(event) {\n    if (this.disabled || this.isExpanded) {\n      return undefined;\n    }\n    this.isExpanded = true;\n    const overlay = this.overlay = await this.createOverlay(event);\n    // Add logic to scroll selected item into view before presenting\n    const scrollSelectedIntoView = () => {\n      const indexOfSelected = this.childOpts.findIndex(o => o.value === this.value);\n      if (indexOfSelected > -1) {\n        const selectedItem = overlay.querySelector(`.select-interface-option:nth-child(${indexOfSelected + 1})`);\n        if (selectedItem) {\n          /**\n           * Browsers such as Firefox do not\n           * correctly delegate focus when manually\n           * focusing an element with delegatesFocus.\n           * We work around this by manually focusing\n           * the interactive element.\n           * ion-radio and ion-checkbox are the only\n           * elements that ion-select-popover uses, so\n           * we only need to worry about those two components\n           * when focusing.\n           */\n          const interactiveEl = selectedItem.querySelector('ion-radio, ion-checkbox');\n          if (interactiveEl) {\n            selectedItem.scrollIntoView({\n              block: 'nearest'\n            });\n            // Needs to be called before `focusVisibleElement` to prevent issue with focus event bubbling\n            // and removing `ion-focused` style\n            interactiveEl.setFocus();\n          }\n          focusVisibleElement(selectedItem);\n        }\n      } else {\n        /**\n         * If no value is set then focus the first enabled option.\n         */\n        const firstEnabledOption = overlay.querySelector('ion-radio:not(.radio-disabled), ion-checkbox:not(.checkbox-disabled)');\n        if (firstEnabledOption) {\n          /**\n           * Focus the option for the same reason as we do above.\n           *\n           * Needs to be called before `focusVisibleElement` to prevent issue with focus event bubbling\n           * and removing `ion-focused` style\n           */\n          firstEnabledOption.setFocus();\n          focusVisibleElement(firstEnabledOption.closest('ion-item'));\n        }\n      }\n    };\n    // For modals and popovers, we can scroll before they're visible\n    if (this.interface === 'modal') {\n      overlay.addEventListener('ionModalWillPresent', scrollSelectedIntoView, {\n        once: true\n      });\n    } else if (this.interface === 'popover') {\n      overlay.addEventListener('ionPopoverWillPresent', scrollSelectedIntoView, {\n        once: true\n      });\n    } else {\n      /**\n       * For alerts and action sheets, we need to wait a frame after willPresent\n       * because these overlays don't have their content in the DOM immediately\n       * when willPresent fires. By waiting a frame, we ensure the content is\n       * rendered and can be properly scrolled into view.\n       */\n      const scrollAfterRender = () => {\n        requestAnimationFrame(() => {\n          scrollSelectedIntoView();\n        });\n      };\n      if (this.interface === 'alert') {\n        overlay.addEventListener('ionAlertWillPresent', scrollAfterRender, {\n          once: true\n        });\n      } else if (this.interface === 'action-sheet') {\n        overlay.addEventListener('ionActionSheetWillPresent', scrollAfterRender, {\n          once: true\n        });\n      }\n    }\n    overlay.onDidDismiss().then(() => {\n      this.overlay = undefined;\n      this.isExpanded = false;\n      this.ionDismiss.emit();\n      this.setFocus();\n    });\n    await overlay.present();\n    return overlay;\n  }\n  createOverlay(ev) {\n    let selectInterface = this.interface;\n    if (selectInterface === 'action-sheet' && this.multiple) {\n      printIonWarning(`[ion-select] - Interface cannot be \"${selectInterface}\" with a multi-value select. Using the \"alert\" interface instead.`);\n      selectInterface = 'alert';\n    }\n    if (selectInterface === 'popover' && !ev) {\n      printIonWarning(`[ion-select] - Interface cannot be a \"${selectInterface}\" without passing an event. Using the \"alert\" interface instead.`);\n      selectInterface = 'alert';\n    }\n    if (selectInterface === 'action-sheet') {\n      return this.openActionSheet();\n    }\n    if (selectInterface === 'popover') {\n      return this.openPopover(ev);\n    }\n    if (selectInterface === 'modal') {\n      return this.openModal();\n    }\n    return this.openAlert();\n  }\n  updateOverlayOptions() {\n    const overlay = this.overlay;\n    if (!overlay) {\n      return;\n    }\n    const childOpts = this.childOpts;\n    const value = this.value;\n    switch (this.interface) {\n      case 'action-sheet':\n        overlay.buttons = this.createActionSheetButtons(childOpts, value);\n        break;\n      case 'popover':\n        const popover = overlay.querySelector('ion-select-popover');\n        if (popover) {\n          popover.options = this.createOverlaySelectOptions(childOpts, value);\n        }\n        break;\n      case 'modal':\n        const modal = overlay.querySelector('ion-select-modal');\n        if (modal) {\n          modal.options = this.createOverlaySelectOptions(childOpts, value);\n        }\n        break;\n      case 'alert':\n        const inputType = this.multiple ? 'checkbox' : 'radio';\n        overlay.inputs = this.createAlertInputs(childOpts, inputType, value);\n        break;\n    }\n  }\n  createActionSheetButtons(data, selectValue) {\n    const actionSheetButtons = data.map(option => {\n      const value = getOptionValue(option);\n      // Remove hydrated before copying over classes\n      const copyClasses = Array.from(option.classList).filter(cls => cls !== 'hydrated').join(' ');\n      const optClass = `${OPTION_CLASS} ${copyClasses}`;\n      return {\n        role: isOptionSelected(selectValue, value, this.compareWith) ? 'selected' : '',\n        text: option.textContent,\n        cssClass: optClass,\n        handler: () => {\n          this.setValue(value);\n        }\n      };\n    });\n    // Add \"cancel\" button\n    actionSheetButtons.push({\n      text: this.cancelText,\n      role: 'cancel',\n      handler: () => {\n        this.ionCancel.emit();\n      }\n    });\n    return actionSheetButtons;\n  }\n  createAlertInputs(data, inputType, selectValue) {\n    const alertInputs = data.map(option => {\n      const value = getOptionValue(option);\n      // Remove hydrated before copying over classes\n      const copyClasses = Array.from(option.classList).filter(cls => cls !== 'hydrated').join(' ');\n      const optClass = `${OPTION_CLASS} ${copyClasses}`;\n      return {\n        type: inputType,\n        cssClass: optClass,\n        label: option.textContent || '',\n        value,\n        checked: isOptionSelected(selectValue, value, this.compareWith),\n        disabled: option.disabled\n      };\n    });\n    return alertInputs;\n  }\n  createOverlaySelectOptions(data, selectValue) {\n    const popoverOptions = data.map(option => {\n      const value = getOptionValue(option);\n      // Remove hydrated before copying over classes\n      const copyClasses = Array.from(option.classList).filter(cls => cls !== 'hydrated').join(' ');\n      const optClass = `${OPTION_CLASS} ${copyClasses}`;\n      return {\n        text: option.textContent || '',\n        cssClass: optClass,\n        value,\n        checked: isOptionSelected(selectValue, value, this.compareWith),\n        disabled: option.disabled,\n        handler: selected => {\n          this.setValue(selected);\n          if (!this.multiple) {\n            this.close();\n          }\n        }\n      };\n    });\n    return popoverOptions;\n  }\n  async openPopover(ev) {\n    const {\n      fill,\n      labelPlacement\n    } = this;\n    const interfaceOptions = this.interfaceOptions;\n    const mode = getIonMode(this);\n    const showBackdrop = mode === 'md' ? false : true;\n    const multiple = this.multiple;\n    const value = this.value;\n    let event = ev;\n    let size = 'auto';\n    const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n    /**\n     * The popover should take up the full width\n     * when using a fill in MD mode or if the\n     * label is floating/stacked.\n     */\n    if (hasFloatingOrStackedLabel || mode === 'md' && fill !== undefined) {\n      size = 'cover';\n      /**\n       * Otherwise the popover\n       * should be positioned relative\n       * to the native element.\n       */\n    } else {\n      event = Object.assign(Object.assign({}, ev), {\n        detail: {\n          ionShadowTarget: this.nativeWrapperEl\n        }\n      });\n    }\n    const popoverOpts = Object.assign(Object.assign({\n      mode,\n      event,\n      alignment: 'center',\n      size,\n      showBackdrop\n    }, interfaceOptions), {\n      component: 'ion-select-popover',\n      cssClass: ['select-popover', interfaceOptions.cssClass],\n      componentProps: {\n        header: interfaceOptions.header,\n        subHeader: interfaceOptions.subHeader,\n        message: interfaceOptions.message,\n        multiple,\n        value,\n        options: this.createOverlaySelectOptions(this.childOpts, value)\n      }\n    });\n    return popoverController.create(popoverOpts);\n  }\n  async openActionSheet() {\n    const mode = getIonMode(this);\n    const interfaceOptions = this.interfaceOptions;\n    const actionSheetOpts = Object.assign(Object.assign({\n      mode\n    }, interfaceOptions), {\n      buttons: this.createActionSheetButtons(this.childOpts, this.value),\n      cssClass: ['select-action-sheet', interfaceOptions.cssClass]\n    });\n    return actionSheetController.create(actionSheetOpts);\n  }\n  async openAlert() {\n    const interfaceOptions = this.interfaceOptions;\n    const inputType = this.multiple ? 'checkbox' : 'radio';\n    const mode = getIonMode(this);\n    const alertOpts = Object.assign(Object.assign({\n      mode\n    }, interfaceOptions), {\n      header: interfaceOptions.header ? interfaceOptions.header : this.labelText,\n      inputs: this.createAlertInputs(this.childOpts, inputType, this.value),\n      buttons: [{\n        text: this.cancelText,\n        role: 'cancel',\n        handler: () => {\n          this.ionCancel.emit();\n        }\n      }, {\n        text: this.okText,\n        handler: selectedValues => {\n          this.setValue(selectedValues);\n        }\n      }],\n      cssClass: ['select-alert', interfaceOptions.cssClass, this.multiple ? 'multiple-select-alert' : 'single-select-alert']\n    });\n    return alertController.create(alertOpts);\n  }\n  openModal() {\n    const {\n      multiple,\n      value,\n      interfaceOptions\n    } = this;\n    const mode = getIonMode(this);\n    const modalOpts = Object.assign(Object.assign({}, interfaceOptions), {\n      mode,\n      cssClass: ['select-modal', interfaceOptions.cssClass],\n      component: 'ion-select-modal',\n      componentProps: {\n        header: interfaceOptions.header,\n        multiple,\n        value,\n        options: this.createOverlaySelectOptions(this.childOpts, value)\n      }\n    });\n    return modalController.create(modalOpts);\n  }\n  /**\n   * Close the select interface.\n   */\n  close() {\n    if (!this.overlay) {\n      return Promise.resolve(false);\n    }\n    return this.overlay.dismiss();\n  }\n  hasValue() {\n    return this.getText() !== '';\n  }\n  get childOpts() {\n    return Array.from(this.el.querySelectorAll('ion-select-option'));\n  }\n  /**\n   * Returns any plaintext associated with\n   * the label (either prop or slot).\n   * Note: This will not return any custom\n   * HTML. Use the `hasLabel` getter if you\n   * want to know if any slotted label content\n   * was passed.\n   */\n  get labelText() {\n    const {\n      label\n    } = this;\n    if (label !== undefined) {\n      return label;\n    }\n    const {\n      labelSlot\n    } = this;\n    if (labelSlot !== null) {\n      return labelSlot.textContent;\n    }\n    return;\n  }\n  getText() {\n    const selectedText = this.selectedText;\n    if (selectedText != null && selectedText !== '') {\n      return selectedText;\n    }\n    return generateText(this.childOpts, this.value, this.compareWith);\n  }\n  setFocus() {\n    if (this.focusEl) {\n      this.focusEl.focus();\n    }\n  }\n  emitStyle() {\n    const {\n      disabled\n    } = this;\n    const style = {\n      'interactive-disabled': disabled\n    };\n    this.ionStyle.emit(style);\n  }\n  renderLabel() {\n    const {\n      label\n    } = this;\n    return h(\"div\", {\n      class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': !this.hasLabel\n      },\n      part: \"label\"\n    }, label === undefined ? h(\"slot\", {\n      name: \"label\"\n    }) : h(\"div\", {\n      class: \"label-text\"\n    }, label));\n  }\n  componentDidRender() {\n    var _a;\n    (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n  }\n  /**\n   * Gets any content passed into the `label` slot,\n   * not the <slot> definition.\n   */\n  get labelSlot() {\n    return this.el.querySelector('[slot=\"label\"]');\n  }\n  /**\n   * Returns `true` if label content is provided\n   * either by a prop or a content. If you want\n   * to get the plaintext value of the label use\n   * the `labelText` getter instead.\n   */\n  get hasLabel() {\n    return this.label !== undefined || this.labelSlot !== null;\n  }\n  /**\n   * Renders the border container\n   * when fill=\"outline\".\n   */\n  renderLabelContainer() {\n    const mode = getIonMode(this);\n    const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n    if (hasOutlineFill) {\n      /**\n       * The outline fill has a special outline\n       * that appears around the select and the label.\n       * Certain stacked and floating label placements cause the\n       * label to translate up and create a \"cut out\"\n       * inside of that border by using the notch-spacer element.\n       */\n      return [h(\"div\", {\n        class: \"select-outline-container\"\n      }, h(\"div\", {\n        class: \"select-outline-start\"\n      }), h(\"div\", {\n        class: {\n          'select-outline-notch': true,\n          'select-outline-notch-hidden': !this.hasLabel\n        }\n      }, h(\"div\", {\n        class: \"notch-spacer\",\n        \"aria-hidden\": \"true\",\n        ref: el => this.notchSpacerEl = el\n      }, this.label)), h(\"div\", {\n        class: \"select-outline-end\"\n      })), this.renderLabel()];\n    }\n    /**\n     * If not using the outline style,\n     * we can render just the label.\n     */\n    return this.renderLabel();\n  }\n  /**\n   * Renders either the placeholder\n   * or the selected values based on\n   * the state of the select.\n   */\n  renderSelectText() {\n    const {\n      placeholder\n    } = this;\n    const displayValue = this.getText();\n    let addPlaceholderClass = false;\n    let selectText = displayValue;\n    if (selectText === '' && placeholder !== undefined) {\n      selectText = placeholder;\n      addPlaceholderClass = true;\n    }\n    const selectTextClasses = {\n      'select-text': true,\n      'select-placeholder': addPlaceholderClass\n    };\n    const textPart = addPlaceholderClass ? 'placeholder' : 'text';\n    return h(\"div\", {\n      \"aria-hidden\": \"true\",\n      class: selectTextClasses,\n      part: textPart\n    }, selectText);\n  }\n  /**\n   * Renders the chevron icon\n   * next to the select text.\n   */\n  renderSelectIcon() {\n    const mode = getIonMode(this);\n    const {\n      isExpanded,\n      toggleIcon,\n      expandedIcon\n    } = this;\n    let icon;\n    if (isExpanded && expandedIcon !== undefined) {\n      icon = expandedIcon;\n    } else {\n      const defaultIcon = mode === 'ios' ? chevronExpand : caretDownSharp;\n      icon = toggleIcon !== null && toggleIcon !== void 0 ? toggleIcon : defaultIcon;\n    }\n    return h(\"ion-icon\", {\n      class: \"select-icon\",\n      part: \"icon\",\n      \"aria-hidden\": \"true\",\n      icon: icon\n    });\n  }\n  get ariaLabel() {\n    var _a;\n    const {\n      placeholder,\n      inheritedAttributes\n    } = this;\n    const displayValue = this.getText();\n    // The aria label should be preferred over visible text if both are specified\n    const definedLabel = (_a = inheritedAttributes['aria-label']) !== null && _a !== void 0 ? _a : this.labelText;\n    /**\n     * If developer has specified a placeholder\n     * and there is nothing selected, the selectText\n     * should have the placeholder value.\n     */\n    let renderedLabel = displayValue;\n    if (renderedLabel === '' && placeholder !== undefined) {\n      renderedLabel = placeholder;\n    }\n    /**\n     * If there is a developer-defined label,\n     * then we need to concatenate the developer label\n     * string with the current current value.\n     * The label for the control should be read\n     * before the values of the control.\n     */\n    if (definedLabel !== undefined) {\n      renderedLabel = renderedLabel === '' ? definedLabel : `${definedLabel}, ${renderedLabel}`;\n    }\n    return renderedLabel;\n  }\n  renderListbox() {\n    const {\n      disabled,\n      inputId,\n      isExpanded,\n      required\n    } = this;\n    return h(\"button\", {\n      disabled: disabled,\n      id: inputId,\n      \"aria-label\": this.ariaLabel,\n      \"aria-haspopup\": \"dialog\",\n      \"aria-expanded\": `${isExpanded}`,\n      \"aria-describedby\": this.getHintTextID(),\n      \"aria-invalid\": this.getHintTextID() === this.errorTextId,\n      \"aria-required\": `${required}`,\n      onFocus: this.onFocus,\n      onBlur: this.onBlur,\n      ref: focusEl => this.focusEl = focusEl\n    });\n  }\n  getHintTextID() {\n    const {\n      el,\n      helperText,\n      errorText,\n      helperTextId,\n      errorTextId\n    } = this;\n    if (el.classList.contains('ion-touched') && el.classList.contains('ion-invalid') && errorText) {\n      return errorTextId;\n    }\n    if (helperText) {\n      return helperTextId;\n    }\n    return undefined;\n  }\n  /**\n   * Renders the helper text or error text values\n   */\n  renderHintText() {\n    const {\n      helperText,\n      errorText,\n      helperTextId,\n      errorTextId\n    } = this;\n    return [h(\"div\", {\n      id: helperTextId,\n      class: \"helper-text\",\n      part: \"supporting-text helper-text\"\n    }, helperText), h(\"div\", {\n      id: errorTextId,\n      class: \"error-text\",\n      part: \"supporting-text error-text\"\n    }, errorText)];\n  }\n  /**\n   * Responsible for rendering helper text, and error text. This element\n   * should only be rendered if hint text is set.\n   */\n  renderBottomContent() {\n    const {\n      helperText,\n      errorText\n    } = this;\n    /**\n     * undefined and empty string values should\n     * be treated as not having helper/error text.\n     */\n    const hasHintText = !!helperText || !!errorText;\n    if (!hasHintText) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"select-bottom\"\n    }, this.renderHintText());\n  }\n  render() {\n    const {\n      disabled,\n      el,\n      isExpanded,\n      expandedIcon,\n      labelPlacement,\n      justify,\n      placeholder,\n      fill,\n      shape,\n      name,\n      value,\n      hasFocus\n    } = this;\n    const mode = getIonMode(this);\n    const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n    const justifyEnabled = !hasFloatingOrStackedLabel && justify !== undefined;\n    const rtl = isRTL(el) ? 'rtl' : 'ltr';\n    const inItem = hostContext('ion-item', this.el);\n    const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n    const hasValue = this.hasValue();\n    const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n    renderHiddenInput(true, el, name, parseValue(value), disabled);\n    /**\n     * If the label is stacked, it should always sit above the select.\n     * For floating labels, the label should move above the select if\n     * the select has a value, is open, or has anything in either\n     * the start or end slot.\n     *\n     * If there is content in the start slot, the label would overlap\n     * it if not forced to float. This is also applied to the end slot\n     * because with the default or solid fills, the select is not\n     * vertically centered in the container, but the label is. This\n     * causes the slots and label to appear vertically offset from each\n     * other when the label isn't floating above the input. This doesn't\n     * apply to the outline fill, but this was not accounted for to keep\n     * things consistent.\n     *\n     * TODO(FW-5592): Remove hasStartEndSlots condition\n     */\n    const labelShouldFloat = labelPlacement === 'stacked' || labelPlacement === 'floating' && (hasValue || isExpanded || hasStartEndSlots);\n    return h(Host, {\n      key: '6dd3c92dc3c587960d767c7dd1c142fc20bef8ed',\n      onClick: this.onClick,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'in-item': inItem,\n        'in-item-color': hostContext('ion-item.ion-color', el),\n        'select-disabled': disabled,\n        'select-expanded': isExpanded,\n        'has-expanded-icon': expandedIcon !== undefined,\n        'has-value': hasValue,\n        'label-floating': labelShouldFloat,\n        'has-placeholder': placeholder !== undefined,\n        'has-focus': hasFocus,\n        // TODO(FW-6451): Remove `ion-focusable` class in favor of `has-focus`.\n        'ion-focusable': true,\n        [`select-${rtl}`]: true,\n        [`select-fill-${fill}`]: fill !== undefined,\n        [`select-justify-${justify}`]: justifyEnabled,\n        [`select-shape-${shape}`]: shape !== undefined,\n        [`select-label-placement-${labelPlacement}`]: true\n      })\n    }, h(\"label\", {\n      key: 'bc80ee471debb20b33d1cf55f55932b621f1744a',\n      class: \"select-wrapper\",\n      id: \"select-label\",\n      onClick: this.onLabelClick\n    }, this.renderLabelContainer(), h(\"div\", {\n      key: 'deea85c3a3769e90af0933c522028148ee5781a3',\n      class: \"select-wrapper-inner\"\n    }, h(\"slot\", {\n      key: 'af8192f4d8d5426b39a00ef2ed96f3c4a9e12908',\n      name: \"start\"\n    }), h(\"div\", {\n      key: '6c278fc6c56b18e26cae930d375aa1da05af135b',\n      class: \"native-wrapper\",\n      ref: el => this.nativeWrapperEl = el,\n      part: \"container\"\n    }, this.renderSelectText(), this.renderListbox()), h(\"slot\", {\n      key: 'c459d54471029872b8b3e63f7dfb6bdb869b4942',\n      name: \"end\"\n    }), !hasFloatingOrStackedLabel && this.renderSelectIcon()), hasFloatingOrStackedLabel && this.renderSelectIcon(), shouldRenderHighlight && h(\"div\", {\n      key: '718960718fa91163dd4412e1865f476fef089127',\n      class: \"select-highlight\"\n    })), this.renderBottomContent());\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"disabled\": [\"styleChanged\"],\n      \"isExpanded\": [\"styleChanged\"],\n      \"placeholder\": [\"styleChanged\"],\n      \"value\": [\"styleChanged\"]\n    };\n  }\n};\nconst getOptionValue = el => {\n  const value = el.value;\n  return value === undefined ? el.textContent || '' : value;\n};\nconst parseValue = value => {\n  if (value == null) {\n    return undefined;\n  }\n  if (Array.isArray(value)) {\n    return value.join(',');\n  }\n  return value.toString();\n};\nconst generateText = (opts, value, compareWith) => {\n  if (value === undefined) {\n    return '';\n  }\n  if (Array.isArray(value)) {\n    return value.map(v => textForValue(opts, v, compareWith)).filter(opt => opt !== null).join(', ');\n  } else {\n    return textForValue(opts, value, compareWith) || '';\n  }\n};\nconst textForValue = (opts, value, compareWith) => {\n  const selectOpt = opts.find(opt => {\n    return compareOptions(value, getOptionValue(opt), compareWith);\n  });\n  return selectOpt ? selectOpt.textContent : null;\n};\nlet selectIds = 0;\nconst OPTION_CLASS = 'select-interface-option';\nSelect.style = {\n  ios: IonSelectIosStyle0,\n  md: IonSelectMdStyle0\n};\nconst selectOptionCss = \":host{display:none}\";\nconst IonSelectOptionStyle0 = selectOptionCss;\nconst SelectOption = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.inputId = `ion-selopt-${selectOptionIds++}`;\n    this.disabled = false;\n    this.value = undefined;\n  }\n  render() {\n    return h(Host, {\n      key: '8c96c199ce3a3065de3fe446500f567236e0610a',\n      role: \"option\",\n      id: this.inputId,\n      class: getIonMode(this)\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nlet selectOptionIds = 0;\nSelectOption.style = IonSelectOptionStyle0;\nconst selectPopoverIosCss = \".sc-ion-select-popover-ios-h ion-list.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-ios,ion-label.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-ios-h{overflow-y:auto}\";\nconst IonSelectPopoverIosStyle0 = selectPopoverIosCss;\nconst selectPopoverMdCss = \".sc-ion-select-popover-md-h ion-list.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-md,ion-label.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-md-h{overflow-y:auto}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(container){display:none}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(label){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-item.sc-ion-select-popover-md{--inner-border-width:0}.item-radio-checked.sc-ion-select-popover-md{--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08);--background-focused:var(--ion-color-primary, #0054e9);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #0054e9);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-popover-md{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #0054e9)}\";\nconst IonSelectPopoverMdStyle0 = selectPopoverMdCss;\nconst SelectPopover = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.header = undefined;\n    this.subHeader = undefined;\n    this.message = undefined;\n    this.multiple = undefined;\n    this.options = [];\n  }\n  findOptionFromEvent(ev) {\n    const {\n      options\n    } = this;\n    return options.find(o => o.value === ev.target.value);\n  }\n  /**\n   * When an option is selected we need to get the value(s)\n   * of the selected option(s) and return it in the option\n   * handler\n   */\n  callOptionHandler(ev) {\n    const option = this.findOptionFromEvent(ev);\n    const values = this.getValues(ev);\n    if (option === null || option === void 0 ? void 0 : option.handler) {\n      safeCall(option.handler, values);\n    }\n  }\n  /**\n   * Dismisses the host popover that the `ion-select-popover`\n   * is rendered within.\n   */\n  dismissParentPopover() {\n    const popover = this.el.closest('ion-popover');\n    if (popover) {\n      popover.dismiss();\n    }\n  }\n  setChecked(ev) {\n    const {\n      multiple\n    } = this;\n    const option = this.findOptionFromEvent(ev);\n    // this is a popover with checkboxes (multiple value select)\n    // we need to set the checked value for this option\n    if (multiple && option) {\n      option.checked = ev.detail.checked;\n    }\n  }\n  getValues(ev) {\n    const {\n      multiple,\n      options\n    } = this;\n    if (multiple) {\n      // this is a popover with checkboxes (multiple value select)\n      // return an array of all the checked values\n      return options.filter(o => o.checked).map(o => o.value);\n    }\n    // this is a popover with radio buttons (single value select)\n    // return the value that was clicked, otherwise undefined\n    const option = this.findOptionFromEvent(ev);\n    return option ? option.value : undefined;\n  }\n  renderOptions(options) {\n    const {\n      multiple\n    } = this;\n    switch (multiple) {\n      case true:\n        return this.renderCheckboxOptions(options);\n      default:\n        return this.renderRadioOptions(options);\n    }\n  }\n  renderCheckboxOptions(options) {\n    return options.map(option => h(\"ion-item\", {\n      class: Object.assign({\n        // TODO FW-4784\n        'item-checkbox-checked': option.checked\n      }, getClassMap(option.cssClass))\n    }, h(\"ion-checkbox\", {\n      value: option.value,\n      disabled: option.disabled,\n      checked: option.checked,\n      justify: \"start\",\n      labelPlacement: \"end\",\n      onIonChange: ev => {\n        this.setChecked(ev);\n        this.callOptionHandler(ev);\n        // TODO FW-4784\n        forceUpdate(this);\n      }\n    }, option.text)));\n  }\n  renderRadioOptions(options) {\n    const checked = options.filter(o => o.checked).map(o => o.value)[0];\n    return h(\"ion-radio-group\", {\n      value: checked,\n      onIonChange: ev => this.callOptionHandler(ev)\n    }, options.map(option => h(\"ion-item\", {\n      class: Object.assign({\n        // TODO FW-4784\n        'item-radio-checked': option.value === checked\n      }, getClassMap(option.cssClass))\n    }, h(\"ion-radio\", {\n      value: option.value,\n      disabled: option.disabled,\n      onClick: () => this.dismissParentPopover(),\n      onKeyUp: ev => {\n        if (ev.key === ' ') {\n          /**\n           * Selecting a radio option with keyboard navigation,\n           * either through the Enter or Space keys, should\n           * dismiss the popover.\n           */\n          this.dismissParentPopover();\n        }\n      }\n    }, option.text))));\n  }\n  render() {\n    const {\n      header,\n      message,\n      options,\n      subHeader\n    } = this;\n    const hasSubHeaderOrMessage = subHeader !== undefined || message !== undefined;\n    return h(Host, {\n      key: '542367ab8fb72bfebf7e65630b91017d68827fd6',\n      class: getIonMode(this)\n    }, h(\"ion-list\", {\n      key: 'f2f0f37e1365cd7780b02de1a1698700d0df48a7'\n    }, header !== undefined && h(\"ion-list-header\", {\n      key: '4b8800a68e800f19277a44b7074ca24b70218daf'\n    }, header), hasSubHeaderOrMessage && h(\"ion-item\", {\n      key: '932b7903daf97d5a57d289b7ee49e868bb9b0cf5'\n    }, h(\"ion-label\", {\n      key: 'fc3f1b69aa2a0bc6125d35692dcad3a8a99fd160',\n      class: \"ion-text-wrap\"\n    }, subHeader !== undefined && h(\"h3\", {\n      key: 'eceab2f47afa95f04b138342b0bdbfa1f50919a8'\n    }, subHeader), message !== undefined && h(\"p\", {\n      key: '70f4e27ad1316318efd0c17efce31e5e45c8fa02'\n    }, message))), this.renderOptions(options)));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nSelectPopover.style = {\n  ios: IonSelectPopoverIosStyle0,\n  md: IonSelectPopoverMdStyle0\n};\nexport { Select as ion_select, SelectOption as ion_select_option, SelectPopover as ion_select_popover };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAkBM,cACA,oBACA,aACA,mBACA,QA01BA,gBAIA,YASA,cAUA,cAMF,WACE,cAKA,iBACA,uBACA,cAmBF,iBAEE,qBACA,2BACA,oBACA,0BACA;AA96BN;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAM,eAAe;AACrB,IAAM,qBAAqB;AAC3B,IAAM,cAAc;AACpB,IAAM,oBAAoB;AAC1B,IAAM,SAAS,MAAM;AAAA,MACnB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,aAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,aAAK,aAAa,YAAY,MAAM,cAAc,CAAC;AACnD,aAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,aAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,aAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,aAAK,UAAU,WAAW,WAAW;AACrC,aAAK,eAAe,GAAG,KAAK,OAAO;AACnC,aAAK,cAAc,GAAG,KAAK,OAAO;AAClC,aAAK,sBAAsB,CAAC;AAC5B,aAAK,UAAU,QAAM;AACnB,gBAAM,SAAS,GAAG;AAClB,gBAAM,cAAc,OAAO,QAAQ,8BAA8B;AACjE,cAAI,WAAW,KAAK,MAAM,gBAAgB,MAAM;AAC9C,iBAAK,SAAS;AACd,iBAAK,KAAK,EAAE;AAAA,UACd,OAAO;AA2BL,eAAG,eAAe;AAAA,UACpB;AAAA,QACF;AACA,aAAK,UAAU,MAAM;AACnB,eAAK,WAAW;AAChB,eAAK,SAAS,KAAK;AAAA,QACrB;AACA,aAAK,SAAS,MAAM;AAClB,eAAK,WAAW;AAChB,eAAK,QAAQ,KAAK;AAAA,QACpB;AAKA,aAAK,eAAe,QAAM;AAGxB,cAAI,GAAG,WAAW,GAAG,eAAe;AAClC,eAAG,gBAAgB;AAAA,UACrB;AAAA,QACF;AACA,aAAK,aAAa;AAClB,aAAK,WAAW;AAChB,aAAK,aAAa;AAClB,aAAK,QAAQ;AACb,aAAK,cAAc;AACnB,aAAK,WAAW;AAChB,aAAK,OAAO;AACZ,aAAK,YAAY;AACjB,aAAK,aAAa;AAClB,aAAK,YAAY;AACjB,aAAK,mBAAmB,CAAC;AACzB,aAAK,UAAU;AACf,aAAK,QAAQ;AACb,aAAK,iBAAiB;AACtB,aAAK,WAAW;AAChB,aAAK,OAAO,KAAK;AACjB,aAAK,SAAS;AACd,aAAK,cAAc;AACnB,aAAK,eAAe;AACpB,aAAK,aAAa;AAClB,aAAK,eAAe;AACpB,aAAK,QAAQ;AACb,aAAK,QAAQ;AACb,aAAK,WAAW;AAAA,MAClB;AAAA,MACA,eAAe;AACb,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,SAAS,OAAO;AACd,aAAK,QAAQ;AACb,aAAK,UAAU,KAAK;AAAA,UAClB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACM,oBAAoB;AAAA;AACxB,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,eAAK,kBAAkB,sBAAsB,IAAI,MAAM,KAAK,eAAe,MAAM,KAAK,SAAS;AAC/F,eAAK,qBAAqB;AAC1B,eAAK,UAAU;AACf,eAAK,YAAY,gBAAgB,KAAK,IAAI,qBAAqB,MAAY;AACzE,iBAAK,qBAAqB;AAO1B,wBAAY,IAAI;AAAA,UAClB,EAAC;AAAA,QACH;AAAA;AAAA,MACA,oBAAoB;AAClB,aAAK,sBAAsB,kBAAkB,KAAK,IAAI,CAAC,YAAY,CAAC;AAAA,MACtE;AAAA,MACA,mBAAmB;AAWjB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,uBAAuB;AACrB,YAAI,KAAK,WAAW;AAClB,eAAK,UAAU,WAAW;AAC1B,eAAK,YAAY;AAAA,QACnB;AACA,YAAI,KAAK,iBAAiB;AACxB,eAAK,gBAAgB,QAAQ;AAC7B,eAAK,kBAAkB;AAAA,QACzB;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOM,KAAK,OAAO;AAAA;AAChB,cAAI,KAAK,YAAY,KAAK,YAAY;AACpC,mBAAO;AAAA,UACT;AACA,eAAK,aAAa;AAClB,gBAAM,UAAU,KAAK,UAAU,MAAM,KAAK,cAAc,KAAK;AAE7D,gBAAM,yBAAyB,MAAM;AACnC,kBAAM,kBAAkB,KAAK,UAAU,UAAU,OAAK,EAAE,UAAU,KAAK,KAAK;AAC5E,gBAAI,kBAAkB,IAAI;AACxB,oBAAM,eAAe,QAAQ,cAAc,sCAAsC,kBAAkB,CAAC,GAAG;AACvG,kBAAI,cAAc;AAYhB,sBAAM,gBAAgB,aAAa,cAAc,yBAAyB;AAC1E,oBAAI,eAAe;AACjB,+BAAa,eAAe;AAAA,oBAC1B,OAAO;AAAA,kBACT,CAAC;AAGD,gCAAc,SAAS;AAAA,gBACzB;AACA,oCAAoB,YAAY;AAAA,cAClC;AAAA,YACF,OAAO;AAIL,oBAAM,qBAAqB,QAAQ,cAAc,sEAAsE;AACvH,kBAAI,oBAAoB;AAOtB,mCAAmB,SAAS;AAC5B,oCAAoB,mBAAmB,QAAQ,UAAU,CAAC;AAAA,cAC5D;AAAA,YACF;AAAA,UACF;AAEA,cAAI,KAAK,cAAc,SAAS;AAC9B,oBAAQ,iBAAiB,uBAAuB,wBAAwB;AAAA,cACtE,MAAM;AAAA,YACR,CAAC;AAAA,UACH,WAAW,KAAK,cAAc,WAAW;AACvC,oBAAQ,iBAAiB,yBAAyB,wBAAwB;AAAA,cACxE,MAAM;AAAA,YACR,CAAC;AAAA,UACH,OAAO;AAOL,kBAAM,oBAAoB,MAAM;AAC9B,oCAAsB,MAAM;AAC1B,uCAAuB;AAAA,cACzB,CAAC;AAAA,YACH;AACA,gBAAI,KAAK,cAAc,SAAS;AAC9B,sBAAQ,iBAAiB,uBAAuB,mBAAmB;AAAA,gBACjE,MAAM;AAAA,cACR,CAAC;AAAA,YACH,WAAW,KAAK,cAAc,gBAAgB;AAC5C,sBAAQ,iBAAiB,6BAA6B,mBAAmB;AAAA,gBACvE,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF;AACA,kBAAQ,aAAa,EAAE,KAAK,MAAM;AAChC,iBAAK,UAAU;AACf,iBAAK,aAAa;AAClB,iBAAK,WAAW,KAAK;AACrB,iBAAK,SAAS;AAAA,UAChB,CAAC;AACD,gBAAM,QAAQ,QAAQ;AACtB,iBAAO;AAAA,QACT;AAAA;AAAA,MACA,cAAc,IAAI;AAChB,YAAI,kBAAkB,KAAK;AAC3B,YAAI,oBAAoB,kBAAkB,KAAK,UAAU;AACvD,0BAAgB,uCAAuC,eAAe,mEAAmE;AACzI,4BAAkB;AAAA,QACpB;AACA,YAAI,oBAAoB,aAAa,CAAC,IAAI;AACxC,0BAAgB,yCAAyC,eAAe,kEAAkE;AAC1I,4BAAkB;AAAA,QACpB;AACA,YAAI,oBAAoB,gBAAgB;AACtC,iBAAO,KAAK,gBAAgB;AAAA,QAC9B;AACA,YAAI,oBAAoB,WAAW;AACjC,iBAAO,KAAK,YAAY,EAAE;AAAA,QAC5B;AACA,YAAI,oBAAoB,SAAS;AAC/B,iBAAO,KAAK,UAAU;AAAA,QACxB;AACA,eAAO,KAAK,UAAU;AAAA,MACxB;AAAA,MACA,uBAAuB;AACrB,cAAM,UAAU,KAAK;AACrB,YAAI,CAAC,SAAS;AACZ;AAAA,QACF;AACA,cAAM,YAAY,KAAK;AACvB,cAAM,QAAQ,KAAK;AACnB,gBAAQ,KAAK,WAAW;AAAA,UACtB,KAAK;AACH,oBAAQ,UAAU,KAAK,yBAAyB,WAAW,KAAK;AAChE;AAAA,UACF,KAAK;AACH,kBAAM,UAAU,QAAQ,cAAc,oBAAoB;AAC1D,gBAAI,SAAS;AACX,sBAAQ,UAAU,KAAK,2BAA2B,WAAW,KAAK;AAAA,YACpE;AACA;AAAA,UACF,KAAK;AACH,kBAAM,QAAQ,QAAQ,cAAc,kBAAkB;AACtD,gBAAI,OAAO;AACT,oBAAM,UAAU,KAAK,2BAA2B,WAAW,KAAK;AAAA,YAClE;AACA;AAAA,UACF,KAAK;AACH,kBAAM,YAAY,KAAK,WAAW,aAAa;AAC/C,oBAAQ,SAAS,KAAK,kBAAkB,WAAW,WAAW,KAAK;AACnE;AAAA,QACJ;AAAA,MACF;AAAA,MACA,yBAAyB,MAAM,aAAa;AAC1C,cAAM,qBAAqB,KAAK,IAAI,YAAU;AAC5C,gBAAM,QAAQ,eAAe,MAAM;AAEnC,gBAAM,cAAc,MAAM,KAAK,OAAO,SAAS,EAAE,OAAO,SAAO,QAAQ,UAAU,EAAE,KAAK,GAAG;AAC3F,gBAAM,WAAW,GAAG,YAAY,IAAI,WAAW;AAC/C,iBAAO;AAAA,YACL,MAAM,iBAAiB,aAAa,OAAO,KAAK,WAAW,IAAI,aAAa;AAAA,YAC5E,MAAM,OAAO;AAAA,YACb,UAAU;AAAA,YACV,SAAS,MAAM;AACb,mBAAK,SAAS,KAAK;AAAA,YACrB;AAAA,UACF;AAAA,QACF,CAAC;AAED,2BAAmB,KAAK;AAAA,UACtB,MAAM,KAAK;AAAA,UACX,MAAM;AAAA,UACN,SAAS,MAAM;AACb,iBAAK,UAAU,KAAK;AAAA,UACtB;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT;AAAA,MACA,kBAAkB,MAAM,WAAW,aAAa;AAC9C,cAAM,cAAc,KAAK,IAAI,YAAU;AACrC,gBAAM,QAAQ,eAAe,MAAM;AAEnC,gBAAM,cAAc,MAAM,KAAK,OAAO,SAAS,EAAE,OAAO,SAAO,QAAQ,UAAU,EAAE,KAAK,GAAG;AAC3F,gBAAM,WAAW,GAAG,YAAY,IAAI,WAAW;AAC/C,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,UAAU;AAAA,YACV,OAAO,OAAO,eAAe;AAAA,YAC7B;AAAA,YACA,SAAS,iBAAiB,aAAa,OAAO,KAAK,WAAW;AAAA,YAC9D,UAAU,OAAO;AAAA,UACnB;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT;AAAA,MACA,2BAA2B,MAAM,aAAa;AAC5C,cAAM,iBAAiB,KAAK,IAAI,YAAU;AACxC,gBAAM,QAAQ,eAAe,MAAM;AAEnC,gBAAM,cAAc,MAAM,KAAK,OAAO,SAAS,EAAE,OAAO,SAAO,QAAQ,UAAU,EAAE,KAAK,GAAG;AAC3F,gBAAM,WAAW,GAAG,YAAY,IAAI,WAAW;AAC/C,iBAAO;AAAA,YACL,MAAM,OAAO,eAAe;AAAA,YAC5B,UAAU;AAAA,YACV;AAAA,YACA,SAAS,iBAAiB,aAAa,OAAO,KAAK,WAAW;AAAA,YAC9D,UAAU,OAAO;AAAA,YACjB,SAAS,cAAY;AACnB,mBAAK,SAAS,QAAQ;AACtB,kBAAI,CAAC,KAAK,UAAU;AAClB,qBAAK,MAAM;AAAA,cACb;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT;AAAA,MACM,YAAY,IAAI;AAAA;AACpB,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM,mBAAmB,KAAK;AAC9B,gBAAM,OAAO,WAAW,IAAI;AAC5B,gBAAM,eAAe,SAAS,OAAO,QAAQ;AAC7C,gBAAM,WAAW,KAAK;AACtB,gBAAM,QAAQ,KAAK;AACnB,cAAI,QAAQ;AACZ,cAAI,OAAO;AACX,gBAAM,4BAA4B,mBAAmB,cAAc,mBAAmB;AAMtF,cAAI,6BAA6B,SAAS,QAAQ,SAAS,QAAW;AACpE,mBAAO;AAAA,UAMT,OAAO;AACL,oBAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,EAAE,GAAG;AAAA,cAC3C,QAAQ;AAAA,gBACN,iBAAiB,KAAK;AAAA,cACxB;AAAA,YACF,CAAC;AAAA,UACH;AACA,gBAAM,cAAc,OAAO,OAAO,OAAO,OAAO;AAAA,YAC9C;AAAA,YACA;AAAA,YACA,WAAW;AAAA,YACX;AAAA,YACA;AAAA,UACF,GAAG,gBAAgB,GAAG;AAAA,YACpB,WAAW;AAAA,YACX,UAAU,CAAC,kBAAkB,iBAAiB,QAAQ;AAAA,YACtD,gBAAgB;AAAA,cACd,QAAQ,iBAAiB;AAAA,cACzB,WAAW,iBAAiB;AAAA,cAC5B,SAAS,iBAAiB;AAAA,cAC1B;AAAA,cACA;AAAA,cACA,SAAS,KAAK,2BAA2B,KAAK,WAAW,KAAK;AAAA,YAChE;AAAA,UACF,CAAC;AACD,iBAAO,kBAAkB,OAAO,WAAW;AAAA,QAC7C;AAAA;AAAA,MACM,kBAAkB;AAAA;AACtB,gBAAM,OAAO,WAAW,IAAI;AAC5B,gBAAM,mBAAmB,KAAK;AAC9B,gBAAM,kBAAkB,OAAO,OAAO,OAAO,OAAO;AAAA,YAClD;AAAA,UACF,GAAG,gBAAgB,GAAG;AAAA,YACpB,SAAS,KAAK,yBAAyB,KAAK,WAAW,KAAK,KAAK;AAAA,YACjE,UAAU,CAAC,uBAAuB,iBAAiB,QAAQ;AAAA,UAC7D,CAAC;AACD,iBAAO,sBAAsB,OAAO,eAAe;AAAA,QACrD;AAAA;AAAA,MACM,YAAY;AAAA;AAChB,gBAAM,mBAAmB,KAAK;AAC9B,gBAAM,YAAY,KAAK,WAAW,aAAa;AAC/C,gBAAM,OAAO,WAAW,IAAI;AAC5B,gBAAM,YAAY,OAAO,OAAO,OAAO,OAAO;AAAA,YAC5C;AAAA,UACF,GAAG,gBAAgB,GAAG;AAAA,YACpB,QAAQ,iBAAiB,SAAS,iBAAiB,SAAS,KAAK;AAAA,YACjE,QAAQ,KAAK,kBAAkB,KAAK,WAAW,WAAW,KAAK,KAAK;AAAA,YACpE,SAAS,CAAC;AAAA,cACR,MAAM,KAAK;AAAA,cACX,MAAM;AAAA,cACN,SAAS,MAAM;AACb,qBAAK,UAAU,KAAK;AAAA,cACtB;AAAA,YACF,GAAG;AAAA,cACD,MAAM,KAAK;AAAA,cACX,SAAS,oBAAkB;AACzB,qBAAK,SAAS,cAAc;AAAA,cAC9B;AAAA,YACF,CAAC;AAAA,YACD,UAAU,CAAC,gBAAgB,iBAAiB,UAAU,KAAK,WAAW,0BAA0B,qBAAqB;AAAA,UACvH,CAAC;AACD,iBAAO,gBAAgB,OAAO,SAAS;AAAA,QACzC;AAAA;AAAA,MACA,YAAY;AACV,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM,YAAY,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,gBAAgB,GAAG;AAAA,UACnE;AAAA,UACA,UAAU,CAAC,gBAAgB,iBAAiB,QAAQ;AAAA,UACpD,WAAW;AAAA,UACX,gBAAgB;AAAA,YACd,QAAQ,iBAAiB;AAAA,YACzB;AAAA,YACA;AAAA,YACA,SAAS,KAAK,2BAA2B,KAAK,WAAW,KAAK;AAAA,UAChE;AAAA,QACF,CAAC;AACD,eAAO,gBAAgB,OAAO,SAAS;AAAA,MACzC;AAAA;AAAA;AAAA;AAAA,MAIA,QAAQ;AACN,YAAI,CAAC,KAAK,SAAS;AACjB,iBAAO,QAAQ,QAAQ,KAAK;AAAA,QAC9B;AACA,eAAO,KAAK,QAAQ,QAAQ;AAAA,MAC9B;AAAA,MACA,WAAW;AACT,eAAO,KAAK,QAAQ,MAAM;AAAA,MAC5B;AAAA,MACA,IAAI,YAAY;AACd,eAAO,MAAM,KAAK,KAAK,GAAG,iBAAiB,mBAAmB,CAAC;AAAA,MACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,IAAI,YAAY;AACd,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,YAAI,UAAU,QAAW;AACvB,iBAAO;AAAA,QACT;AACA,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,YAAI,cAAc,MAAM;AACtB,iBAAO,UAAU;AAAA,QACnB;AACA;AAAA,MACF;AAAA,MACA,UAAU;AACR,cAAM,eAAe,KAAK;AAC1B,YAAI,gBAAgB,QAAQ,iBAAiB,IAAI;AAC/C,iBAAO;AAAA,QACT;AACA,eAAO,aAAa,KAAK,WAAW,KAAK,OAAO,KAAK,WAAW;AAAA,MAClE;AAAA,MACA,WAAW;AACT,YAAI,KAAK,SAAS;AAChB,eAAK,QAAQ,MAAM;AAAA,QACrB;AAAA,MACF;AAAA,MACA,YAAY;AACV,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,cAAM,QAAQ;AAAA,UACZ,wBAAwB;AAAA,QAC1B;AACA,aAAK,SAAS,KAAK,KAAK;AAAA,MAC1B;AAAA,MACA,cAAc;AACZ,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,eAAO,EAAE,OAAO;AAAA,UACd,OAAO;AAAA,YACL,sBAAsB;AAAA,YACtB,6BAA6B,CAAC,KAAK;AAAA,UACrC;AAAA,UACA,MAAM;AAAA,QACR,GAAG,UAAU,SAAY,EAAE,QAAQ;AAAA,UACjC,MAAM;AAAA,QACR,CAAC,IAAI,EAAE,OAAO;AAAA,UACZ,OAAO;AAAA,QACT,GAAG,KAAK,CAAC;AAAA,MACX;AAAA,MACA,qBAAqB;AACnB,YAAI;AACJ,SAAC,KAAK,KAAK,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,oBAAoB;AAAA,MAC1F;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,IAAI,YAAY;AACd,eAAO,KAAK,GAAG,cAAc,gBAAgB;AAAA,MAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,IAAI,WAAW;AACb,eAAO,KAAK,UAAU,UAAa,KAAK,cAAc;AAAA,MACxD;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,uBAAuB;AACrB,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM,iBAAiB,SAAS,QAAQ,KAAK,SAAS;AACtD,YAAI,gBAAgB;AAQlB,iBAAO,CAAC,EAAE,OAAO;AAAA,YACf,OAAO;AAAA,UACT,GAAG,EAAE,OAAO;AAAA,YACV,OAAO;AAAA,UACT,CAAC,GAAG,EAAE,OAAO;AAAA,YACX,OAAO;AAAA,cACL,wBAAwB;AAAA,cACxB,+BAA+B,CAAC,KAAK;AAAA,YACvC;AAAA,UACF,GAAG,EAAE,OAAO;AAAA,YACV,OAAO;AAAA,YACP,eAAe;AAAA,YACf,KAAK,QAAM,KAAK,gBAAgB;AAAA,UAClC,GAAG,KAAK,KAAK,CAAC,GAAG,EAAE,OAAO;AAAA,YACxB,OAAO;AAAA,UACT,CAAC,CAAC,GAAG,KAAK,YAAY,CAAC;AAAA,QACzB;AAKA,eAAO,KAAK,YAAY;AAAA,MAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,mBAAmB;AACjB,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,cAAM,eAAe,KAAK,QAAQ;AAClC,YAAI,sBAAsB;AAC1B,YAAI,aAAa;AACjB,YAAI,eAAe,MAAM,gBAAgB,QAAW;AAClD,uBAAa;AACb,gCAAsB;AAAA,QACxB;AACA,cAAM,oBAAoB;AAAA,UACxB,eAAe;AAAA,UACf,sBAAsB;AAAA,QACxB;AACA,cAAM,WAAW,sBAAsB,gBAAgB;AACvD,eAAO,EAAE,OAAO;AAAA,UACd,eAAe;AAAA,UACf,OAAO;AAAA,UACP,MAAM;AAAA,QACR,GAAG,UAAU;AAAA,MACf;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,mBAAmB;AACjB,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI;AACJ,YAAI,cAAc,iBAAiB,QAAW;AAC5C,iBAAO;AAAA,QACT,OAAO;AACL,gBAAM,cAAc,SAAS,QAAQ,gBAAgB;AACrD,iBAAO,eAAe,QAAQ,eAAe,SAAS,aAAa;AAAA,QACrE;AACA,eAAO,EAAE,YAAY;AAAA,UACnB,OAAO;AAAA,UACP,MAAM;AAAA,UACN,eAAe;AAAA,UACf;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,IAAI,YAAY;AACd,YAAI;AACJ,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,eAAe,KAAK,QAAQ;AAElC,cAAM,gBAAgB,KAAK,oBAAoB,YAAY,OAAO,QAAQ,OAAO,SAAS,KAAK,KAAK;AAMpG,YAAI,gBAAgB;AACpB,YAAI,kBAAkB,MAAM,gBAAgB,QAAW;AACrD,0BAAgB;AAAA,QAClB;AAQA,YAAI,iBAAiB,QAAW;AAC9B,0BAAgB,kBAAkB,KAAK,eAAe,GAAG,YAAY,KAAK,aAAa;AAAA,QACzF;AACA,eAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AACd,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,eAAO,EAAE,UAAU;AAAA,UACjB;AAAA,UACA,IAAI;AAAA,UACJ,cAAc,KAAK;AAAA,UACnB,iBAAiB;AAAA,UACjB,iBAAiB,GAAG,UAAU;AAAA,UAC9B,oBAAoB,KAAK,cAAc;AAAA,UACvC,gBAAgB,KAAK,cAAc,MAAM,KAAK;AAAA,UAC9C,iBAAiB,GAAG,QAAQ;AAAA,UAC5B,SAAS,KAAK;AAAA,UACd,QAAQ,KAAK;AAAA,UACb,KAAK,aAAW,KAAK,UAAU;AAAA,QACjC,CAAC;AAAA,MACH;AAAA,MACA,gBAAgB;AACd,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,GAAG,UAAU,SAAS,aAAa,KAAK,GAAG,UAAU,SAAS,aAAa,KAAK,WAAW;AAC7F,iBAAO;AAAA,QACT;AACA,YAAI,YAAY;AACd,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA,MAIA,iBAAiB;AACf,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,eAAO,CAAC,EAAE,OAAO;AAAA,UACf,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,GAAG,UAAU,GAAG,EAAE,OAAO;AAAA,UACvB,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,GAAG,SAAS,CAAC;AAAA,MACf;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,sBAAsB;AACpB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AAKJ,cAAM,cAAc,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,YAAI,CAAC,aAAa;AAChB;AAAA,QACF;AACA,eAAO,EAAE,OAAO;AAAA,UACd,OAAO;AAAA,QACT,GAAG,KAAK,eAAe,CAAC;AAAA,MAC1B;AAAA,MACA,SAAS;AACP,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM,4BAA4B,mBAAmB,cAAc,mBAAmB;AACtF,cAAM,iBAAiB,CAAC,6BAA6B,YAAY;AACjE,cAAM,MAAM,MAAM,EAAE,IAAI,QAAQ;AAChC,cAAM,SAAS,YAAY,YAAY,KAAK,EAAE;AAC9C,cAAM,wBAAwB,SAAS,QAAQ,SAAS,aAAa,CAAC;AACtE,cAAM,WAAW,KAAK,SAAS;AAC/B,cAAM,mBAAmB,GAAG,cAAc,8BAA8B,MAAM;AAC9E,0BAAkB,MAAM,IAAI,MAAM,WAAW,KAAK,GAAG,QAAQ;AAkB7D,cAAM,mBAAmB,mBAAmB,aAAa,mBAAmB,eAAe,YAAY,cAAc;AACrH,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,SAAS,KAAK;AAAA,UACd,OAAO,mBAAmB,KAAK,OAAO;AAAA,YACpC,CAAC,IAAI,GAAG;AAAA,YACR,WAAW;AAAA,YACX,iBAAiB,YAAY,sBAAsB,EAAE;AAAA,YACrD,mBAAmB;AAAA,YACnB,mBAAmB;AAAA,YACnB,qBAAqB,iBAAiB;AAAA,YACtC,aAAa;AAAA,YACb,kBAAkB;AAAA,YAClB,mBAAmB,gBAAgB;AAAA,YACnC,aAAa;AAAA;AAAA,YAEb,iBAAiB;AAAA,YACjB,CAAC,UAAU,GAAG,EAAE,GAAG;AAAA,YACnB,CAAC,eAAe,IAAI,EAAE,GAAG,SAAS;AAAA,YAClC,CAAC,kBAAkB,OAAO,EAAE,GAAG;AAAA,YAC/B,CAAC,gBAAgB,KAAK,EAAE,GAAG,UAAU;AAAA,YACrC,CAAC,0BAA0B,cAAc,EAAE,GAAG;AAAA,UAChD,CAAC;AAAA,QACH,GAAG,EAAE,SAAS;AAAA,UACZ,KAAK;AAAA,UACL,OAAO;AAAA,UACP,IAAI;AAAA,UACJ,SAAS,KAAK;AAAA,QAChB,GAAG,KAAK,qBAAqB,GAAG,EAAE,OAAO;AAAA,UACvC,KAAK;AAAA,UACL,OAAO;AAAA,QACT,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,UACL,MAAM;AAAA,QACR,CAAC,GAAG,EAAE,OAAO;AAAA,UACX,KAAK;AAAA,UACL,OAAO;AAAA,UACP,KAAK,CAAAA,QAAM,KAAK,kBAAkBA;AAAA,UAClC,MAAM;AAAA,QACR,GAAG,KAAK,iBAAiB,GAAG,KAAK,cAAc,CAAC,GAAG,EAAE,QAAQ;AAAA,UAC3D,KAAK;AAAA,UACL,MAAM;AAAA,QACR,CAAC,GAAG,CAAC,6BAA6B,KAAK,iBAAiB,CAAC,GAAG,6BAA6B,KAAK,iBAAiB,GAAG,yBAAyB,EAAE,OAAO;AAAA,UAClJ,KAAK;AAAA,UACL,OAAO;AAAA,QACT,CAAC,CAAC,GAAG,KAAK,oBAAoB,CAAC;AAAA,MACjC;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,YAAY,CAAC,cAAc;AAAA,UAC3B,cAAc,CAAC,cAAc;AAAA,UAC7B,eAAe,CAAC,cAAc;AAAA,UAC9B,SAAS,CAAC,cAAc;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,IAAM,iBAAiB,QAAM;AAC3B,YAAM,QAAQ,GAAG;AACjB,aAAO,UAAU,SAAY,GAAG,eAAe,KAAK;AAAA,IACtD;AACA,IAAM,aAAa,WAAS;AAC1B,UAAI,SAAS,MAAM;AACjB,eAAO;AAAA,MACT;AACA,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,eAAO,MAAM,KAAK,GAAG;AAAA,MACvB;AACA,aAAO,MAAM,SAAS;AAAA,IACxB;AACA,IAAM,eAAe,CAAC,MAAM,OAAO,gBAAgB;AACjD,UAAI,UAAU,QAAW;AACvB,eAAO;AAAA,MACT;AACA,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,eAAO,MAAM,IAAI,OAAK,aAAa,MAAM,GAAG,WAAW,CAAC,EAAE,OAAO,SAAO,QAAQ,IAAI,EAAE,KAAK,IAAI;AAAA,MACjG,OAAO;AACL,eAAO,aAAa,MAAM,OAAO,WAAW,KAAK;AAAA,MACnD;AAAA,IACF;AACA,IAAM,eAAe,CAAC,MAAM,OAAO,gBAAgB;AACjD,YAAM,YAAY,KAAK,KAAK,SAAO;AACjC,eAAO,eAAe,OAAO,eAAe,GAAG,GAAG,WAAW;AAAA,MAC/D,CAAC;AACD,aAAO,YAAY,UAAU,cAAc;AAAA,IAC7C;AACA,IAAI,YAAY;AAChB,IAAM,eAAe;AACrB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AACA,IAAM,kBAAkB;AACxB,IAAM,wBAAwB;AAC9B,IAAM,eAAe,MAAM;AAAA,MACzB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,UAAU,cAAc,iBAAiB;AAC9C,aAAK,WAAW;AAChB,aAAK,QAAQ;AAAA,MACf;AAAA,MACA,SAAS;AACP,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,MAAM;AAAA,UACN,IAAI,KAAK;AAAA,UACT,OAAO,WAAW,IAAI;AAAA,QACxB,CAAC;AAAA,MACH;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,IACF;AACA,IAAI,kBAAkB;AACtB,iBAAa,QAAQ;AACrB,IAAM,sBAAsB;AAC5B,IAAM,4BAA4B;AAClC,IAAM,qBAAqB;AAC3B,IAAM,2BAA2B;AACjC,IAAM,gBAAgB,MAAM;AAAA,MAC1B,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,SAAS;AACd,aAAK,YAAY;AACjB,aAAK,UAAU;AACf,aAAK,WAAW;AAChB,aAAK,UAAU,CAAC;AAAA,MAClB;AAAA,MACA,oBAAoB,IAAI;AACtB,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,eAAO,QAAQ,KAAK,OAAK,EAAE,UAAU,GAAG,OAAO,KAAK;AAAA,MACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,kBAAkB,IAAI;AACpB,cAAM,SAAS,KAAK,oBAAoB,EAAE;AAC1C,cAAM,SAAS,KAAK,UAAU,EAAE;AAChC,YAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS;AAClE,mBAAS,OAAO,SAAS,MAAM;AAAA,QACjC;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,uBAAuB;AACrB,cAAM,UAAU,KAAK,GAAG,QAAQ,aAAa;AAC7C,YAAI,SAAS;AACX,kBAAQ,QAAQ;AAAA,QAClB;AAAA,MACF;AAAA,MACA,WAAW,IAAI;AACb,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,cAAM,SAAS,KAAK,oBAAoB,EAAE;AAG1C,YAAI,YAAY,QAAQ;AACtB,iBAAO,UAAU,GAAG,OAAO;AAAA,QAC7B;AAAA,MACF;AAAA,MACA,UAAU,IAAI;AACZ,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,UAAU;AAGZ,iBAAO,QAAQ,OAAO,OAAK,EAAE,OAAO,EAAE,IAAI,OAAK,EAAE,KAAK;AAAA,QACxD;AAGA,cAAM,SAAS,KAAK,oBAAoB,EAAE;AAC1C,eAAO,SAAS,OAAO,QAAQ;AAAA,MACjC;AAAA,MACA,cAAc,SAAS;AACrB,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,gBAAQ,UAAU;AAAA,UAChB,KAAK;AACH,mBAAO,KAAK,sBAAsB,OAAO;AAAA,UAC3C;AACE,mBAAO,KAAK,mBAAmB,OAAO;AAAA,QAC1C;AAAA,MACF;AAAA,MACA,sBAAsB,SAAS;AAC7B,eAAO,QAAQ,IAAI,YAAU,EAAE,YAAY;AAAA,UACzC,OAAO,OAAO,OAAO;AAAA;AAAA,YAEnB,yBAAyB,OAAO;AAAA,UAClC,GAAG,YAAY,OAAO,QAAQ,CAAC;AAAA,QACjC,GAAG,EAAE,gBAAgB;AAAA,UACnB,OAAO,OAAO;AAAA,UACd,UAAU,OAAO;AAAA,UACjB,SAAS,OAAO;AAAA,UAChB,SAAS;AAAA,UACT,gBAAgB;AAAA,UAChB,aAAa,QAAM;AACjB,iBAAK,WAAW,EAAE;AAClB,iBAAK,kBAAkB,EAAE;AAEzB,wBAAY,IAAI;AAAA,UAClB;AAAA,QACF,GAAG,OAAO,IAAI,CAAC,CAAC;AAAA,MAClB;AAAA,MACA,mBAAmB,SAAS;AAC1B,cAAM,UAAU,QAAQ,OAAO,OAAK,EAAE,OAAO,EAAE,IAAI,OAAK,EAAE,KAAK,EAAE,CAAC;AAClE,eAAO,EAAE,mBAAmB;AAAA,UAC1B,OAAO;AAAA,UACP,aAAa,QAAM,KAAK,kBAAkB,EAAE;AAAA,QAC9C,GAAG,QAAQ,IAAI,YAAU,EAAE,YAAY;AAAA,UACrC,OAAO,OAAO,OAAO;AAAA;AAAA,YAEnB,sBAAsB,OAAO,UAAU;AAAA,UACzC,GAAG,YAAY,OAAO,QAAQ,CAAC;AAAA,QACjC,GAAG,EAAE,aAAa;AAAA,UAChB,OAAO,OAAO;AAAA,UACd,UAAU,OAAO;AAAA,UACjB,SAAS,MAAM,KAAK,qBAAqB;AAAA,UACzC,SAAS,QAAM;AACb,gBAAI,GAAG,QAAQ,KAAK;AAMlB,mBAAK,qBAAqB;AAAA,YAC5B;AAAA,UACF;AAAA,QACF,GAAG,OAAO,IAAI,CAAC,CAAC,CAAC;AAAA,MACnB;AAAA,MACA,SAAS;AACP,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,wBAAwB,cAAc,UAAa,YAAY;AACrE,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,OAAO,WAAW,IAAI;AAAA,QACxB,GAAG,EAAE,YAAY;AAAA,UACf,KAAK;AAAA,QACP,GAAG,WAAW,UAAa,EAAE,mBAAmB;AAAA,UAC9C,KAAK;AAAA,QACP,GAAG,MAAM,GAAG,yBAAyB,EAAE,YAAY;AAAA,UACjD,KAAK;AAAA,QACP,GAAG,EAAE,aAAa;AAAA,UAChB,KAAK;AAAA,UACL,OAAO;AAAA,QACT,GAAG,cAAc,UAAa,EAAE,MAAM;AAAA,UACpC,KAAK;AAAA,QACP,GAAG,SAAS,GAAG,YAAY,UAAa,EAAE,KAAK;AAAA,UAC7C,KAAK;AAAA,QACP,GAAG,OAAO,CAAC,CAAC,GAAG,KAAK,cAAc,OAAO,CAAC,CAAC;AAAA,MAC7C;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,IACF;AACA,kBAAc,QAAQ;AAAA,MACpB,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AAAA;AAAA;", "names": ["el"], "x_google_ignoreList": [0]}