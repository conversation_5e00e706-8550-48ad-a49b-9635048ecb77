﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Schema\20250501202000_AlterTableOccurrenceAddMessage.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Data\Scripts\202505021949_InsertModelVehicles.sql" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Data\Scripts\202505021949_InsertModelVehicles.sql" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FluentMigrator" Version="7.1.0" />
    <PackageReference Include="FluentMigrator.Runner" Version="7.1.0" />
    <PackageReference Include="FluentMigrator.Runner.SqlServer" Version="7.1.0" />
    <PackageReference Include="Microsoft.Extensions.CommandLineUtils" Version="1.1.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.Development.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
