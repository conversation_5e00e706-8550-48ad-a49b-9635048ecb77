{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/index-39782642.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { G as GESTURE_CONTROLLER } from './gesture-controller-314a54f6.js';\nexport { G as GESTURE_CONTROLLER } from './gesture-controller-314a54f6.js';\nconst addEventListener = (el,\n// TODO(FW-2832): type\neventName, callback, opts) => {\n  // use event listener options when supported\n  // otherwise it's just a boolean for the \"capture\" arg\n  const listenerOpts = supportsPassive(el) ? {\n    capture: !!opts.capture,\n    passive: !!opts.passive\n  } : !!opts.capture;\n  let add;\n  let remove;\n  if (el['__zone_symbol__addEventListener']) {\n    add = '__zone_symbol__addEventListener';\n    remove = '__zone_symbol__removeEventListener';\n  } else {\n    add = 'addEventListener';\n    remove = 'removeEventListener';\n  }\n  el[add](eventName, callback, listenerOpts);\n  return () => {\n    el[remove](eventName, callback, listenerOpts);\n  };\n};\nconst supportsPassive = node => {\n  if (_sPassive === undefined) {\n    try {\n      const opts = Object.defineProperty({}, 'passive', {\n        get: () => {\n          _sPassive = true;\n        }\n      });\n      node.addEventListener('optsTest', () => {\n        return;\n      }, opts);\n    } catch (e) {\n      _sPassive = false;\n    }\n  }\n  return !!_sPassive;\n};\nlet _sPassive;\nconst MOUSE_WAIT = 2000;\n// TODO(FW-2832): types\nconst createPointerEvents = (el, pointerDown, pointerMove, pointerUp, options) => {\n  let rmTouchStart;\n  let rmTouchMove;\n  let rmTouchEnd;\n  let rmTouchCancel;\n  let rmMouseStart;\n  let rmMouseMove;\n  let rmMouseUp;\n  let lastTouchEvent = 0;\n  const handleTouchStart = ev => {\n    lastTouchEvent = Date.now() + MOUSE_WAIT;\n    if (!pointerDown(ev)) {\n      return;\n    }\n    if (!rmTouchMove && pointerMove) {\n      rmTouchMove = addEventListener(el, 'touchmove', pointerMove, options);\n    }\n    /**\n     * Events are dispatched on the element that is tapped and bubble up to\n     * the reference element in the gesture. In the event that the element this\n     * event was first dispatched on is removed from the DOM, the event will no\n     * longer bubble up to our reference element. This leaves the gesture in an\n     * unusable state. To account for this, the touchend and touchcancel listeners\n     * should be added to the event target so that they still fire even if the target\n     * is removed from the DOM.\n     */\n    if (!rmTouchEnd) {\n      rmTouchEnd = addEventListener(ev.target, 'touchend', handleTouchEnd, options);\n    }\n    if (!rmTouchCancel) {\n      rmTouchCancel = addEventListener(ev.target, 'touchcancel', handleTouchEnd, options);\n    }\n  };\n  const handleMouseDown = ev => {\n    if (lastTouchEvent > Date.now()) {\n      return;\n    }\n    if (!pointerDown(ev)) {\n      return;\n    }\n    if (!rmMouseMove && pointerMove) {\n      rmMouseMove = addEventListener(getDocument(el), 'mousemove', pointerMove, options);\n    }\n    if (!rmMouseUp) {\n      rmMouseUp = addEventListener(getDocument(el), 'mouseup', handleMouseUp, options);\n    }\n  };\n  const handleTouchEnd = ev => {\n    stopTouch();\n    if (pointerUp) {\n      pointerUp(ev);\n    }\n  };\n  const handleMouseUp = ev => {\n    stopMouse();\n    if (pointerUp) {\n      pointerUp(ev);\n    }\n  };\n  const stopTouch = () => {\n    if (rmTouchMove) {\n      rmTouchMove();\n    }\n    if (rmTouchEnd) {\n      rmTouchEnd();\n    }\n    if (rmTouchCancel) {\n      rmTouchCancel();\n    }\n    rmTouchMove = rmTouchEnd = rmTouchCancel = undefined;\n  };\n  const stopMouse = () => {\n    if (rmMouseMove) {\n      rmMouseMove();\n    }\n    if (rmMouseUp) {\n      rmMouseUp();\n    }\n    rmMouseMove = rmMouseUp = undefined;\n  };\n  const stop = () => {\n    stopTouch();\n    stopMouse();\n  };\n  const enable = (isEnabled = true) => {\n    if (!isEnabled) {\n      if (rmTouchStart) {\n        rmTouchStart();\n      }\n      if (rmMouseStart) {\n        rmMouseStart();\n      }\n      rmTouchStart = rmMouseStart = undefined;\n      stop();\n    } else {\n      if (!rmTouchStart) {\n        rmTouchStart = addEventListener(el, 'touchstart', handleTouchStart, options);\n      }\n      if (!rmMouseStart) {\n        rmMouseStart = addEventListener(el, 'mousedown', handleMouseDown, options);\n      }\n    }\n  };\n  const destroy = () => {\n    enable(false);\n    pointerUp = pointerMove = pointerDown = undefined;\n  };\n  return {\n    enable,\n    stop,\n    destroy\n  };\n};\nconst getDocument = node => {\n  return node instanceof Document ? node : node.ownerDocument;\n};\nconst createPanRecognizer = (direction, thresh, maxAngle) => {\n  const radians = maxAngle * (Math.PI / 180);\n  const isDirX = direction === 'x';\n  const maxCosine = Math.cos(radians);\n  const threshold = thresh * thresh;\n  let startX = 0;\n  let startY = 0;\n  let dirty = false;\n  let isPan = 0;\n  return {\n    start(x, y) {\n      startX = x;\n      startY = y;\n      isPan = 0;\n      dirty = true;\n    },\n    detect(x, y) {\n      if (!dirty) {\n        return false;\n      }\n      const deltaX = x - startX;\n      const deltaY = y - startY;\n      const distance = deltaX * deltaX + deltaY * deltaY;\n      if (distance < threshold) {\n        return false;\n      }\n      const hypotenuse = Math.sqrt(distance);\n      const cosine = (isDirX ? deltaX : deltaY) / hypotenuse;\n      if (cosine > maxCosine) {\n        isPan = 1;\n      } else if (cosine < -maxCosine) {\n        isPan = -1;\n      } else {\n        isPan = 0;\n      }\n      dirty = false;\n      return true;\n    },\n    isGesture() {\n      return isPan !== 0;\n    },\n    getDirection() {\n      return isPan;\n    }\n  };\n};\n\n// TODO(FW-2832): types\nconst createGesture = config => {\n  let hasCapturedPan = false;\n  let hasStartedPan = false;\n  let hasFiredStart = true;\n  let isMoveQueued = false;\n  const finalConfig = Object.assign({\n    disableScroll: false,\n    direction: 'x',\n    gesturePriority: 0,\n    passive: true,\n    maxAngle: 40,\n    threshold: 10\n  }, config);\n  const canStart = finalConfig.canStart;\n  const onWillStart = finalConfig.onWillStart;\n  const onStart = finalConfig.onStart;\n  const onEnd = finalConfig.onEnd;\n  const notCaptured = finalConfig.notCaptured;\n  const onMove = finalConfig.onMove;\n  const threshold = finalConfig.threshold;\n  const passive = finalConfig.passive;\n  const blurOnStart = finalConfig.blurOnStart;\n  const detail = {\n    type: 'pan',\n    startX: 0,\n    startY: 0,\n    startTime: 0,\n    currentX: 0,\n    currentY: 0,\n    velocityX: 0,\n    velocityY: 0,\n    deltaX: 0,\n    deltaY: 0,\n    currentTime: 0,\n    event: undefined,\n    data: undefined\n  };\n  const pan = createPanRecognizer(finalConfig.direction, finalConfig.threshold, finalConfig.maxAngle);\n  const gesture = GESTURE_CONTROLLER.createGesture({\n    name: config.gestureName,\n    priority: config.gesturePriority,\n    disableScroll: config.disableScroll\n  });\n  const pointerDown = ev => {\n    const timeStamp = now(ev);\n    if (hasStartedPan || !hasFiredStart) {\n      return false;\n    }\n    updateDetail(ev, detail);\n    detail.startX = detail.currentX;\n    detail.startY = detail.currentY;\n    detail.startTime = detail.currentTime = timeStamp;\n    detail.velocityX = detail.velocityY = detail.deltaX = detail.deltaY = 0;\n    detail.event = ev;\n    // Check if gesture can start\n    if (canStart && canStart(detail) === false) {\n      return false;\n    }\n    // Release fallback\n    gesture.release();\n    // Start gesture\n    if (!gesture.start()) {\n      return false;\n    }\n    hasStartedPan = true;\n    if (threshold === 0) {\n      return tryToCapturePan();\n    }\n    pan.start(detail.startX, detail.startY);\n    return true;\n  };\n  const pointerMove = ev => {\n    // fast path, if gesture is currently captured\n    // do minimum job to get user-land even dispatched\n    if (hasCapturedPan) {\n      if (!isMoveQueued && hasFiredStart) {\n        isMoveQueued = true;\n        calcGestureData(detail, ev);\n        requestAnimationFrame(fireOnMove);\n      }\n      return;\n    }\n    // gesture is currently being detected\n    calcGestureData(detail, ev);\n    if (pan.detect(detail.currentX, detail.currentY)) {\n      if (!pan.isGesture() || !tryToCapturePan()) {\n        abortGesture();\n      }\n    }\n  };\n  const fireOnMove = () => {\n    // Since fireOnMove is called inside a RAF, onEnd() might be called,\n    // we must double check hasCapturedPan\n    if (!hasCapturedPan) {\n      return;\n    }\n    isMoveQueued = false;\n    if (onMove) {\n      onMove(detail);\n    }\n  };\n  const tryToCapturePan = () => {\n    if (!gesture.capture()) {\n      return false;\n    }\n    hasCapturedPan = true;\n    hasFiredStart = false;\n    // reset start position since the real user-land event starts here\n    // If the pan detector threshold is big, not resetting the start position\n    // will cause a jump in the animation equal to the detector threshold.\n    // the array of positions used to calculate the gesture velocity does not\n    // need to be cleaned, more points in the positions array always results in a\n    // more accurate value of the velocity.\n    detail.startX = detail.currentX;\n    detail.startY = detail.currentY;\n    detail.startTime = detail.currentTime;\n    if (onWillStart) {\n      onWillStart(detail).then(fireOnStart);\n    } else {\n      fireOnStart();\n    }\n    return true;\n  };\n  const blurActiveElement = () => {\n    if (typeof document !== 'undefined') {\n      const activeElement = document.activeElement;\n      if (activeElement === null || activeElement === void 0 ? void 0 : activeElement.blur) {\n        activeElement.blur();\n      }\n    }\n  };\n  const fireOnStart = () => {\n    if (blurOnStart) {\n      blurActiveElement();\n    }\n    if (onStart) {\n      onStart(detail);\n    }\n    hasFiredStart = true;\n  };\n  const reset = () => {\n    hasCapturedPan = false;\n    hasStartedPan = false;\n    isMoveQueued = false;\n    hasFiredStart = true;\n    gesture.release();\n  };\n  // END *************************\n  const pointerUp = ev => {\n    const tmpHasCaptured = hasCapturedPan;\n    const tmpHasFiredStart = hasFiredStart;\n    reset();\n    if (!tmpHasFiredStart) {\n      return;\n    }\n    calcGestureData(detail, ev);\n    // Try to capture press\n    if (tmpHasCaptured) {\n      if (onEnd) {\n        onEnd(detail);\n      }\n      return;\n    }\n    // Not captured any event\n    if (notCaptured) {\n      notCaptured(detail);\n    }\n  };\n  const pointerEvents = createPointerEvents(finalConfig.el, pointerDown, pointerMove, pointerUp, {\n    capture: false,\n    passive\n  });\n  const abortGesture = () => {\n    reset();\n    pointerEvents.stop();\n    if (notCaptured) {\n      notCaptured(detail);\n    }\n  };\n  return {\n    enable(enable = true) {\n      if (!enable) {\n        if (hasCapturedPan) {\n          pointerUp(undefined);\n        }\n        reset();\n      }\n      pointerEvents.enable(enable);\n    },\n    destroy() {\n      gesture.destroy();\n      pointerEvents.destroy();\n    }\n  };\n};\nconst calcGestureData = (detail, ev) => {\n  if (!ev) {\n    return;\n  }\n  const prevX = detail.currentX;\n  const prevY = detail.currentY;\n  const prevT = detail.currentTime;\n  updateDetail(ev, detail);\n  const currentX = detail.currentX;\n  const currentY = detail.currentY;\n  const timestamp = detail.currentTime = now(ev);\n  const timeDelta = timestamp - prevT;\n  if (timeDelta > 0 && timeDelta < 100) {\n    const velocityX = (currentX - prevX) / timeDelta;\n    const velocityY = (currentY - prevY) / timeDelta;\n    detail.velocityX = velocityX * 0.7 + detail.velocityX * 0.3;\n    detail.velocityY = velocityY * 0.7 + detail.velocityY * 0.3;\n  }\n  detail.deltaX = currentX - detail.startX;\n  detail.deltaY = currentY - detail.startY;\n  detail.event = ev;\n};\nconst updateDetail = (ev, detail) => {\n  // get X coordinates for either a mouse click\n  // or a touch depending on the given event\n  let x = 0;\n  let y = 0;\n  if (ev) {\n    const changedTouches = ev.changedTouches;\n    if (changedTouches && changedTouches.length > 0) {\n      const touch = changedTouches[0];\n      x = touch.clientX;\n      y = touch.clientY;\n    } else if (ev.pageX !== undefined) {\n      x = ev.pageX;\n      y = ev.pageY;\n    }\n  }\n  detail.currentX = x;\n  detail.currentY = y;\n};\nconst now = ev => {\n  return ev.timeStamp || Date.now();\n};\nexport { createGesture };"], "mappings": ";;;;;;;;;AAAA,IAKM,kBAuBA,iBAiBF,WACE,YAEA,qBAiHA,aAGA,qBAgDA,eAmMA,iBAsBA,cAmBA;AAhcN;AAAA;AAGA;AACA;AACA,IAAM,mBAAmB,CAAC,IAE1B,WAAW,UAAU,SAAS;AAG5B,YAAM,eAAe,gBAAgB,EAAE,IAAI;AAAA,QACzC,SAAS,CAAC,CAAC,KAAK;AAAA,QAChB,SAAS,CAAC,CAAC,KAAK;AAAA,MAClB,IAAI,CAAC,CAAC,KAAK;AACX,UAAI;AACJ,UAAI;AACJ,UAAI,GAAG,iCAAiC,GAAG;AACzC,cAAM;AACN,iBAAS;AAAA,MACX,OAAO;AACL,cAAM;AACN,iBAAS;AAAA,MACX;AACA,SAAG,GAAG,EAAE,WAAW,UAAU,YAAY;AACzC,aAAO,MAAM;AACX,WAAG,MAAM,EAAE,WAAW,UAAU,YAAY;AAAA,MAC9C;AAAA,IACF;AACA,IAAM,kBAAkB,UAAQ;AAC9B,UAAI,cAAc,QAAW;AAC3B,YAAI;AACF,gBAAM,OAAO,OAAO,eAAe,CAAC,GAAG,WAAW;AAAA,YAChD,KAAK,MAAM;AACT,0BAAY;AAAA,YACd;AAAA,UACF,CAAC;AACD,eAAK,iBAAiB,YAAY,MAAM;AACtC;AAAA,UACF,GAAG,IAAI;AAAA,QACT,SAAS,GAAG;AACV,sBAAY;AAAA,QACd;AAAA,MACF;AACA,aAAO,CAAC,CAAC;AAAA,IACX;AAEA,IAAM,aAAa;AAEnB,IAAM,sBAAsB,CAAC,IAAI,aAAa,aAAa,WAAW,YAAY;AAChF,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,iBAAiB;AACrB,YAAM,mBAAmB,QAAM;AAC7B,yBAAiB,KAAK,IAAI,IAAI;AAC9B,YAAI,CAAC,YAAY,EAAE,GAAG;AACpB;AAAA,QACF;AACA,YAAI,CAAC,eAAe,aAAa;AAC/B,wBAAc,iBAAiB,IAAI,aAAa,aAAa,OAAO;AAAA,QACtE;AAUA,YAAI,CAAC,YAAY;AACf,uBAAa,iBAAiB,GAAG,QAAQ,YAAY,gBAAgB,OAAO;AAAA,QAC9E;AACA,YAAI,CAAC,eAAe;AAClB,0BAAgB,iBAAiB,GAAG,QAAQ,eAAe,gBAAgB,OAAO;AAAA,QACpF;AAAA,MACF;AACA,YAAM,kBAAkB,QAAM;AAC5B,YAAI,iBAAiB,KAAK,IAAI,GAAG;AAC/B;AAAA,QACF;AACA,YAAI,CAAC,YAAY,EAAE,GAAG;AACpB;AAAA,QACF;AACA,YAAI,CAAC,eAAe,aAAa;AAC/B,wBAAc,iBAAiB,YAAY,EAAE,GAAG,aAAa,aAAa,OAAO;AAAA,QACnF;AACA,YAAI,CAAC,WAAW;AACd,sBAAY,iBAAiB,YAAY,EAAE,GAAG,WAAW,eAAe,OAAO;AAAA,QACjF;AAAA,MACF;AACA,YAAM,iBAAiB,QAAM;AAC3B,kBAAU;AACV,YAAI,WAAW;AACb,oBAAU,EAAE;AAAA,QACd;AAAA,MACF;AACA,YAAM,gBAAgB,QAAM;AAC1B,kBAAU;AACV,YAAI,WAAW;AACb,oBAAU,EAAE;AAAA,QACd;AAAA,MACF;AACA,YAAM,YAAY,MAAM;AACtB,YAAI,aAAa;AACf,sBAAY;AAAA,QACd;AACA,YAAI,YAAY;AACd,qBAAW;AAAA,QACb;AACA,YAAI,eAAe;AACjB,wBAAc;AAAA,QAChB;AACA,sBAAc,aAAa,gBAAgB;AAAA,MAC7C;AACA,YAAM,YAAY,MAAM;AACtB,YAAI,aAAa;AACf,sBAAY;AAAA,QACd;AACA,YAAI,WAAW;AACb,oBAAU;AAAA,QACZ;AACA,sBAAc,YAAY;AAAA,MAC5B;AACA,YAAM,OAAO,MAAM;AACjB,kBAAU;AACV,kBAAU;AAAA,MACZ;AACA,YAAM,SAAS,CAAC,YAAY,SAAS;AACnC,YAAI,CAAC,WAAW;AACd,cAAI,cAAc;AAChB,yBAAa;AAAA,UACf;AACA,cAAI,cAAc;AAChB,yBAAa;AAAA,UACf;AACA,yBAAe,eAAe;AAC9B,eAAK;AAAA,QACP,OAAO;AACL,cAAI,CAAC,cAAc;AACjB,2BAAe,iBAAiB,IAAI,cAAc,kBAAkB,OAAO;AAAA,UAC7E;AACA,cAAI,CAAC,cAAc;AACjB,2BAAe,iBAAiB,IAAI,aAAa,iBAAiB,OAAO;AAAA,UAC3E;AAAA,QACF;AAAA,MACF;AACA,YAAM,UAAU,MAAM;AACpB,eAAO,KAAK;AACZ,oBAAY,cAAc,cAAc;AAAA,MAC1C;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,IAAM,cAAc,UAAQ;AAC1B,aAAO,gBAAgB,WAAW,OAAO,KAAK;AAAA,IAChD;AACA,IAAM,sBAAsB,CAAC,WAAW,QAAQ,aAAa;AAC3D,YAAM,UAAU,YAAY,KAAK,KAAK;AACtC,YAAM,SAAS,cAAc;AAC7B,YAAM,YAAY,KAAK,IAAI,OAAO;AAClC,YAAM,YAAY,SAAS;AAC3B,UAAI,SAAS;AACb,UAAI,SAAS;AACb,UAAI,QAAQ;AACZ,UAAI,QAAQ;AACZ,aAAO;AAAA,QACL,MAAM,GAAG,GAAG;AACV,mBAAS;AACT,mBAAS;AACT,kBAAQ;AACR,kBAAQ;AAAA,QACV;AAAA,QACA,OAAO,GAAG,GAAG;AACX,cAAI,CAAC,OAAO;AACV,mBAAO;AAAA,UACT;AACA,gBAAM,SAAS,IAAI;AACnB,gBAAM,SAAS,IAAI;AACnB,gBAAM,WAAW,SAAS,SAAS,SAAS;AAC5C,cAAI,WAAW,WAAW;AACxB,mBAAO;AAAA,UACT;AACA,gBAAM,aAAa,KAAK,KAAK,QAAQ;AACrC,gBAAM,UAAU,SAAS,SAAS,UAAU;AAC5C,cAAI,SAAS,WAAW;AACtB,oBAAQ;AAAA,UACV,WAAW,SAAS,CAAC,WAAW;AAC9B,oBAAQ;AAAA,UACV,OAAO;AACL,oBAAQ;AAAA,UACV;AACA,kBAAQ;AACR,iBAAO;AAAA,QACT;AAAA,QACA,YAAY;AACV,iBAAO,UAAU;AAAA,QACnB;AAAA,QACA,eAAe;AACb,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAGA,IAAM,gBAAgB,YAAU;AAC9B,UAAI,iBAAiB;AACrB,UAAI,gBAAgB;AACpB,UAAI,gBAAgB;AACpB,UAAI,eAAe;AACnB,YAAM,cAAc,OAAO,OAAO;AAAA,QAChC,eAAe;AAAA,QACf,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW;AAAA,MACb,GAAG,MAAM;AACT,YAAM,WAAW,YAAY;AAC7B,YAAM,cAAc,YAAY;AAChC,YAAM,UAAU,YAAY;AAC5B,YAAM,QAAQ,YAAY;AAC1B,YAAM,cAAc,YAAY;AAChC,YAAM,SAAS,YAAY;AAC3B,YAAM,YAAY,YAAY;AAC9B,YAAM,UAAU,YAAY;AAC5B,YAAM,cAAc,YAAY;AAChC,YAAM,SAAS;AAAA,QACb,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,UAAU;AAAA,QACV,UAAU;AAAA,QACV,WAAW;AAAA,QACX,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AACA,YAAM,MAAM,oBAAoB,YAAY,WAAW,YAAY,WAAW,YAAY,QAAQ;AAClG,YAAM,UAAU,mBAAmB,cAAc;AAAA,QAC/C,MAAM,OAAO;AAAA,QACb,UAAU,OAAO;AAAA,QACjB,eAAe,OAAO;AAAA,MACxB,CAAC;AACD,YAAM,cAAc,QAAM;AACxB,cAAM,YAAY,IAAI,EAAE;AACxB,YAAI,iBAAiB,CAAC,eAAe;AACnC,iBAAO;AAAA,QACT;AACA,qBAAa,IAAI,MAAM;AACvB,eAAO,SAAS,OAAO;AACvB,eAAO,SAAS,OAAO;AACvB,eAAO,YAAY,OAAO,cAAc;AACxC,eAAO,YAAY,OAAO,YAAY,OAAO,SAAS,OAAO,SAAS;AACtE,eAAO,QAAQ;AAEf,YAAI,YAAY,SAAS,MAAM,MAAM,OAAO;AAC1C,iBAAO;AAAA,QACT;AAEA,gBAAQ,QAAQ;AAEhB,YAAI,CAAC,QAAQ,MAAM,GAAG;AACpB,iBAAO;AAAA,QACT;AACA,wBAAgB;AAChB,YAAI,cAAc,GAAG;AACnB,iBAAO,gBAAgB;AAAA,QACzB;AACA,YAAI,MAAM,OAAO,QAAQ,OAAO,MAAM;AACtC,eAAO;AAAA,MACT;AACA,YAAM,cAAc,QAAM;AAGxB,YAAI,gBAAgB;AAClB,cAAI,CAAC,gBAAgB,eAAe;AAClC,2BAAe;AACf,4BAAgB,QAAQ,EAAE;AAC1B,kCAAsB,UAAU;AAAA,UAClC;AACA;AAAA,QACF;AAEA,wBAAgB,QAAQ,EAAE;AAC1B,YAAI,IAAI,OAAO,OAAO,UAAU,OAAO,QAAQ,GAAG;AAChD,cAAI,CAAC,IAAI,UAAU,KAAK,CAAC,gBAAgB,GAAG;AAC1C,yBAAa;AAAA,UACf;AAAA,QACF;AAAA,MACF;AACA,YAAM,aAAa,MAAM;AAGvB,YAAI,CAAC,gBAAgB;AACnB;AAAA,QACF;AACA,uBAAe;AACf,YAAI,QAAQ;AACV,iBAAO,MAAM;AAAA,QACf;AAAA,MACF;AACA,YAAM,kBAAkB,MAAM;AAC5B,YAAI,CAAC,QAAQ,QAAQ,GAAG;AACtB,iBAAO;AAAA,QACT;AACA,yBAAiB;AACjB,wBAAgB;AAOhB,eAAO,SAAS,OAAO;AACvB,eAAO,SAAS,OAAO;AACvB,eAAO,YAAY,OAAO;AAC1B,YAAI,aAAa;AACf,sBAAY,MAAM,EAAE,KAAK,WAAW;AAAA,QACtC,OAAO;AACL,sBAAY;AAAA,QACd;AACA,eAAO;AAAA,MACT;AACA,YAAM,oBAAoB,MAAM;AAC9B,YAAI,OAAO,aAAa,aAAa;AACnC,gBAAM,gBAAgB,SAAS;AAC/B,cAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,MAAM;AACpF,0BAAc,KAAK;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AACA,YAAM,cAAc,MAAM;AACxB,YAAI,aAAa;AACf,4BAAkB;AAAA,QACpB;AACA,YAAI,SAAS;AACX,kBAAQ,MAAM;AAAA,QAChB;AACA,wBAAgB;AAAA,MAClB;AACA,YAAM,QAAQ,MAAM;AAClB,yBAAiB;AACjB,wBAAgB;AAChB,uBAAe;AACf,wBAAgB;AAChB,gBAAQ,QAAQ;AAAA,MAClB;AAEA,YAAM,YAAY,QAAM;AACtB,cAAM,iBAAiB;AACvB,cAAM,mBAAmB;AACzB,cAAM;AACN,YAAI,CAAC,kBAAkB;AACrB;AAAA,QACF;AACA,wBAAgB,QAAQ,EAAE;AAE1B,YAAI,gBAAgB;AAClB,cAAI,OAAO;AACT,kBAAM,MAAM;AAAA,UACd;AACA;AAAA,QACF;AAEA,YAAI,aAAa;AACf,sBAAY,MAAM;AAAA,QACpB;AAAA,MACF;AACA,YAAM,gBAAgB,oBAAoB,YAAY,IAAI,aAAa,aAAa,WAAW;AAAA,QAC7F,SAAS;AAAA,QACT;AAAA,MACF,CAAC;AACD,YAAM,eAAe,MAAM;AACzB,cAAM;AACN,sBAAc,KAAK;AACnB,YAAI,aAAa;AACf,sBAAY,MAAM;AAAA,QACpB;AAAA,MACF;AACA,aAAO;AAAA,QACL,OAAO,SAAS,MAAM;AACpB,cAAI,CAAC,QAAQ;AACX,gBAAI,gBAAgB;AAClB,wBAAU,MAAS;AAAA,YACrB;AACA,kBAAM;AAAA,UACR;AACA,wBAAc,OAAO,MAAM;AAAA,QAC7B;AAAA,QACA,UAAU;AACR,kBAAQ,QAAQ;AAChB,wBAAc,QAAQ;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,IAAM,kBAAkB,CAAC,QAAQ,OAAO;AACtC,UAAI,CAAC,IAAI;AACP;AAAA,MACF;AACA,YAAM,QAAQ,OAAO;AACrB,YAAM,QAAQ,OAAO;AACrB,YAAM,QAAQ,OAAO;AACrB,mBAAa,IAAI,MAAM;AACvB,YAAM,WAAW,OAAO;AACxB,YAAM,WAAW,OAAO;AACxB,YAAM,YAAY,OAAO,cAAc,IAAI,EAAE;AAC7C,YAAM,YAAY,YAAY;AAC9B,UAAI,YAAY,KAAK,YAAY,KAAK;AACpC,cAAM,aAAa,WAAW,SAAS;AACvC,cAAM,aAAa,WAAW,SAAS;AACvC,eAAO,YAAY,YAAY,MAAM,OAAO,YAAY;AACxD,eAAO,YAAY,YAAY,MAAM,OAAO,YAAY;AAAA,MAC1D;AACA,aAAO,SAAS,WAAW,OAAO;AAClC,aAAO,SAAS,WAAW,OAAO;AAClC,aAAO,QAAQ;AAAA,IACjB;AACA,IAAM,eAAe,CAAC,IAAI,WAAW;AAGnC,UAAI,IAAI;AACR,UAAI,IAAI;AACR,UAAI,IAAI;AACN,cAAM,iBAAiB,GAAG;AAC1B,YAAI,kBAAkB,eAAe,SAAS,GAAG;AAC/C,gBAAM,QAAQ,eAAe,CAAC;AAC9B,cAAI,MAAM;AACV,cAAI,MAAM;AAAA,QACZ,WAAW,GAAG,UAAU,QAAW;AACjC,cAAI,GAAG;AACP,cAAI,GAAG;AAAA,QACT;AAAA,MACF;AACA,aAAO,WAAW;AAClB,aAAO,WAAW;AAAA,IACpB;AACA,IAAM,MAAM,QAAM;AAChB,aAAO,GAAG,aAAa,KAAK,IAAI;AAAA,IAClC;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}