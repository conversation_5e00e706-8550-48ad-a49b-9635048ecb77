import {
  init_esm_browser,
  v4_default
} from "./chunk-ZCUEDWU7.js";
import {
  ApiService,
  BehaviorSubject,
  DataStorageService,
  HttpClient,
  Injectable,
  NetworkService,
  Platform,
  SessionService,
  SyncStatus,
  VehicleService,
  firstValueFrom,
  init_api_service,
  init_core,
  init_data_storage_service,
  init_esm,
  init_http,
  init_ionic_angular,
  init_network_service,
  init_session_service,
  init_sync_model,
  init_vehicle_service,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-2IV7NJLP.js";
import {
  init_dist,
  registerPlugin
} from "./chunk-WY354WYL.js";
import {
  __async,
  __esm,
  __spreadProps,
  __spreadValues
} from "./chunk-4W6HR7MY.js";

// node_modules/@capacitor/synapse/dist/synapse.mjs
function s(t) {
  t.CapacitorUtils.Synapse = new Proxy({}, {
    get(e, n) {
      return new Proxy({}, {
        get(w, o) {
          return (c, p, r) => {
            const i = t.Capacitor.Plugins[n];
            if (i === void 0) {
              r(new Error(`Capacitor plugin ${n} not found`));
              return;
            }
            if (typeof i[o] != "function") {
              r(new Error(`Method ${o} not found in Capacitor plugin ${n}`));
              return;
            }
            (() => __async(null, null, function* () {
              try {
                const a = yield i[o](c);
                p(a);
              } catch (a) {
                r(a);
              }
            }))();
          };
        }
      });
    }
  });
}
function u(t) {
  t.CapacitorUtils.Synapse = new Proxy({}, {
    get(e, n) {
      return t.cordova.plugins[n];
    }
  });
}
function f(t = false) {
  typeof window > "u" || (window.CapacitorUtils = window.CapacitorUtils || {}, window.Capacitor !== void 0 && !t ? s(window) : window.cordova !== void 0 && u(window));
}
var init_synapse = __esm({
  "node_modules/@capacitor/synapse/dist/synapse.mjs"() {
    "use strict";
  }
});

// node_modules/@capacitor/geolocation/dist/esm/definitions.js
var init_definitions = __esm({
  "node_modules/@capacitor/geolocation/dist/esm/definitions.js"() {
    "use strict";
  }
});

// node_modules/@capacitor/geolocation/dist/esm/index.js
var Geolocation;
var init_esm2 = __esm({
  "node_modules/@capacitor/geolocation/dist/esm/index.js"() {
    "use strict";
    init_dist();
    init_synapse();
    init_definitions();
    Geolocation = registerPlugin("Geolocation", {
      web: () => import("./web-B46OEUWN.js").then((m) => new m.GeolocationWeb())
    });
    f();
  }
});

// node_modules/@capacitor/motion/dist/esm/definitions.js
var init_definitions2 = __esm({
  "node_modules/@capacitor/motion/dist/esm/definitions.js"() {
    "use strict";
  }
});

// node_modules/@capacitor/motion/dist/esm/index.js
var Motion;
var init_esm3 = __esm({
  "node_modules/@capacitor/motion/dist/esm/index.js"() {
    "use strict";
    init_dist();
    init_definitions2();
    Motion = registerPlugin("Motion", {
      android: () => import("./web-B4UY27XM.js").then((m) => new m.MotionWeb()),
      ios: () => import("./web-B4UY27XM.js").then((m) => new m.MotionWeb()),
      web: () => import("./web-B4UY27XM.js").then((m) => new m.MotionWeb())
    });
  }
});

// src/app/core/services/journeyinfo.service.ts
var _JourneyInfoService, JourneyInfoService;
var init_journeyinfo_service = __esm({
  "src/app/core/services/journeyinfo.service.ts"() {
    "use strict";
    init_core();
    init_esm2();
    init_esm3();
    init_esm();
    init_core();
    init_data_storage_service();
    _JourneyInfoService = class _JourneyInfoService {
      constructor(dataStorageService) {
        this.dataStorageService = dataStorageService;
        this.journeyId = "";
        this.trackingInterval = null;
        this.trackingStartTime = 0;
        this.totalTimeAboveSpeed = 0;
        this.previousSpeed = 0;
        this.previousSpeedTime = 0;
        this.trackingActiveSubject = new BehaviorSubject(false);
        this.trackingActive$ = this.trackingActiveSubject.asObservable();
        this.storage = this.dataStorageService;
      }
      // Função para iniciar o rastreamento
      startTracking(journeyId) {
        this.trackingStartTime = Date.now();
        this.totalTimeAboveSpeed = 0;
        this.journeyId = journeyId;
        this.previousSpeed = 0;
        this.previousSpeedTime = 0;
        this.trackingActiveSubject.next(true);
        this.trackingInterval = setInterval(() => __async(this, null, function* () {
          try {
            console.log("Executando ciclo de rastreamento...");
            const location = yield this.getCurrentLocation();
            if (location) {
              console.log("Localiza\xE7\xE3o obtida:", location);
              try {
                console.log("Verificando freada brusca...");
                const hardBreakDetected = yield this.checkHardBreak(location);
                console.log("Resultado checkHardBreak:", hardBreakDetected);
              } catch (error) {
                console.error("Erro em checkHardBreak:", error);
              }
              try {
                console.log("Verificando alta velocidade...");
                const highVelocityDetected = yield this.checkHighVelocity(location);
                console.log("Resultado checkHighVelocity:", highVelocityDetected);
              } catch (error) {
                console.error("Erro em checkHighVelocity:", error);
              }
              try {
                console.log("Salvando localiza\xE7\xE3o...");
                yield this.saveLocation(location);
                console.log("Localiza\xE7\xE3o salva com sucesso");
              } catch (error) {
                console.error("Erro ao salvar localiza\xE7\xE3o:", error);
              }
            } else {
              console.log("N\xE3o foi poss\xEDvel obter localiza\xE7\xE3o");
            }
          } catch (error) {
            console.error("Erro no ciclo de rastreamento:", error);
          }
        }), 1e4);
        console.log("Rastreamento iniciado para viagem:", journeyId);
      }
      // Função para parar o rastreamento
      stopTracking(journeyId) {
        if (this.trackingInterval) {
          clearInterval(this.trackingInterval);
          this.trackingInterval = null;
          this.trackingActiveSubject.next(false);
          console.log("Rastreamento parado para viagem:", journeyId);
        }
      }
      // Função para obter a localização atual
      getCurrentLocation() {
        return __async(this, null, function* () {
          try {
            const coordinates = yield Geolocation.getCurrentPosition({
              enableHighAccuracy: true,
              timeout: 15e3,
              maximumAge: 5e3
              // Cache de 5 segundos para evitar leituras muito frequentes
            });
            const accuracy = coordinates.coords.accuracy;
            if (accuracy && accuracy > 50) {
              console.log(`Precis\xE3o GPS baixa: ${accuracy}m, tentando novamente...`);
              const retryCoordinates = yield Geolocation.getCurrentPosition({
                enableHighAccuracy: true,
                timeout: 2e4,
                maximumAge: 0
                // Força nova leitura
              });
              if (retryCoordinates.coords.accuracy && retryCoordinates.coords.accuracy <= 50) {
                coordinates.coords = retryCoordinates.coords;
              }
            }
            const location = {
              id: this.generateUniqueId(),
              journeyId: this.journeyId,
              latitude: coordinates.coords.latitude,
              longitude: coordinates.coords.longitude,
              timestamp: (/* @__PURE__ */ new Date()).toISOString(),
              // Adicionar informações de precisão se disponíveis
              accuracy: coordinates.coords.accuracy || void 0,
              altitude: coordinates.coords.altitude || void 0,
              speed: coordinates.coords.speed || void 0
            };
            console.log(`Localiza\xE7\xE3o obtida: ${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)} (precis\xE3o: ${accuracy}m)`);
            return location;
          } catch (error) {
            console.error("Erro ao obter a localiza\xE7\xE3o:", error);
            return null;
          }
        });
      }
      // Função para obter a velocidade atual (em km/h)
      getCurrentSpeed() {
        return __async(this, null, function* () {
          try {
            const position = yield Geolocation.getCurrentPosition();
            const speed = position.coords.speed ? position.coords.speed * 3.6 : 0;
            return speed;
          } catch (error) {
            console.error("Erro ao obter a velocidade:", error);
            return 0;
          }
        });
      }
      // Função para salvar a localização no banco de dados
      saveLocation(location) {
        return __async(this, null, function* () {
          try {
            yield this.storage.insert("journeyInfo", location);
            console.log("Localiza\xE7\xE3o salva:", location);
          } catch (error) {
            console.error("Erro ao salvar a localiza\xE7\xE3o:", error);
          }
        });
      }
      // Função para gerar um ID único para cada localização (pode ser melhorada)
      generateUniqueId() {
        return Math.random().toString(36).substring(2, 15);
      }
      checkHardBreak(location) {
        return __async(this, null, function* () {
          try {
            const currentSpeed = yield this.getCurrentSpeed();
            const currentTime = Date.now();
            if (this.previousSpeed > 0 && this.previousSpeedTime > 0) {
              const speedDifference = this.previousSpeed - currentSpeed;
              const timeDifference = (currentTime - this.previousSpeedTime) / 1e3;
              if (timeDifference > 0) {
                const deceleration = speedDifference / timeDifference;
                console.log(`Velocidade anterior: ${this.previousSpeed} km/h, Atual: ${currentSpeed} km/h`);
                console.log(`Desacelera\xE7\xE3o: ${deceleration} km/h/s`);
                if (deceleration > 15) {
                  console.log("Freada brusca detectada por velocidade!");
                  location.occurrenceType = "HardBreak";
                  yield this.saveLocation(location);
                  this.previousSpeed = currentSpeed;
                  this.previousSpeedTime = currentTime;
                  return true;
                }
              }
            }
            this.previousSpeed = currentSpeed;
            this.previousSpeedTime = currentTime;
            return yield this.checkHardBreakWithMotionSensor(location);
          } catch (error) {
            console.error("Erro em checkHardBreak:", error);
            return false;
          }
        });
      }
      checkHardBreakWithMotionSensor(location) {
        return __async(this, null, function* () {
          return new Promise((resolve) => __async(this, null, function* () {
            let isResolved = false;
            let listenerHandle = null;
            try {
              const isMotionAvailable = yield this.isMotionSensorAvailable();
              if (!isMotionAvailable) {
                console.log("Sensor de movimento n\xE3o dispon\xEDvel");
                resolve(false);
                return;
              }
              listenerHandle = yield Motion.addListener("accel", (event) => {
                const { acceleration } = event;
                if ((acceleration == null ? void 0 : acceleration.x) && acceleration.x < -10) {
                  console.log("Freada brusca detectada por sensor de movimento!");
                  location.occurrenceType = "HardBreak";
                  this.saveLocation(location);
                  if (listenerHandle) {
                    listenerHandle.remove();
                  }
                  if (!isResolved) {
                    isResolved = true;
                    resolve(true);
                  }
                }
              });
              setTimeout(() => {
                if (!isResolved) {
                  isResolved = true;
                  if (listenerHandle) {
                    listenerHandle.remove();
                  }
                  resolve(false);
                }
              }, 1e3);
            } catch (error) {
              console.error("Erro ao configurar listener de acelera\xE7\xE3o:", error);
              if (!isResolved) {
                isResolved = true;
                resolve(false);
              }
            }
          }));
        });
      }
      isMotionSensorAvailable() {
        return __async(this, null, function* () {
          try {
            const testListener = yield Motion.addListener("accel", () => {
            });
            if (testListener) {
              testListener.remove();
              return true;
            }
            return false;
          } catch (error) {
            console.error("Erro ao verificar disponibilidade do sensor de movimento:", error);
            return false;
          }
        });
      }
      checkHighVelocity(location) {
        return __async(this, null, function* () {
          const speedKmH = yield this.getCurrentSpeed();
          if (speedKmH > 120) {
            console.log("Alta velocidade detectada!");
            location.occurrenceType = "Acceleration";
            this.saveLocation(location);
            return true;
          }
          return false;
        });
      }
    };
    _JourneyInfoService.\u0275fac = function JourneyInfoService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _JourneyInfoService)(\u0275\u0275inject(DataStorageService));
    };
    _JourneyInfoService.\u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _JourneyInfoService, factory: _JourneyInfoService.\u0275fac, providedIn: "root" });
    JourneyInfoService = _JourneyInfoService;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(JourneyInfoService, [{
        type: Injectable,
        args: [{
          providedIn: "root"
        }]
      }], () => [{ type: DataStorageService }], null);
    })();
  }
});

// src/app/core/services/geocoding.service.ts
var _GeocodingService, GeocodingService;
var init_geocoding_service = __esm({
  "src/app/core/services/geocoding.service.ts"() {
    "use strict";
    init_core();
    init_core();
    _GeocodingService = class _GeocodingService {
      constructor() {
        this.addressCache = /* @__PURE__ */ new Map();
      }
      /**
       * Converte coordenadas em endereço usando múltiplas estratégias
       */
      reverseGeocode(lat, lon) {
        return __async(this, null, function* () {
          try {
            console.log(`Iniciando reverse geocoding para: ${lat}, ${lon}`);
            let address = yield this.tryBigDataCloudAPI(lat, lon);
            if (address && address !== "Endere\xE7o n\xE3o encontrado") {
              return address;
            }
            address = yield this.tryOpenCageAPI(lat, lon);
            if (address && address !== "Endere\xE7o n\xE3o encontrado") {
              return address;
            }
            address = yield this.tryNominatimWithProxy(lat, lon);
            if (address && address !== "Endere\xE7o n\xE3o encontrado") {
              return address;
            }
            return this.formatCoordinatesAsAddress(lat, lon);
          } catch (error) {
            console.error("Erro geral no reverse geocoding:", error);
            return this.formatCoordinatesAsAddress(lat, lon);
          }
        });
      }
      /**
       * API BigDataCloud - Gratuita e suporta CORS
       */
      tryBigDataCloudAPI(lat, lon) {
        return __async(this, null, function* () {
          try {
            const url = `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lon}&localityLanguage=pt`;
            console.log("Tentando BigDataCloud API...");
            const response = yield fetch(url, {
              method: "GET",
              headers: {
                "Accept": "application/json"
              }
            });
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = yield response.json();
            if (data) {
              const addressParts = [];
              if (data.locality)
                addressParts.push(data.locality);
              if (data.city && data.city !== data.locality)
                addressParts.push(data.city);
              if (data.principalSubdivision)
                addressParts.push(data.principalSubdivision);
              if (data.countryName)
                addressParts.push(data.countryName);
              if (addressParts.length > 0) {
                const address = addressParts.join(", ");
                console.log("Endere\xE7o obtido via BigDataCloud:", address);
                return address;
              }
            }
            return "Endere\xE7o n\xE3o encontrado";
          } catch (error) {
            console.error("Erro na BigDataCloud API:", error);
            return "Endere\xE7o n\xE3o encontrado";
          }
        });
      }
      /**
       * API OpenCage - Requer chave API (opcional)
       */
      tryOpenCageAPI(lat, lon) {
        return __async(this, null, function* () {
          try {
            const apiKey = "YOUR_API_KEY";
            if (!apiKey || apiKey === "YOUR_API_KEY") {
              console.log("OpenCage API key n\xE3o configurada, pulando...");
              return "Endere\xE7o n\xE3o encontrado";
            }
            const url = `https://api.opencagedata.com/geocode/v1/json?q=${lat}+${lon}&key=${apiKey}&language=pt&pretty=1`;
            console.log("Tentando OpenCage API...");
            const response = yield fetch(url);
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = yield response.json();
            if (data && data.results && data.results.length > 0) {
              const result = data.results[0];
              const address = result.formatted;
              console.log("Endere\xE7o obtido via OpenCage:", address);
              return address;
            }
            return "Endere\xE7o n\xE3o encontrado";
          } catch (error) {
            console.error("Erro na OpenCage API:", error);
            return "Endere\xE7o n\xE3o encontrado";
          }
        });
      }
      /**
       * Nominatim com proxy CORS
       */
      tryNominatimWithProxy(lat, lon) {
        return __async(this, null, function* () {
          try {
            const proxyUrl = "https://api.allorigins.win/get?url=";
            const targetUrl = encodeURIComponent(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lon}&addressdetails=1&accept-language=pt-BR,pt,en`);
            const url = proxyUrl + targetUrl;
            console.log("Tentando Nominatim com proxy...");
            const response = yield fetch(url, {
              method: "GET",
              headers: {
                "Accept": "application/json"
              }
            });
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }
            const proxyData = yield response.json();
            if (proxyData && proxyData.contents) {
              const data = JSON.parse(proxyData.contents);
              if (data && data.display_name) {
                console.log("Endere\xE7o obtido via Nominatim (proxy):", data.display_name);
                return data.display_name;
              }
            }
            return "Endere\xE7o n\xE3o encontrado";
          } catch (error) {
            console.error("Erro no Nominatim com proxy:", error);
            return "Endere\xE7o n\xE3o encontrado";
          }
        });
      }
      /**
       * Formata coordenadas como endereço quando todas as APIs falham
       */
      formatCoordinatesAsAddress(lat, lon) {
        const latDirection = lat >= 0 ? "N" : "S";
        const lonDirection = lon >= 0 ? "L" : "O";
        const latFormatted = Math.abs(lat).toFixed(6);
        const lonFormatted = Math.abs(lon).toFixed(6);
        return `${latFormatted}\xB0${latDirection}, ${lonFormatted}\xB0${lonDirection}`;
      }
      /**
       * Verifica se uma string é um endereço válido ou apenas coordenadas
       */
      isValidAddress(address) {
        const coordinatePattern = /^[\d\.\-°NSLO,\s]+$/;
        return !coordinatePattern.test(address);
      }
      getReverseGeocodeWithCache(lat, lon) {
        return __async(this, null, function* () {
          const key = `${lat.toFixed(6)},${lon.toFixed(6)}`;
          if (this.addressCache.has(key)) {
            console.log("Endere\xE7o obtido do cache:", this.addressCache.get(key));
            return this.addressCache.get(key);
          }
          const address = yield this.reverseGeocode(lat, lon);
          this.addressCache.set(key, address);
          return address;
        });
      }
      /**
       * Limpa o cache de endereços
       */
      clearCache() {
        this.addressCache.clear();
        console.log("Cache de endere\xE7os limpo");
      }
    };
    _GeocodingService.\u0275fac = function GeocodingService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _GeocodingService)();
    };
    _GeocodingService.\u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _GeocodingService, factory: _GeocodingService.\u0275fac, providedIn: "root" });
    GeocodingService = _GeocodingService;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(GeocodingService, [{
        type: Injectable,
        args: [{
          providedIn: "root"
        }]
      }], () => [], null);
    })();
  }
});

// src/app/core/services/journey-storage.service.ts
var _JourneyStorageService, JourneyStorageService;
var init_journey_storage_service = __esm({
  "src/app/core/services/journey-storage.service.ts"() {
    "use strict";
    init_core();
    init_esm_browser();
    init_sync_model();
    init_esm();
    init_core();
    init_session_service();
    init_data_storage_service();
    init_ionic_angular();
    init_journeyinfo_service();
    init_vehicle_service();
    init_network_service();
    init_http();
    init_api_service();
    init_geocoding_service();
    _JourneyStorageService = class _JourneyStorageService {
      constructor(sessionService, dataStorageService, platform, journeyInfoService, vehicleService, networkService, http, apiService, geocodingService) {
        this.sessionService = sessionService;
        this.dataStorageService = dataStorageService;
        this.platform = platform;
        this.journeyInfoService = journeyInfoService;
        this.vehicleService = vehicleService;
        this.networkService = networkService;
        this.http = http;
        this.apiService = apiService;
        this.geocodingService = geocodingService;
        this.tableName = "journeys";
        this.isNative = this.platform.is("capacitor") || this.platform.is("cordova");
      }
      init() {
        return __async(this, null, function* () {
          yield this.dataStorageService.init();
        });
      }
      /**
       * Starts a new journey if the user has at least one vehicle
       * @returns The journey ID if successful, null if no vehicles exist
       */
      startJourney() {
        return __async(this, null, function* () {
          const userId = yield this.sessionService.getUserId();
          const hasVehicles = yield this.vehicleService.hasVehicles(userId);
          if (!hasVehicles) {
            console.error("Cannot start journey: No vehicles registered for this user");
            return null;
          }
          const primaryVehicle = yield this.vehicleService.getPrimaryVehicle(userId);
          if (!primaryVehicle) {
            console.error("Cannot start journey: No primary vehicle set");
            return null;
          }
          const id = v4_default();
          const startDate = (/* @__PURE__ */ new Date()).toISOString();
          const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingCreate : SyncStatus.PendingCreate;
          yield this.dataStorageService.insert(this.tableName, {
            id,
            startDate,
            userId,
            vehicleId: primaryVehicle.id,
            syncStatus,
            lastSyncDate: null
          });
          this.journeyInfoService.startTracking(id);
          return id;
        });
      }
      endJourney(journeyId) {
        return __async(this, null, function* () {
          this.journeyInfoService.stopTracking(journeyId);
          const endDate = (/* @__PURE__ */ new Date()).toISOString();
          console.log(`Finalizando viagem ${journeyId}...`);
          const result = yield this.dataStorageService.select("journeyInfo", `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);
          let infosJourney = [];
          if (Array.isArray(result)) {
            infosJourney = result;
          } else if (result) {
            try {
              infosJourney = Array.isArray(result) ? result : [];
            } catch (error) {
              console.error("Error converting journey info to array:", error);
            }
          }
          console.log(`Total de pontos coletados: ${infosJourney.length}`);
          yield this.loadAndSaveAddressesForJourney(infosJourney);
          let totalDistance = 0;
          if (infosJourney && infosJourney.length > 1) {
            const filteredLocations = this.filterLocationsByAccuracy(infosJourney);
            console.log(`Pontos ap\xF3s filtragem: ${filteredLocations.length}`);
            totalDistance = this.calculateJourneyDistance(filteredLocations);
            console.log(`Dist\xE2ncia calculada: ${(totalDistance / 1e3).toFixed(3)} km`);
          } else {
            console.log("Viagem sem pontos suficientes para calcular dist\xE2ncia");
          }
          const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingUpdate : SyncStatus.PendingUpdate;
          yield this.dataStorageService.update(this.tableName, {
            endDate,
            distance: totalDistance / 1e3,
            // Convert meters to kilometers
            syncStatus,
            lastSyncDate: null
          }, `id = '${journeyId}'`);
          console.log(`Viagem ${journeyId} finalizada com ${(totalDistance / 1e3).toFixed(3)} km`);
        });
      }
      addLocation(journeyId, latitude, longitude) {
        return __async(this, null, function* () {
          const id = v4_default();
          const timestamp = (/* @__PURE__ */ new Date()).toISOString();
          const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingCreate : SyncStatus.PendingCreate;
          yield this.dataStorageService.insert("journeyInfo", {
            id,
            journeyId,
            latitude,
            longitude,
            timestamp,
            syncStatus,
            lastSyncDate: null
          });
        });
      }
      closeConnection() {
        return __async(this, null, function* () {
          if (this.db) {
            this.db.close();
            this.db = null;
          }
        });
      }
      getAllJourneys() {
        return __async(this, null, function* () {
          let result = yield this.dataStorageService.select(this.tableName, "ORDER BY startDate DESC");
          if (!result || result.length === 0)
            return [];
          for (let i = 0; i < result.length; i++) {
            const journeyId = result[i].id;
            const infosJourney = yield this.dataStorageService.select("journeyInfo", `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);
            if (Array.isArray(infosJourney)) {
              result[i].infos = infosJourney.filter((info) => info.journeyId === journeyId);
            } else {
              result[i].infos = [];
            }
          }
          const journeys = result.map((data) => {
            return {
              id: data.id,
              startDate: data.startDate,
              endDate: data.endDate,
              distance: data.distance,
              userId: data.userId,
              vehicleId: data.vehicleId,
              infosJourney: data.infos,
              syncStatus: data.syncStatus || SyncStatus.Synced,
              lastSyncDate: data.lastSyncDate ? new Date(data.lastSyncDate) : void 0
            };
          });
          return journeys;
        });
      }
      getAllJourneysByUser(userId) {
        return __async(this, null, function* () {
          let result = yield this.dataStorageService.select(this.tableName, " ORDER BY startDate DESC");
          if (Array.isArray(result)) {
            result = result.filter((data) => data.userId === userId);
          } else {
            return [];
          }
          if (!result || result.length === 0)
            return [];
          for (let i = 0; i < result.length; i++) {
            const journeyId = result[i].id;
            const infosJourney = yield this.dataStorageService.select("journeyInfo", `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);
            if (Array.isArray(infosJourney)) {
              result[i].infos = infosJourney.filter((info) => info.journeyId === journeyId);
            } else {
              result[i].infos = [];
            }
          }
          const journeys = result.map((data) => {
            return {
              id: data.id,
              startDate: data.startDate,
              endDate: data.endDate,
              distance: data.distance,
              userId: data.userId,
              vehicleId: data.vehicleId,
              infosJourney: data.infos,
              syncStatus: data.syncStatus || SyncStatus.Synced,
              lastSyncDate: data.lastSyncDate ? new Date(data.lastSyncDate) : void 0
            };
          });
          return journeys;
        });
      }
      /**
       * Filtra localizações por precisão e remove pontos muito próximos
       */
      filterLocationsByAccuracy(locations) {
        if (locations.length <= 2)
          return locations;
        const filtered = [];
        const MIN_DISTANCE_THRESHOLD = 10;
        const MAX_SPEED_THRESHOLD = 200;
        filtered.push(locations[0]);
        for (let i = 1; i < locations.length; i++) {
          const current = locations[i];
          const previous = filtered[filtered.length - 1];
          const distance = this.calculateDistance(previous.latitude, previous.longitude, current.latitude, current.longitude);
          const timeDiff = (new Date(current.timestamp).getTime() - new Date(previous.timestamp).getTime()) / (1e3 * 60 * 60);
          const speed = timeDiff > 0 ? distance / 1e3 / timeDiff : 0;
          const hasGoodAccuracy = !current.accuracy || current.accuracy <= 30;
          if (distance >= MIN_DISTANCE_THRESHOLD && speed <= MAX_SPEED_THRESHOLD && this.isValidCoordinate(current.latitude, current.longitude) && hasGoodAccuracy) {
            filtered.push(current);
          } else {
            const reason = [];
            if (distance < MIN_DISTANCE_THRESHOLD)
              reason.push(`dist=${distance.toFixed(1)}m`);
            if (speed > MAX_SPEED_THRESHOLD)
              reason.push(`speed=${speed.toFixed(1)}km/h`);
            if (!this.isValidCoordinate(current.latitude, current.longitude))
              reason.push("coords_invalid");
            if (!hasGoodAccuracy)
              reason.push(`accuracy=${current.accuracy}m`);
            console.log(`Ponto filtrado: ${reason.join(", ")}`);
          }
        }
        const lastOriginal = locations[locations.length - 1];
        const lastFiltered = filtered[filtered.length - 1];
        if (lastOriginal.timestamp !== lastFiltered.timestamp) {
          filtered.push(lastOriginal);
        }
        return filtered;
      }
      /**
       * Verifica se as coordenadas são válidas
       */
      isValidCoordinate(lat, lon) {
        return lat >= -90 && lat <= 90 && lon >= -180 && lon <= 180 && lat !== 0 && lon !== 0;
      }
      /**
       * Calcula a distância total da viagem usando múltiplos métodos
       */
      calculateJourneyDistance(locations) {
        if (locations.length < 2)
          return 0;
        const directDistance = this.calculateDistance(locations[0].latitude, locations[0].longitude, locations[locations.length - 1].latitude, locations[locations.length - 1].longitude);
        let cumulativeDistance = 0;
        for (let i = 0; i < locations.length - 1; i++) {
          const segmentDistance = this.calculateDistance(locations[i].latitude, locations[i].longitude, locations[i + 1].latitude, locations[i + 1].longitude);
          cumulativeDistance += segmentDistance;
        }
        const smoothedDistance = this.calculateSmoothedDistance(locations);
        console.log(`Dist\xE2ncias calculadas:`);
        console.log(`- Direta: ${(directDistance / 1e3).toFixed(3)} km`);
        console.log(`- Cumulativa: ${(cumulativeDistance / 1e3).toFixed(3)} km`);
        console.log(`- Suavizada: ${(smoothedDistance / 1e3).toFixed(3)} km`);
        const ratio = cumulativeDistance / directDistance;
        if (ratio > 3) {
          console.log(`Ratio muito alto (${ratio.toFixed(2)}), usando dist\xE2ncia direta`);
          return directDistance;
        } else {
          console.log(`Usando dist\xE2ncia suavizada`);
          return smoothedDistance;
        }
      }
      /**
       * Calcula distância suavizada removendo segmentos com velocidades irreais
       */
      calculateSmoothedDistance(locations) {
        if (locations.length < 2)
          return 0;
        let totalDistance = 0;
        const MAX_SEGMENT_SPEED = 150;
        for (let i = 0; i < locations.length - 1; i++) {
          const current = locations[i];
          const next = locations[i + 1];
          const segmentDistance = this.calculateDistance(current.latitude, current.longitude, next.latitude, next.longitude);
          const timeDiff = (new Date(next.timestamp).getTime() - new Date(current.timestamp).getTime()) / (1e3 * 60 * 60);
          const speed = timeDiff > 0 ? segmentDistance / 1e3 / timeDiff : 0;
          if (speed <= MAX_SEGMENT_SPEED) {
            totalDistance += segmentDistance;
          } else {
            console.log(`Segmento ignorado: ${speed.toFixed(1)} km/h`);
          }
        }
        return totalDistance;
      }
      /**
       * Calcula distância entre dois pontos usando fórmula de Haversine
       */
      calculateDistance(lat1, lon1, lat2, lon2) {
        const toRad = (value) => value * Math.PI / 180;
        const R = 6371e3;
        const dLat = toRad(lat2 - lat1);
        const dLon = toRad(lon2 - lon1);
        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
      }
      /**
       * Carrega e salva endereços para pontos da viagem que não possuem endereço
       */
      loadAndSaveAddressesForJourney(infosJourney) {
        return __async(this, null, function* () {
          if (!infosJourney || infosJourney.length === 0)
            return;
          console.log("Carregando endere\xE7os para pontos da viagem...");
          const pointsNeedingAddress = infosJourney.filter((info) => !info.address);
          if (pointsNeedingAddress.length === 0) {
            console.log("Todos os pontos j\xE1 possuem endere\xE7o");
            return;
          }
          console.log(`${pointsNeedingAddress.length} pontos precisam de endere\xE7o`);
          const priorityPoints = [];
          const regularPoints = [];
          pointsNeedingAddress.forEach((info) => {
            const actualIndex = infosJourney.indexOf(info);
            if (actualIndex === 0) {
              priorityPoints.push({ info, index: actualIndex, priority: "start" });
            } else if (actualIndex === infosJourney.length - 1 && infosJourney.length > 1) {
              priorityPoints.push({ info, index: actualIndex, priority: "end" });
            } else {
              regularPoints.push(info);
            }
          });
          if (priorityPoints.length > 0) {
            console.log(`Carregando endere\xE7os priorit\xE1rios (${priorityPoints.length} pontos)...`);
            yield Promise.all(priorityPoints.map((_0) => __async(this, [_0], function* ({ info, priority }) {
              try {
                console.log(`Carregando endere\xE7o para ${priority === "start" ? "in\xEDcio" : "fim"} da viagem...`);
                const address = yield this.geocodingService.getReverseGeocodeWithCache(info.latitude, info.longitude);
                info.address = address;
                yield this.updateJourneyInfoAddress(info.id, address);
                console.log(`Endere\xE7o do ${priority === "start" ? "in\xEDcio" : "fim"} salvo: ${address}`);
              } catch (error) {
                console.error(`Erro ao carregar endere\xE7o para ${priority}:`, error);
                info.address = "Endere\xE7o n\xE3o dispon\xEDvel";
                yield this.updateJourneyInfoAddress(info.id, "Endere\xE7o n\xE3o dispon\xEDvel");
              }
            })));
            yield new Promise((resolve) => setTimeout(resolve, 500));
          }
          if (regularPoints.length > 0) {
            console.log(`Carregando endere\xE7os intermedi\xE1rios (${regularPoints.length} pontos)...`);
            const batchSize = 3;
            for (let i = 0; i < regularPoints.length; i += batchSize) {
              const batch = regularPoints.slice(i, i + batchSize);
              yield Promise.all(batch.map((info) => __async(this, null, function* () {
                try {
                  const address = yield this.geocodingService.getReverseGeocodeWithCache(info.latitude, info.longitude);
                  info.address = address;
                  yield this.updateJourneyInfoAddress(info.id, address);
                  console.log(`Endere\xE7o intermedi\xE1rio salvo: ${address}`);
                } catch (error) {
                  console.error("Erro ao carregar endere\xE7o intermedi\xE1rio:", error);
                  info.address = "Endere\xE7o n\xE3o dispon\xEDvel";
                  yield this.updateJourneyInfoAddress(info.id, "Endere\xE7o n\xE3o dispon\xEDvel");
                }
              })));
              if (i + batchSize < regularPoints.length) {
                yield new Promise((resolve) => setTimeout(resolve, 1e3));
              }
            }
          }
          console.log("Carregamento de endere\xE7os conclu\xEDdo");
        });
      }
      /**
       * Atualiza apenas o campo address de um JourneyInfo
       */
      updateJourneyInfoAddress(journeyInfoId, address) {
        return __async(this, null, function* () {
          try {
            yield this.dataStorageService.update("journeyInfo", { address }, `id = '${journeyInfoId}'`);
          } catch (error) {
            console.error(`Erro ao atualizar endere\xE7o para journeyInfo ${journeyInfoId}:`, error);
          }
        });
      }
      /**
       * Updates the sync status of a journey
       * @param journey The journey to update
       * @returns True if the update was successful
       */
      updateJourneySync(journey) {
        return __async(this, null, function* () {
          try {
            const journeyForDb = __spreadProps(__spreadValues({}, journey), {
              lastSyncDate: journey.lastSyncDate ? journey.lastSyncDate.toISOString() : null
            });
            if (journeyForDb.infosJourney) {
              delete journeyForDb.infosJourney;
            }
            yield this.dataStorageService.update(this.tableName, journeyForDb, `id = '${journey.id}'`);
            return true;
          } catch (error) {
            console.error("Error updating journey sync status:", error);
            return false;
          }
        });
      }
      /**
       * Gets journeys that need to be synchronized
       * @param userId The user ID
       * @returns Array of journeys that need to be synchronized
       */
      getPendingSyncJourneys(userId) {
        return __async(this, null, function* () {
          const journeys = yield this.getAllJourneysByUser(userId);
          return journeys.filter((j) => j.syncStatus === SyncStatus.PendingCreate || j.syncStatus === SyncStatus.PendingUpdate || j.syncStatus === SyncStatus.PendingDelete);
        });
      }
      /**
       * Sends a journey to the server for synchronization
       * @param journey The journey to send
       * @returns True if the journey was sent successfully
       */
      sendJourneyToSync(journey) {
        return __async(this, null, function* () {
          try {
            const url = this.apiService.getUrl("journey/SyncJourney");
            yield firstValueFrom(this.http.post(url, journey));
            return true;
          } catch (error) {
            console.error("Error sending journey to sync:", error);
            throw error;
          }
        });
      }
    };
    _JourneyStorageService.\u0275fac = function JourneyStorageService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _JourneyStorageService)(\u0275\u0275inject(SessionService), \u0275\u0275inject(DataStorageService), \u0275\u0275inject(Platform), \u0275\u0275inject(JourneyInfoService), \u0275\u0275inject(VehicleService), \u0275\u0275inject(NetworkService), \u0275\u0275inject(HttpClient), \u0275\u0275inject(ApiService), \u0275\u0275inject(GeocodingService));
    };
    _JourneyStorageService.\u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _JourneyStorageService, factory: _JourneyStorageService.\u0275fac, providedIn: "root" });
    JourneyStorageService = _JourneyStorageService;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(JourneyStorageService, [{
        type: Injectable,
        args: [{
          providedIn: "root"
        }]
      }], () => [{ type: SessionService }, { type: DataStorageService }, { type: Platform }, { type: JourneyInfoService }, { type: VehicleService }, { type: NetworkService }, { type: HttpClient }, { type: ApiService }, { type: GeocodingService }], null);
    })();
  }
});

export {
  Geolocation,
  init_esm2 as init_esm,
  Motion,
  init_esm3 as init_esm2,
  JourneyInfoService,
  init_journeyinfo_service,
  GeocodingService,
  init_geocoding_service,
  JourneyStorageService,
  init_journey_storage_service
};
//# sourceMappingURL=chunk-6KW4HZP7.js.map
