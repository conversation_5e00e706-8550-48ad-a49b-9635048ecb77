{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/theme-01f3f29c.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst hostContext = (selector, el) => {\n  return el.closest(selector) !== null;\n};\n/**\n * Create the mode and color classes for the component based on the classes passed in\n */\nconst createColorClasses = (color, cssClassMap) => {\n  return typeof color === 'string' && color.length > 0 ? Object.assign({\n    'ion-color': true,\n    [`ion-color-${color}`]: true\n  }, cssClassMap) : cssClassMap;\n};\nconst getClassList = classes => {\n  if (classes !== undefined) {\n    const array = Array.isArray(classes) ? classes : classes.split(' ');\n    return array.filter(c => c != null).map(c => c.trim()).filter(c => c !== '');\n  }\n  return [];\n};\nconst getClassMap = classes => {\n  const map = {};\n  getClassList(classes).forEach(c => map[c] = true);\n  return map;\n};\nconst SCHEME = /^[a-z][a-z0-9+\\-.]*:/;\nconst openURL = async (url, ev, direction, animation) => {\n  if (url != null && url[0] !== '#' && !SCHEME.test(url)) {\n    const router = document.querySelector('ion-router');\n    if (router) {\n      if (ev != null) {\n        ev.preventDefault();\n      }\n      return router.push(url, direction, animation);\n    }\n  }\n  return false;\n};\nexport { createColorClasses as c, getClassMap as g, hostContext as h, openURL as o };"], "mappings": ";;;;;;AAAA,IAGM,aAMA,oBAMA,cAOA,aAKA,QACA;AA5BN;AAAA;AAAA;AAGA,IAAM,cAAc,CAAC,UAAU,OAAO;AACpC,aAAO,GAAG,QAAQ,QAAQ,MAAM;AAAA,IAClC;AAIA,IAAM,qBAAqB,CAAC,OAAO,gBAAgB;AACjD,aAAO,OAAO,UAAU,YAAY,MAAM,SAAS,IAAI,OAAO,OAAO;AAAA,QACnE,aAAa;AAAA,QACb,CAAC,aAAa,KAAK,EAAE,GAAG;AAAA,MAC1B,GAAG,WAAW,IAAI;AAAA,IACpB;AACA,IAAM,eAAe,aAAW;AAC9B,UAAI,YAAY,QAAW;AACzB,cAAM,QAAQ,MAAM,QAAQ,OAAO,IAAI,UAAU,QAAQ,MAAM,GAAG;AAClE,eAAO,MAAM,OAAO,OAAK,KAAK,IAAI,EAAE,IAAI,OAAK,EAAE,KAAK,CAAC,EAAE,OAAO,OAAK,MAAM,EAAE;AAAA,MAC7E;AACA,aAAO,CAAC;AAAA,IACV;AACA,IAAM,cAAc,aAAW;AAC7B,YAAM,MAAM,CAAC;AACb,mBAAa,OAAO,EAAE,QAAQ,OAAK,IAAI,CAAC,IAAI,IAAI;AAChD,aAAO;AAAA,IACT;AACA,IAAM,SAAS;AACf,IAAM,UAAU,CAAO,KAAK,IAAI,WAAW,cAAc;AACvD,UAAI,OAAO,QAAQ,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,KAAK,GAAG,GAAG;AACtD,cAAM,SAAS,SAAS,cAAc,YAAY;AAClD,YAAI,QAAQ;AACV,cAAI,MAAM,MAAM;AACd,eAAG,eAAe;AAAA,UACpB;AACA,iBAAO,OAAO,KAAK,KAAK,WAAW,SAAS;AAAA,QAC9C;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}