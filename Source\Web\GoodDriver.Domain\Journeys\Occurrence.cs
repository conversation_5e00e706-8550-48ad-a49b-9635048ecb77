﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Domain.Journeys
{
	public class Occurrence
	{
        public Occurrence()
        {
            
        }


        public int Id { get; private set; }

		public string Name { get; private set; }

		public string Ico { get; private set; }

        public string Type { get; private set; }

		public int DiscountScore { get; private set; } = 0;

		public DateTime CreatedOn { get; private set; }

		public DateTime? UpdatedOn { get; private set; }

        public string Message { get; private set; }

    }
}
