.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;

  ion-spinner {
    margin-bottom: 16px;
  }

  p {
    color: var(--ion-color-medium);
    font-size: 0.9rem;
  }
}

.location-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: var(--ion-color-primary);
  color: var(--ion-color-primary-contrast);
  font-weight: bold;
  font-size: 0.9rem;
  border-radius: 50%;
}

// Estilos para início da viagem
.start-location {
  background: linear-gradient(135deg, var(--ion-color-success), var(--ion-color-success-shade));
  border: 3px solid var(--ion-color-success-tint);
  box-shadow: 0 4px 12px rgba(var(--ion-color-success-rgb), 0.3);

  ion-icon {
    font-size: 1.8rem;
    color: white;
  }
}

// Estilos para fim da viagem
.end-location {
  background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-primary-shade));
  border: 3px solid var(--ion-color-primary-tint);
  box-shadow: 0 4px 12px rgba(var(--ion-color-primary-rgb), 0.3);

  ion-icon {
    font-size: 1.8rem;
    color: white;
  }
}

// Classes para os itens de localização
.start-location-item {
  --background: linear-gradient(135deg, rgba(var(--ion-color-success-rgb), 0.1), rgba(var(--ion-color-success-rgb), 0.05));
  border-left: 4px solid var(--ion-color-success);
  margin-bottom: 12px;

  .start-title {
    color: var(--ion-color-success);
    font-weight: 600;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;

    ion-icon {
      font-size: 1.2rem;
    }
  }
}

.end-location-item {
  --background: linear-gradient(135deg, rgba(var(--ion-color-primary-rgb), 0.1), rgba(var(--ion-color-primary-rgb), 0.05));
  border-left: 4px solid var(--ion-color-primary);
  margin-bottom: 12px;

  .end-title {
    color: var(--ion-color-primary);
    font-weight: 600;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;

    ion-icon {
      font-size: 1.2rem;
    }
  }
}

.intermediate-location-item {
  --background: var(--ion-color-light);
  border-left: 2px solid var(--ion-color-medium);

  .intermediate-title {
    color: var(--ion-color-dark);
    font-weight: 500;
    font-size: 1rem;
    margin-bottom: 6px;
  }
}

.loading-address {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--ion-color-medium);
  font-style: italic;

  ion-spinner {
    --color: var(--ion-color-medium);
  }
}

.occurrence-type {
  color: var(--ion-color-warning);
  font-weight: 500;
  text-transform: capitalize;
}

ion-item {
  --padding-start: 16px;
  --inner-padding-end: 16px;
  border-radius: 12px;
  margin-bottom: 12px;

  ion-label {
    margin: 12px 0;

    h3 {
      color: var(--ion-color-dark);
      font-weight: 600;
      margin-bottom: 8px;
    }

    // Estilos específicos para cada tipo de informação
    .timestamp {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 6px 0;
      font-size: 0.95rem;
      font-weight: 500;
      color: var(--ion-color-dark);

      ion-icon {
        font-size: 1.1rem;
        color: var(--ion-color-primary);
      }
    }

    .coordinates {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 4px 0;
      font-size: 0.85rem;
      color: var(--ion-color-medium);
      font-family: 'Courier New', monospace;

      ion-icon {
        font-size: 1rem;
        color: var(--ion-color-medium);
      }
    }

    .address {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      margin: 6px 0;
      font-size: 0.9rem;
      line-height: 1.4;

      ion-icon {
        font-size: 1rem;
        color: var(--ion-color-tertiary);
        margin-top: 2px;
        flex-shrink: 0;
      }

      strong {
        color: var(--ion-color-dark);
      }
    }

    .occurrence {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 6px 0;
      font-size: 0.9rem;
      padding: 6px 12px;
      background-color: rgba(var(--ion-color-warning-rgb), 0.1);
      border-radius: 8px;
      border-left: 3px solid var(--ion-color-warning);

      ion-icon {
        font-size: 1.1rem;
      }
    }

    // Estilo geral para parágrafos que não têm classe específica
    p:not(.timestamp):not(.coordinates):not(.address):not(.occurrence):not(.loading-address) {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 4px 0;
      font-size: 0.9rem;

      ion-icon {
        font-size: 1rem;
        color: var(--ion-color-medium);
      }
    }
  }
}

ion-card {
  margin-bottom: 16px;

  ion-card-title {
    display: flex;
    align-items: center;
    gap: 8px;

    ion-icon {
      font-size: 1.3rem;
      color: var(--ion-color-primary);
    }

    ion-badge {
      margin-left: auto;
    }
  }
}

// Journey Progress Indicator
.journey-progress {
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(var(--ion-color-primary-rgb), 0.05), rgba(var(--ion-color-success-rgb), 0.05));
  border-radius: 12px;
  border: 1px solid rgba(var(--ion-color-primary-rgb), 0.1);

  .progress-line {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .progress-start, .progress-end {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: white;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      ion-icon {
        font-size: 1.5rem;
      }
    }

    .progress-track {
      flex: 1;
      height: 4px;
      background: linear-gradient(90deg, var(--ion-color-success), var(--ion-color-primary));
      margin: 0 12px;
      border-radius: 2px;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 8px;
        height: 8px;
        background: var(--ion-color-warning);
        border-radius: 50%;
        box-shadow: 0 0 0 3px rgba(var(--ion-color-warning-rgb), 0.3);
      }
    }
  }

  .progress-info {
    text-align: center;

    .progress-points {
      font-size: 0.9rem;
      color: var(--ion-color-medium);
      font-weight: 500;
    }
  }
}

// Journey Details
.journey-details {
  p {
    margin: 8px 0;
    font-size: 0.95rem;

    strong {
      color: var(--ion-color-dark);
      font-weight: 600;
    }
  }
}

ion-list {
  background: transparent;
  padding: 8px 0;

  ion-item {
    --background: var(--ion-color-light);
    margin-bottom: 12px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    &:last-child {
      margin-bottom: 0;
    }

    // Animação especial para início e fim
    &.start-location-item, &.end-location-item {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
      }
    }
  }
}