.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;

  ion-spinner {
    margin-bottom: 16px;
  }

  p {
    color: var(--ion-color-medium);
    font-size: 0.9rem;
  }
}

.location-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: var(--ion-color-primary);
  color: var(--ion-color-primary-contrast);
  font-weight: bold;
  font-size: 0.9rem;
  border-radius: 50%;
}

.loading-address {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--ion-color-medium);
  font-style: italic;

  ion-spinner {
    --color: var(--ion-color-medium);
  }
}

.occurrence-type {
  color: var(--ion-color-warning);
  font-weight: 500;
  text-transform: capitalize;
}

ion-item {
  --padding-start: 16px;
  --inner-padding-end: 16px;

  ion-label {
    margin: 8px 0;

    h3 {
      color: var(--ion-color-dark);
      font-weight: 600;
      margin-bottom: 4px;
    }

    p {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 4px 0;
      font-size: 0.9rem;

      ion-icon {
        font-size: 1rem;
        color: var(--ion-color-medium);
      }
    }
  }
}

ion-card {
  margin-bottom: 16px;

  ion-card-title {
    display: flex;
    align-items: center;
    justify-content: space-between;

    ion-badge {
      margin-left: 8px;
    }
  }
}

ion-list {
  background: transparent;

  ion-item {
    --background: var(--ion-color-light);
    margin-bottom: 8px;
    border-radius: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}