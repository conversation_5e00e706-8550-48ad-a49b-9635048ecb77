﻿using Dapper;
using GoodDriver.ReadModels.Vehicles._1_Query;
using GoodDriver.ReadModels.Vehicles._2_Result;
using Microsoft.Extensions.Configuration;
using Rogerio.Cqrs.Requests;
using Rogerio.Data;

namespace GoodDriver.ReadModels.Vehicles._0_Handler
{
    public class VehicleQueryHandler : BaseDataAccess, IRequestHandler<ListVehiclesByUserIdQuery, ListVehiclesByUserIdResult>
    {
        public VehicleQueryHandler(IConfiguration configuration) : base(configuration)
        {
        }

        public async Task<ListVehiclesByUserIdResult> HandleAsync(ListVehiclesByUserIdQuery query)
        {
            var sql = @"SELECT v.Id, v.Plate, v.Year, v.BrandId, b.Name, v.ModelId, m.Name, v.Version, v.PolicyNumber, v.CreatedOn  from Vehicle v
                    INNER JOIN Brand b on b.id = b.BrandId
                    INNER JOIN Model m on m.id = b.ModelId
                    WHERE UserId = @UserId";

            using (var connection = this.CreateConnection())
            {
                var result = (await connection.QueryAsync<ListVehiclesByUserIdResult.VehicleResult>(sql, query)).ToList();
                return new ListVehiclesByUserIdResult(result);
            }
        }
    }
}
