{"version": 3, "sources": ["src/app/pages/splash/splash.page.scss"], "sourcesContent": [".splash {\r\n  --background: #f8f8f8;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: linear-gradient(135deg, rgba(46, 125, 50, 0.1) 0%, rgba(255, 255, 255, 1) 100%);\r\n\r\n  .splash-content {\r\n    text-align: center;\r\n    animation: fadeIn 1.2s ease-in-out;\r\n\r\n    .logo {\r\n      width: 180px;\r\n      height: 180px;\r\n      margin-bottom: 24px;\r\n      animation: pulse 2s infinite;\r\n    }\r\n\r\n    h1 {\r\n      font-size: 32px;\r\n      font-weight: 600;\r\n      color: var(--ion-color-primary);\r\n      margin-bottom: 8px;\r\n    }\r\n\r\n    p {\r\n      font-size: 18px;\r\n      color: var(--ion-color-secondary);\r\n      margin-top: 0;\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.05);\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n  }\r\n}"], "mappings": ";AAAA,CAAA;AACE,gBAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA;IAAA;MAAA,MAAA;MAAA,KAAA,EAAA,EAAA,GAAA,EAAA,EAAA,EAAA,KAAA,EAAA;MAAA,IAAA,GAAA,EAAA,GAAA,EAAA,KAAA;;AAEA,CAPF,OAOE,CAAA;AACE,cAAA;AACA,aAAA,OAAA,KAAA;;AAEA,CAXJ,OAWI,CAJF,eAIE,CAAA;AACE,SAAA;AACA,UAAA;AACA,iBAAA;AACA,aAAA,MAAA,GAAA;;AAGF,CAlBJ,OAkBI,CAXF,eAWE;AACE,aAAA;AACA,eAAA;AACA,SAAA,IAAA;AACA,iBAAA;;AAGF,CAzBJ,OAyBI,CAlBF,eAkBE;AACE,aAAA;AACA,SAAA,IAAA;AACA,cAAA;;AAKN,WAxBI;AAyBF;AACE,aAAA;AACA,eAAA,WAAA;;AAEF;AACE,aAAA;AACA,eAAA,WAAA;;;AAIJ,WA7BM;AA8BJ;AACE,eAAA,MAAA;;AAEF;AACE,eAAA,MAAA;;AAEF;AACE,eAAA,MAAA;;;", "names": []}