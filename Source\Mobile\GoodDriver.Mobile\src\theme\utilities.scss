// Utility Classes
// --------------------------------------------------
// Reusable utility classes for common styling needs

@use './mixins.scss';

// Spacing utilities
// --------------------------------------------------

// Margin
.m-0 { margin: 0 !important; }
.m-xs { margin: var(--app-spacing-xs) !important; }
.m-sm { margin: var(--app-spacing-sm) !important; }
.m-md { margin: var(--app-spacing-md) !important; }
.m-lg { margin: var(--app-spacing-lg) !important; }
.m-xl { margin: var(--app-spacing-xl) !important; }

// Margin top
.mt-0 { margin-top: 0 !important; }
.mt-xs { margin-top: var(--app-spacing-xs) !important; }
.mt-sm { margin-top: var(--app-spacing-sm) !important; }
.mt-md { margin-top: var(--app-spacing-md) !important; }
.mt-lg { margin-top: var(--app-spacing-lg) !important; }
.mt-xl { margin-top: var(--app-spacing-xl) !important; }

// Margin right
.mr-0 { margin-right: 0 !important; }
.mr-xs { margin-right: var(--app-spacing-xs) !important; }
.mr-sm { margin-right: var(--app-spacing-sm) !important; }
.mr-md { margin-right: var(--app-spacing-md) !important; }
.mr-lg { margin-right: var(--app-spacing-lg) !important; }
.mr-xl { margin-right: var(--app-spacing-xl) !important; }

// Margin bottom
.mb-0 { margin-bottom: 0 !important; }
.mb-xs { margin-bottom: var(--app-spacing-xs) !important; }
.mb-sm { margin-bottom: var(--app-spacing-sm) !important; }
.mb-md { margin-bottom: var(--app-spacing-md) !important; }
.mb-lg { margin-bottom: var(--app-spacing-lg) !important; }
.mb-xl { margin-bottom: var(--app-spacing-xl) !important; }

// Margin left
.ml-0 { margin-left: 0 !important; }
.ml-xs { margin-left: var(--app-spacing-xs) !important; }
.ml-sm { margin-left: var(--app-spacing-sm) !important; }
.ml-md { margin-left: var(--app-spacing-md) !important; }
.ml-lg { margin-left: var(--app-spacing-lg) !important; }
.ml-xl { margin-left: var(--app-spacing-xl) !important; }

// Margin x-axis (left and right)
.mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.mx-xs { margin-left: var(--app-spacing-xs) !important; margin-right: var(--app-spacing-xs) !important; }
.mx-sm { margin-left: var(--app-spacing-sm) !important; margin-right: var(--app-spacing-sm) !important; }
.mx-md { margin-left: var(--app-spacing-md) !important; margin-right: var(--app-spacing-md) !important; }
.mx-lg { margin-left: var(--app-spacing-lg) !important; margin-right: var(--app-spacing-lg) !important; }
.mx-xl { margin-left: var(--app-spacing-xl) !important; margin-right: var(--app-spacing-xl) !important; }
.mx-auto { margin-left: auto !important; margin-right: auto !important; }

// Margin y-axis (top and bottom)
.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.my-xs { margin-top: var(--app-spacing-xs) !important; margin-bottom: var(--app-spacing-xs) !important; }
.my-sm { margin-top: var(--app-spacing-sm) !important; margin-bottom: var(--app-spacing-sm) !important; }
.my-md { margin-top: var(--app-spacing-md) !important; margin-bottom: var(--app-spacing-md) !important; }
.my-lg { margin-top: var(--app-spacing-lg) !important; margin-bottom: var(--app-spacing-lg) !important; }
.my-xl { margin-top: var(--app-spacing-xl) !important; margin-bottom: var(--app-spacing-xl) !important; }

// Padding
.p-0 { padding: 0 !important; }
.p-xs { padding: var(--app-spacing-xs) !important; }
.p-sm { padding: var(--app-spacing-sm) !important; }
.p-md { padding: var(--app-spacing-md) !important; }
.p-lg { padding: var(--app-spacing-lg) !important; }
.p-xl { padding: var(--app-spacing-xl) !important; }

// Padding top
.pt-0 { padding-top: 0 !important; }
.pt-xs { padding-top: var(--app-spacing-xs) !important; }
.pt-sm { padding-top: var(--app-spacing-sm) !important; }
.pt-md { padding-top: var(--app-spacing-md) !important; }
.pt-lg { padding-top: var(--app-spacing-lg) !important; }
.pt-xl { padding-top: var(--app-spacing-xl) !important; }

// Padding right
.pr-0 { padding-right: 0 !important; }
.pr-xs { padding-right: var(--app-spacing-xs) !important; }
.pr-sm { padding-right: var(--app-spacing-sm) !important; }
.pr-md { padding-right: var(--app-spacing-md) !important; }
.pr-lg { padding-right: var(--app-spacing-lg) !important; }
.pr-xl { padding-right: var(--app-spacing-xl) !important; }

// Padding bottom
.pb-0 { padding-bottom: 0 !important; }
.pb-xs { padding-bottom: var(--app-spacing-xs) !important; }
.pb-sm { padding-bottom: var(--app-spacing-sm) !important; }
.pb-md { padding-bottom: var(--app-spacing-md) !important; }
.pb-lg { padding-bottom: var(--app-spacing-lg) !important; }
.pb-xl { padding-bottom: var(--app-spacing-xl) !important; }

// Padding left
.pl-0 { padding-left: 0 !important; }
.pl-xs { padding-left: var(--app-spacing-xs) !important; }
.pl-sm { padding-left: var(--app-spacing-sm) !important; }
.pl-md { padding-left: var(--app-spacing-md) !important; }
.pl-lg { padding-left: var(--app-spacing-lg) !important; }
.pl-xl { padding-left: var(--app-spacing-xl) !important; }

// Padding x-axis (left and right)
.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.px-xs { padding-left: var(--app-spacing-xs) !important; padding-right: var(--app-spacing-xs) !important; }
.px-sm { padding-left: var(--app-spacing-sm) !important; padding-right: var(--app-spacing-sm) !important; }
.px-md { padding-left: var(--app-spacing-md) !important; padding-right: var(--app-spacing-md) !important; }
.px-lg { padding-left: var(--app-spacing-lg) !important; padding-right: var(--app-spacing-lg) !important; }
.px-xl { padding-left: var(--app-spacing-xl) !important; padding-right: var(--app-spacing-xl) !important; }

// Padding y-axis (top and bottom)
.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.py-xs { padding-top: var(--app-spacing-xs) !important; padding-bottom: var(--app-spacing-xs) !important; }
.py-sm { padding-top: var(--app-spacing-sm) !important; padding-bottom: var(--app-spacing-sm) !important; }
.py-md { padding-top: var(--app-spacing-md) !important; padding-bottom: var(--app-spacing-md) !important; }
.py-lg { padding-top: var(--app-spacing-lg) !important; padding-bottom: var(--app-spacing-lg) !important; }
.py-xl { padding-top: var(--app-spacing-xl) !important; padding-bottom: var(--app-spacing-xl) !important; }

// Typography utilities
// --------------------------------------------------

// Text alignment
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

// Text transformation
.text-lowercase { text-transform: lowercase !important; }
.text-uppercase { text-transform: uppercase !important; }
.text-capitalize { text-transform: capitalize !important; }

// Font weight
.font-light { font-weight: 300 !important; }
.font-normal { font-weight: 400 !important; }
.font-medium { font-weight: 500 !important; }
.font-semibold { font-weight: 600 !important; }
.font-bold { font-weight: 700 !important; }

// Font size
.text-xs { font-size: 0.75rem !important; }
.text-sm { font-size: 0.875rem !important; }
.text-md { font-size: 1rem !important; }
.text-lg { font-size: 1.125rem !important; }
.text-xl { font-size: 1.25rem !important; }
.text-2xl { font-size: 1.5rem !important; }
.text-3xl { font-size: 1.875rem !important; }
.text-4xl { font-size: 2.25rem !important; }

// Text colors
.text-primary { color: var(--ion-color-primary) !important; }
.text-secondary { color: var(--ion-color-secondary) !important; }
.text-tertiary { color: var(--ion-color-tertiary) !important; }
.text-success { color: var(--ion-color-success) !important; }
.text-warning { color: var(--ion-color-warning) !important; }
.text-danger { color: var(--ion-color-danger) !important; }
.text-dark { color: var(--ion-color-dark) !important; }
.text-medium { color: var(--ion-color-medium) !important; }
.text-light { color: var(--ion-color-light) !important; }

// Display utilities
// --------------------------------------------------
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

// Flex utilities
// --------------------------------------------------
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-row-reverse { flex-direction: row-reverse !important; }
.flex-column-reverse { flex-direction: column-reverse !important; }

.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.justify-content-evenly { justify-content: space-evenly !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.align-self-start { align-self: flex-start !important; }
.align-self-end { align-self: flex-end !important; }
.align-self-center { align-self: center !important; }
.align-self-baseline { align-self: baseline !important; }
.align-self-stretch { align-self: stretch !important; }

.flex-grow-0 { flex-grow: 0 !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.flex-shrink-0 { flex-shrink: 0 !important; }
.flex-shrink-1 { flex-shrink: 1 !important; }

// Position utilities
// --------------------------------------------------
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

// Border utilities
// --------------------------------------------------
.border { border: 1px solid var(--app-border-color) !important; }
.border-top { border-top: 1px solid var(--app-border-color) !important; }
.border-right { border-right: 1px solid var(--app-border-color) !important; }
.border-bottom { border-bottom: 1px solid var(--app-border-color) !important; }
.border-left { border-left: 1px solid var(--app-border-color) !important; }
.border-0 { border: 0 !important; }

.rounded { border-radius: var(--app-border-radius-md) !important; }
.rounded-sm { border-radius: var(--app-border-radius-sm) !important; }
.rounded-lg { border-radius: var(--app-border-radius-lg) !important; }
.rounded-circle { border-radius: 50% !important; }
.rounded-0 { border-radius: 0 !important; }

// Background utilities
// --------------------------------------------------
.bg-primary { background-color: var(--ion-color-primary) !important; }
.bg-secondary { background-color: var(--ion-color-secondary) !important; }
.bg-tertiary { background-color: var(--ion-color-tertiary) !important; }
.bg-success { background-color: var(--ion-color-success) !important; }
.bg-warning { background-color: var(--ion-color-warning) !important; }
.bg-danger { background-color: var(--ion-color-danger) !important; }
.bg-dark { background-color: var(--ion-color-dark) !important; }
.bg-medium { background-color: var(--ion-color-medium) !important; }
.bg-light { background-color: var(--ion-color-light) !important; }
.bg-transparent { background-color: transparent !important; }

// Custom background colors
.bg-primary-light { background-color: var(--app-primary-light) !important; }
.bg-success-light { background-color: var(--app-success-light) !important; }
.bg-warning-light { background-color: var(--app-warning-light) !important; }
.bg-danger-light { background-color: var(--app-danger-light) !important; }
