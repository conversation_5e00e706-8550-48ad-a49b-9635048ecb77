﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Domain.Journeys
{
	public class JourneyInfo
	{
		protected JourneyInfo() { } //for NHibernate

		protected JourneyInfo(string id) : this()
        {
            this.Id = id;
		}

        public JourneyInfo(string id, Journey journey, DateTime date, string latitude, string longitude, Occurrence occurrence = null) : this(id)
		{	
            if(string.IsNullOrEmpty(id))
				throw new ArgumentNullException(nameof(id), "JourneyInfo Id cannot be null or empty.");
            if (journey == null)
				throw new ArgumentNullException(nameof(journey), "Journey cannot be null or empty.");
			if (string.IsNullOrEmpty(latitude))
				throw new ArgumentNullException(nameof(latitude), "Latitude cannot be null or empty.");
			if (string.IsNullOrEmpty(longitude))
				throw new ArgumentNullException(nameof(longitude), "Longitude cannot be null or empty.");

			string pattern = @"^[-+]?([1-8]?\d(\.\d+)?|90(\.0+)?)$";
			if (!System.Text.RegularExpressions.Regex.IsMatch(latitude, pattern))
				throw new ArgumentException("Latitude is not valid.", nameof(latitude));
			if (!System.Text.RegularExpressions.Regex.IsMatch(longitude, pattern))
				throw new ArgumentException("Longitude is not valid.", nameof(longitude));

			this.Journey = journey;
			this.Date = date;
			this.Latitude = latitude;
			this.Longitude = longitude;
			this.Occurrence = occurrence;
		}

        public string Id { get; private set; }

        public Journey Journey { get; private set; }

        public DateTime Date { get; private set; }
        
        public string Latitude { get; private set; }

        public string Longitude { get; private set; }

		public Occurrence Occurrence { get; private set; }
	}
}
