﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Common
{
    public static class Constants
    {
        public const string DefaultConnectionStringName = "GoodDriverConnection";


        public static class UserStatus
        {
            public const string Registered = "Registrado"; 
            public const string PendingConfirmation = "Cadastro Pendente Confirmação";
            public const string Active = "Ativo";
            public const string Inactive = "Inativo";
            public const string Blocked = "Bloqueado";
            public const string Canceled = "Cancelado";
        }

        public static class OccurrenceTye
        {
            public const string HardBreak = "HardBreak";
            public const string PhoneUse = "PhoneUse";
            public const string Acceleration = "Acceleration";
            public const string Collision = "Collision";
        }
    }
}
