{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/spinner-configs-964f7cf3.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst spinners = {\n  bubbles: {\n    dur: 1000,\n    circles: 9,\n    fn: (dur, index, total) => {\n      const animationDelay = `${dur * index / total - dur}ms`;\n      const angle = 2 * Math.PI * index / total;\n      return {\n        r: 5,\n        style: {\n          top: `${32 * Math.sin(angle)}%`,\n          left: `${32 * Math.cos(angle)}%`,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  circles: {\n    dur: 1000,\n    circles: 8,\n    fn: (dur, index, total) => {\n      const step = index / total;\n      const animationDelay = `${dur * step - dur}ms`;\n      const angle = 2 * Math.PI * step;\n      return {\n        r: 5,\n        style: {\n          top: `${32 * Math.sin(angle)}%`,\n          left: `${32 * Math.cos(angle)}%`,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  circular: {\n    dur: 1400,\n    elmDuration: true,\n    circles: 1,\n    fn: () => {\n      return {\n        r: 20,\n        cx: 48,\n        cy: 48,\n        fill: 'none',\n        viewBox: '24 24 48 48',\n        transform: 'translate(0,0)',\n        style: {}\n      };\n    }\n  },\n  crescent: {\n    dur: 750,\n    circles: 1,\n    fn: () => {\n      return {\n        r: 26,\n        style: {}\n      };\n    }\n  },\n  dots: {\n    dur: 750,\n    circles: 3,\n    fn: (_, index) => {\n      const animationDelay = -(110 * index) + 'ms';\n      return {\n        r: 6,\n        style: {\n          left: `${32 - 32 * index}%`,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  lines: {\n    dur: 1000,\n    lines: 8,\n    fn: (dur, index, total) => {\n      const transform = `rotate(${360 / total * index + (index < total / 2 ? 180 : -180)}deg)`;\n      const animationDelay = `${dur * index / total - dur}ms`;\n      return {\n        y1: 14,\n        y2: 26,\n        style: {\n          transform: transform,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  'lines-small': {\n    dur: 1000,\n    lines: 8,\n    fn: (dur, index, total) => {\n      const transform = `rotate(${360 / total * index + (index < total / 2 ? 180 : -180)}deg)`;\n      const animationDelay = `${dur * index / total - dur}ms`;\n      return {\n        y1: 12,\n        y2: 20,\n        style: {\n          transform: transform,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  'lines-sharp': {\n    dur: 1000,\n    lines: 12,\n    fn: (dur, index, total) => {\n      const transform = `rotate(${30 * index + (index < 6 ? 180 : -180)}deg)`;\n      const animationDelay = `${dur * index / total - dur}ms`;\n      return {\n        y1: 17,\n        y2: 29,\n        style: {\n          transform: transform,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  },\n  'lines-sharp-small': {\n    dur: 1000,\n    lines: 12,\n    fn: (dur, index, total) => {\n      const transform = `rotate(${30 * index + (index < 6 ? 180 : -180)}deg)`;\n      const animationDelay = `${dur * index / total - dur}ms`;\n      return {\n        y1: 12,\n        y2: 20,\n        style: {\n          transform: transform,\n          'animation-delay': animationDelay\n        }\n      };\n    }\n  }\n};\nconst SPINNERS = spinners;\nexport { SPINNERS as S };"], "mappings": ";;;;;AAAA,IAGM,UA2IA;AA9IN;AAAA;AAAA;AAGA,IAAM,WAAW;AAAA,MACf,SAAS;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,QACT,IAAI,CAAC,KAAK,OAAO,UAAU;AACzB,gBAAM,iBAAiB,GAAG,MAAM,QAAQ,QAAQ,GAAG;AACnD,gBAAM,QAAQ,IAAI,KAAK,KAAK,QAAQ;AACpC,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,OAAO;AAAA,cACL,KAAK,GAAG,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,cAC5B,MAAM,GAAG,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,cAC7B,mBAAmB;AAAA,YACrB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,SAAS;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,QACT,IAAI,CAAC,KAAK,OAAO,UAAU;AACzB,gBAAM,OAAO,QAAQ;AACrB,gBAAM,iBAAiB,GAAG,MAAM,OAAO,GAAG;AAC1C,gBAAM,QAAQ,IAAI,KAAK,KAAK;AAC5B,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,OAAO;AAAA,cACL,KAAK,GAAG,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,cAC5B,MAAM,GAAG,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,cAC7B,mBAAmB;AAAA,YACrB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,UAAU;AAAA,QACR,KAAK;AAAA,QACL,aAAa;AAAA,QACb,SAAS;AAAA,QACT,IAAI,MAAM;AACR,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,IAAI;AAAA,YACJ,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,SAAS;AAAA,YACT,WAAW;AAAA,YACX,OAAO,CAAC;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA,MACA,UAAU;AAAA,QACR,KAAK;AAAA,QACL,SAAS;AAAA,QACT,IAAI,MAAM;AACR,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,OAAO,CAAC;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM;AAAA,QACJ,KAAK;AAAA,QACL,SAAS;AAAA,QACT,IAAI,CAAC,GAAG,UAAU;AAChB,gBAAM,iBAAiB,EAAE,MAAM,SAAS;AACxC,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,OAAO;AAAA,cACL,MAAM,GAAG,KAAK,KAAK,KAAK;AAAA,cACxB,mBAAmB;AAAA,YACrB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,KAAK;AAAA,QACL,OAAO;AAAA,QACP,IAAI,CAAC,KAAK,OAAO,UAAU;AACzB,gBAAM,YAAY,UAAU,MAAM,QAAQ,SAAS,QAAQ,QAAQ,IAAI,MAAM,KAAK;AAClF,gBAAM,iBAAiB,GAAG,MAAM,QAAQ,QAAQ,GAAG;AACnD,iBAAO;AAAA,YACL,IAAI;AAAA,YACJ,IAAI;AAAA,YACJ,OAAO;AAAA,cACL;AAAA,cACA,mBAAmB;AAAA,YACrB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb,KAAK;AAAA,QACL,OAAO;AAAA,QACP,IAAI,CAAC,KAAK,OAAO,UAAU;AACzB,gBAAM,YAAY,UAAU,MAAM,QAAQ,SAAS,QAAQ,QAAQ,IAAI,MAAM,KAAK;AAClF,gBAAM,iBAAiB,GAAG,MAAM,QAAQ,QAAQ,GAAG;AACnD,iBAAO;AAAA,YACL,IAAI;AAAA,YACJ,IAAI;AAAA,YACJ,OAAO;AAAA,cACL;AAAA,cACA,mBAAmB;AAAA,YACrB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb,KAAK;AAAA,QACL,OAAO;AAAA,QACP,IAAI,CAAC,KAAK,OAAO,UAAU;AACzB,gBAAM,YAAY,UAAU,KAAK,SAAS,QAAQ,IAAI,MAAM,KAAK;AACjE,gBAAM,iBAAiB,GAAG,MAAM,QAAQ,QAAQ,GAAG;AACnD,iBAAO;AAAA,YACL,IAAI;AAAA,YACJ,IAAI;AAAA,YACJ,OAAO;AAAA,cACL;AAAA,cACA,mBAAmB;AAAA,YACrB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,qBAAqB;AAAA,QACnB,KAAK;AAAA,QACL,OAAO;AAAA,QACP,IAAI,CAAC,KAAK,OAAO,UAAU;AACzB,gBAAM,YAAY,UAAU,KAAK,SAAS,QAAQ,IAAI,MAAM,KAAK;AACjE,gBAAM,iBAAiB,GAAG,MAAM,QAAQ,QAAQ,GAAG;AACnD,iBAAO;AAAA,YACL,IAAI;AAAA,YACJ,IAAI;AAAA,YACJ,OAAO;AAAA,cACL;AAAA,cACA,mBAAmB;AAAA,YACrB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,IAAM,WAAW;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}