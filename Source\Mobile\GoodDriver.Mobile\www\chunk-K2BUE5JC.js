import {
  hapticSelectionChanged,
  hapticSelectionEnd,
  hapticSelectionStart,
  init_haptic_ac164e4c
} from "./chunk-5XAFDKGG.js";
import {
  init_index_527b9e34,
  writeTask
} from "./chunk-V6QEVD67.js";
import {
  createGesture,
  init_index_39782642
} from "./chunk-NRG74LSH.js";
import {
  __esm
} from "./chunk-4W6HR7MY.js";

// node_modules/@ionic/core/dist/esm/button-active-90f1dbc4.js
var createButtonActiveGesture;
var init_button_active_90f1dbc4 = __esm({
  "node_modules/@ionic/core/dist/esm/button-active-90f1dbc4.js"() {
    "use strict";
    init_index_527b9e34();
    init_haptic_ac164e4c();
    init_index_39782642();
    createButtonActiveGesture = (el, isButton) => {
      let currentTouchedButton;
      let initialTouchedButton;
      const activateButtonAtPoint = (x, y, hapticFeedbackFn) => {
        if (typeof document === "undefined") {
          return;
        }
        const target = document.elementFromPoint(x, y);
        if (!target || !isButton(target) || target.disabled) {
          clearActiveButton();
          return;
        }
        if (target !== currentTouchedButton) {
          clearActiveButton();
          setActiveButton(target, hapticFeedbackFn);
        }
      };
      const setActiveButton = (button, hapticFeedbackFn) => {
        currentTouchedButton = button;
        if (!initialTouchedButton) {
          initialTouchedButton = currentTouchedButton;
        }
        const buttonToModify = currentTouchedButton;
        writeTask(() => buttonToModify.classList.add("ion-activated"));
        hapticFeedbackFn();
      };
      const clearActiveButton = (dispatchClick = false) => {
        if (!currentTouchedButton) {
          return;
        }
        const buttonToModify = currentTouchedButton;
        writeTask(() => buttonToModify.classList.remove("ion-activated"));
        if (dispatchClick && initialTouchedButton !== currentTouchedButton) {
          currentTouchedButton.click();
        }
        currentTouchedButton = void 0;
      };
      return createGesture({
        el,
        gestureName: "buttonActiveDrag",
        threshold: 0,
        onStart: (ev) => activateButtonAtPoint(ev.currentX, ev.currentY, hapticSelectionStart),
        onMove: (ev) => activateButtonAtPoint(ev.currentX, ev.currentY, hapticSelectionChanged),
        onEnd: () => {
          clearActiveButton(true);
          hapticSelectionEnd();
          initialTouchedButton = void 0;
        }
      });
    };
  }
});

export {
  createButtonActiveGesture,
  init_button_active_90f1dbc4
};
/*! Bundled license information:

@ionic/core/dist/esm/button-active-90f1dbc4.js:
  (*!
   * (C) Ionic http://ionicframework.com - MIT License
   *)
*/
//# sourceMappingURL=chunk-K2BUE5JC.js.map
