﻿using FluentNHibernate.Mapping;
using GoodDriver.Domain.Devices;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Infrastructure.Database.Maps
{
	public class DeviceMap : ClassMap<Device>
	{
        public DeviceMap()
        {
			Id(u => u.Id);
			References(a => a.User).Column("UserId").Not.Nullable().Fetch.Join().Cascade.None();
			Map(a => a.Brand).Nullable();
			Map(a => a.Model).Nullable();
			Map(a => a.SystemOperational).Nullable();			
			Map(a => a.CreatedOn).Not.Nullable();
			Map(a => a.UpdatedOn).Nullable();
		}
    }
}
