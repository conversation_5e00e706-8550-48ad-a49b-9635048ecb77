﻿using Dapper;
using GoodDriver.ReadModels.Brands.Result;
using GoodDriver.ReadModels.Models._1_Query;
using GoodDriver.ReadModels.Models._2_Result;
using Microsoft.Extensions.Configuration;
using Rogerio.Cqrs.Requests;
using Rogerio.Data;


namespace GoodDriver.ReadModels.Models._0_Handler
{
    public class ModelQueryHandler : BaseDataAccess, IRequestHandler<ModelListQuery, ModelListResult>
    {
        public ModelQueryHandler(IConfiguration configuration) : base(configuration)
        {
        }
        public async Task<ModelListResult> HandleAsync(ModelListQuery query)
        {
            var sql = @$"SELECT Id, Name, ReferenceCode, ReferenceMonth, ShortName, BrandId, CreatedOn, UpdatedOn from Model where BrandId = { query.BrandId} Order by Name";

            using (var connection = this.CreateConnection())
            {
                var result = (await connection.QueryAsync<ModelListResult.ModelListItemResult>(sql, query)).ToList();
                return new ModelListResult(result);
            }
        }
    }
}
