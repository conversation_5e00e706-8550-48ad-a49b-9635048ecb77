﻿using Rogerio.Cqrs.Events;

namespace GoodDriver.Contracts.Users.Events
{
	public class UserCreatedEvent : IDomainEvent
    {
        public UserCreatedEvent(string id, string name, string phone, string email, string password, string token)
        {
            Id = id;
            Name = name;            
            Phone = phone;            
            Email = email;
            Password = password;
            Token = token;
        }

        public string Id { get; set; }
        public string Name { get; set; }
        public string Phone { get; set; } 
        public string Email { get; set; }
        public string Password { get; set; }
        public string Token { get; set; }
    }
}
