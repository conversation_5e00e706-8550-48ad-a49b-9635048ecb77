import {
  CommonModule,
  Component,
  IonButton,
  IonButtons,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonContent,
  IonHeader,
  IonIcon,
  IonItem,
  IonLabel,
  IonSpinner,
  IonTitle,
  IonToolbar,
  IonicModule,
  NgIf,
  ReactiveFormsModule,
  Router,
  RouterLink,
  RouterLinkDelegateDirective2 as RouterLinkDelegateDirective,
  RouterModule,
  SessionService,
  ToastService,
  VehicleService,
  VehicleStateService,
  init_common,
  init_core,
  init_forms,
  init_ionic_angular,
  init_router,
  init_session_service,
  init_toast_service,
  init_vehicle_service,
  init_vehicle_state_service,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2
} from "./chunk-PUABLQ3Y.js";
import {
  __async,
  __esm
} from "./chunk-4W6HR7MY.js";

// src/app/pages/tabs/home/<USER>
function HomePage_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 8);
    \u0275\u0275element(1, "ion-spinner");
    \u0275\u0275elementEnd();
  }
}
function HomePage_div_10_ion_card_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-card", 14)(1, "ion-card-header")(2, "ion-card-title");
    \u0275\u0275element(3, "ion-icon", 15);
    \u0275\u0275text(4, " Aten\xE7\xE3o ");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(5, "ion-card-content")(6, "p");
    \u0275\u0275text(7, "Voc\xEA ainda n\xE3o possui nenhum ve\xEDculo cadastrado. Para utilizar o rastreamento de viagens, \xE9 necess\xE1rio cadastrar pelo menos um ve\xEDculo.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "ion-button", 16);
    \u0275\u0275element(9, "ion-icon", 17);
    \u0275\u0275text(10, " Cadastrar Ve\xEDculo ");
    \u0275\u0275elementEnd()()();
  }
}
function HomePage_div_10_ion_card_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-card")(1, "ion-card-header")(2, "ion-card-title");
    \u0275\u0275element(3, "ion-icon", 18);
    \u0275\u0275text(4, " Ve\xEDculo Principal ");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(5, "ion-card-content")(6, "ion-item", 19)(7, "ion-label")(8, "h2");
    \u0275\u0275text(9);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "p");
    \u0275\u0275text(11);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "p");
    \u0275\u0275text(13);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(14, "ion-button", 20);
    \u0275\u0275element(15, "ion-icon", 21);
    \u0275\u0275text(16, " Gerenciar Ve\xEDculos ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(9);
    \u0275\u0275textInterpolate(ctx_r0.primaryVehicle.plate);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate2("", ctx_r0.primaryVehicle.brandName, " ", ctx_r0.primaryVehicle.modelName, "");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("Ano: ", ctx_r0.primaryVehicle.year, "");
  }
}
function HomePage_div_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div")(1, "ion-card")(2, "ion-card-header")(3, "ion-card-title", 9);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(5, "ion-card-content")(6, "p", 9);
    \u0275\u0275text(7, " Voc\xEA est\xE1 logado com sucesso no aplicativo. ");
    \u0275\u0275elementEnd()()();
    \u0275\u0275template(8, HomePage_div_10_ion_card_8_Template, 11, 0, "ion-card", 10)(9, HomePage_div_10_ion_card_9_Template, 17, 4, "ion-card", 7);
    \u0275\u0275elementStart(10, "ion-card")(11, "ion-card-header")(12, "ion-card-title");
    \u0275\u0275element(13, "ion-icon", 11);
    \u0275\u0275text(14, " Sincroniza\xE7\xE3o ");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(15, "ion-card-content")(16, "p");
    \u0275\u0275text(17, "Mantenha seus dados sincronizados entre o aplicativo e o servidor.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(18, "ion-button", 12);
    \u0275\u0275element(19, "ion-icon", 13);
    \u0275\u0275text(20, " Ir para Sincroniza\xE7\xE3o ");
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1("Bem-vindo, ", ctx_r0.username, "!");
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", !ctx_r0.hasVehicles);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.hasVehicles && ctx_r0.primaryVehicle);
  }
}
var _HomePage, HomePage;
var init_home_page = __esm({
  "src/app/pages/tabs/home/<USER>"() {
    "use strict";
    init_core();
    init_router();
    init_ionic_angular();
    init_common();
    init_forms();
    init_core();
    init_router();
    init_toast_service();
    init_session_service();
    init_vehicle_service();
    init_vehicle_state_service();
    init_ionic_angular();
    init_common();
    _HomePage = class _HomePage {
      constructor(router, toastService, sessionService, vehicleService, vehicleStateService) {
        this.router = router;
        this.toastService = toastService;
        this.sessionService = sessionService;
        this.vehicleService = vehicleService;
        this.vehicleStateService = vehicleStateService;
        this.username = "Usu\xE1rio";
        this.hasVehicles = false;
        this.isLoading = true;
        this.primaryVehicle = null;
        this.subscriptions = [];
      }
      ngOnInit() {
        return __async(this, null, function* () {
          this.username = (yield this.sessionService.getUserName()) || "Usu\xE1rio";
          yield this.checkVehicles();
          this.subscriptions.push(this.vehicleStateService.primaryVehicle$.subscribe((vehicle) => {
            if (vehicle) {
              this.primaryVehicle = vehicle;
              this.hasVehicles = true;
            }
          }));
          this.subscriptions.push(this.vehicleStateService.vehicles$.subscribe((vehicles) => {
            this.hasVehicles = vehicles.length > 0;
          }));
        });
      }
      ngOnDestroy() {
        this.subscriptions.forEach((sub) => sub.unsubscribe());
      }
      /**
       * Checks if the user has any vehicles registered
       */
      checkVehicles() {
        return __async(this, null, function* () {
          this.isLoading = true;
          try {
            const userId = (yield this.sessionService.getUserId()) || "";
            this.hasVehicles = yield this.vehicleService.hasVehicles(userId);
            if (this.hasVehicles) {
              this.primaryVehicle = yield this.vehicleService.getPrimaryVehicle(userId);
            }
          } catch (error) {
            console.error("Error checking vehicles:", error);
            this.toastService.showToast("Erro ao verificar ve\xEDculos", "danger");
          } finally {
            this.isLoading = false;
          }
        });
      }
      logout() {
        this.sessionService.clearSession();
        console.log("Logout realizado");
        this.router.navigate(["/login"]);
      }
    };
    _HomePage.\u0275fac = function HomePage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _HomePage)(\u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(ToastService), \u0275\u0275directiveInject(SessionService), \u0275\u0275directiveInject(VehicleService), \u0275\u0275directiveInject(VehicleStateService));
    };
    _HomePage.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _HomePage, selectors: [["app-home"]], decls: 11, vars: 4, consts: [[3, "translucent"], ["color", "primary"], ["slot", "end"], ["fill", "clear", "color", "light", 3, "click"], [1, "material-icons"], [1, "ion-padding", 3, "fullscreen"], ["class", "ion-text-center ion-padding", 4, "ngIf"], [4, "ngIf"], [1, "ion-text-center", "ion-padding"], [1, "ion-text-center"], ["color", "warning", 4, "ngIf"], ["name", "sync-outline"], ["expand", "block", "color", "primary", "routerLink", "/tabs/sync", 1, "ion-margin-top"], ["name", "sync-outline", "slot", "start"], ["color", "warning"], ["name", "warning-outline"], ["expand", "block", "color", "primary", "routerLink", "/new-vehicle", 1, "ion-margin-top"], ["name", "car-outline", "slot", "start"], ["name", "car-outline"], ["lines", "none"], ["expand", "block", "color", "primary", "routerLink", "/tabs/vehicles", 1, "ion-margin-top"], ["name", "list-outline", "slot", "start"]], template: function HomePage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header", 0)(1, "ion-toolbar", 1)(2, "ion-title");
        \u0275\u0275text(3, " Home ");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(4, "ion-buttons", 2)(5, "ion-button", 3);
        \u0275\u0275listener("click", function HomePage_Template_ion_button_click_5_listener() {
          return ctx.logout();
        });
        \u0275\u0275elementStart(6, "span", 4);
        \u0275\u0275text(7, "logout");
        \u0275\u0275elementEnd()()()()();
        \u0275\u0275elementStart(8, "ion-content", 5);
        \u0275\u0275template(9, HomePage_div_9_Template, 2, 0, "div", 6)(10, HomePage_div_10_Template, 21, 3, "div", 7);
        \u0275\u0275elementEnd();
      }
      if (rf & 2) {
        \u0275\u0275property("translucent", true);
        \u0275\u0275advance(8);
        \u0275\u0275property("fullscreen", true);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", ctx.isLoading);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", !ctx.isLoading);
      }
    }, dependencies: [
      IonicModule,
      IonButton,
      IonButtons,
      IonCard,
      IonCardContent,
      IonCardHeader,
      IonCardTitle,
      IonContent,
      IonHeader,
      IonIcon,
      IonItem,
      IonLabel,
      IonSpinner,
      IonTitle,
      IonToolbar,
      RouterLinkDelegateDirective,
      CommonModule,
      NgIf,
      ReactiveFormsModule,
      RouterModule,
      RouterLink
    ], styles: ["\n\n.welcome-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  height: 100%;\n}\nion-card[_ngcontent-%COMP%] {\n  margin: 0 auto;\n  max-width: 500px;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n}\nion-list[_ngcontent-%COMP%] {\n  margin-top: 16px;\n  margin-bottom: 16px;\n}\nion-item[_ngcontent-%COMP%] {\n  --border-color: rgba(0, 0, 0, 0.1);\n  cursor: pointer;\n}\nion-item[_ngcontent-%COMP%]:hover {\n  --background: rgba(0, 0, 0, 0.05);\n}\n/*# sourceMappingURL=home.page.css.map */"] });
    HomePage = _HomePage;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(HomePage, [{
        type: Component,
        args: [{ selector: "app-home", imports: [
          IonicModule,
          CommonModule,
          ReactiveFormsModule,
          RouterModule
        ], template: '<ion-header [translucent]="true">\n  <ion-toolbar color="primary">\n    <ion-title>\n      Home\n    </ion-title>\n    <ion-buttons slot="end">\n      <ion-button (click)="logout()" fill="clear" color="light">\n        <span class="material-icons">logout</span>\n      </ion-button>\n    </ion-buttons>\n  </ion-toolbar>\n</ion-header>\n\n\n<ion-content [fullscreen]="true" class="ion-padding">\n  <!-- Indicador de carregamento -->\n  <div *ngIf="isLoading" class="ion-text-center ion-padding">\n    <ion-spinner></ion-spinner>\n  </div>\n\n  <div *ngIf="!isLoading">\n    <ion-card>\n      <ion-card-header>\n        <ion-card-title class="ion-text-center">Bem-vindo, {{username}}!</ion-card-title>\n      </ion-card-header>\n\n      <ion-card-content>\n        <p class="ion-text-center">\n          Voc\xEA est\xE1 logado com sucesso no aplicativo.\n        </p>\n      </ion-card-content>\n    </ion-card>\n\n    <!-- Alerta de ve\xEDculo n\xE3o cadastrado -->\n    <ion-card *ngIf="!hasVehicles" color="warning">\n      <ion-card-header>\n        <ion-card-title>\n          <ion-icon name="warning-outline"></ion-icon>\n          Aten\xE7\xE3o\n        </ion-card-title>\n      </ion-card-header>\n      <ion-card-content>\n        <p>Voc\xEA ainda n\xE3o possui nenhum ve\xEDculo cadastrado. Para utilizar o rastreamento de viagens, \xE9 necess\xE1rio cadastrar pelo menos um ve\xEDculo.</p>\n        <ion-button expand="block" color="primary" routerLink="/new-vehicle" class="ion-margin-top">\n          <ion-icon name="car-outline" slot="start"></ion-icon>\n          Cadastrar Ve\xEDculo\n        </ion-button>\n      </ion-card-content>\n    </ion-card>\n\n    <!-- Informa\xE7\xF5es do ve\xEDculo principal -->\n    <ion-card *ngIf="hasVehicles && primaryVehicle">\n      <ion-card-header>\n        <ion-card-title>\n          <ion-icon name="car-outline"></ion-icon>\n          Ve\xEDculo Principal\n        </ion-card-title>\n      </ion-card-header>\n      <ion-card-content>\n        <ion-item lines="none">\n          <ion-label>\n            <h2>{{ primaryVehicle.plate }}</h2>\n            <p>{{ primaryVehicle.brandName }} {{ primaryVehicle.modelName }}</p>\n            <p>Ano: {{ primaryVehicle.year }}</p>\n          </ion-label>\n        </ion-item>\n        <ion-button expand="block" color="primary" routerLink="/tabs/vehicles" class="ion-margin-top">\n          <ion-icon name="list-outline" slot="start"></ion-icon>\n          Gerenciar Ve\xEDculos\n        </ion-button>\n      </ion-card-content>\n    </ion-card>\n\n    <!-- Card de Sincroniza\xE7\xE3o -->\n    <ion-card>\n      <ion-card-header>\n        <ion-card-title>\n          <ion-icon name="sync-outline"></ion-icon>\n          Sincroniza\xE7\xE3o\n        </ion-card-title>\n      </ion-card-header>\n      <ion-card-content>\n        <p>Mantenha seus dados sincronizados entre o aplicativo e o servidor.</p>\n        <ion-button expand="block" color="primary" routerLink="/tabs/sync" class="ion-margin-top">\n          <ion-icon name="sync-outline" slot="start"></ion-icon>\n          Ir para Sincroniza\xE7\xE3o\n        </ion-button>\n      </ion-card-content>\n    </ion-card>\n  </div>\n</ion-content>', styles: ["/* src/app/pages/tabs/home/<USER>/\n.welcome-container {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  height: 100%;\n}\nion-card {\n  margin: 0 auto;\n  max-width: 500px;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n}\nion-list {\n  margin-top: 16px;\n  margin-bottom: 16px;\n}\nion-item {\n  --border-color: rgba(0, 0, 0, 0.1);\n  cursor: pointer;\n}\nion-item:hover {\n  --background: rgba(0, 0, 0, 0.05);\n}\n/*# sourceMappingURL=home.page.css.map */\n"] }]
      }], () => [{ type: Router }, { type: ToastService }, { type: SessionService }, { type: VehicleService }, { type: VehicleStateService }], null);
    })();
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(HomePage, { className: "HomePage", filePath: "src/app/pages/tabs/home/<USER>", lineNumber: 23 });
    })();
  }
});

export {
  HomePage,
  init_home_page
};
//# sourceMappingURL=chunk-WG6NUK5B.js.map
