﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.SetupDomain.Cqrs.Messages
{
    public interface IMessageContext
    {
        string MessageId { get; set; }

        string MessageType { get; set; }

        string CausationId { get; set; }

        string CorrelationId { get; set; }

        void CorrelateWith(IMessageContext message);
    }
}
