{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-fab_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as Host, f as getElement, c as createEvent } from './index-527b9e34.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport { i as inheritAriaAttributes } from './helpers-d94bc8ad.js';\nimport { h as hostContext, o as openURL, c as createColorClasses } from './theme-01f3f29c.js';\nimport { t as close } from './index-e2cf2ceb.js';\nimport './index-cfd9c1f2.js';\nconst fabCss = \":host{position:absolute;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content;height:-webkit-fit-content;height:-moz-fit-content;height:fit-content;z-index:999}:host(.fab-horizontal-center){left:0px;right:0px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto}:host(.fab-horizontal-start){left:calc(10px + var(--ion-safe-area-left, 0px));}:host-context([dir=rtl]):host(.fab-horizontal-start),:host-context([dir=rtl]).fab-horizontal-start{right:calc(10px + var(--ion-safe-area-right, 0px));left:unset}@supports selector(:dir(rtl)){:host(.fab-horizontal-start:dir(rtl)){right:calc(10px + var(--ion-safe-area-right, 0px));left:unset}}:host(.fab-horizontal-end){right:calc(10px + var(--ion-safe-area-right, 0px));}:host-context([dir=rtl]):host(.fab-horizontal-end),:host-context([dir=rtl]).fab-horizontal-end{left:calc(10px + var(--ion-safe-area-left, 0px));right:unset}@supports selector(:dir(rtl)){:host(.fab-horizontal-end:dir(rtl)){left:calc(10px + var(--ion-safe-area-left, 0px));right:unset}}:host(.fab-vertical-top){top:10px}:host(.fab-vertical-top.fab-edge){top:0}:host(.fab-vertical-top.fab-edge) ::slotted(ion-fab-button){margin-top:-50%}:host(.fab-vertical-top.fab-edge) ::slotted(ion-fab-button.fab-button-small){margin-top:calc((-100% + 16px) / 2)}:host(.fab-vertical-top.fab-edge) ::slotted(ion-fab-list.fab-list-side-start),:host(.fab-vertical-top.fab-edge) ::slotted(ion-fab-list.fab-list-side-end){margin-top:-50%}:host(.fab-vertical-top.fab-edge) ::slotted(ion-fab-list.fab-list-side-top),:host(.fab-vertical-top.fab-edge) ::slotted(ion-fab-list.fab-list-side-bottom){margin-top:calc(50% + 10px)}:host(.fab-vertical-bottom){bottom:10px}:host(.fab-vertical-bottom.fab-edge){bottom:0}:host(.fab-vertical-bottom.fab-edge) ::slotted(ion-fab-button){margin-bottom:-50%}:host(.fab-vertical-bottom.fab-edge) ::slotted(ion-fab-button.fab-button-small){margin-bottom:calc((-100% + 16px) / 2)}:host(.fab-vertical-bottom.fab-edge) ::slotted(ion-fab-list.fab-list-side-start),:host(.fab-vertical-bottom.fab-edge) ::slotted(ion-fab-list.fab-list-side-end){margin-bottom:-50%}:host(.fab-vertical-bottom.fab-edge) ::slotted(ion-fab-list.fab-list-side-top),:host(.fab-vertical-bottom.fab-edge) ::slotted(ion-fab-list.fab-list-side-bottom){margin-bottom:calc(50% + 10px)}:host(.fab-vertical-center){top:0px;bottom:0px;margin-top:auto;margin-bottom:auto}\";\nconst IonFabStyle0 = fabCss;\nconst Fab = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.horizontal = undefined;\n    this.vertical = undefined;\n    this.edge = false;\n    this.activated = false;\n  }\n  activatedChanged() {\n    const activated = this.activated;\n    const fab = this.getFab();\n    if (fab) {\n      fab.activated = activated;\n    }\n    Array.from(this.el.querySelectorAll('ion-fab-list')).forEach(list => {\n      list.activated = activated;\n    });\n  }\n  componentDidLoad() {\n    if (this.activated) {\n      this.activatedChanged();\n    }\n  }\n  /**\n   * Close an active FAB list container.\n   */\n  async close() {\n    this.activated = false;\n  }\n  getFab() {\n    return this.el.querySelector('ion-fab-button');\n  }\n  /**\n   * Opens/Closes the FAB list container.\n   * @internal\n   */\n  async toggle() {\n    const hasList = !!this.el.querySelector('ion-fab-list');\n    if (hasList) {\n      this.activated = !this.activated;\n    }\n  }\n  render() {\n    const {\n      horizontal,\n      vertical,\n      edge\n    } = this;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '8a310806d0e748d7ebb0ed3d9a2652038e0f2960',\n      class: {\n        [mode]: true,\n        [`fab-horizontal-${horizontal}`]: horizontal !== undefined,\n        [`fab-vertical-${vertical}`]: vertical !== undefined,\n        'fab-edge': edge\n      }\n    }, h(\"slot\", {\n      key: '9394ef6d6e5b0410fa6ba212171f687fb178ce2d'\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"activated\": [\"activatedChanged\"]\n    };\n  }\n};\nFab.style = IonFabStyle0;\nconst fabButtonIosCss = \":host{--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--background-hover:var(--ion-color-primary-contrast, #fff);--background-hover-opacity:.08;--transition:background-color, opacity 100ms linear;--ripple-color:currentColor;--border-radius:50%;--border-width:0;--border-style:none;--border-color:initial;--padding-top:0;--padding-end:0;--padding-bottom:0;--padding-start:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;width:56px;height:56px;font-size:14px;text-align:center;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:block;position:relative;width:100%;height:100%;-webkit-transform:var(--transform);transform:var(--transform);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);background-clip:padding-box;color:var(--color);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);contain:strict;cursor:pointer;overflow:hidden;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-icon){line-height:1}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.button-inner{left:0;right:0;top:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-transition:all ease-in-out 300ms;transition:all ease-in-out 300ms;-webkit-transition-property:opacity, -webkit-transform;transition-property:opacity, -webkit-transform;transition-property:transform, opacity;transition-property:transform, opacity, -webkit-transform;z-index:1}:host(.fab-button-disabled){cursor:default;opacity:0.5;pointer-events:none}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(.ion-activated) .button-native{color:var(--color-activated)}:host(.ion-activated) .button-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}::slotted(ion-icon){line-height:1}:host(.fab-button-small){-webkit-margin-start:8px;margin-inline-start:8px;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:8px;margin-bottom:8px;width:40px;height:40px}.close-icon{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;left:0;right:0;top:0;position:absolute;height:100%;-webkit-transform:scale(0.4) rotateZ(-45deg);transform:scale(0.4) rotateZ(-45deg);-webkit-transition:all ease-in-out 300ms;transition:all ease-in-out 300ms;-webkit-transition-property:opacity, -webkit-transform;transition-property:opacity, -webkit-transform;transition-property:transform, opacity;transition-property:transform, opacity, -webkit-transform;font-size:var(--close-icon-font-size);opacity:0;z-index:1}:host(.fab-button-close-active) .close-icon{-webkit-transform:scale(1) rotateZ(0deg);transform:scale(1) rotateZ(0deg);opacity:1}:host(.fab-button-close-active) .button-inner{-webkit-transform:scale(0.4) rotateZ(45deg);transform:scale(0.4) rotateZ(45deg);opacity:0}ion-ripple-effect{color:var(--ripple-color)}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.fab-button-translucent) .button-native{-webkit-backdrop-filter:var(--backdrop-filter);backdrop-filter:var(--backdrop-filter)}}:host(.ion-color) .button-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host{--background:var(--ion-color-primary, #0054e9);--background-activated:var(--ion-color-primary-shade, #004acd);--background-focused:var(--ion-color-primary-shade, #004acd);--background-hover:var(--ion-color-primary-tint, #1a65eb);--background-activated-opacity:1;--background-focused-opacity:1;--background-hover-opacity:1;--color:var(--ion-color-primary-contrast, #fff);--box-shadow:0 4px 16px rgba(0, 0, 0, 0.12);--transition:0.2s transform cubic-bezier(0.25, 1.11, 0.78, 1.59);--close-icon-font-size:28px}:host(.ion-activated){--box-shadow:0 4px 16px rgba(0, 0, 0, 0.12);--transform:scale(1.1);--transition:0.2s transform ease-out}::slotted(ion-icon){font-size:28px}:host(.fab-button-in-list){--background:var(--ion-color-light, #f4f5f8);--background-activated:var(--ion-color-light-shade, #d7d8da);--background-focused:var(--background-activated);--background-hover:var(--ion-color-light-tint, #f5f6f9);--color:var(--ion-color-light-contrast, #000);--color-activated:var(--ion-color-light-contrast, #000);--color-focused:var(--color-activated);--transition:transform 200ms ease 10ms, opacity 200ms ease 10ms}:host(.fab-button-in-list) ::slotted(ion-icon){font-size:18px}:host(.ion-color.ion-focused) .button-native::after{background:var(--ion-color-shade)}:host(.ion-color.ion-focused) .button-native,:host(.ion-color.ion-activated) .button-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-focused) .button-native::after,:host(.ion-color.ion-activated) .button-native::after{background:var(--ion-color-shade)}@media (any-hover: hover){:host(.ion-color:hover) .button-native{color:var(--ion-color-contrast)}:host(.ion-color:hover) .button-native::after{background:var(--ion-color-tint)}}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.fab-button-translucent){--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.9);--background-hover:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.8);--background-focused:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.82);--backdrop-filter:saturate(180%) blur(20px)}:host(.fab-button-translucent-in-list){--background:rgba(var(--ion-color-light-rgb, 244, 245, 248), 0.9);--background-hover:rgba(var(--ion-color-light-rgb, 244, 245, 248), 0.8);--background-focused:rgba(var(--ion-color-light-rgb, 244, 245, 248), 0.82)}}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){@media (any-hover: hover){:host(.fab-button-translucent.ion-color:hover) .button-native{background:rgba(var(--ion-color-base-rgb), 0.8)}}:host(.ion-color.fab-button-translucent) .button-native{background:rgba(var(--ion-color-base-rgb), 0.9)}:host(.ion-color.ion-focused.fab-button-translucent) .button-native,:host(.ion-color.ion-activated.fab-button-translucent) .button-native{background:var(--ion-color-base)}}\";\nconst IonFabButtonIosStyle0 = fabButtonIosCss;\nconst fabButtonMdCss = \":host{--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--background-hover:var(--ion-color-primary-contrast, #fff);--background-hover-opacity:.08;--transition:background-color, opacity 100ms linear;--ripple-color:currentColor;--border-radius:50%;--border-width:0;--border-style:none;--border-color:initial;--padding-top:0;--padding-end:0;--padding-bottom:0;--padding-start:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;width:56px;height:56px;font-size:14px;text-align:center;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:block;position:relative;width:100%;height:100%;-webkit-transform:var(--transform);transform:var(--transform);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);background-clip:padding-box;color:var(--color);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);contain:strict;cursor:pointer;overflow:hidden;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-icon){line-height:1}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.button-inner{left:0;right:0;top:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-transition:all ease-in-out 300ms;transition:all ease-in-out 300ms;-webkit-transition-property:opacity, -webkit-transform;transition-property:opacity, -webkit-transform;transition-property:transform, opacity;transition-property:transform, opacity, -webkit-transform;z-index:1}:host(.fab-button-disabled){cursor:default;opacity:0.5;pointer-events:none}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(.ion-activated) .button-native{color:var(--color-activated)}:host(.ion-activated) .button-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}::slotted(ion-icon){line-height:1}:host(.fab-button-small){-webkit-margin-start:8px;margin-inline-start:8px;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:8px;margin-bottom:8px;width:40px;height:40px}.close-icon{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;left:0;right:0;top:0;position:absolute;height:100%;-webkit-transform:scale(0.4) rotateZ(-45deg);transform:scale(0.4) rotateZ(-45deg);-webkit-transition:all ease-in-out 300ms;transition:all ease-in-out 300ms;-webkit-transition-property:opacity, -webkit-transform;transition-property:opacity, -webkit-transform;transition-property:transform, opacity;transition-property:transform, opacity, -webkit-transform;font-size:var(--close-icon-font-size);opacity:0;z-index:1}:host(.fab-button-close-active) .close-icon{-webkit-transform:scale(1) rotateZ(0deg);transform:scale(1) rotateZ(0deg);opacity:1}:host(.fab-button-close-active) .button-inner{-webkit-transform:scale(0.4) rotateZ(45deg);transform:scale(0.4) rotateZ(45deg);opacity:0}ion-ripple-effect{color:var(--ripple-color)}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.fab-button-translucent) .button-native{-webkit-backdrop-filter:var(--backdrop-filter);backdrop-filter:var(--backdrop-filter)}}:host(.ion-color) .button-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host{--background:var(--ion-color-primary, #0054e9);--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor;--background-activated-opacity:0;--background-focused-opacity:.24;--background-hover-opacity:.08;--color:var(--ion-color-primary-contrast, #fff);--box-shadow:0 3px 5px -1px rgba(0, 0, 0, 0.2), 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);--transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1), background-color 280ms cubic-bezier(0.4, 0, 0.2, 1), color 280ms cubic-bezier(0.4, 0, 0.2, 1), opacity 15ms linear 30ms, transform 270ms cubic-bezier(0, 0, 0.2, 1) 0ms;--close-icon-font-size:24px}:host(.ion-activated){--box-shadow:0 7px 8px -4px rgba(0, 0, 0, 0.2), 0 12px 17px 2px rgba(0, 0, 0, 0.14), 0 5px 22px 4px rgba(0, 0, 0, 0.12)}::slotted(ion-icon){font-size:24px}:host(.fab-button-in-list){--color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54);--color-activated:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54);--color-focused:var(--color-activated);--background:var(--ion-color-light, #f4f5f8);--background-activated:transparent;--background-focused:var(--ion-color-light-shade, #d7d8da);--background-hover:var(--ion-color-light-tint, #f5f6f9)}:host(.fab-button-in-list) ::slotted(ion-icon){font-size:18px}:host(.ion-color.ion-focused) .button-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-focused) .button-native::after{background:var(--ion-color-contrast)}:host(.ion-color.ion-activated) .button-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-activated) .button-native::after{background:transparent}@media (any-hover: hover){:host(.ion-color:hover) .button-native{color:var(--ion-color-contrast)}:host(.ion-color:hover) .button-native::after{background:var(--ion-color-contrast)}}\";\nconst IonFabButtonMdStyle0 = fabButtonMdCss;\nconst FabButton = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.fab = null;\n    this.inheritedAttributes = {};\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.onClick = () => {\n      const {\n        fab\n      } = this;\n      if (!fab) {\n        return;\n      }\n      fab.toggle();\n    };\n    this.color = undefined;\n    this.activated = false;\n    this.disabled = false;\n    this.download = undefined;\n    this.href = undefined;\n    this.rel = undefined;\n    this.routerDirection = 'forward';\n    this.routerAnimation = undefined;\n    this.target = undefined;\n    this.show = false;\n    this.translucent = false;\n    this.type = 'button';\n    this.size = undefined;\n    this.closeIcon = close;\n  }\n  connectedCallback() {\n    this.fab = this.el.closest('ion-fab');\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n  }\n  render() {\n    const {\n      el,\n      disabled,\n      color,\n      href,\n      activated,\n      show,\n      translucent,\n      size,\n      inheritedAttributes\n    } = this;\n    const inList = hostContext('ion-fab-list', el);\n    const mode = getIonMode(this);\n    const TagType = href === undefined ? 'button' : 'a';\n    const attrs = TagType === 'button' ? {\n      type: this.type\n    } : {\n      download: this.download,\n      href,\n      rel: this.rel,\n      target: this.target\n    };\n    return h(Host, {\n      key: '4eee204d20b0e2ffed49a88f6cb3e04b6697965c',\n      onClick: this.onClick,\n      \"aria-disabled\": disabled ? 'true' : null,\n      class: createColorClasses(color, {\n        [mode]: true,\n        'fab-button-in-list': inList,\n        'fab-button-translucent-in-list': inList && translucent,\n        'fab-button-close-active': activated,\n        'fab-button-show': show,\n        'fab-button-disabled': disabled,\n        'fab-button-translucent': translucent,\n        'ion-activatable': true,\n        'ion-focusable': true,\n        [`fab-button-${size}`]: size !== undefined\n      })\n    }, h(TagType, Object.assign({\n      key: '914561622c0c6bd41453e828a7d8a39f924875ac'\n    }, attrs, {\n      class: \"button-native\",\n      part: \"native\",\n      disabled: disabled,\n      onFocus: this.onFocus,\n      onBlur: this.onBlur,\n      onClick: ev => openURL(href, ev, this.routerDirection, this.routerAnimation)\n    }, inheritedAttributes), h(\"ion-icon\", {\n      key: '2c8090742a64c62a79243667027a195cca9d5912',\n      \"aria-hidden\": \"true\",\n      icon: this.closeIcon,\n      part: \"close-icon\",\n      class: \"close-icon\",\n      lazy: false\n    }), h(\"span\", {\n      key: 'c3e55291e4c4d306d34a4b95dd2e727e87bdf39c',\n      class: \"button-inner\"\n    }, h(\"slot\", {\n      key: 'f8e57f71d8f8878d9746cfece82f57f19ef9e988'\n    })), mode === 'md' && h(\"ion-ripple-effect\", {\n      key: 'a5e94fa0bb9836072300617245ed0c1b4887bac6'\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nFabButton.style = {\n  ios: IonFabButtonIosStyle0,\n  md: IonFabButtonMdStyle0\n};\nconst fabListCss = \":host{margin-left:0;margin-right:0;margin-top:calc(100% + 10px);margin-bottom:calc(100% + 10px);display:none;position:absolute;top:0;-ms-flex-direction:column;flex-direction:column;-ms-flex-align:center;align-items:center;min-width:56px;min-height:56px}:host(.fab-list-active){display:-ms-flexbox;display:flex}::slotted(.fab-button-in-list){margin-left:0;margin-right:0;margin-top:8px;margin-bottom:8px;width:40px;height:40px;-webkit-transform:scale(0);transform:scale(0);opacity:0;visibility:hidden}:host(.fab-list-side-top) ::slotted(.fab-button-in-list),:host(.fab-list-side-bottom) ::slotted(.fab-button-in-list){margin-left:0;margin-right:0;margin-top:5px;margin-bottom:5px}:host(.fab-list-side-start) ::slotted(.fab-button-in-list),:host(.fab-list-side-end) ::slotted(.fab-button-in-list){-webkit-margin-start:5px;margin-inline-start:5px;-webkit-margin-end:5px;margin-inline-end:5px;margin-top:0;margin-bottom:0}::slotted(.fab-button-in-list.fab-button-show){-webkit-transform:scale(1);transform:scale(1);opacity:1;visibility:visible}:host(.fab-list-side-top){top:auto;bottom:0;-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.fab-list-side-start){-webkit-margin-start:calc(100% + 10px);margin-inline-start:calc(100% + 10px);-webkit-margin-end:calc(100% + 10px);margin-inline-end:calc(100% + 10px);margin-top:0;margin-bottom:0;-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.fab-list-side-start){inset-inline-end:0}:host(.fab-list-side-end){-webkit-margin-start:calc(100% + 10px);margin-inline-start:calc(100% + 10px);-webkit-margin-end:calc(100% + 10px);margin-inline-end:calc(100% + 10px);margin-top:0;margin-bottom:0;-ms-flex-direction:row;flex-direction:row}:host(.fab-list-side-end){inset-inline-start:0}\";\nconst IonFabListStyle0 = fabListCss;\nconst FabList = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.activated = false;\n    this.side = 'bottom';\n  }\n  activatedChanged(activated) {\n    const fabs = Array.from(this.el.querySelectorAll('ion-fab-button'));\n    // if showing the fabs add a timeout, else show immediately\n    const timeout = activated ? 30 : 0;\n    fabs.forEach((fab, i) => {\n      setTimeout(() => fab.show = activated, i * timeout);\n    });\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '64b33366447f66c7f979cfac56307fbb1a6fac1c',\n      class: {\n        [mode]: true,\n        'fab-list-active': this.activated,\n        [`fab-list-side-${this.side}`]: true\n      }\n    }, h(\"slot\", {\n      key: 'd9f474f7f20fd7e813db358fddc720534ca05bb6'\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"activated\": [\"activatedChanged\"]\n    };\n  }\n};\nFabList.style = IonFabListStyle0;\nexport { Fab as ion_fab, FabButton as ion_fab_button, FabList as ion_fab_list };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IASM,QACA,cACA,KAuEA,iBACA,uBACA,gBACA,sBACA,WAmHA,YACA,kBACA;AA3MN;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,IAAM,SAAS;AACf,IAAM,eAAe;AACrB,IAAM,MAAM,MAAM;AAAA,MAChB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,aAAa;AAClB,aAAK,WAAW;AAChB,aAAK,OAAO;AACZ,aAAK,YAAY;AAAA,MACnB;AAAA,MACA,mBAAmB;AACjB,cAAM,YAAY,KAAK;AACvB,cAAM,MAAM,KAAK,OAAO;AACxB,YAAI,KAAK;AACP,cAAI,YAAY;AAAA,QAClB;AACA,cAAM,KAAK,KAAK,GAAG,iBAAiB,cAAc,CAAC,EAAE,QAAQ,UAAQ;AACnE,eAAK,YAAY;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,MACA,mBAAmB;AACjB,YAAI,KAAK,WAAW;AAClB,eAAK,iBAAiB;AAAA,QACxB;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAIM,QAAQ;AAAA;AACZ,eAAK,YAAY;AAAA,QACnB;AAAA;AAAA,MACA,SAAS;AACP,eAAO,KAAK,GAAG,cAAc,gBAAgB;AAAA,MAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,MAKM,SAAS;AAAA;AACb,gBAAM,UAAU,CAAC,CAAC,KAAK,GAAG,cAAc,cAAc;AACtD,cAAI,SAAS;AACX,iBAAK,YAAY,CAAC,KAAK;AAAA,UACzB;AAAA,QACF;AAAA;AAAA,MACA,SAAS;AACP,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,OAAO,WAAW,IAAI;AAC5B,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,OAAO;AAAA,YACL,CAAC,IAAI,GAAG;AAAA,YACR,CAAC,kBAAkB,UAAU,EAAE,GAAG,eAAe;AAAA,YACjD,CAAC,gBAAgB,QAAQ,EAAE,GAAG,aAAa;AAAA,YAC3C,YAAY;AAAA,UACd;AAAA,QACF,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,QACP,CAAC,CAAC;AAAA,MACJ;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,aAAa,CAAC,kBAAkB;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AACA,QAAI,QAAQ;AACZ,IAAM,kBAAkB;AACxB,IAAM,wBAAwB;AAC9B,IAAM,iBAAiB;AACvB,IAAM,uBAAuB;AAC7B,IAAM,YAAY,MAAM;AAAA,MACtB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,aAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,aAAK,MAAM;AACX,aAAK,sBAAsB,CAAC;AAC5B,aAAK,UAAU,MAAM;AACnB,eAAK,SAAS,KAAK;AAAA,QACrB;AACA,aAAK,SAAS,MAAM;AAClB,eAAK,QAAQ,KAAK;AAAA,QACpB;AACA,aAAK,UAAU,MAAM;AACnB,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,cAAI,CAAC,KAAK;AACR;AAAA,UACF;AACA,cAAI,OAAO;AAAA,QACb;AACA,aAAK,QAAQ;AACb,aAAK,YAAY;AACjB,aAAK,WAAW;AAChB,aAAK,WAAW;AAChB,aAAK,OAAO;AACZ,aAAK,MAAM;AACX,aAAK,kBAAkB;AACvB,aAAK,kBAAkB;AACvB,aAAK,SAAS;AACd,aAAK,OAAO;AACZ,aAAK,cAAc;AACnB,aAAK,OAAO;AACZ,aAAK,OAAO;AACZ,aAAK,YAAY;AAAA,MACnB;AAAA,MACA,oBAAoB;AAClB,aAAK,MAAM,KAAK,GAAG,QAAQ,SAAS;AAAA,MACtC;AAAA,MACA,oBAAoB;AAClB,aAAK,sBAAsB,sBAAsB,KAAK,EAAE;AAAA,MAC1D;AAAA,MACA,SAAS;AACP,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,SAAS,YAAY,gBAAgB,EAAE;AAC7C,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM,UAAU,SAAS,SAAY,WAAW;AAChD,cAAM,QAAQ,YAAY,WAAW;AAAA,UACnC,MAAM,KAAK;AAAA,QACb,IAAI;AAAA,UACF,UAAU,KAAK;AAAA,UACf;AAAA,UACA,KAAK,KAAK;AAAA,UACV,QAAQ,KAAK;AAAA,QACf;AACA,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,SAAS,KAAK;AAAA,UACd,iBAAiB,WAAW,SAAS;AAAA,UACrC,OAAO,mBAAmB,OAAO;AAAA,YAC/B,CAAC,IAAI,GAAG;AAAA,YACR,sBAAsB;AAAA,YACtB,kCAAkC,UAAU;AAAA,YAC5C,2BAA2B;AAAA,YAC3B,mBAAmB;AAAA,YACnB,uBAAuB;AAAA,YACvB,0BAA0B;AAAA,YAC1B,mBAAmB;AAAA,YACnB,iBAAiB;AAAA,YACjB,CAAC,cAAc,IAAI,EAAE,GAAG,SAAS;AAAA,UACnC,CAAC;AAAA,QACH,GAAG,EAAE,SAAS,OAAO,OAAO;AAAA,UAC1B,KAAK;AAAA,QACP,GAAG,OAAO;AAAA,UACR,OAAO;AAAA,UACP,MAAM;AAAA,UACN;AAAA,UACA,SAAS,KAAK;AAAA,UACd,QAAQ,KAAK;AAAA,UACb,SAAS,QAAM,QAAQ,MAAM,IAAI,KAAK,iBAAiB,KAAK,eAAe;AAAA,QAC7E,GAAG,mBAAmB,GAAG,EAAE,YAAY;AAAA,UACrC,KAAK;AAAA,UACL,eAAe;AAAA,UACf,MAAM,KAAK;AAAA,UACX,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC,GAAG,EAAE,QAAQ;AAAA,UACZ,KAAK;AAAA,UACL,OAAO;AAAA,QACT,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,QACP,CAAC,CAAC,GAAG,SAAS,QAAQ,EAAE,qBAAqB;AAAA,UAC3C,KAAK;AAAA,QACP,CAAC,CAAC,CAAC;AAAA,MACL;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,IACF;AACA,cAAU,QAAQ;AAAA,MAChB,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AACA,IAAM,aAAa;AACnB,IAAM,mBAAmB;AACzB,IAAM,UAAU,MAAM;AAAA,MACpB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,YAAY;AACjB,aAAK,OAAO;AAAA,MACd;AAAA,MACA,iBAAiB,WAAW;AAC1B,cAAM,OAAO,MAAM,KAAK,KAAK,GAAG,iBAAiB,gBAAgB,CAAC;AAElE,cAAM,UAAU,YAAY,KAAK;AACjC,aAAK,QAAQ,CAAC,KAAK,MAAM;AACvB,qBAAW,MAAM,IAAI,OAAO,WAAW,IAAI,OAAO;AAAA,QACpD,CAAC;AAAA,MACH;AAAA,MACA,SAAS;AACP,cAAM,OAAO,WAAW,IAAI;AAC5B,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,OAAO;AAAA,YACL,CAAC,IAAI,GAAG;AAAA,YACR,mBAAmB,KAAK;AAAA,YACxB,CAAC,iBAAiB,KAAK,IAAI,EAAE,GAAG;AAAA,UAClC;AAAA,QACF,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,QACP,CAAC,CAAC;AAAA,MACJ;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,aAAa,CAAC,kBAAkB;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AACA,YAAQ,QAAQ;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}