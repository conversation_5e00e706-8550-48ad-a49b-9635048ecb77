{"name": "@capacitor/geolocation", "version": "7.1.2", "description": "The Geolocation API provides simple methods for getting and tracking the current position of the device using GPS, along with altitude, heading, and speed information if available.", "main": "dist/plugin.cjs.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "unpkg": "dist/plugin.js", "files": ["android/src/main/", "android/build.gradle", "dist/", "ios/Sources", "ios/Tests", "Package.swift", "CapacitorGeolocation.podspec"], "author": "Outsystems", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ionic-team/capacitor-geolocation.git"}, "bugs": {"url": "https://github.com/ionic-team/capacitor-geolocation/issues"}, "keywords": ["capacitor", "plugin", "native"], "scripts": {"verify": "npm run verify:ios && npm run verify:android && npm run verify:web", "verify:ios": "xcodebuild -scheme GeolocationCapacitor -destination generic/platform=iOS", "verify:android": "cd android && ./gradlew clean build test && cd ..", "verify:web": "npm run build", "lint": "npm run eslint && npm run prettier -- --check && npm run swiftlint -- lint", "fmt": "npm run eslint -- --fix && npm run prettier -- --write && npm run swiftlint -- --fix --format", "eslint": "eslint . --ext ts", "prettier": "prettier \"**/*.{css,html,ts,js,java}\" --plugin=prettier-plugin-java", "swiftlint": "node-swiftlint", "docgen": "docgen --api GeolocationPlugin --output-readme README.md --output-json dist/docs.json && cp README.md ../../README.md", "build": "npm run clean && npm run docgen && tsc && rollup -c rollup.config.mjs", "clean": "rimraf ./dist", "watch": "tsc --watch", "prepublishOnly": "npm run build"}, "dependencies": {"@capacitor/synapse": "^1.0.1"}, "devDependencies": {"@capacitor/android": "next", "@capacitor/core": "next", "@capacitor/docgen": "^0.2.2", "@capacitor/ios": "next", "@ionic/eslint-config": "^0.4.0", "@ionic/prettier-config": "^4.0.0", "@ionic/swiftlint-config": "^2.0.0", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.1", "@semantic-release/npm": "^12.0.1", "@types/node": "^20.14.8", "eslint": "^8.57.0", "prettier": "^3.3.3", "prettier-plugin-java": "^2.6.4", "rimraf": "^6.0.1", "rollup": "^2.78.1", "semantic-release": "^24.0.0", "swiftlint": "^2.0.0", "typescript": "~5.4.5"}, "peerDependencies": {"@capacitor/core": ">=7.0.0"}, "prettier": "@ionic/prettier-config", "swiftlint": "@ionic/swiftlint-config", "eslintConfig": {"extends": "@ionic/eslint-config/recommended"}, "capacitor": {"ios": {"src": "ios"}, "android": {"src": "android"}}}