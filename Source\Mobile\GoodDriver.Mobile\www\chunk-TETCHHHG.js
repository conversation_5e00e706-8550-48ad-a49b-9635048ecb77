import {
  __async,
  __esm
} from "./chunk-4W6HR7MY.js";

// node_modules/@ionic/core/dist/esm/lock-controller-316928be.js
var createLockController;
var init_lock_controller_316928be = __esm({
  "node_modules/@ionic/core/dist/esm/lock-controller-316928be.js"() {
    "use strict";
    createLockController = () => {
      let waitPromise;
      const lock = () => __async(null, null, function* () {
        const p = waitPromise;
        let resolve;
        waitPromise = new Promise((r) => resolve = r);
        if (p !== void 0) {
          yield p;
        }
        return resolve;
      });
      return {
        lock
      };
    };
  }
});

export {
  createLockController,
  init_lock_controller_316928be
};
/*! Bundled license information:

@ionic/core/dist/esm/lock-controller-316928be.js:
  (*!
   * (C) Ionic http://ionicframework.com - MIT License
   *)
*/
//# sourceMappingURL=chunk-TETCHHHG.js.map
