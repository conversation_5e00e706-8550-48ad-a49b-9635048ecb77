import { Injectable } from '@angular/core';
import { IDataStorage } from './data-storage.interface';

@Injectable({
  providedIn: 'root',
})
export class IndexedDBStorageService implements IDataStorage {
  private dbName = 'journeydb';
  private dbVersion = 10; // Increased version to handle sync status fields
  private db: IDBDatabase | null = null;

  constructor() {}

  // Inicializa o IndexedDB para o navegador
  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion + 1);  // Aumente a versão para forçar o upgrade


      request.onerror = () => reject('Erro ao abrir IndexedDB');
      request.onsuccess = (event) => {
        this.db = (event.target as IDBOpenDBRequest).result;
        console.log('Banco de dados aberto com sucesso!');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        console.log('Banco de dados em processo de upgrade...');
        // Criação da tabela "journeys"
        if (!db.objectStoreNames.contains('journeys')) {
          const journeysStore = db.createObjectStore('journeys', { keyPath: 'id' });
          console.log('Tabela "journeys" criada com sucesso!');
        }

        // Criação da tabela "location"
        if (!db.objectStoreNames.contains('journeyInfo')) {
          const locationStore = db.createObjectStore('journeyInfo', { keyPath: 'id' });
          console.log('Tabela "location" criada com sucesso!');
        }

        // Criação da tabela "vehicles"
        if (!db.objectStoreNames.contains('vehicles')) {
          const VehicleStore = db.createObjectStore('vehicles', { keyPath: 'id' });
          console.log('Tabela "vehicles" criada com sucesso!');
        }
      };
    });
  }


  async insert(table: string, data: any): Promise<any> {
    if (!this.db) {
      await this.init();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([table], 'readwrite');
      const store = transaction.objectStore(table);
      const request = store.add(data);

      request.onsuccess = () => {
        console.log('Cadastrou veiculo no IndexedDB com sucesso');
        resolve(request.result);
      };

      request.onerror = (event) => {
        console.error('Erro ao cadastrar veiculo no IndexedDB:', event);
        reject(event);
      };
    });
  }

  async select(table: string, condition: string = ''): Promise<any[]> {
    if (!this.db) {
      await this.init();
    }
    const transaction = this.db!.transaction([table], 'readonly');
    const store = transaction.objectStore(table);
    const request = store.getAll();

    return new Promise((resolve, reject) => {
      request.onsuccess = (e) => {
        if (e.target) {
          resolve((e.target as IDBRequest).result);
        } else {
          reject(new Error('Request target is null'));
        }
      };
      request.onerror = (e) => reject(e);
    });
  }

  async update(table: string, data: any, condition: string): Promise<any> {
    if (!this.db) {
      await this.init();
    }

    // Make sure the data object has the id field
    if (!data.id && condition) {
      // Try to extract the ID from the condition string (e.g., "id = '123'")
      const idMatch = condition.match(/id\s*=\s*['"]([^'"]+)['"]/i);
      if (idMatch && idMatch[1]) {
        data.id = idMatch[1];
      }
    }

    // Ensure we have an ID before proceeding
    if (!data.id) {
      console.error('Cannot update object without ID');
      throw new Error('Cannot update object without ID');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([table], 'readwrite');
      const store = transaction.objectStore(table);

      const request = store.put(data);

      request.onsuccess = () => resolve(true);
      request.onerror = (event) => {
        console.error('Error updating data:', event);
        reject(event);
      };
    });
  }

  async delete(table: string, condition: string): Promise<void> {
    if (!this.db) {
      await this.init();
    }
    const transaction = this.db!.transaction([table], 'readwrite');
    const store = transaction.objectStore(table);
    store.delete(condition);
  }
}
