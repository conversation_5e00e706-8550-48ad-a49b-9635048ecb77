﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Migration.Schema
{
	[FluentMigrator.Migration(20250409212500)]
	public class CreateTableJourneyInfo : FluentMigrator.Migration
	{
		public override void Down()
		{
			if (Schema.Table("JourneyInfo").Exists())
			{
				Delete.Table("JourneyInfo");
			}
		}

		public override void Up()
		{
			if (!Schema.Table("JourneyInfo").Exists())
			{
				Create.Table("JourneyInfo")
					.WithColumn("Id").AsGuid().PrimaryKey().NotNullable()
					.WithColumn("JourneyId").AsGuid().NotNullable().ForeignKey("FK_JourneyInfo_Journey", "Journey", "Id")
					.WithColumn("Date").AsDateTime().NotNullable()
					.WithColumn("Latitude").AsString(50).NotNullable()
					.WithColumn("OccurrenceId").AsInt32().Nullable().Foreign<PERSON><PERSON>("FK_JourneyInfo_Occurrence", "Occurrence", "Id")
					.WithColumn("Longitude").AsString(50).NotNullable();
			}
		}
	}
}
