{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-datetime_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, w as writeTask, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { startFocusVisible } from './focus-visible-dd40d69f.js';\nimport { r as raf, d as renderHiddenInput, g as getElementRoot, j as clamp } from './helpers-d94bc8ad.js';\nimport { d as printIonError, p as printIonWarning } from './index-cfd9c1f2.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { c as createColorClasses, g as getClassMap } from './theme-01f3f29c.js';\nimport { c as chevronBack, o as chevronForward, l as chevronDown, p as caretUpSharp, q as caretDownSharp } from './index-e2cf2ceb.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport { g as generateDay<PERSON>riaLabel, a as getDay, i as isBefore, b as isAfter, c as isSameDay, d as getPreviousMonth, e as getNextMonth, v as validateParts, f as getPartsFromCalendarDay, h as getNextYear, j as getPreviousYear, k as getEndOfWeek, l as getStartOfWeek, m as getPreviousDay, n as getNextDay, o as getPreviousWeek, p as getNextWeek, q as parseMinParts, r as parseMaxParts, s as parseDate, w as warnIfValueOutOfBounds, t as convertToArrayOfNumbers, u as convertDataToISO, x as getToday, y as getClosestValidDate, z as generateMonths, A as getNumDaysInMonth, B as getCombinedDateColumnData, C as getMonthColumnData, D as getDayColumnData, E as getYearColumnData, F as isMonthFirstLocale, G as getTimeColumnsData, H as isLocaleDayPeriodRTL, I as getDaysOfWeek, J as getMonthAndYear, K as getDaysOfMonth, L as getHourCycle, M as getLocalizedTime, N as getLocalizedDateTime, O as formatValue, P as clampDate, Q as parseAmPm, R as calculateHourFromAMPM } from './data-0d7ea6eb.js';\nimport { c as createLockController } from './lock-controller-316928be.js';\nimport { d as createDelegateController, e as createTriggerController, B as BACKDROP, i as isCancel, j as prepareOverlay, k as setOverlayId, f as present, g as dismiss, h as eventMethod, s as safeCall } from './overlays-d99dcb0a.js';\nimport { c as createAnimation } from './animation-8b25e105.js';\nimport { b as hapticSelectionChanged, h as hapticSelectionEnd, a as hapticSelectionStart } from './haptic-ac164e4c.js';\nimport './index-a5d50daf.js';\nimport './hardware-back-button-a7eb8233.js';\nimport './framework-delegate-56b467ad.js';\nimport './gesture-controller-314a54f6.js';\nimport './capacitor-59395cbd.js';\nconst isYearDisabled = (refYear, minParts, maxParts) => {\n  if (minParts && minParts.year > refYear) {\n    return true;\n  }\n  if (maxParts && maxParts.year < refYear) {\n    return true;\n  }\n  return false;\n};\n/**\n * Returns true if a given day should\n * not be interactive according to its value,\n * or the max/min dates.\n */\nconst isDayDisabled = (refParts, minParts, maxParts, dayValues) => {\n  /**\n   * If this is a filler date (i.e. padding)\n   * then the date is disabled.\n   */\n  if (refParts.day === null) {\n    return true;\n  }\n  /**\n   * If user passed in a list of acceptable day values\n   * check to make sure that the date we are looking\n   * at is in this array.\n   */\n  if (dayValues !== undefined && !dayValues.includes(refParts.day)) {\n    return true;\n  }\n  /**\n   * Given a min date, perform the following\n   * checks. If any of them are true, then the\n   * day should be disabled:\n   * 1. Is the current year < the min allowed year?\n   * 2. Is the current year === min allowed year,\n   * but the current month < the min allowed month?\n   * 3. Is the current year === min allowed year, the\n   * current month === min allow month, but the current\n   * day < the min allowed day?\n   */\n  if (minParts && isBefore(refParts, minParts)) {\n    return true;\n  }\n  /**\n   * Given a max date, perform the following\n   * checks. If any of them are true, then the\n   * day should be disabled:\n   * 1. Is the current year > the max allowed year?\n   * 2. Is the current year === max allowed year,\n   * but the current month > the max allowed month?\n   * 3. Is the current year === max allowed year, the\n   * current month === max allow month, but the current\n   * day > the max allowed day?\n   */\n  if (maxParts && isAfter(refParts, maxParts)) {\n    return true;\n  }\n  /**\n   * If none of these checks\n   * passed then the date should\n   * be interactive.\n   */\n  return false;\n};\n/**\n * Given a locale, a date, the selected date(s), and today's date,\n * generate the state for a given calendar day button.\n */\nconst getCalendarDayState = (locale, refParts, activeParts, todayParts, minParts, maxParts, dayValues) => {\n  /**\n   * activeParts signals what day(s) are currently selected in the datetime.\n   * If multiple=\"true\", this will be an array, but the logic in this util\n   * is the same whether we have one selected day or many because we're only\n   * calculating the state for one button. So, we treat a single activeParts value\n   * the same as an array of length one.\n   */\n  const activePartsArray = Array.isArray(activeParts) ? activeParts : [activeParts];\n  /**\n   * The day button is active if it is selected, or in other words, if refParts\n   * matches at least one selected date.\n   */\n  const isActive = activePartsArray.find(parts => isSameDay(refParts, parts)) !== undefined;\n  const isToday = isSameDay(refParts, todayParts);\n  const disabled = isDayDisabled(refParts, minParts, maxParts, dayValues);\n  /**\n   * Note that we always return one object regardless of whether activeParts\n   * was an array, since we pare down to one value for isActive.\n   */\n  return {\n    disabled,\n    isActive,\n    isToday,\n    ariaSelected: isActive ? 'true' : null,\n    ariaLabel: generateDayAriaLabel(locale, isToday, refParts),\n    text: refParts.day != null ? getDay(locale, refParts) : null\n  };\n};\n/**\n * Returns `true` if the month is disabled given the\n * current date value and min/max date constraints.\n */\nconst isMonthDisabled = (refParts, {\n  minParts,\n  maxParts\n}) => {\n  // If the year is disabled then the month is disabled.\n  if (isYearDisabled(refParts.year, minParts, maxParts)) {\n    return true;\n  }\n  // If the date value is before the min date, then the month is disabled.\n  // If the date value is after the max date, then the month is disabled.\n  if (minParts && isBefore(refParts, minParts) || maxParts && isAfter(refParts, maxParts)) {\n    return true;\n  }\n  return false;\n};\n/**\n * Given a working date, an optional minimum date range,\n * and an optional maximum date range; determine if the\n * previous navigation button is disabled.\n */\nconst isPrevMonthDisabled = (refParts, minParts, maxParts) => {\n  const prevMonth = Object.assign(Object.assign({}, getPreviousMonth(refParts)), {\n    day: null\n  });\n  return isMonthDisabled(prevMonth, {\n    minParts,\n    maxParts\n  });\n};\n/**\n * Given a working date and a maximum date range,\n * determine if the next navigation button is disabled.\n */\nconst isNextMonthDisabled = (refParts, maxParts) => {\n  const nextMonth = Object.assign(Object.assign({}, getNextMonth(refParts)), {\n    day: null\n  });\n  return isMonthDisabled(nextMonth, {\n    maxParts\n  });\n};\n/**\n * Given the value of the highlightedDates property\n * and an ISO string, return the styles to use for\n * that date, or undefined if none are found.\n */\nconst getHighlightStyles = (highlightedDates, dateIsoString, el) => {\n  if (Array.isArray(highlightedDates)) {\n    const dateStringWithoutTime = dateIsoString.split('T')[0];\n    const matchingHighlight = highlightedDates.find(hd => hd.date === dateStringWithoutTime);\n    if (matchingHighlight) {\n      return {\n        textColor: matchingHighlight.textColor,\n        backgroundColor: matchingHighlight.backgroundColor\n      };\n    }\n  } else {\n    /**\n     * Wrap in a try-catch to prevent exceptions in the user's function\n     * from interrupting the calendar's rendering.\n     */\n    try {\n      return highlightedDates(dateIsoString);\n    } catch (e) {\n      printIonError('[ion-datetime] - Exception thrown from provided `highlightedDates` callback. Please check your function and try again.', el, e);\n    }\n  }\n  return undefined;\n};\n\n/**\n * If a time zone is provided in the format options, the rendered text could\n * differ from what was selected in the Datetime, which could cause\n * confusion.\n */\nconst warnIfTimeZoneProvided = (el, formatOptions) => {\n  var _a, _b, _c, _d;\n  if (((_a = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) === null || _a === void 0 ? void 0 : _a.timeZone) || ((_b = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) === null || _b === void 0 ? void 0 : _b.timeZoneName) || ((_c = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time) === null || _c === void 0 ? void 0 : _c.timeZone) || ((_d = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time) === null || _d === void 0 ? void 0 : _d.timeZoneName)) {\n    printIonWarning('[ion-datetime] - \"timeZone\" and \"timeZoneName\" are not supported in \"formatOptions\".', el);\n  }\n};\nconst checkForPresentationFormatMismatch = (el, presentation, formatOptions) => {\n  // formatOptions is not required\n  if (!formatOptions) return;\n  // If formatOptions is provided, the date and/or time objects are required, depending on the presentation\n  switch (presentation) {\n    case 'date':\n    case 'month-year':\n    case 'month':\n    case 'year':\n      if (formatOptions.date === undefined) {\n        printIonWarning(`[ion-datetime] - The '${presentation}' presentation requires a date object in formatOptions.`, el);\n      }\n      break;\n    case 'time':\n      if (formatOptions.time === undefined) {\n        printIonWarning(`[ion-datetime] - The 'time' presentation requires a time object in formatOptions.`, el);\n      }\n      break;\n    case 'date-time':\n    case 'time-date':\n      if (formatOptions.date === undefined && formatOptions.time === undefined) {\n        printIonWarning(`[ion-datetime] - The '${presentation}' presentation requires either a date or time object (or both) in formatOptions.`, el);\n      }\n      break;\n  }\n};\nconst datetimeIosCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;background:var(--background);overflow:hidden}:host(.datetime-size-fixed){width:auto;height:auto}:host(.datetime-size-fixed:not(.datetime-prefer-wheel)){max-width:350px}:host(.datetime-size-fixed.datetime-prefer-wheel){min-width:350px;max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content}:host(.datetime-size-cover){width:100%}:host .calendar-body,:host .datetime-year{opacity:0}:host(:not(.datetime-ready)) .datetime-year{position:absolute;pointer-events:none}:host(.datetime-ready) .calendar-body{opacity:1}:host(.datetime-ready) .datetime-year{display:none;opacity:1}:host .wheel-order-year-first .day-column{-ms-flex-order:3;order:3;text-align:end}:host .wheel-order-year-first .month-column{-ms-flex-order:2;order:2;text-align:end}:host .wheel-order-year-first .year-column{-ms-flex-order:1;order:1;text-align:start}:host .datetime-calendar,:host .datetime-year{display:-ms-flexbox;display:flex;-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-flow:column;flex-flow:column}:host(.show-month-and-year) .datetime-year{display:-ms-flexbox;display:flex}:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{display:none}:host(.month-year-picker-open) .datetime-footer{display:none}:host(.datetime-disabled){pointer-events:none}:host(.datetime-disabled) .calendar-days-of-week,:host(.datetime-disabled) .datetime-time{opacity:0.4}:host(.datetime-readonly){pointer-events:none;}:host(.datetime-readonly) .calendar-action-buttons,:host(.datetime-readonly) .calendar-body,:host(.datetime-readonly) .datetime-year{pointer-events:initial}:host(.datetime-readonly) .calendar-day[disabled]:not(.calendar-day-constrained),:host(.datetime-readonly) .datetime-action-buttons ion-button[disabled]{opacity:1}:host .datetime-header .datetime-title{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host .datetime-action-buttons.has-clear-button{width:100%}:host .datetime-action-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.datetime-action-buttons .datetime-action-buttons-container{display:-ms-flexbox;display:flex}:host .calendar-action-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons ion-button{--background:transparent}:host .calendar-days-of-week{display:grid;grid-template-columns:repeat(7, 1fr);text-align:center}.calendar-days-of-week .day-of-week{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}:host .calendar-body{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;overflow-x:scroll;overflow-y:hidden;scrollbar-width:none;outline:none}:host .calendar-body .calendar-month{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;scroll-snap-align:start;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}:host .calendar-body .calendar-month-disabled{scroll-snap-align:none}:host .calendar-body::-webkit-scrollbar{display:none}:host .calendar-body .calendar-month-grid{display:grid;grid-template-columns:repeat(7, 1fr)}:host .calendar-day-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:0;min-height:0;overflow:visible}.calendar-day{border-radius:50%;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:0px;margin-bottom:0px;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border:none;outline:none;background:none;color:currentColor;font-family:var(--ion-font-family, inherit);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:0}:host .calendar-day[disabled]{pointer-events:none;opacity:0.4}.calendar-day:focus{background:rgba(var(--ion-color-base-rgb), 0.2);-webkit-box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2);box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2)}:host .datetime-time{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host(.datetime-presentation-time) .datetime-time{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host ion-popover{--height:200px}:host .time-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .time-body{border-radius:8px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px;display:-ms-flexbox;display:flex;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host .time-body-active{color:var(--ion-color-base)}:host(.in-item){position:static}:host(.show-month-and-year) .calendar-action-buttons .calendar-month-year-toggle{color:var(--ion-color-base)}.calendar-month-year{min-width:0}.calendar-month-year-toggle{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;position:relative;border:0;outline:none;background:transparent;cursor:pointer;z-index:1}.calendar-month-year-toggle::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;-webkit-transition:opacity 15ms linear, background-color 15ms linear;transition:opacity 15ms linear, background-color 15ms linear;z-index:-1}.calendar-month-year-toggle.ion-focused::after{background:currentColor}.calendar-month-year-toggle:disabled{opacity:0.3;pointer-events:none}.calendar-month-year-toggle ion-icon{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0;-ms-flex-negative:0;flex-shrink:0}.calendar-month-year-toggle #toggle-wrapper{display:-ms-inline-flexbox;display:inline-flex;-ms-flex-align:center;align-items:center}ion-picker{--highlight-background:var(--wheel-highlight-background);--highlight-border-radius:var(--wheel-highlight-border-radius);--fade-background-rgb:var(--wheel-fade-background-rgb)}:host{--background:var(--ion-color-light, #f4f5f8);--background-rgb:var(--ion-color-light-rgb, 244, 245, 248);--title-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}:host(.datetime-presentation-date-time:not(.datetime-prefer-wheel)),:host(.datetime-presentation-time-date:not(.datetime-prefer-wheel)),:host(.datetime-presentation-date:not(.datetime-prefer-wheel)){min-height:350px}:host .datetime-header{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:16px;padding-bottom:16px;border-bottom:0.55px solid var(--ion-color-step-200, var(--ion-background-color-step-200, #cccccc));font-size:min(0.875rem, 22.4px)}:host .datetime-header .datetime-title{color:var(--title-color)}:host .datetime-header .datetime-selected-date{margin-top:10px}.calendar-month-year-toggle{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0px;padding-bottom:0px;min-height:44px;font-size:min(1rem, 25.6px);font-weight:600}.calendar-month-year-toggle.ion-focused::after{opacity:0.15}.calendar-month-year-toggle #toggle-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:10px;margin-bottom:10px}:host .calendar-action-buttons .calendar-month-year-toggle ion-icon,:host .calendar-action-buttons ion-buttons ion-button{color:var(--ion-color-base)}:host .calendar-action-buttons ion-buttons{padding-left:0;padding-right:0;padding-top:8px;padding-bottom:0}:host .calendar-action-buttons ion-buttons ion-button{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}:host .calendar-days-of-week{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0;color:var(--ion-color-step-300, var(--ion-text-color-step-700, #b3b3b3));font-size:min(0.75rem, 19.2px);font-weight:600;line-height:24px;text-transform:uppercase}@supports (border-radius: mod(1px, 1px)){.calendar-days-of-week .day-of-week{width:clamp(20px, calc(mod(min(1rem, 24px), 24px) * 10), 100%);height:24px;overflow:hidden}.calendar-day{border-radius:max(8px, mod(min(1rem, 24px), 24px) * 10)}}@supports ((border-radius: mod(1px, 1px)) and (background: -webkit-named-image(apple-pay-logo-black)) and (not (contain-intrinsic-size: none))) or (not (border-radius: mod(1px, 1px))){.calendar-days-of-week .day-of-week{width:auto;height:auto;overflow:initial}.calendar-day{border-radius:32px}}:host .calendar-body .calendar-month .calendar-month-grid{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;-ms-flex-align:center;align-items:center;height:calc(100% - 16px)}:host .calendar-day-wrapper{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;height:0;min-height:1rem}:host .calendar-day{width:40px;min-width:40px;height:40px;font-size:min(1.25rem, 32px)}.calendar-day.calendar-day-active{background:rgba(var(--ion-color-base-rgb), 0.2);font-size:min(1.375rem, 35.2px)}:host .calendar-day.calendar-day-today{color:var(--ion-color-base)}:host .calendar-day.calendar-day-active{color:var(--ion-color-base);font-weight:600}:host .calendar-day.calendar-day-today.calendar-day-active{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host .datetime-time{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:8px;padding-bottom:16px;font-size:min(1rem, 25.6px)}:host .datetime-time .time-header{font-weight:600}:host .datetime-buttons{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;border-top:0.55px solid var(--ion-color-step-200, var(--ion-background-color-step-200, #cccccc))}:host .datetime-buttons ::slotted(ion-buttons),:host .datetime-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between}:host .datetime-action-buttons{width:100%}\";\nconst IonDatetimeIosStyle0 = datetimeIosCss;\nconst datetimeMdCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;background:var(--background);overflow:hidden}:host(.datetime-size-fixed){width:auto;height:auto}:host(.datetime-size-fixed:not(.datetime-prefer-wheel)){max-width:350px}:host(.datetime-size-fixed.datetime-prefer-wheel){min-width:350px;max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content}:host(.datetime-size-cover){width:100%}:host .calendar-body,:host .datetime-year{opacity:0}:host(:not(.datetime-ready)) .datetime-year{position:absolute;pointer-events:none}:host(.datetime-ready) .calendar-body{opacity:1}:host(.datetime-ready) .datetime-year{display:none;opacity:1}:host .wheel-order-year-first .day-column{-ms-flex-order:3;order:3;text-align:end}:host .wheel-order-year-first .month-column{-ms-flex-order:2;order:2;text-align:end}:host .wheel-order-year-first .year-column{-ms-flex-order:1;order:1;text-align:start}:host .datetime-calendar,:host .datetime-year{display:-ms-flexbox;display:flex;-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-flow:column;flex-flow:column}:host(.show-month-and-year) .datetime-year{display:-ms-flexbox;display:flex}:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{display:none}:host(.month-year-picker-open) .datetime-footer{display:none}:host(.datetime-disabled){pointer-events:none}:host(.datetime-disabled) .calendar-days-of-week,:host(.datetime-disabled) .datetime-time{opacity:0.4}:host(.datetime-readonly){pointer-events:none;}:host(.datetime-readonly) .calendar-action-buttons,:host(.datetime-readonly) .calendar-body,:host(.datetime-readonly) .datetime-year{pointer-events:initial}:host(.datetime-readonly) .calendar-day[disabled]:not(.calendar-day-constrained),:host(.datetime-readonly) .datetime-action-buttons ion-button[disabled]{opacity:1}:host .datetime-header .datetime-title{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host .datetime-action-buttons.has-clear-button{width:100%}:host .datetime-action-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.datetime-action-buttons .datetime-action-buttons-container{display:-ms-flexbox;display:flex}:host .calendar-action-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons ion-button{--background:transparent}:host .calendar-days-of-week{display:grid;grid-template-columns:repeat(7, 1fr);text-align:center}.calendar-days-of-week .day-of-week{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}:host .calendar-body{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;overflow-x:scroll;overflow-y:hidden;scrollbar-width:none;outline:none}:host .calendar-body .calendar-month{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;scroll-snap-align:start;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}:host .calendar-body .calendar-month-disabled{scroll-snap-align:none}:host .calendar-body::-webkit-scrollbar{display:none}:host .calendar-body .calendar-month-grid{display:grid;grid-template-columns:repeat(7, 1fr)}:host .calendar-day-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:0;min-height:0;overflow:visible}.calendar-day{border-radius:50%;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:0px;margin-bottom:0px;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border:none;outline:none;background:none;color:currentColor;font-family:var(--ion-font-family, inherit);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:0}:host .calendar-day[disabled]{pointer-events:none;opacity:0.4}.calendar-day:focus{background:rgba(var(--ion-color-base-rgb), 0.2);-webkit-box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2);box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2)}:host .datetime-time{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host(.datetime-presentation-time) .datetime-time{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host ion-popover{--height:200px}:host .time-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .time-body{border-radius:8px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px;display:-ms-flexbox;display:flex;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host .time-body-active{color:var(--ion-color-base)}:host(.in-item){position:static}:host(.show-month-and-year) .calendar-action-buttons .calendar-month-year-toggle{color:var(--ion-color-base)}.calendar-month-year{min-width:0}.calendar-month-year-toggle{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;position:relative;border:0;outline:none;background:transparent;cursor:pointer;z-index:1}.calendar-month-year-toggle::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;-webkit-transition:opacity 15ms linear, background-color 15ms linear;transition:opacity 15ms linear, background-color 15ms linear;z-index:-1}.calendar-month-year-toggle.ion-focused::after{background:currentColor}.calendar-month-year-toggle:disabled{opacity:0.3;pointer-events:none}.calendar-month-year-toggle ion-icon{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0;-ms-flex-negative:0;flex-shrink:0}.calendar-month-year-toggle #toggle-wrapper{display:-ms-inline-flexbox;display:inline-flex;-ms-flex-align:center;align-items:center}ion-picker{--highlight-background:var(--wheel-highlight-background);--highlight-border-radius:var(--wheel-highlight-border-radius);--fade-background-rgb:var(--wheel-fade-background-rgb)}:host{--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #ffffff));--title-color:var(--ion-color-contrast)}:host .datetime-header{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:20px;padding-bottom:20px;background:var(--ion-color-base);color:var(--title-color)}:host .datetime-header .datetime-title{font-size:0.75rem;text-transform:uppercase}:host .datetime-header .datetime-selected-date{margin-top:30px;font-size:2.125rem}:host .calendar-action-buttons ion-button{--color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959))}.calendar-month-year-toggle{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:12px;padding-bottom:12px;min-height:48px;background:transparent;color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959));z-index:1}.calendar-month-year-toggle.ion-focused::after{opacity:0.04}.calendar-month-year-toggle ion-ripple-effect{color:currentColor}@media (any-hover: hover){.calendar-month-year-toggle.ion-activatable:not(.ion-focused):hover::after{background:currentColor;opacity:0.04}}:host .calendar-days-of-week{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:0px;padding-bottom:0px;color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray));font-size:0.875rem;line-height:36px}:host .calendar-body .calendar-month .calendar-month-grid{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:4px;padding-bottom:4px;grid-template-rows:repeat(6, 1fr)}:host .calendar-day{width:42px;min-width:42px;height:42px;font-size:0.875rem}:host .calendar-day.calendar-day-today{border:1px solid var(--ion-color-base);color:var(--ion-color-base)}:host .calendar-day.calendar-day-active{color:var(--ion-color-contrast)}.calendar-day.calendar-day-active{border:1px solid var(--ion-color-base);background:var(--ion-color-base)}:host .datetime-time{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:8px;padding-bottom:8px}:host .time-header{color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959))}:host(.datetime-presentation-month) .datetime-year,:host(.datetime-presentation-year) .datetime-year,:host(.datetime-presentation-month-year) .datetime-year{margin-top:20px;margin-bottom:20px}:host .datetime-buttons{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:10px;padding-bottom:10px;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:end;justify-content:flex-end}\";\nconst IonDatetimeMdStyle0 = datetimeMdCss;\nconst Datetime = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionCancel = createEvent(this, \"ionCancel\", 7);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionValueChange = createEvent(this, \"ionValueChange\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.ionRender = createEvent(this, \"ionRender\", 7);\n    this.inputId = `ion-dt-${datetimeIds++}`;\n    this.prevPresentation = null;\n    this.warnIfIncorrectValueUsage = () => {\n      const {\n        multiple,\n        value\n      } = this;\n      if (!multiple && Array.isArray(value)) {\n        /**\n         * We do some processing on the `value` array so\n         * that it looks more like an array when logged to\n         * the console.\n         * Example given ['a', 'b']\n         * Default toString() behavior: a,b\n         * Custom behavior: ['a', 'b']\n         */\n        printIonWarning(`[ion-datetime] - An array of values was passed, but multiple is \"false\". This is incorrect usage and may result in unexpected behaviors. To dismiss this warning, pass a string to the \"value\" property when multiple=\"false\".\n\n  Value Passed: [${value.map(v => `'${v}'`).join(', ')}]\n`, this.el);\n      }\n    };\n    this.setValue = value => {\n      this.value = value;\n      this.ionChange.emit({\n        value\n      });\n    };\n    /**\n     * Returns the DatetimePart interface\n     * to use when rendering an initial set of\n     * data. This should be used when rendering an\n     * interface in an environment where the `value`\n     * may not be set. This function works\n     * by returning the first selected date and then\n     * falling back to defaultParts if no active date\n     * is selected.\n     */\n    this.getActivePartsWithFallback = () => {\n      var _a;\n      const {\n        defaultParts\n      } = this;\n      return (_a = this.getActivePart()) !== null && _a !== void 0 ? _a : defaultParts;\n    };\n    this.getActivePart = () => {\n      const {\n        activeParts\n      } = this;\n      return Array.isArray(activeParts) ? activeParts[0] : activeParts;\n    };\n    this.closeParentOverlay = role => {\n      const popoverOrModal = this.el.closest('ion-modal, ion-popover');\n      if (popoverOrModal) {\n        popoverOrModal.dismiss(undefined, role);\n      }\n    };\n    this.setWorkingParts = parts => {\n      this.workingParts = Object.assign({}, parts);\n    };\n    this.setActiveParts = (parts, removeDate = false) => {\n      /** if the datetime component is in readonly mode,\n       * allow browsing of the calendar without changing\n       * the set value\n       */\n      if (this.readonly) {\n        return;\n      }\n      const {\n        multiple,\n        minParts,\n        maxParts,\n        activeParts\n      } = this;\n      /**\n       * When setting the active parts, it is possible\n       * to set invalid data. For example,\n       * when updating January 31 to February,\n       * February 31 does not exist. As a result\n       * we need to validate the active parts and\n       * ensure that we are only setting valid dates.\n       * Additionally, we need to update the working parts\n       * too in the event that the validated parts are different.\n       */\n      const validatedParts = validateParts(parts, minParts, maxParts);\n      this.setWorkingParts(validatedParts);\n      if (multiple) {\n        const activePartsArray = Array.isArray(activeParts) ? activeParts : [activeParts];\n        if (removeDate) {\n          this.activeParts = activePartsArray.filter(p => !isSameDay(p, validatedParts));\n        } else {\n          this.activeParts = [...activePartsArray, validatedParts];\n        }\n      } else {\n        this.activeParts = Object.assign({}, validatedParts);\n      }\n      const hasSlottedButtons = this.el.querySelector('[slot=\"buttons\"]') !== null;\n      if (hasSlottedButtons || this.showDefaultButtons) {\n        return;\n      }\n      this.confirm();\n    };\n    this.initializeKeyboardListeners = () => {\n      const calendarBodyRef = this.calendarBodyRef;\n      if (!calendarBodyRef) {\n        return;\n      }\n      const root = this.el.shadowRoot;\n      /**\n       * Get a reference to the month\n       * element we are currently viewing.\n       */\n      const currentMonth = calendarBodyRef.querySelector('.calendar-month:nth-of-type(2)');\n      /**\n       * When focusing the calendar body, we want to pass focus\n       * to the working day, but other days should\n       * only be accessible using the arrow keys. Pressing\n       * Tab should jump between bodies of selectable content.\n       */\n      const checkCalendarBodyFocus = ev => {\n        var _a;\n        const record = ev[0];\n        /**\n         * If calendar body was already focused\n         * when this fired or if the calendar body\n         * if not currently focused, we should not re-focus\n         * the inner day.\n         */\n        if (((_a = record.oldValue) === null || _a === void 0 ? void 0 : _a.includes('ion-focused')) || !calendarBodyRef.classList.contains('ion-focused')) {\n          return;\n        }\n        this.focusWorkingDay(currentMonth);\n      };\n      const mo = new MutationObserver(checkCalendarBodyFocus);\n      mo.observe(calendarBodyRef, {\n        attributeFilter: ['class'],\n        attributeOldValue: true\n      });\n      this.destroyKeyboardMO = () => {\n        mo === null || mo === void 0 ? void 0 : mo.disconnect();\n      };\n      /**\n       * We must use keydown not keyup as we want\n       * to prevent scrolling when using the arrow keys.\n       */\n      calendarBodyRef.addEventListener('keydown', ev => {\n        const activeElement = root.activeElement;\n        if (!activeElement || !activeElement.classList.contains('calendar-day')) {\n          return;\n        }\n        const parts = getPartsFromCalendarDay(activeElement);\n        let partsToFocus;\n        switch (ev.key) {\n          case 'ArrowDown':\n            ev.preventDefault();\n            partsToFocus = getNextWeek(parts);\n            break;\n          case 'ArrowUp':\n            ev.preventDefault();\n            partsToFocus = getPreviousWeek(parts);\n            break;\n          case 'ArrowRight':\n            ev.preventDefault();\n            partsToFocus = getNextDay(parts);\n            break;\n          case 'ArrowLeft':\n            ev.preventDefault();\n            partsToFocus = getPreviousDay(parts);\n            break;\n          case 'Home':\n            ev.preventDefault();\n            partsToFocus = getStartOfWeek(parts);\n            break;\n          case 'End':\n            ev.preventDefault();\n            partsToFocus = getEndOfWeek(parts);\n            break;\n          case 'PageUp':\n            ev.preventDefault();\n            partsToFocus = ev.shiftKey ? getPreviousYear(parts) : getPreviousMonth(parts);\n            break;\n          case 'PageDown':\n            ev.preventDefault();\n            partsToFocus = ev.shiftKey ? getNextYear(parts) : getNextMonth(parts);\n            break;\n          /**\n           * Do not preventDefault here\n           * as we do not want to override other\n           * browser defaults such as pressing Enter/Space\n           * to select a day.\n           */\n          default:\n            return;\n        }\n        /**\n         * If the day we want to move focus to is\n         * disabled, do not do anything.\n         */\n        if (isDayDisabled(partsToFocus, this.minParts, this.maxParts)) {\n          return;\n        }\n        this.setWorkingParts(Object.assign(Object.assign({}, this.workingParts), partsToFocus));\n        /**\n         * Give view a chance to re-render\n         * then move focus to the new working day\n         */\n        requestAnimationFrame(() => this.focusWorkingDay(currentMonth));\n      });\n    };\n    this.focusWorkingDay = currentMonth => {\n      /**\n       * Get the number of padding days so\n       * we know how much to offset our next selector by\n       * to grab the correct calendar-day element.\n       */\n      const padding = currentMonth.querySelectorAll('.calendar-day-padding');\n      const {\n        day\n      } = this.workingParts;\n      if (day === null) {\n        return;\n      }\n      /**\n       * Get the calendar day element\n       * and focus it.\n       */\n      const dayEl = currentMonth.querySelector(`.calendar-day-wrapper:nth-of-type(${padding.length + day}) .calendar-day`);\n      if (dayEl) {\n        dayEl.focus();\n      }\n    };\n    this.processMinParts = () => {\n      const {\n        min,\n        defaultParts\n      } = this;\n      if (min === undefined) {\n        this.minParts = undefined;\n        return;\n      }\n      this.minParts = parseMinParts(min, defaultParts);\n    };\n    this.processMaxParts = () => {\n      const {\n        max,\n        defaultParts\n      } = this;\n      if (max === undefined) {\n        this.maxParts = undefined;\n        return;\n      }\n      this.maxParts = parseMaxParts(max, defaultParts);\n    };\n    this.initializeCalendarListener = () => {\n      const calendarBodyRef = this.calendarBodyRef;\n      if (!calendarBodyRef) {\n        return;\n      }\n      /**\n       * For performance reasons, we only render 3\n       * months at a time: The current month, the previous\n       * month, and the next month. We have a scroll listener\n       * on the calendar body to append/prepend new months.\n       *\n       * We can do this because Stencil is smart enough to not\n       * re-create the .calendar-month containers, but rather\n       * update the content within those containers.\n       *\n       * As an added bonus, WebKit has some troubles with\n       * scroll-snap-stop: always, so not rendering all of\n       * the months in a row allows us to mostly sidestep\n       * that issue.\n       */\n      const months = calendarBodyRef.querySelectorAll('.calendar-month');\n      const startMonth = months[0];\n      const workingMonth = months[1];\n      const endMonth = months[2];\n      const mode = getIonMode(this);\n      const needsiOSRubberBandFix = mode === 'ios' && typeof navigator !== 'undefined' && navigator.maxTouchPoints > 1;\n      /**\n       * Before setting up the scroll listener,\n       * scroll the middle month into view.\n       * scrollIntoView() will scroll entire page\n       * if element is not in viewport. Use scrollLeft instead.\n       */\n      writeTask(() => {\n        calendarBodyRef.scrollLeft = startMonth.clientWidth * (isRTL(this.el) ? -1 : 1);\n        const getChangedMonth = parts => {\n          const box = calendarBodyRef.getBoundingClientRect();\n          /**\n           * If the current scroll position is all the way to the left\n           * then we have scrolled to the previous month.\n           * Otherwise, assume that we have scrolled to the next\n           * month. We have a tolerance of 2px to account for\n           * sub pixel rendering.\n           *\n           * Check below the next line ensures that we did not\n           * swipe and abort (i.e. we swiped but we are still on the current month).\n           */\n          const condition = isRTL(this.el) ? calendarBodyRef.scrollLeft >= -2 : calendarBodyRef.scrollLeft <= 2;\n          const month = condition ? startMonth : endMonth;\n          /**\n           * The edge of the month must be lined up with\n           * the edge of the calendar body in order for\n           * the component to update. Otherwise, it\n           * may be the case that the user has paused their\n           * swipe or the browser has not finished snapping yet.\n           * Rather than check if the x values are equal,\n           * we give it a tolerance of 2px to account for\n           * sub pixel rendering.\n           */\n          const monthBox = month.getBoundingClientRect();\n          if (Math.abs(monthBox.x - box.x) > 2) return;\n          /**\n           * If we're force-rendering a month, assume we've\n           * scrolled to that and return it.\n           *\n           * If forceRenderDate is ever used in a context where the\n           * forced month is not immediately auto-scrolled to, this\n           * should be updated to also check whether `month` has the\n           * same month and year as the forced date.\n           */\n          const {\n            forceRenderDate\n          } = this;\n          if (forceRenderDate !== undefined) {\n            return {\n              month: forceRenderDate.month,\n              year: forceRenderDate.year,\n              day: forceRenderDate.day\n            };\n          }\n          /**\n           * From here, we can determine if the start\n           * month or the end month was scrolled into view.\n           * If no month was changed, then we can return from\n           * the scroll callback early.\n           */\n          if (month === startMonth) {\n            return getPreviousMonth(parts);\n          } else if (month === endMonth) {\n            return getNextMonth(parts);\n          } else {\n            return;\n          }\n        };\n        const updateActiveMonth = () => {\n          if (needsiOSRubberBandFix) {\n            calendarBodyRef.style.removeProperty('pointer-events');\n            appliediOSRubberBandFix = false;\n          }\n          /**\n           * If the month did not change\n           * then we can return early.\n           */\n          const newDate = getChangedMonth(this.workingParts);\n          if (!newDate) return;\n          const {\n            month,\n            day,\n            year\n          } = newDate;\n          if (isMonthDisabled({\n            month,\n            year,\n            day: null\n          }, {\n            minParts: Object.assign(Object.assign({}, this.minParts), {\n              day: null\n            }),\n            maxParts: Object.assign(Object.assign({}, this.maxParts), {\n              day: null\n            })\n          })) {\n            return;\n          }\n          /**\n           * Prevent scrolling for other browsers\n           * to give the DOM time to update and the container\n           * time to properly snap.\n           */\n          calendarBodyRef.style.setProperty('overflow', 'hidden');\n          /**\n           * Use a writeTask here to ensure\n           * that the state is updated and the\n           * correct month is scrolled into view\n           * in the same frame. This is not\n           * typically a problem on newer devices\n           * but older/slower device may have a flicker\n           * if we did not do this.\n           */\n          writeTask(() => {\n            this.setWorkingParts(Object.assign(Object.assign({}, this.workingParts), {\n              month,\n              day: day,\n              year\n            }));\n            calendarBodyRef.scrollLeft = workingMonth.clientWidth * (isRTL(this.el) ? -1 : 1);\n            calendarBodyRef.style.removeProperty('overflow');\n            if (this.resolveForceDateScrolling) {\n              this.resolveForceDateScrolling();\n            }\n          });\n        };\n        /**\n         * When the container finishes scrolling we\n         * need to update the DOM with the selected month.\n         */\n        let scrollTimeout;\n        /**\n         * We do not want to attempt to set pointer-events\n         * multiple times within a single swipe gesture as\n         * that adds unnecessary work to the main thread.\n         */\n        let appliediOSRubberBandFix = false;\n        const scrollCallback = () => {\n          if (scrollTimeout) {\n            clearTimeout(scrollTimeout);\n          }\n          /**\n           * On iOS it is possible to quickly rubber band\n           * the scroll area before the scroll timeout has fired.\n           * This results in users reaching the end of the scrollable\n           * container before the DOM has updated.\n           * By setting `pointer-events: none` we can ensure that\n           * subsequent swipes do not happen while the container\n           * is snapping.\n           */\n          if (!appliediOSRubberBandFix && needsiOSRubberBandFix) {\n            calendarBodyRef.style.setProperty('pointer-events', 'none');\n            appliediOSRubberBandFix = true;\n          }\n          // Wait ~3 frames\n          scrollTimeout = setTimeout(updateActiveMonth, 50);\n        };\n        calendarBodyRef.addEventListener('scroll', scrollCallback);\n        this.destroyCalendarListener = () => {\n          calendarBodyRef.removeEventListener('scroll', scrollCallback);\n        };\n      });\n    };\n    /**\n     * Clean up all listeners except for the overlay\n     * listener. This is so that we can re-create the listeners\n     * if the datetime has been hidden/presented by a modal or popover.\n     */\n    this.destroyInteractionListeners = () => {\n      const {\n        destroyCalendarListener,\n        destroyKeyboardMO\n      } = this;\n      if (destroyCalendarListener !== undefined) {\n        destroyCalendarListener();\n      }\n      if (destroyKeyboardMO !== undefined) {\n        destroyKeyboardMO();\n      }\n    };\n    this.processValue = value => {\n      const hasValue = value !== null && value !== undefined && value !== '' && (!Array.isArray(value) || value.length > 0);\n      const valueToProcess = hasValue ? parseDate(value) : this.defaultParts;\n      const {\n        minParts,\n        maxParts,\n        workingParts,\n        el\n      } = this;\n      this.warnIfIncorrectValueUsage();\n      /**\n       * Return early if the value wasn't parsed correctly, such as\n       * if an improperly formatted date string was provided.\n       */\n      if (!valueToProcess) {\n        return;\n      }\n      /**\n       * Datetime should only warn of out of bounds values\n       * if set by the user. If the `value` is undefined,\n       * we will default to today's date which may be out\n       * of bounds. In this case, the warning makes it look\n       * like the developer did something wrong which is\n       * not true.\n       */\n      if (hasValue) {\n        warnIfValueOutOfBounds(valueToProcess, minParts, maxParts);\n      }\n      /**\n       * If there are multiple values, pick an arbitrary one to clamp to. This way,\n       * if the values are across months, we always show at least one of them. Note\n       * that the values don't necessarily have to be in order.\n       */\n      const singleValue = Array.isArray(valueToProcess) ? valueToProcess[0] : valueToProcess;\n      const targetValue = clampDate(singleValue, minParts, maxParts);\n      const {\n        month,\n        day,\n        year,\n        hour,\n        minute\n      } = targetValue;\n      const ampm = parseAmPm(hour);\n      /**\n       * Since `activeParts` indicates a value that\n       * been explicitly selected either by the\n       * user or the app, only update `activeParts`\n       * if the `value` property is set.\n       */\n      if (hasValue) {\n        if (Array.isArray(valueToProcess)) {\n          this.activeParts = [...valueToProcess];\n        } else {\n          this.activeParts = {\n            month,\n            day,\n            year,\n            hour,\n            minute,\n            ampm\n          };\n        }\n      } else {\n        /**\n         * Reset the active parts if the value is not set.\n         * This will clear the selected calendar day when\n         * performing a clear action or using the reset() method.\n         */\n        this.activeParts = [];\n      }\n      /**\n       * Only animate if:\n       * 1. We're using grid style (wheel style pickers should just jump to new value)\n       * 2. The month and/or year actually changed, and both are defined (otherwise there's nothing to animate to)\n       * 3. The calendar body is visible (prevents animation when in collapsed datetime-button, for example)\n       * 4. The month/year picker is not open (since you wouldn't see the animation anyway)\n       */\n      const didChangeMonth = month !== undefined && month !== workingParts.month || year !== undefined && year !== workingParts.year;\n      const bodyIsVisible = el.classList.contains('datetime-ready');\n      const {\n        isGridStyle,\n        showMonthAndYear\n      } = this;\n      let areAllSelectedDatesInSameMonth = true;\n      if (Array.isArray(valueToProcess)) {\n        const firstMonth = valueToProcess[0].month;\n        for (const date of valueToProcess) {\n          if (date.month !== firstMonth) {\n            areAllSelectedDatesInSameMonth = false;\n            break;\n          }\n        }\n      }\n      /**\n       * If there is more than one date selected\n       * and the dates aren't all in the same month,\n       * then we should neither animate to the date\n       * nor update the working parts because we do\n       * not know which date the user wants to view.\n       */\n      if (areAllSelectedDatesInSameMonth) {\n        if (isGridStyle && didChangeMonth && bodyIsVisible && !showMonthAndYear) {\n          this.animateToDate(targetValue);\n        } else {\n          /**\n           * We only need to do this if we didn't just animate to a new month,\n           * since that calls prevMonth/nextMonth which calls setWorkingParts for us.\n           */\n          this.setWorkingParts({\n            month,\n            day,\n            year,\n            hour,\n            minute,\n            ampm\n          });\n        }\n      }\n    };\n    this.animateToDate = async targetValue => {\n      const {\n        workingParts\n      } = this;\n      /**\n       * Tell other render functions that we need to force the\n       * target month to appear in place of the actual next/prev month.\n       * Because this is a State variable, a rerender will be triggered\n       * automatically, updating the rendered months.\n       */\n      this.forceRenderDate = targetValue;\n      /**\n       * Flag that we've started scrolling to the forced date.\n       * The resolve function will be called by the datetime's\n       * scroll listener when it's done updating everything.\n       * This is a replacement for making prev/nextMonth async,\n       * since the logic we're waiting on is in a listener.\n       */\n      const forceDateScrollingPromise = new Promise(resolve => {\n        this.resolveForceDateScrolling = resolve;\n      });\n      /**\n       * Animate smoothly to the forced month. This will also update\n       * workingParts and correct the surrounding months for us.\n       */\n      const targetMonthIsBefore = isBefore(targetValue, workingParts);\n      targetMonthIsBefore ? this.prevMonth() : this.nextMonth();\n      await forceDateScrollingPromise;\n      this.resolveForceDateScrolling = undefined;\n      this.forceRenderDate = undefined;\n    };\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.hasValue = () => {\n      return this.value != null;\n    };\n    this.nextMonth = () => {\n      const calendarBodyRef = this.calendarBodyRef;\n      if (!calendarBodyRef) {\n        return;\n      }\n      const nextMonth = calendarBodyRef.querySelector('.calendar-month:last-of-type');\n      if (!nextMonth) {\n        return;\n      }\n      const left = nextMonth.offsetWidth * 2;\n      calendarBodyRef.scrollTo({\n        top: 0,\n        left: left * (isRTL(this.el) ? -1 : 1),\n        behavior: 'smooth'\n      });\n    };\n    this.prevMonth = () => {\n      const calendarBodyRef = this.calendarBodyRef;\n      if (!calendarBodyRef) {\n        return;\n      }\n      const prevMonth = calendarBodyRef.querySelector('.calendar-month:first-of-type');\n      if (!prevMonth) {\n        return;\n      }\n      calendarBodyRef.scrollTo({\n        top: 0,\n        left: 0,\n        behavior: 'smooth'\n      });\n    };\n    this.toggleMonthAndYearView = () => {\n      this.showMonthAndYear = !this.showMonthAndYear;\n    };\n    this.showMonthAndYear = false;\n    this.activeParts = [];\n    this.workingParts = {\n      month: 5,\n      day: 28,\n      year: 2021,\n      hour: 13,\n      minute: 52,\n      ampm: 'pm'\n    };\n    this.isTimePopoverOpen = false;\n    this.forceRenderDate = undefined;\n    this.color = 'primary';\n    this.name = this.inputId;\n    this.disabled = false;\n    this.formatOptions = undefined;\n    this.readonly = false;\n    this.isDateEnabled = undefined;\n    this.min = undefined;\n    this.max = undefined;\n    this.presentation = 'date-time';\n    this.cancelText = 'Cancel';\n    this.doneText = 'Done';\n    this.clearText = 'Clear';\n    this.yearValues = undefined;\n    this.monthValues = undefined;\n    this.dayValues = undefined;\n    this.hourValues = undefined;\n    this.minuteValues = undefined;\n    this.locale = 'default';\n    this.firstDayOfWeek = 0;\n    this.titleSelectedDatesFormatter = undefined;\n    this.multiple = false;\n    this.highlightedDates = undefined;\n    this.value = undefined;\n    this.showDefaultTitle = false;\n    this.showDefaultButtons = false;\n    this.showClearButton = false;\n    this.showDefaultTimeLabel = true;\n    this.hourCycle = undefined;\n    this.size = 'fixed';\n    this.preferWheel = false;\n  }\n  formatOptionsChanged() {\n    const {\n      el,\n      formatOptions,\n      presentation\n    } = this;\n    checkForPresentationFormatMismatch(el, presentation, formatOptions);\n    warnIfTimeZoneProvided(el, formatOptions);\n  }\n  disabledChanged() {\n    this.emitStyle();\n  }\n  minChanged() {\n    this.processMinParts();\n  }\n  maxChanged() {\n    this.processMaxParts();\n  }\n  presentationChanged() {\n    const {\n      el,\n      formatOptions,\n      presentation\n    } = this;\n    checkForPresentationFormatMismatch(el, presentation, formatOptions);\n  }\n  get isGridStyle() {\n    const {\n      presentation,\n      preferWheel\n    } = this;\n    const hasDatePresentation = presentation === 'date' || presentation === 'date-time' || presentation === 'time-date';\n    return hasDatePresentation && !preferWheel;\n  }\n  yearValuesChanged() {\n    this.parsedYearValues = convertToArrayOfNumbers(this.yearValues);\n  }\n  monthValuesChanged() {\n    this.parsedMonthValues = convertToArrayOfNumbers(this.monthValues);\n  }\n  dayValuesChanged() {\n    this.parsedDayValues = convertToArrayOfNumbers(this.dayValues);\n  }\n  hourValuesChanged() {\n    this.parsedHourValues = convertToArrayOfNumbers(this.hourValues);\n  }\n  minuteValuesChanged() {\n    this.parsedMinuteValues = convertToArrayOfNumbers(this.minuteValues);\n  }\n  /**\n   * Update the datetime value when the value changes\n   */\n  async valueChanged() {\n    const {\n      value\n    } = this;\n    if (this.hasValue()) {\n      this.processValue(value);\n    }\n    this.emitStyle();\n    this.ionValueChange.emit({\n      value\n    });\n  }\n  /**\n   * Confirms the selected datetime value, updates the\n   * `value` property, and optionally closes the popover\n   * or modal that the datetime was presented in.\n   */\n  async confirm(closeOverlay = false) {\n    const {\n      isCalendarPicker,\n      activeParts,\n      preferWheel,\n      workingParts\n    } = this;\n    /**\n     * We only update the value if the presentation is not a calendar picker.\n     */\n    if (activeParts !== undefined || !isCalendarPicker) {\n      const activePartsIsArray = Array.isArray(activeParts);\n      if (activePartsIsArray && activeParts.length === 0) {\n        if (preferWheel) {\n          /**\n           * If the datetime is using a wheel picker, but the\n           * active parts are empty, then the user has confirmed the\n           * initial value (working parts) presented to them.\n           */\n          this.setValue(convertDataToISO(workingParts));\n        } else {\n          this.setValue(undefined);\n        }\n      } else {\n        this.setValue(convertDataToISO(activeParts));\n      }\n    }\n    if (closeOverlay) {\n      this.closeParentOverlay(CONFIRM_ROLE);\n    }\n  }\n  /**\n   * Resets the internal state of the datetime but does not update the value.\n   * Passing a valid ISO-8601 string will reset the state of the component to the provided date.\n   * If no value is provided, the internal state will be reset to the clamped value of the min, max and today.\n   */\n  async reset(startDate) {\n    this.processValue(startDate);\n  }\n  /**\n   * Emits the ionCancel event and\n   * optionally closes the popover\n   * or modal that the datetime was\n   * presented in.\n   */\n  async cancel(closeOverlay = false) {\n    this.ionCancel.emit();\n    if (closeOverlay) {\n      this.closeParentOverlay(CANCEL_ROLE);\n    }\n  }\n  get isCalendarPicker() {\n    const {\n      presentation\n    } = this;\n    return presentation === 'date' || presentation === 'date-time' || presentation === 'time-date';\n  }\n  connectedCallback() {\n    this.clearFocusVisible = startFocusVisible(this.el).destroy;\n  }\n  disconnectedCallback() {\n    if (this.clearFocusVisible) {\n      this.clearFocusVisible();\n      this.clearFocusVisible = undefined;\n    }\n  }\n  initializeListeners() {\n    this.initializeCalendarListener();\n    this.initializeKeyboardListeners();\n  }\n  componentDidLoad() {\n    const {\n      el,\n      intersectionTrackerRef\n    } = this;\n    /**\n     * If a scrollable element is hidden using `display: none`,\n     * it will not have a scroll height meaning we cannot scroll elements\n     * into view. As a result, we will need to wait for the datetime to become\n     * visible if used inside of a modal or a popover otherwise the scrollable\n     * areas will not have the correct values snapped into place.\n     */\n    const visibleCallback = entries => {\n      const ev = entries[0];\n      if (!ev.isIntersecting) {\n        return;\n      }\n      this.initializeListeners();\n      /**\n       * TODO FW-2793: Datetime needs a frame to ensure that it\n       * can properly scroll contents into view. As a result\n       * we hide the scrollable content until after that frame\n       * so users do not see the content quickly shifting. The downside\n       * is that the content will pop into view a frame after. Maybe there\n       * is a better way to handle this?\n       */\n      writeTask(() => {\n        this.el.classList.add('datetime-ready');\n      });\n    };\n    const visibleIO = new IntersectionObserver(visibleCallback, {\n      threshold: 0.01,\n      root: el\n    });\n    /**\n     * Use raf to avoid a race condition between the component loading and\n     * its display animation starting (such as when shown in a modal). This\n     * could cause the datetime to start at a visibility of 0, erroneously\n     * triggering the `hiddenIO` observer below.\n     */\n    raf(() => visibleIO === null || visibleIO === void 0 ? void 0 : visibleIO.observe(intersectionTrackerRef));\n    /**\n     * We need to clean up listeners when the datetime is hidden\n     * in a popover/modal so that we can properly scroll containers\n     * back into view if they are re-presented. When the datetime is hidden\n     * the scroll areas have scroll widths/heights of 0px, so any snapping\n     * we did originally has been lost.\n     */\n    const hiddenCallback = entries => {\n      const ev = entries[0];\n      if (ev.isIntersecting) {\n        return;\n      }\n      this.destroyInteractionListeners();\n      /**\n       * When datetime is hidden, we need to make sure that\n       * the month/year picker is closed. Otherwise,\n       * it will be open when the datetime re-appears\n       * and the scroll area of the calendar grid will be 0.\n       * As a result, the wrong month will be shown.\n       */\n      this.showMonthAndYear = false;\n      writeTask(() => {\n        this.el.classList.remove('datetime-ready');\n      });\n    };\n    const hiddenIO = new IntersectionObserver(hiddenCallback, {\n      threshold: 0,\n      root: el\n    });\n    raf(() => hiddenIO === null || hiddenIO === void 0 ? void 0 : hiddenIO.observe(intersectionTrackerRef));\n    /**\n     * Datetime uses Ionic components that emit\n     * ionFocus and ionBlur. These events are\n     * composed meaning they will cross\n     * the shadow dom boundary. We need to\n     * stop propagation on these events otherwise\n     * developers will see 2 ionFocus or 2 ionBlur\n     * events at a time.\n     */\n    const root = getElementRoot(this.el);\n    root.addEventListener('ionFocus', ev => ev.stopPropagation());\n    root.addEventListener('ionBlur', ev => ev.stopPropagation());\n  }\n  /**\n   * When the presentation is changed, all calendar content is recreated,\n   * so we need to re-init behavior with the new elements.\n   */\n  componentDidRender() {\n    const {\n      presentation,\n      prevPresentation,\n      calendarBodyRef,\n      minParts,\n      preferWheel,\n      forceRenderDate\n    } = this;\n    /**\n     * TODO(FW-2165)\n     * Remove this when https://bugs.webkit.org/show_bug.cgi?id=235960 is fixed.\n     * When using `min`, we add `scroll-snap-align: none`\n     * to the disabled month so that users cannot scroll to it.\n     * This triggers a bug in WebKit where the scroll position is reset.\n     * Since the month change logic is handled by a scroll listener,\n     * this causes the month to change leading to `scroll-snap-align`\n     * changing again, thus changing the scroll position again and causing\n     * an infinite loop.\n     * This issue only applies to the calendar grid, so we can disable\n     * it if the calendar grid is not being used.\n     */\n    const hasCalendarGrid = !preferWheel && ['date-time', 'time-date', 'date'].includes(presentation);\n    if (minParts !== undefined && hasCalendarGrid && calendarBodyRef) {\n      const workingMonth = calendarBodyRef.querySelector('.calendar-month:nth-of-type(1)');\n      /**\n       * We need to make sure the datetime is not in the process\n       * of scrolling to a new datetime value if the value\n       * is updated programmatically.\n       * Otherwise, the datetime will appear to not scroll at all because\n       * we are resetting the scroll position to the center of the view.\n       * Prior to the datetime's value being updated programmatically,\n       * the calendarBodyRef is scrolled such that the middle month is centered\n       * in the view. The below code updates the scroll position so the middle\n       * month is also centered in the view. Since the scroll position did not change,\n       * the scroll callback in this file does not fire,\n       * and the resolveForceDateScrolling promise never resolves.\n       */\n      if (workingMonth && forceRenderDate === undefined) {\n        calendarBodyRef.scrollLeft = workingMonth.clientWidth * (isRTL(this.el) ? -1 : 1);\n      }\n    }\n    if (prevPresentation === null) {\n      this.prevPresentation = presentation;\n      return;\n    }\n    if (presentation === prevPresentation) {\n      return;\n    }\n    this.prevPresentation = presentation;\n    this.destroyInteractionListeners();\n    this.initializeListeners();\n    /**\n     * The month/year picker from the date interface\n     * should be closed as it is not available in non-date\n     * interfaces.\n     */\n    this.showMonthAndYear = false;\n    raf(() => {\n      this.ionRender.emit();\n    });\n  }\n  componentWillLoad() {\n    const {\n      el,\n      formatOptions,\n      highlightedDates,\n      multiple,\n      presentation,\n      preferWheel\n    } = this;\n    if (multiple) {\n      if (presentation !== 'date') {\n        printIonWarning('[ion-datetime] - Multiple date selection is only supported for presentation=\"date\".', el);\n      }\n      if (preferWheel) {\n        printIonWarning('[ion-datetime] - Multiple date selection is not supported with preferWheel=\"true\".', el);\n      }\n    }\n    if (highlightedDates !== undefined) {\n      if (presentation !== 'date' && presentation !== 'date-time' && presentation !== 'time-date') {\n        printIonWarning('[ion-datetime] - The highlightedDates property is only supported with the date, date-time, and time-date presentations.', el);\n      }\n      if (preferWheel) {\n        printIonWarning('[ion-datetime] - The highlightedDates property is not supported with preferWheel=\"true\".', el);\n      }\n    }\n    if (formatOptions) {\n      checkForPresentationFormatMismatch(el, presentation, formatOptions);\n      warnIfTimeZoneProvided(el, formatOptions);\n    }\n    const hourValues = this.parsedHourValues = convertToArrayOfNumbers(this.hourValues);\n    const minuteValues = this.parsedMinuteValues = convertToArrayOfNumbers(this.minuteValues);\n    const monthValues = this.parsedMonthValues = convertToArrayOfNumbers(this.monthValues);\n    const yearValues = this.parsedYearValues = convertToArrayOfNumbers(this.yearValues);\n    const dayValues = this.parsedDayValues = convertToArrayOfNumbers(this.dayValues);\n    const todayParts = this.todayParts = parseDate(getToday());\n    this.processMinParts();\n    this.processMaxParts();\n    this.defaultParts = getClosestValidDate({\n      refParts: todayParts,\n      monthValues,\n      dayValues,\n      yearValues,\n      hourValues,\n      minuteValues,\n      minParts: this.minParts,\n      maxParts: this.maxParts\n    });\n    this.processValue(this.value);\n    this.emitStyle();\n  }\n  emitStyle() {\n    this.ionStyle.emit({\n      interactive: true,\n      datetime: true,\n      'interactive-disabled': this.disabled\n    });\n  }\n  /**\n   * Universal render methods\n   * These are pieces of datetime that\n   * are rendered independently of presentation.\n   */\n  renderFooter() {\n    const {\n      disabled,\n      readonly,\n      showDefaultButtons,\n      showClearButton\n    } = this;\n    /**\n     * The cancel, clear, and confirm buttons\n     * should not be interactive if the datetime\n     * is disabled or readonly.\n     */\n    const isButtonDisabled = disabled || readonly;\n    const hasSlottedButtons = this.el.querySelector('[slot=\"buttons\"]') !== null;\n    if (!hasSlottedButtons && !showDefaultButtons && !showClearButton) {\n      return;\n    }\n    const clearButtonClick = () => {\n      this.reset();\n      this.setValue(undefined);\n    };\n    /**\n     * By default we render two buttons:\n     * Cancel - Dismisses the datetime and\n     * does not update the `value` prop.\n     * OK - Dismisses the datetime and\n     * updates the `value` prop.\n     */\n    return h(\"div\", {\n      class: \"datetime-footer\"\n    }, h(\"div\", {\n      class: \"datetime-buttons\"\n    }, h(\"div\", {\n      class: {\n        ['datetime-action-buttons']: true,\n        ['has-clear-button']: this.showClearButton\n      }\n    }, h(\"slot\", {\n      name: \"buttons\"\n    }, h(\"ion-buttons\", null, showDefaultButtons && h(\"ion-button\", {\n      id: \"cancel-button\",\n      color: this.color,\n      onClick: () => this.cancel(true),\n      disabled: isButtonDisabled\n    }, this.cancelText), h(\"div\", {\n      class: \"datetime-action-buttons-container\"\n    }, showClearButton && h(\"ion-button\", {\n      id: \"clear-button\",\n      color: this.color,\n      onClick: () => clearButtonClick(),\n      disabled: isButtonDisabled\n    }, this.clearText), showDefaultButtons && h(\"ion-button\", {\n      id: \"confirm-button\",\n      color: this.color,\n      onClick: () => this.confirm(true),\n      disabled: isButtonDisabled\n    }, this.doneText)))))));\n  }\n  /**\n   * Wheel picker render methods\n   */\n  renderWheelPicker(forcePresentation = this.presentation) {\n    /**\n     * If presentation=\"time-date\" we switch the\n     * order of the render array here instead of\n     * manually reordering each date/time picker\n     * column with CSS. This allows for additional\n     * flexibility if we need to render subsets\n     * of the date/time data or do additional ordering\n     * within the child render functions.\n     */\n    const renderArray = forcePresentation === 'time-date' ? [this.renderTimePickerColumns(forcePresentation), this.renderDatePickerColumns(forcePresentation)] : [this.renderDatePickerColumns(forcePresentation), this.renderTimePickerColumns(forcePresentation)];\n    return h(\"ion-picker\", null, renderArray);\n  }\n  renderDatePickerColumns(forcePresentation) {\n    return forcePresentation === 'date-time' || forcePresentation === 'time-date' ? this.renderCombinedDatePickerColumn() : this.renderIndividualDatePickerColumns(forcePresentation);\n  }\n  renderCombinedDatePickerColumn() {\n    const {\n      defaultParts,\n      disabled,\n      workingParts,\n      locale,\n      minParts,\n      maxParts,\n      todayParts,\n      isDateEnabled\n    } = this;\n    const activePart = this.getActivePartsWithFallback();\n    /**\n     * By default, generate a range of 3 months:\n     * Previous month, current month, and next month\n     */\n    const monthsToRender = generateMonths(workingParts);\n    const lastMonth = monthsToRender[monthsToRender.length - 1];\n    /**\n     * Ensure that users can select the entire window of dates.\n     */\n    monthsToRender[0].day = 1;\n    lastMonth.day = getNumDaysInMonth(lastMonth.month, lastMonth.year);\n    /**\n     * Narrow the dates rendered based on min/max dates (if any).\n     * The `min` date is used if the min is after the generated min month.\n     * The `max` date is used if the max is before the generated max month.\n     * This ensures that the sliding window always stays at 3 months\n     * but still allows future dates to be lazily rendered based on any min/max\n     * constraints.\n     */\n    const min = minParts !== undefined && isAfter(minParts, monthsToRender[0]) ? minParts : monthsToRender[0];\n    const max = maxParts !== undefined && isBefore(maxParts, lastMonth) ? maxParts : lastMonth;\n    const result = getCombinedDateColumnData(locale, todayParts, min, max, this.parsedDayValues, this.parsedMonthValues);\n    let items = result.items;\n    const parts = result.parts;\n    if (isDateEnabled) {\n      items = items.map((itemObject, index) => {\n        const referenceParts = parts[index];\n        let disabled;\n        try {\n          /**\n           * The `isDateEnabled` implementation is try-catch wrapped\n           * to prevent exceptions in the user's function from\n           * interrupting the calendar rendering.\n           */\n          disabled = !isDateEnabled(convertDataToISO(referenceParts));\n        } catch (e) {\n          printIonError('[ion-datetime] - Exception thrown from provided `isDateEnabled` function. Please check your function and try again.', e);\n        }\n        return Object.assign(Object.assign({}, itemObject), {\n          disabled\n        });\n      });\n    }\n    /**\n     * If we have selected a day already, then default the column\n     * to that value. Otherwise, set it to the default date.\n     */\n    const todayString = workingParts.day !== null ? `${workingParts.year}-${workingParts.month}-${workingParts.day}` : `${defaultParts.year}-${defaultParts.month}-${defaultParts.day}`;\n    return h(\"ion-picker-column\", {\n      \"aria-label\": \"Select a date\",\n      class: \"date-column\",\n      color: this.color,\n      disabled: disabled,\n      value: todayString,\n      onIonChange: ev => {\n        const {\n          value\n        } = ev.detail;\n        const findPart = parts.find(({\n          month,\n          day,\n          year\n        }) => value === `${year}-${month}-${day}`);\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), findPart));\n        this.setActiveParts(Object.assign(Object.assign({}, activePart), findPart));\n        ev.stopPropagation();\n      }\n    }, items.map(item => h(\"ion-picker-column-option\", {\n      part: item.value === todayString ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART,\n      key: item.value,\n      disabled: item.disabled,\n      value: item.value\n    }, item.text)));\n  }\n  renderIndividualDatePickerColumns(forcePresentation) {\n    const {\n      workingParts,\n      isDateEnabled\n    } = this;\n    const shouldRenderMonths = forcePresentation !== 'year' && forcePresentation !== 'time';\n    const months = shouldRenderMonths ? getMonthColumnData(this.locale, workingParts, this.minParts, this.maxParts, this.parsedMonthValues) : [];\n    const shouldRenderDays = forcePresentation === 'date';\n    let days = shouldRenderDays ? getDayColumnData(this.locale, workingParts, this.minParts, this.maxParts, this.parsedDayValues) : [];\n    if (isDateEnabled) {\n      days = days.map(dayObject => {\n        const {\n          value\n        } = dayObject;\n        const valueNum = typeof value === 'string' ? parseInt(value) : value;\n        const referenceParts = {\n          month: workingParts.month,\n          day: valueNum,\n          year: workingParts.year\n        };\n        let disabled;\n        try {\n          /**\n           * The `isDateEnabled` implementation is try-catch wrapped\n           * to prevent exceptions in the user's function from\n           * interrupting the calendar rendering.\n           */\n          disabled = !isDateEnabled(convertDataToISO(referenceParts));\n        } catch (e) {\n          printIonError('[ion-datetime] - Exception thrown from provided `isDateEnabled` function. Please check your function and try again.', e);\n        }\n        return Object.assign(Object.assign({}, dayObject), {\n          disabled\n        });\n      });\n    }\n    const shouldRenderYears = forcePresentation !== 'month' && forcePresentation !== 'time';\n    const years = shouldRenderYears ? getYearColumnData(this.locale, this.defaultParts, this.minParts, this.maxParts, this.parsedYearValues) : [];\n    /**\n     * Certain locales show the day before the month.\n     */\n    const showMonthFirst = isMonthFirstLocale(this.locale, {\n      month: 'numeric',\n      day: 'numeric'\n    });\n    let renderArray = [];\n    if (showMonthFirst) {\n      renderArray = [this.renderMonthPickerColumn(months), this.renderDayPickerColumn(days), this.renderYearPickerColumn(years)];\n    } else {\n      renderArray = [this.renderDayPickerColumn(days), this.renderMonthPickerColumn(months), this.renderYearPickerColumn(years)];\n    }\n    return renderArray;\n  }\n  renderDayPickerColumn(days) {\n    var _a;\n    if (days.length === 0) {\n      return [];\n    }\n    const {\n      disabled,\n      workingParts\n    } = this;\n    const activePart = this.getActivePartsWithFallback();\n    const pickerColumnValue = (_a = workingParts.day !== null ? workingParts.day : this.defaultParts.day) !== null && _a !== void 0 ? _a : undefined;\n    return h(\"ion-picker-column\", {\n      \"aria-label\": \"Select a day\",\n      class: \"day-column\",\n      color: this.color,\n      disabled: disabled,\n      value: pickerColumnValue,\n      onIonChange: ev => {\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), {\n          day: ev.detail.value\n        }));\n        this.setActiveParts(Object.assign(Object.assign({}, activePart), {\n          day: ev.detail.value\n        }));\n        ev.stopPropagation();\n      }\n    }, days.map(day => h(\"ion-picker-column-option\", {\n      part: day.value === pickerColumnValue ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART,\n      key: day.value,\n      disabled: day.disabled,\n      value: day.value\n    }, day.text)));\n  }\n  renderMonthPickerColumn(months) {\n    if (months.length === 0) {\n      return [];\n    }\n    const {\n      disabled,\n      workingParts\n    } = this;\n    const activePart = this.getActivePartsWithFallback();\n    return h(\"ion-picker-column\", {\n      \"aria-label\": \"Select a month\",\n      class: \"month-column\",\n      color: this.color,\n      disabled: disabled,\n      value: workingParts.month,\n      onIonChange: ev => {\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), {\n          month: ev.detail.value\n        }));\n        this.setActiveParts(Object.assign(Object.assign({}, activePart), {\n          month: ev.detail.value\n        }));\n        ev.stopPropagation();\n      }\n    }, months.map(month => h(\"ion-picker-column-option\", {\n      part: month.value === workingParts.month ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART,\n      key: month.value,\n      disabled: month.disabled,\n      value: month.value\n    }, month.text)));\n  }\n  renderYearPickerColumn(years) {\n    if (years.length === 0) {\n      return [];\n    }\n    const {\n      disabled,\n      workingParts\n    } = this;\n    const activePart = this.getActivePartsWithFallback();\n    return h(\"ion-picker-column\", {\n      \"aria-label\": \"Select a year\",\n      class: \"year-column\",\n      color: this.color,\n      disabled: disabled,\n      value: workingParts.year,\n      onIonChange: ev => {\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), {\n          year: ev.detail.value\n        }));\n        this.setActiveParts(Object.assign(Object.assign({}, activePart), {\n          year: ev.detail.value\n        }));\n        ev.stopPropagation();\n      }\n    }, years.map(year => h(\"ion-picker-column-option\", {\n      part: year.value === workingParts.year ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART,\n      key: year.value,\n      disabled: year.disabled,\n      value: year.value\n    }, year.text)));\n  }\n  renderTimePickerColumns(forcePresentation) {\n    if (['date', 'month', 'month-year', 'year'].includes(forcePresentation)) {\n      return [];\n    }\n    /**\n     * If a user has not selected a date,\n     * then we should show all times. If the\n     * user has selected a date (even if it has\n     * not been confirmed yet), we should apply\n     * the max and min restrictions so that the\n     * time picker shows values that are\n     * appropriate for the selected date.\n     */\n    const activePart = this.getActivePart();\n    const userHasSelectedDate = activePart !== undefined;\n    const {\n      hoursData,\n      minutesData,\n      dayPeriodData\n    } = getTimeColumnsData(this.locale, this.workingParts, this.hourCycle, userHasSelectedDate ? this.minParts : undefined, userHasSelectedDate ? this.maxParts : undefined, this.parsedHourValues, this.parsedMinuteValues);\n    return [this.renderHourPickerColumn(hoursData), this.renderMinutePickerColumn(minutesData), this.renderDayPeriodPickerColumn(dayPeriodData)];\n  }\n  renderHourPickerColumn(hoursData) {\n    const {\n      disabled,\n      workingParts\n    } = this;\n    if (hoursData.length === 0) return [];\n    const activePart = this.getActivePartsWithFallback();\n    return h(\"ion-picker-column\", {\n      \"aria-label\": \"Select an hour\",\n      color: this.color,\n      disabled: disabled,\n      value: activePart.hour,\n      numericInput: true,\n      onIonChange: ev => {\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), {\n          hour: ev.detail.value\n        }));\n        this.setActiveParts(Object.assign(Object.assign({}, this.getActivePartsWithFallback()), {\n          hour: ev.detail.value\n        }));\n        ev.stopPropagation();\n      }\n    }, hoursData.map(hour => h(\"ion-picker-column-option\", {\n      part: hour.value === activePart.hour ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART,\n      key: hour.value,\n      disabled: hour.disabled,\n      value: hour.value\n    }, hour.text)));\n  }\n  renderMinutePickerColumn(minutesData) {\n    const {\n      disabled,\n      workingParts\n    } = this;\n    if (minutesData.length === 0) return [];\n    const activePart = this.getActivePartsWithFallback();\n    return h(\"ion-picker-column\", {\n      \"aria-label\": \"Select a minute\",\n      color: this.color,\n      disabled: disabled,\n      value: activePart.minute,\n      numericInput: true,\n      onIonChange: ev => {\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), {\n          minute: ev.detail.value\n        }));\n        this.setActiveParts(Object.assign(Object.assign({}, this.getActivePartsWithFallback()), {\n          minute: ev.detail.value\n        }));\n        ev.stopPropagation();\n      }\n    }, minutesData.map(minute => h(\"ion-picker-column-option\", {\n      part: minute.value === activePart.minute ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART,\n      key: minute.value,\n      disabled: minute.disabled,\n      value: minute.value\n    }, minute.text)));\n  }\n  renderDayPeriodPickerColumn(dayPeriodData) {\n    const {\n      disabled,\n      workingParts\n    } = this;\n    if (dayPeriodData.length === 0) {\n      return [];\n    }\n    const activePart = this.getActivePartsWithFallback();\n    const isDayPeriodRTL = isLocaleDayPeriodRTL(this.locale);\n    return h(\"ion-picker-column\", {\n      \"aria-label\": \"Select a day period\",\n      style: isDayPeriodRTL ? {\n        order: '-1'\n      } : {},\n      color: this.color,\n      disabled: disabled,\n      value: activePart.ampm,\n      onIonChange: ev => {\n        const hour = calculateHourFromAMPM(workingParts, ev.detail.value);\n        this.setWorkingParts(Object.assign(Object.assign({}, workingParts), {\n          ampm: ev.detail.value,\n          hour\n        }));\n        this.setActiveParts(Object.assign(Object.assign({}, this.getActivePartsWithFallback()), {\n          ampm: ev.detail.value,\n          hour\n        }));\n        ev.stopPropagation();\n      }\n    }, dayPeriodData.map(dayPeriod => h(\"ion-picker-column-option\", {\n      part: dayPeriod.value === activePart.ampm ? `${WHEEL_ITEM_PART} ${WHEEL_ITEM_ACTIVE_PART}` : WHEEL_ITEM_PART,\n      key: dayPeriod.value,\n      disabled: dayPeriod.disabled,\n      value: dayPeriod.value\n    }, dayPeriod.text)));\n  }\n  renderWheelView(forcePresentation) {\n    const {\n      locale\n    } = this;\n    const showMonthFirst = isMonthFirstLocale(locale);\n    const columnOrder = showMonthFirst ? 'month-first' : 'year-first';\n    return h(\"div\", {\n      class: {\n        [`wheel-order-${columnOrder}`]: true\n      }\n    }, this.renderWheelPicker(forcePresentation));\n  }\n  /**\n   * Grid Render Methods\n   */\n  renderCalendarHeader(mode) {\n    const {\n      disabled\n    } = this;\n    const expandedIcon = mode === 'ios' ? chevronDown : caretUpSharp;\n    const collapsedIcon = mode === 'ios' ? chevronForward : caretDownSharp;\n    const prevMonthDisabled = disabled || isPrevMonthDisabled(this.workingParts, this.minParts, this.maxParts);\n    const nextMonthDisabled = disabled || isNextMonthDisabled(this.workingParts, this.maxParts);\n    // don't use the inheritAttributes util because it removes dir from the host, and we still need that\n    const hostDir = this.el.getAttribute('dir') || undefined;\n    return h(\"div\", {\n      class: \"calendar-header\"\n    }, h(\"div\", {\n      class: \"calendar-action-buttons\"\n    }, h(\"div\", {\n      class: \"calendar-month-year\"\n    }, h(\"button\", {\n      class: {\n        'calendar-month-year-toggle': true,\n        'ion-activatable': true,\n        'ion-focusable': true\n      },\n      part: \"month-year-button\",\n      disabled: disabled,\n      \"aria-label\": this.showMonthAndYear ? 'Hide year picker' : 'Show year picker',\n      onClick: () => this.toggleMonthAndYearView()\n    }, h(\"span\", {\n      id: \"toggle-wrapper\"\n    }, getMonthAndYear(this.locale, this.workingParts), h(\"ion-icon\", {\n      \"aria-hidden\": \"true\",\n      icon: this.showMonthAndYear ? expandedIcon : collapsedIcon,\n      lazy: false,\n      flipRtl: true\n    })), mode === 'md' && h(\"ion-ripple-effect\", null))), h(\"div\", {\n      class: \"calendar-next-prev\"\n    }, h(\"ion-buttons\", null, h(\"ion-button\", {\n      \"aria-label\": \"Previous month\",\n      disabled: prevMonthDisabled,\n      onClick: () => this.prevMonth()\n    }, h(\"ion-icon\", {\n      dir: hostDir,\n      \"aria-hidden\": \"true\",\n      slot: \"icon-only\",\n      icon: chevronBack,\n      lazy: false,\n      flipRtl: true\n    })), h(\"ion-button\", {\n      \"aria-label\": \"Next month\",\n      disabled: nextMonthDisabled,\n      onClick: () => this.nextMonth()\n    }, h(\"ion-icon\", {\n      dir: hostDir,\n      \"aria-hidden\": \"true\",\n      slot: \"icon-only\",\n      icon: chevronForward,\n      lazy: false,\n      flipRtl: true\n    }))))), h(\"div\", {\n      class: \"calendar-days-of-week\",\n      \"aria-hidden\": \"true\"\n    }, getDaysOfWeek(this.locale, mode, this.firstDayOfWeek % 7).map(d => {\n      return h(\"div\", {\n        class: \"day-of-week\"\n      }, d);\n    })));\n  }\n  renderMonth(month, year) {\n    const {\n      disabled,\n      readonly\n    } = this;\n    const yearAllowed = this.parsedYearValues === undefined || this.parsedYearValues.includes(year);\n    const monthAllowed = this.parsedMonthValues === undefined || this.parsedMonthValues.includes(month);\n    const isCalMonthDisabled = !yearAllowed || !monthAllowed;\n    const isDatetimeDisabled = disabled || readonly;\n    const swipeDisabled = disabled || isMonthDisabled({\n      month,\n      year,\n      day: null\n    }, {\n      // The day is not used when checking if a month is disabled.\n      // Users should be able to access the min or max month, even if the\n      // min/max date is out of bounds (e.g. min is set to Feb 15, Feb should not be disabled).\n      minParts: Object.assign(Object.assign({}, this.minParts), {\n        day: null\n      }),\n      maxParts: Object.assign(Object.assign({}, this.maxParts), {\n        day: null\n      })\n    });\n    // The working month should never have swipe disabled.\n    // Otherwise the CSS scroll snap will not work and the user\n    // can free-scroll the calendar.\n    const isWorkingMonth = this.workingParts.month === month && this.workingParts.year === year;\n    const activePart = this.getActivePartsWithFallback();\n    return h(\"div\", {\n      \"aria-hidden\": !isWorkingMonth ? 'true' : null,\n      class: {\n        'calendar-month': true,\n        // Prevents scroll snap swipe gestures for months outside of the min/max bounds\n        'calendar-month-disabled': !isWorkingMonth && swipeDisabled\n      }\n    }, h(\"div\", {\n      class: \"calendar-month-grid\"\n    }, getDaysOfMonth(month, year, this.firstDayOfWeek % 7).map((dateObject, index) => {\n      const {\n        day,\n        dayOfWeek\n      } = dateObject;\n      const {\n        el,\n        highlightedDates,\n        isDateEnabled,\n        multiple\n      } = this;\n      const referenceParts = {\n        month,\n        day,\n        year\n      };\n      const isCalendarPadding = day === null;\n      const {\n        isActive,\n        isToday,\n        ariaLabel,\n        ariaSelected,\n        disabled: isDayDisabled,\n        text\n      } = getCalendarDayState(this.locale, referenceParts, this.activeParts, this.todayParts, this.minParts, this.maxParts, this.parsedDayValues);\n      const dateIsoString = convertDataToISO(referenceParts);\n      let isCalDayDisabled = isCalMonthDisabled || isDayDisabled;\n      if (!isCalDayDisabled && isDateEnabled !== undefined) {\n        try {\n          /**\n           * The `isDateEnabled` implementation is try-catch wrapped\n           * to prevent exceptions in the user's function from\n           * interrupting the calendar rendering.\n           */\n          isCalDayDisabled = !isDateEnabled(dateIsoString);\n        } catch (e) {\n          printIonError('[ion-datetime] - Exception thrown from provided `isDateEnabled` function. Please check your function and try again.', el, e);\n        }\n      }\n      /**\n       * Some days are constrained through max & min or allowed dates\n       * and also disabled because the component is readonly or disabled.\n       * These need to be displayed differently.\n       */\n      const isCalDayConstrained = isCalDayDisabled && isDatetimeDisabled;\n      const isButtonDisabled = isCalDayDisabled || isDatetimeDisabled;\n      let dateStyle = undefined;\n      /**\n       * Custom highlight styles should not override the style for selected dates,\n       * nor apply to \"filler days\" at the start of the grid.\n       */\n      if (highlightedDates !== undefined && !isActive && day !== null) {\n        dateStyle = getHighlightStyles(highlightedDates, dateIsoString, el);\n      }\n      let dateParts = undefined;\n      // \"Filler days\" at the beginning of the grid should not get the calendar day\n      // CSS parts added to them\n      if (!isCalendarPadding) {\n        dateParts = `calendar-day${isActive ? ' active' : ''}${isToday ? ' today' : ''}${isCalDayDisabled ? ' disabled' : ''}`;\n      }\n      return h(\"div\", {\n        class: \"calendar-day-wrapper\"\n      }, h(\"button\", {\n        // We need to use !important for the inline styles here because\n        // otherwise the CSS shadow parts will override these styles.\n        // See https://github.com/WICG/webcomponents/issues/847\n        // Both the CSS shadow parts and highlightedDates styles are\n        // provided by the developer, but highlightedDates styles should\n        // always take priority.\n        ref: el => {\n          if (el) {\n            el.style.setProperty('color', `${dateStyle ? dateStyle.textColor : ''}`, 'important');\n            el.style.setProperty('background-color', `${dateStyle ? dateStyle.backgroundColor : ''}`, 'important');\n          }\n        },\n        tabindex: \"-1\",\n        \"data-day\": day,\n        \"data-month\": month,\n        \"data-year\": year,\n        \"data-index\": index,\n        \"data-day-of-week\": dayOfWeek,\n        disabled: isButtonDisabled,\n        class: {\n          'calendar-day-padding': isCalendarPadding,\n          'calendar-day': true,\n          'calendar-day-active': isActive,\n          'calendar-day-constrained': isCalDayConstrained,\n          'calendar-day-today': isToday\n        },\n        part: dateParts,\n        \"aria-hidden\": isCalendarPadding ? 'true' : null,\n        \"aria-selected\": ariaSelected,\n        \"aria-label\": ariaLabel,\n        onClick: () => {\n          if (isCalendarPadding) {\n            return;\n          }\n          this.setWorkingParts(Object.assign(Object.assign({}, this.workingParts), {\n            month,\n            day,\n            year\n          }));\n          // multiple only needs date info, so we can wipe out other fields like time\n          if (multiple) {\n            this.setActiveParts({\n              month,\n              day,\n              year\n            }, isActive);\n          } else {\n            this.setActiveParts(Object.assign(Object.assign({}, activePart), {\n              month,\n              day,\n              year\n            }));\n          }\n        }\n      }, text));\n    })));\n  }\n  renderCalendarBody() {\n    return h(\"div\", {\n      class: \"calendar-body ion-focusable\",\n      ref: el => this.calendarBodyRef = el,\n      tabindex: \"0\"\n    }, generateMonths(this.workingParts, this.forceRenderDate).map(({\n      month,\n      year\n    }) => {\n      return this.renderMonth(month, year);\n    }));\n  }\n  renderCalendar(mode) {\n    return h(\"div\", {\n      class: \"datetime-calendar\",\n      key: \"datetime-calendar\"\n    }, this.renderCalendarHeader(mode), this.renderCalendarBody());\n  }\n  renderTimeLabel() {\n    const hasSlottedTimeLabel = this.el.querySelector('[slot=\"time-label\"]') !== null;\n    if (!hasSlottedTimeLabel && !this.showDefaultTimeLabel) {\n      return;\n    }\n    return h(\"slot\", {\n      name: \"time-label\"\n    }, \"Time\");\n  }\n  renderTimeOverlay() {\n    const {\n      disabled,\n      hourCycle,\n      isTimePopoverOpen,\n      locale,\n      formatOptions\n    } = this;\n    const computedHourCycle = getHourCycle(locale, hourCycle);\n    const activePart = this.getActivePartsWithFallback();\n    return [h(\"div\", {\n      class: \"time-header\"\n    }, this.renderTimeLabel()), h(\"button\", {\n      class: {\n        'time-body': true,\n        'time-body-active': isTimePopoverOpen\n      },\n      part: `time-button${isTimePopoverOpen ? ' active' : ''}`,\n      \"aria-expanded\": \"false\",\n      \"aria-haspopup\": \"true\",\n      disabled: disabled,\n      onClick: async ev => {\n        const {\n          popoverRef\n        } = this;\n        if (popoverRef) {\n          this.isTimePopoverOpen = true;\n          popoverRef.present(new CustomEvent('ionShadowTarget', {\n            detail: {\n              ionShadowTarget: ev.target\n            }\n          }));\n          await popoverRef.onWillDismiss();\n          this.isTimePopoverOpen = false;\n        }\n      }\n    }, getLocalizedTime(locale, activePart, computedHourCycle, formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time)), h(\"ion-popover\", {\n      alignment: \"center\",\n      translucent: true,\n      overlayIndex: 1,\n      arrow: false,\n      onWillPresent: ev => {\n        /**\n         * Intersection Observers do not consistently fire between Blink and Webkit\n         * when toggling the visibility of the popover and trying to scroll the picker\n         * column to the correct time value.\n         *\n         * This will correctly scroll the element position to the correct time value,\n         * before the popover is fully presented.\n         */\n        const cols = ev.target.querySelectorAll('ion-picker-column');\n        // TODO (FW-615): Potentially remove this when intersection observers are fixed in picker column\n        cols.forEach(col => col.scrollActiveItemIntoView());\n      },\n      style: {\n        '--offset-y': '-10px',\n        '--min-width': 'fit-content'\n      },\n      // Allow native browser keyboard events to support up/down/home/<USER>\n      // navigation within the time picker.\n      keyboardEvents: true,\n      ref: el => this.popoverRef = el\n    }, this.renderWheelPicker('time'))];\n  }\n  getHeaderSelectedDateText() {\n    var _a;\n    const {\n      activeParts,\n      formatOptions,\n      multiple,\n      titleSelectedDatesFormatter\n    } = this;\n    const isArray = Array.isArray(activeParts);\n    let headerText;\n    if (multiple && isArray && activeParts.length !== 1) {\n      headerText = `${activeParts.length} days`; // default/fallback for multiple selection\n      if (titleSelectedDatesFormatter !== undefined) {\n        try {\n          headerText = titleSelectedDatesFormatter(convertDataToISO(activeParts));\n        } catch (e) {\n          printIonError('[ion-datetime] - Exception in provided `titleSelectedDatesFormatter`:', e);\n        }\n      }\n    } else {\n      // for exactly 1 day selected (multiple set or not), show a formatted version of that\n      headerText = getLocalizedDateTime(this.locale, this.getActivePartsWithFallback(), (_a = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) !== null && _a !== void 0 ? _a : {\n        weekday: 'short',\n        month: 'short',\n        day: 'numeric'\n      });\n    }\n    return headerText;\n  }\n  renderHeader(showExpandedHeader = true) {\n    const hasSlottedTitle = this.el.querySelector('[slot=\"title\"]') !== null;\n    if (!hasSlottedTitle && !this.showDefaultTitle) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"datetime-header\"\n    }, h(\"div\", {\n      class: \"datetime-title\"\n    }, h(\"slot\", {\n      name: \"title\"\n    }, \"Select Date\")), showExpandedHeader && h(\"div\", {\n      class: \"datetime-selected-date\"\n    }, this.getHeaderSelectedDateText()));\n  }\n  /**\n   * Render time picker inside of datetime.\n   * Do not pass color prop to segment on\n   * iOS mode. MD segment has been customized and\n   * should take on the color prop, but iOS\n   * should just be the default segment.\n   */\n  renderTime() {\n    const {\n      presentation\n    } = this;\n    const timeOnlyPresentation = presentation === 'time';\n    return h(\"div\", {\n      class: \"datetime-time\"\n    }, timeOnlyPresentation ? this.renderWheelPicker() : this.renderTimeOverlay());\n  }\n  /**\n   * Renders the month/year picker that is\n   * displayed on the calendar grid.\n   * The .datetime-year class has additional\n   * styles that let us show/hide the\n   * picker when the user clicks on the\n   * toggle in the calendar header.\n   */\n  renderCalendarViewMonthYearPicker() {\n    return h(\"div\", {\n      class: \"datetime-year\"\n    }, this.renderWheelView('month-year'));\n  }\n  /**\n   * Render entry point\n   * All presentation types are rendered from here.\n   */\n  renderDatetime(mode) {\n    const {\n      presentation,\n      preferWheel\n    } = this;\n    /**\n     * Certain presentation types have separate grid and wheel displays.\n     * If preferWheel is true then we should show a wheel picker instead.\n     */\n    const hasWheelVariant = presentation === 'date' || presentation === 'date-time' || presentation === 'time-date';\n    if (preferWheel && hasWheelVariant) {\n      return [this.renderHeader(false), this.renderWheelView(), this.renderFooter()];\n    }\n    switch (presentation) {\n      case 'date-time':\n        return [this.renderHeader(), this.renderCalendar(mode), this.renderCalendarViewMonthYearPicker(), this.renderTime(), this.renderFooter()];\n      case 'time-date':\n        return [this.renderHeader(), this.renderTime(), this.renderCalendar(mode), this.renderCalendarViewMonthYearPicker(), this.renderFooter()];\n      case 'time':\n        return [this.renderHeader(false), this.renderTime(), this.renderFooter()];\n      case 'month':\n      case 'month-year':\n      case 'year':\n        return [this.renderHeader(false), this.renderWheelView(), this.renderFooter()];\n      default:\n        return [this.renderHeader(), this.renderCalendar(mode), this.renderCalendarViewMonthYearPicker(), this.renderFooter()];\n    }\n  }\n  render() {\n    const {\n      name,\n      value,\n      disabled,\n      el,\n      color,\n      readonly,\n      showMonthAndYear,\n      preferWheel,\n      presentation,\n      size,\n      isGridStyle\n    } = this;\n    const mode = getIonMode(this);\n    const isMonthAndYearPresentation = presentation === 'year' || presentation === 'month' || presentation === 'month-year';\n    const shouldShowMonthAndYear = showMonthAndYear || isMonthAndYearPresentation;\n    const monthYearPickerOpen = showMonthAndYear && !isMonthAndYearPresentation;\n    const hasDatePresentation = presentation === 'date' || presentation === 'date-time' || presentation === 'time-date';\n    const hasWheelVariant = hasDatePresentation && preferWheel;\n    renderHiddenInput(true, el, name, formatValue(value), disabled);\n    return h(Host, {\n      key: 'c3dfea8f46fcbcef38eb9e8a69b1b46a4e4b82fd',\n      \"aria-disabled\": disabled ? 'true' : null,\n      onFocus: this.onFocus,\n      onBlur: this.onBlur,\n      class: Object.assign({}, createColorClasses(color, {\n        [mode]: true,\n        ['datetime-readonly']: readonly,\n        ['datetime-disabled']: disabled,\n        'show-month-and-year': shouldShowMonthAndYear,\n        'month-year-picker-open': monthYearPickerOpen,\n        [`datetime-presentation-${presentation}`]: true,\n        [`datetime-size-${size}`]: true,\n        [`datetime-prefer-wheel`]: hasWheelVariant,\n        [`datetime-grid`]: isGridStyle\n      }))\n    }, h(\"div\", {\n      key: '75c91243cf6a51f44b83d7cf7d8c0c96bfd3c83f',\n      class: \"intersection-tracker\",\n      ref: el => this.intersectionTrackerRef = el\n    }), this.renderDatetime(mode));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"formatOptions\": [\"formatOptionsChanged\"],\n      \"disabled\": [\"disabledChanged\"],\n      \"min\": [\"minChanged\"],\n      \"max\": [\"maxChanged\"],\n      \"presentation\": [\"presentationChanged\"],\n      \"yearValues\": [\"yearValuesChanged\"],\n      \"monthValues\": [\"monthValuesChanged\"],\n      \"dayValues\": [\"dayValuesChanged\"],\n      \"hourValues\": [\"hourValuesChanged\"],\n      \"minuteValues\": [\"minuteValuesChanged\"],\n      \"value\": [\"valueChanged\"]\n    };\n  }\n};\nlet datetimeIds = 0;\nconst CANCEL_ROLE = 'datetime-cancel';\nconst CONFIRM_ROLE = 'datetime-confirm';\nconst WHEEL_ITEM_PART = 'wheel-item';\nconst WHEEL_ITEM_ACTIVE_PART = `active`;\nDatetime.style = {\n  ios: IonDatetimeIosStyle0,\n  md: IonDatetimeMdStyle0\n};\n\n/**\n * iOS Picker Enter Animation\n */\nconst iosEnterAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 0.01, 'var(--backdrop-opacity)').beforeStyles({\n    'pointer-events': 'none'\n  }).afterClearStyles(['pointer-events']);\n  wrapperAnimation.addElement(baseEl.querySelector('.picker-wrapper')).fromTo('transform', 'translateY(100%)', 'translateY(0%)');\n  return baseAnimation.addElement(baseEl).easing('cubic-bezier(.36,.66,.04,1)').duration(400).addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * iOS Picker Leave Animation\n */\nconst iosLeaveAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0.01);\n  wrapperAnimation.addElement(baseEl.querySelector('.picker-wrapper')).fromTo('transform', 'translateY(0%)', 'translateY(100%)');\n  return baseAnimation.addElement(baseEl).easing('cubic-bezier(.36,.66,.04,1)').duration(400).addAnimation([backdropAnimation, wrapperAnimation]);\n};\nconst pickerIosCss = \".sc-ion-picker-legacy-ios-h{--border-radius:0;--border-style:solid;--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--max-height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.sc-ion-picker-legacy-ios-h{inset-inline-start:0}.overlay-hidden.sc-ion-picker-legacy-ios-h{display:none}.picker-wrapper.sc-ion-picker-legacy-ios{border-radius:var(--border-radius);left:0;right:0;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;overflow:hidden;z-index:10}.picker-toolbar.sc-ion-picker-legacy-ios{width:100%;background:transparent;contain:strict;z-index:1}.picker-button.sc-ion-picker-legacy-ios{border:0;font-family:inherit}.picker-button.sc-ion-picker-legacy-ios:active,.picker-button.sc-ion-picker-legacy-ios:focus{outline:none}.picker-columns.sc-ion-picker-legacy-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-pack:center;justify-content:center;margin-bottom:var(--ion-safe-area-bottom, 0);contain:strict;overflow:hidden}.picker-above-highlight.sc-ion-picker-legacy-ios,.picker-below-highlight.sc-ion-picker-legacy-ios{display:none;pointer-events:none}.sc-ion-picker-legacy-ios-h{--background:var(--ion-background-color, #fff);--border-width:1px 0 0;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--height:260px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.26);color:var(--ion-item-color, var(--ion-text-color, #000))}.picker-toolbar.sc-ion-picker-legacy-ios{display:-ms-flexbox;display:flex;height:44px;border-bottom:0.55px solid var(--border-color)}.picker-toolbar-button.sc-ion-picker-legacy-ios{-ms-flex:1;flex:1;text-align:end}.picker-toolbar-button.sc-ion-picker-legacy-ios:last-child .picker-button.sc-ion-picker-legacy-ios{font-weight:600}.picker-toolbar-button.sc-ion-picker-legacy-ios:first-child{font-weight:normal;text-align:start}.picker-button.sc-ion-picker-legacy-ios,.picker-button.ion-activated.sc-ion-picker-legacy-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:1em;padding-inline-start:1em;-webkit-padding-end:1em;padding-inline-end:1em;padding-top:0;padding-bottom:0;height:44px;background:transparent;color:var(--ion-color-primary, #0054e9);font-size:16px}.picker-columns.sc-ion-picker-legacy-ios{height:215px;-webkit-perspective:1000px;perspective:1000px}.picker-above-highlight.sc-ion-picker-legacy-ios{top:0;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);display:block;position:absolute;width:100%;height:81px;border-bottom:1px solid var(--border-color);background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, var(--background, var(--ion-background-color, #fff))), to(rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8)));background:linear-gradient(to bottom, var(--background, var(--ion-background-color, #fff)) 20%, rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8) 100%);z-index:10}.picker-above-highlight.sc-ion-picker-legacy-ios{inset-inline-start:0}.picker-below-highlight.sc-ion-picker-legacy-ios{top:115px;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);display:block;position:absolute;width:100%;height:119px;border-top:1px solid var(--border-color);background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, var(--background, var(--ion-background-color, #fff))), to(rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8)));background:linear-gradient(to top, var(--background, var(--ion-background-color, #fff)) 30%, rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8) 100%);z-index:11}.picker-below-highlight.sc-ion-picker-legacy-ios{inset-inline-start:0}\";\nconst IonPickerLegacyIosStyle0 = pickerIosCss;\nconst pickerMdCss = \".sc-ion-picker-legacy-md-h{--border-radius:0;--border-style:solid;--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--max-height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.sc-ion-picker-legacy-md-h{inset-inline-start:0}.overlay-hidden.sc-ion-picker-legacy-md-h{display:none}.picker-wrapper.sc-ion-picker-legacy-md{border-radius:var(--border-radius);left:0;right:0;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;overflow:hidden;z-index:10}.picker-toolbar.sc-ion-picker-legacy-md{width:100%;background:transparent;contain:strict;z-index:1}.picker-button.sc-ion-picker-legacy-md{border:0;font-family:inherit}.picker-button.sc-ion-picker-legacy-md:active,.picker-button.sc-ion-picker-legacy-md:focus{outline:none}.picker-columns.sc-ion-picker-legacy-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-pack:center;justify-content:center;margin-bottom:var(--ion-safe-area-bottom, 0);contain:strict;overflow:hidden}.picker-above-highlight.sc-ion-picker-legacy-md,.picker-below-highlight.sc-ion-picker-legacy-md{display:none;pointer-events:none}.sc-ion-picker-legacy-md-h{--background:var(--ion-background-color, #fff);--border-width:0.55px 0 0;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--height:260px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.26);color:var(--ion-item-color, var(--ion-text-color, #000))}.picker-toolbar.sc-ion-picker-legacy-md{display:-ms-flexbox;display:flex;-ms-flex-pack:end;justify-content:flex-end;height:44px}.picker-button.sc-ion-picker-legacy-md,.picker-button.ion-activated.sc-ion-picker-legacy-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:1.1em;padding-inline-start:1.1em;-webkit-padding-end:1.1em;padding-inline-end:1.1em;padding-top:0;padding-bottom:0;height:44px;background:transparent;color:var(--ion-color-primary, #0054e9);font-size:14px;font-weight:500;text-transform:uppercase;-webkit-box-shadow:none;box-shadow:none}.picker-columns.sc-ion-picker-legacy-md{height:216px;-webkit-perspective:1800px;perspective:1800px}.picker-above-highlight.sc-ion-picker-legacy-md{top:0;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);position:absolute;width:100%;height:81px;border-bottom:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, var(--ion-background-color, #fff)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background:linear-gradient(to bottom, var(--ion-background-color, #fff) 20%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%);z-index:10}.picker-above-highlight.sc-ion-picker-legacy-md{inset-inline-start:0}.picker-below-highlight.sc-ion-picker-legacy-md{top:115px;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);position:absolute;width:100%;height:119px;border-top:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, var(--ion-background-color, #fff)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background:linear-gradient(to top, var(--ion-background-color, #fff) 30%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%);z-index:11}.picker-below-highlight.sc-ion-picker-legacy-md{inset-inline-start:0}\";\nconst IonPickerLegacyMdStyle0 = pickerMdCss;\nconst Picker = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.didPresent = createEvent(this, \"ionPickerDidPresent\", 7);\n    this.willPresent = createEvent(this, \"ionPickerWillPresent\", 7);\n    this.willDismiss = createEvent(this, \"ionPickerWillDismiss\", 7);\n    this.didDismiss = createEvent(this, \"ionPickerDidDismiss\", 7);\n    this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n    this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n    this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n    this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n    this.delegateController = createDelegateController(this);\n    this.lockController = createLockController();\n    this.triggerController = createTriggerController();\n    this.onBackdropTap = () => {\n      this.dismiss(undefined, BACKDROP);\n    };\n    this.dispatchCancelHandler = ev => {\n      const role = ev.detail.role;\n      if (isCancel(role)) {\n        const cancelButton = this.buttons.find(b => b.role === 'cancel');\n        this.callButtonHandler(cancelButton);\n      }\n    };\n    this.presented = false;\n    this.overlayIndex = undefined;\n    this.delegate = undefined;\n    this.hasController = false;\n    this.keyboardClose = true;\n    this.enterAnimation = undefined;\n    this.leaveAnimation = undefined;\n    this.buttons = [];\n    this.columns = [];\n    this.cssClass = undefined;\n    this.duration = 0;\n    this.showBackdrop = true;\n    this.backdropDismiss = true;\n    this.animated = true;\n    this.htmlAttributes = undefined;\n    this.isOpen = false;\n    this.trigger = undefined;\n  }\n  onIsOpenChange(newValue, oldValue) {\n    if (newValue === true && oldValue === false) {\n      this.present();\n    } else if (newValue === false && oldValue === true) {\n      this.dismiss();\n    }\n  }\n  triggerChanged() {\n    const {\n      trigger,\n      el,\n      triggerController\n    } = this;\n    if (trigger) {\n      triggerController.addClickListener(el, trigger);\n    }\n  }\n  connectedCallback() {\n    prepareOverlay(this.el);\n    this.triggerChanged();\n  }\n  disconnectedCallback() {\n    this.triggerController.removeClickListener();\n  }\n  componentWillLoad() {\n    var _a;\n    if (!((_a = this.htmlAttributes) === null || _a === void 0 ? void 0 : _a.id)) {\n      setOverlayId(this.el);\n    }\n  }\n  componentDidLoad() {\n    printIonWarning('[ion-picker-legacy] - ion-picker-legacy and ion-picker-legacy-column have been deprecated in favor of new versions of the ion-picker and ion-picker-column components. These new components display inline with your page content allowing for more presentation flexibility than before.', this.el);\n    /**\n     * If picker was rendered with isOpen=\"true\"\n     * then we should open picker immediately.\n     */\n    if (this.isOpen === true) {\n      raf(() => this.present());\n    }\n    /**\n     * When binding values in frameworks such as Angular\n     * it is possible for the value to be set after the Web Component\n     * initializes but before the value watcher is set up in Stencil.\n     * As a result, the watcher callback may not be fired.\n     * We work around this by manually calling the watcher\n     * callback when the component has loaded and the watcher\n     * is configured.\n     */\n    this.triggerChanged();\n  }\n  /**\n   * Present the picker overlay after it has been created.\n   */\n  async present() {\n    const unlock = await this.lockController.lock();\n    await this.delegateController.attachViewToDom();\n    await present(this, 'pickerEnter', iosEnterAnimation, iosEnterAnimation, undefined);\n    if (this.duration > 0) {\n      this.durationTimeout = setTimeout(() => this.dismiss(), this.duration);\n    }\n    unlock();\n  }\n  /**\n   * Dismiss the picker overlay after it has been presented.\n   *\n   * @param data Any data to emit in the dismiss events.\n   * @param role The role of the element that is dismissing the picker.\n   * This can be useful in a button handler for determining which button was\n   * clicked to dismiss the picker.\n   * Some examples include: ``\"cancel\"`, `\"destructive\"`, \"selected\"`, and `\"backdrop\"`.\n   */\n  async dismiss(data, role) {\n    const unlock = await this.lockController.lock();\n    if (this.durationTimeout) {\n      clearTimeout(this.durationTimeout);\n    }\n    const dismissed = await dismiss(this, data, role, 'pickerLeave', iosLeaveAnimation, iosLeaveAnimation);\n    if (dismissed) {\n      this.delegateController.removeViewFromDom();\n    }\n    unlock();\n    return dismissed;\n  }\n  /**\n   * Returns a promise that resolves when the picker did dismiss.\n   */\n  onDidDismiss() {\n    return eventMethod(this.el, 'ionPickerDidDismiss');\n  }\n  /**\n   * Returns a promise that resolves when the picker will dismiss.\n   */\n  onWillDismiss() {\n    return eventMethod(this.el, 'ionPickerWillDismiss');\n  }\n  /**\n   * Get the column that matches the specified name.\n   *\n   * @param name The name of the column.\n   */\n  getColumn(name) {\n    return Promise.resolve(this.columns.find(column => column.name === name));\n  }\n  async buttonClick(button) {\n    const role = button.role;\n    if (isCancel(role)) {\n      return this.dismiss(undefined, role);\n    }\n    const shouldDismiss = await this.callButtonHandler(button);\n    if (shouldDismiss) {\n      return this.dismiss(this.getSelected(), button.role);\n    }\n    return Promise.resolve();\n  }\n  async callButtonHandler(button) {\n    if (button) {\n      // a handler has been provided, execute it\n      // pass the handler the values from the inputs\n      const rtn = await safeCall(button.handler, this.getSelected());\n      if (rtn === false) {\n        // if the return value of the handler is false then do not dismiss\n        return false;\n      }\n    }\n    return true;\n  }\n  getSelected() {\n    const selected = {};\n    this.columns.forEach((col, index) => {\n      const selectedColumn = col.selectedIndex !== undefined ? col.options[col.selectedIndex] : undefined;\n      selected[col.name] = {\n        text: selectedColumn ? selectedColumn.text : undefined,\n        value: selectedColumn ? selectedColumn.value : undefined,\n        columnIndex: index\n      };\n    });\n    return selected;\n  }\n  render() {\n    const {\n      htmlAttributes\n    } = this;\n    const mode = getIonMode(this);\n    return h(Host, Object.assign({\n      key: 'b6b6ca6f9aa74681e6d67f64b366f5965fec2a6d',\n      \"aria-modal\": \"true\",\n      tabindex: \"-1\"\n    }, htmlAttributes, {\n      style: {\n        zIndex: `${20000 + this.overlayIndex}`\n      },\n      class: Object.assign({\n        [mode]: true,\n        // Used internally for styling\n        [`picker-${mode}`]: true,\n        'overlay-hidden': true\n      }, getClassMap(this.cssClass)),\n      onIonBackdropTap: this.onBackdropTap,\n      onIonPickerWillDismiss: this.dispatchCancelHandler\n    }), h(\"ion-backdrop\", {\n      key: '20202ca1d7b6cd5f517a802879b39efb79033cb1',\n      visible: this.showBackdrop,\n      tappable: this.backdropDismiss\n    }), h(\"div\", {\n      key: '72fe76a1e1748593cdf38deab5100087bfa75983',\n      tabindex: \"0\",\n      \"aria-hidden\": \"true\"\n    }), h(\"div\", {\n      key: '921954cfc716f3774aab66677563754ff479a44a',\n      class: \"picker-wrapper ion-overlay-wrapper\",\n      role: \"dialog\"\n    }, h(\"div\", {\n      key: '224413950bfcf2a948e58c2554c2a37a4e6d0319',\n      class: \"picker-toolbar\"\n    }, this.buttons.map(b => h(\"div\", {\n      class: buttonWrapperClass(b)\n    }, h(\"button\", {\n      type: \"button\",\n      onClick: () => this.buttonClick(b),\n      class: buttonClass(b)\n    }, b.text)))), h(\"div\", {\n      key: '7e688c2d0705940ec8a9ace493b679e6a9b68860',\n      class: \"picker-columns\"\n    }, h(\"div\", {\n      key: '0ec2db79a9ca9e2a0b324b6c4b90176a0eb33df3',\n      class: \"picker-above-highlight\"\n    }), this.presented && this.columns.map(c => h(\"ion-picker-legacy-column\", {\n      col: c\n    })), h(\"div\", {\n      key: 'b8344f4f342fddc3f773435515567ef8f3accbb0',\n      class: \"picker-below-highlight\"\n    }))), h(\"div\", {\n      key: '374c7a6b31b0a00ab3913faeea0ec3d6c02274b9',\n      tabindex: \"0\",\n      \"aria-hidden\": \"true\"\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"isOpen\": [\"onIsOpenChange\"],\n      \"trigger\": [\"triggerChanged\"]\n    };\n  }\n};\nconst buttonWrapperClass = button => {\n  return {\n    [`picker-toolbar-${button.role}`]: button.role !== undefined,\n    'picker-toolbar-button': true\n  };\n};\nconst buttonClass = button => {\n  return Object.assign({\n    'picker-button': true,\n    'ion-activatable': true\n  }, getClassMap(button.cssClass));\n};\nPicker.style = {\n  ios: IonPickerLegacyIosStyle0,\n  md: IonPickerLegacyMdStyle0\n};\nconst pickerColumnIosCss = \".picker-col{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-box-sizing:content-box;box-sizing:content-box;contain:content}.picker-opts{position:relative;-ms-flex:1;flex:1;max-width:100%}.picker-opt{top:0;display:block;position:absolute;width:100%;border:0;text-align:center;text-overflow:ellipsis;white-space:nowrap;contain:strict;overflow:hidden;will-change:transform}.picker-opt{inset-inline-start:0}.picker-opt.picker-opt-disabled{pointer-events:none}.picker-opt-disabled{opacity:0}.picker-opts-left{-ms-flex-pack:start;justify-content:flex-start}.picker-opts-right{-ms-flex-pack:end;justify-content:flex-end}.picker-opt:active,.picker-opt:focus{outline:none}.picker-prefix{position:relative;-ms-flex:1;flex:1;text-align:end;white-space:nowrap}.picker-suffix{position:relative;-ms-flex:1;flex:1;text-align:start;white-space:nowrap}.picker-col{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:0;padding-bottom:0;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}.picker-prefix,.picker-suffix,.picker-opts{top:77px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;color:inherit;font-size:20px;line-height:42px;pointer-events:none}.picker-opt{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-transform-origin:center center;transform-origin:center center;height:46px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;-webkit-transition-timing-function:ease-out;transition-timing-function:ease-out;background:transparent;color:inherit;font-size:20px;line-height:42px;-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:auto}:host-context([dir=rtl]) .picker-opt{-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}[dir=rtl] .picker-opt{-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}@supports selector(:dir(rtl)){.picker-opt:dir(rtl){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}}\";\nconst IonPickerLegacyColumnIosStyle0 = pickerColumnIosCss;\nconst pickerColumnMdCss = \".picker-col{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-box-sizing:content-box;box-sizing:content-box;contain:content}.picker-opts{position:relative;-ms-flex:1;flex:1;max-width:100%}.picker-opt{top:0;display:block;position:absolute;width:100%;border:0;text-align:center;text-overflow:ellipsis;white-space:nowrap;contain:strict;overflow:hidden;will-change:transform}.picker-opt{inset-inline-start:0}.picker-opt.picker-opt-disabled{pointer-events:none}.picker-opt-disabled{opacity:0}.picker-opts-left{-ms-flex-pack:start;justify-content:flex-start}.picker-opts-right{-ms-flex-pack:end;justify-content:flex-end}.picker-opt:active,.picker-opt:focus{outline:none}.picker-prefix{position:relative;-ms-flex:1;flex:1;text-align:end;white-space:nowrap}.picker-suffix{position:relative;-ms-flex:1;flex:1;text-align:start;white-space:nowrap}.picker-col{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}.picker-prefix,.picker-suffix,.picker-opts{top:77px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;color:inherit;font-size:22px;line-height:42px;pointer-events:none}.picker-opt{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;height:43px;-webkit-transition-timing-function:ease-out;transition-timing-function:ease-out;background:transparent;color:inherit;font-size:22px;line-height:42px;-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:auto}.picker-prefix,.picker-suffix,.picker-opt.picker-opt-selected{color:var(--ion-color-primary, #0054e9)}\";\nconst IonPickerLegacyColumnMdStyle0 = pickerColumnMdCss;\nconst PickerColumnCmp = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionPickerColChange = createEvent(this, \"ionPickerColChange\", 7);\n    this.optHeight = 0;\n    this.rotateFactor = 0;\n    this.scaleFactor = 1;\n    this.velocity = 0;\n    this.y = 0;\n    this.noAnimate = true;\n    // `colDidChange` is a flag that gets set when the column is changed\n    // dynamically. When this flag is set, the column will refresh\n    // after the component re-renders to incorporate the new column data.\n    // This is necessary because `this.refresh` queries for the option elements,\n    // so it needs to wait for the latest elements to be available in the DOM.\n    // Ex: column is created with 3 options. User updates the column data\n    // to have 5 options. The column will still think it only has 3 options.\n    this.colDidChange = false;\n    this.col = undefined;\n  }\n  colChanged() {\n    this.colDidChange = true;\n  }\n  async connectedCallback() {\n    let pickerRotateFactor = 0;\n    let pickerScaleFactor = 0.81;\n    const mode = getIonMode(this);\n    if (mode === 'ios') {\n      pickerRotateFactor = -0.46;\n      pickerScaleFactor = 1;\n    }\n    this.rotateFactor = pickerRotateFactor;\n    this.scaleFactor = pickerScaleFactor;\n    this.gesture = (await import('./index-39782642.js')).createGesture({\n      el: this.el,\n      gestureName: 'picker-swipe',\n      gesturePriority: 100,\n      threshold: 0,\n      passive: false,\n      onStart: ev => this.onStart(ev),\n      onMove: ev => this.onMove(ev),\n      onEnd: ev => this.onEnd(ev)\n    });\n    this.gesture.enable();\n    // Options have not been initialized yet\n    // Animation must be disabled through the `noAnimate` flag\n    // Otherwise, the options will render\n    // at the top of the column and transition down\n    this.tmrId = setTimeout(() => {\n      this.noAnimate = false;\n      // After initialization, `refresh()` will be called\n      // At this point, animation will be enabled. The options will\n      // animate as they are being selected.\n      this.refresh(true);\n    }, 250);\n  }\n  componentDidLoad() {\n    this.onDomChange();\n  }\n  componentDidUpdate() {\n    // Options may have changed since last update.\n    if (this.colDidChange) {\n      // Animation must be disabled through the `onDomChange` parameter.\n      // Otherwise, the recently added options will render\n      // at the top of the column and transition down\n      this.onDomChange(true, false);\n      this.colDidChange = false;\n    }\n  }\n  disconnectedCallback() {\n    if (this.rafId !== undefined) cancelAnimationFrame(this.rafId);\n    if (this.tmrId) clearTimeout(this.tmrId);\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n  }\n  emitColChange() {\n    this.ionPickerColChange.emit(this.col);\n  }\n  setSelected(selectedIndex, duration) {\n    // if there is a selected index, then figure out it's y position\n    // if there isn't a selected index, then just use the top y position\n    const y = selectedIndex > -1 ? -(selectedIndex * this.optHeight) : 0;\n    this.velocity = 0;\n    // set what y position we're at\n    if (this.rafId !== undefined) cancelAnimationFrame(this.rafId);\n    this.update(y, duration, true);\n    this.emitColChange();\n  }\n  update(y, duration, saveY) {\n    if (!this.optsEl) {\n      return;\n    }\n    // ensure we've got a good round number :)\n    let translateY = 0;\n    let translateZ = 0;\n    const {\n      col,\n      rotateFactor\n    } = this;\n    const prevSelected = col.selectedIndex;\n    const selectedIndex = col.selectedIndex = this.indexForY(-y);\n    const durationStr = duration === 0 ? '' : duration + 'ms';\n    const scaleStr = `scale(${this.scaleFactor})`;\n    const children = this.optsEl.children;\n    for (let i = 0; i < children.length; i++) {\n      const button = children[i];\n      const opt = col.options[i];\n      const optOffset = i * this.optHeight + y;\n      let transform = '';\n      if (rotateFactor !== 0) {\n        const rotateX = optOffset * rotateFactor;\n        if (Math.abs(rotateX) <= 90) {\n          translateY = 0;\n          translateZ = 90;\n          transform = `rotateX(${rotateX}deg) `;\n        } else {\n          translateY = -9999;\n        }\n      } else {\n        translateZ = 0;\n        translateY = optOffset;\n      }\n      const selected = selectedIndex === i;\n      transform += `translate3d(0px,${translateY}px,${translateZ}px) `;\n      if (this.scaleFactor !== 1 && !selected) {\n        transform += scaleStr;\n      }\n      // Update transition duration\n      if (this.noAnimate) {\n        opt.duration = 0;\n        button.style.transitionDuration = '';\n      } else if (duration !== opt.duration) {\n        opt.duration = duration;\n        button.style.transitionDuration = durationStr;\n      }\n      // Update transform\n      if (transform !== opt.transform) {\n        opt.transform = transform;\n      }\n      button.style.transform = transform;\n      /**\n       * Ensure that the select column\n       * item has the selected class\n       */\n      opt.selected = selected;\n      if (selected) {\n        button.classList.add(PICKER_OPT_SELECTED);\n      } else {\n        button.classList.remove(PICKER_OPT_SELECTED);\n      }\n    }\n    this.col.prevSelected = prevSelected;\n    if (saveY) {\n      this.y = y;\n    }\n    if (this.lastIndex !== selectedIndex) {\n      // have not set a last index yet\n      hapticSelectionChanged();\n      this.lastIndex = selectedIndex;\n    }\n  }\n  decelerate() {\n    if (this.velocity !== 0) {\n      // still decelerating\n      this.velocity *= DECELERATION_FRICTION;\n      // do not let it go slower than a velocity of 1\n      this.velocity = this.velocity > 0 ? Math.max(this.velocity, 1) : Math.min(this.velocity, -1);\n      let y = this.y + this.velocity;\n      if (y > this.minY) {\n        // whoops, it's trying to scroll up farther than the options we have!\n        y = this.minY;\n        this.velocity = 0;\n      } else if (y < this.maxY) {\n        // gahh, it's trying to scroll down farther than we can!\n        y = this.maxY;\n        this.velocity = 0;\n      }\n      this.update(y, 0, true);\n      const notLockedIn = Math.round(y) % this.optHeight !== 0 || Math.abs(this.velocity) > 1;\n      if (notLockedIn) {\n        // isn't locked in yet, keep decelerating until it is\n        this.rafId = requestAnimationFrame(() => this.decelerate());\n      } else {\n        this.velocity = 0;\n        this.emitColChange();\n        hapticSelectionEnd();\n      }\n    } else if (this.y % this.optHeight !== 0) {\n      // needs to still get locked into a position so options line up\n      const currentPos = Math.abs(this.y % this.optHeight);\n      // create a velocity in the direction it needs to scroll\n      this.velocity = currentPos > this.optHeight / 2 ? 1 : -1;\n      this.decelerate();\n    }\n  }\n  indexForY(y) {\n    return Math.min(Math.max(Math.abs(Math.round(y / this.optHeight)), 0), this.col.options.length - 1);\n  }\n  onStart(detail) {\n    // We have to prevent default in order to block scrolling under the picker\n    // but we DO NOT have to stop propagation, since we still want\n    // some \"click\" events to capture\n    if (detail.event.cancelable) {\n      detail.event.preventDefault();\n    }\n    detail.event.stopPropagation();\n    hapticSelectionStart();\n    // reset everything\n    if (this.rafId !== undefined) cancelAnimationFrame(this.rafId);\n    const options = this.col.options;\n    let minY = options.length - 1;\n    let maxY = 0;\n    for (let i = 0; i < options.length; i++) {\n      if (!options[i].disabled) {\n        minY = Math.min(minY, i);\n        maxY = Math.max(maxY, i);\n      }\n    }\n    this.minY = -(minY * this.optHeight);\n    this.maxY = -(maxY * this.optHeight);\n  }\n  onMove(detail) {\n    if (detail.event.cancelable) {\n      detail.event.preventDefault();\n    }\n    detail.event.stopPropagation();\n    // update the scroll position relative to pointer start position\n    let y = this.y + detail.deltaY;\n    if (y > this.minY) {\n      // scrolling up higher than scroll area\n      y = Math.pow(y, 0.8);\n      this.bounceFrom = y;\n    } else if (y < this.maxY) {\n      // scrolling down below scroll area\n      y += Math.pow(this.maxY - y, 0.9);\n      this.bounceFrom = y;\n    } else {\n      this.bounceFrom = 0;\n    }\n    this.update(y, 0, false);\n  }\n  onEnd(detail) {\n    if (this.bounceFrom > 0) {\n      // bounce back up\n      this.update(this.minY, 100, true);\n      this.emitColChange();\n      return;\n    } else if (this.bounceFrom < 0) {\n      // bounce back down\n      this.update(this.maxY, 100, true);\n      this.emitColChange();\n      return;\n    }\n    this.velocity = clamp(-MAX_PICKER_SPEED, detail.velocityY * 23, MAX_PICKER_SPEED);\n    if (this.velocity === 0 && detail.deltaY === 0) {\n      const opt = detail.event.target.closest('.picker-opt');\n      if (opt === null || opt === void 0 ? void 0 : opt.hasAttribute('opt-index')) {\n        this.setSelected(parseInt(opt.getAttribute('opt-index'), 10), TRANSITION_DURATION);\n      }\n    } else {\n      this.y += detail.deltaY;\n      if (Math.abs(detail.velocityY) < 0.05) {\n        const isScrollingUp = detail.deltaY > 0;\n        const optHeightFraction = Math.abs(this.y) % this.optHeight / this.optHeight;\n        if (isScrollingUp && optHeightFraction > 0.5) {\n          this.velocity = Math.abs(this.velocity) * -1;\n        } else if (!isScrollingUp && optHeightFraction <= 0.5) {\n          this.velocity = Math.abs(this.velocity);\n        }\n      }\n      this.decelerate();\n    }\n  }\n  refresh(forceRefresh, animated) {\n    var _a;\n    let min = this.col.options.length - 1;\n    let max = 0;\n    const options = this.col.options;\n    for (let i = 0; i < options.length; i++) {\n      if (!options[i].disabled) {\n        min = Math.min(min, i);\n        max = Math.max(max, i);\n      }\n    }\n    /**\n     * Only update selected value if column has a\n     * velocity of 0. If it does not, then the\n     * column is animating might land on\n     * a value different than the value at\n     * selectedIndex\n     */\n    if (this.velocity !== 0) {\n      return;\n    }\n    const selectedIndex = clamp(min, (_a = this.col.selectedIndex) !== null && _a !== void 0 ? _a : 0, max);\n    if (this.col.prevSelected !== selectedIndex || forceRefresh) {\n      const y = selectedIndex * this.optHeight * -1;\n      const duration = animated ? TRANSITION_DURATION : 0;\n      this.velocity = 0;\n      this.update(y, duration, true);\n    }\n  }\n  onDomChange(forceRefresh, animated) {\n    const colEl = this.optsEl;\n    if (colEl) {\n      // DOM READ\n      // We perfom a DOM read over a rendered item, this needs to happen after the first render or after the column has changed\n      this.optHeight = colEl.firstElementChild ? colEl.firstElementChild.clientHeight : 0;\n    }\n    this.refresh(forceRefresh, animated);\n  }\n  render() {\n    const col = this.col;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '10ef653cbae8e1ed92a23fa17f3396082e35951f',\n      class: Object.assign({\n        [mode]: true,\n        'picker-col': true,\n        'picker-opts-left': this.col.align === 'left',\n        'picker-opts-right': this.col.align === 'right'\n      }, getClassMap(col.cssClass)),\n      style: {\n        'max-width': this.col.columnWidth\n      }\n    }, col.prefix && h(\"div\", {\n      key: '253066708012034206b1d3d466ea48d0a3be3323',\n      class: \"picker-prefix\",\n      style: {\n        width: col.prefixWidth\n      }\n    }, col.prefix), h(\"div\", {\n      key: '15fa07307d4d2b4994504a5bf2de8db05b51cd2c',\n      class: \"picker-opts\",\n      style: {\n        maxWidth: col.optionsWidth\n      },\n      ref: el => this.optsEl = el\n    }, col.options.map((o, index) => h(\"button\", {\n      \"aria-label\": o.ariaLabel,\n      class: {\n        'picker-opt': true,\n        'picker-opt-disabled': !!o.disabled\n      },\n      \"opt-index\": index\n    }, o.text))), col.suffix && h(\"div\", {\n      key: '4db779830b1e6aa6b324e5a72541d4f610ac1ff5',\n      class: \"picker-suffix\",\n      style: {\n        width: col.suffixWidth\n      }\n    }, col.suffix));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"col\": [\"colChanged\"]\n    };\n  }\n};\nconst PICKER_OPT_SELECTED = 'picker-opt-selected';\nconst DECELERATION_FRICTION = 0.97;\nconst MAX_PICKER_SPEED = 90;\nconst TRANSITION_DURATION = 150;\nPickerColumnCmp.style = {\n  ios: IonPickerLegacyColumnIosStyle0,\n  md: IonPickerLegacyColumnMdStyle0\n};\nexport { Datetime as ion_datetime, Picker as ion_picker_legacy, PickerColumnCmp as ion_picker_legacy_column };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAqBM,gBAcA,eAuDA,qBAiCA,iBAoBA,qBAaA,qBAaA,oBA6BA,wBAMA,oCA0BA,gBACA,sBACA,eACA,qBACA,UA47DF,aACE,aACA,cACA,iBACA,wBASA,mBAcA,mBAQA,cACA,0BACA,aACA,yBACA,QAyPA,oBAMA,aAUA,oBACA,gCACA,mBACA,+BACA,iBA4WA,qBACA,uBACA,kBACA;AAz0FN;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAM,iBAAiB,CAAC,SAAS,UAAU,aAAa;AACtD,UAAI,YAAY,SAAS,OAAO,SAAS;AACvC,eAAO;AAAA,MACT;AACA,UAAI,YAAY,SAAS,OAAO,SAAS;AACvC,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAMA,IAAM,gBAAgB,CAAC,UAAU,UAAU,UAAU,cAAc;AAKjE,UAAI,SAAS,QAAQ,MAAM;AACzB,eAAO;AAAA,MACT;AAMA,UAAI,cAAc,UAAa,CAAC,UAAU,SAAS,SAAS,GAAG,GAAG;AAChE,eAAO;AAAA,MACT;AAYA,UAAI,YAAY,SAAS,UAAU,QAAQ,GAAG;AAC5C,eAAO;AAAA,MACT;AAYA,UAAI,YAAY,QAAQ,UAAU,QAAQ,GAAG;AAC3C,eAAO;AAAA,MACT;AAMA,aAAO;AAAA,IACT;AAKA,IAAM,sBAAsB,CAAC,QAAQ,UAAU,aAAa,YAAY,UAAU,UAAU,cAAc;AAQxG,YAAM,mBAAmB,MAAM,QAAQ,WAAW,IAAI,cAAc,CAAC,WAAW;AAKhF,YAAM,WAAW,iBAAiB,KAAK,WAAS,UAAU,UAAU,KAAK,CAAC,MAAM;AAChF,YAAM,UAAU,UAAU,UAAU,UAAU;AAC9C,YAAM,WAAW,cAAc,UAAU,UAAU,UAAU,SAAS;AAKtE,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,WAAW,SAAS;AAAA,QAClC,WAAW,qBAAqB,QAAQ,SAAS,QAAQ;AAAA,QACzD,MAAM,SAAS,OAAO,OAAO,OAAO,QAAQ,QAAQ,IAAI;AAAA,MAC1D;AAAA,IACF;AAKA,IAAM,kBAAkB,CAAC,UAAU;AAAA,MACjC;AAAA,MACA;AAAA,IACF,MAAM;AAEJ,UAAI,eAAe,SAAS,MAAM,UAAU,QAAQ,GAAG;AACrD,eAAO;AAAA,MACT;AAGA,UAAI,YAAY,SAAS,UAAU,QAAQ,KAAK,YAAY,QAAQ,UAAU,QAAQ,GAAG;AACvF,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAMA,IAAM,sBAAsB,CAAC,UAAU,UAAU,aAAa;AAC5D,YAAM,YAAY,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,iBAAiB,QAAQ,CAAC,GAAG;AAAA,QAC7E,KAAK;AAAA,MACP,CAAC;AACD,aAAO,gBAAgB,WAAW;AAAA,QAChC;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAKA,IAAM,sBAAsB,CAAC,UAAU,aAAa;AAClD,YAAM,YAAY,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,aAAa,QAAQ,CAAC,GAAG;AAAA,QACzE,KAAK;AAAA,MACP,CAAC;AACD,aAAO,gBAAgB,WAAW;AAAA,QAChC;AAAA,MACF,CAAC;AAAA,IACH;AAMA,IAAM,qBAAqB,CAAC,kBAAkB,eAAe,OAAO;AAClE,UAAI,MAAM,QAAQ,gBAAgB,GAAG;AACnC,cAAM,wBAAwB,cAAc,MAAM,GAAG,EAAE,CAAC;AACxD,cAAM,oBAAoB,iBAAiB,KAAK,QAAM,GAAG,SAAS,qBAAqB;AACvF,YAAI,mBAAmB;AACrB,iBAAO;AAAA,YACL,WAAW,kBAAkB;AAAA,YAC7B,iBAAiB,kBAAkB;AAAA,UACrC;AAAA,QACF;AAAA,MACF,OAAO;AAKL,YAAI;AACF,iBAAO,iBAAiB,aAAa;AAAA,QACvC,SAAS,GAAG;AACV,wBAAc,0HAA0H,IAAI,CAAC;AAAA,QAC/I;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAOA,IAAM,yBAAyB,CAAC,IAAI,kBAAkB;AACpD,UAAI,IAAI,IAAI,IAAI;AAChB,YAAM,KAAK,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,KAAK,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,mBAAmB,KAAK,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,KAAK,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe;AAChkB,wBAAgB,wFAAwF,EAAE;AAAA,MAC5G;AAAA,IACF;AACA,IAAM,qCAAqC,CAAC,IAAI,cAAc,kBAAkB;AAE9E,UAAI,CAAC,cAAe;AAEpB,cAAQ,cAAc;AAAA,QACpB,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,cAAI,cAAc,SAAS,QAAW;AACpC,4BAAgB,yBAAyB,YAAY,2DAA2D,EAAE;AAAA,UACpH;AACA;AAAA,QACF,KAAK;AACH,cAAI,cAAc,SAAS,QAAW;AACpC,4BAAgB,qFAAqF,EAAE;AAAA,UACzG;AACA;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,cAAI,cAAc,SAAS,UAAa,cAAc,SAAS,QAAW;AACxE,4BAAgB,yBAAyB,YAAY,oFAAoF,EAAE;AAAA,UAC7I;AACA;AAAA,MACJ;AAAA,IACF;AACA,IAAM,iBAAiB;AACvB,IAAM,uBAAuB;AAC7B,IAAM,gBAAgB;AACtB,IAAM,sBAAsB;AAC5B,IAAM,WAAW,MAAM;AAAA,MACrB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,aAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,aAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,aAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,aAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,aAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,aAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,aAAK,UAAU,UAAU,aAAa;AACtC,aAAK,mBAAmB;AACxB,aAAK,4BAA4B,MAAM;AACrC,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,CAAC,YAAY,MAAM,QAAQ,KAAK,GAAG;AASrC,4BAAgB;AAAA;AAAA,mBAEL,MAAM,IAAI,OAAK,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC;AAAA,GACnD,KAAK,EAAE;AAAA,UACJ;AAAA,QACF;AACA,aAAK,WAAW,WAAS;AACvB,eAAK,QAAQ;AACb,eAAK,UAAU,KAAK;AAAA,YAClB;AAAA,UACF,CAAC;AAAA,QACH;AAWA,aAAK,6BAA6B,MAAM;AACtC,cAAI;AACJ,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,kBAAQ,KAAK,KAAK,cAAc,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,QACtE;AACA,aAAK,gBAAgB,MAAM;AACzB,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,iBAAO,MAAM,QAAQ,WAAW,IAAI,YAAY,CAAC,IAAI;AAAA,QACvD;AACA,aAAK,qBAAqB,UAAQ;AAChC,gBAAM,iBAAiB,KAAK,GAAG,QAAQ,wBAAwB;AAC/D,cAAI,gBAAgB;AAClB,2BAAe,QAAQ,QAAW,IAAI;AAAA,UACxC;AAAA,QACF;AACA,aAAK,kBAAkB,WAAS;AAC9B,eAAK,eAAe,OAAO,OAAO,CAAC,GAAG,KAAK;AAAA,QAC7C;AACA,aAAK,iBAAiB,CAAC,OAAO,aAAa,UAAU;AAKnD,cAAI,KAAK,UAAU;AACjB;AAAA,UACF;AACA,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AAWJ,gBAAM,iBAAiB,cAAc,OAAO,UAAU,QAAQ;AAC9D,eAAK,gBAAgB,cAAc;AACnC,cAAI,UAAU;AACZ,kBAAM,mBAAmB,MAAM,QAAQ,WAAW,IAAI,cAAc,CAAC,WAAW;AAChF,gBAAI,YAAY;AACd,mBAAK,cAAc,iBAAiB,OAAO,OAAK,CAAC,UAAU,GAAG,cAAc,CAAC;AAAA,YAC/E,OAAO;AACL,mBAAK,cAAc,CAAC,GAAG,kBAAkB,cAAc;AAAA,YACzD;AAAA,UACF,OAAO;AACL,iBAAK,cAAc,OAAO,OAAO,CAAC,GAAG,cAAc;AAAA,UACrD;AACA,gBAAM,oBAAoB,KAAK,GAAG,cAAc,kBAAkB,MAAM;AACxE,cAAI,qBAAqB,KAAK,oBAAoB;AAChD;AAAA,UACF;AACA,eAAK,QAAQ;AAAA,QACf;AACA,aAAK,8BAA8B,MAAM;AACvC,gBAAM,kBAAkB,KAAK;AAC7B,cAAI,CAAC,iBAAiB;AACpB;AAAA,UACF;AACA,gBAAM,OAAO,KAAK,GAAG;AAKrB,gBAAM,eAAe,gBAAgB,cAAc,gCAAgC;AAOnF,gBAAM,yBAAyB,QAAM;AACnC,gBAAI;AACJ,kBAAM,SAAS,GAAG,CAAC;AAOnB,kBAAM,KAAK,OAAO,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,aAAa,MAAM,CAAC,gBAAgB,UAAU,SAAS,aAAa,GAAG;AAClJ;AAAA,YACF;AACA,iBAAK,gBAAgB,YAAY;AAAA,UACnC;AACA,gBAAM,KAAK,IAAI,iBAAiB,sBAAsB;AACtD,aAAG,QAAQ,iBAAiB;AAAA,YAC1B,iBAAiB,CAAC,OAAO;AAAA,YACzB,mBAAmB;AAAA,UACrB,CAAC;AACD,eAAK,oBAAoB,MAAM;AAC7B,mBAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AAAA,UACxD;AAKA,0BAAgB,iBAAiB,WAAW,QAAM;AAChD,kBAAM,gBAAgB,KAAK;AAC3B,gBAAI,CAAC,iBAAiB,CAAC,cAAc,UAAU,SAAS,cAAc,GAAG;AACvE;AAAA,YACF;AACA,kBAAM,QAAQ,wBAAwB,aAAa;AACnD,gBAAI;AACJ,oBAAQ,GAAG,KAAK;AAAA,cACd,KAAK;AACH,mBAAG,eAAe;AAClB,+BAAe,YAAY,KAAK;AAChC;AAAA,cACF,KAAK;AACH,mBAAG,eAAe;AAClB,+BAAe,gBAAgB,KAAK;AACpC;AAAA,cACF,KAAK;AACH,mBAAG,eAAe;AAClB,+BAAe,WAAW,KAAK;AAC/B;AAAA,cACF,KAAK;AACH,mBAAG,eAAe;AAClB,+BAAe,eAAe,KAAK;AACnC;AAAA,cACF,KAAK;AACH,mBAAG,eAAe;AAClB,+BAAe,eAAe,KAAK;AACnC;AAAA,cACF,KAAK;AACH,mBAAG,eAAe;AAClB,+BAAe,aAAa,KAAK;AACjC;AAAA,cACF,KAAK;AACH,mBAAG,eAAe;AAClB,+BAAe,GAAG,WAAW,gBAAgB,KAAK,IAAI,iBAAiB,KAAK;AAC5E;AAAA,cACF,KAAK;AACH,mBAAG,eAAe;AAClB,+BAAe,GAAG,WAAW,YAAY,KAAK,IAAI,aAAa,KAAK;AACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAOF;AACE;AAAA,YACJ;AAKA,gBAAI,cAAc,cAAc,KAAK,UAAU,KAAK,QAAQ,GAAG;AAC7D;AAAA,YACF;AACA,iBAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,YAAY,GAAG,YAAY,CAAC;AAKtF,kCAAsB,MAAM,KAAK,gBAAgB,YAAY,CAAC;AAAA,UAChE,CAAC;AAAA,QACH;AACA,aAAK,kBAAkB,kBAAgB;AAMrC,gBAAM,UAAU,aAAa,iBAAiB,uBAAuB;AACrE,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,KAAK;AACT,cAAI,QAAQ,MAAM;AAChB;AAAA,UACF;AAKA,gBAAM,QAAQ,aAAa,cAAc,qCAAqC,QAAQ,SAAS,GAAG,iBAAiB;AACnH,cAAI,OAAO;AACT,kBAAM,MAAM;AAAA,UACd;AAAA,QACF;AACA,aAAK,kBAAkB,MAAM;AAC3B,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,QAAQ,QAAW;AACrB,iBAAK,WAAW;AAChB;AAAA,UACF;AACA,eAAK,WAAW,cAAc,KAAK,YAAY;AAAA,QACjD;AACA,aAAK,kBAAkB,MAAM;AAC3B,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,QAAQ,QAAW;AACrB,iBAAK,WAAW;AAChB;AAAA,UACF;AACA,eAAK,WAAW,cAAc,KAAK,YAAY;AAAA,QACjD;AACA,aAAK,6BAA6B,MAAM;AACtC,gBAAM,kBAAkB,KAAK;AAC7B,cAAI,CAAC,iBAAiB;AACpB;AAAA,UACF;AAgBA,gBAAM,SAAS,gBAAgB,iBAAiB,iBAAiB;AACjE,gBAAM,aAAa,OAAO,CAAC;AAC3B,gBAAM,eAAe,OAAO,CAAC;AAC7B,gBAAM,WAAW,OAAO,CAAC;AACzB,gBAAM,OAAO,WAAW,IAAI;AAC5B,gBAAM,wBAAwB,SAAS,SAAS,OAAO,cAAc,eAAe,UAAU,iBAAiB;AAO/G,oBAAU,MAAM;AACd,4BAAgB,aAAa,WAAW,eAAe,MAAM,KAAK,EAAE,IAAI,KAAK;AAC7E,kBAAM,kBAAkB,WAAS;AAC/B,oBAAM,MAAM,gBAAgB,sBAAsB;AAWlD,oBAAM,YAAY,MAAM,KAAK,EAAE,IAAI,gBAAgB,cAAc,KAAK,gBAAgB,cAAc;AACpG,oBAAM,QAAQ,YAAY,aAAa;AAWvC,oBAAM,WAAW,MAAM,sBAAsB;AAC7C,kBAAI,KAAK,IAAI,SAAS,IAAI,IAAI,CAAC,IAAI,EAAG;AAUtC,oBAAM;AAAA,gBACJ;AAAA,cACF,IAAI;AACJ,kBAAI,oBAAoB,QAAW;AACjC,uBAAO;AAAA,kBACL,OAAO,gBAAgB;AAAA,kBACvB,MAAM,gBAAgB;AAAA,kBACtB,KAAK,gBAAgB;AAAA,gBACvB;AAAA,cACF;AAOA,kBAAI,UAAU,YAAY;AACxB,uBAAO,iBAAiB,KAAK;AAAA,cAC/B,WAAW,UAAU,UAAU;AAC7B,uBAAO,aAAa,KAAK;AAAA,cAC3B,OAAO;AACL;AAAA,cACF;AAAA,YACF;AACA,kBAAM,oBAAoB,MAAM;AAC9B,kBAAI,uBAAuB;AACzB,gCAAgB,MAAM,eAAe,gBAAgB;AACrD,0CAA0B;AAAA,cAC5B;AAKA,oBAAM,UAAU,gBAAgB,KAAK,YAAY;AACjD,kBAAI,CAAC,QAAS;AACd,oBAAM;AAAA,gBACJ;AAAA,gBACA;AAAA,gBACA;AAAA,cACF,IAAI;AACJ,kBAAI,gBAAgB;AAAA,gBAClB;AAAA,gBACA;AAAA,gBACA,KAAK;AAAA,cACP,GAAG;AAAA,gBACD,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,QAAQ,GAAG;AAAA,kBACxD,KAAK;AAAA,gBACP,CAAC;AAAA,gBACD,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,QAAQ,GAAG;AAAA,kBACxD,KAAK;AAAA,gBACP,CAAC;AAAA,cACH,CAAC,GAAG;AACF;AAAA,cACF;AAMA,8BAAgB,MAAM,YAAY,YAAY,QAAQ;AAUtD,wBAAU,MAAM;AACd,qBAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,YAAY,GAAG;AAAA,kBACvE;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF,CAAC,CAAC;AACF,gCAAgB,aAAa,aAAa,eAAe,MAAM,KAAK,EAAE,IAAI,KAAK;AAC/E,gCAAgB,MAAM,eAAe,UAAU;AAC/C,oBAAI,KAAK,2BAA2B;AAClC,uBAAK,0BAA0B;AAAA,gBACjC;AAAA,cACF,CAAC;AAAA,YACH;AAKA,gBAAI;AAMJ,gBAAI,0BAA0B;AAC9B,kBAAM,iBAAiB,MAAM;AAC3B,kBAAI,eAAe;AACjB,6BAAa,aAAa;AAAA,cAC5B;AAUA,kBAAI,CAAC,2BAA2B,uBAAuB;AACrD,gCAAgB,MAAM,YAAY,kBAAkB,MAAM;AAC1D,0CAA0B;AAAA,cAC5B;AAEA,8BAAgB,WAAW,mBAAmB,EAAE;AAAA,YAClD;AACA,4BAAgB,iBAAiB,UAAU,cAAc;AACzD,iBAAK,0BAA0B,MAAM;AACnC,8BAAgB,oBAAoB,UAAU,cAAc;AAAA,YAC9D;AAAA,UACF,CAAC;AAAA,QACH;AAMA,aAAK,8BAA8B,MAAM;AACvC,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,4BAA4B,QAAW;AACzC,oCAAwB;AAAA,UAC1B;AACA,cAAI,sBAAsB,QAAW;AACnC,8BAAkB;AAAA,UACpB;AAAA,QACF;AACA,aAAK,eAAe,WAAS;AAC3B,gBAAM,WAAW,UAAU,QAAQ,UAAU,UAAa,UAAU,OAAO,CAAC,MAAM,QAAQ,KAAK,KAAK,MAAM,SAAS;AACnH,gBAAM,iBAAiB,WAAW,UAAU,KAAK,IAAI,KAAK;AAC1D,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,eAAK,0BAA0B;AAK/B,cAAI,CAAC,gBAAgB;AACnB;AAAA,UACF;AASA,cAAI,UAAU;AACZ,mCAAuB,gBAAgB,UAAU,QAAQ;AAAA,UAC3D;AAMA,gBAAM,cAAc,MAAM,QAAQ,cAAc,IAAI,eAAe,CAAC,IAAI;AACxE,gBAAM,cAAc,UAAU,aAAa,UAAU,QAAQ;AAC7D,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM,OAAO,UAAU,IAAI;AAO3B,cAAI,UAAU;AACZ,gBAAI,MAAM,QAAQ,cAAc,GAAG;AACjC,mBAAK,cAAc,CAAC,GAAG,cAAc;AAAA,YACvC,OAAO;AACL,mBAAK,cAAc;AAAA,gBACjB;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AAAA,UACF,OAAO;AAML,iBAAK,cAAc,CAAC;AAAA,UACtB;AAQA,gBAAM,iBAAiB,UAAU,UAAa,UAAU,aAAa,SAAS,SAAS,UAAa,SAAS,aAAa;AAC1H,gBAAM,gBAAgB,GAAG,UAAU,SAAS,gBAAgB;AAC5D,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,iCAAiC;AACrC,cAAI,MAAM,QAAQ,cAAc,GAAG;AACjC,kBAAM,aAAa,eAAe,CAAC,EAAE;AACrC,uBAAW,QAAQ,gBAAgB;AACjC,kBAAI,KAAK,UAAU,YAAY;AAC7B,iDAAiC;AACjC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAQA,cAAI,gCAAgC;AAClC,gBAAI,eAAe,kBAAkB,iBAAiB,CAAC,kBAAkB;AACvE,mBAAK,cAAc,WAAW;AAAA,YAChC,OAAO;AAKL,mBAAK,gBAAgB;AAAA,gBACnB;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AACA,aAAK,gBAAgB,CAAM,gBAAe;AACxC,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AAOJ,eAAK,kBAAkB;AAQvB,gBAAM,4BAA4B,IAAI,QAAQ,aAAW;AACvD,iBAAK,4BAA4B;AAAA,UACnC,CAAC;AAKD,gBAAM,sBAAsB,SAAS,aAAa,YAAY;AAC9D,gCAAsB,KAAK,UAAU,IAAI,KAAK,UAAU;AACxD,gBAAM;AACN,eAAK,4BAA4B;AACjC,eAAK,kBAAkB;AAAA,QACzB;AACA,aAAK,UAAU,MAAM;AACnB,eAAK,SAAS,KAAK;AAAA,QACrB;AACA,aAAK,SAAS,MAAM;AAClB,eAAK,QAAQ,KAAK;AAAA,QACpB;AACA,aAAK,WAAW,MAAM;AACpB,iBAAO,KAAK,SAAS;AAAA,QACvB;AACA,aAAK,YAAY,MAAM;AACrB,gBAAM,kBAAkB,KAAK;AAC7B,cAAI,CAAC,iBAAiB;AACpB;AAAA,UACF;AACA,gBAAM,YAAY,gBAAgB,cAAc,8BAA8B;AAC9E,cAAI,CAAC,WAAW;AACd;AAAA,UACF;AACA,gBAAM,OAAO,UAAU,cAAc;AACrC,0BAAgB,SAAS;AAAA,YACvB,KAAK;AAAA,YACL,MAAM,QAAQ,MAAM,KAAK,EAAE,IAAI,KAAK;AAAA,YACpC,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AACA,aAAK,YAAY,MAAM;AACrB,gBAAM,kBAAkB,KAAK;AAC7B,cAAI,CAAC,iBAAiB;AACpB;AAAA,UACF;AACA,gBAAM,YAAY,gBAAgB,cAAc,+BAA+B;AAC/E,cAAI,CAAC,WAAW;AACd;AAAA,UACF;AACA,0BAAgB,SAAS;AAAA,YACvB,KAAK;AAAA,YACL,MAAM;AAAA,YACN,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AACA,aAAK,yBAAyB,MAAM;AAClC,eAAK,mBAAmB,CAAC,KAAK;AAAA,QAChC;AACA,aAAK,mBAAmB;AACxB,aAAK,cAAc,CAAC;AACpB,aAAK,eAAe;AAAA,UAClB,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,MAAM;AAAA,QACR;AACA,aAAK,oBAAoB;AACzB,aAAK,kBAAkB;AACvB,aAAK,QAAQ;AACb,aAAK,OAAO,KAAK;AACjB,aAAK,WAAW;AAChB,aAAK,gBAAgB;AACrB,aAAK,WAAW;AAChB,aAAK,gBAAgB;AACrB,aAAK,MAAM;AACX,aAAK,MAAM;AACX,aAAK,eAAe;AACpB,aAAK,aAAa;AAClB,aAAK,WAAW;AAChB,aAAK,YAAY;AACjB,aAAK,aAAa;AAClB,aAAK,cAAc;AACnB,aAAK,YAAY;AACjB,aAAK,aAAa;AAClB,aAAK,eAAe;AACpB,aAAK,SAAS;AACd,aAAK,iBAAiB;AACtB,aAAK,8BAA8B;AACnC,aAAK,WAAW;AAChB,aAAK,mBAAmB;AACxB,aAAK,QAAQ;AACb,aAAK,mBAAmB;AACxB,aAAK,qBAAqB;AAC1B,aAAK,kBAAkB;AACvB,aAAK,uBAAuB;AAC5B,aAAK,YAAY;AACjB,aAAK,OAAO;AACZ,aAAK,cAAc;AAAA,MACrB;AAAA,MACA,uBAAuB;AACrB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,2CAAmC,IAAI,cAAc,aAAa;AAClE,+BAAuB,IAAI,aAAa;AAAA,MAC1C;AAAA,MACA,kBAAkB;AAChB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,aAAa;AACX,aAAK,gBAAgB;AAAA,MACvB;AAAA,MACA,aAAa;AACX,aAAK,gBAAgB;AAAA,MACvB;AAAA,MACA,sBAAsB;AACpB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,2CAAmC,IAAI,cAAc,aAAa;AAAA,MACpE;AAAA,MACA,IAAI,cAAc;AAChB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,sBAAsB,iBAAiB,UAAU,iBAAiB,eAAe,iBAAiB;AACxG,eAAO,uBAAuB,CAAC;AAAA,MACjC;AAAA,MACA,oBAAoB;AAClB,aAAK,mBAAmB,wBAAwB,KAAK,UAAU;AAAA,MACjE;AAAA,MACA,qBAAqB;AACnB,aAAK,oBAAoB,wBAAwB,KAAK,WAAW;AAAA,MACnE;AAAA,MACA,mBAAmB;AACjB,aAAK,kBAAkB,wBAAwB,KAAK,SAAS;AAAA,MAC/D;AAAA,MACA,oBAAoB;AAClB,aAAK,mBAAmB,wBAAwB,KAAK,UAAU;AAAA,MACjE;AAAA,MACA,sBAAsB;AACpB,aAAK,qBAAqB,wBAAwB,KAAK,YAAY;AAAA,MACrE;AAAA;AAAA;AAAA;AAAA,MAIM,eAAe;AAAA;AACnB,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,cAAI,KAAK,SAAS,GAAG;AACnB,iBAAK,aAAa,KAAK;AAAA,UACzB;AACA,eAAK,UAAU;AACf,eAAK,eAAe,KAAK;AAAA,YACvB;AAAA,UACF,CAAC;AAAA,QACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMM,QAAQ,eAAe,OAAO;AAAA;AAClC,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AAIJ,cAAI,gBAAgB,UAAa,CAAC,kBAAkB;AAClD,kBAAM,qBAAqB,MAAM,QAAQ,WAAW;AACpD,gBAAI,sBAAsB,YAAY,WAAW,GAAG;AAClD,kBAAI,aAAa;AAMf,qBAAK,SAAS,iBAAiB,YAAY,CAAC;AAAA,cAC9C,OAAO;AACL,qBAAK,SAAS,MAAS;AAAA,cACzB;AAAA,YACF,OAAO;AACL,mBAAK,SAAS,iBAAiB,WAAW,CAAC;AAAA,YAC7C;AAAA,UACF;AACA,cAAI,cAAc;AAChB,iBAAK,mBAAmB,YAAY;AAAA,UACtC;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMM,MAAM,WAAW;AAAA;AACrB,eAAK,aAAa,SAAS;AAAA,QAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOM,OAAO,eAAe,OAAO;AAAA;AACjC,eAAK,UAAU,KAAK;AACpB,cAAI,cAAc;AAChB,iBAAK,mBAAmB,WAAW;AAAA,UACrC;AAAA,QACF;AAAA;AAAA,MACA,IAAI,mBAAmB;AACrB,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,eAAO,iBAAiB,UAAU,iBAAiB,eAAe,iBAAiB;AAAA,MACrF;AAAA,MACA,oBAAoB;AAClB,aAAK,oBAAoB,kBAAkB,KAAK,EAAE,EAAE;AAAA,MACtD;AAAA,MACA,uBAAuB;AACrB,YAAI,KAAK,mBAAmB;AAC1B,eAAK,kBAAkB;AACvB,eAAK,oBAAoB;AAAA,QAC3B;AAAA,MACF;AAAA,MACA,sBAAsB;AACpB,aAAK,2BAA2B;AAChC,aAAK,4BAA4B;AAAA,MACnC;AAAA,MACA,mBAAmB;AACjB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AAQJ,cAAM,kBAAkB,aAAW;AACjC,gBAAM,KAAK,QAAQ,CAAC;AACpB,cAAI,CAAC,GAAG,gBAAgB;AACtB;AAAA,UACF;AACA,eAAK,oBAAoB;AASzB,oBAAU,MAAM;AACd,iBAAK,GAAG,UAAU,IAAI,gBAAgB;AAAA,UACxC,CAAC;AAAA,QACH;AACA,cAAM,YAAY,IAAI,qBAAqB,iBAAiB;AAAA,UAC1D,WAAW;AAAA,UACX,MAAM;AAAA,QACR,CAAC;AAOD,YAAI,MAAM,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,QAAQ,sBAAsB,CAAC;AAQzG,cAAM,iBAAiB,aAAW;AAChC,gBAAM,KAAK,QAAQ,CAAC;AACpB,cAAI,GAAG,gBAAgB;AACrB;AAAA,UACF;AACA,eAAK,4BAA4B;AAQjC,eAAK,mBAAmB;AACxB,oBAAU,MAAM;AACd,iBAAK,GAAG,UAAU,OAAO,gBAAgB;AAAA,UAC3C,CAAC;AAAA,QACH;AACA,cAAM,WAAW,IAAI,qBAAqB,gBAAgB;AAAA,UACxD,WAAW;AAAA,UACX,MAAM;AAAA,QACR,CAAC;AACD,YAAI,MAAM,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,QAAQ,sBAAsB,CAAC;AAUtG,cAAM,OAAO,eAAe,KAAK,EAAE;AACnC,aAAK,iBAAiB,YAAY,QAAM,GAAG,gBAAgB,CAAC;AAC5D,aAAK,iBAAiB,WAAW,QAAM,GAAG,gBAAgB,CAAC;AAAA,MAC7D;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,qBAAqB;AACnB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AAcJ,cAAM,kBAAkB,CAAC,eAAe,CAAC,aAAa,aAAa,MAAM,EAAE,SAAS,YAAY;AAChG,YAAI,aAAa,UAAa,mBAAmB,iBAAiB;AAChE,gBAAM,eAAe,gBAAgB,cAAc,gCAAgC;AAcnF,cAAI,gBAAgB,oBAAoB,QAAW;AACjD,4BAAgB,aAAa,aAAa,eAAe,MAAM,KAAK,EAAE,IAAI,KAAK;AAAA,UACjF;AAAA,QACF;AACA,YAAI,qBAAqB,MAAM;AAC7B,eAAK,mBAAmB;AACxB;AAAA,QACF;AACA,YAAI,iBAAiB,kBAAkB;AACrC;AAAA,QACF;AACA,aAAK,mBAAmB;AACxB,aAAK,4BAA4B;AACjC,aAAK,oBAAoB;AAMzB,aAAK,mBAAmB;AACxB,YAAI,MAAM;AACR,eAAK,UAAU,KAAK;AAAA,QACtB,CAAC;AAAA,MACH;AAAA,MACA,oBAAoB;AAClB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,UAAU;AACZ,cAAI,iBAAiB,QAAQ;AAC3B,4BAAgB,uFAAuF,EAAE;AAAA,UAC3G;AACA,cAAI,aAAa;AACf,4BAAgB,sFAAsF,EAAE;AAAA,UAC1G;AAAA,QACF;AACA,YAAI,qBAAqB,QAAW;AAClC,cAAI,iBAAiB,UAAU,iBAAiB,eAAe,iBAAiB,aAAa;AAC3F,4BAAgB,2HAA2H,EAAE;AAAA,UAC/I;AACA,cAAI,aAAa;AACf,4BAAgB,4FAA4F,EAAE;AAAA,UAChH;AAAA,QACF;AACA,YAAI,eAAe;AACjB,6CAAmC,IAAI,cAAc,aAAa;AAClE,iCAAuB,IAAI,aAAa;AAAA,QAC1C;AACA,cAAM,aAAa,KAAK,mBAAmB,wBAAwB,KAAK,UAAU;AAClF,cAAM,eAAe,KAAK,qBAAqB,wBAAwB,KAAK,YAAY;AACxF,cAAM,cAAc,KAAK,oBAAoB,wBAAwB,KAAK,WAAW;AACrF,cAAM,aAAa,KAAK,mBAAmB,wBAAwB,KAAK,UAAU;AAClF,cAAM,YAAY,KAAK,kBAAkB,wBAAwB,KAAK,SAAS;AAC/E,cAAM,aAAa,KAAK,aAAa,UAAU,SAAS,CAAC;AACzD,aAAK,gBAAgB;AACrB,aAAK,gBAAgB;AACrB,aAAK,eAAe,oBAAoB;AAAA,UACtC,UAAU;AAAA,UACV;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAU,KAAK;AAAA,UACf,UAAU,KAAK;AAAA,QACjB,CAAC;AACD,aAAK,aAAa,KAAK,KAAK;AAC5B,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,YAAY;AACV,aAAK,SAAS,KAAK;AAAA,UACjB,aAAa;AAAA,UACb,UAAU;AAAA,UACV,wBAAwB,KAAK;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,eAAe;AACb,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AAMJ,cAAM,mBAAmB,YAAY;AACrC,cAAM,oBAAoB,KAAK,GAAG,cAAc,kBAAkB,MAAM;AACxE,YAAI,CAAC,qBAAqB,CAAC,sBAAsB,CAAC,iBAAiB;AACjE;AAAA,QACF;AACA,cAAM,mBAAmB,MAAM;AAC7B,eAAK,MAAM;AACX,eAAK,SAAS,MAAS;AAAA,QACzB;AAQA,eAAO,EAAE,OAAO;AAAA,UACd,OAAO;AAAA,QACT,GAAG,EAAE,OAAO;AAAA,UACV,OAAO;AAAA,QACT,GAAG,EAAE,OAAO;AAAA,UACV,OAAO;AAAA,YACL,CAAC,yBAAyB,GAAG;AAAA,YAC7B,CAAC,kBAAkB,GAAG,KAAK;AAAA,UAC7B;AAAA,QACF,GAAG,EAAE,QAAQ;AAAA,UACX,MAAM;AAAA,QACR,GAAG,EAAE,eAAe,MAAM,sBAAsB,EAAE,cAAc;AAAA,UAC9D,IAAI;AAAA,UACJ,OAAO,KAAK;AAAA,UACZ,SAAS,MAAM,KAAK,OAAO,IAAI;AAAA,UAC/B,UAAU;AAAA,QACZ,GAAG,KAAK,UAAU,GAAG,EAAE,OAAO;AAAA,UAC5B,OAAO;AAAA,QACT,GAAG,mBAAmB,EAAE,cAAc;AAAA,UACpC,IAAI;AAAA,UACJ,OAAO,KAAK;AAAA,UACZ,SAAS,MAAM,iBAAiB;AAAA,UAChC,UAAU;AAAA,QACZ,GAAG,KAAK,SAAS,GAAG,sBAAsB,EAAE,cAAc;AAAA,UACxD,IAAI;AAAA,UACJ,OAAO,KAAK;AAAA,UACZ,SAAS,MAAM,KAAK,QAAQ,IAAI;AAAA,UAChC,UAAU;AAAA,QACZ,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,MACxB;AAAA;AAAA;AAAA;AAAA,MAIA,kBAAkB,oBAAoB,KAAK,cAAc;AAUvD,cAAM,cAAc,sBAAsB,cAAc,CAAC,KAAK,wBAAwB,iBAAiB,GAAG,KAAK,wBAAwB,iBAAiB,CAAC,IAAI,CAAC,KAAK,wBAAwB,iBAAiB,GAAG,KAAK,wBAAwB,iBAAiB,CAAC;AAC9P,eAAO,EAAE,cAAc,MAAM,WAAW;AAAA,MAC1C;AAAA,MACA,wBAAwB,mBAAmB;AACzC,eAAO,sBAAsB,eAAe,sBAAsB,cAAc,KAAK,+BAA+B,IAAI,KAAK,kCAAkC,iBAAiB;AAAA,MAClL;AAAA,MACA,iCAAiC;AAC/B,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,aAAa,KAAK,2BAA2B;AAKnD,cAAM,iBAAiB,eAAe,YAAY;AAClD,cAAM,YAAY,eAAe,eAAe,SAAS,CAAC;AAI1D,uBAAe,CAAC,EAAE,MAAM;AACxB,kBAAU,MAAM,kBAAkB,UAAU,OAAO,UAAU,IAAI;AASjE,cAAM,MAAM,aAAa,UAAa,QAAQ,UAAU,eAAe,CAAC,CAAC,IAAI,WAAW,eAAe,CAAC;AACxG,cAAM,MAAM,aAAa,UAAa,SAAS,UAAU,SAAS,IAAI,WAAW;AACjF,cAAM,SAAS,0BAA0B,QAAQ,YAAY,KAAK,KAAK,KAAK,iBAAiB,KAAK,iBAAiB;AACnH,YAAI,QAAQ,OAAO;AACnB,cAAM,QAAQ,OAAO;AACrB,YAAI,eAAe;AACjB,kBAAQ,MAAM,IAAI,CAAC,YAAY,UAAU;AACvC,kBAAM,iBAAiB,MAAM,KAAK;AAClC,gBAAIA;AACJ,gBAAI;AAMF,cAAAA,YAAW,CAAC,cAAc,iBAAiB,cAAc,CAAC;AAAA,YAC5D,SAAS,GAAG;AACV,4BAAc,uHAAuH,CAAC;AAAA,YACxI;AACA,mBAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG;AAAA,cAClD,UAAAA;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAKA,cAAM,cAAc,aAAa,QAAQ,OAAO,GAAG,aAAa,IAAI,IAAI,aAAa,KAAK,IAAI,aAAa,GAAG,KAAK,GAAG,aAAa,IAAI,IAAI,aAAa,KAAK,IAAI,aAAa,GAAG;AACjL,eAAO,EAAE,qBAAqB;AAAA,UAC5B,cAAc;AAAA,UACd,OAAO;AAAA,UACP,OAAO,KAAK;AAAA,UACZ;AAAA,UACA,OAAO;AAAA,UACP,aAAa,QAAM;AACjB,kBAAM;AAAA,cACJ;AAAA,YACF,IAAI,GAAG;AACP,kBAAM,WAAW,MAAM,KAAK,CAAC;AAAA,cAC3B;AAAA,cACA;AAAA,cACA;AAAA,YACF,MAAM,UAAU,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,EAAE;AACzC,iBAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,QAAQ,CAAC;AAC7E,iBAAK,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG,QAAQ,CAAC;AAC1E,eAAG,gBAAgB;AAAA,UACrB;AAAA,QACF,GAAG,MAAM,IAAI,UAAQ,EAAE,4BAA4B;AAAA,UACjD,MAAM,KAAK,UAAU,cAAc,GAAG,eAAe,IAAI,sBAAsB,KAAK;AAAA,UACpF,KAAK,KAAK;AAAA,UACV,UAAU,KAAK;AAAA,UACf,OAAO,KAAK;AAAA,QACd,GAAG,KAAK,IAAI,CAAC,CAAC;AAAA,MAChB;AAAA,MACA,kCAAkC,mBAAmB;AACnD,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,qBAAqB,sBAAsB,UAAU,sBAAsB;AACjF,cAAM,SAAS,qBAAqB,mBAAmB,KAAK,QAAQ,cAAc,KAAK,UAAU,KAAK,UAAU,KAAK,iBAAiB,IAAI,CAAC;AAC3I,cAAM,mBAAmB,sBAAsB;AAC/C,YAAI,OAAO,mBAAmB,iBAAiB,KAAK,QAAQ,cAAc,KAAK,UAAU,KAAK,UAAU,KAAK,eAAe,IAAI,CAAC;AACjI,YAAI,eAAe;AACjB,iBAAO,KAAK,IAAI,eAAa;AAC3B,kBAAM;AAAA,cACJ;AAAA,YACF,IAAI;AACJ,kBAAM,WAAW,OAAO,UAAU,WAAW,SAAS,KAAK,IAAI;AAC/D,kBAAM,iBAAiB;AAAA,cACrB,OAAO,aAAa;AAAA,cACpB,KAAK;AAAA,cACL,MAAM,aAAa;AAAA,YACrB;AACA,gBAAI;AACJ,gBAAI;AAMF,yBAAW,CAAC,cAAc,iBAAiB,cAAc,CAAC;AAAA,YAC5D,SAAS,GAAG;AACV,4BAAc,uHAAuH,CAAC;AAAA,YACxI;AACA,mBAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,SAAS,GAAG;AAAA,cACjD;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,cAAM,oBAAoB,sBAAsB,WAAW,sBAAsB;AACjF,cAAM,QAAQ,oBAAoB,kBAAkB,KAAK,QAAQ,KAAK,cAAc,KAAK,UAAU,KAAK,UAAU,KAAK,gBAAgB,IAAI,CAAC;AAI5I,cAAM,iBAAiB,mBAAmB,KAAK,QAAQ;AAAA,UACrD,OAAO;AAAA,UACP,KAAK;AAAA,QACP,CAAC;AACD,YAAI,cAAc,CAAC;AACnB,YAAI,gBAAgB;AAClB,wBAAc,CAAC,KAAK,wBAAwB,MAAM,GAAG,KAAK,sBAAsB,IAAI,GAAG,KAAK,uBAAuB,KAAK,CAAC;AAAA,QAC3H,OAAO;AACL,wBAAc,CAAC,KAAK,sBAAsB,IAAI,GAAG,KAAK,wBAAwB,MAAM,GAAG,KAAK,uBAAuB,KAAK,CAAC;AAAA,QAC3H;AACA,eAAO;AAAA,MACT;AAAA,MACA,sBAAsB,MAAM;AAC1B,YAAI;AACJ,YAAI,KAAK,WAAW,GAAG;AACrB,iBAAO,CAAC;AAAA,QACV;AACA,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,aAAa,KAAK,2BAA2B;AACnD,cAAM,qBAAqB,KAAK,aAAa,QAAQ,OAAO,aAAa,MAAM,KAAK,aAAa,SAAS,QAAQ,OAAO,SAAS,KAAK;AACvI,eAAO,EAAE,qBAAqB;AAAA,UAC5B,cAAc;AAAA,UACd,OAAO;AAAA,UACP,OAAO,KAAK;AAAA,UACZ;AAAA,UACA,OAAO;AAAA,UACP,aAAa,QAAM;AACjB,iBAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG;AAAA,cAClE,KAAK,GAAG,OAAO;AAAA,YACjB,CAAC,CAAC;AACF,iBAAK,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG;AAAA,cAC/D,KAAK,GAAG,OAAO;AAAA,YACjB,CAAC,CAAC;AACF,eAAG,gBAAgB;AAAA,UACrB;AAAA,QACF,GAAG,KAAK,IAAI,SAAO,EAAE,4BAA4B;AAAA,UAC/C,MAAM,IAAI,UAAU,oBAAoB,GAAG,eAAe,IAAI,sBAAsB,KAAK;AAAA,UACzF,KAAK,IAAI;AAAA,UACT,UAAU,IAAI;AAAA,UACd,OAAO,IAAI;AAAA,QACb,GAAG,IAAI,IAAI,CAAC,CAAC;AAAA,MACf;AAAA,MACA,wBAAwB,QAAQ;AAC9B,YAAI,OAAO,WAAW,GAAG;AACvB,iBAAO,CAAC;AAAA,QACV;AACA,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,aAAa,KAAK,2BAA2B;AACnD,eAAO,EAAE,qBAAqB;AAAA,UAC5B,cAAc;AAAA,UACd,OAAO;AAAA,UACP,OAAO,KAAK;AAAA,UACZ;AAAA,UACA,OAAO,aAAa;AAAA,UACpB,aAAa,QAAM;AACjB,iBAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG;AAAA,cAClE,OAAO,GAAG,OAAO;AAAA,YACnB,CAAC,CAAC;AACF,iBAAK,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG;AAAA,cAC/D,OAAO,GAAG,OAAO;AAAA,YACnB,CAAC,CAAC;AACF,eAAG,gBAAgB;AAAA,UACrB;AAAA,QACF,GAAG,OAAO,IAAI,WAAS,EAAE,4BAA4B;AAAA,UACnD,MAAM,MAAM,UAAU,aAAa,QAAQ,GAAG,eAAe,IAAI,sBAAsB,KAAK;AAAA,UAC5F,KAAK,MAAM;AAAA,UACX,UAAU,MAAM;AAAA,UAChB,OAAO,MAAM;AAAA,QACf,GAAG,MAAM,IAAI,CAAC,CAAC;AAAA,MACjB;AAAA,MACA,uBAAuB,OAAO;AAC5B,YAAI,MAAM,WAAW,GAAG;AACtB,iBAAO,CAAC;AAAA,QACV;AACA,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,aAAa,KAAK,2BAA2B;AACnD,eAAO,EAAE,qBAAqB;AAAA,UAC5B,cAAc;AAAA,UACd,OAAO;AAAA,UACP,OAAO,KAAK;AAAA,UACZ;AAAA,UACA,OAAO,aAAa;AAAA,UACpB,aAAa,QAAM;AACjB,iBAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG;AAAA,cAClE,MAAM,GAAG,OAAO;AAAA,YAClB,CAAC,CAAC;AACF,iBAAK,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG;AAAA,cAC/D,MAAM,GAAG,OAAO;AAAA,YAClB,CAAC,CAAC;AACF,eAAG,gBAAgB;AAAA,UACrB;AAAA,QACF,GAAG,MAAM,IAAI,UAAQ,EAAE,4BAA4B;AAAA,UACjD,MAAM,KAAK,UAAU,aAAa,OAAO,GAAG,eAAe,IAAI,sBAAsB,KAAK;AAAA,UAC1F,KAAK,KAAK;AAAA,UACV,UAAU,KAAK;AAAA,UACf,OAAO,KAAK;AAAA,QACd,GAAG,KAAK,IAAI,CAAC,CAAC;AAAA,MAChB;AAAA,MACA,wBAAwB,mBAAmB;AACzC,YAAI,CAAC,QAAQ,SAAS,cAAc,MAAM,EAAE,SAAS,iBAAiB,GAAG;AACvE,iBAAO,CAAC;AAAA,QACV;AAUA,cAAM,aAAa,KAAK,cAAc;AACtC,cAAM,sBAAsB,eAAe;AAC3C,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI,mBAAmB,KAAK,QAAQ,KAAK,cAAc,KAAK,WAAW,sBAAsB,KAAK,WAAW,QAAW,sBAAsB,KAAK,WAAW,QAAW,KAAK,kBAAkB,KAAK,kBAAkB;AACvN,eAAO,CAAC,KAAK,uBAAuB,SAAS,GAAG,KAAK,yBAAyB,WAAW,GAAG,KAAK,4BAA4B,aAAa,CAAC;AAAA,MAC7I;AAAA,MACA,uBAAuB,WAAW;AAChC,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,UAAU,WAAW,EAAG,QAAO,CAAC;AACpC,cAAM,aAAa,KAAK,2BAA2B;AACnD,eAAO,EAAE,qBAAqB;AAAA,UAC5B,cAAc;AAAA,UACd,OAAO,KAAK;AAAA,UACZ;AAAA,UACA,OAAO,WAAW;AAAA,UAClB,cAAc;AAAA,UACd,aAAa,QAAM;AACjB,iBAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG;AAAA,cAClE,MAAM,GAAG,OAAO;AAAA,YAClB,CAAC,CAAC;AACF,iBAAK,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,2BAA2B,CAAC,GAAG;AAAA,cACtF,MAAM,GAAG,OAAO;AAAA,YAClB,CAAC,CAAC;AACF,eAAG,gBAAgB;AAAA,UACrB;AAAA,QACF,GAAG,UAAU,IAAI,UAAQ,EAAE,4BAA4B;AAAA,UACrD,MAAM,KAAK,UAAU,WAAW,OAAO,GAAG,eAAe,IAAI,sBAAsB,KAAK;AAAA,UACxF,KAAK,KAAK;AAAA,UACV,UAAU,KAAK;AAAA,UACf,OAAO,KAAK;AAAA,QACd,GAAG,KAAK,IAAI,CAAC,CAAC;AAAA,MAChB;AAAA,MACA,yBAAyB,aAAa;AACpC,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,YAAY,WAAW,EAAG,QAAO,CAAC;AACtC,cAAM,aAAa,KAAK,2BAA2B;AACnD,eAAO,EAAE,qBAAqB;AAAA,UAC5B,cAAc;AAAA,UACd,OAAO,KAAK;AAAA,UACZ;AAAA,UACA,OAAO,WAAW;AAAA,UAClB,cAAc;AAAA,UACd,aAAa,QAAM;AACjB,iBAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG;AAAA,cAClE,QAAQ,GAAG,OAAO;AAAA,YACpB,CAAC,CAAC;AACF,iBAAK,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,2BAA2B,CAAC,GAAG;AAAA,cACtF,QAAQ,GAAG,OAAO;AAAA,YACpB,CAAC,CAAC;AACF,eAAG,gBAAgB;AAAA,UACrB;AAAA,QACF,GAAG,YAAY,IAAI,YAAU,EAAE,4BAA4B;AAAA,UACzD,MAAM,OAAO,UAAU,WAAW,SAAS,GAAG,eAAe,IAAI,sBAAsB,KAAK;AAAA,UAC5F,KAAK,OAAO;AAAA,UACZ,UAAU,OAAO;AAAA,UACjB,OAAO,OAAO;AAAA,QAChB,GAAG,OAAO,IAAI,CAAC,CAAC;AAAA,MAClB;AAAA,MACA,4BAA4B,eAAe;AACzC,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,cAAc,WAAW,GAAG;AAC9B,iBAAO,CAAC;AAAA,QACV;AACA,cAAM,aAAa,KAAK,2BAA2B;AACnD,cAAM,iBAAiB,qBAAqB,KAAK,MAAM;AACvD,eAAO,EAAE,qBAAqB;AAAA,UAC5B,cAAc;AAAA,UACd,OAAO,iBAAiB;AAAA,YACtB,OAAO;AAAA,UACT,IAAI,CAAC;AAAA,UACL,OAAO,KAAK;AAAA,UACZ;AAAA,UACA,OAAO,WAAW;AAAA,UAClB,aAAa,QAAM;AACjB,kBAAM,OAAO,sBAAsB,cAAc,GAAG,OAAO,KAAK;AAChE,iBAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG;AAAA,cAClE,MAAM,GAAG,OAAO;AAAA,cAChB;AAAA,YACF,CAAC,CAAC;AACF,iBAAK,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,2BAA2B,CAAC,GAAG;AAAA,cACtF,MAAM,GAAG,OAAO;AAAA,cAChB;AAAA,YACF,CAAC,CAAC;AACF,eAAG,gBAAgB;AAAA,UACrB;AAAA,QACF,GAAG,cAAc,IAAI,eAAa,EAAE,4BAA4B;AAAA,UAC9D,MAAM,UAAU,UAAU,WAAW,OAAO,GAAG,eAAe,IAAI,sBAAsB,KAAK;AAAA,UAC7F,KAAK,UAAU;AAAA,UACf,UAAU,UAAU;AAAA,UACpB,OAAO,UAAU;AAAA,QACnB,GAAG,UAAU,IAAI,CAAC,CAAC;AAAA,MACrB;AAAA,MACA,gBAAgB,mBAAmB;AACjC,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,cAAM,iBAAiB,mBAAmB,MAAM;AAChD,cAAM,cAAc,iBAAiB,gBAAgB;AACrD,eAAO,EAAE,OAAO;AAAA,UACd,OAAO;AAAA,YACL,CAAC,eAAe,WAAW,EAAE,GAAG;AAAA,UAClC;AAAA,QACF,GAAG,KAAK,kBAAkB,iBAAiB,CAAC;AAAA,MAC9C;AAAA;AAAA;AAAA;AAAA,MAIA,qBAAqB,MAAM;AACzB,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,cAAM,eAAe,SAAS,QAAQ,cAAc;AACpD,cAAM,gBAAgB,SAAS,QAAQ,iBAAiB;AACxD,cAAM,oBAAoB,YAAY,oBAAoB,KAAK,cAAc,KAAK,UAAU,KAAK,QAAQ;AACzG,cAAM,oBAAoB,YAAY,oBAAoB,KAAK,cAAc,KAAK,QAAQ;AAE1F,cAAM,UAAU,KAAK,GAAG,aAAa,KAAK,KAAK;AAC/C,eAAO,EAAE,OAAO;AAAA,UACd,OAAO;AAAA,QACT,GAAG,EAAE,OAAO;AAAA,UACV,OAAO;AAAA,QACT,GAAG,EAAE,OAAO;AAAA,UACV,OAAO;AAAA,QACT,GAAG,EAAE,UAAU;AAAA,UACb,OAAO;AAAA,YACL,8BAA8B;AAAA,YAC9B,mBAAmB;AAAA,YACnB,iBAAiB;AAAA,UACnB;AAAA,UACA,MAAM;AAAA,UACN;AAAA,UACA,cAAc,KAAK,mBAAmB,qBAAqB;AAAA,UAC3D,SAAS,MAAM,KAAK,uBAAuB;AAAA,QAC7C,GAAG,EAAE,QAAQ;AAAA,UACX,IAAI;AAAA,QACN,GAAG,gBAAgB,KAAK,QAAQ,KAAK,YAAY,GAAG,EAAE,YAAY;AAAA,UAChE,eAAe;AAAA,UACf,MAAM,KAAK,mBAAmB,eAAe;AAAA,UAC7C,MAAM;AAAA,UACN,SAAS;AAAA,QACX,CAAC,CAAC,GAAG,SAAS,QAAQ,EAAE,qBAAqB,IAAI,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,UAC7D,OAAO;AAAA,QACT,GAAG,EAAE,eAAe,MAAM,EAAE,cAAc;AAAA,UACxC,cAAc;AAAA,UACd,UAAU;AAAA,UACV,SAAS,MAAM,KAAK,UAAU;AAAA,QAChC,GAAG,EAAE,YAAY;AAAA,UACf,KAAK;AAAA,UACL,eAAe;AAAA,UACf,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACX,CAAC,CAAC,GAAG,EAAE,cAAc;AAAA,UACnB,cAAc;AAAA,UACd,UAAU;AAAA,UACV,SAAS,MAAM,KAAK,UAAU;AAAA,QAChC,GAAG,EAAE,YAAY;AAAA,UACf,KAAK;AAAA,UACL,eAAe;AAAA,UACf,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACX,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,UACf,OAAO;AAAA,UACP,eAAe;AAAA,QACjB,GAAG,cAAc,KAAK,QAAQ,MAAM,KAAK,iBAAiB,CAAC,EAAE,IAAI,OAAK;AACpE,iBAAO,EAAE,OAAO;AAAA,YACd,OAAO;AAAA,UACT,GAAG,CAAC;AAAA,QACN,CAAC,CAAC,CAAC;AAAA,MACL;AAAA,MACA,YAAY,OAAO,MAAM;AACvB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,cAAc,KAAK,qBAAqB,UAAa,KAAK,iBAAiB,SAAS,IAAI;AAC9F,cAAM,eAAe,KAAK,sBAAsB,UAAa,KAAK,kBAAkB,SAAS,KAAK;AAClG,cAAM,qBAAqB,CAAC,eAAe,CAAC;AAC5C,cAAM,qBAAqB,YAAY;AACvC,cAAM,gBAAgB,YAAY,gBAAgB;AAAA,UAChD;AAAA,UACA;AAAA,UACA,KAAK;AAAA,QACP,GAAG;AAAA;AAAA;AAAA;AAAA,UAID,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,QAAQ,GAAG;AAAA,YACxD,KAAK;AAAA,UACP,CAAC;AAAA,UACD,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,QAAQ,GAAG;AAAA,YACxD,KAAK;AAAA,UACP,CAAC;AAAA,QACH,CAAC;AAID,cAAM,iBAAiB,KAAK,aAAa,UAAU,SAAS,KAAK,aAAa,SAAS;AACvF,cAAM,aAAa,KAAK,2BAA2B;AACnD,eAAO,EAAE,OAAO;AAAA,UACd,eAAe,CAAC,iBAAiB,SAAS;AAAA,UAC1C,OAAO;AAAA,YACL,kBAAkB;AAAA;AAAA,YAElB,2BAA2B,CAAC,kBAAkB;AAAA,UAChD;AAAA,QACF,GAAG,EAAE,OAAO;AAAA,UACV,OAAO;AAAA,QACT,GAAG,eAAe,OAAO,MAAM,KAAK,iBAAiB,CAAC,EAAE,IAAI,CAAC,YAAY,UAAU;AACjF,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM,iBAAiB;AAAA,YACrB;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,gBAAM,oBAAoB,QAAQ;AAClC,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,UAAUC;AAAA,YACV;AAAA,UACF,IAAI,oBAAoB,KAAK,QAAQ,gBAAgB,KAAK,aAAa,KAAK,YAAY,KAAK,UAAU,KAAK,UAAU,KAAK,eAAe;AAC1I,gBAAM,gBAAgB,iBAAiB,cAAc;AACrD,cAAI,mBAAmB,sBAAsBA;AAC7C,cAAI,CAAC,oBAAoB,kBAAkB,QAAW;AACpD,gBAAI;AAMF,iCAAmB,CAAC,cAAc,aAAa;AAAA,YACjD,SAAS,GAAG;AACV,4BAAc,uHAAuH,IAAI,CAAC;AAAA,YAC5I;AAAA,UACF;AAMA,gBAAM,sBAAsB,oBAAoB;AAChD,gBAAM,mBAAmB,oBAAoB;AAC7C,cAAI,YAAY;AAKhB,cAAI,qBAAqB,UAAa,CAAC,YAAY,QAAQ,MAAM;AAC/D,wBAAY,mBAAmB,kBAAkB,eAAe,EAAE;AAAA,UACpE;AACA,cAAI,YAAY;AAGhB,cAAI,CAAC,mBAAmB;AACtB,wBAAY,eAAe,WAAW,YAAY,EAAE,GAAG,UAAU,WAAW,EAAE,GAAG,mBAAmB,cAAc,EAAE;AAAA,UACtH;AACA,iBAAO,EAAE,OAAO;AAAA,YACd,OAAO;AAAA,UACT,GAAG,EAAE,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAOb,KAAK,CAAAC,QAAM;AACT,kBAAIA,KAAI;AACN,gBAAAA,IAAG,MAAM,YAAY,SAAS,GAAG,YAAY,UAAU,YAAY,EAAE,IAAI,WAAW;AACpF,gBAAAA,IAAG,MAAM,YAAY,oBAAoB,GAAG,YAAY,UAAU,kBAAkB,EAAE,IAAI,WAAW;AAAA,cACvG;AAAA,YACF;AAAA,YACA,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,cAAc;AAAA,YACd,aAAa;AAAA,YACb,cAAc;AAAA,YACd,oBAAoB;AAAA,YACpB,UAAU;AAAA,YACV,OAAO;AAAA,cACL,wBAAwB;AAAA,cACxB,gBAAgB;AAAA,cAChB,uBAAuB;AAAA,cACvB,4BAA4B;AAAA,cAC5B,sBAAsB;AAAA,YACxB;AAAA,YACA,MAAM;AAAA,YACN,eAAe,oBAAoB,SAAS;AAAA,YAC5C,iBAAiB;AAAA,YACjB,cAAc;AAAA,YACd,SAAS,MAAM;AACb,kBAAI,mBAAmB;AACrB;AAAA,cACF;AACA,mBAAK,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,YAAY,GAAG;AAAA,gBACvE;AAAA,gBACA;AAAA,gBACA;AAAA,cACF,CAAC,CAAC;AAEF,kBAAI,UAAU;AACZ,qBAAK,eAAe;AAAA,kBAClB;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF,GAAG,QAAQ;AAAA,cACb,OAAO;AACL,qBAAK,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG;AAAA,kBAC/D;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF,CAAC,CAAC;AAAA,cACJ;AAAA,YACF;AAAA,UACF,GAAG,IAAI,CAAC;AAAA,QACV,CAAC,CAAC,CAAC;AAAA,MACL;AAAA,MACA,qBAAqB;AACnB,eAAO,EAAE,OAAO;AAAA,UACd,OAAO;AAAA,UACP,KAAK,QAAM,KAAK,kBAAkB;AAAA,UAClC,UAAU;AAAA,QACZ,GAAG,eAAe,KAAK,cAAc,KAAK,eAAe,EAAE,IAAI,CAAC;AAAA,UAC9D;AAAA,UACA;AAAA,QACF,MAAM;AACJ,iBAAO,KAAK,YAAY,OAAO,IAAI;AAAA,QACrC,CAAC,CAAC;AAAA,MACJ;AAAA,MACA,eAAe,MAAM;AACnB,eAAO,EAAE,OAAO;AAAA,UACd,OAAO;AAAA,UACP,KAAK;AAAA,QACP,GAAG,KAAK,qBAAqB,IAAI,GAAG,KAAK,mBAAmB,CAAC;AAAA,MAC/D;AAAA,MACA,kBAAkB;AAChB,cAAM,sBAAsB,KAAK,GAAG,cAAc,qBAAqB,MAAM;AAC7E,YAAI,CAAC,uBAAuB,CAAC,KAAK,sBAAsB;AACtD;AAAA,QACF;AACA,eAAO,EAAE,QAAQ;AAAA,UACf,MAAM;AAAA,QACR,GAAG,MAAM;AAAA,MACX;AAAA,MACA,oBAAoB;AAClB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,oBAAoB,aAAa,QAAQ,SAAS;AACxD,cAAM,aAAa,KAAK,2BAA2B;AACnD,eAAO,CAAC,EAAE,OAAO;AAAA,UACf,OAAO;AAAA,QACT,GAAG,KAAK,gBAAgB,CAAC,GAAG,EAAE,UAAU;AAAA,UACtC,OAAO;AAAA,YACL,aAAa;AAAA,YACb,oBAAoB;AAAA,UACtB;AAAA,UACA,MAAM,cAAc,oBAAoB,YAAY,EAAE;AAAA,UACtD,iBAAiB;AAAA,UACjB,iBAAiB;AAAA,UACjB;AAAA,UACA,SAAS,CAAM,OAAM;AACnB,kBAAM;AAAA,cACJ;AAAA,YACF,IAAI;AACJ,gBAAI,YAAY;AACd,mBAAK,oBAAoB;AACzB,yBAAW,QAAQ,IAAI,YAAY,mBAAmB;AAAA,gBACpD,QAAQ;AAAA,kBACN,iBAAiB,GAAG;AAAA,gBACtB;AAAA,cACF,CAAC,CAAC;AACF,oBAAM,WAAW,cAAc;AAC/B,mBAAK,oBAAoB;AAAA,YAC3B;AAAA,UACF;AAAA,QACF,GAAG,iBAAiB,QAAQ,YAAY,mBAAmB,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,IAAI,CAAC,GAAG,EAAE,eAAe;AAAA,UAC9J,WAAW;AAAA,UACX,aAAa;AAAA,UACb,cAAc;AAAA,UACd,OAAO;AAAA,UACP,eAAe,QAAM;AASnB,kBAAM,OAAO,GAAG,OAAO,iBAAiB,mBAAmB;AAE3D,iBAAK,QAAQ,SAAO,IAAI,yBAAyB,CAAC;AAAA,UACpD;AAAA,UACA,OAAO;AAAA,YACL,cAAc;AAAA,YACd,eAAe;AAAA,UACjB;AAAA;AAAA;AAAA,UAGA,gBAAgB;AAAA,UAChB,KAAK,QAAM,KAAK,aAAa;AAAA,QAC/B,GAAG,KAAK,kBAAkB,MAAM,CAAC,CAAC;AAAA,MACpC;AAAA,MACA,4BAA4B;AAC1B,YAAI;AACJ,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,UAAU,MAAM,QAAQ,WAAW;AACzC,YAAI;AACJ,YAAI,YAAY,WAAW,YAAY,WAAW,GAAG;AACnD,uBAAa,GAAG,YAAY,MAAM;AAClC,cAAI,gCAAgC,QAAW;AAC7C,gBAAI;AACF,2BAAa,4BAA4B,iBAAiB,WAAW,CAAC;AAAA,YACxE,SAAS,GAAG;AACV,4BAAc,yEAAyE,CAAC;AAAA,YAC1F;AAAA,UACF;AAAA,QACF,OAAO;AAEL,uBAAa,qBAAqB,KAAK,QAAQ,KAAK,2BAA2B,IAAI,KAAK,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU,QAAQ,OAAO,SAAS,KAAK;AAAA,YACzM,SAAS;AAAA,YACT,OAAO;AAAA,YACP,KAAK;AAAA,UACP,CAAC;AAAA,QACH;AACA,eAAO;AAAA,MACT;AAAA,MACA,aAAa,qBAAqB,MAAM;AACtC,cAAM,kBAAkB,KAAK,GAAG,cAAc,gBAAgB,MAAM;AACpE,YAAI,CAAC,mBAAmB,CAAC,KAAK,kBAAkB;AAC9C;AAAA,QACF;AACA,eAAO,EAAE,OAAO;AAAA,UACd,OAAO;AAAA,QACT,GAAG,EAAE,OAAO;AAAA,UACV,OAAO;AAAA,QACT,GAAG,EAAE,QAAQ;AAAA,UACX,MAAM;AAAA,QACR,GAAG,aAAa,CAAC,GAAG,sBAAsB,EAAE,OAAO;AAAA,UACjD,OAAO;AAAA,QACT,GAAG,KAAK,0BAA0B,CAAC,CAAC;AAAA,MACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,aAAa;AACX,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,cAAM,uBAAuB,iBAAiB;AAC9C,eAAO,EAAE,OAAO;AAAA,UACd,OAAO;AAAA,QACT,GAAG,uBAAuB,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC;AAAA,MAC/E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,oCAAoC;AAClC,eAAO,EAAE,OAAO;AAAA,UACd,OAAO;AAAA,QACT,GAAG,KAAK,gBAAgB,YAAY,CAAC;AAAA,MACvC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,eAAe,MAAM;AACnB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AAKJ,cAAM,kBAAkB,iBAAiB,UAAU,iBAAiB,eAAe,iBAAiB;AACpG,YAAI,eAAe,iBAAiB;AAClC,iBAAO,CAAC,KAAK,aAAa,KAAK,GAAG,KAAK,gBAAgB,GAAG,KAAK,aAAa,CAAC;AAAA,QAC/E;AACA,gBAAQ,cAAc;AAAA,UACpB,KAAK;AACH,mBAAO,CAAC,KAAK,aAAa,GAAG,KAAK,eAAe,IAAI,GAAG,KAAK,kCAAkC,GAAG,KAAK,WAAW,GAAG,KAAK,aAAa,CAAC;AAAA,UAC1I,KAAK;AACH,mBAAO,CAAC,KAAK,aAAa,GAAG,KAAK,WAAW,GAAG,KAAK,eAAe,IAAI,GAAG,KAAK,kCAAkC,GAAG,KAAK,aAAa,CAAC;AAAA,UAC1I,KAAK;AACH,mBAAO,CAAC,KAAK,aAAa,KAAK,GAAG,KAAK,WAAW,GAAG,KAAK,aAAa,CAAC;AAAA,UAC1E,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,CAAC,KAAK,aAAa,KAAK,GAAG,KAAK,gBAAgB,GAAG,KAAK,aAAa,CAAC;AAAA,UAC/E;AACE,mBAAO,CAAC,KAAK,aAAa,GAAG,KAAK,eAAe,IAAI,GAAG,KAAK,kCAAkC,GAAG,KAAK,aAAa,CAAC;AAAA,QACzH;AAAA,MACF;AAAA,MACA,SAAS;AACP,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,OAAO,WAAW,IAAI;AAC5B,cAAM,6BAA6B,iBAAiB,UAAU,iBAAiB,WAAW,iBAAiB;AAC3G,cAAM,yBAAyB,oBAAoB;AACnD,cAAM,sBAAsB,oBAAoB,CAAC;AACjD,cAAM,sBAAsB,iBAAiB,UAAU,iBAAiB,eAAe,iBAAiB;AACxG,cAAM,kBAAkB,uBAAuB;AAC/C,0BAAkB,MAAM,IAAI,MAAM,YAAY,KAAK,GAAG,QAAQ;AAC9D,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,iBAAiB,WAAW,SAAS;AAAA,UACrC,SAAS,KAAK;AAAA,UACd,QAAQ,KAAK;AAAA,UACb,OAAO,OAAO,OAAO,CAAC,GAAG,mBAAmB,OAAO;AAAA,YACjD,CAAC,IAAI,GAAG;AAAA,YACR,CAAC,mBAAmB,GAAG;AAAA,YACvB,CAAC,mBAAmB,GAAG;AAAA,YACvB,uBAAuB;AAAA,YACvB,0BAA0B;AAAA,YAC1B,CAAC,yBAAyB,YAAY,EAAE,GAAG;AAAA,YAC3C,CAAC,iBAAiB,IAAI,EAAE,GAAG;AAAA,YAC3B,CAAC,uBAAuB,GAAG;AAAA,YAC3B,CAAC,eAAe,GAAG;AAAA,UACrB,CAAC,CAAC;AAAA,QACJ,GAAG,EAAE,OAAO;AAAA,UACV,KAAK;AAAA,UACL,OAAO;AAAA,UACP,KAAK,CAAAA,QAAM,KAAK,yBAAyBA;AAAA,QAC3C,CAAC,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,MAC/B;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,iBAAiB,CAAC,sBAAsB;AAAA,UACxC,YAAY,CAAC,iBAAiB;AAAA,UAC9B,OAAO,CAAC,YAAY;AAAA,UACpB,OAAO,CAAC,YAAY;AAAA,UACpB,gBAAgB,CAAC,qBAAqB;AAAA,UACtC,cAAc,CAAC,mBAAmB;AAAA,UAClC,eAAe,CAAC,oBAAoB;AAAA,UACpC,aAAa,CAAC,kBAAkB;AAAA,UAChC,cAAc,CAAC,mBAAmB;AAAA,UAClC,gBAAgB,CAAC,qBAAqB;AAAA,UACtC,SAAS,CAAC,cAAc;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,IAAI,cAAc;AAClB,IAAM,cAAc;AACpB,IAAM,eAAe;AACrB,IAAM,kBAAkB;AACxB,IAAM,yBAAyB;AAC/B,aAAS,QAAQ;AAAA,MACf,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AAKA,IAAM,oBAAoB,YAAU;AAClC,YAAM,gBAAgB,gBAAgB;AACtC,YAAM,oBAAoB,gBAAgB;AAC1C,YAAM,mBAAmB,gBAAgB;AACzC,wBAAkB,WAAW,OAAO,cAAc,cAAc,CAAC,EAAE,OAAO,WAAW,MAAM,yBAAyB,EAAE,aAAa;AAAA,QACjI,kBAAkB;AAAA,MACpB,CAAC,EAAE,iBAAiB,CAAC,gBAAgB,CAAC;AACtC,uBAAiB,WAAW,OAAO,cAAc,iBAAiB,CAAC,EAAE,OAAO,aAAa,oBAAoB,gBAAgB;AAC7H,aAAO,cAAc,WAAW,MAAM,EAAE,OAAO,6BAA6B,EAAE,SAAS,GAAG,EAAE,aAAa,CAAC,mBAAmB,gBAAgB,CAAC;AAAA,IAChJ;AAKA,IAAM,oBAAoB,YAAU;AAClC,YAAM,gBAAgB,gBAAgB;AACtC,YAAM,oBAAoB,gBAAgB;AAC1C,YAAM,mBAAmB,gBAAgB;AACzC,wBAAkB,WAAW,OAAO,cAAc,cAAc,CAAC,EAAE,OAAO,WAAW,2BAA2B,IAAI;AACpH,uBAAiB,WAAW,OAAO,cAAc,iBAAiB,CAAC,EAAE,OAAO,aAAa,kBAAkB,kBAAkB;AAC7H,aAAO,cAAc,WAAW,MAAM,EAAE,OAAO,6BAA6B,EAAE,SAAS,GAAG,EAAE,aAAa,CAAC,mBAAmB,gBAAgB,CAAC;AAAA,IAChJ;AACA,IAAM,eAAe;AACrB,IAAM,2BAA2B;AACjC,IAAM,cAAc;AACpB,IAAM,0BAA0B;AAChC,IAAM,SAAS,MAAM;AAAA,MACnB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,aAAa,YAAY,MAAM,uBAAuB,CAAC;AAC5D,aAAK,cAAc,YAAY,MAAM,wBAAwB,CAAC;AAC9D,aAAK,cAAc,YAAY,MAAM,wBAAwB,CAAC;AAC9D,aAAK,aAAa,YAAY,MAAM,uBAAuB,CAAC;AAC5D,aAAK,sBAAsB,YAAY,MAAM,cAAc,CAAC;AAC5D,aAAK,uBAAuB,YAAY,MAAM,eAAe,CAAC;AAC9D,aAAK,uBAAuB,YAAY,MAAM,eAAe,CAAC;AAC9D,aAAK,sBAAsB,YAAY,MAAM,cAAc,CAAC;AAC5D,aAAK,qBAAqB,yBAAyB,IAAI;AACvD,aAAK,iBAAiB,qBAAqB;AAC3C,aAAK,oBAAoB,wBAAwB;AACjD,aAAK,gBAAgB,MAAM;AACzB,eAAK,QAAQ,QAAW,QAAQ;AAAA,QAClC;AACA,aAAK,wBAAwB,QAAM;AACjC,gBAAM,OAAO,GAAG,OAAO;AACvB,cAAI,SAAS,IAAI,GAAG;AAClB,kBAAM,eAAe,KAAK,QAAQ,KAAK,OAAK,EAAE,SAAS,QAAQ;AAC/D,iBAAK,kBAAkB,YAAY;AAAA,UACrC;AAAA,QACF;AACA,aAAK,YAAY;AACjB,aAAK,eAAe;AACpB,aAAK,WAAW;AAChB,aAAK,gBAAgB;AACrB,aAAK,gBAAgB;AACrB,aAAK,iBAAiB;AACtB,aAAK,iBAAiB;AACtB,aAAK,UAAU,CAAC;AAChB,aAAK,UAAU,CAAC;AAChB,aAAK,WAAW;AAChB,aAAK,WAAW;AAChB,aAAK,eAAe;AACpB,aAAK,kBAAkB;AACvB,aAAK,WAAW;AAChB,aAAK,iBAAiB;AACtB,aAAK,SAAS;AACd,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,eAAe,UAAU,UAAU;AACjC,YAAI,aAAa,QAAQ,aAAa,OAAO;AAC3C,eAAK,QAAQ;AAAA,QACf,WAAW,aAAa,SAAS,aAAa,MAAM;AAClD,eAAK,QAAQ;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AACf,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,SAAS;AACX,4BAAkB,iBAAiB,IAAI,OAAO;AAAA,QAChD;AAAA,MACF;AAAA,MACA,oBAAoB;AAClB,uBAAe,KAAK,EAAE;AACtB,aAAK,eAAe;AAAA,MACtB;AAAA,MACA,uBAAuB;AACrB,aAAK,kBAAkB,oBAAoB;AAAA,MAC7C;AAAA,MACA,oBAAoB;AAClB,YAAI;AACJ,YAAI,GAAG,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAC5E,uBAAa,KAAK,EAAE;AAAA,QACtB;AAAA,MACF;AAAA,MACA,mBAAmB;AACjB,wBAAgB,6RAA6R,KAAK,EAAE;AAKpT,YAAI,KAAK,WAAW,MAAM;AACxB,cAAI,MAAM,KAAK,QAAQ,CAAC;AAAA,QAC1B;AAUA,aAAK,eAAe;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA,MAIM,UAAU;AAAA;AACd,gBAAM,SAAS,MAAM,KAAK,eAAe,KAAK;AAC9C,gBAAM,KAAK,mBAAmB,gBAAgB;AAC9C,gBAAM,QAAQ,MAAM,eAAe,mBAAmB,mBAAmB,MAAS;AAClF,cAAI,KAAK,WAAW,GAAG;AACrB,iBAAK,kBAAkB,WAAW,MAAM,KAAK,QAAQ,GAAG,KAAK,QAAQ;AAAA,UACvE;AACA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUM,QAAQ,MAAM,MAAM;AAAA;AACxB,gBAAM,SAAS,MAAM,KAAK,eAAe,KAAK;AAC9C,cAAI,KAAK,iBAAiB;AACxB,yBAAa,KAAK,eAAe;AAAA,UACnC;AACA,gBAAM,YAAY,MAAM,QAAQ,MAAM,MAAM,MAAM,eAAe,mBAAmB,iBAAiB;AACrG,cAAI,WAAW;AACb,iBAAK,mBAAmB,kBAAkB;AAAA,UAC5C;AACA,iBAAO;AACP,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAIA,eAAe;AACb,eAAO,YAAY,KAAK,IAAI,qBAAqB;AAAA,MACnD;AAAA;AAAA;AAAA;AAAA,MAIA,gBAAgB;AACd,eAAO,YAAY,KAAK,IAAI,sBAAsB;AAAA,MACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,UAAU,MAAM;AACd,eAAO,QAAQ,QAAQ,KAAK,QAAQ,KAAK,YAAU,OAAO,SAAS,IAAI,CAAC;AAAA,MAC1E;AAAA,MACM,YAAY,QAAQ;AAAA;AACxB,gBAAM,OAAO,OAAO;AACpB,cAAI,SAAS,IAAI,GAAG;AAClB,mBAAO,KAAK,QAAQ,QAAW,IAAI;AAAA,UACrC;AACA,gBAAM,gBAAgB,MAAM,KAAK,kBAAkB,MAAM;AACzD,cAAI,eAAe;AACjB,mBAAO,KAAK,QAAQ,KAAK,YAAY,GAAG,OAAO,IAAI;AAAA,UACrD;AACA,iBAAO,QAAQ,QAAQ;AAAA,QACzB;AAAA;AAAA,MACM,kBAAkB,QAAQ;AAAA;AAC9B,cAAI,QAAQ;AAGV,kBAAM,MAAM,MAAM,SAAS,OAAO,SAAS,KAAK,YAAY,CAAC;AAC7D,gBAAI,QAAQ,OAAO;AAEjB,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA;AAAA,MACA,cAAc;AACZ,cAAM,WAAW,CAAC;AAClB,aAAK,QAAQ,QAAQ,CAAC,KAAK,UAAU;AACnC,gBAAM,iBAAiB,IAAI,kBAAkB,SAAY,IAAI,QAAQ,IAAI,aAAa,IAAI;AAC1F,mBAAS,IAAI,IAAI,IAAI;AAAA,YACnB,MAAM,iBAAiB,eAAe,OAAO;AAAA,YAC7C,OAAO,iBAAiB,eAAe,QAAQ;AAAA,YAC/C,aAAa;AAAA,UACf;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT;AAAA,MACA,SAAS;AACP,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,cAAM,OAAO,WAAW,IAAI;AAC5B,eAAO,EAAE,MAAM,OAAO,OAAO;AAAA,UAC3B,KAAK;AAAA,UACL,cAAc;AAAA,UACd,UAAU;AAAA,QACZ,GAAG,gBAAgB;AAAA,UACjB,OAAO;AAAA,YACL,QAAQ,GAAG,MAAQ,KAAK,YAAY;AAAA,UACtC;AAAA,UACA,OAAO,OAAO,OAAO;AAAA,YACnB,CAAC,IAAI,GAAG;AAAA;AAAA,YAER,CAAC,UAAU,IAAI,EAAE,GAAG;AAAA,YACpB,kBAAkB;AAAA,UACpB,GAAG,YAAY,KAAK,QAAQ,CAAC;AAAA,UAC7B,kBAAkB,KAAK;AAAA,UACvB,wBAAwB,KAAK;AAAA,QAC/B,CAAC,GAAG,EAAE,gBAAgB;AAAA,UACpB,KAAK;AAAA,UACL,SAAS,KAAK;AAAA,UACd,UAAU,KAAK;AAAA,QACjB,CAAC,GAAG,EAAE,OAAO;AAAA,UACX,KAAK;AAAA,UACL,UAAU;AAAA,UACV,eAAe;AAAA,QACjB,CAAC,GAAG,EAAE,OAAO;AAAA,UACX,KAAK;AAAA,UACL,OAAO;AAAA,UACP,MAAM;AAAA,QACR,GAAG,EAAE,OAAO;AAAA,UACV,KAAK;AAAA,UACL,OAAO;AAAA,QACT,GAAG,KAAK,QAAQ,IAAI,OAAK,EAAE,OAAO;AAAA,UAChC,OAAO,mBAAmB,CAAC;AAAA,QAC7B,GAAG,EAAE,UAAU;AAAA,UACb,MAAM;AAAA,UACN,SAAS,MAAM,KAAK,YAAY,CAAC;AAAA,UACjC,OAAO,YAAY,CAAC;AAAA,QACtB,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,UACtB,KAAK;AAAA,UACL,OAAO;AAAA,QACT,GAAG,EAAE,OAAO;AAAA,UACV,KAAK;AAAA,UACL,OAAO;AAAA,QACT,CAAC,GAAG,KAAK,aAAa,KAAK,QAAQ,IAAI,OAAK,EAAE,4BAA4B;AAAA,UACxE,KAAK;AAAA,QACP,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,UACZ,KAAK;AAAA,UACL,OAAO;AAAA,QACT,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,UACb,KAAK;AAAA,UACL,UAAU;AAAA,UACV,eAAe;AAAA,QACjB,CAAC,CAAC;AAAA,MACJ;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,UAAU,CAAC,gBAAgB;AAAA,UAC3B,WAAW,CAAC,gBAAgB;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AACA,IAAM,qBAAqB,YAAU;AACnC,aAAO;AAAA,QACL,CAAC,kBAAkB,OAAO,IAAI,EAAE,GAAG,OAAO,SAAS;AAAA,QACnD,yBAAyB;AAAA,MAC3B;AAAA,IACF;AACA,IAAM,cAAc,YAAU;AAC5B,aAAO,OAAO,OAAO;AAAA,QACnB,iBAAiB;AAAA,QACjB,mBAAmB;AAAA,MACrB,GAAG,YAAY,OAAO,QAAQ,CAAC;AAAA,IACjC;AACA,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AACA,IAAM,qBAAqB;AAC3B,IAAM,iCAAiC;AACvC,IAAM,oBAAoB;AAC1B,IAAM,gCAAgC;AACtC,IAAM,kBAAkB,MAAM;AAAA,MAC5B,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,qBAAqB,YAAY,MAAM,sBAAsB,CAAC;AACnE,aAAK,YAAY;AACjB,aAAK,eAAe;AACpB,aAAK,cAAc;AACnB,aAAK,WAAW;AAChB,aAAK,IAAI;AACT,aAAK,YAAY;AAQjB,aAAK,eAAe;AACpB,aAAK,MAAM;AAAA,MACb;AAAA,MACA,aAAa;AACX,aAAK,eAAe;AAAA,MACtB;AAAA,MACM,oBAAoB;AAAA;AACxB,cAAI,qBAAqB;AACzB,cAAI,oBAAoB;AACxB,gBAAM,OAAO,WAAW,IAAI;AAC5B,cAAI,SAAS,OAAO;AAClB,iCAAqB;AACrB,gCAAoB;AAAA,UACtB;AACA,eAAK,eAAe;AACpB,eAAK,cAAc;AACnB,eAAK,WAAW,MAAM,OAAO,8BAAqB,GAAG,cAAc;AAAA,YACjE,IAAI,KAAK;AAAA,YACT,aAAa;AAAA,YACb,iBAAiB;AAAA,YACjB,WAAW;AAAA,YACX,SAAS;AAAA,YACT,SAAS,QAAM,KAAK,QAAQ,EAAE;AAAA,YAC9B,QAAQ,QAAM,KAAK,OAAO,EAAE;AAAA,YAC5B,OAAO,QAAM,KAAK,MAAM,EAAE;AAAA,UAC5B,CAAC;AACD,eAAK,QAAQ,OAAO;AAKpB,eAAK,QAAQ,WAAW,MAAM;AAC5B,iBAAK,YAAY;AAIjB,iBAAK,QAAQ,IAAI;AAAA,UACnB,GAAG,GAAG;AAAA,QACR;AAAA;AAAA,MACA,mBAAmB;AACjB,aAAK,YAAY;AAAA,MACnB;AAAA,MACA,qBAAqB;AAEnB,YAAI,KAAK,cAAc;AAIrB,eAAK,YAAY,MAAM,KAAK;AAC5B,eAAK,eAAe;AAAA,QACtB;AAAA,MACF;AAAA,MACA,uBAAuB;AACrB,YAAI,KAAK,UAAU,OAAW,sBAAqB,KAAK,KAAK;AAC7D,YAAI,KAAK,MAAO,cAAa,KAAK,KAAK;AACvC,YAAI,KAAK,SAAS;AAChB,eAAK,QAAQ,QAAQ;AACrB,eAAK,UAAU;AAAA,QACjB;AAAA,MACF;AAAA,MACA,gBAAgB;AACd,aAAK,mBAAmB,KAAK,KAAK,GAAG;AAAA,MACvC;AAAA,MACA,YAAY,eAAe,UAAU;AAGnC,cAAM,IAAI,gBAAgB,KAAK,EAAE,gBAAgB,KAAK,aAAa;AACnE,aAAK,WAAW;AAEhB,YAAI,KAAK,UAAU,OAAW,sBAAqB,KAAK,KAAK;AAC7D,aAAK,OAAO,GAAG,UAAU,IAAI;AAC7B,aAAK,cAAc;AAAA,MACrB;AAAA,MACA,OAAO,GAAG,UAAU,OAAO;AACzB,YAAI,CAAC,KAAK,QAAQ;AAChB;AAAA,QACF;AAEA,YAAI,aAAa;AACjB,YAAI,aAAa;AACjB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,eAAe,IAAI;AACzB,cAAM,gBAAgB,IAAI,gBAAgB,KAAK,UAAU,CAAC,CAAC;AAC3D,cAAM,cAAc,aAAa,IAAI,KAAK,WAAW;AACrD,cAAM,WAAW,SAAS,KAAK,WAAW;AAC1C,cAAM,WAAW,KAAK,OAAO;AAC7B,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,gBAAM,SAAS,SAAS,CAAC;AACzB,gBAAM,MAAM,IAAI,QAAQ,CAAC;AACzB,gBAAM,YAAY,IAAI,KAAK,YAAY;AACvC,cAAI,YAAY;AAChB,cAAI,iBAAiB,GAAG;AACtB,kBAAM,UAAU,YAAY;AAC5B,gBAAI,KAAK,IAAI,OAAO,KAAK,IAAI;AAC3B,2BAAa;AACb,2BAAa;AACb,0BAAY,WAAW,OAAO;AAAA,YAChC,OAAO;AACL,2BAAa;AAAA,YACf;AAAA,UACF,OAAO;AACL,yBAAa;AACb,yBAAa;AAAA,UACf;AACA,gBAAM,WAAW,kBAAkB;AACnC,uBAAa,mBAAmB,UAAU,MAAM,UAAU;AAC1D,cAAI,KAAK,gBAAgB,KAAK,CAAC,UAAU;AACvC,yBAAa;AAAA,UACf;AAEA,cAAI,KAAK,WAAW;AAClB,gBAAI,WAAW;AACf,mBAAO,MAAM,qBAAqB;AAAA,UACpC,WAAW,aAAa,IAAI,UAAU;AACpC,gBAAI,WAAW;AACf,mBAAO,MAAM,qBAAqB;AAAA,UACpC;AAEA,cAAI,cAAc,IAAI,WAAW;AAC/B,gBAAI,YAAY;AAAA,UAClB;AACA,iBAAO,MAAM,YAAY;AAKzB,cAAI,WAAW;AACf,cAAI,UAAU;AACZ,mBAAO,UAAU,IAAI,mBAAmB;AAAA,UAC1C,OAAO;AACL,mBAAO,UAAU,OAAO,mBAAmB;AAAA,UAC7C;AAAA,QACF;AACA,aAAK,IAAI,eAAe;AACxB,YAAI,OAAO;AACT,eAAK,IAAI;AAAA,QACX;AACA,YAAI,KAAK,cAAc,eAAe;AAEpC,iCAAuB;AACvB,eAAK,YAAY;AAAA,QACnB;AAAA,MACF;AAAA,MACA,aAAa;AACX,YAAI,KAAK,aAAa,GAAG;AAEvB,eAAK,YAAY;AAEjB,eAAK,WAAW,KAAK,WAAW,IAAI,KAAK,IAAI,KAAK,UAAU,CAAC,IAAI,KAAK,IAAI,KAAK,UAAU,EAAE;AAC3F,cAAI,IAAI,KAAK,IAAI,KAAK;AACtB,cAAI,IAAI,KAAK,MAAM;AAEjB,gBAAI,KAAK;AACT,iBAAK,WAAW;AAAA,UAClB,WAAW,IAAI,KAAK,MAAM;AAExB,gBAAI,KAAK;AACT,iBAAK,WAAW;AAAA,UAClB;AACA,eAAK,OAAO,GAAG,GAAG,IAAI;AACtB,gBAAM,cAAc,KAAK,MAAM,CAAC,IAAI,KAAK,cAAc,KAAK,KAAK,IAAI,KAAK,QAAQ,IAAI;AACtF,cAAI,aAAa;AAEf,iBAAK,QAAQ,sBAAsB,MAAM,KAAK,WAAW,CAAC;AAAA,UAC5D,OAAO;AACL,iBAAK,WAAW;AAChB,iBAAK,cAAc;AACnB,+BAAmB;AAAA,UACrB;AAAA,QACF,WAAW,KAAK,IAAI,KAAK,cAAc,GAAG;AAExC,gBAAM,aAAa,KAAK,IAAI,KAAK,IAAI,KAAK,SAAS;AAEnD,eAAK,WAAW,aAAa,KAAK,YAAY,IAAI,IAAI;AACtD,eAAK,WAAW;AAAA,QAClB;AAAA,MACF;AAAA,MACA,UAAU,GAAG;AACX,eAAO,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,MAAM,IAAI,KAAK,SAAS,CAAC,GAAG,CAAC,GAAG,KAAK,IAAI,QAAQ,SAAS,CAAC;AAAA,MACpG;AAAA,MACA,QAAQ,QAAQ;AAId,YAAI,OAAO,MAAM,YAAY;AAC3B,iBAAO,MAAM,eAAe;AAAA,QAC9B;AACA,eAAO,MAAM,gBAAgB;AAC7B,6BAAqB;AAErB,YAAI,KAAK,UAAU,OAAW,sBAAqB,KAAK,KAAK;AAC7D,cAAM,UAAU,KAAK,IAAI;AACzB,YAAI,OAAO,QAAQ,SAAS;AAC5B,YAAI,OAAO;AACX,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAI,CAAC,QAAQ,CAAC,EAAE,UAAU;AACxB,mBAAO,KAAK,IAAI,MAAM,CAAC;AACvB,mBAAO,KAAK,IAAI,MAAM,CAAC;AAAA,UACzB;AAAA,QACF;AACA,aAAK,OAAO,EAAE,OAAO,KAAK;AAC1B,aAAK,OAAO,EAAE,OAAO,KAAK;AAAA,MAC5B;AAAA,MACA,OAAO,QAAQ;AACb,YAAI,OAAO,MAAM,YAAY;AAC3B,iBAAO,MAAM,eAAe;AAAA,QAC9B;AACA,eAAO,MAAM,gBAAgB;AAE7B,YAAI,IAAI,KAAK,IAAI,OAAO;AACxB,YAAI,IAAI,KAAK,MAAM;AAEjB,cAAI,KAAK,IAAI,GAAG,GAAG;AACnB,eAAK,aAAa;AAAA,QACpB,WAAW,IAAI,KAAK,MAAM;AAExB,eAAK,KAAK,IAAI,KAAK,OAAO,GAAG,GAAG;AAChC,eAAK,aAAa;AAAA,QACpB,OAAO;AACL,eAAK,aAAa;AAAA,QACpB;AACA,aAAK,OAAO,GAAG,GAAG,KAAK;AAAA,MACzB;AAAA,MACA,MAAM,QAAQ;AACZ,YAAI,KAAK,aAAa,GAAG;AAEvB,eAAK,OAAO,KAAK,MAAM,KAAK,IAAI;AAChC,eAAK,cAAc;AACnB;AAAA,QACF,WAAW,KAAK,aAAa,GAAG;AAE9B,eAAK,OAAO,KAAK,MAAM,KAAK,IAAI;AAChC,eAAK,cAAc;AACnB;AAAA,QACF;AACA,aAAK,WAAW,MAAM,CAAC,kBAAkB,OAAO,YAAY,IAAI,gBAAgB;AAChF,YAAI,KAAK,aAAa,KAAK,OAAO,WAAW,GAAG;AAC9C,gBAAM,MAAM,OAAO,MAAM,OAAO,QAAQ,aAAa;AACrD,cAAI,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,aAAa,WAAW,GAAG;AAC3E,iBAAK,YAAY,SAAS,IAAI,aAAa,WAAW,GAAG,EAAE,GAAG,mBAAmB;AAAA,UACnF;AAAA,QACF,OAAO;AACL,eAAK,KAAK,OAAO;AACjB,cAAI,KAAK,IAAI,OAAO,SAAS,IAAI,MAAM;AACrC,kBAAM,gBAAgB,OAAO,SAAS;AACtC,kBAAM,oBAAoB,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,KAAK;AACnE,gBAAI,iBAAiB,oBAAoB,KAAK;AAC5C,mBAAK,WAAW,KAAK,IAAI,KAAK,QAAQ,IAAI;AAAA,YAC5C,WAAW,CAAC,iBAAiB,qBAAqB,KAAK;AACrD,mBAAK,WAAW,KAAK,IAAI,KAAK,QAAQ;AAAA,YACxC;AAAA,UACF;AACA,eAAK,WAAW;AAAA,QAClB;AAAA,MACF;AAAA,MACA,QAAQ,cAAc,UAAU;AAC9B,YAAI;AACJ,YAAI,MAAM,KAAK,IAAI,QAAQ,SAAS;AACpC,YAAI,MAAM;AACV,cAAM,UAAU,KAAK,IAAI;AACzB,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAI,CAAC,QAAQ,CAAC,EAAE,UAAU;AACxB,kBAAM,KAAK,IAAI,KAAK,CAAC;AACrB,kBAAM,KAAK,IAAI,KAAK,CAAC;AAAA,UACvB;AAAA,QACF;AAQA,YAAI,KAAK,aAAa,GAAG;AACvB;AAAA,QACF;AACA,cAAM,gBAAgB,MAAM,MAAM,KAAK,KAAK,IAAI,mBAAmB,QAAQ,OAAO,SAAS,KAAK,GAAG,GAAG;AACtG,YAAI,KAAK,IAAI,iBAAiB,iBAAiB,cAAc;AAC3D,gBAAM,IAAI,gBAAgB,KAAK,YAAY;AAC3C,gBAAM,WAAW,WAAW,sBAAsB;AAClD,eAAK,WAAW;AAChB,eAAK,OAAO,GAAG,UAAU,IAAI;AAAA,QAC/B;AAAA,MACF;AAAA,MACA,YAAY,cAAc,UAAU;AAClC,cAAM,QAAQ,KAAK;AACnB,YAAI,OAAO;AAGT,eAAK,YAAY,MAAM,oBAAoB,MAAM,kBAAkB,eAAe;AAAA,QACpF;AACA,aAAK,QAAQ,cAAc,QAAQ;AAAA,MACrC;AAAA,MACA,SAAS;AACP,cAAM,MAAM,KAAK;AACjB,cAAM,OAAO,WAAW,IAAI;AAC5B,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,OAAO,OAAO,OAAO;AAAA,YACnB,CAAC,IAAI,GAAG;AAAA,YACR,cAAc;AAAA,YACd,oBAAoB,KAAK,IAAI,UAAU;AAAA,YACvC,qBAAqB,KAAK,IAAI,UAAU;AAAA,UAC1C,GAAG,YAAY,IAAI,QAAQ,CAAC;AAAA,UAC5B,OAAO;AAAA,YACL,aAAa,KAAK,IAAI;AAAA,UACxB;AAAA,QACF,GAAG,IAAI,UAAU,EAAE,OAAO;AAAA,UACxB,KAAK;AAAA,UACL,OAAO;AAAA,UACP,OAAO;AAAA,YACL,OAAO,IAAI;AAAA,UACb;AAAA,QACF,GAAG,IAAI,MAAM,GAAG,EAAE,OAAO;AAAA,UACvB,KAAK;AAAA,UACL,OAAO;AAAA,UACP,OAAO;AAAA,YACL,UAAU,IAAI;AAAA,UAChB;AAAA,UACA,KAAK,QAAM,KAAK,SAAS;AAAA,QAC3B,GAAG,IAAI,QAAQ,IAAI,CAAC,GAAG,UAAU,EAAE,UAAU;AAAA,UAC3C,cAAc,EAAE;AAAA,UAChB,OAAO;AAAA,YACL,cAAc;AAAA,YACd,uBAAuB,CAAC,CAAC,EAAE;AAAA,UAC7B;AAAA,UACA,aAAa;AAAA,QACf,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,UAAU,EAAE,OAAO;AAAA,UACnC,KAAK;AAAA,UACL,OAAO;AAAA,UACP,OAAO;AAAA,YACL,OAAO,IAAI;AAAA,UACb;AAAA,QACF,GAAG,IAAI,MAAM,CAAC;AAAA,MAChB;AAAA,MACA,IAAI,KAAK;AACP,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,MACA,WAAW,WAAW;AACpB,eAAO;AAAA,UACL,OAAO,CAAC,YAAY;AAAA,QACtB;AAAA,MACF;AAAA,IACF;AACA,IAAM,sBAAsB;AAC5B,IAAM,wBAAwB;AAC9B,IAAM,mBAAmB;AACzB,IAAM,sBAAsB;AAC5B,oBAAgB,QAAQ;AAAA,MACtB,KAAK;AAAA,MACL,IAAI;AAAA,IACN;AAAA;AAAA;", "names": ["disabled", "isDayDisabled", "el"], "x_google_ignoreList": [0]}