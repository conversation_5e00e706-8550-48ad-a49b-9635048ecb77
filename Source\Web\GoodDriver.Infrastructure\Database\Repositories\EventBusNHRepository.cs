﻿using Microsoft.Extensions.Configuration;
using NHibernate;
using Rogerio.Data;
using Rogerio.Cqrs.Events;
using System.Data;

namespace GoodDriver.Infrastructure.Database.Repositories
{
    public class EventBusNHRepository<T> : Rogerio.Data.NHibernate.NHibernateRepository<T> where T : class, new()
    {
        private readonly IServiceProvider serviceProvider;



        public EventBusNHRepository(IConfiguration configuration, IUnitOfWorkFactory<ISession> unitOfWorkFactory, IServiceProvider serviceProvider)
              : base(configuration, unitOfWorkFactory)
        {
            if (serviceProvider == null) throw new ArgumentNullException("serviceProvider");
            this.serviceProvider = serviceProvider;
        }

        protected override async Task UpdateAsync<P>(P instance, bool autoFlush = true)
        {
            using (var scope = UnitOfWorkFactory.Get(true, IsolationLevel.ReadUncommitted))
            {
                await base.UpdateAsync<P>(instance, autoFlush);

                if (instance is IDomainEventRoot)
                    await PublishEvents(instance as IDomainEventRoot);

                scope.Complete();
            }
        }

        protected override async Task<object> AddAsync<P>(P instance, bool autoFlush = true)
        {
            using (var scope = UnitOfWorkFactory.Get(true, IsolationLevel.ReadUncommitted))
            {
                var result = await base.AddAsync<P>(instance, autoFlush);

                if (instance is IDomainEventRoot)
                    await PublishEvents(instance as IDomainEventRoot);

                scope.Complete();

                return result;
            }
        }

        protected async Task PublishEvents(IDomainEventRoot aggregate)
        {
            var bus = GetBus();
            var uncommitedEvents = aggregate.GetUncommittedEvents();
            var events = new List<IDomainEvent>();

            foreach (var @event in uncommitedEvents)
                events.Add(@event);

            aggregate.ClearUncommittedEvents();

            foreach (var @event in events)
                await bus.PublishAsync(@event);
        }

        private IEventBus GetBus()
        {
            return serviceProvider.GetService(typeof(IEventBus)) as IEventBus;
        }
    }
}
