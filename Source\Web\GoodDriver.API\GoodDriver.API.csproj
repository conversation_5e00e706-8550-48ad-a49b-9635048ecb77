<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FluentValidation" Version="11.11.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.15" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.4" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.8.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.8.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\GoodDriver.Infrastructure\GoodDriver.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Rogerio.Commom">
      <HintPath>..\..\..\..\RogerioFramework\RogerioIT\Rogerio.Commom\bin\Debug\net8.0\Rogerio.Commom.dll</HintPath>
    </Reference>
    <Reference Include="Rogerio.Cqrs">
      <HintPath>..\..\..\..\RogerioFramework\RogerioIT\Rogerio.Cqrs\bin\Debug\net8.0\Rogerio.Cqrs.dll</HintPath>
    </Reference>
    <Reference Include="Rogerio.Mvc">
      <HintPath>..\..\..\..\RogerioFramework\RogerioIT\Rogerio.Mvc\bin\Debug\net8.0\Rogerio.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="Rogerio.Security">
      <HintPath>..\..\..\..\RogerioFramework\RogerioIT\Rogerio.Security\bin\Debug\net8.0\Rogerio.Security.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
