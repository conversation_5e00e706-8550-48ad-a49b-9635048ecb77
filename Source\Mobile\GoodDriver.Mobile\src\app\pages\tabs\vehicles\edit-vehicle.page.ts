import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { BrandService } from 'src/app/core/services/brand.service';
import { ModelService } from 'src/app/core/services/model.service';
import { VehicleService } from 'src/app/core/services/vehicle.service';
import { ToastService } from 'src/app/core/services/toast.service';
import { Brand } from 'src/app/core/models/brand.model';
import { Model } from 'src/app/core/models/model.model';
import { SessionService } from 'src/app/core/services/session.service';

@Component({
  selector: 'app-edit-vehicle',
  templateUrl: './edit-vehicle.page.html',
  styleUrls: ['./edit-vehicle.page.scss']
})
export class EditVehiclePage implements OnInit {
  vehicleForm: FormGroup;
  brands: Brand[] = [];
  models: Model[] = [];
  vehicleId: string = '';
  isLoading = true;
  isSubmitting = false;
  userId: string = '';

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private brandService: BrandService,
    private modelService: ModelService,
    private vehicleService: VehicleService,
    private toastService: ToastService,
    private sessionService: SessionService
  ) {
    this.vehicleForm = this.fb.group({
      brand: [null, Validators.required],
      model: [null, Validators.required],
      version: [null],
      year: [null, [Validators.required, Validators.min(1990), Validators.max(2026)]],
      plate: [null, [Validators.required]],
      policy: [null]
    });
  }

  async ngOnInit() {
    this.vehicleId = this.route.snapshot.paramMap.get('id') || '';
    this.userId = await this.sessionService.getUserId() || '';
    await this.loadBrands();
    if (this.vehicleId) {
      await this.loadVehicleAndPatchForm();
    }
    this.isLoading = false;
  }

  async loadBrands() {
    this.brands = await this.brandService.listAll();
  }

  async loadVehicleAndPatchForm() {
    const vehicle = await this.vehicleService.getById(this.vehicleId);
    if (!vehicle) {
      this.toastService.showToast('Veículo não encontrado', 'danger');
      this.router.navigate(['/tabs/vehicles']);
      return;
    }
    this.vehicleForm.patchValue({
      brand: Number(vehicle.brandId),
      year: vehicle.year,
      plate: vehicle.plate,
      version: vehicle.version,
      policy: vehicle.policyNumber
    });
    await this.onBrandChange(Number(vehicle.brandId), Number(vehicle.modelId));
  }

  async onBrandChange(brandId: number, modelIdToSelect?: number) {
    this.vehicleForm.patchValue({ model: null });
    this.models = await this.modelService.getByBranch({ brandId });
    if (modelIdToSelect) {
      this.vehicleForm.patchValue({ model: modelIdToSelect });
    }
  }

  async onSubmit() {
    if (this.vehicleForm.invalid) return;
    this.isSubmitting = true;
    const formValues = this.vehicleForm.value;
    const selectedBrand = this.brands.find(b => b.id === formValues.brand);
    const selectedModel = this.models.find(m => m.id === formValues.model);
    const updatedVehicle = {
      id: this.vehicleId,
      userId: this.userId,
      brandId: formValues.brand,
      brandName: selectedBrand?.name || '',
      modelId: formValues.model,
      modelName: selectedModel?.name || '',
      plate: formValues.plate,
      year: formValues.year,
      version: formValues.version,
      policyNumber: formValues.policy
    };
    try {
      await this.vehicleService.update(this.vehicleId, updatedVehicle);
      this.toastService.showToast('Veículo atualizado com sucesso');
      this.router.navigate(['/tabs/vehicles']);
    } catch (error) {
      this.toastService.showToast('Erro ao atualizar veículo', 'danger');
    } finally {
      this.isSubmitting = false;
    }
  }
} 