﻿using FluentNHibernate.Mapping;
using GoodDriver.Domain.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Infrastructure.Database.Maps
{
	public class ModelMap : ClassMap<Model>
	{
        public ModelMap()
        {
			Id(u => u.Id).Column("Id").GeneratedBy.Identity();
			Map(a => a.Name).Not.Nullable();
            Map(a => a.ReferenceCode).Not.Nullable();
            Map(a => a.ReferenceMonth).Not.Nullable();
            Map(a => a.ShortName).Not.Nullable();

            Map(a => a.CreatedOn).Not.Nullable();
			Map(a => a.UpdatedOn).Nullable();
			References(a => a.Brand).Column("BrandId").Nullable().Fetch.Join().Cascade.None();
		}
    }
}
