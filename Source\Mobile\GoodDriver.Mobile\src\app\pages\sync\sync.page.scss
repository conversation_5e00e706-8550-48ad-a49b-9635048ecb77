ion-card {
  border-radius: var(--app-border-radius-md, 8px);
  box-shadow: var(--app-card-shadow, 0 2px 4px rgba(0, 0, 0, 0.1)) !important;
  margin: var(--app-spacing-md, 16px) var(--app-spacing-md, 16px) var(--app-spacing-lg, 24px);
  
  ion-card-header {
    padding: var(--app-spacing-md, 16px);
    
    ion-card-title {
      font-size: 1.25rem;
      font-weight: var(--app-heading-font-weight, 600);
      display: flex;
      align-items: center;
      
      ion-icon {
        margin-right: 8px;
        font-size: 1.5rem;
      }
    }
    
    ion-card-subtitle {
      font-size: 0.9rem;
      color: var(--ion-color-medium);
      margin-top: var(--app-spacing-xs, 4px);
    }
  }
  
  ion-card-content {
    padding: var(--app-spacing-md, 16px);
  }
}

ion-item {
  --padding-start: var(--app-spacing-md, 16px);
  --padding-end: var(--app-spacing-md, 16px);
  --inner-padding-end: 0;
  
  h2 {
    font-weight: 500;
    font-size: 1rem;
  }
  
  p {
    font-size: 0.9rem;
    color: var(--ion-color-medium);
  }
  
  ion-icon {
    font-size: 1.5rem;
  }
}

ion-button {
  margin: var(--app-spacing-md, 16px) 0 0;
  height: 48px;
  
  ion-icon {
    margin-right: 8px;
  }
}

.sync-status {
  margin: var(--app-spacing-md, 16px) 0;
}
