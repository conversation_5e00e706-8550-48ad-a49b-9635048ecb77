<ion-header [translucent]="true">
  <ion-toolbar color="primary">
    <ion-title>
      Home
    </ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="logout()" fill="clear" color="light">
        <span class="material-icons">logout</span>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>


<ion-content [fullscreen]="true" class="ion-padding">
  <!-- Indicador de carregamento -->
  <div *ngIf="isLoading" class="ion-text-center ion-padding">
    <ion-spinner></ion-spinner>
  </div>

  <div *ngIf="!isLoading">
    <ion-card>
      <ion-card-header>
        <ion-card-title class="ion-text-center">Bem-vindo, {{username}}!</ion-card-title>
      </ion-card-header>

      <ion-card-content>
        <p class="ion-text-center">
          Você está logado com sucesso no aplicativo.
        </p>
      </ion-card-content>
    </ion-card>

    <!-- Alerta de veículo não cadastrado -->
    <ion-card *ngIf="!hasVehicles" color="warning">
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="warning-outline"></ion-icon>
          Atenção
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <p>Você ainda não possui nenhum veículo cadastrado. Para utilizar o rastreamento de viagens, é necessário cadastrar pelo menos um veículo.</p>
        <ion-button expand="block" color="primary" routerLink="/new-vehicle" class="ion-margin-top">
          <ion-icon name="car-outline" slot="start"></ion-icon>
          Cadastrar Veículo
        </ion-button>
      </ion-card-content>
    </ion-card>

    <!-- Informações do veículo principal -->
    <ion-card *ngIf="hasVehicles && primaryVehicle">
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="car-outline"></ion-icon>
          Veículo Principal
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-item lines="none">
          <ion-label>
            <h2>{{ primaryVehicle.plate }}</h2>
            <p>{{ primaryVehicle.brandName }} {{ primaryVehicle.modelName }}</p>
            <p>Ano: {{ primaryVehicle.year }}</p>
          </ion-label>
        </ion-item>
        <ion-button expand="block" color="primary" routerLink="/tabs/vehicles" class="ion-margin-top">
          <ion-icon name="list-outline" slot="start"></ion-icon>
          Gerenciar Veículos
        </ion-button>
      </ion-card-content>
    </ion-card>

    <!-- Card de Sincronização -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="sync-outline"></ion-icon>
          Sincronização
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <p>Mantenha seus dados sincronizados entre o aplicativo e o servidor.</p>
        <ion-button expand="block" color="primary" routerLink="/tabs/sync" class="ion-margin-top">
          <ion-icon name="sync-outline" slot="start"></ion-icon>
          Ir para Sincronização
        </ion-button>
      </ion-card-content>
    </ion-card>
  </div>
</ion-content>