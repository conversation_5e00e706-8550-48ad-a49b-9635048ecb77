import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ListBrandResponseDto } from '../dtos/brand/list-brand-responseDto';
import { Brand } from '../models/brand.model';
import { ApiService } from './api.service';


@Injectable({
    providedIn: 'root'
  })
export class BrandService
{
    constructor(
      private http: HttpClient,
      private apiService: ApiService
    ) {}

    async listAll(): Promise<Brand[]> {
        try {
          const url = this.apiService.getUrl('brands');
          const result = await this.http.get<ListBrandResponseDto[]>(url).toPromise();

          if (!result) return [];

          // Aqui você pode usar o map na resposta real (os dados que você recebe)
          const brands: Brand[] = result.map((data: any) => {
            return {
              id: data.id,
              name: data.name,
              ico: data.ico
            };
          });

          return brands;
        } catch (error) {
          console.error('Error fetching brands:', error);
          return [];
        }
      }

}