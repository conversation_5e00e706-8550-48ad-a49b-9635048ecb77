import {
  WebPlugin,
  init_dist
} from "./chunk-WY354WYL.js";
import {
  __esm
} from "./chunk-4W6HR7MY.js";

// node_modules/@capacitor/motion/dist/esm/web.js
var MotionWeb;
var init_web = __esm({
  "node_modules/@capacitor/motion/dist/esm/web.js"() {
    init_dist();
    MotionWeb = class extends WebPlugin {
      constructor() {
        super();
        this.registerWindowListener("devicemotion", "accel");
        this.registerWindowListener("deviceorientation", "orientation");
      }
    };
  }
});
init_web();
export {
  MotionWeb
};
//# sourceMappingURL=web-B4UY27XM.js.map
