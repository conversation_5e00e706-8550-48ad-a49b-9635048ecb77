﻿using GoodDriver.API.Controllers.Users;
using GoodDriver.ReadModels.Brands.Queries;
using GoodDriver.ReadModels.Brands.Result;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Rogerio.Commom.Exceptions;
using Rogerio.Cqrs.Commands;
using Rogerio.Cqrs.Requests;
using Rogerio.Security.Domain;

namespace GoodDriver.API.Controllers
{
    [ApiController]
    [Route("api/brand")]
    public class BrandController : BaseControllerGoodDriver
    {
        private readonly ICommandBus commandBus;
        private readonly IConfiguration _config;
        private readonly IRequestBus requestBus;

        public BrandController(IConfiguration config, ILogger<UserController> logger, ISecurityManager securityManager, ICommandBus _commandBus, IRequestBus requestBus) : base(securityManager)
        {
            _config = config;
            commandBus = _commandBus;
            this.requestBus = requestBus;
        }

        [Authorize]
        [HttpGet("list")]
        public async Task<IActionResult> ListBrands()
        {
            try
            {
                var brands = await requestBus.RequestAsync<BrandListQuery, BrandListResult>(new BrandListQuery());

                //var response = new
                //{
                //    Success = true,
                //    Data = brands
                //};

                return Ok(brands);
            }
            catch (BusinessException ex)
            {
                return BadRequest(new { message = ex.Message, errorCode = ex.Key });
            }
            catch (Exception exception)
            {
                return BadRequest(new { message = exception.Message });
            }
        }
    }
}
