﻿using GoodDriver.SetupDomain.Cqrs.Domain;
using System.Reflection;

namespace GoodDriver.SetupDomain.Cqrs
{
    public class ConventionDomainEventRouter<I> : IRouteDomainEvents<I>
    {
        private readonly bool throwOnApplyNotFound;

        private readonly IDictionary<Type, Action<object>> handlers = new Dictionary<Type, Action<object>>();

        private IEntity<I> registered;

        public ConventionDomainEventRouter()
            : this(throwOnApplyNotFound: true)
        {
        }

        public ConventionDomainEventRouter(bool throwOnApplyNotFound)
        {
            this.throwOnApplyNotFound = throwOnApplyNotFound;
        }

        public ConventionDomainEventRouter(bool throwOnApplyNotFound, IEntity<I> aggregate)
            : this(throwOnApplyNotFound)
        {
            Register(aggregate);
        }

        public virtual void Register<T>(Action<T> handler)
        {
            if (handler == null)
            {
                throw new ArgumentNullException("handler");
            }

            Register(typeof(T), delegate (object @event)
            {
                handler((T)@event);
            });
        }

        public virtual void Register(IEntity<I> aggregate)
        {
            if (aggregate == null)
            {
                throw new ArgumentNullException("aggregate");
            }

            registered = aggregate;
            foreach (var item in from m in aggregate.GetType().GetMethods(BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic)
                                 where m.Name == "Apply" && m.GetParameters().Length == 1 && m.ReturnParameter.ParameterType == typeof(void)
                                 select new
                                 {
                                     Method = m,
                                     MessageType = m.GetParameters().Single().ParameterType
                                 })
            {
                MethodInfo applyMethod = item.Method;
                handlers.Add(item.MessageType, delegate (object m)
                {
                    applyMethod.Invoke(aggregate, new object[1] { m });
                });
            }
        }

        public virtual void Dispatch(IDomainEvent eventMessage)
        {
            if (eventMessage == null)
            {
                throw new ArgumentNullException("eventMessage");
            }

            if (handlers.TryGetValue(eventMessage.GetType(), out var value))
            {
                value(eventMessage);
            }
            else if (throwOnApplyNotFound)
            {
                throw new Exception("ThrowHandlerNotFound");
            }
        }

        private void Register(Type messageType, Action<object> handler)
        {
            handlers[messageType] = handler;
        }
    }
}