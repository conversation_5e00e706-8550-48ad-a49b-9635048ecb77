﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Migration.Data
{
    [FluentMigrator.Migration(20250502165600)]
    public class InsertModelVehicles : FluentMigrator.Migration
    {
        public override void Down()
        {
            if (Schema.Table("Model").Exists())
                Delete.Table("Model");
        }

        public override void Up()
        {
            if (Schema.Table("Model").Exists())
            {
                Execute.EmbeddedScript("202505021949_InsertModelVehicles.sql");
            }
        }
    }
}
