{"version": 3, "sources": ["src/app/pages/signup/signup.page.scss"], "sourcesContent": ["/* src/app/signup/signup.page.scss */\r\n.signup-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    height: 100%;\r\n  }\r\n  \r\n  ion-card {\r\n    margin: 0 auto;\r\n    max-width: 500px;\r\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\r\n  }\r\n  \r\n  .error-message {\r\n    color: var(--ion-color-danger);\r\n    font-size: small;\r\n    margin-left: 16px;\r\n    margin-top: 4px;\r\n  }"], "mappings": ";AACA,CAAA;AACI,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,UAAA;;AAGF;AACE,UAAA,EAAA;AACA,aAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,SAAA,IAAA;AACA,aAAA;AACA,eAAA;AACA,cAAA;;", "names": []}