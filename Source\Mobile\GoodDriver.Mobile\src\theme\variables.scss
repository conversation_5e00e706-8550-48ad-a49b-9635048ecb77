// Ionic Variables and Theming
// --------------------------------------------------
// For more information on theming, visit:
// http://ionicframework.com/docs/theming/

// Ionic Colors
// --------------------------------------------------
// Named Color Variables
// --------------------------------------------------
// Named colors makes it easy to reuse colors across your app.
// It's highly recommended to change the default colors
// to match your app's branding.

:root {
  // Primary brand color - Green for Good Driver
  --ion-color-primary: #2E7D32;
  --ion-color-primary-rgb: 46, 125, 50;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #286e2c;
  --ion-color-primary-tint: #438a47;

  // Secondary color - Darker green for contrast
  --ion-color-secondary: #1B5E20;
  --ion-color-secondary-rgb: 27, 94, 32;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #18531c;
  --ion-color-secondary-tint: #326e36;

  // Tertiary color
  --ion-color-tertiary: #5260ff;
  --ion-color-tertiary-rgb: 82, 96, 255;
  --ion-color-tertiary-contrast: #ffffff;
  --ion-color-tertiary-contrast-rgb: 255, 255, 255;
  --ion-color-tertiary-shade: #4854e0;
  --ion-color-tertiary-tint: #6370ff;

  // Success color
  --ion-color-success: #2dd36f;
  --ion-color-success-rgb: 45, 211, 111;
  --ion-color-success-contrast: #ffffff;
  --ion-color-success-contrast-rgb: 255, 255, 255;
  --ion-color-success-shade: #28ba62;
  --ion-color-success-tint: #42d77d;

  // Warning color
  --ion-color-warning: #ffc409;
  --ion-color-warning-rgb: 255, 196, 9;
  --ion-color-warning-contrast: #000000;
  --ion-color-warning-contrast-rgb: 0, 0, 0;
  --ion-color-warning-shade: #e0ac08;
  --ion-color-warning-tint: #ffca22;

  // Danger color
  --ion-color-danger: #eb445a;
  --ion-color-danger-rgb: 235, 68, 90;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #cf3c4f;
  --ion-color-danger-tint: #ed576b;

  // Dark color
  --ion-color-dark: #222428;
  --ion-color-dark-rgb: 34, 36, 40;
  --ion-color-dark-contrast: #ffffff;
  --ion-color-dark-contrast-rgb: 255, 255, 255;
  --ion-color-dark-shade: #1e2023;
  --ion-color-dark-tint: #383a3e;

  // Medium color
  --ion-color-medium: #92949c;
  --ion-color-medium-rgb: 146, 148, 156;
  --ion-color-medium-contrast: #ffffff;
  --ion-color-medium-contrast-rgb: 255, 255, 255;
  --ion-color-medium-shade: #808289;
  --ion-color-medium-tint: #9d9fa6;

  // Light color
  --ion-color-light: #f4f5f8;
  --ion-color-light-rgb: 244, 245, 248;
  --ion-color-light-contrast: #000000;
  --ion-color-light-contrast-rgb: 0, 0, 0;
  --ion-color-light-shade: #d7d8da;
  --ion-color-light-tint: #f5f6f9;

  // Custom app colors
  --app-background-color: #ffffff;
  --app-text-color: #333333;
  --app-border-color: #e0e0e0;
  --app-card-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  --app-input-background: #f9f9f9;
  --app-success-light: #e6f7ee;
  --app-warning-light: #fff8e6;
  --app-danger-light: #fde8eb;
  --app-primary-light: #e6f3ff;
}

// App specific variables
// --------------------------------------------------
:root {
  // Typography
  --app-font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
  --app-heading-font-weight: 500;
  --app-body-font-weight: 400;

  // Spacing
  --app-spacing-xs: 4px;
  --app-spacing-sm: 8px;
  --app-spacing-md: 16px;
  --app-spacing-lg: 24px;
  --app-spacing-xl: 32px;

  // Border radius
  --app-border-radius-sm: 4px;
  --app-border-radius-md: 8px;
  --app-border-radius-lg: 12px;

  // Transitions
  --app-transition-fast: 0.15s ease;
  --app-transition-normal: 0.25s ease;
  --app-transition-slow: 0.4s ease;
}

// Dark theme overrides
// --------------------------------------------------
@media (prefers-color-scheme: dark) {
  :root {
    --ion-color-primary: #4CAF50;
    --ion-color-primary-rgb: 76, 175, 80;
    --ion-color-primary-contrast: #ffffff;
    --ion-color-primary-contrast-rgb: 255, 255, 255;
    --ion-color-primary-shade: #439a46;
    --ion-color-primary-tint: #5eb762;

    --ion-color-secondary: #388E3C;
    --ion-color-secondary-rgb: 56, 142, 60;
    --ion-color-secondary-contrast: #ffffff;
    --ion-color-secondary-contrast-rgb: 255, 255, 255;
    --ion-color-secondary-shade: #317d35;
    --ion-color-secondary-tint: #4c9a50;

    --ion-color-tertiary: #6a75ff;
    --ion-color-tertiary-rgb: 106, 117, 255;
    --ion-color-tertiary-contrast: #ffffff;
    --ion-color-tertiary-contrast-rgb: 255, 255, 255;
    --ion-color-tertiary-shade: #5d67e0;
    --ion-color-tertiary-tint: #7983ff;

    --ion-color-success: #2fdf75;
    --ion-color-success-rgb: 47, 223, 117;
    --ion-color-success-contrast: #000000;
    --ion-color-success-contrast-rgb: 0, 0, 0;
    --ion-color-success-shade: #29c467;
    --ion-color-success-tint: #44e283;

    --ion-color-warning: #ffd534;
    --ion-color-warning-rgb: 255, 213, 52;
    --ion-color-warning-contrast: #000000;
    --ion-color-warning-contrast-rgb: 0, 0, 0;
    --ion-color-warning-shade: #e0bb2e;
    --ion-color-warning-tint: #ffd948;

    --ion-color-danger: #ff4961;
    --ion-color-danger-rgb: 255, 73, 97;
    --ion-color-danger-contrast: #ffffff;
    --ion-color-danger-contrast-rgb: 255, 255, 255;
    --ion-color-danger-shade: #e04055;
    --ion-color-danger-tint: #ff5b71;

    --ion-color-dark: #f4f5f8;
    --ion-color-dark-rgb: 244, 245, 248;
    --ion-color-dark-contrast: #000000;
    --ion-color-dark-contrast-rgb: 0, 0, 0;
    --ion-color-dark-shade: #d7d8da;
    --ion-color-dark-tint: #f5f6f9;

    --ion-color-medium: #989aa2;
    --ion-color-medium-rgb: 152, 154, 162;
    --ion-color-medium-contrast: #000000;
    --ion-color-medium-contrast-rgb: 0, 0, 0;
    --ion-color-medium-shade: #86888f;
    --ion-color-medium-tint: #a2a4ab;

    --ion-color-light: #222428;
    --ion-color-light-rgb: 34, 36, 40;
    --ion-color-light-contrast: #ffffff;
    --ion-color-light-contrast-rgb: 255, 255, 255;
    --ion-color-light-shade: #1e2023;
    --ion-color-light-tint: #383a3e;

    // Custom app colors for dark mode
    --app-background-color: #121212;
    --app-text-color: #f4f4f4;
    --app-border-color: #333333;
    --app-card-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    --app-input-background: #1e1e1e;
    --app-success-light: #1a3328;
    --app-warning-light: #332b1a;
    --app-danger-light: #331a22;
    --app-primary-light: #1a2733;
  }
}
