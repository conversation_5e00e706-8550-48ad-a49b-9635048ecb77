import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, fromEvent, merge, of } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class NetworkService {
  private online$: Observable<boolean>;
  private isOnline = new BehaviorSubject<boolean>(navigator.onLine);

  constructor() {
    // Create an observable from the window's online and offline events
    this.online$ = merge(
      of(navigator.onLine),
      fromEvent(window, 'online').pipe(map(() => true)),
      fromEvent(window, 'offline').pipe(map(() => false))
    );

    // Subscribe to the online$ observable and update the BehaviorSubject
    this.online$.subscribe(isOnline => {
      this.isOnline.next(isOnline);
    });
  }

  /**
   * Returns an observable that emits the current online status
   * and continues to emit when the status changes
   */
  public getOnlineStatus(): Observable<boolean> {
    return this.isOnline.asObservable();
  }

  /**
   * Returns the current online status
   */
  public isOnlineNow(): boolean {
    return navigator.onLine;
  }
}
