{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ionic-global-b26f573e.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { g as getMode, a as setMode } from './index-527b9e34.js';\nimport { c as config, a as configFromSession, b as configFromURL, s as saveConfig, p as printIonWarning } from './index-cfd9c1f2.js';\nconst getPlatforms = win => setupPlatforms(win);\nconst isPlatform = (winOrPlatform, platform) => {\n  if (typeof winOrPlatform === 'string') {\n    platform = winOrPlatform;\n    winOrPlatform = undefined;\n  }\n  return getPlatforms(winOrPlatform).includes(platform);\n};\nconst setupPlatforms = (win = window) => {\n  if (typeof win === 'undefined') {\n    return [];\n  }\n  win.Ionic = win.Ionic || {};\n  let platforms = win.Ionic.platforms;\n  if (platforms == null) {\n    platforms = win.Ionic.platforms = detectPlatforms(win);\n    platforms.forEach(p => win.document.documentElement.classList.add(`plt-${p}`));\n  }\n  return platforms;\n};\nconst detectPlatforms = win => {\n  const customPlatformMethods = config.get('platform');\n  return Object.keys(PLATFORMS_MAP).filter(p => {\n    const customMethod = customPlatformMethods === null || customPlatformMethods === void 0 ? void 0 : customPlatformMethods[p];\n    return typeof customMethod === 'function' ? customMethod(win) : PLATFORMS_MAP[p](win);\n  });\n};\nconst isMobileWeb = win => isMobile(win) && !isHybrid(win);\nconst isIpad = win => {\n  // iOS 12 and below\n  if (testUserAgent(win, /iPad/i)) {\n    return true;\n  }\n  // iOS 13+\n  if (testUserAgent(win, /Macintosh/i) && isMobile(win)) {\n    return true;\n  }\n  return false;\n};\nconst isIphone = win => testUserAgent(win, /iPhone/i);\nconst isIOS = win => testUserAgent(win, /iPhone|iPod/i) || isIpad(win);\nconst isAndroid = win => testUserAgent(win, /android|sink/i);\nconst isAndroidTablet = win => {\n  return isAndroid(win) && !testUserAgent(win, /mobile/i);\n};\nconst isPhablet = win => {\n  const width = win.innerWidth;\n  const height = win.innerHeight;\n  const smallest = Math.min(width, height);\n  const largest = Math.max(width, height);\n  return smallest > 390 && smallest < 520 && largest > 620 && largest < 800;\n};\nconst isTablet = win => {\n  const width = win.innerWidth;\n  const height = win.innerHeight;\n  const smallest = Math.min(width, height);\n  const largest = Math.max(width, height);\n  return isIpad(win) || isAndroidTablet(win) || smallest > 460 && smallest < 820 && largest > 780 && largest < 1400;\n};\nconst isMobile = win => matchMedia(win, '(any-pointer:coarse)');\nconst isDesktop = win => !isMobile(win);\nconst isHybrid = win => isCordova(win) || isCapacitorNative(win);\nconst isCordova = win => !!(win['cordova'] || win['phonegap'] || win['PhoneGap']);\nconst isCapacitorNative = win => {\n  const capacitor = win['Capacitor'];\n  // TODO(ROU-11693): Remove when we no longer support Capacitor 2, which does not have isNativePlatform\n  return !!((capacitor === null || capacitor === void 0 ? void 0 : capacitor.isNative) || (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isNativePlatform) && !!capacitor.isNativePlatform());\n};\nconst isElectron = win => testUserAgent(win, /electron/i);\nconst isPWA = win => {\n  var _a;\n  return !!(((_a = win.matchMedia) === null || _a === void 0 ? void 0 : _a.call(win, '(display-mode: standalone)').matches) || win.navigator.standalone);\n};\nconst testUserAgent = (win, expr) => expr.test(win.navigator.userAgent);\nconst matchMedia = (win, query) => {\n  var _a;\n  return (_a = win.matchMedia) === null || _a === void 0 ? void 0 : _a.call(win, query).matches;\n};\nconst PLATFORMS_MAP = {\n  ipad: isIpad,\n  iphone: isIphone,\n  ios: isIOS,\n  android: isAndroid,\n  phablet: isPhablet,\n  tablet: isTablet,\n  cordova: isCordova,\n  capacitor: isCapacitorNative,\n  electron: isElectron,\n  pwa: isPWA,\n  mobile: isMobile,\n  mobileweb: isMobileWeb,\n  desktop: isDesktop,\n  hybrid: isHybrid\n};\n\n// TODO(FW-2832): types\nlet defaultMode;\nconst getIonMode = ref => {\n  return ref && getMode(ref) || defaultMode;\n};\nconst initialize = (userConfig = {}) => {\n  if (typeof window === 'undefined') {\n    return;\n  }\n  const doc = window.document;\n  const win = window;\n  const Ionic = win.Ionic = win.Ionic || {};\n  // create the Ionic.config from raw config object (if it exists)\n  // and convert Ionic.config into a ConfigApi that has a get() fn\n  const configObj = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, configFromSession(win)), {\n    persistConfig: false\n  }), Ionic.config), configFromURL(win)), userConfig);\n  config.reset(configObj);\n  if (config.getBoolean('persistConfig')) {\n    saveConfig(win, configObj);\n  }\n  // Setup platforms\n  setupPlatforms(win);\n  // first see if the mode was set as an attribute on <html>\n  // which could have been set by the user, or by pre-rendering\n  // otherwise get the mode via config settings, and fallback to md\n  Ionic.config = config;\n  Ionic.mode = defaultMode = config.get('mode', doc.documentElement.getAttribute('mode') || (isPlatform(win, 'ios') ? 'ios' : 'md'));\n  config.set('mode', defaultMode);\n  doc.documentElement.setAttribute('mode', defaultMode);\n  doc.documentElement.classList.add(defaultMode);\n  if (config.getBoolean('_testing')) {\n    config.set('animated', false);\n  }\n  const isIonicElement = elm => {\n    var _a;\n    return (_a = elm.tagName) === null || _a === void 0 ? void 0 : _a.startsWith('ION-');\n  };\n  const isAllowedIonicModeValue = elmMode => ['ios', 'md'].includes(elmMode);\n  setMode(elm => {\n    while (elm) {\n      const elmMode = elm.mode || elm.getAttribute('mode');\n      if (elmMode) {\n        if (isAllowedIonicModeValue(elmMode)) {\n          return elmMode;\n        } else if (isIonicElement(elm)) {\n          printIonWarning('Invalid ionic mode: \"' + elmMode + '\", expected: \"ios\" or \"md\"');\n        }\n      }\n      elm = elm.parentElement;\n    }\n    return defaultMode;\n  });\n};\nexport { isPlatform as a, getIonMode as b, getPlatforms as g, initialize as i };"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,IAKM,cACA,YAOA,gBAYA,iBAOA,aACA,QAWA,UACA,OACA,WACA,iBAGA,WAOA,UAOA,UACA,WACA,UACA,WACA,mBAKA,YACA,OAIA,eACA,YAIA,eAkBF,aACE,YAGA;AAzGN;AAAA;AAAA;AAGA;AACA;AACA,IAAM,eAAe,SAAO,eAAe,GAAG;AAC9C,IAAM,aAAa,CAAC,eAAe,aAAa;AAC9C,UAAI,OAAO,kBAAkB,UAAU;AACrC,mBAAW;AACX,wBAAgB;AAAA,MAClB;AACA,aAAO,aAAa,aAAa,EAAE,SAAS,QAAQ;AAAA,IACtD;AACA,IAAM,iBAAiB,CAAC,MAAM,WAAW;AACvC,UAAI,OAAO,QAAQ,aAAa;AAC9B,eAAO,CAAC;AAAA,MACV;AACA,UAAI,QAAQ,IAAI,SAAS,CAAC;AAC1B,UAAI,YAAY,IAAI,MAAM;AAC1B,UAAI,aAAa,MAAM;AACrB,oBAAY,IAAI,MAAM,YAAY,gBAAgB,GAAG;AACrD,kBAAU,QAAQ,OAAK,IAAI,SAAS,gBAAgB,UAAU,IAAI,OAAO,CAAC,EAAE,CAAC;AAAA,MAC/E;AACA,aAAO;AAAA,IACT;AACA,IAAM,kBAAkB,SAAO;AAC7B,YAAM,wBAAwB,OAAO,IAAI,UAAU;AACnD,aAAO,OAAO,KAAK,aAAa,EAAE,OAAO,OAAK;AAC5C,cAAM,eAAe,0BAA0B,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,CAAC;AAC1H,eAAO,OAAO,iBAAiB,aAAa,aAAa,GAAG,IAAI,cAAc,CAAC,EAAE,GAAG;AAAA,MACtF,CAAC;AAAA,IACH;AACA,IAAM,cAAc,SAAO,SAAS,GAAG,KAAK,CAAC,SAAS,GAAG;AACzD,IAAM,SAAS,SAAO;AAEpB,UAAI,cAAc,KAAK,OAAO,GAAG;AAC/B,eAAO;AAAA,MACT;AAEA,UAAI,cAAc,KAAK,YAAY,KAAK,SAAS,GAAG,GAAG;AACrD,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,IAAM,WAAW,SAAO,cAAc,KAAK,SAAS;AACpD,IAAM,QAAQ,SAAO,cAAc,KAAK,cAAc,KAAK,OAAO,GAAG;AACrE,IAAM,YAAY,SAAO,cAAc,KAAK,eAAe;AAC3D,IAAM,kBAAkB,SAAO;AAC7B,aAAO,UAAU,GAAG,KAAK,CAAC,cAAc,KAAK,SAAS;AAAA,IACxD;AACA,IAAM,YAAY,SAAO;AACvB,YAAM,QAAQ,IAAI;AAClB,YAAM,SAAS,IAAI;AACnB,YAAM,WAAW,KAAK,IAAI,OAAO,MAAM;AACvC,YAAM,UAAU,KAAK,IAAI,OAAO,MAAM;AACtC,aAAO,WAAW,OAAO,WAAW,OAAO,UAAU,OAAO,UAAU;AAAA,IACxE;AACA,IAAM,WAAW,SAAO;AACtB,YAAM,QAAQ,IAAI;AAClB,YAAM,SAAS,IAAI;AACnB,YAAM,WAAW,KAAK,IAAI,OAAO,MAAM;AACvC,YAAM,UAAU,KAAK,IAAI,OAAO,MAAM;AACtC,aAAO,OAAO,GAAG,KAAK,gBAAgB,GAAG,KAAK,WAAW,OAAO,WAAW,OAAO,UAAU,OAAO,UAAU;AAAA,IAC/G;AACA,IAAM,WAAW,SAAO,WAAW,KAAK,sBAAsB;AAC9D,IAAM,YAAY,SAAO,CAAC,SAAS,GAAG;AACtC,IAAM,WAAW,SAAO,UAAU,GAAG,KAAK,kBAAkB,GAAG;AAC/D,IAAM,YAAY,SAAO,CAAC,EAAE,IAAI,SAAS,KAAK,IAAI,UAAU,KAAK,IAAI,UAAU;AAC/E,IAAM,oBAAoB,SAAO;AAC/B,YAAM,YAAY,IAAI,WAAW;AAEjC,aAAO,CAAC,GAAG,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,cAAc,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,qBAAqB,CAAC,CAAC,UAAU,iBAAiB;AAAA,IAC7M;AACA,IAAM,aAAa,SAAO,cAAc,KAAK,WAAW;AACxD,IAAM,QAAQ,SAAO;AACnB,UAAI;AACJ,aAAO,CAAC,IAAI,KAAK,IAAI,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,4BAA4B,EAAE,YAAY,IAAI,UAAU;AAAA,IAC7I;AACA,IAAM,gBAAgB,CAAC,KAAK,SAAS,KAAK,KAAK,IAAI,UAAU,SAAS;AACtE,IAAM,aAAa,CAAC,KAAK,UAAU;AACjC,UAAI;AACJ,cAAQ,KAAK,IAAI,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,KAAK,EAAE;AAAA,IACxF;AACA,IAAM,gBAAgB;AAAA,MACpB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,MACX,UAAU;AAAA,MACV,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAIA,IAAM,aAAa,SAAO;AACxB,aAAO,OAAO,QAAQ,GAAG,KAAK;AAAA,IAChC;AACA,IAAM,aAAa,CAAC,aAAa,CAAC,MAAM;AACtC,UAAI,OAAO,WAAW,aAAa;AACjC;AAAA,MACF;AACA,YAAM,MAAM,OAAO;AACnB,YAAM,MAAM;AACZ,YAAM,QAAQ,IAAI,QAAQ,IAAI,SAAS,CAAC;AAGxC,YAAM,YAAY,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG;AAAA,QACnH,eAAe;AAAA,MACjB,CAAC,GAAG,MAAM,MAAM,GAAG,cAAc,GAAG,CAAC,GAAG,UAAU;AAClD,aAAO,MAAM,SAAS;AACtB,UAAI,OAAO,WAAW,eAAe,GAAG;AACtC,mBAAW,KAAK,SAAS;AAAA,MAC3B;AAEA,qBAAe,GAAG;AAIlB,YAAM,SAAS;AACf,YAAM,OAAO,cAAc,OAAO,IAAI,QAAQ,IAAI,gBAAgB,aAAa,MAAM,MAAM,WAAW,KAAK,KAAK,IAAI,QAAQ,KAAK;AACjI,aAAO,IAAI,QAAQ,WAAW;AAC9B,UAAI,gBAAgB,aAAa,QAAQ,WAAW;AACpD,UAAI,gBAAgB,UAAU,IAAI,WAAW;AAC7C,UAAI,OAAO,WAAW,UAAU,GAAG;AACjC,eAAO,IAAI,YAAY,KAAK;AAAA,MAC9B;AACA,YAAM,iBAAiB,SAAO;AAC5B,YAAI;AACJ,gBAAQ,KAAK,IAAI,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,MAAM;AAAA,MACrF;AACA,YAAM,0BAA0B,aAAW,CAAC,OAAO,IAAI,EAAE,SAAS,OAAO;AACzE,cAAQ,SAAO;AACb,eAAO,KAAK;AACV,gBAAM,UAAU,IAAI,QAAQ,IAAI,aAAa,MAAM;AACnD,cAAI,SAAS;AACX,gBAAI,wBAAwB,OAAO,GAAG;AACpC,qBAAO;AAAA,YACT,WAAW,eAAe,GAAG,GAAG;AAC9B,8BAAgB,0BAA0B,UAAU,4BAA4B;AAAA,YAClF;AAAA,UACF;AACA,gBAAM,IAAI;AAAA,QACZ;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}