{"version": 3, "sources": ["src/app/pages/tabs/journeys/journey-details/journey-details.page.scss"], "sourcesContent": [".loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 50vh;\r\n\r\n  ion-spinner {\r\n    margin-bottom: 16px;\r\n  }\r\n\r\n  p {\r\n    color: var(--ion-color-medium);\r\n    font-size: 0.9rem;\r\n  }\r\n}\r\n\r\n.location-number {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: var(--ion-color-primary);\r\n  color: var(--ion-color-primary-contrast);\r\n  font-weight: bold;\r\n  font-size: 0.9rem;\r\n  border-radius: 50%;\r\n}\r\n\r\n// Estilos para início da viagem\r\n.start-location {\r\n  background: linear-gradient(135deg, var(--ion-color-success), var(--ion-color-success-shade));\r\n  border: 3px solid var(--ion-color-success-tint);\r\n  box-shadow: 0 4px 12px rgba(var(--ion-color-success-rgb), 0.3);\r\n\r\n  ion-icon {\r\n    font-size: 1.8rem;\r\n    color: white;\r\n  }\r\n}\r\n\r\n// Estilos para fim da viagem\r\n.end-location {\r\n  background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-primary-shade));\r\n  border: 3px solid var(--ion-color-primary-tint);\r\n  box-shadow: 0 4px 12px rgba(var(--ion-color-primary-rgb), 0.3);\r\n\r\n  ion-icon {\r\n    font-size: 1.8rem;\r\n    color: white;\r\n  }\r\n}\r\n\r\n// Classes para os itens de localização\r\n.start-location-item {\r\n  --background: linear-gradient(135deg, rgba(var(--ion-color-success-rgb), 0.1), rgba(var(--ion-color-success-rgb), 0.05));\r\n  border-left: 4px solid var(--ion-color-success);\r\n  margin-bottom: 12px;\r\n\r\n  .start-title {\r\n    color: var(--ion-color-success);\r\n    font-weight: 600;\r\n    font-size: 1.1rem;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    margin-bottom: 8px;\r\n\r\n    ion-icon {\r\n      font-size: 1.2rem;\r\n    }\r\n  }\r\n}\r\n\r\n.end-location-item {\r\n  --background: linear-gradient(135deg, rgba(var(--ion-color-primary-rgb), 0.1), rgba(var(--ion-color-primary-rgb), 0.05));\r\n  border-left: 4px solid var(--ion-color-primary);\r\n  margin-bottom: 12px;\r\n\r\n  .end-title {\r\n    color: var(--ion-color-primary);\r\n    font-weight: 600;\r\n    font-size: 1.1rem;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    margin-bottom: 8px;\r\n\r\n    ion-icon {\r\n      font-size: 1.2rem;\r\n    }\r\n  }\r\n}\r\n\r\n.intermediate-location-item {\r\n  --background: var(--ion-color-light);\r\n  border-left: 2px solid var(--ion-color-medium);\r\n\r\n  .intermediate-title {\r\n    color: var(--ion-color-dark);\r\n    font-weight: 500;\r\n    font-size: 1rem;\r\n    margin-bottom: 6px;\r\n  }\r\n}\r\n\r\n.loading-address {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: var(--ion-color-medium);\r\n  font-style: italic;\r\n\r\n  ion-spinner {\r\n    --color: var(--ion-color-medium);\r\n  }\r\n}\r\n\r\n.occurrence-type {\r\n  color: var(--ion-color-warning);\r\n  font-weight: 500;\r\n  text-transform: capitalize;\r\n}\r\n\r\nion-item {\r\n  --padding-start: 16px;\r\n  --inner-padding-end: 16px;\r\n  border-radius: 12px;\r\n  margin-bottom: 12px;\r\n\r\n  ion-label {\r\n    margin: 12px 0;\r\n\r\n    h3 {\r\n      color: var(--ion-color-dark);\r\n      font-weight: 600;\r\n      margin-bottom: 8px;\r\n    }\r\n\r\n    // Estilos específicos para cada tipo de informação\r\n    .timestamp {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      margin: 6px 0;\r\n      font-size: 0.95rem;\r\n      font-weight: 500;\r\n      color: var(--ion-color-dark);\r\n\r\n      ion-icon {\r\n        font-size: 1.1rem;\r\n        color: var(--ion-color-primary);\r\n      }\r\n    }\r\n\r\n    .coordinates {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      margin: 4px 0;\r\n      font-size: 0.85rem;\r\n      color: var(--ion-color-medium);\r\n      font-family: 'Courier New', monospace;\r\n\r\n      ion-icon {\r\n        font-size: 1rem;\r\n        color: var(--ion-color-medium);\r\n      }\r\n    }\r\n\r\n    .address {\r\n      display: flex;\r\n      align-items: flex-start;\r\n      gap: 8px;\r\n      margin: 6px 0;\r\n      font-size: 0.9rem;\r\n      line-height: 1.4;\r\n\r\n      ion-icon {\r\n        font-size: 1rem;\r\n        color: var(--ion-color-tertiary);\r\n        margin-top: 2px;\r\n        flex-shrink: 0;\r\n      }\r\n\r\n      strong {\r\n        color: var(--ion-color-dark);\r\n      }\r\n    }\r\n\r\n    .occurrence {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      margin: 6px 0;\r\n      font-size: 0.9rem;\r\n      padding: 6px 12px;\r\n      background-color: rgba(var(--ion-color-warning-rgb), 0.1);\r\n      border-radius: 8px;\r\n      border-left: 3px solid var(--ion-color-warning);\r\n\r\n      ion-icon {\r\n        font-size: 1.1rem;\r\n      }\r\n    }\r\n\r\n    // Estilo geral para parágrafos que não têm classe específica\r\n    p:not(.timestamp):not(.coordinates):not(.address):not(.occurrence):not(.loading-address) {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      margin: 4px 0;\r\n      font-size: 0.9rem;\r\n\r\n      ion-icon {\r\n        font-size: 1rem;\r\n        color: var(--ion-color-medium);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nion-card {\r\n  margin-bottom: 16px;\r\n\r\n  ion-card-title {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n\r\n    ion-icon {\r\n      font-size: 1.3rem;\r\n      color: var(--ion-color-primary);\r\n    }\r\n\r\n    ion-badge {\r\n      margin-left: auto;\r\n    }\r\n  }\r\n}\r\n\r\n// Journey Progress Indicator\r\n.journey-progress {\r\n  margin-bottom: 20px;\r\n  padding: 16px;\r\n  background: linear-gradient(135deg, rgba(var(--ion-color-primary-rgb), 0.05), rgba(var(--ion-color-success-rgb), 0.05));\r\n  border-radius: 12px;\r\n  border: 1px solid rgba(var(--ion-color-primary-rgb), 0.1);\r\n\r\n  .progress-line {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 8px;\r\n\r\n    .progress-start, .progress-end {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      width: 40px;\r\n      height: 40px;\r\n      border-radius: 50%;\r\n      background: white;\r\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n      ion-icon {\r\n        font-size: 1.5rem;\r\n      }\r\n    }\r\n\r\n    .progress-track {\r\n      flex: 1;\r\n      height: 4px;\r\n      background: linear-gradient(90deg, var(--ion-color-success), var(--ion-color-primary));\r\n      margin: 0 12px;\r\n      border-radius: 2px;\r\n      position: relative;\r\n\r\n      &::after {\r\n        content: '';\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 50%;\r\n        transform: translate(-50%, -50%);\r\n        width: 8px;\r\n        height: 8px;\r\n        background: var(--ion-color-warning);\r\n        border-radius: 50%;\r\n        box-shadow: 0 0 0 3px rgba(var(--ion-color-warning-rgb), 0.3);\r\n      }\r\n    }\r\n  }\r\n\r\n  .progress-info {\r\n    text-align: center;\r\n\r\n    .progress-points {\r\n      font-size: 0.9rem;\r\n      color: var(--ion-color-medium);\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n// Journey Details\r\n.journey-details {\r\n  p {\r\n    margin: 8px 0;\r\n    font-size: 0.95rem;\r\n\r\n    strong {\r\n      color: var(--ion-color-dark);\r\n      font-weight: 600;\r\n    }\r\n  }\r\n}\r\n\r\nion-list {\r\n  background: transparent;\r\n  padding: 8px 0;\r\n\r\n  ion-item {\r\n    --background: var(--ion-color-light);\r\n    margin-bottom: 12px;\r\n    border-radius: 12px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      transform: translateY(-2px);\r\n      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\r\n    }\r\n\r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n\r\n    // Animação especial para início e fim\r\n    &.start-location-item, &.end-location-item {\r\n      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\r\n\r\n      &:hover {\r\n        transform: translateY(-3px);\r\n        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);\r\n      }\r\n    }\r\n  }\r\n}"], "mappings": ";AAAA,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,UAAA;;AAEA,CAPF,kBAOE;AACE,iBAAA;;AAGF,CAXF,kBAWE;AACE,SAAA,IAAA;AACA,aAAA;;AAIJ,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,SAAA;AACA,UAAA;AACA,oBAAA,IAAA;AACA,SAAA,IAAA;AACA,eAAA;AACA,aAAA;AACA,iBAAA;;AAIF,CAAA;AACE;IAAA;MAAA,MAAA;MAAA,IAAA,oBAAA;MAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,IAAA,wBAAA,EAAA;;AAEA,CALF,eAKE;AACE,aAAA;AACA,SAAA;;AAKJ,CAAA;AACE;IAAA;MAAA,MAAA;MAAA,IAAA,oBAAA;MAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,IAAA,wBAAA,EAAA;;AAEA,CALF,aAKE;AACE,aAAA;AACA,SAAA;;AAKJ,CAAA;AACE;IAAA;MAAA,MAAA;MAAA,KAAA,IAAA,wBAAA,EAAA,IAAA;MAAA,KAAA,IAAA,wBAAA,EAAA;AACA,eAAA,IAAA,MAAA,IAAA;AACA,iBAAA;;AAEA,CALF,oBAKE,CAAA;AACE,SAAA,IAAA;AACA,eAAA;AACA,aAAA;AACA,WAAA;AACA,eAAA;AACA,OAAA;AACA,iBAAA;;AAEA,CAdJ,oBAcI,CATF,YASE;AACE,aAAA;;AAKN,CAAA;AACE;IAAA;MAAA,MAAA;MAAA,KAAA,IAAA,wBAAA,EAAA,IAAA;MAAA,KAAA,IAAA,wBAAA,EAAA;AACA,eAAA,IAAA,MAAA,IAAA;AACA,iBAAA;;AAEA,CALF,kBAKE,CAAA;AACE,SAAA,IAAA;AACA,eAAA;AACA,aAAA;AACA,WAAA;AACA,eAAA;AACA,OAAA;AACA,iBAAA;;AAEA,CAdJ,kBAcI,CATF,UASE;AACE,aAAA;;AAKN,CAAA;AACE,gBAAA,IAAA;AACA,eAAA,IAAA,MAAA,IAAA;;AAEA,CAJF,2BAIE,CAAA;AACE,SAAA,IAAA;AACA,eAAA;AACA,aAAA;AACA,iBAAA;;AAIJ,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,SAAA,IAAA;AACA,cAAA;;AAEA,CAPF,gBAOE;AACE,WAAA,IAAA;;AAIJ,CAAA;AACE,SAAA,IAAA;AACA,eAAA;AACA,kBAAA;;AAGF;AACE,mBAAA;AACA,uBAAA;AACA,iBAAA;AACA,iBAAA;;AAEA,SAAA;AACE,UAAA,KAAA;;AAEA,SAAA,UAAA;AACE,SAAA,IAAA;AACA,eAAA;AACA,iBAAA;;AAIF,SAAA,UAAA,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,UAAA,IAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA,IAAA;;AAEA,SAAA,UAAA,CATF,UASE;AACE,aAAA;AACA,SAAA,IAAA;;AAIJ,SAAA,UAAA,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,UAAA,IAAA;AACA,aAAA;AACA,SAAA,IAAA;AACA,eAAA,aAAA,EAAA;;AAEA,SAAA,UAAA,CATF,YASE;AACE,aAAA;AACA,SAAA,IAAA;;AAIJ,SAAA,UAAA,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,UAAA,IAAA;AACA,aAAA;AACA,eAAA;;AAEA,SAAA,UAAA,CARF,QAQE;AACE,aAAA;AACA,SAAA,IAAA;AACA,cAAA;AACA,eAAA;;AAGF,SAAA,UAAA,CAfF,QAeE;AACE,SAAA,IAAA;;AAIJ,SAAA,UAAA,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,UAAA,IAAA;AACA,aAAA;AACA,WAAA,IAAA;AACA,oBAAA,KAAA,IAAA,wBAAA,EAAA;AACA,iBAAA;AACA,eAAA,IAAA,MAAA,IAAA;;AAEA,SAAA,UAAA,CAXF,WAWE;AACE,aAAA;;AAKJ,SAAA,UAAA,CAAA,KAAA,CAnEA,UAmEA,KAAA,CApDA,YAoDA,KAAA,CArCA,QAqCA,KAAA,CAjBA,WAiBA,KAAA,CArGJ;AAsGM,WAAA;AACA,eAAA;AACA,OAAA;AACA,UAAA,IAAA;AACA,aAAA;;AAEA,SAAA,UAAA,CAAA,KAAA,CA1EF,UA0EE,KAAA,CA3DF,YA2DE,KAAA,CA5CF,QA4CE,KAAA,CAxBF,WAwBE,KAAA,CA5GN,iBA4GM;AACE,aAAA;AACA,SAAA,IAAA;;AAMR;AACE,iBAAA;;AAEA,SAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAEA,SAAA,eAAA;AACE,aAAA;AACA,SAAA,IAAA;;AAGF,SAAA,eAAA;AACE,eAAA;;AAMN,CAAA;AACE,iBAAA;AACA,WAAA;AACA;IAAA;MAAA,MAAA;MAAA,KAAA,IAAA,wBAAA,EAAA,KAAA;MAAA,KAAA,IAAA,wBAAA,EAAA;AACA,iBAAA;AACA,UAAA,IAAA,MAAA,KAAA,IAAA,wBAAA,EAAA;;AAEA,CAPF,iBAOE,CAAA;AACE,WAAA;AACA,eAAA;AACA,iBAAA;;AAEA,CAZJ,iBAYI,CALF,cAKE,CAAA;AAAA,CAZJ,iBAYI,CALF,cAKE,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,cAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAEA,CAtBN,iBAsBM,CAfJ,cAeI,CAVF,eAUE;AAAA,CAtBN,iBAsBM,CAfJ,cAeI,CAVF,aAUE;AACE,aAAA;;AAIJ,CA3BJ,iBA2BI,CApBF,cAoBE,CAAA;AACE,QAAA;AACA,UAAA;AACA;IAAA;MAAA,KAAA;MAAA,IAAA,oBAAA;MAAA,IAAA;AACA,UAAA,EAAA;AACA,iBAAA;AACA,YAAA;;AAEA,CAnCN,iBAmCM,CA5BJ,cA4BI,CARF,cAQE;AACE,WAAA;AACA,YAAA;AACA,OAAA;AACA,QAAA;AACA,aAAA,UAAA,IAAA,EAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA,IAAA;AACA,iBAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,KAAA,IAAA,wBAAA,EAAA;;AAKN,CAlDF,iBAkDE,CAAA;AACE,cAAA;;AAEA,CArDJ,iBAqDI,CAHF,cAGE,CAAA;AACE,aAAA;AACA,SAAA,IAAA;AACA,eAAA;;AAOJ,CAAA,gBAAA;AACE,UAAA,IAAA;AACA,aAAA;;AAEA,CAJF,gBAIE,EAAA;AACE,SAAA,IAAA;AACA,eAAA;;AAKN;AACE,cAAA;AACA,WAAA,IAAA;;AAEA,SAAA;AACE,gBAAA,IAAA;AACA,iBAAA;AACA,iBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,cAAA,IAAA,KAAA;;AAEA,SAAA,QAAA;AACE,aAAA,WAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,SAAA,QAAA;AACE,iBAAA;;AAIF,SAAA,QAAA,CA3RJ;AA2RI,SAAA,QAAA,CAvQJ;AAwQM,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAEA,SAAA,QAAA,CA9RN,mBA8RM;AAAA,SAAA,QAAA,CA1QN,iBA0QM;AACE,aAAA,WAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;", "names": []}