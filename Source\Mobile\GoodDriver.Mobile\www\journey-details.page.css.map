{"version": 3, "sources": ["src/app/pages/tabs/journeys/journey-details/journey-details.page.scss"], "sourcesContent": [".loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 50vh;\r\n\r\n  ion-spinner {\r\n    margin-bottom: 16px;\r\n  }\r\n\r\n  p {\r\n    color: var(--ion-color-medium);\r\n    font-size: 0.9rem;\r\n  }\r\n}\r\n\r\n.location-number {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: var(--ion-color-primary);\r\n  color: var(--ion-color-primary-contrast);\r\n  font-weight: bold;\r\n  font-size: 0.9rem;\r\n  border-radius: 50%;\r\n}\r\n\r\n.loading-address {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: var(--ion-color-medium);\r\n  font-style: italic;\r\n\r\n  ion-spinner {\r\n    --color: var(--ion-color-medium);\r\n  }\r\n}\r\n\r\n.occurrence-type {\r\n  color: var(--ion-color-warning);\r\n  font-weight: 500;\r\n  text-transform: capitalize;\r\n}\r\n\r\nion-item {\r\n  --padding-start: 16px;\r\n  --inner-padding-end: 16px;\r\n\r\n  ion-label {\r\n    margin: 8px 0;\r\n\r\n    h3 {\r\n      color: var(--ion-color-dark);\r\n      font-weight: 600;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    p {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      margin: 4px 0;\r\n      font-size: 0.9rem;\r\n\r\n      ion-icon {\r\n        font-size: 1rem;\r\n        color: var(--ion-color-medium);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nion-card {\r\n  margin-bottom: 16px;\r\n\r\n  ion-card-title {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    ion-badge {\r\n      margin-left: 8px;\r\n    }\r\n  }\r\n}\r\n\r\nion-list {\r\n  background: transparent;\r\n\r\n  ion-item {\r\n    --background: var(--ion-color-light);\r\n    margin-bottom: 8px;\r\n    border-radius: 8px;\r\n\r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}"], "mappings": ";AAAA,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,UAAA;;AAEA,CAPF,kBAOE;AACE,iBAAA;;AAGF,CAXF,kBAWE;AACE,SAAA,IAAA;AACA,aAAA;;AAIJ,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,SAAA;AACA,UAAA;AACA,oBAAA,IAAA;AACA,SAAA,IAAA;AACA,eAAA;AACA,aAAA;AACA,iBAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,SAAA,IAAA;AACA,cAAA;;AAEA,CAPF,gBAOE;AACE,WAAA,IAAA;;AAIJ,CAAA;AACE,SAAA,IAAA;AACA,eAAA;AACA,kBAAA;;AAGF;AACE,mBAAA;AACA,uBAAA;;AAEA,SAAA;AACE,UAAA,IAAA;;AAEA,SAAA,UAAA;AACE,SAAA,IAAA;AACA,eAAA;AACA,iBAAA;;AAGF,SAAA,UAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,UAAA,IAAA;AACA,aAAA;;AAEA,SAAA,UAAA,EAAA;AACE,aAAA;AACA,SAAA,IAAA;;AAMR;AACE,iBAAA;;AAEA,SAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;;AAEA,SAAA,eAAA;AACE,eAAA;;AAKN;AACE,cAAA;;AAEA,SAAA;AACE,gBAAA,IAAA;AACA,iBAAA;AACA,iBAAA;;AAEA,SAAA,QAAA;AACE,iBAAA;;", "names": []}