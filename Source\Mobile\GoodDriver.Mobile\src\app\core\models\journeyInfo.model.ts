import { SyncStatus } from "./sync.model";

export interface JourneyInfo {
    id: string;
    journeyId: string;
    latitude: number;
    longitude: number;
    timestamp: string;
    address?: string;
    occurrenceType?: 'HardBreak' | 'Acceleration' | 'turn' | 'speeding' | 'stop' | 'Collision' | 'PhoneUse' | 'other';
    syncStatus?: SyncStatus;
    lastSyncDate?: Date;
    // Propriedades de precisão GPS
    accuracy?: number;    // Precisão em metros
    altitude?: number;    // Altitude em metros
    speed?: number;       // Velocidade em m/s
  }
