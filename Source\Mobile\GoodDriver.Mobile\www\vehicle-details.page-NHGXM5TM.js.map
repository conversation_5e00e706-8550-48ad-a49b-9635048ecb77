{"version": 3, "sources": ["src/app/pages/tabs/vehicles/vehicle-details/vehicle-details.page.ts", "src/app/pages/tabs/vehicles/vehicle-details/vehicle-details.page.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { CommonModule } from '@angular/common';\nimport { Vehicle } from 'src/app/core/models/vehicle.model';\nimport { VehicleService } from 'src/app/core/services/vehicle.service';\nimport { ToastService } from 'src/app/core/services/toast.service';\nimport { SessionService } from 'src/app/core/services/session.service';\nimport { VehicleStateService } from 'src/app/core/services/vehicle-state.service';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-vehicle-details',\n  templateUrl: './vehicle-details.page.html',\n  styleUrls: ['./vehicle-details.page.scss'],\n  standalone: true,\n  imports: [IonicModule, CommonModule]\n})\nexport class VehicleDetailsPage implements OnInit, OnDestroy {\n  vehicleId: string = '';\n  vehicle: Vehicle | null = null;\n  isLoading = true;\n\n  // Subscriptions\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private vehicleService: VehicleService,\n    private toastService: ToastService,\n    private sessionService: SessionService,\n    private vehicleStateService: VehicleStateService\n  ) {}\n\n  ngOnInit() {\n    this.vehicleId = this.route.snapshot.paramMap.get('id') || '';\n    this.loadVehicleDetails();\n\n    // Subscribe to primary vehicle changes\n    this.subscriptions.push(\n      this.vehicleStateService.primaryVehicle$.subscribe(primaryVehicle => {\n        if (primaryVehicle && this.vehicle && primaryVehicle.id === this.vehicle.id) {\n          this.vehicle.isPrimary = primaryVehicle.isPrimary;\n        }\n      })\n    );\n  }\n\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  async loadVehicleDetails() {\n    if (!this.vehicleId) {\n      this.toastService.showToast('ID do veículo não encontrado', 'danger');\n      this.router.navigate(['/tabs/vehicles']);\n      return;\n    }\n\n    this.isLoading = true;\n    try {\n      const userId = await this.sessionService.getUserId() || '';\n      const vehicles = await this.vehicleService.listAllLocal(userId);\n      this.vehicle = vehicles.find(v => v.id === this.vehicleId) || null;\n\n      if (!this.vehicle) {\n        this.toastService.showToast('Veículo não encontrado', 'danger');\n        this.router.navigate(['/tabs/vehicles']);\n      }\n    } catch (error) {\n      console.error('Error loading vehicle details:', error);\n      this.toastService.showToast('Erro ao carregar detalhes do veículo', 'danger');\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  goBack() {\n    this.router.navigate(['/tabs/vehicles']);\n  }\n\n  /**\n   * Sets this vehicle as the primary vehicle\n   */\n  async setPrimaryVehicle() {\n    if (!this.vehicle) return;\n\n    const userId = await this.sessionService.getUserId() || '';\n    try {\n      const success = await this.vehicleService.setPrimaryVehicle(this.vehicle.id, userId);\n      if (success) {\n        this.toastService.showToast('Veículo definido como principal');\n        // Update the vehicle object to reflect the change\n        this.vehicle.isPrimary = true;\n\n        // The VehicleService will update the VehicleStateService,\n        // which will notify all subscribers about the change\n      } else {\n        this.toastService.showToast('Erro ao definir veículo como principal', 'danger');\n      }\n    } catch (error) {\n      console.error('Error setting primary vehicle:', error);\n      this.toastService.showToast('Erro ao definir veículo como principal', 'danger');\n    }\n  }\n\n  goToEditVehicle(id: string) {\n    this.router.navigate(['/tabs/vehicles/edit/', id]);\n  }\n}\n", "<ion-header>\n  <ion-toolbar>\n    <ion-buttons slot=\"start\">\n      <ion-back-button defaultHref=\"/tabs/vehicles\"></ion-back-button>\n    </ion-buttons>\n    <ion-title>Detalhes do Veículo</ion-title>\n  </ion-toolbar>\n</ion-header>\n\n<ion-content class=\"ion-padding\">\n  <!-- Indicador de carregamento -->\n  <div *ngIf=\"isLoading\" class=\"ion-text-center ion-padding\">\n    <ion-spinner></ion-spinner>\n  </div>\n\n  <!-- Detalhes do veículo -->\n  <div *ngIf=\"!isLoading && vehicle\">\n    <ion-card>\n      <ion-card-header>\n        <div class=\"vehicle-header\">\n          <ion-card-title>{{ vehicle.plate }}</ion-card-title>\n          <ion-badge color=\"success\" *ngIf=\"vehicle.isPrimary\">Veículo Principal</ion-badge>\n        </div>\n        <ion-card-subtitle>{{ vehicle.brandName }} {{ vehicle.modelName }}</ion-card-subtitle>\n      </ion-card-header>\n\n      <ion-card-content>\n        <ion-list lines=\"none\">\n          <ion-item>\n            <ion-icon name=\"calendar-outline\" slot=\"start\" color=\"primary\"></ion-icon>\n            <ion-label>\n              <h3>Ano</h3>\n              <p>{{ vehicle.year }}</p>\n            </ion-label>\n          </ion-item>\n\n          <ion-item *ngIf=\"vehicle.version\">\n            <ion-icon name=\"options-outline\" slot=\"start\" color=\"primary\"></ion-icon>\n            <ion-label>\n              <h3>Versão</h3>\n              <p>{{ vehicle.version }}</p>\n            </ion-label>\n          </ion-item>\n\n          <ion-item *ngIf=\"vehicle.policyNumber\">\n            <ion-icon name=\"shield-checkmark-outline\" slot=\"start\" color=\"primary\"></ion-icon>\n            <ion-label>\n              <h3>Número da Apólice</h3>\n              <p>{{ vehicle.policyNumber }}</p>\n            </ion-label>\n          </ion-item>\n\n          <ion-item>\n            <ion-icon name=\"time-outline\" slot=\"start\" color=\"primary\"></ion-icon>\n            <ion-label>\n              <h3>Data de Cadastro</h3>\n              <p>{{ vehicle.createdOn | date:'dd/MM/yyyy HH:mm' }}</p>\n            </ion-label>\n          </ion-item>\n        </ion-list>\n      </ion-card-content>\n    </ion-card>\n\n    <!-- Botões de ação -->\n    <div class=\"ion-padding\">\n      <ion-button expand=\"block\" color=\"success\" *ngIf=\"!vehicle.isPrimary\" (click)=\"setPrimaryVehicle()\">\n        <ion-icon name=\"star\" slot=\"start\"></ion-icon>\n        Definir como Veículo Principal\n      </ion-button>\n\n      <ion-button expand=\"block\" color=\"primary\" (click)=\"goToEditVehicle(vehicle.id)\">\n        <ion-icon name=\"create-outline\" slot=\"start\"></ion-icon>\n        Editar Veículo\n      </ion-button>\n\n      <ion-button expand=\"block\" color=\"danger\" disabled>\n        <ion-icon name=\"trash-outline\" slot=\"start\"></ion-icon>\n        Excluir Veículo\n      </ion-button>\n    </div>\n  </div>\n\n  <!-- Mensagem de erro -->\n  <ion-text color=\"danger\" *ngIf=\"!isLoading && !vehicle\">\n    <p class=\"ion-text-center\">Veículo não encontrado.</p>\n  </ion-text>\n</ion-content>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACWE,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,oBAAA,GAAA,aAAA;AACF,IAAA,uBAAA;;;;;AAQQ,IAAA,yBAAA,GAAA,aAAA,EAAA;AAAqD,IAAA,iBAAA,GAAA,sBAAA;AAAiB,IAAA,uBAAA;;;;;AAetE,IAAA,yBAAA,GAAA,UAAA;AACE,IAAA,oBAAA,GAAA,YAAA,EAAA;AACA,IAAA,yBAAA,GAAA,WAAA,EAAW,GAAA,IAAA;AACL,IAAA,iBAAA,GAAA,WAAA;AAAM,IAAA,uBAAA;AACV,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,CAAA;AAAqB,IAAA,uBAAA,EAAI,EAClB;;;;AADP,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,QAAA,OAAA;;;;;AAIP,IAAA,yBAAA,GAAA,UAAA;AACE,IAAA,oBAAA,GAAA,YAAA,EAAA;AACA,IAAA,yBAAA,GAAA,WAAA,EAAW,GAAA,IAAA;AACL,IAAA,iBAAA,GAAA,yBAAA;AAAiB,IAAA,uBAAA;AACrB,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,CAAA;AAA0B,IAAA,uBAAA,EAAI,EACvB;;;;AADP,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,QAAA,YAAA;;;;;;AAiBX,IAAA,yBAAA,GAAA,cAAA,EAAA;AAAsE,IAAA,qBAAA,SAAA,SAAA,8EAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,kBAAA,CAAmB;IAAA,CAAA;AAChG,IAAA,oBAAA,GAAA,YAAA,EAAA;AACA,IAAA,iBAAA,GAAA,qCAAA;AACF,IAAA,uBAAA;;;;;;AApDJ,IAAA,yBAAA,GAAA,KAAA,EAAmC,GAAA,UAAA,EACvB,GAAA,iBAAA,EACS,GAAA,OAAA,CAAA,EACa,GAAA,gBAAA;AACV,IAAA,iBAAA,CAAA;AAAmB,IAAA,uBAAA;AACnC,IAAA,qBAAA,GAAA,+CAAA,GAAA,GAAA,aAAA,CAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,mBAAA;AAAmB,IAAA,iBAAA,CAAA;AAA+C,IAAA,uBAAA,EAAoB;AAGxF,IAAA,yBAAA,GAAA,kBAAA,EAAkB,IAAA,YAAA,CAAA,EACO,IAAA,UAAA;AAEnB,IAAA,oBAAA,IAAA,YAAA,EAAA;AACA,IAAA,yBAAA,IAAA,WAAA,EAAW,IAAA,IAAA;AACL,IAAA,iBAAA,IAAA,KAAA;AAAG,IAAA,uBAAA;AACP,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,EAAA;AAAkB,IAAA,uBAAA,EAAI,EACf;AAGd,IAAA,qBAAA,IAAA,+CAAA,GAAA,GAAA,YAAA,CAAA,EAAkC,IAAA,+CAAA,GAAA,GAAA,YAAA,CAAA;AAgBlC,IAAA,yBAAA,IAAA,UAAA;AACE,IAAA,oBAAA,IAAA,YAAA,EAAA;AACA,IAAA,yBAAA,IAAA,WAAA,EAAW,IAAA,IAAA;AACL,IAAA,iBAAA,IAAA,kBAAA;AAAgB,IAAA,uBAAA;AACpB,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,EAAA;;AAAiD,IAAA,uBAAA,EAAI,EAC9C,EACH,EACF,EACM;AAIrB,IAAA,yBAAA,IAAA,OAAA,CAAA;AACE,IAAA,qBAAA,IAAA,iDAAA,GAAA,GAAA,cAAA,EAAA;AAKA,IAAA,yBAAA,IAAA,cAAA,EAAA;AAA2C,IAAA,qBAAA,SAAA,SAAA,iEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,OAAA,QAAA,EAAA,CAA2B;IAAA,CAAA;AAC7E,IAAA,oBAAA,IAAA,YAAA,EAAA;AACA,IAAA,iBAAA,IAAA,qBAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,cAAA,EAAA;AACE,IAAA,oBAAA,IAAA,YAAA,EAAA;AACA,IAAA,iBAAA,IAAA,sBAAA;AACF,IAAA,uBAAA,EAAa,EACT;;;;AA3DgB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,QAAA,KAAA;AACY,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA,SAAA;AAEX,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,IAAA,OAAA,QAAA,WAAA,KAAA,OAAA,QAAA,WAAA,EAAA;AASV,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,QAAA,IAAA;AAII,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA,OAAA;AAQA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA,YAAA;AAYJ,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,sBAAA,IAAA,GAAA,OAAA,QAAA,WAAA,kBAAA,CAAA;AASiC,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,OAAA,QAAA,SAAA;;;;;AAkBhD,IAAA,yBAAA,GAAA,YAAA,EAAA,EAAwD,GAAA,KAAA,EAAA;AAC3B,IAAA,iBAAA,GAAA,+BAAA;AAAuB,IAAA,uBAAA,EAAI;;;ADpF1D,IAkBa;AAlBb;;;AAEA;AACA;;;;;;;;;AAeM,IAAO,sBAAP,MAAO,oBAAkB;MAQ7B,YACU,OACA,QACA,gBACA,cACA,gBACA,qBAAwC;AALxC,aAAA,QAAA;AACA,aAAA,SAAA;AACA,aAAA,iBAAA;AACA,aAAA,eAAA;AACA,aAAA,iBAAA;AACA,aAAA,sBAAA;AAbV,aAAA,YAAoB;AACpB,aAAA,UAA0B;AAC1B,aAAA,YAAY;AAGJ,aAAA,gBAAgC,CAAA;MASrC;MAEH,WAAQ;AACN,aAAK,YAAY,KAAK,MAAM,SAAS,SAAS,IAAI,IAAI,KAAK;AAC3D,aAAK,mBAAkB;AAGvB,aAAK,cAAc,KACjB,KAAK,oBAAoB,gBAAgB,UAAU,oBAAiB;AAClE,cAAI,kBAAkB,KAAK,WAAW,eAAe,OAAO,KAAK,QAAQ,IAAI;AAC3E,iBAAK,QAAQ,YAAY,eAAe;UAC1C;QACF,CAAC,CAAC;MAEN;MAEA,cAAW;AAET,aAAK,cAAc,QAAQ,SAAO,IAAI,YAAW,CAAE;MACrD;MAEM,qBAAkB;;AACtB,cAAI,CAAC,KAAK,WAAW;AACnB,iBAAK,aAAa,UAAU,sCAAgC,QAAQ;AACpE,iBAAK,OAAO,SAAS,CAAC,gBAAgB,CAAC;AACvC;UACF;AAEA,eAAK,YAAY;AACjB,cAAI;AACF,kBAAM,UAAS,MAAM,KAAK,eAAe,UAAS,MAAM;AACxD,kBAAM,WAAW,MAAM,KAAK,eAAe,aAAa,MAAM;AAC9D,iBAAK,UAAU,SAAS,KAAK,OAAK,EAAE,OAAO,KAAK,SAAS,KAAK;AAE9D,gBAAI,CAAC,KAAK,SAAS;AACjB,mBAAK,aAAa,UAAU,gCAA0B,QAAQ;AAC9D,mBAAK,OAAO,SAAS,CAAC,gBAAgB,CAAC;YACzC;UACF,SAAS,OAAO;AACd,oBAAQ,MAAM,kCAAkC,KAAK;AACrD,iBAAK,aAAa,UAAU,2CAAwC,QAAQ;UAC9E;AACE,iBAAK,YAAY;UACnB;QACF;;MAEA,SAAM;AACJ,aAAK,OAAO,SAAS,CAAC,gBAAgB,CAAC;MACzC;;;;MAKM,oBAAiB;;AACrB,cAAI,CAAC,KAAK;AAAS;AAEnB,gBAAM,UAAS,MAAM,KAAK,eAAe,UAAS,MAAM;AACxD,cAAI;AACF,kBAAM,UAAU,MAAM,KAAK,eAAe,kBAAkB,KAAK,QAAQ,IAAI,MAAM;AACnF,gBAAI,SAAS;AACX,mBAAK,aAAa,UAAU,oCAAiC;AAE7D,mBAAK,QAAQ,YAAY;YAI3B,OAAO;AACL,mBAAK,aAAa,UAAU,6CAA0C,QAAQ;YAChF;UACF,SAAS,OAAO;AACd,oBAAQ,MAAM,kCAAkC,KAAK;AACrD,iBAAK,aAAa,UAAU,6CAA0C,QAAQ;UAChF;QACF;;MAEA,gBAAgB,IAAU;AACxB,aAAK,OAAO,SAAS,CAAC,wBAAwB,EAAE,CAAC;MACnD;;;uCA5FW,qBAAkB,4BAAA,cAAA,GAAA,4BAAA,MAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,YAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,mBAAA,CAAA;IAAA;wFAAlB,qBAAkB,WAAA,CAAA,CAAA,qBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,QAAA,OAAA,GAAA,CAAA,eAAA,gBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,+BAAA,GAAA,MAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,SAAA,UAAA,GAAA,MAAA,GAAA,CAAA,GAAA,mBAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,WAAA,GAAA,MAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,QAAA,oBAAA,QAAA,SAAA,SAAA,SAAA,GAAA,CAAA,QAAA,gBAAA,QAAA,SAAA,SAAA,SAAA,GAAA,CAAA,UAAA,SAAA,SAAA,WAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,UAAA,SAAA,SAAA,WAAA,GAAA,OAAA,GAAA,CAAA,QAAA,kBAAA,QAAA,OAAA,GAAA,CAAA,UAAA,SAAA,SAAA,UAAA,YAAA,EAAA,GAAA,CAAA,QAAA,iBAAA,QAAA,OAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,QAAA,mBAAA,QAAA,SAAA,SAAA,SAAA,GAAA,CAAA,QAAA,4BAAA,QAAA,SAAA,SAAA,SAAA,GAAA,CAAA,UAAA,SAAA,SAAA,WAAA,GAAA,OAAA,GAAA,CAAA,QAAA,QAAA,QAAA,OAAA,GAAA,CAAA,SAAA,QAAA,GAAA,CAAA,GAAA,iBAAA,CAAA,GAAA,UAAA,SAAA,4BAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;AClB/B,QAAA,yBAAA,GAAA,YAAA,EAAY,GAAA,aAAA,EACG,GAAA,eAAA,CAAA;AAET,QAAA,oBAAA,GAAA,mBAAA,CAAA;AACF,QAAA,uBAAA;AACA,QAAA,yBAAA,GAAA,WAAA;AAAW,QAAA,iBAAA,GAAA,wBAAA;AAAmB,QAAA,uBAAA,EAAY,EAC9B;AAGhB,QAAA,yBAAA,GAAA,eAAA,CAAA;AAEE,QAAA,qBAAA,GAAA,mCAAA,GAAA,GAAA,OAAA,CAAA,EAA2D,GAAA,mCAAA,IAAA,IAAA,OAAA,CAAA,EAKxB,GAAA,wCAAA,GAAA,GAAA,YAAA,CAAA;AAsErC,QAAA,uBAAA;;;AA3EQ,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,SAAA;AAKA,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,CAAA,IAAA,aAAA,IAAA,OAAA;AAmEoB,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,CAAA,IAAA,aAAA,CAAA,IAAA,OAAA;;sBDnEhB,aAAW,UAAA,WAAA,YAAA,SAAA,gBAAA,eAAA,iBAAA,cAAA,YAAA,WAAA,SAAA,SAAA,UAAA,SAAA,YAAA,SAAA,UAAA,YAAA,eAAE,cAAY,MAAA,QAAA,GAAA,QAAA,CAAA,mpCAAA,EAAA,CAAA;AAE/B,IAAO,qBAAP;;0EAAO,oBAAkB,CAAA;cAP9B;2BACW,uBAAqB,YAGnB,MAAI,SACP,CAAC,aAAa,YAAY,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,g1BAAA,EAAA,CAAA;;;;iFAEzB,oBAAkB,EAAA,WAAA,sBAAA,UAAA,uEAAA,YAAA,GAAA,CAAA;IAAA,GAAA;;;", "names": []}