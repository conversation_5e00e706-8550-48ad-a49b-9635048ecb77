﻿using GoodDriver.Domain.Brands;
using GoodDriver.Domain.Models;
using GoodDriver.Domain.Users;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Domain.Vehicles
{
	public class Vehicle
	{
        protected Vehicle() //for NHibernate
        {
            
        }
        public Vehicle(string id)
        {
            this.Id = id;
            this.CreatedOn = DateTime.Now;
		}

        public Vehicle(string id, string plate, string year, User user, int brandId, int modelId) : this(id)
		{
            string platePattern = @"^[A-Z]{3}-\d{4}$";
			if (!System.Text.RegularExpressions.Regex.IsMatch(plate, platePattern))
				throw new ArgumentException("Invalid plate format. Expected format: AAA-0000");
			if (string.IsNullOrEmpty(year))
				throw new ArgumentNullException(nameof(year));

			this.Plate = plate;
			this.Year = year;
			this.User = user ?? throw new ArgumentNullException(nameof(user));
			this.Brand = new Brand(brandId) ?? throw new ArgumentNullException(nameof(brandId));
            this.Model = new Model(modelId) ?? throw new ArgumentNullException(nameof(modelId));
            this.UpdatedOn = DateTime.Now;
		}


        public string Id { get; private set; }

        public string Plate { get; private set; }

		public string Year { get; private set; }

        public User User { get; private set; }

        public Brand Brand { get; private set; }
		
        public Model Model { get; private set; }

		public string Version { get; private set; }

        public string PolicyNumber { get; private set; }

        public bool Active { get; private set; }

        public DateTime CreatedOn { get; private set; }

        public DateTime UpdatedOn { get; private set; }

        
    }
}
