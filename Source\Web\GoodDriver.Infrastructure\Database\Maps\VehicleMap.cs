﻿using FluentNHibernate.Mapping;
using GoodDriver.Domain.Vehicles;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Infrastructure.Database.Maps
{
	public class VehicleMap : ClassMap<Vehicle>
	{
        public VehicleMap()
        {
			Id(u => u.Id);
			
			References(a => a.User).Column("UserId").Not.Nullable().Fetch.Join().Cascade.None();
			References(a => a.Brand).Column("BrandId").Not.Nullable().Fetch.Join().Cascade.None();
			References(a => a.Model).Column("ModelId").Not.Nullable();
			Map(a => a.Year).Not.Nullable();
			Map(a => a.Plate).Not.Nullable();
			Map(a => a.Version).Nullable();
			Map(a => a.CreatedOn).Not.Nullable();
			Map(a => a.UpdatedOn).Nullable();
			Map(a => a.PolicyNumber).Nullable();
		}
    }
}
