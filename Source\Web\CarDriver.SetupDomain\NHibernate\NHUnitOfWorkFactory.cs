﻿using GoodDriver.SetupDomain.Data;
using GoodDriver.SetupDomain.UnitOfWork;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.SetupDomain.NHibernate
{
    public class NHUnitOfWorkFactory : IUnitOfWorkFactory<ISession>, IUnitOfWorkFactory
    {
        private string alias = "SESSION.HB.UNITOFWORK";

        private readonly IUnitOfWorkStore store;

        private readonly ISessionFactory sessionFactory;

        private readonly FlushMode flushMode;

        public NHUnitOfWorkFactory(IUnitOfWorkStore store, ISessionFactory sessionFactory, FlushMode flushMode = FlushMode.Auto)
        {
            if (store == null)
            {
                throw new ArgumentNullException("store");
            }

            if (sessionFactory == null)
            {
                throw new ArgumentNullException("sessionFactory");
            }

            this.store = store;
            this.sessionFactory = sessionFactory;
            this.flushMode = flushMode;
        }

        public void Dispose()
        {
            sessionFactory.Dispose();
        }

        public IUnitOfWork<ISession> Get(bool withTransaction = false, IsolationLevel? il = null)
        {
            NHUnitOfWork nHUnitOfWork = store.Get(alias) as NHUnitOfWork;
            if (nHUnitOfWork == null)
            {
                lock (this)
                {
                    nHUnitOfWork = store.Get(alias) as NHUnitOfWork;
                    if (nHUnitOfWork == null)
                    {
                        ISession session = sessionFactory.OpenSession();
                        session.FlushMode = flushMode;
                        nHUnitOfWork = new NHUnitOfWork(session);
                        nHUnitOfWork.Closed += unitOfWork_Closed;
                        store.Store(alias, nHUnitOfWork);
                    }
                }
            }

            lock (nHUnitOfWork)
            {
                nHUnitOfWork.Usages++;
                if (withTransaction)
                {
                    nHUnitOfWork.CreateTransactionIfNotExists(il);
                }
            }

            return nHUnitOfWork;
        }

        private void unitOfWork_Closed(object sender, EventArgs e)
        {
            store.Remove(alias);
        }

        IUnitOfWork IUnitOfWorkFactory.Get(bool withTransaction, IsolationLevel? il)
        {
            return Get(withTransaction, il);
        }
    }
}
