{"name": "zone.js", "version": "0.15.1", "description": "Zones for JavaScript", "main": "./bundles/zone.umd.js", "module": "./fesm2015/zone.js", "es2015": "./fesm2015/zone.js", "fesm2015": "./fesm2015/zone.js", "typings": "./zone.d.ts", "devDependencies": {"@externs/nodejs": "^1.5.0", "@types/node": "^10.9.4", "domino": "https://github.com/angular/domino.git#93e720f143d0296dd2726ffbcf4fc12283363a7b", "google-closure-compiler": "^20250519.0.0", "jest": "^29.0", "jest-environment-jsdom": "^29.0.3", "jest-environment-node": "^29.0.3", "mocha": "^11.0.0", "mock-require": "3.0.3", "tslib": "^2.3.0", "vitest": "^3.1.3"}, "scripts": {"electrontest": "cd test/extra && node electron.js", "jest:test": "jest --config ./test/jest/jest.config.js ./test/jest/jest.spec.js", "jest:nodetest": "jest --config ./test/jest/jest.node.config.js ./test/jest/jest.spec.js", "vitest:test": "vitest ./test/vitest/vitest.spec.js", "promisefinallytest": "mocha ./test/promise/promise.finally.spec.mjs"}, "repository": {"type": "git", "url": "git://github.com/angular/angular.git", "directory": "packages/zone.js"}, "publishConfig": {"registry": "https://wombat-dressing-room.appspot.com"}, "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular/issues"}, "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./zone.d.ts", "require": "./bundles/zone.umd.js", "default": "./fesm2015/zone.js"}, "./testing": {"require": "./bundles/zone-testing.umd.js", "default": "./fesm2015/zone-testing.js"}, "./node": {"require": "./bundles/zone-node.umd.js", "default": "./fesm2015/zone-node.js"}, "./mix": {"require": "./bundles/zone-mix.umd.js", "default": "./fesm2015/zone-mix.js"}, "./plugins/*.min": {"require": "./bundles/*.umd.min.js", "default": "./fesm2015/*.min.js"}, "./plugins/*": {"require": "./bundles/*.umd.js", "default": "./fesm2015/*.js"}}}