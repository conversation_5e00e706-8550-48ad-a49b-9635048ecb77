{"version": 3, "sources": ["node_modules/@ionic/core/components/animation.js", "node_modules/@ionic/core/components/index2.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { p as printIonError } from './index4.js';\nimport { w as win } from './index6.js';\nlet animationPrefix;\nconst getAnimationPrefix = el => {\n  if (animationPrefix === undefined) {\n    const supportsUnprefixed = el.style.animationName !== undefined;\n    const supportsWebkitPrefix = el.style.webkitAnimationName !== undefined;\n    animationPrefix = !supportsUnprefixed && supportsWebkitPrefix ? '-webkit-' : '';\n  }\n  return animationPrefix;\n};\nconst setStyleProperty = (element, propertyName, value) => {\n  const prefix = propertyName.startsWith('animation') ? getAnimationPrefix(element) : '';\n  element.style.setProperty(prefix + propertyName, value);\n};\nconst addClassToArray = (classes = [], className) => {\n  if (className !== undefined) {\n    const classNameToAppend = Array.isArray(className) ? className : [className];\n    return [...classes, ...classNameToAppend];\n  }\n  return classes;\n};\nconst createAnimation = animationId => {\n  let _delay;\n  let _duration;\n  let _easing;\n  let _iterations;\n  let _fill;\n  let _direction;\n  let _keyframes = [];\n  let beforeAddClasses = [];\n  let beforeRemoveClasses = [];\n  let initialized = false;\n  let parentAnimation;\n  let beforeStylesValue = {};\n  let afterAddClasses = [];\n  let afterRemoveClasses = [];\n  let afterStylesValue = {};\n  let numAnimationsRunning = 0;\n  let shouldForceLinearEasing = false;\n  let shouldForceSyncPlayback = false;\n  let forceDirectionValue;\n  let forceDurationValue;\n  let forceDelayValue;\n  let willComplete = true;\n  let finished = false;\n  let shouldCalculateNumAnimations = true;\n  let ani;\n  let paused = false;\n  const id = animationId;\n  const onFinishCallbacks = [];\n  const onFinishOneTimeCallbacks = [];\n  const onStopOneTimeCallbacks = [];\n  const elements = [];\n  const childAnimations = [];\n  const stylesheets = [];\n  const _beforeAddReadFunctions = [];\n  const _beforeAddWriteFunctions = [];\n  const _afterAddReadFunctions = [];\n  const _afterAddWriteFunctions = [];\n  const webAnimations = [];\n  const supportsAnimationEffect = typeof AnimationEffect === 'function' || win !== undefined && typeof win.AnimationEffect === 'function';\n  /**\n   * This is a feature detection for Web Animations.\n   *\n   * Certain environments such as emulated browser environments for testing,\n   * do not support Web Animations. As a result, we need to check for support\n   * and provide a fallback to test certain functionality related to Web Animations.\n   */\n  const supportsWebAnimations = typeof Element === 'function' && typeof Element.prototype.animate === 'function' && supportsAnimationEffect;\n  const getWebAnimations = () => {\n    return webAnimations;\n  };\n  const destroy = clearStyleSheets => {\n    childAnimations.forEach(childAnimation => {\n      childAnimation.destroy(clearStyleSheets);\n    });\n    cleanUp(clearStyleSheets);\n    elements.length = 0;\n    childAnimations.length = 0;\n    _keyframes.length = 0;\n    clearOnFinish();\n    initialized = false;\n    shouldCalculateNumAnimations = true;\n    return ani;\n  };\n  /**\n   * Cancels any Web Animations, removes\n   * any animation properties from the\n   * animation's elements, and removes the\n   * animation's stylesheets from the DOM.\n   */\n  const cleanUp = clearStyleSheets => {\n    cleanUpElements();\n    if (clearStyleSheets) {\n      cleanUpStyleSheets();\n    }\n  };\n  const resetFlags = () => {\n    shouldForceLinearEasing = false;\n    shouldForceSyncPlayback = false;\n    shouldCalculateNumAnimations = true;\n    forceDirectionValue = undefined;\n    forceDurationValue = undefined;\n    forceDelayValue = undefined;\n    numAnimationsRunning = 0;\n    finished = false;\n    willComplete = true;\n    paused = false;\n  };\n  const isRunning = () => {\n    return numAnimationsRunning !== 0 && !paused;\n  };\n  /**\n   * @internal\n   * Remove a callback from a chosen callback array\n   * @param callbackToRemove: A reference to the callback that should be removed\n   * @param callbackObjects: An array of callbacks that callbackToRemove should be removed from.\n   */\n  const clearCallback = (callbackToRemove, callbackObjects) => {\n    const index = callbackObjects.findIndex(callbackObject => callbackObject.c === callbackToRemove);\n    if (index > -1) {\n      callbackObjects.splice(index, 1);\n    }\n  };\n  /**\n   * @internal\n   * Add a callback to be fired when an animation is stopped/cancelled.\n   * @param callback: A reference to the callback that should be fired\n   * @param opts: Any options associated with this particular callback\n   */\n  const onStop = (callback, opts) => {\n    onStopOneTimeCallbacks.push({\n      c: callback,\n      o: opts\n    });\n    return ani;\n  };\n  const onFinish = (callback, opts) => {\n    const callbacks = (opts === null || opts === void 0 ? void 0 : opts.oneTimeCallback) ? onFinishOneTimeCallbacks : onFinishCallbacks;\n    callbacks.push({\n      c: callback,\n      o: opts\n    });\n    return ani;\n  };\n  const clearOnFinish = () => {\n    onFinishCallbacks.length = 0;\n    onFinishOneTimeCallbacks.length = 0;\n    return ani;\n  };\n  /**\n   * Cancels any Web Animations and removes\n   * any animation properties from the\n   * the animation's elements.\n   */\n  const cleanUpElements = () => {\n    if (supportsWebAnimations) {\n      webAnimations.forEach(animation => {\n        animation.cancel();\n      });\n      webAnimations.length = 0;\n    }\n  };\n  /**\n   * Removes the animation's stylesheets\n   * from the DOM.\n   */\n  const cleanUpStyleSheets = () => {\n    stylesheets.forEach(stylesheet => {\n      /**\n       * When sharing stylesheets, it's possible\n       * for another animation to have already\n       * cleaned up a particular stylesheet\n       */\n      if (stylesheet === null || stylesheet === void 0 ? void 0 : stylesheet.parentNode) {\n        stylesheet.parentNode.removeChild(stylesheet);\n      }\n    });\n    stylesheets.length = 0;\n  };\n  const beforeAddRead = readFn => {\n    _beforeAddReadFunctions.push(readFn);\n    return ani;\n  };\n  const beforeAddWrite = writeFn => {\n    _beforeAddWriteFunctions.push(writeFn);\n    return ani;\n  };\n  const afterAddRead = readFn => {\n    _afterAddReadFunctions.push(readFn);\n    return ani;\n  };\n  const afterAddWrite = writeFn => {\n    _afterAddWriteFunctions.push(writeFn);\n    return ani;\n  };\n  const beforeAddClass = className => {\n    beforeAddClasses = addClassToArray(beforeAddClasses, className);\n    return ani;\n  };\n  const beforeRemoveClass = className => {\n    beforeRemoveClasses = addClassToArray(beforeRemoveClasses, className);\n    return ani;\n  };\n  /**\n   * Set CSS inline styles to the animation's\n   * elements before the animation begins.\n   */\n  const beforeStyles = (styles = {}) => {\n    beforeStylesValue = styles;\n    return ani;\n  };\n  /**\n   * Clear CSS inline styles from the animation's\n   * elements before the animation begins.\n   */\n  const beforeClearStyles = (propertyNames = []) => {\n    for (const property of propertyNames) {\n      beforeStylesValue[property] = '';\n    }\n    return ani;\n  };\n  const afterAddClass = className => {\n    afterAddClasses = addClassToArray(afterAddClasses, className);\n    return ani;\n  };\n  const afterRemoveClass = className => {\n    afterRemoveClasses = addClassToArray(afterRemoveClasses, className);\n    return ani;\n  };\n  const afterStyles = (styles = {}) => {\n    afterStylesValue = styles;\n    return ani;\n  };\n  const afterClearStyles = (propertyNames = []) => {\n    for (const property of propertyNames) {\n      afterStylesValue[property] = '';\n    }\n    return ani;\n  };\n  const getFill = () => {\n    if (_fill !== undefined) {\n      return _fill;\n    }\n    if (parentAnimation) {\n      return parentAnimation.getFill();\n    }\n    return 'both';\n  };\n  const getDirection = () => {\n    if (forceDirectionValue !== undefined) {\n      return forceDirectionValue;\n    }\n    if (_direction !== undefined) {\n      return _direction;\n    }\n    if (parentAnimation) {\n      return parentAnimation.getDirection();\n    }\n    return 'normal';\n  };\n  const getEasing = () => {\n    if (shouldForceLinearEasing) {\n      return 'linear';\n    }\n    if (_easing !== undefined) {\n      return _easing;\n    }\n    if (parentAnimation) {\n      return parentAnimation.getEasing();\n    }\n    return 'linear';\n  };\n  const getDuration = () => {\n    if (shouldForceSyncPlayback) {\n      return 0;\n    }\n    if (forceDurationValue !== undefined) {\n      return forceDurationValue;\n    }\n    if (_duration !== undefined) {\n      return _duration;\n    }\n    if (parentAnimation) {\n      return parentAnimation.getDuration();\n    }\n    return 0;\n  };\n  const getIterations = () => {\n    if (_iterations !== undefined) {\n      return _iterations;\n    }\n    if (parentAnimation) {\n      return parentAnimation.getIterations();\n    }\n    return 1;\n  };\n  const getDelay = () => {\n    if (forceDelayValue !== undefined) {\n      return forceDelayValue;\n    }\n    if (_delay !== undefined) {\n      return _delay;\n    }\n    if (parentAnimation) {\n      return parentAnimation.getDelay();\n    }\n    return 0;\n  };\n  const getKeyframes = () => {\n    return _keyframes;\n  };\n  const direction = animationDirection => {\n    _direction = animationDirection;\n    update(true);\n    return ani;\n  };\n  const fill = animationFill => {\n    _fill = animationFill;\n    update(true);\n    return ani;\n  };\n  const delay = animationDelay => {\n    _delay = animationDelay;\n    update(true);\n    return ani;\n  };\n  const easing = animationEasing => {\n    _easing = animationEasing;\n    update(true);\n    return ani;\n  };\n  const duration = animationDuration => {\n    /**\n     * CSS Animation Durations of 0ms work fine on Chrome\n     * but do not run on Safari, so force it to 1ms to\n     * get it to run on both platforms.\n     */\n    if (!supportsWebAnimations && animationDuration === 0) {\n      animationDuration = 1;\n    }\n    _duration = animationDuration;\n    update(true);\n    return ani;\n  };\n  const iterations = animationIterations => {\n    _iterations = animationIterations;\n    update(true);\n    return ani;\n  };\n  const parent = animation => {\n    parentAnimation = animation;\n    return ani;\n  };\n  const addElement = el => {\n    if (el != null) {\n      if (el.nodeType === 1) {\n        elements.push(el);\n      } else if (el.length >= 0) {\n        for (let i = 0; i < el.length; i++) {\n          elements.push(el[i]);\n        }\n      } else {\n        printIonError('createAnimation - Invalid addElement value.');\n      }\n    }\n    return ani;\n  };\n  const addAnimation = animationToAdd => {\n    if (animationToAdd != null) {\n      if (Array.isArray(animationToAdd)) {\n        for (const animation of animationToAdd) {\n          animation.parent(ani);\n          childAnimations.push(animation);\n        }\n      } else {\n        animationToAdd.parent(ani);\n        childAnimations.push(animationToAdd);\n      }\n    }\n    return ani;\n  };\n  const keyframes = keyframeValues => {\n    const different = _keyframes !== keyframeValues;\n    _keyframes = keyframeValues;\n    if (different) {\n      updateKeyframes(_keyframes);\n    }\n    return ani;\n  };\n  const updateKeyframes = keyframeValues => {\n    if (supportsWebAnimations) {\n      getWebAnimations().forEach(animation => {\n        /**\n         * animation.effect's type is AnimationEffect.\n         * However, in this case we have a more specific\n         * type of AnimationEffect called KeyframeEffect which\n         * inherits from AnimationEffect. As a result,\n         * we cast animation.effect to KeyframeEffect.\n         */\n        const keyframeEffect = animation.effect;\n        /**\n         * setKeyframes is not supported in all browser\n         * versions that Ionic supports, so we need to\n         * check for support before using it.\n         */\n        // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n        if (keyframeEffect.setKeyframes) {\n          keyframeEffect.setKeyframes(keyframeValues);\n        } else {\n          const newEffect = new KeyframeEffect(keyframeEffect.target, keyframeValues, keyframeEffect.getTiming());\n          animation.effect = newEffect;\n        }\n      });\n    }\n  };\n  /**\n   * Run all \"before\" animation hooks.\n   */\n  const beforeAnimation = () => {\n    // Runs all before read callbacks\n    _beforeAddReadFunctions.forEach(callback => callback());\n    // Runs all before write callbacks\n    _beforeAddWriteFunctions.forEach(callback => callback());\n    // Updates styles and classes before animation runs\n    const addClasses = beforeAddClasses;\n    const removeClasses = beforeRemoveClasses;\n    const styles = beforeStylesValue;\n    elements.forEach(el => {\n      const elementClassList = el.classList;\n      addClasses.forEach(c => elementClassList.add(c));\n      removeClasses.forEach(c => elementClassList.remove(c));\n      for (const property in styles) {\n        // eslint-disable-next-line no-prototype-builtins\n        if (styles.hasOwnProperty(property)) {\n          setStyleProperty(el, property, styles[property]);\n        }\n      }\n    });\n  };\n  /**\n   * Run all \"after\" animation hooks.\n   */\n  const afterAnimation = () => {\n    // Runs all after read callbacks\n    _afterAddReadFunctions.forEach(callback => callback());\n    // Runs all after write callbacks\n    _afterAddWriteFunctions.forEach(callback => callback());\n    // Updates styles and classes before animation ends\n    const currentStep = willComplete ? 1 : 0;\n    const addClasses = afterAddClasses;\n    const removeClasses = afterRemoveClasses;\n    const styles = afterStylesValue;\n    elements.forEach(el => {\n      const elementClassList = el.classList;\n      addClasses.forEach(c => elementClassList.add(c));\n      removeClasses.forEach(c => elementClassList.remove(c));\n      for (const property in styles) {\n        // eslint-disable-next-line no-prototype-builtins\n        if (styles.hasOwnProperty(property)) {\n          setStyleProperty(el, property, styles[property]);\n        }\n      }\n    });\n    /**\n     * Clean up any value coercion before\n     * the user callbacks fire otherwise\n     * they may get stale values. For example,\n     * if someone calls progressStart(0) the\n     * animation may still be reversed.\n     */\n    forceDurationValue = undefined;\n    forceDirectionValue = undefined;\n    forceDelayValue = undefined;\n    onFinishCallbacks.forEach(onFinishCallback => {\n      return onFinishCallback.c(currentStep, ani);\n    });\n    onFinishOneTimeCallbacks.forEach(onFinishCallback => {\n      return onFinishCallback.c(currentStep, ani);\n    });\n    onFinishOneTimeCallbacks.length = 0;\n    shouldCalculateNumAnimations = true;\n    if (willComplete) {\n      finished = true;\n    }\n    willComplete = true;\n  };\n  const animationFinish = () => {\n    if (numAnimationsRunning === 0) {\n      return;\n    }\n    numAnimationsRunning--;\n    if (numAnimationsRunning === 0) {\n      afterAnimation();\n      if (parentAnimation) {\n        parentAnimation.animationFinish();\n      }\n    }\n  };\n  const initializeWebAnimation = () => {\n    elements.forEach(element => {\n      const animation = element.animate(_keyframes, {\n        id,\n        delay: getDelay(),\n        duration: getDuration(),\n        easing: getEasing(),\n        iterations: getIterations(),\n        fill: getFill(),\n        direction: getDirection()\n      });\n      animation.pause();\n      webAnimations.push(animation);\n    });\n    if (webAnimations.length > 0) {\n      webAnimations[0].onfinish = () => {\n        animationFinish();\n      };\n    }\n  };\n  const initializeAnimation = () => {\n    beforeAnimation();\n    if (_keyframes.length > 0) {\n      if (supportsWebAnimations) {\n        initializeWebAnimation();\n      }\n    }\n    initialized = true;\n  };\n  const setAnimationStep = step => {\n    step = Math.min(Math.max(step, 0), 0.9999);\n    if (supportsWebAnimations) {\n      webAnimations.forEach(animation => {\n        // When creating the animation the delay is guaranteed to be set to a number.\n        animation.currentTime = animation.effect.getComputedTiming().delay + getDuration() * step;\n        animation.pause();\n      });\n    }\n  };\n  const updateWebAnimation = step => {\n    webAnimations.forEach(animation => {\n      animation.effect.updateTiming({\n        delay: getDelay(),\n        duration: getDuration(),\n        easing: getEasing(),\n        iterations: getIterations(),\n        fill: getFill(),\n        direction: getDirection()\n      });\n    });\n    if (step !== undefined) {\n      setAnimationStep(step);\n    }\n  };\n  const update = (deep = false, toggleAnimationName = true, step) => {\n    if (deep) {\n      childAnimations.forEach(animation => {\n        animation.update(deep, toggleAnimationName, step);\n      });\n    }\n    if (supportsWebAnimations) {\n      updateWebAnimation(step);\n    }\n    return ani;\n  };\n  const progressStart = (forceLinearEasing = false, step) => {\n    childAnimations.forEach(animation => {\n      animation.progressStart(forceLinearEasing, step);\n    });\n    pauseAnimation();\n    shouldForceLinearEasing = forceLinearEasing;\n    if (!initialized) {\n      initializeAnimation();\n    }\n    update(false, true, step);\n    return ani;\n  };\n  const progressStep = step => {\n    childAnimations.forEach(animation => {\n      animation.progressStep(step);\n    });\n    setAnimationStep(step);\n    return ani;\n  };\n  const progressEnd = (playTo, step, dur) => {\n    shouldForceLinearEasing = false;\n    childAnimations.forEach(animation => {\n      animation.progressEnd(playTo, step, dur);\n    });\n    if (dur !== undefined) {\n      forceDurationValue = dur;\n    }\n    finished = false;\n    willComplete = true;\n    if (playTo === 0) {\n      forceDirectionValue = getDirection() === 'reverse' ? 'normal' : 'reverse';\n      if (forceDirectionValue === 'reverse') {\n        willComplete = false;\n      }\n      if (supportsWebAnimations) {\n        update();\n        setAnimationStep(1 - step);\n      } else {\n        forceDelayValue = (1 - step) * getDuration() * -1;\n        update(false, false);\n      }\n    } else if (playTo === 1) {\n      if (supportsWebAnimations) {\n        update();\n        setAnimationStep(step);\n      } else {\n        forceDelayValue = step * getDuration() * -1;\n        update(false, false);\n      }\n    }\n    if (playTo !== undefined && !parentAnimation) {\n      play();\n    }\n    return ani;\n  };\n  const pauseAnimation = () => {\n    if (initialized) {\n      if (supportsWebAnimations) {\n        webAnimations.forEach(animation => {\n          animation.pause();\n        });\n      } else {\n        elements.forEach(element => {\n          setStyleProperty(element, 'animation-play-state', 'paused');\n        });\n      }\n      paused = true;\n    }\n  };\n  const pause = () => {\n    childAnimations.forEach(animation => {\n      animation.pause();\n    });\n    pauseAnimation();\n    return ani;\n  };\n  const playCSSAnimations = () => {\n    animationFinish();\n  };\n  const playWebAnimations = () => {\n    webAnimations.forEach(animation => {\n      animation.play();\n    });\n    if (_keyframes.length === 0 || elements.length === 0) {\n      animationFinish();\n    }\n  };\n  const resetAnimation = () => {\n    if (supportsWebAnimations) {\n      setAnimationStep(0);\n      updateWebAnimation();\n    }\n  };\n  const play = opts => {\n    return new Promise(resolve => {\n      if (opts === null || opts === void 0 ? void 0 : opts.sync) {\n        shouldForceSyncPlayback = true;\n        onFinish(() => shouldForceSyncPlayback = false, {\n          oneTimeCallback: true\n        });\n      }\n      if (!initialized) {\n        initializeAnimation();\n      }\n      if (finished) {\n        resetAnimation();\n        finished = false;\n      }\n      if (shouldCalculateNumAnimations) {\n        numAnimationsRunning = childAnimations.length + 1;\n        shouldCalculateNumAnimations = false;\n      }\n      /**\n       * When one of these callbacks fires we\n       * need to clear the other's callback otherwise\n       * you can potentially get these callbacks\n       * firing multiple times if the play method\n       * is subsequently called.\n       * Example:\n       * animation.play() (onStop and onFinish callbacks are registered)\n       * animation.stop() (onStop callback is fired, onFinish is not)\n       * animation.play() (onStop and onFinish callbacks are registered)\n       * Total onStop callbacks: 1\n       * Total onFinish callbacks: 2\n       */\n      const onStopCallback = () => {\n        clearCallback(onFinishCallback, onFinishOneTimeCallbacks);\n        resolve();\n      };\n      const onFinishCallback = () => {\n        clearCallback(onStopCallback, onStopOneTimeCallbacks);\n        resolve();\n      };\n      /**\n       * The play method resolves when an animation\n       * run either finishes or is cancelled.\n       */\n      onFinish(onFinishCallback, {\n        oneTimeCallback: true\n      });\n      onStop(onStopCallback, {\n        oneTimeCallback: true\n      });\n      childAnimations.forEach(animation => {\n        animation.play();\n      });\n      if (supportsWebAnimations) {\n        playWebAnimations();\n      } else {\n        playCSSAnimations();\n      }\n      paused = false;\n    });\n  };\n  /**\n   * Stops an animation and resets it state to the\n   * beginning. This does not fire any onFinish\n   * callbacks because the animation did not finish.\n   * However, since the animation was not destroyed\n   * (i.e. the animation could run again) we do not\n   * clear the onFinish callbacks.\n   */\n  const stop = () => {\n    childAnimations.forEach(animation => {\n      animation.stop();\n    });\n    if (initialized) {\n      cleanUpElements();\n      initialized = false;\n    }\n    resetFlags();\n    onStopOneTimeCallbacks.forEach(onStopCallback => onStopCallback.c(0, ani));\n    onStopOneTimeCallbacks.length = 0;\n  };\n  const from = (property, value) => {\n    const firstFrame = _keyframes[0];\n    if (firstFrame !== undefined && (firstFrame.offset === undefined || firstFrame.offset === 0)) {\n      firstFrame[property] = value;\n    } else {\n      _keyframes = [{\n        offset: 0,\n        [property]: value\n      }, ..._keyframes];\n    }\n    return ani;\n  };\n  const to = (property, value) => {\n    const lastFrame = _keyframes[_keyframes.length - 1];\n    if (lastFrame !== undefined && (lastFrame.offset === undefined || lastFrame.offset === 1)) {\n      lastFrame[property] = value;\n    } else {\n      _keyframes = [..._keyframes, {\n        offset: 1,\n        [property]: value\n      }];\n    }\n    return ani;\n  };\n  const fromTo = (property, fromValue, toValue) => {\n    return from(property, fromValue).to(property, toValue);\n  };\n  return ani = {\n    parentAnimation,\n    elements,\n    childAnimations,\n    id,\n    animationFinish,\n    from,\n    to,\n    fromTo,\n    parent,\n    play,\n    pause,\n    stop,\n    destroy,\n    keyframes,\n    addAnimation,\n    addElement,\n    update,\n    fill,\n    direction,\n    iterations,\n    duration,\n    easing,\n    delay,\n    getWebAnimations,\n    getKeyframes,\n    getFill,\n    getDirection,\n    getDelay,\n    getIterations,\n    getEasing,\n    getDuration,\n    afterAddRead,\n    afterAddWrite,\n    afterClearStyles,\n    afterStyles,\n    afterRemoveClass,\n    afterAddClass,\n    beforeAddRead,\n    beforeAddWrite,\n    beforeClearStyles,\n    beforeStyles,\n    beforeRemoveClass,\n    beforeAddClass,\n    onFinish,\n    isRunning,\n    progressStart,\n    progressStep,\n    progressEnd\n  };\n};\nexport { createAnimation as c };", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as config, a as printIonWarning } from './index4.js';\nimport { writeTask, Build } from '@stencil/core/internal/client';\nimport { r as raf } from './helpers.js';\nconst LIFECYCLE_WILL_ENTER = 'ionViewWillEnter';\nconst LIFECYCLE_DID_ENTER = 'ionViewDidEnter';\nconst LIFECYCLE_WILL_LEAVE = 'ionViewWillLeave';\nconst LIFECYCLE_DID_LEAVE = 'ionViewDidLeave';\nconst LIFECYCLE_WILL_UNLOAD = 'ionViewWillUnload';\n\n/**\n * Moves focus to a specified element. Note that we do not remove the tabindex\n * because that can result in an unintentional blur. Non-focusables can't be\n * focused, so the body will get focused again.\n */\nconst moveFocus = el => {\n  el.tabIndex = -1;\n  el.focus();\n};\n/**\n * Elements that are hidden using `display: none` should not be focused even if\n * they are present in the DOM.\n */\nconst isVisible = el => {\n  return el.offsetParent !== null;\n};\n/**\n * The focus controller allows us to manage focus within a view so assistive\n * technologies can inform users of changes to the navigation state. Traditional\n * native apps have a way of informing assistive technology about a navigation\n * state change. Mobile browsers have this too, but only when doing a full page\n * load. In a single page app we do not do that, so we need to build this\n * integration ourselves.\n */\nconst createFocusController = () => {\n  const saveViewFocus = referenceEl => {\n    const focusManagerEnabled = config.get('focusManagerPriority', false);\n    /**\n     * When going back to a previously visited page focus should typically be moved\n     * back to the element that was last focused when the user was on this view.\n     */\n    if (focusManagerEnabled) {\n      const activeEl = document.activeElement;\n      if (activeEl !== null && (referenceEl === null || referenceEl === void 0 ? void 0 : referenceEl.contains(activeEl))) {\n        activeEl.setAttribute(LAST_FOCUS, 'true');\n      }\n    }\n  };\n  const setViewFocus = referenceEl => {\n    const focusManagerPriorities = config.get('focusManagerPriority', false);\n    /**\n     * If the focused element is a descendant of the referenceEl then it's possible\n     * that the app developer manually moved focus, so we do not want to override that.\n     * This can happen with inputs the are focused when a view transitions in.\n     */\n    if (Array.isArray(focusManagerPriorities) && !referenceEl.contains(document.activeElement)) {\n      /**\n       * When going back to a previously visited view focus should always be moved back\n       * to the element that the user was last focused on when they were on this view.\n       */\n      const lastFocus = referenceEl.querySelector(`[${LAST_FOCUS}]`);\n      if (lastFocus && isVisible(lastFocus)) {\n        moveFocus(lastFocus);\n        return;\n      }\n      for (const priority of focusManagerPriorities) {\n        /**\n         * For each recognized case (excluding the default case) make sure to return\n         * so that the fallback focus behavior does not run.\n         *\n         * We intentionally query for specific roles/semantic elements so that the\n         * transition manager can work with both Ionic and non-Ionic UI components.\n         *\n         * If new selectors are added, be sure to remove the outline ring by adding\n         * new selectors to rule in core.scss.\n         */\n        switch (priority) {\n          case 'content':\n            const content = referenceEl.querySelector('main, [role=\"main\"]');\n            if (content && isVisible(content)) {\n              moveFocus(content);\n              return;\n            }\n            break;\n          case 'heading':\n            const headingOne = referenceEl.querySelector('h1, [role=\"heading\"][aria-level=\"1\"]');\n            if (headingOne && isVisible(headingOne)) {\n              moveFocus(headingOne);\n              return;\n            }\n            break;\n          case 'banner':\n            const header = referenceEl.querySelector('header, [role=\"banner\"]');\n            if (header && isVisible(header)) {\n              moveFocus(header);\n              return;\n            }\n            break;\n          default:\n            printIonWarning(`Unrecognized focus manager priority value ${priority}`);\n            break;\n        }\n      }\n      /**\n       * If there is nothing to focus then focus the page so focus at least moves to\n       * the correct view. The browser will then determine where within the page to\n       * move focus to.\n       */\n      moveFocus(referenceEl);\n    }\n  };\n  return {\n    saveViewFocus,\n    setViewFocus\n  };\n};\nconst LAST_FOCUS = 'ion-last-focus';\nconst iosTransitionAnimation = () => import('./ios.transition.js');\nconst mdTransitionAnimation = () => import('./md.transition.js');\nconst focusController = createFocusController();\n// TODO(FW-2832): types\nconst transition = opts => {\n  return new Promise((resolve, reject) => {\n    writeTask(() => {\n      beforeTransition(opts);\n      runTransition(opts).then(result => {\n        if (result.animation) {\n          result.animation.destroy();\n        }\n        afterTransition(opts);\n        resolve(result);\n      }, error => {\n        afterTransition(opts);\n        reject(error);\n      });\n    });\n  });\n};\nconst beforeTransition = opts => {\n  const enteringEl = opts.enteringEl;\n  const leavingEl = opts.leavingEl;\n  focusController.saveViewFocus(leavingEl);\n  setZIndex(enteringEl, leavingEl, opts.direction);\n  if (opts.showGoBack) {\n    enteringEl.classList.add('can-go-back');\n  } else {\n    enteringEl.classList.remove('can-go-back');\n  }\n  setPageHidden(enteringEl, false);\n  /**\n   * When transitioning, the page should not\n   * respond to click events. This resolves small\n   * issues like users double tapping the ion-back-button.\n   * These pointer events are removed in `afterTransition`.\n   */\n  enteringEl.style.setProperty('pointer-events', 'none');\n  if (leavingEl) {\n    setPageHidden(leavingEl, false);\n    leavingEl.style.setProperty('pointer-events', 'none');\n  }\n};\nconst runTransition = async opts => {\n  const animationBuilder = await getAnimationBuilder(opts);\n  const ani = animationBuilder && Build.isBrowser ? animation(animationBuilder, opts) : noAnimation(opts); // fast path for no animation\n  return ani;\n};\nconst afterTransition = opts => {\n  const enteringEl = opts.enteringEl;\n  const leavingEl = opts.leavingEl;\n  enteringEl.classList.remove('ion-page-invisible');\n  enteringEl.style.removeProperty('pointer-events');\n  if (leavingEl !== undefined) {\n    leavingEl.classList.remove('ion-page-invisible');\n    leavingEl.style.removeProperty('pointer-events');\n  }\n  focusController.setViewFocus(enteringEl);\n};\nconst getAnimationBuilder = async opts => {\n  if (!opts.leavingEl || !opts.animated || opts.duration === 0) {\n    return undefined;\n  }\n  if (opts.animationBuilder) {\n    return opts.animationBuilder;\n  }\n  const getAnimation = opts.mode === 'ios' ? (await iosTransitionAnimation()).iosTransitionAnimation : (await mdTransitionAnimation()).mdTransitionAnimation;\n  return getAnimation;\n};\nconst animation = async (animationBuilder, opts) => {\n  await waitForReady(opts, true);\n  const trans = animationBuilder(opts.baseEl, opts);\n  fireWillEvents(opts.enteringEl, opts.leavingEl);\n  const didComplete = await playTransition(trans, opts);\n  if (opts.progressCallback) {\n    opts.progressCallback(undefined);\n  }\n  if (didComplete) {\n    fireDidEvents(opts.enteringEl, opts.leavingEl);\n  }\n  return {\n    hasCompleted: didComplete,\n    animation: trans\n  };\n};\nconst noAnimation = async opts => {\n  const enteringEl = opts.enteringEl;\n  const leavingEl = opts.leavingEl;\n  const focusManagerEnabled = config.get('focusManagerPriority', false);\n  /**\n   * If the focus manager is enabled then we need to wait for Ionic components to be\n   * rendered otherwise the component to focus may not be focused because it is hidden.\n   */\n  await waitForReady(opts, focusManagerEnabled);\n  fireWillEvents(enteringEl, leavingEl);\n  fireDidEvents(enteringEl, leavingEl);\n  return {\n    hasCompleted: true\n  };\n};\nconst waitForReady = async (opts, defaultDeep) => {\n  const deep = opts.deepWait !== undefined ? opts.deepWait : defaultDeep;\n  if (deep) {\n    await Promise.all([deepReady(opts.enteringEl), deepReady(opts.leavingEl)]);\n  }\n  await notifyViewReady(opts.viewIsReady, opts.enteringEl);\n};\nconst notifyViewReady = async (viewIsReady, enteringEl) => {\n  if (viewIsReady) {\n    await viewIsReady(enteringEl);\n  }\n};\nconst playTransition = (trans, opts) => {\n  const progressCallback = opts.progressCallback;\n  const promise = new Promise(resolve => {\n    trans.onFinish(currentStep => resolve(currentStep === 1));\n  });\n  // cool, let's do this, start the transition\n  if (progressCallback) {\n    // this is a swipe to go back, just get the transition progress ready\n    // kick off the swipe animation start\n    trans.progressStart(true);\n    progressCallback(trans);\n  } else {\n    // only the top level transition should actually start \"play\"\n    // kick it off and let it play through\n    // ******** DOM WRITE ****************\n    trans.play();\n  }\n  // create a callback for when the animation is done\n  return promise;\n};\nconst fireWillEvents = (enteringEl, leavingEl) => {\n  lifecycle(leavingEl, LIFECYCLE_WILL_LEAVE);\n  lifecycle(enteringEl, LIFECYCLE_WILL_ENTER);\n};\nconst fireDidEvents = (enteringEl, leavingEl) => {\n  lifecycle(enteringEl, LIFECYCLE_DID_ENTER);\n  lifecycle(leavingEl, LIFECYCLE_DID_LEAVE);\n};\nconst lifecycle = (el, eventName) => {\n  if (el) {\n    const ev = new CustomEvent(eventName, {\n      bubbles: false,\n      cancelable: false\n    });\n    el.dispatchEvent(ev);\n  }\n};\n/**\n * Wait two request animation frame loops.\n * This allows the framework implementations enough time to mount\n * the user-defined contents. This is often needed when using inline\n * modals and popovers that accept user components. For popover,\n * the contents must be mounted for the popover to be sized correctly.\n * For modals, the contents must be mounted for iOS to run the\n * transition correctly.\n *\n * On Angular and React, a single raf is enough time, but for Vue\n * we need to wait two rafs. As a result we are using two rafs for\n * all frameworks to ensure contents are mounted.\n */\nconst waitForMount = () => {\n  return new Promise(resolve => raf(() => raf(() => resolve())));\n};\nconst deepReady = async el => {\n  const element = el;\n  if (element) {\n    if (element.componentOnReady != null) {\n      // eslint-disable-next-line custom-rules/no-component-on-ready-method\n      const stencilEl = await element.componentOnReady();\n      if (stencilEl != null) {\n        return;\n      }\n      /**\n       * Custom elements in Stencil will have __registerHost.\n       */\n    } else if (element.__registerHost != null) {\n      /**\n       * Non-lazy loaded custom elements need to wait\n       * one frame for component to be loaded.\n       */\n      const waitForCustomElement = new Promise(resolve => raf(resolve));\n      await waitForCustomElement;\n      return;\n    }\n    await Promise.all(Array.from(element.children).map(deepReady));\n  }\n};\nconst setPageHidden = (el, hidden) => {\n  if (hidden) {\n    el.setAttribute('aria-hidden', 'true');\n    el.classList.add('ion-page-hidden');\n  } else {\n    el.hidden = false;\n    el.removeAttribute('aria-hidden');\n    el.classList.remove('ion-page-hidden');\n  }\n};\nconst setZIndex = (enteringEl, leavingEl, direction) => {\n  if (enteringEl !== undefined) {\n    enteringEl.style.zIndex = direction === 'back' ? '99' : '101';\n  }\n  if (leavingEl !== undefined) {\n    leavingEl.style.zIndex = '100';\n  }\n};\nconst getIonPageElement = element => {\n  if (element.classList.contains('ion-page')) {\n    return element;\n  }\n  const ionPage = element.querySelector(':scope > .ion-page, :scope > ion-nav, :scope > ion-tabs');\n  if (ionPage) {\n    return ionPage;\n  }\n  // idk, return the original element so at least something animates and we don't have a null pointer\n  return element;\n};\nexport { LIFECYCLE_WILL_ENTER as L, LIFECYCLE_DID_ENTER as a, LIFECYCLE_WILL_LEAVE as b, LIFECYCLE_DID_LEAVE as c, LIFECYCLE_WILL_UNLOAD as d, deepReady as e, getIonPageElement as g, lifecycle as l, setPageHidden as s, transition as t, waitForMount as w };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAKI,iBACE,oBAQA,kBAIA,iBAOA;AAzBN;AAAA;AAAA;AAGA;AACA;AAEA,IAAM,qBAAqB,QAAM;AAC/B,UAAI,oBAAoB,QAAW;AACjC,cAAM,qBAAqB,GAAG,MAAM,kBAAkB;AACtD,cAAM,uBAAuB,GAAG,MAAM,wBAAwB;AAC9D,0BAAkB,CAAC,sBAAsB,uBAAuB,aAAa;AAAA,MAC/E;AACA,aAAO;AAAA,IACT;AACA,IAAM,mBAAmB,CAAC,SAAS,cAAc,UAAU;AACzD,YAAM,SAAS,aAAa,WAAW,WAAW,IAAI,mBAAmB,OAAO,IAAI;AACpF,cAAQ,MAAM,YAAY,SAAS,cAAc,KAAK;AAAA,IACxD;AACA,IAAM,kBAAkB,CAAC,UAAU,CAAC,GAAG,cAAc;AACnD,UAAI,cAAc,QAAW;AAC3B,cAAM,oBAAoB,MAAM,QAAQ,SAAS,IAAI,YAAY,CAAC,SAAS;AAC3E,eAAO,CAAC,GAAG,SAAS,GAAG,iBAAiB;AAAA,MAC1C;AACA,aAAO;AAAA,IACT;AACA,IAAM,kBAAkB,iBAAe;AACrC,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,aAAa,CAAC;AAClB,UAAI,mBAAmB,CAAC;AACxB,UAAI,sBAAsB,CAAC;AAC3B,UAAI,cAAc;AAClB,UAAI;AACJ,UAAI,oBAAoB,CAAC;AACzB,UAAI,kBAAkB,CAAC;AACvB,UAAI,qBAAqB,CAAC;AAC1B,UAAI,mBAAmB,CAAC;AACxB,UAAI,uBAAuB;AAC3B,UAAI,0BAA0B;AAC9B,UAAI,0BAA0B;AAC9B,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,eAAe;AACnB,UAAI,WAAW;AACf,UAAI,+BAA+B;AACnC,UAAI;AACJ,UAAI,SAAS;AACb,YAAM,KAAK;AACX,YAAM,oBAAoB,CAAC;AAC3B,YAAM,2BAA2B,CAAC;AAClC,YAAM,yBAAyB,CAAC;AAChC,YAAM,WAAW,CAAC;AAClB,YAAM,kBAAkB,CAAC;AACzB,YAAM,cAAc,CAAC;AACrB,YAAM,0BAA0B,CAAC;AACjC,YAAM,2BAA2B,CAAC;AAClC,YAAM,yBAAyB,CAAC;AAChC,YAAM,0BAA0B,CAAC;AACjC,YAAM,gBAAgB,CAAC;AACvB,YAAM,0BAA0B,OAAO,oBAAoB,cAAc,QAAQ,UAAa,OAAO,IAAI,oBAAoB;AAQ7H,YAAM,wBAAwB,OAAO,YAAY,cAAc,OAAO,QAAQ,UAAU,YAAY,cAAc;AAClH,YAAM,mBAAmB,MAAM;AAC7B,eAAO;AAAA,MACT;AACA,YAAM,UAAU,sBAAoB;AAClC,wBAAgB,QAAQ,oBAAkB;AACxC,yBAAe,QAAQ,gBAAgB;AAAA,QACzC,CAAC;AACD,gBAAQ,gBAAgB;AACxB,iBAAS,SAAS;AAClB,wBAAgB,SAAS;AACzB,mBAAW,SAAS;AACpB,sBAAc;AACd,sBAAc;AACd,uCAA+B;AAC/B,eAAO;AAAA,MACT;AAOA,YAAM,UAAU,sBAAoB;AAClC,wBAAgB;AAChB,YAAI,kBAAkB;AACpB,6BAAmB;AAAA,QACrB;AAAA,MACF;AACA,YAAM,aAAa,MAAM;AACvB,kCAA0B;AAC1B,kCAA0B;AAC1B,uCAA+B;AAC/B,8BAAsB;AACtB,6BAAqB;AACrB,0BAAkB;AAClB,+BAAuB;AACvB,mBAAW;AACX,uBAAe;AACf,iBAAS;AAAA,MACX;AACA,YAAM,YAAY,MAAM;AACtB,eAAO,yBAAyB,KAAK,CAAC;AAAA,MACxC;AAOA,YAAM,gBAAgB,CAAC,kBAAkB,oBAAoB;AAC3D,cAAM,QAAQ,gBAAgB,UAAU,oBAAkB,eAAe,MAAM,gBAAgB;AAC/F,YAAI,QAAQ,IAAI;AACd,0BAAgB,OAAO,OAAO,CAAC;AAAA,QACjC;AAAA,MACF;AAOA,YAAM,SAAS,CAAC,UAAU,SAAS;AACjC,+BAAuB,KAAK;AAAA,UAC1B,GAAG;AAAA,UACH,GAAG;AAAA,QACL,CAAC;AACD,eAAO;AAAA,MACT;AACA,YAAM,WAAW,CAAC,UAAU,SAAS;AACnC,cAAM,aAAa,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,mBAAmB,2BAA2B;AAClH,kBAAU,KAAK;AAAA,UACb,GAAG;AAAA,UACH,GAAG;AAAA,QACL,CAAC;AACD,eAAO;AAAA,MACT;AACA,YAAM,gBAAgB,MAAM;AAC1B,0BAAkB,SAAS;AAC3B,iCAAyB,SAAS;AAClC,eAAO;AAAA,MACT;AAMA,YAAM,kBAAkB,MAAM;AAC5B,YAAI,uBAAuB;AACzB,wBAAc,QAAQ,CAAAA,eAAa;AACjC,YAAAA,WAAU,OAAO;AAAA,UACnB,CAAC;AACD,wBAAc,SAAS;AAAA,QACzB;AAAA,MACF;AAKA,YAAM,qBAAqB,MAAM;AAC/B,oBAAY,QAAQ,gBAAc;AAMhC,cAAI,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,YAAY;AACjF,uBAAW,WAAW,YAAY,UAAU;AAAA,UAC9C;AAAA,QACF,CAAC;AACD,oBAAY,SAAS;AAAA,MACvB;AACA,YAAM,gBAAgB,YAAU;AAC9B,gCAAwB,KAAK,MAAM;AACnC,eAAO;AAAA,MACT;AACA,YAAM,iBAAiB,aAAW;AAChC,iCAAyB,KAAK,OAAO;AACrC,eAAO;AAAA,MACT;AACA,YAAM,eAAe,YAAU;AAC7B,+BAAuB,KAAK,MAAM;AAClC,eAAO;AAAA,MACT;AACA,YAAM,gBAAgB,aAAW;AAC/B,gCAAwB,KAAK,OAAO;AACpC,eAAO;AAAA,MACT;AACA,YAAM,iBAAiB,eAAa;AAClC,2BAAmB,gBAAgB,kBAAkB,SAAS;AAC9D,eAAO;AAAA,MACT;AACA,YAAM,oBAAoB,eAAa;AACrC,8BAAsB,gBAAgB,qBAAqB,SAAS;AACpE,eAAO;AAAA,MACT;AAKA,YAAM,eAAe,CAAC,SAAS,CAAC,MAAM;AACpC,4BAAoB;AACpB,eAAO;AAAA,MACT;AAKA,YAAM,oBAAoB,CAAC,gBAAgB,CAAC,MAAM;AAChD,mBAAW,YAAY,eAAe;AACpC,4BAAkB,QAAQ,IAAI;AAAA,QAChC;AACA,eAAO;AAAA,MACT;AACA,YAAM,gBAAgB,eAAa;AACjC,0BAAkB,gBAAgB,iBAAiB,SAAS;AAC5D,eAAO;AAAA,MACT;AACA,YAAM,mBAAmB,eAAa;AACpC,6BAAqB,gBAAgB,oBAAoB,SAAS;AAClE,eAAO;AAAA,MACT;AACA,YAAM,cAAc,CAAC,SAAS,CAAC,MAAM;AACnC,2BAAmB;AACnB,eAAO;AAAA,MACT;AACA,YAAM,mBAAmB,CAAC,gBAAgB,CAAC,MAAM;AAC/C,mBAAW,YAAY,eAAe;AACpC,2BAAiB,QAAQ,IAAI;AAAA,QAC/B;AACA,eAAO;AAAA,MACT;AACA,YAAM,UAAU,MAAM;AACpB,YAAI,UAAU,QAAW;AACvB,iBAAO;AAAA,QACT;AACA,YAAI,iBAAiB;AACnB,iBAAO,gBAAgB,QAAQ;AAAA,QACjC;AACA,eAAO;AAAA,MACT;AACA,YAAM,eAAe,MAAM;AACzB,YAAI,wBAAwB,QAAW;AACrC,iBAAO;AAAA,QACT;AACA,YAAI,eAAe,QAAW;AAC5B,iBAAO;AAAA,QACT;AACA,YAAI,iBAAiB;AACnB,iBAAO,gBAAgB,aAAa;AAAA,QACtC;AACA,eAAO;AAAA,MACT;AACA,YAAM,YAAY,MAAM;AACtB,YAAI,yBAAyB;AAC3B,iBAAO;AAAA,QACT;AACA,YAAI,YAAY,QAAW;AACzB,iBAAO;AAAA,QACT;AACA,YAAI,iBAAiB;AACnB,iBAAO,gBAAgB,UAAU;AAAA,QACnC;AACA,eAAO;AAAA,MACT;AACA,YAAM,cAAc,MAAM;AACxB,YAAI,yBAAyB;AAC3B,iBAAO;AAAA,QACT;AACA,YAAI,uBAAuB,QAAW;AACpC,iBAAO;AAAA,QACT;AACA,YAAI,cAAc,QAAW;AAC3B,iBAAO;AAAA,QACT;AACA,YAAI,iBAAiB;AACnB,iBAAO,gBAAgB,YAAY;AAAA,QACrC;AACA,eAAO;AAAA,MACT;AACA,YAAM,gBAAgB,MAAM;AAC1B,YAAI,gBAAgB,QAAW;AAC7B,iBAAO;AAAA,QACT;AACA,YAAI,iBAAiB;AACnB,iBAAO,gBAAgB,cAAc;AAAA,QACvC;AACA,eAAO;AAAA,MACT;AACA,YAAM,WAAW,MAAM;AACrB,YAAI,oBAAoB,QAAW;AACjC,iBAAO;AAAA,QACT;AACA,YAAI,WAAW,QAAW;AACxB,iBAAO;AAAA,QACT;AACA,YAAI,iBAAiB;AACnB,iBAAO,gBAAgB,SAAS;AAAA,QAClC;AACA,eAAO;AAAA,MACT;AACA,YAAM,eAAe,MAAM;AACzB,eAAO;AAAA,MACT;AACA,YAAM,YAAY,wBAAsB;AACtC,qBAAa;AACb,eAAO,IAAI;AACX,eAAO;AAAA,MACT;AACA,YAAM,OAAO,mBAAiB;AAC5B,gBAAQ;AACR,eAAO,IAAI;AACX,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,oBAAkB;AAC9B,iBAAS;AACT,eAAO,IAAI;AACX,eAAO;AAAA,MACT;AACA,YAAM,SAAS,qBAAmB;AAChC,kBAAU;AACV,eAAO,IAAI;AACX,eAAO;AAAA,MACT;AACA,YAAM,WAAW,uBAAqB;AAMpC,YAAI,CAAC,yBAAyB,sBAAsB,GAAG;AACrD,8BAAoB;AAAA,QACtB;AACA,oBAAY;AACZ,eAAO,IAAI;AACX,eAAO;AAAA,MACT;AACA,YAAM,aAAa,yBAAuB;AACxC,sBAAc;AACd,eAAO,IAAI;AACX,eAAO;AAAA,MACT;AACA,YAAM,SAAS,CAAAA,eAAa;AAC1B,0BAAkBA;AAClB,eAAO;AAAA,MACT;AACA,YAAM,aAAa,QAAM;AACvB,YAAI,MAAM,MAAM;AACd,cAAI,GAAG,aAAa,GAAG;AACrB,qBAAS,KAAK,EAAE;AAAA,UAClB,WAAW,GAAG,UAAU,GAAG;AACzB,qBAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClC,uBAAS,KAAK,GAAG,CAAC,CAAC;AAAA,YACrB;AAAA,UACF,OAAO;AACL,0BAAc,6CAA6C;AAAA,UAC7D;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,eAAe,oBAAkB;AACrC,YAAI,kBAAkB,MAAM;AAC1B,cAAI,MAAM,QAAQ,cAAc,GAAG;AACjC,uBAAWA,cAAa,gBAAgB;AACtC,cAAAA,WAAU,OAAO,GAAG;AACpB,8BAAgB,KAAKA,UAAS;AAAA,YAChC;AAAA,UACF,OAAO;AACL,2BAAe,OAAO,GAAG;AACzB,4BAAgB,KAAK,cAAc;AAAA,UACrC;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,YAAY,oBAAkB;AAClC,cAAM,YAAY,eAAe;AACjC,qBAAa;AACb,YAAI,WAAW;AACb,0BAAgB,UAAU;AAAA,QAC5B;AACA,eAAO;AAAA,MACT;AACA,YAAM,kBAAkB,oBAAkB;AACxC,YAAI,uBAAuB;AACzB,2BAAiB,EAAE,QAAQ,CAAAA,eAAa;AAQtC,kBAAM,iBAAiBA,WAAU;AAOjC,gBAAI,eAAe,cAAc;AAC/B,6BAAe,aAAa,cAAc;AAAA,YAC5C,OAAO;AACL,oBAAM,YAAY,IAAI,eAAe,eAAe,QAAQ,gBAAgB,eAAe,UAAU,CAAC;AACtG,cAAAA,WAAU,SAAS;AAAA,YACrB;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAIA,YAAM,kBAAkB,MAAM;AAE5B,gCAAwB,QAAQ,cAAY,SAAS,CAAC;AAEtD,iCAAyB,QAAQ,cAAY,SAAS,CAAC;AAEvD,cAAM,aAAa;AACnB,cAAM,gBAAgB;AACtB,cAAM,SAAS;AACf,iBAAS,QAAQ,QAAM;AACrB,gBAAM,mBAAmB,GAAG;AAC5B,qBAAW,QAAQ,OAAK,iBAAiB,IAAI,CAAC,CAAC;AAC/C,wBAAc,QAAQ,OAAK,iBAAiB,OAAO,CAAC,CAAC;AACrD,qBAAW,YAAY,QAAQ;AAE7B,gBAAI,OAAO,eAAe,QAAQ,GAAG;AACnC,+BAAiB,IAAI,UAAU,OAAO,QAAQ,CAAC;AAAA,YACjD;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAIA,YAAM,iBAAiB,MAAM;AAE3B,+BAAuB,QAAQ,cAAY,SAAS,CAAC;AAErD,gCAAwB,QAAQ,cAAY,SAAS,CAAC;AAEtD,cAAM,cAAc,eAAe,IAAI;AACvC,cAAM,aAAa;AACnB,cAAM,gBAAgB;AACtB,cAAM,SAAS;AACf,iBAAS,QAAQ,QAAM;AACrB,gBAAM,mBAAmB,GAAG;AAC5B,qBAAW,QAAQ,OAAK,iBAAiB,IAAI,CAAC,CAAC;AAC/C,wBAAc,QAAQ,OAAK,iBAAiB,OAAO,CAAC,CAAC;AACrD,qBAAW,YAAY,QAAQ;AAE7B,gBAAI,OAAO,eAAe,QAAQ,GAAG;AACnC,+BAAiB,IAAI,UAAU,OAAO,QAAQ,CAAC;AAAA,YACjD;AAAA,UACF;AAAA,QACF,CAAC;AAQD,6BAAqB;AACrB,8BAAsB;AACtB,0BAAkB;AAClB,0BAAkB,QAAQ,sBAAoB;AAC5C,iBAAO,iBAAiB,EAAE,aAAa,GAAG;AAAA,QAC5C,CAAC;AACD,iCAAyB,QAAQ,sBAAoB;AACnD,iBAAO,iBAAiB,EAAE,aAAa,GAAG;AAAA,QAC5C,CAAC;AACD,iCAAyB,SAAS;AAClC,uCAA+B;AAC/B,YAAI,cAAc;AAChB,qBAAW;AAAA,QACb;AACA,uBAAe;AAAA,MACjB;AACA,YAAM,kBAAkB,MAAM;AAC5B,YAAI,yBAAyB,GAAG;AAC9B;AAAA,QACF;AACA;AACA,YAAI,yBAAyB,GAAG;AAC9B,yBAAe;AACf,cAAI,iBAAiB;AACnB,4BAAgB,gBAAgB;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AACA,YAAM,yBAAyB,MAAM;AACnC,iBAAS,QAAQ,aAAW;AAC1B,gBAAMA,aAAY,QAAQ,QAAQ,YAAY;AAAA,YAC5C;AAAA,YACA,OAAO,SAAS;AAAA,YAChB,UAAU,YAAY;AAAA,YACtB,QAAQ,UAAU;AAAA,YAClB,YAAY,cAAc;AAAA,YAC1B,MAAM,QAAQ;AAAA,YACd,WAAW,aAAa;AAAA,UAC1B,CAAC;AACD,UAAAA,WAAU,MAAM;AAChB,wBAAc,KAAKA,UAAS;AAAA,QAC9B,CAAC;AACD,YAAI,cAAc,SAAS,GAAG;AAC5B,wBAAc,CAAC,EAAE,WAAW,MAAM;AAChC,4BAAgB;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AACA,YAAM,sBAAsB,MAAM;AAChC,wBAAgB;AAChB,YAAI,WAAW,SAAS,GAAG;AACzB,cAAI,uBAAuB;AACzB,mCAAuB;AAAA,UACzB;AAAA,QACF;AACA,sBAAc;AAAA,MAChB;AACA,YAAM,mBAAmB,UAAQ;AAC/B,eAAO,KAAK,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM;AACzC,YAAI,uBAAuB;AACzB,wBAAc,QAAQ,CAAAA,eAAa;AAEjC,YAAAA,WAAU,cAAcA,WAAU,OAAO,kBAAkB,EAAE,QAAQ,YAAY,IAAI;AACrF,YAAAA,WAAU,MAAM;AAAA,UAClB,CAAC;AAAA,QACH;AAAA,MACF;AACA,YAAM,qBAAqB,UAAQ;AACjC,sBAAc,QAAQ,CAAAA,eAAa;AACjC,UAAAA,WAAU,OAAO,aAAa;AAAA,YAC5B,OAAO,SAAS;AAAA,YAChB,UAAU,YAAY;AAAA,YACtB,QAAQ,UAAU;AAAA,YAClB,YAAY,cAAc;AAAA,YAC1B,MAAM,QAAQ;AAAA,YACd,WAAW,aAAa;AAAA,UAC1B,CAAC;AAAA,QACH,CAAC;AACD,YAAI,SAAS,QAAW;AACtB,2BAAiB,IAAI;AAAA,QACvB;AAAA,MACF;AACA,YAAM,SAAS,CAAC,OAAO,OAAO,sBAAsB,MAAM,SAAS;AACjE,YAAI,MAAM;AACR,0BAAgB,QAAQ,CAAAA,eAAa;AACnC,YAAAA,WAAU,OAAO,MAAM,qBAAqB,IAAI;AAAA,UAClD,CAAC;AAAA,QACH;AACA,YAAI,uBAAuB;AACzB,6BAAmB,IAAI;AAAA,QACzB;AACA,eAAO;AAAA,MACT;AACA,YAAM,gBAAgB,CAAC,oBAAoB,OAAO,SAAS;AACzD,wBAAgB,QAAQ,CAAAA,eAAa;AACnC,UAAAA,WAAU,cAAc,mBAAmB,IAAI;AAAA,QACjD,CAAC;AACD,uBAAe;AACf,kCAA0B;AAC1B,YAAI,CAAC,aAAa;AAChB,8BAAoB;AAAA,QACtB;AACA,eAAO,OAAO,MAAM,IAAI;AACxB,eAAO;AAAA,MACT;AACA,YAAM,eAAe,UAAQ;AAC3B,wBAAgB,QAAQ,CAAAA,eAAa;AACnC,UAAAA,WAAU,aAAa,IAAI;AAAA,QAC7B,CAAC;AACD,yBAAiB,IAAI;AACrB,eAAO;AAAA,MACT;AACA,YAAM,cAAc,CAAC,QAAQ,MAAM,QAAQ;AACzC,kCAA0B;AAC1B,wBAAgB,QAAQ,CAAAA,eAAa;AACnC,UAAAA,WAAU,YAAY,QAAQ,MAAM,GAAG;AAAA,QACzC,CAAC;AACD,YAAI,QAAQ,QAAW;AACrB,+BAAqB;AAAA,QACvB;AACA,mBAAW;AACX,uBAAe;AACf,YAAI,WAAW,GAAG;AAChB,gCAAsB,aAAa,MAAM,YAAY,WAAW;AAChE,cAAI,wBAAwB,WAAW;AACrC,2BAAe;AAAA,UACjB;AACA,cAAI,uBAAuB;AACzB,mBAAO;AACP,6BAAiB,IAAI,IAAI;AAAA,UAC3B,OAAO;AACL,+BAAmB,IAAI,QAAQ,YAAY,IAAI;AAC/C,mBAAO,OAAO,KAAK;AAAA,UACrB;AAAA,QACF,WAAW,WAAW,GAAG;AACvB,cAAI,uBAAuB;AACzB,mBAAO;AACP,6BAAiB,IAAI;AAAA,UACvB,OAAO;AACL,8BAAkB,OAAO,YAAY,IAAI;AACzC,mBAAO,OAAO,KAAK;AAAA,UACrB;AAAA,QACF;AACA,YAAI,WAAW,UAAa,CAAC,iBAAiB;AAC5C,eAAK;AAAA,QACP;AACA,eAAO;AAAA,MACT;AACA,YAAM,iBAAiB,MAAM;AAC3B,YAAI,aAAa;AACf,cAAI,uBAAuB;AACzB,0BAAc,QAAQ,CAAAA,eAAa;AACjC,cAAAA,WAAU,MAAM;AAAA,YAClB,CAAC;AAAA,UACH,OAAO;AACL,qBAAS,QAAQ,aAAW;AAC1B,+BAAiB,SAAS,wBAAwB,QAAQ;AAAA,YAC5D,CAAC;AAAA,UACH;AACA,mBAAS;AAAA,QACX;AAAA,MACF;AACA,YAAM,QAAQ,MAAM;AAClB,wBAAgB,QAAQ,CAAAA,eAAa;AACnC,UAAAA,WAAU,MAAM;AAAA,QAClB,CAAC;AACD,uBAAe;AACf,eAAO;AAAA,MACT;AACA,YAAM,oBAAoB,MAAM;AAC9B,wBAAgB;AAAA,MAClB;AACA,YAAM,oBAAoB,MAAM;AAC9B,sBAAc,QAAQ,CAAAA,eAAa;AACjC,UAAAA,WAAU,KAAK;AAAA,QACjB,CAAC;AACD,YAAI,WAAW,WAAW,KAAK,SAAS,WAAW,GAAG;AACpD,0BAAgB;AAAA,QAClB;AAAA,MACF;AACA,YAAM,iBAAiB,MAAM;AAC3B,YAAI,uBAAuB;AACzB,2BAAiB,CAAC;AAClB,6BAAmB;AAAA,QACrB;AAAA,MACF;AACA,YAAM,OAAO,UAAQ;AACnB,eAAO,IAAI,QAAQ,aAAW;AAC5B,cAAI,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,MAAM;AACzD,sCAA0B;AAC1B,qBAAS,MAAM,0BAA0B,OAAO;AAAA,cAC9C,iBAAiB;AAAA,YACnB,CAAC;AAAA,UACH;AACA,cAAI,CAAC,aAAa;AAChB,gCAAoB;AAAA,UACtB;AACA,cAAI,UAAU;AACZ,2BAAe;AACf,uBAAW;AAAA,UACb;AACA,cAAI,8BAA8B;AAChC,mCAAuB,gBAAgB,SAAS;AAChD,2CAA+B;AAAA,UACjC;AAcA,gBAAM,iBAAiB,MAAM;AAC3B,0BAAc,kBAAkB,wBAAwB;AACxD,oBAAQ;AAAA,UACV;AACA,gBAAM,mBAAmB,MAAM;AAC7B,0BAAc,gBAAgB,sBAAsB;AACpD,oBAAQ;AAAA,UACV;AAKA,mBAAS,kBAAkB;AAAA,YACzB,iBAAiB;AAAA,UACnB,CAAC;AACD,iBAAO,gBAAgB;AAAA,YACrB,iBAAiB;AAAA,UACnB,CAAC;AACD,0BAAgB,QAAQ,CAAAA,eAAa;AACnC,YAAAA,WAAU,KAAK;AAAA,UACjB,CAAC;AACD,cAAI,uBAAuB;AACzB,8BAAkB;AAAA,UACpB,OAAO;AACL,8BAAkB;AAAA,UACpB;AACA,mBAAS;AAAA,QACX,CAAC;AAAA,MACH;AASA,YAAM,OAAO,MAAM;AACjB,wBAAgB,QAAQ,CAAAA,eAAa;AACnC,UAAAA,WAAU,KAAK;AAAA,QACjB,CAAC;AACD,YAAI,aAAa;AACf,0BAAgB;AAChB,wBAAc;AAAA,QAChB;AACA,mBAAW;AACX,+BAAuB,QAAQ,oBAAkB,eAAe,EAAE,GAAG,GAAG,CAAC;AACzE,+BAAuB,SAAS;AAAA,MAClC;AACA,YAAM,OAAO,CAAC,UAAU,UAAU;AAChC,cAAM,aAAa,WAAW,CAAC;AAC/B,YAAI,eAAe,WAAc,WAAW,WAAW,UAAa,WAAW,WAAW,IAAI;AAC5F,qBAAW,QAAQ,IAAI;AAAA,QACzB,OAAO;AACL,uBAAa,CAAC;AAAA,YACZ,QAAQ;AAAA,YACR,CAAC,QAAQ,GAAG;AAAA,UACd,GAAG,GAAG,UAAU;AAAA,QAClB;AACA,eAAO;AAAA,MACT;AACA,YAAM,KAAK,CAAC,UAAU,UAAU;AAC9B,cAAM,YAAY,WAAW,WAAW,SAAS,CAAC;AAClD,YAAI,cAAc,WAAc,UAAU,WAAW,UAAa,UAAU,WAAW,IAAI;AACzF,oBAAU,QAAQ,IAAI;AAAA,QACxB,OAAO;AACL,uBAAa,CAAC,GAAG,YAAY;AAAA,YAC3B,QAAQ;AAAA,YACR,CAAC,QAAQ,GAAG;AAAA,UACd,CAAC;AAAA,QACH;AACA,eAAO;AAAA,MACT;AACA,YAAM,SAAS,CAAC,UAAU,WAAW,YAAY;AAC/C,eAAO,KAAK,UAAU,SAAS,EAAE,GAAG,UAAU,OAAO;AAAA,MACvD;AACA,aAAO,MAAM;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACnzBA,IAMM,sBACA,qBACA,sBACA,qBACA,uBAOA,WAQA,WAWA,uBAkFA,YACA,wBACA,uBACA,iBAEA,YAiBA,kBAuBA,eAKA,iBAWA,qBAUA,WAgBA,aAeA,cAOA,iBAKA,gBAoBA,gBAIA,eAIA,WAsBA,cAGA,WAwBA,eAUA,WAQA;AAvUN;AAAA;AAAA;AAGA;AACA;AACA;AACA,IAAM,uBAAuB;AAC7B,IAAM,sBAAsB;AAC5B,IAAM,uBAAuB;AAC7B,IAAM,sBAAsB;AAC5B,IAAM,wBAAwB;AAO9B,IAAM,YAAY,QAAM;AACtB,SAAG,WAAW;AACd,SAAG,MAAM;AAAA,IACX;AAKA,IAAM,YAAY,QAAM;AACtB,aAAO,GAAG,iBAAiB;AAAA,IAC7B;AASA,IAAM,wBAAwB,MAAM;AAClC,YAAM,gBAAgB,iBAAe;AACnC,cAAM,sBAAsB,OAAO,IAAI,wBAAwB,KAAK;AAKpE,YAAI,qBAAqB;AACvB,gBAAM,WAAW,SAAS;AAC1B,cAAI,aAAa,SAAS,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,SAAS,QAAQ,IAAI;AACnH,qBAAS,aAAa,YAAY,MAAM;AAAA,UAC1C;AAAA,QACF;AAAA,MACF;AACA,YAAM,eAAe,iBAAe;AAClC,cAAM,yBAAyB,OAAO,IAAI,wBAAwB,KAAK;AAMvE,YAAI,MAAM,QAAQ,sBAAsB,KAAK,CAAC,YAAY,SAAS,SAAS,aAAa,GAAG;AAK1F,gBAAM,YAAY,YAAY,cAAc,IAAI,UAAU,GAAG;AAC7D,cAAI,aAAa,UAAU,SAAS,GAAG;AACrC,sBAAU,SAAS;AACnB;AAAA,UACF;AACA,qBAAW,YAAY,wBAAwB;AAW7C,oBAAQ,UAAU;AAAA,cAChB,KAAK;AACH,sBAAM,UAAU,YAAY,cAAc,qBAAqB;AAC/D,oBAAI,WAAW,UAAU,OAAO,GAAG;AACjC,4BAAU,OAAO;AACjB;AAAA,gBACF;AACA;AAAA,cACF,KAAK;AACH,sBAAM,aAAa,YAAY,cAAc,sCAAsC;AACnF,oBAAI,cAAc,UAAU,UAAU,GAAG;AACvC,4BAAU,UAAU;AACpB;AAAA,gBACF;AACA;AAAA,cACF,KAAK;AACH,sBAAM,SAAS,YAAY,cAAc,yBAAyB;AAClE,oBAAI,UAAU,UAAU,MAAM,GAAG;AAC/B,4BAAU,MAAM;AAChB;AAAA,gBACF;AACA;AAAA,cACF;AACE,gCAAgB,6CAA6C,QAAQ,EAAE;AACvE;AAAA,YACJ;AAAA,UACF;AAMA,oBAAU,WAAW;AAAA,QACvB;AAAA,MACF;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,IAAM,aAAa;AACnB,IAAM,yBAAyB,MAAM,OAAO,8BAAqB;AACjE,IAAM,wBAAwB,MAAM,OAAO,6BAAoB;AAC/D,IAAM,kBAAkB,sBAAsB;AAE9C,IAAM,aAAa,UAAQ;AACzB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,kBAAU,MAAM;AACd,2BAAiB,IAAI;AACrB,wBAAc,IAAI,EAAE,KAAK,YAAU;AACjC,gBAAI,OAAO,WAAW;AACpB,qBAAO,UAAU,QAAQ;AAAA,YAC3B;AACA,4BAAgB,IAAI;AACpB,oBAAQ,MAAM;AAAA,UAChB,GAAG,WAAS;AACV,4BAAgB,IAAI;AACpB,mBAAO,KAAK;AAAA,UACd,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,IAAM,mBAAmB,UAAQ;AAC/B,YAAM,aAAa,KAAK;AACxB,YAAM,YAAY,KAAK;AACvB,sBAAgB,cAAc,SAAS;AACvC,gBAAU,YAAY,WAAW,KAAK,SAAS;AAC/C,UAAI,KAAK,YAAY;AACnB,mBAAW,UAAU,IAAI,aAAa;AAAA,MACxC,OAAO;AACL,mBAAW,UAAU,OAAO,aAAa;AAAA,MAC3C;AACA,oBAAc,YAAY,KAAK;AAO/B,iBAAW,MAAM,YAAY,kBAAkB,MAAM;AACrD,UAAI,WAAW;AACb,sBAAc,WAAW,KAAK;AAC9B,kBAAU,MAAM,YAAY,kBAAkB,MAAM;AAAA,MACtD;AAAA,IACF;AACA,IAAM,gBAAgB,CAAM,SAAQ;AAClC,YAAM,mBAAmB,MAAM,oBAAoB,IAAI;AACvD,YAAM,MAAM,oBAAoB,MAAM,YAAY,UAAU,kBAAkB,IAAI,IAAI,YAAY,IAAI;AACtG,aAAO;AAAA,IACT;AACA,IAAM,kBAAkB,UAAQ;AAC9B,YAAM,aAAa,KAAK;AACxB,YAAM,YAAY,KAAK;AACvB,iBAAW,UAAU,OAAO,oBAAoB;AAChD,iBAAW,MAAM,eAAe,gBAAgB;AAChD,UAAI,cAAc,QAAW;AAC3B,kBAAU,UAAU,OAAO,oBAAoB;AAC/C,kBAAU,MAAM,eAAe,gBAAgB;AAAA,MACjD;AACA,sBAAgB,aAAa,UAAU;AAAA,IACzC;AACA,IAAM,sBAAsB,CAAM,SAAQ;AACxC,UAAI,CAAC,KAAK,aAAa,CAAC,KAAK,YAAY,KAAK,aAAa,GAAG;AAC5D,eAAO;AAAA,MACT;AACA,UAAI,KAAK,kBAAkB;AACzB,eAAO,KAAK;AAAA,MACd;AACA,YAAM,eAAe,KAAK,SAAS,SAAS,MAAM,uBAAuB,GAAG,0BAA0B,MAAM,sBAAsB,GAAG;AACrI,aAAO;AAAA,IACT;AACA,IAAM,YAAY,CAAO,kBAAkB,SAAS;AAClD,YAAM,aAAa,MAAM,IAAI;AAC7B,YAAM,QAAQ,iBAAiB,KAAK,QAAQ,IAAI;AAChD,qBAAe,KAAK,YAAY,KAAK,SAAS;AAC9C,YAAM,cAAc,MAAM,eAAe,OAAO,IAAI;AACpD,UAAI,KAAK,kBAAkB;AACzB,aAAK,iBAAiB,MAAS;AAAA,MACjC;AACA,UAAI,aAAa;AACf,sBAAc,KAAK,YAAY,KAAK,SAAS;AAAA,MAC/C;AACA,aAAO;AAAA,QACL,cAAc;AAAA,QACd,WAAW;AAAA,MACb;AAAA,IACF;AACA,IAAM,cAAc,CAAM,SAAQ;AAChC,YAAM,aAAa,KAAK;AACxB,YAAM,YAAY,KAAK;AACvB,YAAM,sBAAsB,OAAO,IAAI,wBAAwB,KAAK;AAKpE,YAAM,aAAa,MAAM,mBAAmB;AAC5C,qBAAe,YAAY,SAAS;AACpC,oBAAc,YAAY,SAAS;AACnC,aAAO;AAAA,QACL,cAAc;AAAA,MAChB;AAAA,IACF;AACA,IAAM,eAAe,CAAO,MAAM,gBAAgB;AAChD,YAAM,OAAO,KAAK,aAAa,SAAY,KAAK,WAAW;AAC3D,UAAI,MAAM;AACR,cAAM,QAAQ,IAAI,CAAC,UAAU,KAAK,UAAU,GAAG,UAAU,KAAK,SAAS,CAAC,CAAC;AAAA,MAC3E;AACA,YAAM,gBAAgB,KAAK,aAAa,KAAK,UAAU;AAAA,IACzD;AACA,IAAM,kBAAkB,CAAO,aAAa,eAAe;AACzD,UAAI,aAAa;AACf,cAAM,YAAY,UAAU;AAAA,MAC9B;AAAA,IACF;AACA,IAAM,iBAAiB,CAAC,OAAO,SAAS;AACtC,YAAM,mBAAmB,KAAK;AAC9B,YAAM,UAAU,IAAI,QAAQ,aAAW;AACrC,cAAM,SAAS,iBAAe,QAAQ,gBAAgB,CAAC,CAAC;AAAA,MAC1D,CAAC;AAED,UAAI,kBAAkB;AAGpB,cAAM,cAAc,IAAI;AACxB,yBAAiB,KAAK;AAAA,MACxB,OAAO;AAIL,cAAM,KAAK;AAAA,MACb;AAEA,aAAO;AAAA,IACT;AACA,IAAM,iBAAiB,CAAC,YAAY,cAAc;AAChD,gBAAU,WAAW,oBAAoB;AACzC,gBAAU,YAAY,oBAAoB;AAAA,IAC5C;AACA,IAAM,gBAAgB,CAAC,YAAY,cAAc;AAC/C,gBAAU,YAAY,mBAAmB;AACzC,gBAAU,WAAW,mBAAmB;AAAA,IAC1C;AACA,IAAM,YAAY,CAAC,IAAI,cAAc;AACnC,UAAI,IAAI;AACN,cAAM,KAAK,IAAI,YAAY,WAAW;AAAA,UACpC,SAAS;AAAA,UACT,YAAY;AAAA,QACd,CAAC;AACD,WAAG,cAAc,EAAE;AAAA,MACrB;AAAA,IACF;AAcA,IAAM,eAAe,MAAM;AACzB,aAAO,IAAI,QAAQ,aAAW,IAAI,MAAM,IAAI,MAAM,QAAQ,CAAC,CAAC,CAAC;AAAA,IAC/D;AACA,IAAM,YAAY,CAAM,OAAM;AAC5B,YAAM,UAAU;AAChB,UAAI,SAAS;AACX,YAAI,QAAQ,oBAAoB,MAAM;AAEpC,gBAAM,YAAY,MAAM,QAAQ,iBAAiB;AACjD,cAAI,aAAa,MAAM;AACrB;AAAA,UACF;AAAA,QAIF,WAAW,QAAQ,kBAAkB,MAAM;AAKzC,gBAAM,uBAAuB,IAAI,QAAQ,aAAW,IAAI,OAAO,CAAC;AAChE,gBAAM;AACN;AAAA,QACF;AACA,cAAM,QAAQ,IAAI,MAAM,KAAK,QAAQ,QAAQ,EAAE,IAAI,SAAS,CAAC;AAAA,MAC/D;AAAA,IACF;AACA,IAAM,gBAAgB,CAAC,IAAI,WAAW;AACpC,UAAI,QAAQ;AACV,WAAG,aAAa,eAAe,MAAM;AACrC,WAAG,UAAU,IAAI,iBAAiB;AAAA,MACpC,OAAO;AACL,WAAG,SAAS;AACZ,WAAG,gBAAgB,aAAa;AAChC,WAAG,UAAU,OAAO,iBAAiB;AAAA,MACvC;AAAA,IACF;AACA,IAAM,YAAY,CAAC,YAAY,WAAW,cAAc;AACtD,UAAI,eAAe,QAAW;AAC5B,mBAAW,MAAM,SAAS,cAAc,SAAS,OAAO;AAAA,MAC1D;AACA,UAAI,cAAc,QAAW;AAC3B,kBAAU,MAAM,SAAS;AAAA,MAC3B;AAAA,IACF;AACA,IAAM,oBAAoB,aAAW;AACnC,UAAI,QAAQ,UAAU,SAAS,UAAU,GAAG;AAC1C,eAAO;AAAA,MACT;AACA,YAAM,UAAU,QAAQ,cAAc,yDAAyD;AAC/F,UAAI,SAAS;AACX,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;", "names": ["animation"], "x_google_ignoreList": [0, 1]}