{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/watch-options-c2911ace.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst watchForOptions = (containerEl, tagName, onChange) => {\n  if (typeof MutationObserver === 'undefined') {\n    return;\n  }\n  const mutation = new MutationObserver(mutationList => {\n    onChange(getSelectedOption(mutationList, tagName));\n  });\n  mutation.observe(containerEl, {\n    childList: true,\n    subtree: true\n  });\n  return mutation;\n};\nconst getSelectedOption = (mutationList, tagName) => {\n  let newOption;\n  mutationList.forEach(mut => {\n    // eslint-disable-next-line @typescript-eslint/prefer-for-of\n    for (let i = 0; i < mut.addedNodes.length; i++) {\n      newOption = findCheckedOption(mut.addedNodes[i], tagName) || newOption;\n    }\n  });\n  return newOption;\n};\n/**\n * The \"value\" key is only set on some components such as ion-select-option.\n * As a result, we create a default union type of HTMLElement and the \"value\" key.\n * However, implementers are required to provide the appropriate component type\n * such as HTMLIonSelectOptionElement.\n */\nconst findCheckedOption = (node, tagName) => {\n  /**\n   * https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\n   * The above check ensures \"node\" is an Element (nodeType 1).\n   */\n  if (node.nodeType !== 1) {\n    return undefined;\n  }\n  // HTMLElement inherits from Element, so we cast \"el\" as T.\n  const el = node;\n  const options = el.tagName === tagName.toUpperCase() ? [el] : Array.from(el.querySelectorAll(tagName));\n  return options.find(o => o.value === el.value);\n};\nexport { watchForOptions as w };"], "mappings": ";;;;;AAAA,IAGM,iBAaA,mBAgBA;AAhCN;AAAA;AAAA;AAGA,IAAM,kBAAkB,CAAC,aAAa,SAAS,aAAa;AAC1D,UAAI,OAAO,qBAAqB,aAAa;AAC3C;AAAA,MACF;AACA,YAAM,WAAW,IAAI,iBAAiB,kBAAgB;AACpD,iBAAS,kBAAkB,cAAc,OAAO,CAAC;AAAA,MACnD,CAAC;AACD,eAAS,QAAQ,aAAa;AAAA,QAC5B,WAAW;AAAA,QACX,SAAS;AAAA,MACX,CAAC;AACD,aAAO;AAAA,IACT;AACA,IAAM,oBAAoB,CAAC,cAAc,YAAY;AACnD,UAAI;AACJ,mBAAa,QAAQ,SAAO;AAE1B,iBAAS,IAAI,GAAG,IAAI,IAAI,WAAW,QAAQ,KAAK;AAC9C,sBAAY,kBAAkB,IAAI,WAAW,CAAC,GAAG,OAAO,KAAK;AAAA,QAC/D;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAOA,IAAM,oBAAoB,CAAC,MAAM,YAAY;AAK3C,UAAI,KAAK,aAAa,GAAG;AACvB,eAAO;AAAA,MACT;AAEA,YAAM,KAAK;AACX,YAAM,UAAU,GAAG,YAAY,QAAQ,YAAY,IAAI,CAAC,EAAE,IAAI,MAAM,KAAK,GAAG,iBAAiB,OAAO,CAAC;AACrG,aAAO,QAAQ,KAAK,OAAK,EAAE,UAAU,GAAG,KAAK;AAAA,IAC/C;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}