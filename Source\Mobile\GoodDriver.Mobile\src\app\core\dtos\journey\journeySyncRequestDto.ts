export interface JourneySyncRequestDto {
    id: string;
    startDate: string;
    endDate: string | null;
    distance: number;
    userId: string;
    vehicleId?: string;
    infosJourney?: JourneyInfoSyncRequestDto[];
  }

  export interface JourneyInfoSyncRequestDto {
    id: string;
    journeyId: string;
    latitude: number;
    longitude: number;
    timestamp: string;
    address?: string;
    occurrenceType?: 'HardBreak' | 'Acceleration' | 'turn' | 'speeding' | 'stop' | 'Collision' | 'PhoneUse' | 'other';
  }
