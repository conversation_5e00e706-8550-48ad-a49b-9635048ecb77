﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Common;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GoodDriver.SetupDomain.Data.ConnectionStrings;

namespace GoodDriver.SetupDomain.Data
{
    public class BaseDataAccess
    {
        protected readonly IConfiguration configuration;

        public BaseDataAccess(IConfiguration configuration)
        {
            if (configuration == null)
            {
                throw new ArgumentNullException("configuration");
            }

            this.configuration = configuration;
        }

        protected IDbConnection CreateConnection(string connectionStringName = null)
        {
            ConnectionStringSettings connectionStringByName = GetConnectionStringByName(connectionStringName);
            DbConnection? dbConnection = DbProviderFactories.GetFactory(connectionStringByName.ProviderName).CreateConnection();
            dbConnection.ConnectionString = connectionStringByName.ConnectionString;
            return dbConnection;
        }

        protected IDbConnection CreateConnection(ConnectionStringSettings ccString)
        {
            if (ccString == null)
            {
                ccString = configuration.ConnectionStrings().Default;
            }

            DbConnection? dbConnection = DbProviderFactories.GetFactory(ccString.ProviderName).CreateConnection();
            dbConnection.ConnectionString = ccString.ConnectionString;
            return dbConnection;
        }

        protected ConnectionStringSettings GetConnectionStringByName(string name)
        {
            ConnectionStrings.ConnectionStringSettingsCollection connectionStringSettingsCollection = configuration.ConnectionStrings();
            if (connectionStringSettingsCollection != null)
            {
                if (string.IsNullOrEmpty(name))
                {
                    return connectionStringSettingsCollection.Default;
                }

                foreach (ConnectionStringSettings value in connectionStringSettingsCollection.Values)
                {
                    if (string.Compare(value.Name, name, ignoreCase: true) == 0)
                    {
                        return value;
                    }
                }
            }

            return null;
        }

        protected virtual void AddParameter(IDbCommand command, string name, object value)
        {
            AddParameter(command, name, value, null);
        }

        protected virtual void AddParameter(IDbCommand command, string name, object value, DbType? type)
        {
            IDbDataParameter dbDataParameter = command.CreateParameter();
            string parameterPrefix = GetParameterPrefix(command.Connection);
            if (!name.StartsWith(parameterPrefix))
            {
                name = parameterPrefix + name;
            }

            dbDataParameter.ParameterName = name;
            if (value != null)
            {
                dbDataParameter.Value = value;
            }
            else
            {
                dbDataParameter.Value = DBNull.Value;
            }

            if (type.HasValue)
            {
                dbDataParameter.DbType = type.Value;
            }

            command.Parameters.Add(dbDataParameter);
        }

        public static string GetParameterPrefix(IDbConnection connection)
        {
            if (!connection.GetType().Name.Contains("Sql"))
            {
                return ":";
            }

            return "@";
        }
    }
}
