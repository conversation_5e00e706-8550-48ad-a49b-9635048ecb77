﻿using GoodDriver.Contracts.Users.Commands;
using Rogerio.Commom;
using Rogerio.Commom.Exceptions;
using Rogerio.Data;

namespace GoodDriver.Domain.Users
{
    public class UserFactory : IUserFactory
	{
		private readonly IUnitOfWorkFactory _unitOfWorkFactory;
		private readonly IUserRepository _userRepository;

		public UserFactory(IUserRepository userRepository, IUnitOfWorkFactory unitOfWorkFactory)
        {
            this._userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
			this._unitOfWorkFactory = unitOfWorkFactory ?? throw new ArgumentNullException(nameof(unitOfWorkFactory));
		}

        public async Task<User> CreateAsync(UserCreateCommand command)
		{
			if (command == null)
				throw new ArgumentNullException(nameof(command));

			ValidateUserCreateCommand(command);
			await EnsureUserDoesNotExistAsync(command);
			return new User(command.Name, command.Phone, command.Email, command.Password, command.InformationResponsibilityChecked);
		}

		private async Task EnsureUserDoesNotExistAsync(UserCreateCommand command)
		{
			var existsEmail = await _userRepository.GetAsyncBy(u => u.Email == command.Email);
			//var existsCPF = await _userRepository.GetAsyncBy(u => u.CPF == Common.Util.UnMaskDocument(command.CPF));

			Throw.IsNotNull(existsEmail, "User.AlreadyCreated", "Já existe um usuário cadastrado com esse E-mail.");
			//Throw.IsNotNull(existsCPF, "User.AlreadyCreated", "Já existe um usuário cadastrado com esse CPF.");
		}


		private static void ValidateUserCreateCommand(UserCreateCommand command)
		{
			Throw.ArgumentIsNull(command, nameof(command));

			if (command.Password != command.PasswordConfirm)
				throw new BusinessException("Invalid.Password", "As senhas digitadas não são iguais.");
		}
	}
}
