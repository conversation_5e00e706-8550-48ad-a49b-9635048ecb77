import {
  init_esm_browser,
  v4_default
} from "./chunk-ZCUEDWU7.js";
import {
  ApiService,
  BehaviorSubject,
  DataStorageService,
  HttpClient,
  Injectable,
  NetworkService,
  Platform,
  SessionService,
  SyncStatus,
  VehicleService,
  init_api_service,
  init_core,
  init_data_storage_service,
  init_esm,
  init_http,
  init_ionic_angular,
  init_network_service,
  init_session_service,
  init_sync_model,
  init_vehicle_service,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-EC6CHFTM.js";
import {
  init_dist,
  registerPlugin
} from "./chunk-WY354WYL.js";
import {
  __async,
  __esm,
  __spreadProps,
  __spreadValues
} from "./chunk-4W6HR7MY.js";

// node_modules/@capacitor/synapse/dist/synapse.mjs
function s(t) {
  t.CapacitorUtils.Synapse = new Proxy({}, {
    get(e, n) {
      return new Proxy({}, {
        get(w, o) {
          return (c, p, r) => {
            const i = t.Capacitor.Plugins[n];
            if (i === void 0) {
              r(new Error(`Capacitor plugin ${n} not found`));
              return;
            }
            if (typeof i[o] != "function") {
              r(new Error(`Method ${o} not found in Capacitor plugin ${n}`));
              return;
            }
            (() => __async(null, null, function* () {
              try {
                const a = yield i[o](c);
                p(a);
              } catch (a) {
                r(a);
              }
            }))();
          };
        }
      });
    }
  });
}
function u(t) {
  t.CapacitorUtils.Synapse = new Proxy({}, {
    get(e, n) {
      return t.cordova.plugins[n];
    }
  });
}
function f(t = false) {
  typeof window > "u" || (window.CapacitorUtils = window.CapacitorUtils || {}, window.Capacitor !== void 0 && !t ? s(window) : window.cordova !== void 0 && u(window));
}
var init_synapse = __esm({
  "node_modules/@capacitor/synapse/dist/synapse.mjs"() {
    "use strict";
  }
});

// node_modules/@capacitor/geolocation/dist/esm/definitions.js
var init_definitions = __esm({
  "node_modules/@capacitor/geolocation/dist/esm/definitions.js"() {
    "use strict";
  }
});

// node_modules/@capacitor/geolocation/dist/esm/index.js
var Geolocation;
var init_esm2 = __esm({
  "node_modules/@capacitor/geolocation/dist/esm/index.js"() {
    "use strict";
    init_dist();
    init_synapse();
    init_definitions();
    Geolocation = registerPlugin("Geolocation", {
      web: () => import("./web-B46OEUWN.js").then((m) => new m.GeolocationWeb())
    });
    f();
  }
});

// node_modules/@capacitor/motion/dist/esm/definitions.js
var init_definitions2 = __esm({
  "node_modules/@capacitor/motion/dist/esm/definitions.js"() {
    "use strict";
  }
});

// node_modules/@capacitor/motion/dist/esm/index.js
var Motion;
var init_esm3 = __esm({
  "node_modules/@capacitor/motion/dist/esm/index.js"() {
    "use strict";
    init_dist();
    init_definitions2();
    Motion = registerPlugin("Motion", {
      android: () => import("./web-B4UY27XM.js").then((m) => new m.MotionWeb()),
      ios: () => import("./web-B4UY27XM.js").then((m) => new m.MotionWeb()),
      web: () => import("./web-B4UY27XM.js").then((m) => new m.MotionWeb())
    });
  }
});

// src/app/core/services/journeyinfo.service.ts
var _JourneyInfoService, JourneyInfoService;
var init_journeyinfo_service = __esm({
  "src/app/core/services/journeyinfo.service.ts"() {
    "use strict";
    init_core();
    init_esm2();
    init_esm3();
    init_esm();
    init_core();
    init_data_storage_service();
    _JourneyInfoService = class _JourneyInfoService {
      constructor(dataStorageService) {
        this.dataStorageService = dataStorageService;
        this.journeyId = "";
        this.trackingInterval = null;
        this.trackingStartTime = 0;
        this.totalTimeAboveSpeed = 0;
        this.previousSpeed = 0;
        this.previousSpeedTime = 0;
        this.trackingActiveSubject = new BehaviorSubject(false);
        this.trackingActive$ = this.trackingActiveSubject.asObservable();
        this.storage = this.dataStorageService;
      }
      // Função para iniciar o rastreamento
      startTracking(journeyId) {
        this.trackingStartTime = Date.now();
        this.totalTimeAboveSpeed = 0;
        this.journeyId = journeyId;
        this.previousSpeed = 0;
        this.previousSpeedTime = 0;
        this.trackingActiveSubject.next(true);
        this.trackingInterval = setInterval(() => __async(this, null, function* () {
          try {
            console.log("Executando ciclo de rastreamento...");
            const location = yield this.getCurrentLocation();
            if (location) {
              console.log("Localiza\xE7\xE3o obtida:", location);
              try {
                console.log("Verificando freada brusca...");
                const hardBreakDetected = yield this.checkHardBreak(location);
                console.log("Resultado checkHardBreak:", hardBreakDetected);
              } catch (error) {
                console.error("Erro em checkHardBreak:", error);
              }
              try {
                console.log("Verificando alta velocidade...");
                const highVelocityDetected = yield this.checkHighVelocity(location);
                console.log("Resultado checkHighVelocity:", highVelocityDetected);
              } catch (error) {
                console.error("Erro em checkHighVelocity:", error);
              }
              try {
                console.log("Salvando localiza\xE7\xE3o...");
                yield this.saveLocation(location);
                console.log("Localiza\xE7\xE3o salva com sucesso");
              } catch (error) {
                console.error("Erro ao salvar localiza\xE7\xE3o:", error);
              }
            } else {
              console.log("N\xE3o foi poss\xEDvel obter localiza\xE7\xE3o");
            }
          } catch (error) {
            console.error("Erro no ciclo de rastreamento:", error);
          }
        }), 1e4);
        console.log("Rastreamento iniciado para viagem:", journeyId);
      }
      // Função para parar o rastreamento
      stopTracking(journeyId) {
        if (this.trackingInterval) {
          clearInterval(this.trackingInterval);
          this.trackingInterval = null;
          this.trackingActiveSubject.next(false);
          console.log("Rastreamento parado para viagem:", journeyId);
        }
      }
      // Função para obter a localização atual
      getCurrentLocation() {
        return __async(this, null, function* () {
          try {
            const coordinates = yield Geolocation.getCurrentPosition({
              enableHighAccuracy: true,
              timeout: 1e4
            });
            const location = {
              id: this.generateUniqueId(),
              journeyId: this.journeyId,
              latitude: coordinates.coords.latitude,
              longitude: coordinates.coords.longitude,
              timestamp: (/* @__PURE__ */ new Date()).toISOString()
              // Marca o horário atual
            };
            return location;
          } catch (error) {
            console.error("Erro ao obter a localiza\xE7\xE3o:", error);
            return null;
          }
        });
      }
      // Função para obter a velocidade atual (em km/h)
      getCurrentSpeed() {
        return __async(this, null, function* () {
          try {
            const position = yield Geolocation.getCurrentPosition();
            const speed = position.coords.speed ? position.coords.speed * 3.6 : 0;
            return speed;
          } catch (error) {
            console.error("Erro ao obter a velocidade:", error);
            return 0;
          }
        });
      }
      // Função para salvar a localização no banco de dados
      saveLocation(location) {
        return __async(this, null, function* () {
          try {
            yield this.storage.insert("journeyInfo", location);
            console.log("Localiza\xE7\xE3o salva:", location);
          } catch (error) {
            console.error("Erro ao salvar a localiza\xE7\xE3o:", error);
          }
        });
      }
      // Função para gerar um ID único para cada localização (pode ser melhorada)
      generateUniqueId() {
        return Math.random().toString(36).substring(2, 15);
      }
      checkHardBreak(location) {
        return __async(this, null, function* () {
          try {
            const currentSpeed = yield this.getCurrentSpeed();
            const currentTime = Date.now();
            if (this.previousSpeed > 0 && this.previousSpeedTime > 0) {
              const speedDifference = this.previousSpeed - currentSpeed;
              const timeDifference = (currentTime - this.previousSpeedTime) / 1e3;
              if (timeDifference > 0) {
                const deceleration = speedDifference / timeDifference;
                console.log(`Velocidade anterior: ${this.previousSpeed} km/h, Atual: ${currentSpeed} km/h`);
                console.log(`Desacelera\xE7\xE3o: ${deceleration} km/h/s`);
                if (deceleration > 15) {
                  console.log("Freada brusca detectada por velocidade!");
                  location.occurrenceType = "HardBreak";
                  yield this.saveLocation(location);
                  this.previousSpeed = currentSpeed;
                  this.previousSpeedTime = currentTime;
                  return true;
                }
              }
            }
            this.previousSpeed = currentSpeed;
            this.previousSpeedTime = currentTime;
            return yield this.checkHardBreakWithMotionSensor(location);
          } catch (error) {
            console.error("Erro em checkHardBreak:", error);
            return false;
          }
        });
      }
      checkHardBreakWithMotionSensor(location) {
        return __async(this, null, function* () {
          return new Promise((resolve) => __async(this, null, function* () {
            let isResolved = false;
            let listenerHandle = null;
            try {
              const isMotionAvailable = yield this.isMotionSensorAvailable();
              if (!isMotionAvailable) {
                console.log("Sensor de movimento n\xE3o dispon\xEDvel");
                resolve(false);
                return;
              }
              listenerHandle = yield Motion.addListener("accel", (event) => {
                const { acceleration } = event;
                if ((acceleration == null ? void 0 : acceleration.x) && acceleration.x < -10) {
                  console.log("Freada brusca detectada por sensor de movimento!");
                  location.occurrenceType = "HardBreak";
                  this.saveLocation(location);
                  if (listenerHandle) {
                    listenerHandle.remove();
                  }
                  if (!isResolved) {
                    isResolved = true;
                    resolve(true);
                  }
                }
              });
              setTimeout(() => {
                if (!isResolved) {
                  isResolved = true;
                  if (listenerHandle) {
                    listenerHandle.remove();
                  }
                  resolve(false);
                }
              }, 1e3);
            } catch (error) {
              console.error("Erro ao configurar listener de acelera\xE7\xE3o:", error);
              if (!isResolved) {
                isResolved = true;
                resolve(false);
              }
            }
          }));
        });
      }
      isMotionSensorAvailable() {
        return __async(this, null, function* () {
          try {
            const testListener = yield Motion.addListener("accel", () => {
            });
            if (testListener) {
              testListener.remove();
              return true;
            }
            return false;
          } catch (error) {
            console.error("Erro ao verificar disponibilidade do sensor de movimento:", error);
            return false;
          }
        });
      }
      checkHighVelocity(location) {
        return __async(this, null, function* () {
          const speedKmH = yield this.getCurrentSpeed();
          if (speedKmH > 120) {
            console.log("Alta velocidade detectada!");
            location.occurrenceType = "Acceleration";
            this.saveLocation(location);
            return true;
          }
          return false;
        });
      }
    };
    _JourneyInfoService.\u0275fac = function JourneyInfoService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _JourneyInfoService)(\u0275\u0275inject(DataStorageService));
    };
    _JourneyInfoService.\u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _JourneyInfoService, factory: _JourneyInfoService.\u0275fac, providedIn: "root" });
    JourneyInfoService = _JourneyInfoService;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(JourneyInfoService, [{
        type: Injectable,
        args: [{
          providedIn: "root"
        }]
      }], () => [{ type: DataStorageService }], null);
    })();
  }
});

// src/app/core/services/journey-storage.service.ts
var _JourneyStorageService, JourneyStorageService;
var init_journey_storage_service = __esm({
  "src/app/core/services/journey-storage.service.ts"() {
    "use strict";
    init_core();
    init_esm_browser();
    init_sync_model();
    init_core();
    init_session_service();
    init_data_storage_service();
    init_ionic_angular();
    init_journeyinfo_service();
    init_vehicle_service();
    init_network_service();
    init_http();
    init_api_service();
    _JourneyStorageService = class _JourneyStorageService {
      constructor(sessionService, dataStorageService, platform, journeyInfoService, vehicleService, networkService, http, apiService) {
        this.sessionService = sessionService;
        this.dataStorageService = dataStorageService;
        this.platform = platform;
        this.journeyInfoService = journeyInfoService;
        this.vehicleService = vehicleService;
        this.networkService = networkService;
        this.http = http;
        this.apiService = apiService;
        this.tableName = "journeys";
        this.isNative = this.platform.is("capacitor") || this.platform.is("cordova");
      }
      init() {
        return __async(this, null, function* () {
          yield this.dataStorageService.init();
        });
      }
      /**
       * Starts a new journey if the user has at least one vehicle
       * @returns The journey ID if successful, null if no vehicles exist
       */
      startJourney() {
        return __async(this, null, function* () {
          const userId = yield this.sessionService.getUserId();
          const hasVehicles = yield this.vehicleService.hasVehicles(userId);
          if (!hasVehicles) {
            console.error("Cannot start journey: No vehicles registered for this user");
            return null;
          }
          const primaryVehicle = yield this.vehicleService.getPrimaryVehicle(userId);
          if (!primaryVehicle) {
            console.error("Cannot start journey: No primary vehicle set");
            return null;
          }
          const id = v4_default();
          const startDate = (/* @__PURE__ */ new Date()).toISOString();
          const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingCreate : SyncStatus.PendingCreate;
          yield this.dataStorageService.insert(this.tableName, {
            id,
            startDate,
            userId,
            vehicleId: primaryVehicle.id,
            syncStatus,
            lastSyncDate: null
          });
          this.journeyInfoService.startTracking(id);
          return id;
        });
      }
      endJourney(journeyId) {
        return __async(this, null, function* () {
          this.journeyInfoService.stopTracking(journeyId);
          const endDate = (/* @__PURE__ */ new Date()).toISOString();
          const result = yield this.dataStorageService.select("journeyInfo", `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);
          let infosJourney = [];
          if (Array.isArray(result)) {
            infosJourney = result;
          } else if (result) {
            try {
              infosJourney = Array.isArray(result) ? result : [];
            } catch (error) {
              console.error("Error converting journey info to array:", error);
            }
          }
          let totalDistance = 0;
          if (infosJourney && infosJourney.length > 1) {
            for (let i = 0; i < infosJourney.length - 1; i++) {
              const start = infosJourney[i];
              const end = infosJourney[i + 1];
              totalDistance += this.calculateDistance(start.latitude, start.longitude, end.latitude, end.longitude);
            }
          }
          const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingUpdate : SyncStatus.PendingUpdate;
          yield this.dataStorageService.update(this.tableName, {
            endDate,
            distance: totalDistance,
            syncStatus,
            lastSyncDate: null
          }, `id = '${journeyId}'`);
        });
      }
      addLocation(journeyId, latitude, longitude) {
        return __async(this, null, function* () {
          const id = v4_default();
          const timestamp = (/* @__PURE__ */ new Date()).toISOString();
          const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingCreate : SyncStatus.PendingCreate;
          yield this.dataStorageService.insert("journeyInfo", {
            id,
            journeyId,
            latitude,
            longitude,
            timestamp,
            syncStatus,
            lastSyncDate: null
          });
        });
      }
      closeConnection() {
        return __async(this, null, function* () {
          if (this.db) {
            this.db.close();
            this.db = null;
          }
        });
      }
      getAllJourneys() {
        return __async(this, null, function* () {
          let result = yield this.dataStorageService.select(this.tableName, "ORDER BY startDate DESC");
          if (!result || result.length === 0)
            return [];
          for (let i = 0; i < result.length; i++) {
            const journeyId = result[i].id;
            const infosJourney = yield this.dataStorageService.select("journeyInfo", `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);
            if (Array.isArray(infosJourney)) {
              result[i].infos = infosJourney.filter((info) => info.journeyId === journeyId);
            } else {
              result[i].infos = [];
            }
          }
          const journeys = result.map((data) => {
            return {
              id: data.id,
              startDate: data.startDate,
              endDate: data.endDate,
              distance: data.distance,
              userId: data.userId,
              vehicleId: data.vehicleId,
              infosJourney: data.infos,
              syncStatus: data.syncStatus || SyncStatus.Synced,
              lastSyncDate: data.lastSyncDate ? new Date(data.lastSyncDate) : void 0
            };
          });
          return journeys;
        });
      }
      getAllJourneysByUser(userId) {
        return __async(this, null, function* () {
          let result = yield this.dataStorageService.select(this.tableName, " ORDER BY startDate DESC");
          if (Array.isArray(result)) {
            result = result.filter((data) => data.userId === userId);
          } else {
            return [];
          }
          if (!result || result.length === 0)
            return [];
          for (let i = 0; i < result.length; i++) {
            const journeyId = result[i].id;
            const infosJourney = yield this.dataStorageService.select("journeyInfo", `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);
            if (Array.isArray(infosJourney)) {
              result[i].infos = infosJourney.filter((info) => info.journeyId === journeyId);
            } else {
              result[i].infos = [];
            }
          }
          const journeys = result.map((data) => {
            return {
              id: data.id,
              startDate: data.startDate,
              endDate: data.endDate,
              distance: data.distance,
              userId: data.userId,
              vehicleId: data.vehicleId,
              infosJourney: data.infos,
              syncStatus: data.syncStatus || SyncStatus.Synced,
              lastSyncDate: data.lastSyncDate ? new Date(data.lastSyncDate) : void 0
            };
          });
          return journeys;
        });
      }
      calculateDistance(lat1, lon1, lat2, lon2) {
        const toRad = (value) => value * Math.PI / 180;
        const R = 6371e3;
        const dLat = toRad(lat2 - lat1);
        const dLon = toRad(lon2 - lon1);
        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
      }
      /**
       * Updates the sync status of a journey
       * @param journey The journey to update
       * @returns True if the update was successful
       */
      updateJourneySync(journey) {
        return __async(this, null, function* () {
          try {
            const journeyForDb = __spreadProps(__spreadValues({}, journey), {
              lastSyncDate: journey.lastSyncDate ? journey.lastSyncDate.toISOString() : null
            });
            if (journeyForDb.infosJourney) {
              delete journeyForDb.infosJourney;
            }
            yield this.dataStorageService.update(this.tableName, journeyForDb, `id = '${journey.id}'`);
            return true;
          } catch (error) {
            console.error("Error updating journey sync status:", error);
            return false;
          }
        });
      }
      /**
       * Gets journeys that need to be synchronized
       * @param userId The user ID
       * @returns Array of journeys that need to be synchronized
       */
      getPendingSyncJourneys(userId) {
        return __async(this, null, function* () {
          const journeys = yield this.getAllJourneysByUser(userId);
          return journeys.filter((j) => j.syncStatus === SyncStatus.PendingCreate || j.syncStatus === SyncStatus.PendingUpdate || j.syncStatus === SyncStatus.PendingDelete);
        });
      }
      /**
       * Sends a journey to the server for synchronization
       * @param journey The journey to send
       * @returns True if the journey was sent successfully
       */
      sendJourneyToSync(journey) {
        return __async(this, null, function* () {
          try {
            const url = this.apiService.getUrl("journey/SyncJourney");
            const result = yield this.http.post(url, journey).toPromise();
            return true;
          } catch (error) {
            console.error("Error sending journey to sync:", error);
            throw error;
          }
        });
      }
    };
    _JourneyStorageService.\u0275fac = function JourneyStorageService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _JourneyStorageService)(\u0275\u0275inject(SessionService), \u0275\u0275inject(DataStorageService), \u0275\u0275inject(Platform), \u0275\u0275inject(JourneyInfoService), \u0275\u0275inject(VehicleService), \u0275\u0275inject(NetworkService), \u0275\u0275inject(HttpClient), \u0275\u0275inject(ApiService));
    };
    _JourneyStorageService.\u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _JourneyStorageService, factory: _JourneyStorageService.\u0275fac, providedIn: "root" });
    JourneyStorageService = _JourneyStorageService;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(JourneyStorageService, [{
        type: Injectable,
        args: [{
          providedIn: "root"
        }]
      }], () => [{ type: SessionService }, { type: DataStorageService }, { type: Platform }, { type: JourneyInfoService }, { type: VehicleService }, { type: NetworkService }, { type: HttpClient }, { type: ApiService }], null);
    })();
  }
});

export {
  Geolocation,
  init_esm2 as init_esm,
  Motion,
  init_esm3 as init_esm2,
  JourneyInfoService,
  init_journeyinfo_service,
  JourneyStorageService,
  init_journey_storage_service
};
//# sourceMappingURL=chunk-L56FEOJB.js.map
