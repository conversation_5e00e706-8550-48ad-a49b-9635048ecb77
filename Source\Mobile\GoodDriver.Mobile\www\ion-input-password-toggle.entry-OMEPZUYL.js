import {
  eye,
  eyeOff,
  init_index_e2cf2ceb
} from "./chunk-KBY3MAQZ.js";
import {
  createColorClasses,
  init_theme_01f3f29c
} from "./chunk-CHFZ6R2P.js";
import {
  getIonMode,
  init_ionic_global_b26f573e
} from "./chunk-XPKADKFH.js";
import {
  Host,
  getElement,
  h,
  init_index_527b9e34,
  registerInstance
} from "./chunk-V6QEVD67.js";
import {
  init_index_cfd9c1f2,
  printIonWarning
} from "./chunk-6SXGDLUH.js";
import {
  __esm
} from "./chunk-4W6HR7MY.js";

// node_modules/@ionic/core/dist/esm/ion-input-password-toggle.entry.js
var iosInputPasswordToggleCss, IonInputPasswordToggleIosStyle0, mdInputPasswordToggleCss, IonInputPasswordToggleMdStyle0, InputPasswordToggle;
var init_ion_input_password_toggle_entry = __esm({
  "node_modules/@ionic/core/dist/esm/ion-input-password-toggle.entry.js"() {
    init_index_527b9e34();
    init_index_cfd9c1f2();
    init_theme_01f3f29c();
    init_index_e2cf2ceb();
    init_ionic_global_b26f573e();
    iosInputPasswordToggleCss = "";
    IonInputPasswordToggleIosStyle0 = iosInputPasswordToggleCss;
    mdInputPasswordToggleCss = "";
    IonInputPasswordToggleMdStyle0 = mdInputPasswordToggleCss;
    InputPasswordToggle = class {
      constructor(hostRef) {
        registerInstance(this, hostRef);
        this.togglePasswordVisibility = () => {
          const {
            inputElRef
          } = this;
          if (!inputElRef) {
            return;
          }
          inputElRef.type = inputElRef.type === "text" ? "password" : "text";
        };
        this.color = void 0;
        this.showIcon = void 0;
        this.hideIcon = void 0;
        this.type = "password";
      }
      /**
       * Whenever the input type changes we need to re-run validation to ensure the password
       * toggle is being used with the correct input type. If the application changes the type
       * outside of this component we also need to re-render so the correct icon is shown.
       */
      onTypeChange(newValue) {
        if (newValue !== "text" && newValue !== "password") {
          printIonWarning(`[ion-input-password-toggle] - Only inputs of type "text" or "password" are supported. Input of type "${newValue}" is not compatible.`, this.el);
          return;
        }
      }
      connectedCallback() {
        const {
          el
        } = this;
        const inputElRef = this.inputElRef = el.closest("ion-input");
        if (!inputElRef) {
          printIonWarning("[ion-input-password-toggle] - No ancestor ion-input found. This component must be slotted inside of an ion-input.", el);
          return;
        }
        this.type = inputElRef.type;
      }
      disconnectedCallback() {
        this.inputElRef = null;
      }
      render() {
        var _a, _b;
        const {
          color,
          type
        } = this;
        const mode = getIonMode(this);
        const showPasswordIcon = (_a = this.showIcon) !== null && _a !== void 0 ? _a : eye;
        const hidePasswordIcon = (_b = this.hideIcon) !== null && _b !== void 0 ? _b : eyeOff;
        const isPasswordVisible = type === "text";
        return h(Host, {
          key: "1a28e078c83e74c72d8bb8189ece93ec2e3fa3d0",
          class: createColorClasses(color, {
            [mode]: true
          })
        }, h("ion-button", {
          key: "61591d11d97f3065240e51d8887f831339fd36cf",
          mode,
          color,
          fill: "clear",
          shape: "round",
          "aria-checked": isPasswordVisible ? "true" : "false",
          "aria-label": isPasswordVisible ? "Hide password" : "Show password",
          role: "switch",
          type: "button",
          onPointerDown: (ev) => {
            ev.preventDefault();
          },
          onClick: this.togglePasswordVisibility
        }, h("ion-icon", {
          key: "9d98e6675d5e2adea5c0f56ab67e98b6f57521dd",
          slot: "icon-only",
          "aria-hidden": "true",
          icon: isPasswordVisible ? hidePasswordIcon : showPasswordIcon
        })));
      }
      get el() {
        return getElement(this);
      }
      static get watchers() {
        return {
          "type": ["onTypeChange"]
        };
      }
    };
    InputPasswordToggle.style = {
      ios: IonInputPasswordToggleIosStyle0,
      md: IonInputPasswordToggleMdStyle0
    };
  }
});
init_ion_input_password_toggle_entry();
export {
  InputPasswordToggle as ion_input_password_toggle
};
/*! Bundled license information:

@ionic/core/dist/esm/ion-input-password-toggle.entry.js:
  (*!
   * (C) Ionic http://ionicframework.com - MIT License
   *)
*/
//# sourceMappingURL=ion-input-password-toggle.entry-OMEPZUYL.js.map
