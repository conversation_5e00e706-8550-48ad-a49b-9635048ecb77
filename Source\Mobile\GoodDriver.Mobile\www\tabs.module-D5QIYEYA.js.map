{"version": 3, "sources": ["src/app/pages/tabs/tabs.page.ts", "src/app/pages/tabs/tabs.page.html", "src/app/pages/tabs/tabs.routing.module.ts", "src/app/pages/tabs/tabs.module.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router, RouterModule } from '@angular/router';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { CommonModule } from '@angular/common';\r\n\r\n@Component({\r\n    selector: 'tabs',\r\n    templateUrl: 'tabs.page.html',\r\n    //styleUrls: ['tabs.page.scss'],\r\n    imports: [\r\n      IonicModule, \r\n      CommonModule,\r\n      RouterModule\r\n    ]\r\n  })\r\n\r\nexport class TabsPage implements OnInit \r\n{\r\n    constructor(private router: Router) {\r\n    }\r\n    ngOnInit(): void {\r\n        \r\n    }\r\n\r\n    logout() {\r\n        console.log('Logout realizado');\r\n        this.router.navigate(['/login']);\r\n    }\r\n}", "<ion-tabs>\r\n  <ion-tab-bar slot=\"bottom\" class=\"custom-tabs\">\r\n    <ion-tab-button tab=\"home\">\r\n      <ion-icon name=\"home-outline\"></ion-icon>\r\n      <ion-label>Home</ion-label>\r\n    </ion-tab-button>\r\n\r\n    <ion-tab-button tab=\"vehicles\">\r\n      <ion-icon name=\"car-outline\"></ion-icon>\r\n      <ion-label>Veículos</ion-label>\r\n    </ion-tab-button>\r\n\r\n    <ion-tab-button tab=\"journeys\">\r\n      <ion-icon name=\"map-outline\"></ion-icon>\r\n      <ion-label>Viagens</ion-label>\r\n    </ion-tab-button>\r\n\r\n    <ion-tab-button tab=\"sync\">\r\n      <ion-icon name=\"sync-outline\"></ion-icon>\r\n      <ion-label>Sincronizar</ion-label>\r\n    </ion-tab-button>\r\n  </ion-tab-bar>\r\n</ion-tabs>\r\n", "import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { TabsPage } from './tabs.page';\r\nimport { HomePage } from './home/<USER>';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: TabsPage,\r\n    children: [\r\n      {\r\n        path: 'home',\r\n        component: HomePage,\r\n      },\r\n      {\r\n        path: 'journeys',\r\n        loadComponent: () =>\r\n          import('./journeys/journey/journey.page').then(m => m.JourneyPage),\r\n      },\r\n      {\r\n        path: 'vehicles',\r\n        loadComponent: () =>\r\n          import('./vehicles/list-vehicles/list-vehicles.page').then(m => m.ListVehiclesPage),\r\n      },\r\n      {\r\n        path: 'vehicles/edit/:id',\r\n        loadComponent: () => import('./vehicles/edit-vehicle/edit-vehicle.page').then(m => m.EditVehiclePage)\r\n      },\r\n      {\r\n        path: 'sync',\r\n        loadComponent: () =>\r\n          import('../../pages/sync/sync.page').then(m => m.SyncPage),\r\n      },\r\n      {\r\n        path: '',\r\n        redirectTo: 'home',\r\n        pathMatch: 'full',\r\n      },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class TabsRoutingModule {}\r\n", "import { NgModule } from '@angular/core';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { CommonModule } from '@angular/common';\r\n import { TabsPage } from './tabs.page';\r\n import { TabsRoutingModule } from './tabs.routing.module';\r\n import { HomePage } from './home/<USER>';\r\n\r\n@NgModule({  \r\n  imports: [\r\n    CommonModule,\r\n    IonicModule,\r\n    TabsRoutingModule,\r\n    TabsPage,\r\n    HomePage\r\n  ]\r\n})\r\nexport class TabsPageModule {}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAgBa;AAhBb;;;;AACA;AACA;AACA;;;;AAaM,IAAO,YAAP,MAAO,UAAQ;MAEjB,YAAoB,QAAc;AAAd,aAAA,SAAA;MACpB;MACA,WAAQ;MAER;MAEA,SAAM;AACF,gBAAQ,IAAI,kBAAkB;AAC9B,aAAK,OAAO,SAAS,CAAC,QAAQ,CAAC;MACnC;;;uCAXS,WAAQ,4BAAA,MAAA,CAAA;IAAA;8EAAR,WAAQ,WAAA,CAAA,CAAA,MAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,QAAA,UAAA,GAAA,aAAA,GAAA,CAAA,OAAA,MAAA,GAAA,CAAA,QAAA,cAAA,GAAA,CAAA,OAAA,UAAA,GAAA,CAAA,QAAA,aAAA,GAAA,CAAA,OAAA,UAAA,GAAA,CAAA,QAAA,aAAA,GAAA,CAAA,OAAA,MAAA,GAAA,CAAA,QAAA,cAAA,CAAA,GAAA,UAAA,SAAA,kBAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;AChBrB,QAAA,yBAAA,GAAA,UAAA,EAAU,GAAA,eAAA,CAAA,EACuC,GAAA,kBAAA,CAAA;AAE3C,QAAA,oBAAA,GAAA,YAAA,CAAA;AACA,QAAA,yBAAA,GAAA,WAAA;AAAW,QAAA,iBAAA,GAAA,MAAA;AAAI,QAAA,uBAAA,EAAY;AAG7B,QAAA,yBAAA,GAAA,kBAAA,CAAA;AACE,QAAA,oBAAA,GAAA,YAAA,CAAA;AACA,QAAA,yBAAA,GAAA,WAAA;AAAW,QAAA,iBAAA,GAAA,aAAA;AAAQ,QAAA,uBAAA,EAAY;AAGjC,QAAA,yBAAA,IAAA,kBAAA,CAAA;AACE,QAAA,oBAAA,IAAA,YAAA,CAAA;AACA,QAAA,yBAAA,IAAA,WAAA;AAAW,QAAA,iBAAA,IAAA,SAAA;AAAO,QAAA,uBAAA,EAAY;AAGhC,QAAA,yBAAA,IAAA,kBAAA,CAAA;AACE,QAAA,oBAAA,IAAA,YAAA,CAAA;AACA,QAAA,yBAAA,IAAA,WAAA;AAAW,QAAA,iBAAA,IAAA,aAAA;AAAW,QAAA,uBAAA,EAAY,EACnB,EACL;;;MDXV;MAAW;MAAA;MAAA;MAAA;MAAA;MACX;MACA;IAAY,GAAA,eAAA,EAAA,CAAA;AAIZ,IAAO,WAAP;;0EAAO,UAAQ,CAAA;cAXpB;2BACa,QAAM,SAGP;UACP;UACA;UACA;WACD,UAAA,4tBAAA,CAAA;;;;iFAGQ,UAAQ,EAAA,WAAA,YAAA,UAAA,mCAAA,YAAA,GAAA,CAAA;IAAA,GAAA;;;;;AEhBrB,IAKM,QAyCO;AA9Cb;;;;AACA;AACA;AACA;;AAEA,IAAM,SAAiB;MACrB;QACE,MAAM;QACN,WAAW;QACX,UAAU;UACR;YACE,MAAM;YACN,WAAW;;UAEb;YACE,MAAM;YACN,eAAe,MACb,OAAO,4BAAiC,EAAE,KAAK,OAAK,EAAE,WAAW;;UAErE;YACE,MAAM;YACN,eAAe,MACb,OAAO,kCAA6C,EAAE,KAAK,OAAK,EAAE,gBAAgB;;UAEtF;YACE,MAAM;YACN,eAAe,MAAM,OAAO,iCAA2C,EAAE,KAAK,OAAK,EAAE,eAAe;;UAEtG;YACE,MAAM;YACN,eAAe,MACb,OAAO,yBAA4B,EAAE,KAAK,OAAK,EAAE,QAAQ;;UAE7D;YACE,MAAM;YACN,YAAY;YACZ,WAAW;;;;;AAUb,IAAO,qBAAP,MAAO,mBAAiB;;;uCAAjB,oBAAiB;IAAA;sFAAjB,mBAAiB,CAAA;0FAHlB,aAAa,SAAS,MAAM,GAC5B,YAAY,EAAA,CAAA;AAElB,IAAO,oBAAP;;0EAAO,mBAAiB,CAAA;cAJ7B;eAAS;UACR,SAAS,CAAC,aAAa,SAAS,MAAM,CAAC;UACvC,SAAS,CAAC,YAAY;SACvB;;;;;;;AC7CD,IAgBa;AAhBb;;;AACA;AACA;AACC;AACA;AACA;;AAWK,IAAO,kBAAP,MAAO,gBAAc;;;uCAAd,iBAAc;IAAA;mFAAd,gBAAc,CAAA;;MAPvB;MACA;MACA;MACA;MACA;IAAQ,EAAA,CAAA;AAGN,IAAO,iBAAP;;0EAAO,gBAAc,CAAA;cAT1B;eAAS;UACR,SAAS;YACP;YACA;YACA;YACA;YACA;;SAEH;;;;;", "names": []}