import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { VehicleCreateRequestDto } from '../dtos/vehicle/VehicleCreateRequestDto';
import { SyncStatus } from '../models/sync.model';
import { v4 as uuidv4 } from 'uuid';
import { SessionService } from './session.service';
import { DataStorageService } from './data-storage-service';
import { ApiService } from './api.service';
import { VehicleStateService } from './vehicle-state.service';
import { NetworkService } from './network.service';
import { Vehicle } from '../models/vehicle.model';
import { VehicleSyncRequestDto } from '../dtos/vehicle/vehicleSyncRequestDto';


@Injectable({
    providedIn: 'root'
  })
export class VehicleService
{
    private readonly tableName = 'vehicles';
    constructor(
      private http: HttpClient,
      private dataStorageService: DataStorageService,
      private sessionService: SessionService,
      private apiService: ApiService,
      private vehicleStateService: VehicleStateService,
      private networkService: NetworkService
    ) {}

    async create(data: VehicleCreateRequestDto): Promise<any> {
      try {
        const url = this.apiService.getUrl('vehicles/createVehicle');
        const result = await firstValueFrom(this.http.post<any>(url, data));
        return result;
      } catch (error) {
        console.error('Error creating vehicle:', error);
        throw error;
      }
    }

    async createLocal(data: VehicleCreateRequestDto): Promise<any> {
      try {
        // Verificar se a tabela vehicles existe e tem a estrutura correta
        await this.verifyVehicleTableStructure();

        // Create a timestamp for createdOn
        const createdOn = new Date().toISOString();

        // Check if this is the first vehicle for the user
        const existingVehicles = await this.listAllLocal(data.userId);
        const isPrimary = existingVehicles.length === 0 ? 1 : 0; // Set as primary if it's the first vehicle

        // Determine sync status based on network connectivity
        const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingCreate : SyncStatus.PendingCreate;
console.log('Vai Cadastrar veiculo: ', JSON.stringify(data));

        const vehicleData = {
          id: data.vehicleId,
          userId: data.userId,
          plate: data.plate,
          year: data.year,
          brandId: data.brandId,
          brandName: data.brandName,
          modelId: data.modelId,
          modelName: data.modelName,
          version: data.version,
          policyNumber: data.policyNumber,
          isPrimary: isPrimary,
          createdOn: createdOn,
          syncStatus: syncStatus,
          lastSyncDate: null
        };

        console.log('Dados do veículo para inserir:', JSON.stringify(vehicleData));
        console.log('Tipo de storage sendo usado:', this.dataStorageService.isUsingSQLite() ? 'SQLite' : 'IndexedDB');

        const insertResult = await this.dataStorageService.insert(this.tableName, vehicleData);
        console.log('Resultado da inserção:', insertResult);

        // Verificar se foi realmente inserido
        const verification = await this.dataStorageService.select(this.tableName, `WHERE id = '${data.vehicleId}'`);
        console.log('Verificação pós-inserção:', verification);

        console.log('Cadastrou veiculo: '+ JSON.stringify(data));
        return { id: data.vehicleId, isPrimary: isPrimary === 1, syncStatus: syncStatus };
      } catch (error) {
        console.error('Error creating vehicle:', error);
        throw error;
      }
    }

    async listAll(userId: string): Promise<Vehicle[]> {
      try {
        const url = this.apiService.getUrl('vehicles/list', { userId });
        const result = await firstValueFrom(this.http.get<Vehicle[]>(url));

        if (!result) return [];

        return result;
      } catch (error) {
        console.error('Error fetching vehicles:', error);
        return [];
      }
    }

    async listAllLocal(userId: string): Promise<Vehicle[]> {
      // Buscar dados do usuário no Local Storage
      let result = await this.dataStorageService.select(this.tableName, '');

      if (!Array.isArray(result)) {
        return [];
      }

      result = result.filter((data: any) => data.userId === userId);
      console.log('result.length vehicles for user:', result.length);
      if(!result || result.length === 0)
        return [];

      const resultVehicles: Vehicle[] = result.map((data: any) => {
        return {
          id: data.id,
          brandId: data.brandId,
          brandName: data.brandName,
          modelId: data.modelId,
          modelName: data.modelName,
          userId: data.userId,
          plate: data.plate,
          year: data.year,
          version: data.version,
          policyNumber: data.policyNumber,
          isPrimary: data.isPrimary === 1 || data.isPrimary === true,
          createdOn: data.createdOn,
          updatedOn: data.updatedOn,
          syncStatus: data.syncStatus || SyncStatus.Synced,
          lastSyncDate: data.lastSyncDate ? new Date(data.lastSyncDate) : undefined
        };
      });

      // Update the vehicle state service with the latest vehicles
      this.vehicleStateService.updateVehicles(resultVehicles);

      return resultVehicles;
    }

    /**
     * Sets a vehicle as the primary vehicle for a user
     * @param vehicleId The ID of the vehicle to set as primary
     * @param userId The user ID
     */
    async setPrimaryVehicle(vehicleId: string, userId: string): Promise<boolean> {
      try {
        // First, unset any existing primary vehicle
        const vehicles = await this.listAllLocal(userId);

        // Update all vehicles to not be primary
        for (const vehicle of vehicles) {
          if (vehicle.isPrimary) {
            // Get the full vehicle object
            const vehicleToUpdate = { ...vehicle, isPrimary: 0 };

            // Update the vehicle
            await this.dataStorageService.update(
              this.tableName,
              vehicleToUpdate,
              `id = '${vehicle.id}'`
            );
          }
        }

        // Get the vehicle to set as primary
        const primaryVehicle = vehicles.find(v => v.id === vehicleId);

        if (primaryVehicle) {
          // Update the vehicle with isPrimary = 1
          const updatedVehicle = { ...primaryVehicle, isPrimary: true };

          // Update the vehicle in the database (convert boolean to number for storage)
          const vehicleForDb = { ...updatedVehicle, isPrimary: 1 };
          await this.dataStorageService.update(
            this.tableName,
            vehicleForDb,
            `id = '${vehicleId}'`
          );

          // Refresh the vehicles list to update the state
          await this.listAllLocal(userId);

          // Update the primary vehicle in the state service
          this.vehicleStateService.updatePrimaryVehicle(updatedVehicle);

          return true;
        } else {
          console.error('Vehicle not found:', vehicleId);
          return false;
        }
      } catch (error) {
        console.error('Error setting primary vehicle:', error);
        return false;
      }
    }

    /**
     * Gets the primary vehicle for a user
     * @param userId The user ID
     * @returns The primary vehicle or null if none exists
     */
    async getPrimaryVehicle(userId: string): Promise<Vehicle | null> {
      try {
        const vehicles = await this.listAllLocal(userId);

        // Find the primary vehicle
        const primaryVehicle = vehicles.find(v => v.isPrimary);

        // If no primary vehicle is set but vehicles exist, set the first one as primary
        if (!primaryVehicle && vehicles.length > 0) {
          await this.setPrimaryVehicle(vehicles[0].id, userId);

          // Get the updated vehicle with isPrimary set to true
          const updatedVehicle = { ...vehicles[0], isPrimary: true };

          // Update the state service
          this.vehicleStateService.updatePrimaryVehicle(updatedVehicle);

          return updatedVehicle;
        }

        // Update the state service with the primary vehicle
        if (primaryVehicle) {
          this.vehicleStateService.updatePrimaryVehicle(primaryVehicle);
        }

        return primaryVehicle || null;
      } catch (error) {
        console.error('Error getting primary vehicle:', error);
        return null;
      }
    }

    /**
     * Checks if the user has any vehicles
     * @param userId The user ID
     * @returns True if the user has at least one vehicle
     */
    async hasVehicles(userId: string): Promise<boolean> {
      const vehicles = await this.listAllLocal(userId);
      return vehicles.length > 0;
    }

    /**
     * Sincroniza veículos: primeiro baixa os existentes do servidor, depois envia os locais
     * @param userId ID do usuário
     * @returns True se a sincronização foi bem-sucedida
     */
    async syncVehicles(userId: string): Promise<boolean> {
      try {
        console.log('Iniciando sincronização de veículos...');

        // 1. Primeiro, baixar veículos existentes do servidor
        const downloadSuccess = await this.downloadVehiclesFromServer(userId);
        if (!downloadSuccess) {
          console.warn('Falha ao baixar veículos do servidor, continuando com upload...');
        }

        // 2. Depois, enviar veículos locais pendentes para o servidor
        const uploadSuccess = await this.uploadPendingVehicles(userId);

        console.log('Sincronização de veículos concluída');
        return downloadSuccess && uploadSuccess;

      } catch (error) {
        console.error('Erro na sincronização de veículos:', error);
        return false;
      }
    }

    /**
     * Baixa veículos do servidor e salva localmente
     */
    private async downloadVehiclesFromServer(userId: string): Promise<boolean> {
      try {
        console.log('Baixando veículos do servidor...');

        // Buscar veículos do servidor
        const serverVehicles = await this.listAll(userId);

        if (!serverVehicles || serverVehicles.length === 0) {
          console.log('Nenhum veículo encontrado no servidor');
          return true;
        }

        console.log(`${serverVehicles.length} veículos encontrados no servidor`);

        // Buscar veículos locais para comparação
        const localVehicles = await this.listAllLocal(userId);
        const localVehicleIds = new Set(localVehicles.map(v => v.id));

        // Processar cada veículo do servidor
        for (const serverVehicle of serverVehicles) {
          try {
            if (localVehicleIds.has(serverVehicle.id)) {
              // Veículo já existe localmente, verificar se precisa atualizar
              const localVehicle = localVehicles.find(v => v.id === serverVehicle.id);

              if (localVehicle && this.shouldUpdateLocalVehicle(localVehicle, serverVehicle)) {
                await this.updateLocalVehicleFromServer(serverVehicle);
                console.log(`Veículo atualizado localmente: ${serverVehicle.plate}`);
              }
            } else {
              // Veículo não existe localmente, criar
              await this.createLocalVehicleFromServer(serverVehicle, userId);
              console.log(`Novo veículo salvo localmente: ${serverVehicle.plate}`);
            }
          } catch (error) {
            console.error(`Erro ao processar veículo ${serverVehicle.id}:`, error);
          }
        }

        // Atualizar estado dos veículos
        await this.listAllLocal(userId);

        return true;

      } catch (error) {
        console.error('Erro ao baixar veículos do servidor:', error);
        return false;
      }
    }

    /**
     * Envia veículos locais pendentes para o servidor
     */
    private async uploadPendingVehicles(userId: string): Promise<boolean> {
      try {
        console.log('Enviando veículos pendentes para o servidor...');

        const pendingVehicles = await this.getPendingSyncVehicles(userId);

        if (pendingVehicles.length === 0) {
          console.log('Nenhum veículo pendente para sincronizar');
          return true;
        }

        console.log(`${pendingVehicles.length} veículos pendentes para sincronizar`);

        let successCount = 0;

        for (const vehicle of pendingVehicles) {
          try {
            const success = await this.sendVehicleToSync(this.convertToSyncDto(vehicle));

            if (success) {
              // Atualizar status de sincronização
              vehicle.syncStatus = SyncStatus.Synced;
              vehicle.lastSyncDate = new Date();
              await this.updateVehicleSync(vehicle);

              successCount++;
              console.log(`Veículo sincronizado: ${vehicle.plate}`);
            } else {
              console.error(`Falha ao sincronizar veículo: ${vehicle.plate} - Resposta inválida do servidor`);
            }
          } catch (error) {
            let errorMessage = `Erro ao sincronizar veículo ${vehicle.plate}`;

            // Adicionar detalhes específicos do erro
            if (error instanceof Error) {
              if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
                errorMessage += ' - Problema de conexão';
              } else if (error.message.includes('400')) {
                errorMessage += ' - Dados do veículo inválidos';
              } else if (error.message.includes('401') || error.message.includes('403')) {
                errorMessage += ' - Erro de autorização';
              } else if (error.message.includes('500')) {
                errorMessage += ' - Erro interno do servidor';
              } else {
                errorMessage += ` - ${error.message}`;
              }
            } else {
              errorMessage += ` - ${error}`;
            }

            console.error(errorMessage);
          }
        }

        console.log(`${successCount}/${pendingVehicles.length} veículos sincronizados com sucesso`);
        return successCount === pendingVehicles.length;

      } catch (error) {
        console.error('Erro ao enviar veículos pendentes:', error);
        return false;
      }
    }

    /**
     * Verifica se o veículo local deve ser atualizado com dados do servidor
     */
    private shouldUpdateLocalVehicle(localVehicle: Vehicle, serverVehicle: Vehicle): boolean {
      // Se o veículo local está pendente de sincronização, não atualizar
      if (localVehicle.syncStatus === SyncStatus.PendingCreate ||
          localVehicle.syncStatus === SyncStatus.PendingUpdate) {
        return false;
      }

      // Verificar se há diferenças nos dados principais
      return (
        localVehicle.plate !== serverVehicle.plate ||
        localVehicle.year !== serverVehicle.year ||
        localVehicle.brandId !== serverVehicle.brandId ||
        localVehicle.modelId !== serverVehicle.modelId ||
        localVehicle.version !== serverVehicle.version ||
        localVehicle.policyNumber !== serverVehicle.policyNumber ||
        localVehicle.isPrimary !== serverVehicle.isPrimary
      );
    }

    /**
     * Atualiza um veículo local com dados do servidor
     */
    private async updateLocalVehicleFromServer(serverVehicle: Vehicle): Promise<void> {
      const vehicleForDb = {
        ...serverVehicle,
        isPrimary: serverVehicle.isPrimary ? 1 : 0,
        syncStatus: SyncStatus.Synced,
        lastSyncDate: new Date().toISOString()
      };

      await this.dataStorageService.update(
        this.tableName,
        vehicleForDb,
        `id = '${serverVehicle.id}'`
      );
    }

    /**
     * Cria um veículo local a partir de dados do servidor
     */
    private async createLocalVehicleFromServer(serverVehicle: Vehicle, userId: string): Promise<void> {
      const vehicleForDb = {
        id: serverVehicle.id,
        userId: userId,
        plate: serverVehicle.plate,
        year: serverVehicle.year,
        brandId: serverVehicle.brandId,
        brandName: serverVehicle.brandName,
        modelId: serverVehicle.modelId,
        modelName: serverVehicle.modelName,
        version: serverVehicle.version,
        policyNumber: serverVehicle.policyNumber,
        isPrimary: serverVehicle.isPrimary ? 1 : 0,
        createdOn: serverVehicle.createdOn,
        updatedOn: serverVehicle.updatedOn,
        syncStatus: SyncStatus.Synced,
        lastSyncDate: new Date().toISOString()
      };

      await this.dataStorageService.insert(this.tableName, vehicleForDb);
    }

    /**
     * Converte um Vehicle para VehicleSyncRequestDto
     */
    private convertToSyncDto(vehicle: Vehicle): VehicleSyncRequestDto {
      return {
        vehicleId: vehicle.id,
        userId: vehicle.userId,
        plate: vehicle.plate,
        year: vehicle.year,
        brandId: vehicle.brandId,
        modelId: vehicle.modelId,
        version: vehicle.version,
        policyNumber: vehicle.policyNumber
      };
    }

    async sendVehicleToSync(vehicle: VehicleSyncRequestDto): Promise<boolean> {
      try {
        const url = this.apiService.getUrl('vehicles/createVehicle');
        const result = await firstValueFrom(this.http.post<any>(url, vehicle));
        return result;
      } catch (error: any) {
        // Melhorar a mensagem de erro com detalhes específicos
        let errorMessage = 'Error sending vehicle to sync';

        if (error.status) {
          switch (error.status) {
            case 400:
              errorMessage = `Dados do veículo inválidos (${error.status}): ${error.error?.message || 'Verifique os dados do veículo'}`;
              break;
            case 401:
              errorMessage = `Não autorizado (${error.status}): Faça login novamente`;
              break;
            case 403:
              errorMessage = `Acesso negado (${error.status}): Sem permissão para sincronizar veículos`;
              break;
            case 404:
              errorMessage = `Serviço não encontrado (${error.status}): Verifique a configuração do servidor`;
              break;
            case 500:
              errorMessage = `Erro interno do servidor (${error.status}): Tente novamente mais tarde`;
              break;
            case 0:
              errorMessage = 'Sem conexão com o servidor: Verifique sua internet';
              break;
            default:
              errorMessage = `Erro HTTP ${error.status}: ${error.error?.message || error.message || 'Erro desconhecido'}`;
          }
        } else if (error.name === 'TimeoutError') {
          errorMessage = 'Timeout: Servidor demorou para responder';
        } else if (error.message?.includes('NetworkError') || error.message?.includes('fetch')) {
          errorMessage = 'Erro de rede: Verifique sua conexão com a internet';
        } else {
          errorMessage = `Erro inesperado: ${error.message || error}`;
        }

        console.error(errorMessage, error);

        // Criar um erro mais informativo
        const enhancedError = new Error(errorMessage);
        enhancedError.name = 'VehicleSyncError';
        throw enhancedError;
      }
    }

    /**
     * Updates the sync status of a vehicle
     * @param vehicle The vehicle to update
     * @returns True if the update was successful
     */
    async updateVehicleSync(vehicle: Vehicle): Promise<boolean> {
      try {
        // Convert boolean isPrimary to number for storage
        const vehicleForDb = {
          ...vehicle,
          isPrimary: vehicle.isPrimary ? 1 : 0,
          lastSyncDate: vehicle.lastSyncDate ? vehicle.lastSyncDate.toISOString() : null
        };

        await this.dataStorageService.update(
          this.tableName,
          vehicleForDb,
          `id = '${vehicle.id}'`
        );

        // Refresh the vehicles list to update the state
        if (vehicle.userId) {
          await this.listAllLocal(vehicle.userId);
        }

        return true;
      } catch (error) {
        console.error('Error updating vehicle sync status:', error);
        return false;
      }
    }

    /**
     * Gets vehicles that need to be synchronized
     * @param userId The user ID
     * @returns Array of vehicles that need to be synchronized
     */
    async getPendingSyncVehicles(userId: string): Promise<Vehicle[]> {
      const vehicles = await this.listAllLocal(userId);
      return vehicles.filter(v =>
        v.syncStatus === SyncStatus.PendingCreate ||
        v.syncStatus === SyncStatus.PendingUpdate ||
        v.syncStatus === SyncStatus.PendingDelete
      );
    }

    /**
     * Busca um veículo pelo id e userId
     */
    async getById(id: string): Promise<Vehicle | null> {
      const userId = await this.sessionService.getUserId() || '';
      const vehicles = await this.listAllLocal(userId);
      return vehicles.find(v => v.id === id) || null;
    }

    /**
     * Atualiza um veículo pelo id
     */
    async update(id: string, updatedVehicle: Vehicle): Promise<boolean> {
      try {
        // Convert boolean isPrimary to number for storage
        const vehicleForDb = {
          ...updatedVehicle,
          isPrimary: updatedVehicle.isPrimary ? 1 : 0,
          lastSyncDate: updatedVehicle.lastSyncDate ? (updatedVehicle.lastSyncDate instanceof Date ? updatedVehicle.lastSyncDate.toISOString() : updatedVehicle.lastSyncDate) : null
        };
        await this.dataStorageService.update(
          this.tableName,
          vehicleForDb,
          `id = '${id}'`
        );
        // Atualiza o estado
        if (updatedVehicle.userId) {
          await this.listAllLocal(updatedVehicle.userId);
        }
        return true;
      } catch (error) {
        console.error('Erro ao atualizar veículo:', error);
        return false;
      }
    }

    /**
     * Verifica se a tabela vehicles existe e tem a estrutura correta
     */
    private async verifyVehicleTableStructure(): Promise<void> {
      try {
        console.log('Verificando estrutura da tabela vehicles...');

        // Verificar se está usando SQLite
        if (!this.dataStorageService.isUsingSQLite()) {
          console.log('Usando IndexedDB - verificação de estrutura não necessária');
          return;
        }

        // Obter informações da tabela
        const sqliteService = this.dataStorageService.getDatabase() as any;
        if (sqliteService.getTableInfo) {
          const tableInfo = await sqliteService.getTableInfo('vehicles');
          console.log('Estrutura da tabela vehicles:', tableInfo);

          if (tableInfo.length === 0) {
            console.error('Tabela vehicles não existe!');
            throw new Error('Tabela vehicles não encontrada no banco de dados');
          }

          // Verificar se as colunas essenciais existem
          const columnNames = tableInfo.map((col: any) => col.name);
          const requiredColumns = ['id', 'userId', 'plate', 'year', 'brandId', 'modelId'];

          const missingColumns = requiredColumns.filter(col => !columnNames.includes(col));
          if (missingColumns.length > 0) {
            console.error('Colunas faltando na tabela vehicles:', missingColumns);
            throw new Error(`Colunas faltando na tabela vehicles: ${missingColumns.join(', ')}`);
          }

          console.log('Estrutura da tabela vehicles está correta');
        } else {
          console.log('Método getTableInfo não disponível');
        }

      } catch (error) {
        console.error('Erro ao verificar estrutura da tabela vehicles:', error);
        throw error;
      }
    }

    /**
     * Método de teste para inserção direta no SQLite
     */
    async testSQLiteInsert(): Promise<void> {
      try {
        console.log('=== TESTE DE INSERÇÃO SQLITE ===');

        const testVehicle = {
          id: 'test-' + Date.now(),
          userId: 'test-user',
          plate: 'TEST-1234',
          year: 2020,
          brandId: 1,
          brandName: 'Test Brand',
          modelId: 1,
          modelName: 'Test Model',
          version: 'Test Version',
          policyNumber: 'TEST123',
          isPrimary: 1,
          createdOn: new Date().toISOString(),
          syncStatus: 'PENDING_CREATE',
          lastSyncDate: null
        };

        console.log('Dados de teste:', testVehicle);

        // Verificar estrutura da tabela
        await this.verifyVehicleTableStructure();

        // Tentar inserir
        const result = await this.dataStorageService.insert('vehicles', testVehicle);
        console.log('Resultado da inserção de teste:', result);

        // Verificar se foi inserido
        const verification = await this.dataStorageService.select('vehicles', `WHERE id = '${testVehicle.id}'`);
        console.log('Verificação pós-inserção:', verification);

        // Limpar dados de teste
        if (verification && verification.length > 0) {
          await this.dataStorageService.delete('vehicles', `id = '${testVehicle.id}'`);
          console.log('Dados de teste removidos');
        }

        console.log('=== FIM DO TESTE ===');

      } catch (error) {
        console.error('Erro no teste de inserção SQLite:', error);
      }
    }
}