import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { VehicleCreateRequestDto } from '../dtos/vehicle/VehicleCreateRequestDto';
import { SyncStatus } from '../models/sync.model';
import { v4 as uuidv4 } from 'uuid';
import { SessionService } from './session.service';
import { DataStorageService } from './data-storage-service';
import { ApiService } from './api.service';
import { VehicleStateService } from './vehicle-state.service';
import { NetworkService } from './network.service';
import { Vehicle } from '../models/vehicle.model';
import { VehicleSyncRequestDto } from '../dtos/vehicle/vehicleSyncRequestDto';


@Injectable({
    providedIn: 'root'
  })
export class VehicleService
{
    private readonly tableName = 'vehicles';
    constructor(
      private http: HttpClient,
      private dataStorageService: DataStorageService,
      private sessionService: SessionService,
      private apiService: ApiService,
      private vehicleStateService: VehicleStateService,
      private networkService: NetworkService
    ) {}

    async create(data: VehicleCreateRequestDto): Promise<any> {
      try {
        const url = this.apiService.getUrl('vehicles/createVehicle');
        const result = await this.http.post<any>(url, data).toPromise();
        return result;
      } catch (error) {
        console.error('Error creating vehicle:', error);
        throw error;
      }
    }

    async createLocal(data: VehicleCreateRequestDto): Promise<any> {
      try {
        // Create a timestamp for createdOn
        const createdOn = new Date().toISOString();

        // Check if this is the first vehicle for the user
        const existingVehicles = await this.listAllLocal(data.userId);
        const isPrimary = existingVehicles.length === 0 ? 1 : 0; // Set as primary if it's the first vehicle

        // Determine sync status based on network connectivity
        const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingCreate : SyncStatus.PendingCreate;
console.log('Vai Cadastrar veiculo: ', JSON.stringify(data));
        await this.dataStorageService.insert(this.tableName, {
          id: data.vehicleId,
          userId: data.userId,
          plate: data.plate,
          year: data.year,
          brandId: data.brandId,
          brandName: data.brandName,
          modelId: data.modelId,
          modelName: data.modelName,
          version: data.version,
          policyNumber: data.policyNumber,
          isPrimary: isPrimary,
          createdOn: createdOn,
          syncStatus: syncStatus,
          lastSyncDate: null
        });
        console.log('Cadastrou veiculo: '+ JSON.stringify(data));
        return { id: data.vehicleId, isPrimary: isPrimary === 1, syncStatus: syncStatus };
      } catch (error) {
        console.error('Error creating vehicle:', error);
        throw error;
      }
    }

    async listAll(userId: string): Promise<Vehicle[]> {
      try {
        const url = this.apiService.getUrl('vehicles/list', { userId });
        const result = await this.http.get<Vehicle[]>(url).toPromise();

        if (!result) return [];

        return result;
      } catch (error) {
        console.error('Error fetching vehicles:', error);
        return [];
      }
    }

    async listAllLocal(userId: string): Promise<Vehicle[]> {
      // Buscar dados do usuário no Local Storage
      let result = await this.dataStorageService.select(this.tableName, '');

      if (!Array.isArray(result)) {
        return [];
      }

      result = result.filter((data: any) => data.userId === userId);
      console.log('result.length vehicles for user:', result.length);
      if(!result || result.length === 0)
        return [];

      const resultVehicles: Vehicle[] = result.map((data: any) => {
        return {
          id: data.id,
          brandId: data.brandId,
          brandName: data.brandName,
          modelId: data.modelId,
          modelName: data.modelName,
          userId: data.userId,
          plate: data.plate,
          year: data.year,
          version: data.version,
          policyNumber: data.policyNumber,
          isPrimary: data.isPrimary === 1 || data.isPrimary === true,
          createdOn: data.createdOn,
          updatedOn: data.updatedOn,
          syncStatus: data.syncStatus || SyncStatus.Synced,
          lastSyncDate: data.lastSyncDate ? new Date(data.lastSyncDate) : undefined
        };
      });

      // Update the vehicle state service with the latest vehicles
      this.vehicleStateService.updateVehicles(resultVehicles);

      return resultVehicles;
    }

    /**
     * Sets a vehicle as the primary vehicle for a user
     * @param vehicleId The ID of the vehicle to set as primary
     * @param userId The user ID
     */
    async setPrimaryVehicle(vehicleId: string, userId: string): Promise<boolean> {
      try {
        // First, unset any existing primary vehicle
        const vehicles = await this.listAllLocal(userId);

        // Update all vehicles to not be primary
        for (const vehicle of vehicles) {
          if (vehicle.isPrimary) {
            // Get the full vehicle object
            const vehicleToUpdate = { ...vehicle, isPrimary: 0 };

            // Update the vehicle
            await this.dataStorageService.update(
              this.tableName,
              vehicleToUpdate,
              `id = '${vehicle.id}'`
            );
          }
        }

        // Get the vehicle to set as primary
        const primaryVehicle = vehicles.find(v => v.id === vehicleId);

        if (primaryVehicle) {
          // Update the vehicle with isPrimary = 1
          const updatedVehicle = { ...primaryVehicle, isPrimary: true };

          // Update the vehicle in the database (convert boolean to number for storage)
          const vehicleForDb = { ...updatedVehicle, isPrimary: 1 };
          await this.dataStorageService.update(
            this.tableName,
            vehicleForDb,
            `id = '${vehicleId}'`
          );

          // Refresh the vehicles list to update the state
          await this.listAllLocal(userId);

          // Update the primary vehicle in the state service
          this.vehicleStateService.updatePrimaryVehicle(updatedVehicle);

          return true;
        } else {
          console.error('Vehicle not found:', vehicleId);
          return false;
        }
      } catch (error) {
        console.error('Error setting primary vehicle:', error);
        return false;
      }
    }

    /**
     * Gets the primary vehicle for a user
     * @param userId The user ID
     * @returns The primary vehicle or null if none exists
     */
    async getPrimaryVehicle(userId: string): Promise<Vehicle | null> {
      try {
        const vehicles = await this.listAllLocal(userId);

        // Find the primary vehicle
        const primaryVehicle = vehicles.find(v => v.isPrimary);

        // If no primary vehicle is set but vehicles exist, set the first one as primary
        if (!primaryVehicle && vehicles.length > 0) {
          await this.setPrimaryVehicle(vehicles[0].id, userId);

          // Get the updated vehicle with isPrimary set to true
          const updatedVehicle = { ...vehicles[0], isPrimary: true };

          // Update the state service
          this.vehicleStateService.updatePrimaryVehicle(updatedVehicle);

          return updatedVehicle;
        }

        // Update the state service with the primary vehicle
        if (primaryVehicle) {
          this.vehicleStateService.updatePrimaryVehicle(primaryVehicle);
        }

        return primaryVehicle || null;
      } catch (error) {
        console.error('Error getting primary vehicle:', error);
        return null;
      }
    }

    /**
     * Checks if the user has any vehicles
     * @param userId The user ID
     * @returns True if the user has at least one vehicle
     */
    async hasVehicles(userId: string): Promise<boolean> {
      const vehicles = await this.listAllLocal(userId);
      return vehicles.length > 0;
    }

    async sendVehicleToSync(vehicle: VehicleSyncRequestDto): Promise<boolean> {
      try {
        const url = this.apiService.getUrl('vehicles/createVehicle');
        const result = await this.http.post<any>(url, vehicle).toPromise();
        return result;
      } catch (error) {
        console.error('Error sending vehicle to sync:', error);
        throw error;
      }
    }

    /**
     * Updates the sync status of a vehicle
     * @param vehicle The vehicle to update
     * @returns True if the update was successful
     */
    async updateVehicleSync(vehicle: Vehicle): Promise<boolean> {
      try {
        // Convert boolean isPrimary to number for storage
        const vehicleForDb = {
          ...vehicle,
          isPrimary: vehicle.isPrimary ? 1 : 0,
          lastSyncDate: vehicle.lastSyncDate ? vehicle.lastSyncDate.toISOString() : null
        };

        await this.dataStorageService.update(
          this.tableName,
          vehicleForDb,
          `id = '${vehicle.id}'`
        );

        // Refresh the vehicles list to update the state
        if (vehicle.userId) {
          await this.listAllLocal(vehicle.userId);
        }

        return true;
      } catch (error) {
        console.error('Error updating vehicle sync status:', error);
        return false;
      }
    }

    /**
     * Gets vehicles that need to be synchronized
     * @param userId The user ID
     * @returns Array of vehicles that need to be synchronized
     */
    async getPendingSyncVehicles(userId: string): Promise<Vehicle[]> {
      const vehicles = await this.listAllLocal(userId);
      return vehicles.filter(v =>
        v.syncStatus === SyncStatus.PendingCreate ||
        v.syncStatus === SyncStatus.PendingUpdate ||
        v.syncStatus === SyncStatus.PendingDelete
      );
    }
}