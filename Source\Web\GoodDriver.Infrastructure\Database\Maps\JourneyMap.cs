﻿using FluentNHibernate.Mapping;
using GoodDriver.Domain.Journeys;


namespace GoodDriver.Infrastructure.Database.Maps
{
	public class JourneyMap : ClassMap<Journey>
	{
        public JourneyMap()
        {
			Id(u => u.Id);
			References(a => a.User).Column("UserId").Not.Nullable().Fetch.Join().Cascade.None();
			References(a => a.Vehicle).Column("VehicleId").Not.Nullable().Fetch.Join().Cascade.None();
			Map(a => a.StartDate).Not.Nullable();
			Map(a => a.EndDate).Nullable();
			Map(a => a.Distance).Nullable();
			Map(a => a.Score).Nullable();			
			Map(a => a.CreatedOn).Not.Nullable();
			Map(a => a.UpdatedOn).Nullable();
			References(a => a.Device).Column("DeviceId").Not.Nullable().Fetch.Join().Cascade.None();
			HasMany(a => a.JourneyInfos).KeyColumn("JourneyId").ExtraLazyLoad().Inverse().Cascade.All();
		}
    }
}
