﻿
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.SetupDomain.Cqrs.Commands
{
    internal interface ICommandBus : IBus, IDisposable
    {
        Task SendAsync<TCommand>(TCommand command) where TCommand : class, ICommand;

        Task<TCommandResult> SendAsync<TCommand, TCommandResult>(TCommand cmd) where TCommand : class, ICommand<TCommandResult> where TCommandResult : class, ICommandResult;

        //void Register<TCommand>(ICommandHandler<TCommand> commandHandler) where TCommand : class, ICommand;

        //void Register<TCommand, TValidator>(ICommandHandler<TCommand> commandHandler) where TCommand : class, ICommand where TValidator : class, IValidator<TCommand>, new();

        //void Register<TCommand, TCommandResult>(ICommandHandler<TCommand, TCommandResult> commandHandler) where TCommand : class, ICommand<TCommandResult> where TCommandResult : class, ICommandResult;

        //void Register<TCommand, TCommandResult, TValidator>(ICommandHandler<TCommand, TCommandResult> commandHandler) where TCommand : class, ICommand<TCommandResult> where TCommandResult : class, ICommandResult where TValidator : class, IValidator<TCommand>, new();

        void AddObserver(ICommandObserver commandObserver);
    }
}
