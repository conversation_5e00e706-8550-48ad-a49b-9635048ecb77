import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { ToastService } from 'src/app/core/services/toast.service';
import { SessionService } from 'src/app/core/services/session.service';
import { VehicleService } from 'src/app/core/services/vehicle.service';
import { VehicleStateService } from 'src/app/core/services/vehicle-state.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  styleUrls: ['home.page.scss'],
  imports: [
    IonicModule,
    CommonModule,
    ReactiveFormsModule,
    RouterModule
  ]
})
export class HomePage implements OnInit, OnDestroy
{
  username: string = 'Usuário';
  hasVehicles: boolean = false;
  isLoading: boolean = true;
  primaryVehicle: any = null;

  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    private router: Router,
    private toastService: ToastService,
    private sessionService: SessionService,
    private vehicleService: VehicleService,
    private vehicleStateService: VehicleStateService
  ) {}

  async ngOnInit(): Promise<void> {
    this.username = await this.sessionService.getUserName() || 'Usuário';

    // Initial check for vehicles
    await this.checkVehicles();

    // Subscribe to primary vehicle changes
    this.subscriptions.push(
      this.vehicleStateService.primaryVehicle$.subscribe(vehicle => {
        if (vehicle) {
          this.primaryVehicle = vehicle;
          this.hasVehicles = true;
        }
      })
    );

    // Subscribe to vehicles list changes
    this.subscriptions.push(
      this.vehicleStateService.vehicles$.subscribe(vehicles => {
        this.hasVehicles = vehicles.length > 0;
      })
    );
  }

  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Checks if the user has any vehicles registered
   */
  async checkVehicles() {
    this.isLoading = true;
    try {
      const userId = await this.sessionService.getUserId() || '';
      this.hasVehicles = await this.vehicleService.hasVehicles(userId);

      if (this.hasVehicles) {
        this.primaryVehicle = await this.vehicleService.getPrimaryVehicle(userId);
      }
    } catch (error) {
      console.error('Error checking vehicles:', error);
      this.toastService.showToast('Erro ao verificar veículos', 'danger');
    } finally {
      this.isLoading = false;
    }
  }




  logout() {
    this.sessionService.clearSession();
    console.log('Logout realizado');
    this.router.navigate(['/login']);
  }
}