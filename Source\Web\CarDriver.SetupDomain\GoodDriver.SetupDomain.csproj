﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Cqrs\**" />
    <Compile Remove="Data\**" />
    <Compile Remove="NHibernate\**" />
    <Compile Remove="Stores\**" />
    <Compile Remove="UnitOfWork\**" />
    <EmbeddedResource Remove="Cqrs\**" />
    <EmbeddedResource Remove="Data\**" />
    <EmbeddedResource Remove="NHibernate\**" />
    <EmbeddedResource Remove="Stores\**" />
    <EmbeddedResource Remove="UnitOfWork\**" />
    <None Remove="Cqrs\**" />
    <None Remove="Data\**" />
    <None Remove="NHibernate\**" />
    <None Remove="Stores\**" />
    <None Remove="UnitOfWork\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Events\IEventBus.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FluentValidation" Version="11.11.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.3" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.3" />
    <PackageReference Include="NHibernate" Version="5.5.2" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Events\" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Eventing">
      <HintPath>..\..\..\..\RogerioFramework\RogerioIT\Eventing\bin\Debug\net8.0\Eventing.dll</HintPath>
    </Reference>
    <Reference Include="Rogerio.NHibernate">
      <HintPath>..\..\..\..\RogerioFramework\RogerioIT\NHibernate\bin\Debug\net8.0\Rogerio.NHibernate.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
