.custom-tabs {
  --background: white;
  --border: none;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  
  ion-tab-button {
    --color: var(--ion-color-medium);
    --color-selected: var(--ion-color-primary);
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 50%;
      width: 0;
      height: 3px;
      background: var(--ion-color-primary);
      transform: translateX(-50%);
      transition: width 0.3s ease;
    }
    
    &.tab-selected {
      &::before {
        width: 40%;
      }
      
      ion-icon {
        transform: translateY(-2px);
      }
    }
    
    ion-icon {
      transition: transform 0.3s ease;
    }
  }
}
