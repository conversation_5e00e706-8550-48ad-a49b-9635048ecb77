import { Component, OnInit, OnDestroy } from '@angular/core';
import { BrandService } from 'src/app/core/services/brand.service';
import { ModelService } from 'src/app/core/services/model.service';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ListBrandResponseDto } from 'src/app/core/dtos/brand/list-brand-responseDto';
import { IonicModule } from '@ionic/angular';
import { Brand } from 'src/app/core/models/brand.model';
import { CommonModule } from '@angular/common';
import { Model } from 'src/app/core/models/model.model';
import { ToastService } from 'src/app/core/services/toast.service';
import { v4 as uuidv4 } from 'uuid';
import { VehicleService } from 'src/app/core/services/vehicle.service';
import { SessionService } from 'src/app/core/services/session.service';
import { NetworkService } from 'src/app/core/services/network.service';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-new-vehicle',
  templateUrl: './new-vehicle.page.html',
  styleUrls: ['./new-vehicle.page.scss'],
  standalone: true,
  imports: [IonicModule, ReactiveFormsModule, CommonModule]
})
export class NewVehiclePage implements OnInit, OnDestroy {

  vehicleForm: FormGroup;
  brands: Brand[] = [];
  models: Model[] = [];
  isSubmitting = false;
  isOnline = true;

  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    private router: Router,
    private brandService: BrandService,
    private modelService: ModelService,
    private fb: FormBuilder,
    private toastService: ToastService,
    private sessionService: SessionService,
    private vehicleService: VehicleService,
    private networkService: NetworkService
  ) {
    this.vehicleForm = this.fb.group({
      brand: [null, Validators.required],
      model: [null, Validators.required],
      version: [null],// opcional
      year: [null, [Validators.required, Validators.min(1990), Validators.max(2026)]],
      plate: [null, [Validators.required]],
      policy: [null] // opcional
    });
   }

  ngOnInit() {
    // Check network status
    this.checkNetworkStatus();

    // Subscribe to network status changes
    this.subscriptions.push(
      this.networkService.getOnlineStatus().subscribe(isOnline => {
        this.isOnline = isOnline;
        if (isOnline) {
          // If we're back online and don't have brands loaded yet, load them
          if (this.brands.length === 0) {
            this.loadBrands();
          }
        }
      })
    );

    // Load brands if online
    if (this.isOnline) {
      this.loadBrands();
    }
  }

  ngOnDestroy() {
    // Clean up subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Check the current network status
   */
  private checkNetworkStatus() {
    this.isOnline = this.networkService.isOnlineNow();
  }

  async loadBrands() {
    try {
      // Check if we're online before trying to load brands
      if (!this.isOnline) {
        this.toastService.showToast('Sem conexão com a internet. Não é possível carregar as marcas de veículos.', 'warning');
        return;
      }

      const data = await this.brandService.listAll();
      this.brands = data;

    } catch (err) {
      console.error('Error to load brands:', err);
      this.toastService.showToast('Erro ao carregar marcas de veículos', 'danger');
    }
  }

  async onBrandChange(branchId: any) {
    try {
      // Check if we're online before trying to load models
      if (!this.isOnline) {
        this.toastService.showToast('Sem conexão com a internet. Não é possível carregar os modelos de veículos.', 'warning');
        return;
      }

      this.vehicleForm.patchValue({ model: null, version: null });
      const data = await this.modelService.getByBranch({ brandId: branchId });
      this.models = data;
    }
    catch (err) {
      console.error('Error to load models:', err);
      this.toastService.showToast('Erro ao carregar modelos de veículos', 'danger');
    }
  }

   async save() {
    if (this.vehicleForm.invalid) {
      this.toastService.showToast('Por favor, preencha todos os campos obrigatórios corretamente');
      return;
    }

    // Check if we're online before trying to save
    if (!this.isOnline) {
      this.toastService.showToast('Sem conexão com a internet. Não é possível cadastrar o veículo.', 'warning');
      return;
    }

    this.isSubmitting = true;

    try {
      const formValues = this.vehicleForm.value;
      const userId = await this.sessionService.getUserId() || '';

      // Find the selected brand and model to get their names
      const selectedBrandId = formValues.brand;
      const selectedModelId = formValues.model;

      // Find the brand and model objects
      const selectedBrand = this.brands.find(brand => brand.id === selectedBrandId);
      const selectedModel = this.models.find(model => model.id === selectedModelId);

      // Validate that we have brand and model information
      if (!selectedBrand || !selectedModel) {
        this.toastService.showToast('Informações de marca ou modelo incompletas. Verifique sua conexão.', 'warning');
        return;
      }

      const vehicleData = {
        vehicleId: uuidv4(),
        userId: userId,
        plate: formValues.plate,
        year: formValues.year,
        brandId: formValues.brand,
        brandName: selectedBrand?.name || '',
        modelId: formValues.model,
        modelName: selectedModel?.name || '',
        version: formValues.version,
        policyNumber: formValues.policy
      };

      // const result = await this.vehicleService.create(vehicleData);
      await this.vehicleService.createLocal(vehicleData);

      this.toastService.showToast('Veículo cadastrado com sucesso!');
      this.router.navigate(['/tabs/vehicles']);
    } catch (error: any) {
      console.error('Error saving vehicle:', error);
      this.toastService.showToast(error.error?.message || 'Erro ao cadastrar veículo', 'danger');
    } finally {
      this.isSubmitting = false;
    }
  }

  // This method is called when the model selection changes
  onModelChange(_modelId: any) {
    // Currently not implemented, but kept for future use
  }

}
