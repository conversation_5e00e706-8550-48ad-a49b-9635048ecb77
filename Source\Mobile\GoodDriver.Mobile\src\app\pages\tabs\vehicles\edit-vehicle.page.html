<ion-header>
  <ion-toolbar>
    <ion-title>Editar Veículo</ion-title>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/tabs/vehicles"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content *ngIf="!isLoading" class="ion-padding">
  <form [formGroup]="vehicleForm" (ngSubmit)="onSubmit()">
    <ion-list>
      <ion-item>
        <ion-label position="stacked">Marca <ion-text color="danger">*</ion-text></ion-label>
        <ion-select formControlName="brand" (ionChange)="onBrandChange($event.detail.value)">
          <ion-select-option *ngFor="let brand of brands" [value]="brand.id">{{ brand.name }}</ion-select-option>
        </ion-select>
      </ion-item>
      <ion-note color="danger" *ngIf="vehicleForm.get('brand')?.invalid && vehicleForm.get('brand')?.touched">
        Selecione a marca
      </ion-note>
      <ion-item>
        <ion-label position="stacked">Modelo <ion-text color="danger">*</ion-text></ion-label>
        <ion-select formControlName="model">
          <ion-select-option *ngFor="let model of models" [value]="model.id">{{ model.name }}</ion-select-option>
        </ion-select>
      </ion-item>
      <ion-note color="danger" *ngIf="vehicleForm.get('model')?.invalid && vehicleForm.get('model')?.touched">
        Selecione o modelo
      </ion-note>
      <ion-item>
        <ion-label position="stacked">Versão</ion-label>
        <ion-input formControlName="version" placeholder="Ex: 1.0 Flex"></ion-input>
      </ion-item>
      <ion-item>
        <ion-label position="stacked">Ano <ion-text color="danger">*</ion-text></ion-label>
        <ion-input type="number" formControlName="year" placeholder="Ex: 2021" min="1990" max="2026"></ion-input>
      </ion-item>
      <ion-note color="danger" *ngIf="vehicleForm.get('year')?.invalid && vehicleForm.get('year')?.touched">
        Informe um ano válido (1990-2026)
      </ion-note>
      <ion-item>
        <ion-label position="stacked">Placa <ion-text color="danger">*</ion-text></ion-label>
        <ion-input formControlName="plate" maxlength="8" placeholder="AAA-0000"></ion-input>
      </ion-item>
      <ion-note color="danger" *ngIf="vehicleForm.get('plate')?.invalid && vehicleForm.get('plate')?.touched">
        Informe a placa
      </ion-note>
      <ion-item>
        <ion-label position="stacked">Número da Apólice</ion-label>
        <ion-input formControlName="policy" placeholder="Opcional"></ion-input>
      </ion-item>
    </ion-list>
    <ion-button expand="block" type="submit" [disabled]="vehicleForm.invalid || isSubmitting">
      <ion-spinner name="crescent" *ngIf="isSubmitting"></ion-spinner>
      <span *ngIf="!isSubmitting">Salvar</span>
    </ion-button>
    <ion-button expand="block" color="medium" fill="outline" (click)="router.navigate(['/tabs/vehicles'])">Cancelar</ion-button>
  </form>
</ion-content>
<ion-spinner *ngIf="isLoading"></ion-spinner> 