import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private baseUrl = environment.apiUrl;
  
  // Mapeamento de endpoints antigos para novos
  private endpointMap = {
    // Usuários
    'create': 'api/user/create',
    'Authentication': 'api/user/authentication',
    
    // Veículos
    'vehicles/createVehicle': 'api/vehicle/create',
    'vehicles/list': 'api/vehicle/list',
    
    // Marcas
    'brands': 'api/brand/list',
    
    // Modelos
    'models': 'api/model/list',
    
    // Jornadas
    'journeys/create': 'api/journey/create',
    'journeys/list': 'api/journey/list',
    'journeys/details': 'api/journey/details'
  };

  constructor() { }

  /**
   * Obtém a URL completa para um endpoint específico
   * @param endpoint O endpoint antigo ou novo
   * @param queryParams Parâmetros de consulta opcionais
   * @returns A URL completa para o endpoint
   */
  getUrl(endpoint: string, queryParams?: Record<string, string | number>): string {
    // Verifica se o endpoint já está no novo formato (começa com 'api/')
    const mappedEndpoint = endpoint.startsWith('api/') 
      ? endpoint 
      : (this.endpointMap as { [key: string]: string })[endpoint] || endpoint;
    
    let url = `${this.baseUrl}/${mappedEndpoint}`;
    
    // Adiciona parâmetros de consulta, se houver
    if (queryParams && Object.keys(queryParams).length > 0) {
      const params = new URLSearchParams();
      Object.entries(queryParams).forEach(([key, value]) => {
        params.append(key, value.toString());
      });
      url += `?${params.toString()}`;
    }
    
    return url;
  }
}

