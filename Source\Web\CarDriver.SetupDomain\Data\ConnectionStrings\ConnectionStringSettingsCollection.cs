﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.SetupDomain.Data.ConnectionStrings
{
    public class ConnectionStringSettingsCollection : IDictionary<string, ConnectionStringSettings>, ICollection<KeyValuePair<string, ConnectionStringSettings>>, IEnumerable<KeyValuePair<string, ConnectionStringSettings>>, IEnumerable
    {
        private readonly Dictionary<string, ConnectionStringSettings> m_ConnectionStrings;

        public int Count => ((ICollection<KeyValuePair<string, ConnectionStringSettings>>)m_ConnectionStrings).Count;

        public bool IsReadOnly => ((ICollection<KeyValuePair<string, ConnectionStringSettings>>)m_ConnectionStrings).IsReadOnly;

        public ConnectionStringSettings this[string key]
        {
            get
            {
                return m_ConnectionStrings[key];
            }
            set
            {
                Add(key, value);
            }
        }

        public ICollection<string> Keys => m_ConnectionStrings.Keys;

        public ICollection<ConnectionStringSettings> Values => m_ConnectionStrings.Values;

        public ConnectionStringSettings Default
        {
            get
            {
                if (m_ConnectionStrings != null)
                {
                    ConnectionStringSettings connectionStringSettings = m_ConnectionStrings.Values.Where((ConnectionStringSettings a) => a.Default).FirstOrDefault();
                    if (connectionStringSettings == null)
                    {
                        connectionStringSettings = m_ConnectionStrings.Values.FirstOrDefault();
                    }

                    return connectionStringSettings;
                }

                return null;
            }
        }

        public ConnectionStringSettingsCollection()
        {
            m_ConnectionStrings = new Dictionary<string, ConnectionStringSettings>();
        }

        public ConnectionStringSettingsCollection(int capacity)
        {
            m_ConnectionStrings = new Dictionary<string, ConnectionStringSettings>(capacity);
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return ((IEnumerable)m_ConnectionStrings).GetEnumerator();
        }

        IEnumerator<KeyValuePair<string, ConnectionStringSettings>> IEnumerable<KeyValuePair<string, ConnectionStringSettings>>.GetEnumerator()
        {
            return ((IEnumerable<KeyValuePair<string, ConnectionStringSettings>>)m_ConnectionStrings).GetEnumerator();
        }

        void ICollection<KeyValuePair<string, ConnectionStringSettings>>.Add(KeyValuePair<string, ConnectionStringSettings> item)
        {
            ((ICollection<KeyValuePair<string, ConnectionStringSettings>>)m_ConnectionStrings).Add(item);
        }

        void ICollection<KeyValuePair<string, ConnectionStringSettings>>.Clear()
        {
            ((ICollection<KeyValuePair<string, ConnectionStringSettings>>)m_ConnectionStrings).Clear();
        }

        bool ICollection<KeyValuePair<string, ConnectionStringSettings>>.Contains(KeyValuePair<string, ConnectionStringSettings> item)
        {
            return ((ICollection<KeyValuePair<string, ConnectionStringSettings>>)m_ConnectionStrings).Contains(item);
        }

        void ICollection<KeyValuePair<string, ConnectionStringSettings>>.CopyTo(KeyValuePair<string, ConnectionStringSettings>[] array, int arrayIndex)
        {
            ((ICollection<KeyValuePair<string, ConnectionStringSettings>>)m_ConnectionStrings).CopyTo(array, arrayIndex);
        }

        bool ICollection<KeyValuePair<string, ConnectionStringSettings>>.Remove(KeyValuePair<string, ConnectionStringSettings> item)
        {
            return ((ICollection<KeyValuePair<string, ConnectionStringSettings>>)m_ConnectionStrings).Remove(item);
        }

        public void Add(string key, ConnectionStringSettings value)
        {
            value.Name = key;
            m_ConnectionStrings.Add(key, value);
        }

        public bool ContainsKey(string key)
        {
            return m_ConnectionStrings.ContainsKey(key);
        }

        public bool Remove(string key)
        {
            return m_ConnectionStrings.Remove(key);
        }

        public bool TryGetValue(string key, out ConnectionStringSettings value)
        {
            return m_ConnectionStrings.TryGetValue(key, out value);
        }

        public IEnumerator GetEnumerator()
        {
            throw new NotImplementedException();
        }
    }
}
