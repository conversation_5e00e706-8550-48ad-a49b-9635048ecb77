﻿using GoodDriver.SetupDomain.Data;
using GoodDriver.SetupDomain.Stores;
using GoodDriver.SetupDomain.UnitOfWork;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.SetupDomain.NHibernate
{
    public static class NhibernateServiceCollectionExtensions
    {
        public static void AddNhibernate(this IServiceCollection services, ISessionFactory sessionFactory, bool isWeb = true, FlushMode flushMode = FlushMode.Manual)
        {
            if (services == null)
            {
                throw new ArgumentNullException("services");
            }

            if (sessionFactory == null)
            {
                throw new ArgumentNullException("sessionFactory");
            }

            services.TryAddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddSingleton((Func<IServiceProvider, IUnitOfWorkFactory<Microsoft.AspNetCore.Http.ISession>>)delegate (IServiceProvider a)
            {
                IUnitOfWorkStore unitOfWorkStore = null;
                unitOfWorkStore = ((!isWeb) ? ((IUnitOfWorkStore)new CallContextUnitOfWorkStore()) : ((IUnitOfWorkStore)new WebUnitOfWorkStore(a.GetRequiredService<IHttpContextAccessor>())));
                return new NHUnitOfWorkFactory(unitOfWorkStore, sessionFactory, flushMode);
            });
            services.AddSingleton((Func<IServiceProvider, IUnitOfWorkFactory>)((IServiceProvider a) => a.GetRequiredService<IUnitOfWorkFactory<Microsoft.AspNetCore.Http.ISession>>()));
        }
    }
}
