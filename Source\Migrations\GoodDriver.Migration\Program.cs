﻿using FluentMigrator.Runner;
using Microsoft.Extensions.CommandLineUtils;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

var app = new CommandLineApplication();

var taskOption = app.Option(
	"-t|--task <TASK>",
	"The task you want FluentMigrator to perform. Available choices are: 'up' or 'down'. Default is 'up'.",
	CommandOptionType.NoValue);

var environment = Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT") ?? "Production";

// Carregar a configuração com base no ambiente
var configuration = new ConfigurationBuilder()
	.SetBasePath(Directory.GetCurrentDirectory())
	.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
	.AddJsonFile($"appsettings.{environment}.json", optional: true, reloadOnChange: true)
	.AddEnvironmentVariables()
	.Build();

// Configuração do Host e execução da migração
try
{
	var host = Host.CreateDefaultBuilder(args)
		.ConfigureServices((context, services) =>
		{
			// Recupera a connection string do arquivo de configuração
			var connectionString = configuration.GetConnectionString("DefaultConnection");

			// Configura o FluentMigrator
			services.AddFluentMigratorCore()
				.ConfigureRunner(runner =>
					runner.AddSqlServer() // Altere aqui BD
						.WithGlobalCommandTimeout(TimeSpan.FromMinutes(15))
						.WithGlobalConnectionString(connectionString) // Conexão com o banco
						.ScanIn(Assembly.GetExecutingAssembly()).For.Migrations() // Escaneia migrações no assembly atual
						.ScanIn(Assembly.GetExecutingAssembly()).For.EmbeddedResources()) // Escaneia migrações embutidas no assembly
				.AddLogging(lb => lb.AddConsole()); // Log para console

			// Serviço que vai rodar as migrações
			services.AddScoped<MigrationService>(); // Registra MigrationService com escopo
		})
		.Build();

	// Executando o serviço de migração explicitamente
	using (var scope = host.Services.CreateScope())
	{
		var migrationService = scope.ServiceProvider.GetRequiredService<MigrationService>();
		await migrationService.StartAsync(CancellationToken.None);  // Chama o StartAsync manualmente
	}

	// Rodando o host
	await host.RunAsync();
}
catch (Exception ex)
{
	Console.WriteLine($"Erro: {ex.Message}");
	throw;
}

// Serviço que vai rodar as migrações
public class MigrationService : IHostedService
{
	private readonly IServiceScopeFactory _serviceScopeFactory;

	public MigrationService(IServiceScopeFactory serviceScopeFactory)
	{
		_serviceScopeFactory = serviceScopeFactory;
	}

	public async Task StartAsync(CancellationToken cancellationToken)
	{
		Console.WriteLine("Iniciando migrações...");

		// Cria um escopo para resolver o IMigrationRunner, que é de escopo
		using (var scope = _serviceScopeFactory.CreateScope())
		{
			var migrationRunner = scope.ServiceProvider.GetRequiredService<IMigrationRunner>();
			migrationRunner.MigrateUp(); // Aplica as migrações
		}

		Console.WriteLine("Migrações aplicadas com sucesso!");
		await Task.CompletedTask;
	}

	public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
}
