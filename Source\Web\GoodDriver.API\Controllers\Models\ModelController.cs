﻿using GoodDriver.API.Controllers.Users;
using GoodDriver.ReadModels.Brands.Queries;
using GoodDriver.ReadModels.Brands.Result;
using GoodDriver.ReadModels.Models._1_Query;
using GoodDriver.ReadModels.Models._2_Result;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Rogerio.Commom.Exceptions;
using Rogerio.Cqrs.Commands;
using Rogerio.Cqrs.Requests;
using Rogerio.Security.Domain;

namespace GoodDriver.API.Controllers.Models
{
    [ApiController]
    [Route("api/model")]
    public class ModelController : BaseControllerGoodDriver
    {
        private readonly ICommandBus commandBus;
        private readonly IConfiguration _config;
        private readonly IRequestBus requestBus;

        public ModelController(IConfiguration config, ILogger<UserController> logger, ISecurityManager securityManager, ICommandBus _commandBus, IRequestBus requestBus) : base(securityManager)
        {
            _config = config;
            commandBus = _commandBus;
            this.requestBus = requestBus;
        }

        [Authorize]
        [HttpGet("list")]
        public async Task<IActionResult> ListModels([FromQuery] int brandId)
        {
            try
            {
                if(brandId <= 0)
                    return BadRequest(new { message = "Brand ID is required." });

                var models = await requestBus.RequestAsync<ModelListQuery, ModelListResult>(new ModelListQuery(brandId));

                return Ok(models);
            }
            catch (BusinessException ex)
            {
                return BadRequest(new { message = ex.Message, errorCode = ex.Key });
            }
            catch (Exception exception)
            {
                return BadRequest(new { message = exception.Message });
            }
        }
    }
}
