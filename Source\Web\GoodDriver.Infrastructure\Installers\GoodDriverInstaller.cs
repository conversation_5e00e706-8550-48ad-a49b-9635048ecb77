﻿using FluentNHibernate.Cfg;
using FluentNHibernate.Cfg.Db;
using GoodDriver.Infrastructure.Database.Maps;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NHibernate;
using Rogerio.Cqrs.Bus;
using Rogerio.Data.NHibernate;
using System.Xml.Linq;

namespace GoodDriver.Infrastructure.Installers
{
	public class GoodDriverInstaller
    {
        public static void Install(IConfiguration configuration, IServiceCollection services)
        {
            var nhFactory = CreateNHFactory(configuration, Common.Constants.DefaultConnectionStringName);

            services.AddNhibernate(nhFactory);
            services.AddSingleton<MemoryContainerBus>();
            //services.AddSingleton<ICacheService, MemoryCacheService>();
            //services.AddSingleton<IMemoryCache, MemoryCache>();
            //services.AddNotification(new NotificationOptions(Conecti.Notification.NotificationType.AmazonEmail, "SES"));

            //AutoMappersInstaller.Install(services);
            RepositoryInstaller.Install(services);
            QueryInstaller.Install(services);
            CommandInstaller.Install(services);
            ServiceInstaller.Install(services);
            EventInstaller.Install(services);
        }

		private static ISessionFactory CreateNHFactory(IConfiguration configuration, string connectionStringName)
		{
			// Acessa a seção ConnectionStrings
			var connectionSection = configuration.GetSection("ConnectionStrings").GetSection(connectionStringName).GetSection("ConnectionString").Value;			

			// Verifica se a string de conexão foi encontrada
			if (string.IsNullOrEmpty(connectionSection))
			{
				throw new InvalidOperationException($"Connection string '{connectionStringName}' not found in configuration.");
			}

			// Obtém a string de conexão
			var connection = connectionSection;

			// Configura o NHibernate
			return Fluently
			   .Configure()
			   .Database(MsSqlConfiguration.MsSql2012
			   .ConnectionString(connection))
			   .Mappings(m =>
							m.FluentMappings.AddFromAssemblyOf<UserMap>()
							.Conventions.Add(FluentNHibernate.Conventions.Helpers.DefaultLazy.Never())
						 )
			   .BuildSessionFactory();
		}
	}
}
