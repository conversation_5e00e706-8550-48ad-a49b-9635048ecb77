import { Component, OnInit } from '@angular/core';
import { IonApp, IonRouterOutlet } from '@ionic/angular/standalone';
import { Platform } from '@ionic/angular';
import { SQLiteStorageService } from './core/storage/sqlite-storage.service';

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  imports: [IonApp, IonRouterOutlet],
})
export class AppComponent implements OnInit {
  constructor(
    private platform: Platform,
    private sqliteService: SQLiteStorageService
  ) {}

  async ngOnInit() {
    await this.platform.ready();

    // Initialize SQLite database
    try {
      await this.sqliteService.init();
      console.log('SQLite initialized in app component');
    } catch (error) {
      console.error('Error initializing SQLite:', error);
    }
  }
}
