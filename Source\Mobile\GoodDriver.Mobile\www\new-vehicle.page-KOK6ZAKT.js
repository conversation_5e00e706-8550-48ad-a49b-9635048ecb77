import {
  BrandService,
  ModelService,
  init_brand_service,
  init_model_service
} from "./chunk-FNHWDMEZ.js";
import {
  init_esm_browser,
  v4_default
} from "./chunk-ZCUEDWU7.js";
import {
  CommonModule,
  Component,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  IonBackButton2 as IonBackButton,
  IonButton,
  IonButtons,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonContent,
  IonHeader,
  IonIcon,
  IonInput,
  IonItem,
  IonLabel,
  IonMaxValidator,
  IonMinValidator,
  IonNote,
  IonSelect,
  IonSelectOption,
  IonSpinner,
  IonText,
  IonTitle,
  IonToolbar,
  IonicModule,
  MaxLengthValidator,
  NetworkService,
  NgControlStatus,
  NgControlStatusGroup,
  NgForOf,
  NgIf,
  NumericValueAccessorDirective,
  ReactiveFormsModule,
  Router,
  SelectValueAccessorDirective,
  SessionService,
  TextValueAccessorDirective,
  ToastService,
  Validators,
  VehicleService,
  init_common,
  init_core,
  init_forms,
  init_ionic_angular,
  init_network_service,
  init_router,
  init_session_service,
  init_toast_service,
  init_vehicle_service,
  setClassMetadata,
  ɵNgNoValidate,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate
} from "./chunk-2IV7NJLP.js";
import "./chunk-GJIVKQBF.js";
import "./chunk-WY354WYL.js";
import "./chunk-3TAP6XVY.js";
import "./chunk-BQEXAUCY.js";
import "./chunk-EUHNYIV3.js";
import "./chunk-AY6ZGCP5.js";
import "./chunk-I3UCYTIQ.js";
import "./chunk-4JI7VCBZ.js";
import "./chunk-YY7NCLAA.js";
import "./chunk-GUQ63TTB.js";
import "./chunk-CHFZ6R2P.js";
import "./chunk-XPKADKFH.js";
import "./chunk-V6QEVD67.js";
import "./chunk-WNPNB2PX.js";
import "./chunk-NRG74LSH.js";
import "./chunk-YSMEPS6E.js";
import "./chunk-NHEUXSZ2.js";
import "./chunk-WX4TOLMF.js";
import "./chunk-6SXGDLUH.js";
import "./chunk-NNIHVF7T.js";
import "./chunk-ZNALOMZJ.js";
import "./chunk-GHA2GP6C.js";
import "./chunk-H2CU6QHT.js";
import "./chunk-6PMAHZVS.js";
import "./chunk-DOEZTQY3.js";
import "./chunk-O4BCOVEV.js";
import "./chunk-TD4SIQNM.js";
import "./chunk-VU5YACRM.js";
import {
  __async,
  __esm
} from "./chunk-4W6HR7MY.js";

// src/app/pages/tabs/vehicles/create-vehicle/new-vehicle.page.ts
function NewVehiclePage_ion_card_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-card", 19)(1, "ion-card-header")(2, "ion-card-title");
    \u0275\u0275element(3, "ion-icon", 20);
    \u0275\u0275text(4, " Aten\xE7\xE3o ");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(5, "ion-card-content")(6, "p");
    \u0275\u0275text(7, "Para cadastrar um ve\xEDculo, \xE9 necess\xE1rio estar conectado \xE0 internet para obter as marcas e modelos atualizados.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "p");
    \u0275\u0275text(9, "Por favor, verifique sua conex\xE3o e tente novamente.");
    \u0275\u0275elementEnd()()();
  }
}
function NewVehiclePage_ion_card_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-card", 21)(1, "ion-card-content");
    \u0275\u0275element(2, "ion-icon", 22);
    \u0275\u0275elementStart(3, "span");
    \u0275\u0275text(4, " \xC9 necess\xE1rio estar conectado \xE0 internet para obter as marcas e modelos de ve\xEDculos atualizados.");
    \u0275\u0275elementEnd()()();
  }
}
function NewVehiclePage_ion_select_option_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-select-option", 23);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const brand_r1 = ctx.$implicit;
    \u0275\u0275property("value", brand_r1.id);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(brand_r1.name);
  }
}
function NewVehiclePage_ion_select_option_19_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-select-option", 23);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const model_r2 = ctx.$implicit;
    \u0275\u0275property("value", model_r2.id);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(model_r2.name);
  }
}
function NewVehiclePage_ion_note_32_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-note", 24);
    \u0275\u0275text(1, " Formato inv\xE1lido. Use o formato AAA-0000 ");
    \u0275\u0275elementEnd();
  }
}
function NewVehiclePage_ion_spinner_38_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "ion-spinner", 25);
  }
}
function NewVehiclePage_span_39_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "Salvar");
    \u0275\u0275elementEnd();
  }
}
function NewVehiclePage_div_40_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 26)(1, "ion-text", 27)(2, "p", 28);
    \u0275\u0275element(3, "ion-icon", 20);
    \u0275\u0275text(4, " N\xE3o \xE9 poss\xEDvel salvar sem conex\xE3o com a internet ");
    \u0275\u0275elementEnd()()();
  }
}
var _NewVehiclePage, NewVehiclePage;
var init_new_vehicle_page = __esm({
  "src/app/pages/tabs/vehicles/create-vehicle/new-vehicle.page.ts"() {
    init_core();
    init_forms();
    init_ionic_angular();
    init_common();
    init_esm_browser();
    init_core();
    init_router();
    init_brand_service();
    init_model_service();
    init_forms();
    init_toast_service();
    init_session_service();
    init_vehicle_service();
    init_network_service();
    init_ionic_angular();
    init_common();
    _NewVehiclePage = class _NewVehiclePage {
      constructor(router, brandService, modelService, fb, toastService, sessionService, vehicleService, networkService) {
        this.router = router;
        this.brandService = brandService;
        this.modelService = modelService;
        this.fb = fb;
        this.toastService = toastService;
        this.sessionService = sessionService;
        this.vehicleService = vehicleService;
        this.networkService = networkService;
        this.brands = [];
        this.models = [];
        this.isSubmitting = false;
        this.isOnline = true;
        this.subscriptions = [];
        this.vehicleForm = this.fb.group({
          brand: [null, Validators.required],
          model: [null, Validators.required],
          version: [null],
          // opcional
          year: [null, [Validators.required, Validators.min(1990), Validators.max(2026)]],
          plate: [null, [Validators.required]],
          policy: [null]
          // opcional
        });
      }
      ngOnInit() {
        this.checkNetworkStatus();
        this.subscriptions.push(this.networkService.getOnlineStatus().subscribe((isOnline) => {
          this.isOnline = isOnline;
          if (isOnline) {
            if (this.brands.length === 0) {
              this.loadBrands();
            }
          }
        }));
        if (this.isOnline) {
          this.loadBrands();
        }
      }
      ngOnDestroy() {
        this.subscriptions.forEach((sub) => sub.unsubscribe());
      }
      /**
       * Check the current network status
       */
      checkNetworkStatus() {
        this.isOnline = this.networkService.isOnlineNow();
      }
      loadBrands() {
        return __async(this, null, function* () {
          try {
            if (!this.isOnline) {
              this.toastService.showToast("Sem conex\xE3o com a internet. N\xE3o \xE9 poss\xEDvel carregar as marcas de ve\xEDculos.", "warning");
              return;
            }
            const data = yield this.brandService.listAll();
            this.brands = data;
          } catch (err) {
            console.error("Error to load brands:", err);
            this.toastService.showToast("Erro ao carregar marcas de ve\xEDculos", "danger");
          }
        });
      }
      onBrandChange(branchId) {
        return __async(this, null, function* () {
          try {
            if (!this.isOnline) {
              this.toastService.showToast("Sem conex\xE3o com a internet. N\xE3o \xE9 poss\xEDvel carregar os modelos de ve\xEDculos.", "warning");
              return;
            }
            this.vehicleForm.patchValue({ model: null, version: null });
            const data = yield this.modelService.getByBranch({ brandId: branchId });
            this.models = data;
          } catch (err) {
            console.error("Error to load models:", err);
            this.toastService.showToast("Erro ao carregar modelos de ve\xEDculos", "danger");
          }
        });
      }
      save() {
        return __async(this, null, function* () {
          var _a;
          if (this.vehicleForm.invalid) {
            this.toastService.showToast("Por favor, preencha todos os campos obrigat\xF3rios corretamente");
            return;
          }
          if (!this.isOnline) {
            this.toastService.showToast("Sem conex\xE3o com a internet. N\xE3o \xE9 poss\xEDvel cadastrar o ve\xEDculo.", "warning");
            return;
          }
          this.isSubmitting = true;
          try {
            const formValues = this.vehicleForm.value;
            const userId = (yield this.sessionService.getUserId()) || "";
            const selectedBrandId = formValues.brand;
            const selectedModelId = formValues.model;
            const selectedBrand = this.brands.find((brand) => brand.id === selectedBrandId);
            const selectedModel = this.models.find((model) => model.id === selectedModelId);
            if (!selectedBrand || !selectedModel) {
              this.toastService.showToast("Informa\xE7\xF5es de marca ou modelo incompletas. Verifique sua conex\xE3o.", "warning");
              return;
            }
            const vehicleData = {
              vehicleId: v4_default(),
              userId,
              plate: formValues.plate,
              year: formValues.year,
              brandId: formValues.brand,
              brandName: (selectedBrand == null ? void 0 : selectedBrand.name) || "",
              modelId: formValues.model,
              modelName: (selectedModel == null ? void 0 : selectedModel.name) || "",
              version: formValues.version,
              policyNumber: formValues.policy
            };
            yield this.vehicleService.createLocal(vehicleData);
            this.toastService.showToast("Ve\xEDculo cadastrado com sucesso!");
            this.router.navigate(["/tabs/vehicles"]);
          } catch (error) {
            console.error("Error saving vehicle:", error);
            this.toastService.showToast(((_a = error.error) == null ? void 0 : _a.message) || "Erro ao cadastrar ve\xEDculo", "danger");
          } finally {
            this.isSubmitting = false;
          }
        });
      }
      // This method is called when the model selection changes
      onModelChange(_modelId) {
      }
    };
    _NewVehiclePage.\u0275fac = function NewVehiclePage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _NewVehiclePage)(\u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(BrandService), \u0275\u0275directiveInject(ModelService), \u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(ToastService), \u0275\u0275directiveInject(SessionService), \u0275\u0275directiveInject(VehicleService), \u0275\u0275directiveInject(NetworkService));
    };
    _NewVehiclePage.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _NewVehiclePage, selectors: [["app-new-vehicle"]], decls: 41, vars: 10, consts: [["slot", "start"], ["defaultHref", "/tabs/vehicles"], [1, "ion-padding"], ["color", "warning", 4, "ngIf"], ["color", "light", 4, "ngIf"], [3, "formGroup"], ["position", "stacked"], ["formControlName", "brand", "interface", "popover", 3, "ionChange"], [3, "value", 4, "ngFor", "ngForOf"], ["formControlName", "model", "interface", "popover", 3, "ionChange"], ["formControlName", "version", "interface", "popover"], ["formControlName", "year", "type", "number", "placeholder", "Ex: 2021", "min", "1900", "max", "2099"], ["formControlName", "plate", "maxlength", "8", "placeholder", "AAA-0000", "inputmode", "text"], ["slot", "error", 4, "ngIf"], ["formControlName", "policy", "type", "text"], ["expand", "block", 1, "ion-margin-top", 3, "click", "disabled"], ["name", "crescent", 4, "ngIf"], [4, "ngIf"], ["class", "offline-message", 4, "ngIf"], ["color", "warning"], ["name", "wifi-outline"], ["color", "light"], ["name", "information-circle-outline", "color", "primary"], [3, "value"], ["slot", "error"], ["name", "crescent"], [1, "offline-message"], ["color", "danger"], [1, "ion-text-center"]], template: function NewVehiclePage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header")(1, "ion-toolbar")(2, "ion-title");
        \u0275\u0275text(3, "Novo Ve\xEDculo");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(4, "ion-buttons", 0);
        \u0275\u0275element(5, "ion-back-button", 1);
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(6, "ion-content", 2);
        \u0275\u0275template(7, NewVehiclePage_ion_card_7_Template, 10, 0, "ion-card", 3)(8, NewVehiclePage_ion_card_8_Template, 5, 0, "ion-card", 4);
        \u0275\u0275elementStart(9, "form", 5)(10, "ion-item")(11, "ion-label", 6);
        \u0275\u0275text(12, "Marca");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(13, "ion-select", 7);
        \u0275\u0275listener("ionChange", function NewVehiclePage_Template_ion_select_ionChange_13_listener($event) {
          return ctx.onBrandChange($event.detail.value);
        });
        \u0275\u0275template(14, NewVehiclePage_ion_select_option_14_Template, 2, 2, "ion-select-option", 8);
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(15, "ion-item")(16, "ion-label", 6);
        \u0275\u0275text(17, "Modelo");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(18, "ion-select", 9);
        \u0275\u0275listener("ionChange", function NewVehiclePage_Template_ion_select_ionChange_18_listener($event) {
          return ctx.onModelChange($event.detail.value);
        });
        \u0275\u0275template(19, NewVehiclePage_ion_select_option_19_Template, 2, 2, "ion-select-option", 8);
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(20, "ion-item")(21, "ion-label", 6);
        \u0275\u0275text(22, "Vers\xE3o");
        \u0275\u0275elementEnd();
        \u0275\u0275element(23, "ion-select", 10);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(24, "ion-item")(25, "ion-label", 6);
        \u0275\u0275text(26, "Ano");
        \u0275\u0275elementEnd();
        \u0275\u0275element(27, "ion-input", 11);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(28, "ion-item")(29, "ion-label", 6);
        \u0275\u0275text(30, "Placa");
        \u0275\u0275elementEnd();
        \u0275\u0275element(31, "ion-input", 12);
        \u0275\u0275template(32, NewVehiclePage_ion_note_32_Template, 2, 0, "ion-note", 13);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(33, "ion-item")(34, "ion-label", 6);
        \u0275\u0275text(35, "N\xFAmero da Ap\xF3lice (opcional)");
        \u0275\u0275elementEnd();
        \u0275\u0275element(36, "ion-input", 14);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(37, "ion-button", 15);
        \u0275\u0275listener("click", function NewVehiclePage_Template_ion_button_click_37_listener() {
          return ctx.save();
        });
        \u0275\u0275template(38, NewVehiclePage_ion_spinner_38_Template, 1, 0, "ion-spinner", 16)(39, NewVehiclePage_span_39_Template, 2, 0, "span", 17);
        \u0275\u0275elementEnd();
        \u0275\u0275template(40, NewVehiclePage_div_40_Template, 5, 0, "div", 18);
        \u0275\u0275elementEnd()();
      }
      if (rf & 2) {
        let tmp_5_0;
        \u0275\u0275advance(7);
        \u0275\u0275property("ngIf", !ctx.isOnline);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", ctx.isOnline);
        \u0275\u0275advance();
        \u0275\u0275property("formGroup", ctx.vehicleForm);
        \u0275\u0275advance(5);
        \u0275\u0275property("ngForOf", ctx.brands);
        \u0275\u0275advance(5);
        \u0275\u0275property("ngForOf", ctx.models);
        \u0275\u0275advance(13);
        \u0275\u0275property("ngIf", ((tmp_5_0 = ctx.vehicleForm.get("plate")) == null ? null : tmp_5_0.hasError("pattern")) && ((tmp_5_0 = ctx.vehicleForm.get("plate")) == null ? null : tmp_5_0.touched));
        \u0275\u0275advance(5);
        \u0275\u0275property("disabled", ctx.vehicleForm.invalid || ctx.isSubmitting || !ctx.isOnline);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", ctx.isSubmitting);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", !ctx.isSubmitting);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", !ctx.isOnline);
      }
    }, dependencies: [IonicModule, IonButton, IonButtons, IonCard, IonCardContent, IonCardHeader, IonCardTitle, IonContent, IonHeader, IonIcon, IonInput, IonItem, IonLabel, IonNote, IonSelect, IonSelectOption, IonSpinner, IonText, IonTitle, IonToolbar, NumericValueAccessorDirective, SelectValueAccessorDirective, TextValueAccessorDirective, IonBackButton, IonMinValidator, IonMaxValidator, ReactiveFormsModule, \u0275NgNoValidate, NgControlStatus, NgControlStatusGroup, MaxLengthValidator, FormGroupDirective, FormControlName, CommonModule, NgForOf, NgIf], styles: ["\n\nion-card[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n}\nion-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  font-size: 18px;\n}\nion-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  margin-right: 8px;\n  font-size: 24px;\n}\nion-card[color=light][_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 12px;\n}\nion-card[color=light][_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 20px;\n  margin-right: 8px;\n}\n.offline-message[_ngcontent-%COMP%] {\n  margin-top: 16px;\n}\n.offline-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.offline-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  margin-right: 8px;\n  font-size: 18px;\n}\nion-item[_ngcontent-%COMP%] {\n  margin-bottom: 8px;\n}\n/*# sourceMappingURL=new-vehicle.page.css.map */"] });
    NewVehiclePage = _NewVehiclePage;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NewVehiclePage, [{
        type: Component,
        args: [{ selector: "app-new-vehicle", standalone: true, imports: [IonicModule, ReactiveFormsModule, CommonModule], template: `<ion-header>
  <ion-toolbar>
    <ion-title>Novo Ve\xEDculo</ion-title>
  </ion-toolbar>
  <ion-buttons slot="start">
    <ion-back-button defaultHref="/tabs/vehicles"></ion-back-button>
  </ion-buttons>
</ion-header>

<ion-content class="ion-padding">
  <!-- Alerta de conex\xE3o com a internet -->
  <ion-card color="warning" *ngIf="!isOnline">
    <ion-card-header>
      <ion-card-title>
        <ion-icon name="wifi-outline"></ion-icon>
        Aten\xE7\xE3o
      </ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <p>Para cadastrar um ve\xEDculo, \xE9 necess\xE1rio estar conectado \xE0 internet para obter as marcas e modelos atualizados.</p>
      <p>Por favor, verifique sua conex\xE3o e tente novamente.</p>
    </ion-card-content>
  </ion-card>

  <!-- Alerta informativo -->
  <ion-card color="light" *ngIf="isOnline">
    <ion-card-content>
      <ion-icon name="information-circle-outline" color="primary"></ion-icon>
      <span> \xC9 necess\xE1rio estar conectado \xE0 internet para obter as marcas e modelos de ve\xEDculos atualizados.</span>
    </ion-card-content>
  </ion-card>

  <form [formGroup]="vehicleForm">
    <!-- Marca -->
    <ion-item>
      <ion-label position="stacked">Marca</ion-label>
      <ion-select formControlName="brand" (ionChange)="onBrandChange($event.detail.value)" interface="popover">
        <ion-select-option *ngFor="let brand of brands" [value]="brand.id">{{ brand.name }}</ion-select-option>
      </ion-select>
    </ion-item>

    <!-- Modelo -->
    <ion-item>
      <ion-label position="stacked">Modelo</ion-label>
      <ion-select formControlName="model" (ionChange)="onModelChange($event.detail.value)" interface="popover">
        <ion-select-option *ngFor="let model of models" [value]="model.id">{{ model.name }}</ion-select-option>
      </ion-select>
    </ion-item>

    <!-- Vers\xE3o -->
    <ion-item>
      <ion-label position="stacked">Vers\xE3o</ion-label>
      <ion-select formControlName="version" interface="popover">
        <!-- <ion-select-option *ngFor="let versao of versoes" [value]="versao.id">{{ versao.nome }}</ion-select-option> -->
      </ion-select>
    </ion-item>

    <!-- Ano -->
    <ion-item>
      <ion-label position="stacked">Ano</ion-label>
      <ion-input formControlName="year" type="number" placeholder="Ex: 2021" min="1900" max="2099"></ion-input>
    </ion-item>

    <!-- Placa -->
    <ion-item>
      <ion-label position="stacked">Placa</ion-label>
      <ion-input formControlName="plate" maxlength="8" placeholder="AAA-0000" inputmode="text"></ion-input>
      <ion-note slot="error" *ngIf="vehicleForm.get('plate')?.hasError('pattern') && vehicleForm.get('plate')?.touched">
        Formato inv\xE1lido. Use o formato AAA-0000
      </ion-note>
    </ion-item>

    <!-- Ap\xF3lice (opcional) -->
    <ion-item>
      <ion-label position="stacked">N\xFAmero da Ap\xF3lice (opcional)</ion-label>
      <ion-input formControlName="policy" type="text"></ion-input>
    </ion-item>

    <!-- Bot\xE3o salvar -->
    <ion-button expand="block" class="ion-margin-top" (click)="save()" [disabled]="vehicleForm.invalid || isSubmitting || !isOnline">
      <ion-spinner name="crescent" *ngIf="isSubmitting"></ion-spinner>
      <span *ngIf="!isSubmitting">Salvar</span>
    </ion-button>

    <!-- Mensagem de erro quando offline -->
    <div *ngIf="!isOnline" class="offline-message">
      <ion-text color="danger">
        <p class="ion-text-center">
          <ion-icon name="wifi-outline"></ion-icon>
          N\xE3o \xE9 poss\xEDvel salvar sem conex\xE3o com a internet
        </p>
      </ion-text>
    </div>
  </form>
</ion-content>
`, styles: ["/* src/app/pages/tabs/vehicles/create-vehicle/new-vehicle.page.scss */\nion-card {\n  margin-bottom: 16px;\n}\nion-card ion-card-title {\n  display: flex;\n  align-items: center;\n  font-size: 18px;\n}\nion-card ion-card-title ion-icon {\n  margin-right: 8px;\n  font-size: 24px;\n}\nion-card[color=light] ion-card-content {\n  display: flex;\n  align-items: center;\n  padding: 12px;\n}\nion-card[color=light] ion-card-content ion-icon {\n  font-size: 20px;\n  margin-right: 8px;\n}\n.offline-message {\n  margin-top: 16px;\n}\n.offline-message p {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.offline-message p ion-icon {\n  margin-right: 8px;\n  font-size: 18px;\n}\nion-item {\n  margin-bottom: 8px;\n}\n/*# sourceMappingURL=new-vehicle.page.css.map */\n"] }]
      }], () => [{ type: Router }, { type: BrandService }, { type: ModelService }, { type: FormBuilder }, { type: ToastService }, { type: SessionService }, { type: VehicleService }, { type: NetworkService }], null);
    })();
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(NewVehiclePage, { className: "NewVehiclePage", filePath: "src/app/pages/tabs/vehicles/create-vehicle/new-vehicle.page.ts", lineNumber: 25 });
    })();
  }
});
init_new_vehicle_page();
export {
  NewVehiclePage
};
//# sourceMappingURL=new-vehicle.page-KOK6ZAKT.js.map
