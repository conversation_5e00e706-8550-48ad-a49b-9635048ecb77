import { Injectable } from '@angular/core';
import { Device } from '@capacitor/device';
import { Platform } from '@ionic/angular';
import { v4 as uuidv4 } from 'uuid';

@Injectable({
  providedIn: 'root'
})
export class DeviceService {
  private readonly DEVICE_ID_KEY = 'gooddriver_device_id';
  private readonly DEVICE_INFO_KEY = 'gooddriver_device_info';
  
  constructor(private platform: Platform) {}
  
  /**
   * Gets or generates a unique device identifier
   * @returns A promise that resolves to the device ID
   */
  async getDeviceId(): Promise<string> {
    // Try to get the stored device ID first
    const storedId = localStorage.getItem(this.DEVICE_ID_KEY);
    
    if (storedId) {
      return storedId;
    }
    
    // If no stored ID, generate a new one
    const newId = await this.generateDeviceId();
    localStorage.setItem(this.DEVICE_ID_KEY, newId);
    
    return newId;
  }
  
  /**
   * Gets information about the device
   * @returns A promise that resolves to an object with device information
   */
  async getDeviceInfo(): Promise<any> {
    // Try to get the stored device info first
    const storedInfo = localStorage.getItem(this.DEVICE_INFO_KEY);
    
    if (storedInfo) {
      return JSON.parse(storedInfo);
    }
    
    // If no stored info, collect it
    const info = await this.collectDeviceInfo();
    localStorage.setItem(this.DEVICE_INFO_KEY, JSON.stringify(info));
    
    return info;
  }
  
  /**
   * Generates a unique device identifier
   * @returns A promise that resolves to a device ID
   */
  private async generateDeviceId(): Promise<string> {
    try {
      // Try to use the native device ID if available
      if (this.platform.is('capacitor') || this.platform.is('cordova')) {
        const info = await Device.getId();
        if (info && info.identifier) {
          return info.identifier;
        }
      }
    } catch (error) {
      console.error('Error getting native device ID:', error);
    }
    
    // Fallback to a UUID
    return uuidv4();
  }
  
  /**
   * Collects information about the device
   * @returns A promise that resolves to an object with device information
   */
  private async collectDeviceInfo(): Promise<any> {
    const info: any = {
      platform: this.platform.platforms().join(', '),
      userAgent: navigator.userAgent
    };
    
    try {
      // Try to get more detailed device info if available
      if (this.platform.is('capacitor') || this.platform.is('cordova')) {
        const deviceInfo = await Device.getInfo();
        info.model = deviceInfo.model;
        info.platform = deviceInfo.platform;
        info.operatingSystem = deviceInfo.operatingSystem;
        info.osVersion = deviceInfo.osVersion;
        info.manufacturer = deviceInfo.manufacturer;
        info.isVirtual = deviceInfo.isVirtual;
        info.webViewVersion = deviceInfo.webViewVersion;
      }
    } catch (error) {
      console.error('Error getting detailed device info:', error);
    }
    
    return info;
  }
}
