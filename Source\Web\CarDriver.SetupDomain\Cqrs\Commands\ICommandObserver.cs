﻿using GoodDriver.SetupDomain.Cqrs.Exceptions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.SetupDomain.Cqrs.Commands
{
    public interface ICommandObserver
    {
        Task PreSend(ICommandContext command);

        Task PosSend(ICommandContext command, IExceptionContext exception);

        Task PreExecute(ICommandContext command);

        //Task PosExecute(ICommandContext command, ICommandResultContext commandResult, IExceptionContext exception);
    }
}
