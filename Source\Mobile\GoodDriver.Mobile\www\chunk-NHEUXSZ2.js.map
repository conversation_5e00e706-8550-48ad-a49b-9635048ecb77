{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/hardware-back-button-a7eb8233.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-a5d50daf.js';\nimport { c as config, d as printIonError } from './index-cfd9c1f2.js';\n\n/**\n * CloseWatcher is a newer API that lets\n * use detect the hardware back button event\n * in a web browser: https://caniuse.com/?search=closewatcher\n * However, not every browser supports it yet.\n *\n * This needs to be a function so that we can\n * check the config once it has been set.\n * Otherwise, this code would be evaluated the\n * moment this file is evaluated which could be\n * before the config is set.\n */\nconst shouldUseCloseWatcher = () => config.get('experimentalCloseWatcher', false) && win !== undefined && 'CloseWatcher' in win;\n/**\n * When hardwareBackButton: false in config,\n * we need to make sure we also block the default\n * webview behavior. If we don't then it will be\n * possible for users to navigate backward while\n * an overlay is still open. Additionally, it will\n * give the appearance that the hardwareBackButton\n * config is not working as the page transition\n * will still happen.\n */\nconst blockHardwareBackButton = () => {\n  document.addEventListener('backbutton', () => {}); // eslint-disable-line\n};\nconst startHardwareBackButton = () => {\n  const doc = document;\n  let busy = false;\n  const backButtonCallback = () => {\n    if (busy) {\n      return;\n    }\n    let index = 0;\n    let handlers = [];\n    const ev = new CustomEvent('ionBackButton', {\n      bubbles: false,\n      detail: {\n        register(priority, handler) {\n          handlers.push({\n            priority,\n            handler,\n            id: index++\n          });\n        }\n      }\n    });\n    doc.dispatchEvent(ev);\n    const executeAction = async handlerRegister => {\n      try {\n        if (handlerRegister === null || handlerRegister === void 0 ? void 0 : handlerRegister.handler) {\n          const result = handlerRegister.handler(processHandlers);\n          if (result != null) {\n            await result;\n          }\n        }\n      } catch (e) {\n        printIonError('[ion-app] - Exception in startHardwareBackButton:', e);\n      }\n    };\n    const processHandlers = () => {\n      if (handlers.length > 0) {\n        let selectedHandler = {\n          priority: Number.MIN_SAFE_INTEGER,\n          handler: () => undefined,\n          id: -1\n        };\n        handlers.forEach(handler => {\n          if (handler.priority >= selectedHandler.priority) {\n            selectedHandler = handler;\n          }\n        });\n        busy = true;\n        handlers = handlers.filter(handler => handler.id !== selectedHandler.id);\n        executeAction(selectedHandler).then(() => busy = false);\n      }\n    };\n    processHandlers();\n  };\n  /**\n   * If the CloseWatcher is defined then\n   * we don't want to also listen for the native\n   * backbutton event otherwise we may get duplicate\n   * events firing.\n   */\n  if (shouldUseCloseWatcher()) {\n    let watcher;\n    const configureWatcher = () => {\n      watcher === null || watcher === void 0 ? void 0 : watcher.destroy();\n      watcher = new win.CloseWatcher();\n      /**\n       * Once a close request happens\n       * the watcher gets destroyed.\n       * As a result, we need to re-configure\n       * the watcher so we can respond to other\n       * close requests.\n       */\n      watcher.onclose = () => {\n        backButtonCallback();\n        configureWatcher();\n      };\n    };\n    configureWatcher();\n  } else {\n    doc.addEventListener('backbutton', backButtonCallback);\n  }\n};\nconst OVERLAY_BACK_BUTTON_PRIORITY = 100;\nconst MENU_BACK_BUTTON_PRIORITY = 99; // 1 less than overlay priority since menu is displayed behind overlays\n\nexport { MENU_BACK_BUTTON_PRIORITY, OVERLAY_BACK_BUTTON_PRIORITY, blockHardwareBackButton, shouldUseCloseWatcher, startHardwareBackButton };"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAkBM,uBAWA,yBAGA,yBAiFA,8BACA;AAlHN;AAAA;AAGA;AACA;AAcA,IAAM,wBAAwB,MAAM,OAAO,IAAI,4BAA4B,KAAK,KAAK,QAAQ,UAAa,kBAAkB;AAW5H,IAAM,0BAA0B,MAAM;AACpC,eAAS,iBAAiB,cAAc,MAAM;AAAA,MAAC,CAAC;AAAA,IAClD;AACA,IAAM,0BAA0B,MAAM;AACpC,YAAM,MAAM;AACZ,UAAI,OAAO;AACX,YAAM,qBAAqB,MAAM;AAC/B,YAAI,MAAM;AACR;AAAA,QACF;AACA,YAAI,QAAQ;AACZ,YAAI,WAAW,CAAC;AAChB,cAAM,KAAK,IAAI,YAAY,iBAAiB;AAAA,UAC1C,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,SAAS,UAAU,SAAS;AAC1B,uBAAS,KAAK;AAAA,gBACZ;AAAA,gBACA;AAAA,gBACA,IAAI;AAAA,cACN,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF,CAAC;AACD,YAAI,cAAc,EAAE;AACpB,cAAM,gBAAgB,CAAM,oBAAmB;AAC7C,cAAI;AACF,gBAAI,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,SAAS;AAC7F,oBAAM,SAAS,gBAAgB,QAAQ,eAAe;AACtD,kBAAI,UAAU,MAAM;AAClB,sBAAM;AAAA,cACR;AAAA,YACF;AAAA,UACF,SAAS,GAAG;AACV,0BAAc,qDAAqD,CAAC;AAAA,UACtE;AAAA,QACF;AACA,cAAM,kBAAkB,MAAM;AAC5B,cAAI,SAAS,SAAS,GAAG;AACvB,gBAAI,kBAAkB;AAAA,cACpB,UAAU,OAAO;AAAA,cACjB,SAAS,MAAM;AAAA,cACf,IAAI;AAAA,YACN;AACA,qBAAS,QAAQ,aAAW;AAC1B,kBAAI,QAAQ,YAAY,gBAAgB,UAAU;AAChD,kCAAkB;AAAA,cACpB;AAAA,YACF,CAAC;AACD,mBAAO;AACP,uBAAW,SAAS,OAAO,aAAW,QAAQ,OAAO,gBAAgB,EAAE;AACvE,0BAAc,eAAe,EAAE,KAAK,MAAM,OAAO,KAAK;AAAA,UACxD;AAAA,QACF;AACA,wBAAgB;AAAA,MAClB;AAOA,UAAI,sBAAsB,GAAG;AAC3B,YAAI;AACJ,cAAM,mBAAmB,MAAM;AAC7B,sBAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,QAAQ;AAClE,oBAAU,IAAI,IAAI,aAAa;AAQ/B,kBAAQ,UAAU,MAAM;AACtB,+BAAmB;AACnB,6BAAiB;AAAA,UACnB;AAAA,QACF;AACA,yBAAiB;AAAA,MACnB,OAAO;AACL,YAAI,iBAAiB,cAAc,kBAAkB;AAAA,MACvD;AAAA,IACF;AACA,IAAM,+BAA+B;AACrC,IAAM,4BAA4B;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}