{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"GoodDriverConnection": {"ProviderName": "System.Data.SqlClient", "Default": true, "ConnectionString": "Server=localhost;Database=GoodDriver;Integrated Security=True;TrustServerCertificate=True;"}}, "AppSettings": {"ApplicationWebUrl": "http://localhost", "Environment": "Development"}, "Security": {"ExpiresInMinutes": 10080, "SlidingExpiration": true, "ConnectionStringName": "GoodDriverConnection", "Jwt": {"IsEnabled": true, "Issuer": "GoodDriver.API", "Audience": "GoodDriver.API", "SigningKey": "!@£§££G00dDr11v33rr@!$C333C1l14"}}, "AllowedHosts": "*"}