form {
  padding: 16px;
}

ion-list {
  margin-bottom: 16px;
}

.offline-message {
  margin-top: var(--app-spacing-md);
  text-align: center;

  ion-icon {
    margin-right: var(--app-spacing-xs);
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;

  ion-spinner {
    margin-bottom: var(--app-spacing-md);
  }

  p {
    color: var(--ion-color-medium);
    font-size: 0.9rem;
  }
}

ion-card {
  margin-bottom: var(--app-spacing-md);

  ion-card-title {
    display: flex;
    align-items: center;

    ion-icon {
      margin-right: var(--app-spacing-sm);
    }
  }

  ion-card-content {
    display: flex;
    align-items: center;

    ion-icon {
      margin-right: var(--app-spacing-sm);
      font-size: 1.2rem;
    }
  }
}

ion-item {
  margin-bottom: var(--app-spacing-sm);

  ion-label {
    font-weight: 500;
  }

  ion-note {
    font-size: 0.8rem;
  }
}

ion-button {
  margin-top: var(--app-spacing-md);

  ion-spinner {
    margin-right: var(--app-spacing-sm);
  }
}