﻿using Rogerio.Cqrs.Events;

namespace GoodDriver.Contracts.Users.Events
{
	public class UserSendEmailActiveRequestedEvent : Rogerio.Cqrs.Events.IDomainEvent
    {
        public UserSendEmailActiveRequestedEvent(string id, string name, string email, string token)
        {
            Id = id;
            Name = name;
            Email = email;
            Token = token;
        }

        public string Id { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string Token { get; set; }
    }
}
