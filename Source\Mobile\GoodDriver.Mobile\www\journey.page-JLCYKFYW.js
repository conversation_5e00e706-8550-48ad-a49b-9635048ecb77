import {
  Geolocation,
  JourneyInfoService,
  JourneyStorageService,
  Motion,
  init_esm as init_esm2,
  init_esm2 as init_esm3,
  init_journey_storage_service,
  init_journeyinfo_service
} from "./chunk-4BATGJUY.js";
import "./chunk-ZCUEDWU7.js";
import {
  BehaviorSubject,
  BooleanValueAccessorDirective,
  CommonModule,
  Component,
  DatePipe,
  DecimalPipe,
  FormsModule,
  Injectable,
  IonButton,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonContent,
  IonIcon,
  IonItem,
  IonLabel,
  IonList,
  IonListHeader,
  IonSegment,
  IonSegmentButton,
  IonSpinner,
  IonToggle,
  IonicModule,
  JsonPipe,
  NgControlStatus,
  NgForOf,
  NgIf,
  NgModel,
  Platform,
  ReactiveFormsModule,
  Router,
  RouterLink,
  RouterLinkDelegateDirective2 as RouterLinkDelegateDirective,
  RouterModule,
  SelectValueAccessorDirective,
  SessionService,
  ToastService,
  VehicleService,
  init_common,
  init_core,
  init_esm,
  init_forms,
  init_ionic_angular,
  init_router,
  init_session_service,
  init_toast_service,
  init_vehicle_service,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵinject,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind1,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵpureFunction1,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-PUABLQ3Y.js";
import "./chunk-GJIVKQBF.js";
import {
  init_dist,
  registerPlugin
} from "./chunk-WY354WYL.js";
import "./chunk-3TAP6XVY.js";
import "./chunk-BQEXAUCY.js";
import "./chunk-EUHNYIV3.js";
import "./chunk-AY6ZGCP5.js";
import "./chunk-I3UCYTIQ.js";
import "./chunk-4JI7VCBZ.js";
import "./chunk-YY7NCLAA.js";
import "./chunk-GUQ63TTB.js";
import "./chunk-CHFZ6R2P.js";
import "./chunk-XPKADKFH.js";
import "./chunk-V6QEVD67.js";
import "./chunk-WNPNB2PX.js";
import "./chunk-NRG74LSH.js";
import "./chunk-YSMEPS6E.js";
import "./chunk-NHEUXSZ2.js";
import "./chunk-WX4TOLMF.js";
import "./chunk-6SXGDLUH.js";
import "./chunk-NNIHVF7T.js";
import "./chunk-ZNALOMZJ.js";
import "./chunk-GHA2GP6C.js";
import "./chunk-H2CU6QHT.js";
import "./chunk-6PMAHZVS.js";
import "./chunk-DOEZTQY3.js";
import "./chunk-O4BCOVEV.js";
import "./chunk-TD4SIQNM.js";
import "./chunk-VU5YACRM.js";
import {
  __async,
  __esm
} from "./chunk-4W6HR7MY.js";

// node_modules/@capacitor/app/dist/esm/definitions.js
var init_definitions = __esm({
  "node_modules/@capacitor/app/dist/esm/definitions.js"() {
    "use strict";
  }
});

// node_modules/@capacitor/app/dist/esm/index.js
var App;
var init_esm4 = __esm({
  "node_modules/@capacitor/app/dist/esm/index.js"() {
    "use strict";
    init_dist();
    init_definitions();
    App = registerPlugin("App", {
      web: () => import("./web-JSNOAPSN.js").then((m) => new m.AppWeb())
    });
  }
});

// src/app/core/services/auto-journey-detection.service.ts
var BackgroundGeolocation, _AutoJourneyDetectionService, AutoJourneyDetectionService;
var init_auto_journey_detection_service = __esm({
  "src/app/core/services/auto-journey-detection.service.ts"() {
    "use strict";
    init_core();
    init_dist();
    init_esm3();
    init_esm();
    init_esm4();
    init_esm2();
    init_core();
    init_journey_storage_service();
    init_toast_service();
    init_ionic_angular();
    BackgroundGeolocation = registerPlugin("BackgroundGeolocation");
    _AutoJourneyDetectionService = class _AutoJourneyDetectionService {
      constructor(journeyService, toastService, platform) {
        this.journeyService = journeyService;
        this.toastService = toastService;
        this.platform = platform;
        this.isEnabled = false;
        this.isRunning = false;
        this.currentJourneyId = null;
        this.lastLocation = null;
        this.movingTimeCounter = 0;
        this.stationaryTimeCounter = 0;
        this.speedThreshold = 10;
        this.movingTimeThreshold = 60;
        this.stationaryTimeThreshold = 180;
        this.checkInterval = 10;
        this.locationWatcher = null;
        this.motionWatcher = null;
        this.backgroundWatcher = null;
        this.intervalId = null;
        this.detectionEnabledSubject = new BehaviorSubject(false);
        this.detectionEnabled$ = this.detectionEnabledSubject.asObservable();
        this.journeyActiveSubject = new BehaviorSubject(false);
        this.journeyActive$ = this.journeyActiveSubject.asObservable();
        this.platform.ready().then(() => {
          this.setupAppStateListeners();
        });
      }
      setupAppStateListeners() {
        App.addListener("appStateChange", ({ isActive }) => {
          if (!this.isEnabled)
            return;
          if (isActive) {
            console.log("App voltou para foreground");
            this.stopBackgroundMonitoring();
            if (this.isRunning) {
              this.startForegroundMonitoring();
            }
          } else {
            console.log("App foi para background");
            this.stopForegroundMonitoring();
            if (this.isRunning) {
              this.startBackgroundMonitoring();
            }
          }
        });
      }
      enableAutoDetection() {
        return __async(this, null, function* () {
          if (this.isEnabled)
            return;
          try {
            yield this.requestPermissions();
            this.isEnabled = true;
            this.detectionEnabledSubject.next(true);
            this.startDetection();
            yield this.toastService.showToast("Detec\xE7\xE3o autom\xE1tica de viagens ativada", "success");
          } catch (error) {
            console.error("Erro ao ativar detec\xE7\xE3o autom\xE1tica:", error);
            yield this.toastService.showToast("Erro ao ativar detec\xE7\xE3o autom\xE1tica. Verifique as permiss\xF5es.", "danger");
          }
        });
      }
      disableAutoDetection() {
        return __async(this, null, function* () {
          if (!this.isEnabled)
            return;
          if (this.currentJourneyId) {
            yield this.finishJourney();
          }
          this.stopDetection();
          this.isEnabled = false;
          this.detectionEnabledSubject.next(false);
          yield this.toastService.showToast("Detec\xE7\xE3o autom\xE1tica de viagens desativada", "success");
        });
      }
      startDetection() {
        if (this.isRunning)
          return;
        this.isRunning = true;
        this.movingTimeCounter = 0;
        this.stationaryTimeCounter = 0;
        this.startForegroundMonitoring();
      }
      stopDetection() {
        if (!this.isRunning)
          return;
        this.stopForegroundMonitoring();
        this.stopBackgroundMonitoring();
        this.isRunning = false;
        this.movingTimeCounter = 0;
        this.stationaryTimeCounter = 0;
      }
      startForegroundMonitoring() {
        this.intervalId = setInterval(() => this.checkMovementStatus(), this.checkInterval * 1e3);
        this.setupLocationWatcher();
        this.setupMotionWatcher();
      }
      stopForegroundMonitoring() {
        if (this.intervalId) {
          clearInterval(this.intervalId);
          this.intervalId = null;
        }
        if (this.locationWatcher) {
          this.locationWatcher.remove();
          this.locationWatcher = null;
        }
        if (this.motionWatcher) {
          this.motionWatcher.remove();
          this.motionWatcher = null;
        }
      }
      startBackgroundMonitoring() {
        return __async(this, null, function* () {
          try {
            const watcherId = yield BackgroundGeolocation.addWatcher({
              backgroundMessage: "Monitorando viagem em segundo plano",
              backgroundTitle: "Rastreamento de viagem",
              requestPermissions: true,
              stale: false,
              distanceFilter: 50
              // stopOnTerminate: false,
              // startOnBoot: true,
              // notificationChannelName: "Viagens",
              // notificationText: "Monitorando sua viagem",
              // notificationTitle: "GoodDriver",
              // notificationIconColor: "#0074d9",
            }, (location, error) => {
              if (error) {
                console.error("Erro no background geolocation:", error);
                return;
              }
              if (location) {
                this.lastLocation = location;
                this.processLocationUpdate(location);
              }
            });
            this.backgroundWatcher = watcherId;
          } catch (error) {
            console.error("Erro ao iniciar background geolocation:", error);
          }
        });
      }
      stopBackgroundMonitoring() {
        return __async(this, null, function* () {
          if (this.backgroundWatcher) {
            yield BackgroundGeolocation.removeWatcher({
              id: this.backgroundWatcher
            });
            this.backgroundWatcher = null;
          }
        });
      }
      setupLocationWatcher() {
      }
      setupMotionWatcher() {
        this.motionWatcher = Motion.addListener("accel", (event) => {
        });
      }
      checkMovementStatus() {
        return __async(this, null, function* () {
          if (!this.lastLocation)
            return;
          const speed = this.lastLocation.speed ? this.lastLocation.speed * 3.6 : 0;
          if (speed > this.speedThreshold) {
            this.movingTimeCounter += this.checkInterval;
            this.stationaryTimeCounter = 0;
            if (this.movingTimeCounter >= this.movingTimeThreshold && !this.currentJourneyId) {
              yield this.startJourney();
            }
          } else {
            this.stationaryTimeCounter += this.checkInterval;
            this.movingTimeCounter = 0;
            if (this.stationaryTimeCounter >= this.stationaryTimeThreshold && this.currentJourneyId) {
              yield this.finishJourney();
            }
          }
        });
      }
      processLocationUpdate(location) {
        this.lastLocation = location;
        if (this.currentJourneyId) {
          this.journeyService.addLocation(this.currentJourneyId, location.latitude, location.longitude);
        }
      }
      startJourney() {
        return __async(this, null, function* () {
          try {
            const journeyId = yield this.journeyService.startJourney();
            this.currentJourneyId = journeyId;
            this.journeyActiveSubject.next(true);
            yield this.toastService.showToast("Viagem iniciada automaticamente", "success");
          } catch (error) {
            console.error("Erro ao iniciar viagem automaticamente:", error);
          }
        });
      }
      finishJourney() {
        return __async(this, null, function* () {
          if (!this.currentJourneyId)
            return;
          try {
            yield this.journeyService.endJourney(this.currentJourneyId);
            yield this.toastService.showToast("Viagem finalizada automaticamente", "success");
            this.currentJourneyId = null;
            this.journeyActiveSubject.next(false);
          } catch (error) {
            console.error("Erro ao finalizar viagem automaticamente:", error);
          }
        });
      }
      requestPermissions() {
        return __async(this, null, function* () {
          try {
            const result = yield Geolocation.requestPermissions();
            console.log("Permiss\xF5es de localiza\xE7\xE3o:", result);
          } catch (error) {
            console.error("Erro ao solicitar permiss\xF5es:", error);
            throw error;
          }
        });
      }
    };
    _AutoJourneyDetectionService.\u0275fac = function AutoJourneyDetectionService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AutoJourneyDetectionService)(\u0275\u0275inject(JourneyStorageService), \u0275\u0275inject(ToastService), \u0275\u0275inject(Platform));
    };
    _AutoJourneyDetectionService.\u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _AutoJourneyDetectionService, factory: _AutoJourneyDetectionService.\u0275fac, providedIn: "root" });
    AutoJourneyDetectionService = _AutoJourneyDetectionService;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AutoJourneyDetectionService, [{
        type: Injectable,
        args: [{
          providedIn: "root"
        }]
      }], () => [{ type: JourneyStorageService }, { type: ToastService }, { type: Platform }], null);
    })();
  }
});

// src/app/pages/tabs/journeys/journey/journey.page.ts
function JourneyPage_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 3);
    \u0275\u0275element(1, "ion-spinner");
    \u0275\u0275elementEnd();
  }
}
function JourneyPage_div_2_ion_card_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-card", 12)(1, "ion-card-header")(2, "ion-card-title");
    \u0275\u0275element(3, "ion-icon", 13);
    \u0275\u0275text(4, " Aten\xE7\xE3o ");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(5, "ion-card-content")(6, "p");
    \u0275\u0275text(7, "Voc\xEA ainda n\xE3o possui nenhum ve\xEDculo cadastrado. Para utilizar o rastreamento de viagens, \xE9 necess\xE1rio cadastrar pelo menos um ve\xEDculo.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "ion-button", 14);
    \u0275\u0275element(9, "ion-icon", 15);
    \u0275\u0275text(10, " Cadastrar Ve\xEDculo ");
    \u0275\u0275elementEnd()()();
  }
}
function JourneyPage_div_2_div_18_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 16)(1, "p", 17);
    \u0275\u0275text(2, "Inicie e finalize suas viagens manualmente.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "ion-button", 18);
    \u0275\u0275listener("click", function JourneyPage_div_2_div_18_Template_ion_button_click_3_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.startTracking());
    });
    \u0275\u0275element(4, "ion-icon", 19);
    \u0275\u0275text(5, " Iniciar Viagem ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "ion-button", 20);
    \u0275\u0275listener("click", function JourneyPage_div_2_div_18_Template_ion_button_click_6_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.stopTracking());
    });
    \u0275\u0275element(7, "ion-icon", 21);
    \u0275\u0275text(8, " Finalizar Viagem ");
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275property("disabled", ctx_r1.tracking || ctx_r1.autoDetectionActive);
    \u0275\u0275advance(3);
    \u0275\u0275property("disabled", !ctx_r1.tracking);
  }
}
function JourneyPage_div_2_div_19_ion_item_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-item", 22);
    \u0275\u0275element(1, "ion-icon", 23);
    \u0275\u0275elementStart(2, "ion-label")(3, "h2");
    \u0275\u0275text(4, "Status");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275property("name", ctx_r1.journeyInProgress ? "car" : "car-outline")("color", ctx_r1.journeyInProgress ? "primary" : "medium");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r1.journeyInProgress ? "Viagem em andamento" : "Aguardando movimento");
  }
}
function JourneyPage_div_2_div_19_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 16)(1, "p", 17);
    \u0275\u0275text(2, "O aplicativo detectar\xE1 automaticamente quando voc\xEA iniciar e finalizar uma viagem.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "ion-item", 22);
    \u0275\u0275element(4, "ion-icon", 23);
    \u0275\u0275elementStart(5, "ion-label")(6, "h2");
    \u0275\u0275text(7, "Detec\xE7\xE3o Autom\xE1tica");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "p");
    \u0275\u0275text(9);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(10, "ion-toggle", 24);
    \u0275\u0275twoWayListener("ngModelChange", function JourneyPage_div_2_div_19_Template_ion_toggle_ngModelChange_10_listener($event) {
      \u0275\u0275restoreView(_r4);
      const ctx_r1 = \u0275\u0275nextContext(2);
      \u0275\u0275twoWayBindingSet(ctx_r1.autoDetectionActive, $event) || (ctx_r1.autoDetectionActive = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275listener("ionChange", function JourneyPage_div_2_div_19_Template_ion_toggle_ionChange_10_listener() {
      \u0275\u0275restoreView(_r4);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.toggleAutoDetection());
    });
    \u0275\u0275elementEnd()();
    \u0275\u0275template(11, JourneyPage_div_2_div_19_ion_item_11_Template, 7, 3, "ion-item", 25);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(4);
    \u0275\u0275property("name", ctx_r1.autoDetectionActive ? "checkmark-circle" : "close-circle")("color", ctx_r1.autoDetectionActive ? "success" : "medium");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r1.autoDetectionActive ? "Ativada" : "Desativada");
    \u0275\u0275advance();
    \u0275\u0275twoWayProperty("ngModel", ctx_r1.autoDetectionActive);
    \u0275\u0275property("disabled", ctx_r1.tracking);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.autoDetectionActive);
  }
}
function JourneyPage_div_2_div_23_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 4)(1, "p");
    \u0275\u0275text(2, "Nenhuma viagem registrada.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "p");
    \u0275\u0275text(4, "Inicie uma viagem para come\xE7ar a registrar.");
    \u0275\u0275elementEnd()();
  }
}
function JourneyPage_div_2_div_24_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 4)(1, "p");
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "p");
    \u0275\u0275text(4, "Toque em uma viagem para ver mais detalhes.");
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("Voc\xEA j\xE1 registrou ", ctx_r1.journeys.length, " viagens.");
  }
}
function JourneyPage_div_2_ion_item_25_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-item", 26);
    \u0275\u0275pipe(1, "json");
    \u0275\u0275elementStart(2, "ion-label")(3, "h2");
    \u0275\u0275text(4);
    \u0275\u0275pipe(5, "date");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "p");
    \u0275\u0275text(7);
    \u0275\u0275pipe(8, "date");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "p");
    \u0275\u0275text(10);
    \u0275\u0275pipe(11, "number");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const journey_r5 = ctx.$implicit;
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(16, _c0, journey_r5.id))("queryParams", \u0275\u0275pureFunction1(18, _c1, \u0275\u0275pipeBind1(1, 5, journey_r5)));
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1("In\xEDcio: ", \u0275\u0275pipeBind2(5, 7, journey_r5.startDate, "short"), "");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1("Fim: ", journey_r5.endDate ? \u0275\u0275pipeBind2(8, 10, journey_r5.endDate, "short") : "Em andamento", "");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1("Dist\xE2ncia: ", \u0275\u0275pipeBind2(11, 13, journey_r5.distance, "1.2-2"), " km");
  }
}
function JourneyPage_div_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div")(1, "ion-card")(2, "ion-card-header")(3, "ion-card-title", 4);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()()();
    \u0275\u0275template(5, JourneyPage_div_2_ion_card_5_Template, 11, 0, "ion-card", 5);
    \u0275\u0275elementStart(6, "ion-card")(7, "ion-card-header")(8, "ion-card-title");
    \u0275\u0275text(9, "Rastreamento de Viagens");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(10, "ion-card-content")(11, "ion-segment", 6);
    \u0275\u0275twoWayListener("ngModelChange", function JourneyPage_div_2_Template_ion_segment_ngModelChange_11_listener($event) {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r1.trackingMode, $event) || (ctx_r1.trackingMode = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275listener("ionChange", function JourneyPage_div_2_Template_ion_segment_ionChange_11_listener($event) {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onTrackingModeChange($event));
    });
    \u0275\u0275elementStart(12, "ion-segment-button", 7)(13, "ion-label");
    \u0275\u0275text(14, "Manual");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(15, "ion-segment-button", 8)(16, "ion-label");
    \u0275\u0275text(17, "Autom\xE1tico");
    \u0275\u0275elementEnd()()();
    \u0275\u0275template(18, JourneyPage_div_2_div_18_Template, 9, 2, "div", 9)(19, JourneyPage_div_2_div_19_Template, 12, 6, "div", 9);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(20, "ion-list")(21, "ion-list-header");
    \u0275\u0275text(22, " Viagens Registradas ");
    \u0275\u0275elementEnd();
    \u0275\u0275template(23, JourneyPage_div_2_div_23_Template, 5, 0, "div", 10)(24, JourneyPage_div_2_div_24_Template, 5, 1, "div", 10)(25, JourneyPage_div_2_ion_item_25_Template, 12, 20, "ion-item", 11);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1("Bem-vindo, ", ctx_r1.username, "!");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r1.hasVehicles);
    \u0275\u0275advance(6);
    \u0275\u0275twoWayProperty("ngModel", ctx_r1.trackingMode);
    \u0275\u0275advance(7);
    \u0275\u0275property("ngIf", ctx_r1.trackingMode === "manual");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.trackingMode === "auto");
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", (ctx_r1.journeys == null ? null : ctx_r1.journeys.length) === 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", (ctx_r1.journeys == null ? null : ctx_r1.journeys.length) && ctx_r1.journeys.length > 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r1.journeys);
  }
}
var _c0, _c1, _JourneyPage, JourneyPage;
var init_journey_page = __esm({
  "src/app/pages/tabs/journeys/journey/journey.page.ts"() {
    init_core();
    init_router();
    init_ionic_angular();
    init_common();
    init_forms();
    init_core();
    init_router();
    init_toast_service();
    init_journey_storage_service();
    init_session_service();
    init_auto_journey_detection_service();
    init_journeyinfo_service();
    init_vehicle_service();
    init_ionic_angular();
    init_common();
    init_forms();
    _c0 = (a0) => ["/journey-details", a0];
    _c1 = (a0) => ({ data: a0 });
    _JourneyPage = class _JourneyPage {
      constructor(router, toastService, journeyService, sessionService, autoDetectionService, journeyInfoService, vehicleService) {
        this.router = router;
        this.toastService = toastService;
        this.journeyService = journeyService;
        this.sessionService = sessionService;
        this.autoDetectionService = autoDetectionService;
        this.journeyInfoService = journeyInfoService;
        this.vehicleService = vehicleService;
        this.username = "Usu\xE1rio";
        this.userId = "";
        this.journeys = [];
        this.tracking = false;
        this.currentJourneyId = null;
        this.trackingMode = "manual";
        this.autoDetectionActive = false;
        this.journeyInProgress = false;
        this.hasVehicles = false;
        this.primaryVehicle = null;
        this.isLoading = true;
        this.subscriptions = [];
      }
      ngOnInit() {
        return __async(this, null, function* () {
          this.isLoading = true;
          this.username = (yield this.sessionService.getUserName()) || "Usu\xE1rio";
          this.userId = (yield this.sessionService.getUserId()) || "";
          yield this.checkVehicles();
          yield this.loadJourneys();
          this.subscriptions.push(this.journeyInfoService.trackingActive$.subscribe((active) => {
            this.tracking = active;
          }));
          this.subscriptions.push(this.autoDetectionService.detectionEnabled$.subscribe((enabled) => {
            this.autoDetectionActive = enabled;
          }));
          this.subscriptions.push(this.autoDetectionService.journeyActive$.subscribe((active) => {
            this.journeyInProgress = active;
          }));
          this.isLoading = false;
        });
      }
      /**
       * Checks if the user has any vehicles registered
       */
      checkVehicles() {
        return __async(this, null, function* () {
          try {
            this.hasVehicles = yield this.vehicleService.hasVehicles(this.userId);
            if (this.hasVehicles) {
              this.primaryVehicle = yield this.vehicleService.getPrimaryVehicle(this.userId);
            }
          } catch (error) {
            console.error("Error checking vehicles:", error);
            this.hasVehicles = false;
          }
        });
      }
      ngOnDestroy() {
        this.subscriptions.forEach((sub) => sub.unsubscribe());
      }
      /**
       * Manipula a mudança no modo de rastreamento (manual/automático)
       */
      onTrackingModeChange(event) {
        this.trackingMode = event.detail.value;
        if (this.trackingMode === "auto" && this.autoDetectionActive) {
          this.toastService.showToast("Detec\xE7\xE3o autom\xE1tica j\xE1 est\xE1 ativa", "info");
        }
        if (this.trackingMode === "manual" && this.tracking) {
          this.toastService.showToast("Viagem manual em andamento", "info");
        }
      }
      /**
       * Ativa/desativa a detecção automática de viagens
       */
      toggleAutoDetection() {
        return __async(this, null, function* () {
          try {
            if (this.autoDetectionActive) {
              yield this.autoDetectionService.enableAutoDetection();
            } else {
              yield this.autoDetectionService.disableAutoDetection();
            }
          } catch (error) {
            console.error("Erro ao alternar detec\xE7\xE3o autom\xE1tica:", error);
            this.autoDetectionActive = !this.autoDetectionActive;
          }
        });
      }
      /**
       * Inicia uma viagem manualmente
       */
      startTracking() {
        return __async(this, null, function* () {
          if (!this.hasVehicles) {
            yield this.toastService.showToast("Voc\xEA precisa cadastrar um ve\xEDculo antes de iniciar uma viagem.", "warning");
            this.router.navigate(["/new-vehicle"]);
            return;
          }
          try {
            const id = yield this.journeyService.startJourney();
            if (!id) {
              yield this.toastService.showToast("N\xE3o foi poss\xEDvel iniciar a viagem. Verifique se voc\xEA tem um ve\xEDculo cadastrado.", "warning");
              return;
            }
            this.currentJourneyId = id;
            yield this.toastService.showToast("Viagem iniciada.", "success");
          } catch (error) {
            console.error(error);
            yield this.toastService.showToast("Erro ao iniciar viagem.", "danger");
          }
        });
      }
      /**
       * Finaliza uma viagem manualmente
       */
      stopTracking() {
        return __async(this, null, function* () {
          if (!this.currentJourneyId) {
            yield this.toastService.showToast("Nenhuma viagem em andamento.");
            return;
          }
          try {
            yield this.journeyService.endJourney(this.currentJourneyId);
            this.currentJourneyId = null;
            yield this.loadJourneys();
            yield this.toastService.showToast("Viagem finalizada.");
          } catch (error) {
            console.error(error);
            yield this.toastService.showToast("Erro ao finalizar viagem.");
          }
        });
      }
      /**
       * Carrega as viagens do usuário
       */
      loadJourneys() {
        return __async(this, null, function* () {
          this.journeys = yield this.journeyService.getAllJourneysByUser(this.userId);
        });
      }
      /**
       * Realiza logout
       */
      logout() {
        if (this.autoDetectionActive) {
          this.autoDetectionService.disableAutoDetection();
        }
        this.sessionService.clearSession();
        console.log("Logout realizado");
        this.router.navigate(["/login"]);
      }
    };
    _JourneyPage.\u0275fac = function JourneyPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _JourneyPage)(\u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(ToastService), \u0275\u0275directiveInject(JourneyStorageService), \u0275\u0275directiveInject(SessionService), \u0275\u0275directiveInject(AutoJourneyDetectionService), \u0275\u0275directiveInject(JourneyInfoService), \u0275\u0275directiveInject(VehicleService));
    };
    _JourneyPage.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _JourneyPage, selectors: [["app-journey"]], decls: 3, vars: 3, consts: [[1, "ion-padding", 3, "fullscreen"], ["class", "ion-text-center ion-padding", 4, "ngIf"], [4, "ngIf"], [1, "ion-text-center", "ion-padding"], [1, "ion-text-center"], ["color", "warning", 4, "ngIf"], [3, "ngModelChange", "ionChange", "ngModel"], ["value", "manual"], ["value", "auto"], ["class", "tracking-controls", 4, "ngIf"], ["class", "ion-text-center", 4, "ngIf"], [3, "routerLink", "queryParams", 4, "ngFor", "ngForOf"], ["color", "warning"], ["name", "warning-outline"], ["expand", "block", "color", "primary", "routerLink", "/new-vehicle", 1, "ion-margin-top"], ["name", "car-outline", "slot", "start"], [1, "tracking-controls"], [1, "description"], ["expand", "full", "color", "success", 3, "click", "disabled"], ["name", "play", "slot", "start"], ["expand", "full", "color", "danger", 3, "click", "disabled"], ["name", "stop", "slot", "start"], ["lines", "none", 1, "status-item"], ["slot", "start", 3, "name", "color"], [3, "ngModelChange", "ionChange", "ngModel", "disabled"], ["lines", "none", "class", "status-item", 4, "ngIf"], [3, "routerLink", "queryParams"]], template: function JourneyPage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-content", 0);
        \u0275\u0275template(1, JourneyPage_div_1_Template, 2, 0, "div", 1)(2, JourneyPage_div_2_Template, 26, 8, "div", 2);
        \u0275\u0275elementEnd();
      }
      if (rf & 2) {
        \u0275\u0275property("fullscreen", true);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", ctx.isLoading);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", !ctx.isLoading);
      }
    }, dependencies: [
      IonicModule,
      IonButton,
      IonCard,
      IonCardContent,
      IonCardHeader,
      IonCardTitle,
      IonContent,
      IonIcon,
      IonItem,
      IonLabel,
      IonList,
      IonListHeader,
      IonSegment,
      IonSegmentButton,
      IonSpinner,
      IonToggle,
      BooleanValueAccessorDirective,
      SelectValueAccessorDirective,
      RouterLinkDelegateDirective,
      CommonModule,
      NgForOf,
      NgIf,
      JsonPipe,
      DecimalPipe,
      DatePipe,
      FormsModule,
      NgControlStatus,
      NgModel,
      ReactiveFormsModule,
      RouterModule,
      RouterLink
    ], styles: ["\n\nion-card[_ngcontent-%COMP%] {\n  margin-bottom: var(--app-spacing-md);\n}\n.tracking-controls[_ngcontent-%COMP%] {\n  margin-top: var(--app-spacing-md);\n}\n.description[_ngcontent-%COMP%] {\n  margin-bottom: var(--app-spacing-md);\n  color: var(--ion-color-medium);\n  font-size: 0.9rem;\n}\nion-segment[_ngcontent-%COMP%] {\n  margin-bottom: var(--app-spacing-md);\n}\n.status-item[_ngcontent-%COMP%] {\n  --background: var(--app-primary-light);\n  border-radius: var(--app-border-radius-md);\n  margin-bottom: var(--app-spacing-sm);\n}\nion-button[_ngcontent-%COMP%] {\n  margin-bottom: var(--app-spacing-sm);\n}\nion-toggle[_ngcontent-%COMP%] {\n  --background: var(--ion-color-medium);\n  --background-checked: var(--ion-color-success);\n  --handle-background: var(--ion-color-light);\n  --handle-background-checked: var(--ion-color-light);\n}\n/*# sourceMappingURL=journey.page.css.map */"] });
    JourneyPage = _JourneyPage;
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(JourneyPage, [{
        type: Component,
        args: [{ selector: "app-journey", standalone: true, imports: [
          IonicModule,
          CommonModule,
          FormsModule,
          ReactiveFormsModule,
          RouterModule
        ], template: `<ion-content [fullscreen]="true" class="ion-padding">
  <!-- Indicador de carregamento -->
  <div *ngIf="isLoading" class="ion-text-center ion-padding">
    <ion-spinner></ion-spinner>
  </div>

  <div *ngIf="!isLoading">
    <ion-card>
      <ion-card-header>
        <ion-card-title class="ion-text-center">Bem-vindo, {{username}}!</ion-card-title>
      </ion-card-header>
    </ion-card>

    <!-- Alerta de ve\xEDculo n\xE3o cadastrado -->
    <ion-card *ngIf="!hasVehicles" color="warning">
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="warning-outline"></ion-icon>
          Aten\xE7\xE3o
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <p>Voc\xEA ainda n\xE3o possui nenhum ve\xEDculo cadastrado. Para utilizar o rastreamento de viagens, \xE9 necess\xE1rio cadastrar pelo menos um ve\xEDculo.</p>
        <ion-button expand="block" color="primary" routerLink="/new-vehicle" class="ion-margin-top">
          <ion-icon name="car-outline" slot="start"></ion-icon>
          Cadastrar Ve\xEDculo
        </ion-button>
      </ion-card-content>
    </ion-card>

  <ion-card>
    <ion-card-header>
      <ion-card-title>Rastreamento de Viagens</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-segment [(ngModel)]="trackingMode" (ionChange)="onTrackingModeChange($event)">
        <ion-segment-button value="manual">
          <ion-label>Manual</ion-label>
        </ion-segment-button>
        <ion-segment-button value="auto">
          <ion-label>Autom\xE1tico</ion-label>
        </ion-segment-button>
      </ion-segment>

      <!-- Modo Manual -->
      <div *ngIf="trackingMode === 'manual'" class="tracking-controls">
        <p class="description">Inicie e finalize suas viagens manualmente.</p>

        <ion-button expand="full" color="success" (click)="startTracking()" [disabled]="tracking || autoDetectionActive">
          <ion-icon name="play" slot="start"></ion-icon>
          Iniciar Viagem
        </ion-button>

        <ion-button expand="full" color="danger" (click)="stopTracking()" [disabled]="!tracking">
          <ion-icon name="stop" slot="start"></ion-icon>
          Finalizar Viagem
        </ion-button>
      </div>

      <!-- Modo Autom\xE1tico -->
      <div *ngIf="trackingMode === 'auto'" class="tracking-controls">
        <p class="description">O aplicativo detectar\xE1 automaticamente quando voc\xEA iniciar e finalizar uma viagem.</p>

        <ion-item lines="none" class="status-item">
          <ion-icon [name]="autoDetectionActive ? 'checkmark-circle' : 'close-circle'"
                   [color]="autoDetectionActive ? 'success' : 'medium'"
                   slot="start"></ion-icon>
          <ion-label>
            <h2>Detec\xE7\xE3o Autom\xE1tica</h2>
            <p>{{ autoDetectionActive ? 'Ativada' : 'Desativada' }}</p>
          </ion-label>
          <ion-toggle [(ngModel)]="autoDetectionActive" (ionChange)="toggleAutoDetection()"
                     [disabled]="tracking"></ion-toggle>
        </ion-item>

        <ion-item lines="none" class="status-item" *ngIf="autoDetectionActive">
          <ion-icon [name]="journeyInProgress ? 'car' : 'car-outline'"
                   [color]="journeyInProgress ? 'primary' : 'medium'"
                   slot="start"></ion-icon>
          <ion-label>
            <h2>Status</h2>
            <p>{{ journeyInProgress ? 'Viagem em andamento' : 'Aguardando movimento' }}</p>
          </ion-label>
        </ion-item>
      </div>
    </ion-card-content>
  </ion-card>

  <ion-list>
    <ion-list-header>
      Viagens Registradas
    </ion-list-header>

    <div *ngIf="journeys?.length === 0" class="ion-text-center">
      <p>Nenhuma viagem registrada.</p>
      <p>Inicie uma viagem para come\xE7ar a registrar.</p>
    </div>

    <div *ngIf="journeys?.length && journeys.length > 0" class="ion-text-center">
      <p>Voc\xEA j\xE1 registrou {{ journeys.length }} viagens.</p>
      <p>Toque em uma viagem para ver mais detalhes.</p>
    </div>

    <ion-item *ngFor="let journey of journeys" [routerLink]="['/journey-details', journey.id]" [queryParams]="{ data: journey | json }">
      <ion-label>
        <h2>In\xEDcio: {{ journey.startDate | date:'short' }}</h2>
        <p>Fim: {{ journey.endDate ? (journey.endDate | date:'short') : 'Em andamento' }}</p>
        <p>Dist\xE2ncia: {{ journey.distance | number:'1.2-2' }} km</p>
      </ion-label>
    </ion-item>
  </ion-list>
  </div>
</ion-content>`, styles: ["/* src/app/pages/tabs/journeys/journey/journey.page.scss */\nion-card {\n  margin-bottom: var(--app-spacing-md);\n}\n.tracking-controls {\n  margin-top: var(--app-spacing-md);\n}\n.description {\n  margin-bottom: var(--app-spacing-md);\n  color: var(--ion-color-medium);\n  font-size: 0.9rem;\n}\nion-segment {\n  margin-bottom: var(--app-spacing-md);\n}\n.status-item {\n  --background: var(--app-primary-light);\n  border-radius: var(--app-border-radius-md);\n  margin-bottom: var(--app-spacing-sm);\n}\nion-button {\n  margin-bottom: var(--app-spacing-sm);\n}\nion-toggle {\n  --background: var(--ion-color-medium);\n  --background-checked: var(--ion-color-success);\n  --handle-background: var(--ion-color-light);\n  --handle-background-checked: var(--ion-color-light);\n}\n/*# sourceMappingURL=journey.page.css.map */\n"] }]
      }], () => [{ type: Router }, { type: ToastService }, { type: JourneyStorageService }, { type: SessionService }, { type: AutoJourneyDetectionService }, { type: JourneyInfoService }, { type: VehicleService }], null);
    })();
    (() => {
      (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(JourneyPage, { className: "JourneyPage", filePath: "src/app/pages/tabs/journeys/journey/journey.page.ts", lineNumber: 28 });
    })();
  }
});
init_journey_page();
export {
  JourneyPage
};
//# sourceMappingURL=journey.page-JLCYKFYW.js.map
