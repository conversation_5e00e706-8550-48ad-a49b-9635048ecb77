﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Folder Include="Journeys\0-Handler\" />
    <Folder Include="Journeys\1-Query\" />
    <Folder Include="Journeys\2-Result\" />
    <Folder Include="Users\0-Handler\" />
    <Folder Include="Users\1-Query\" />
    <Folder Include="Users\2-Result\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Rogerio.Cqrs">
      <HintPath>..\..\..\..\RogerioFramework\RogerioIT\Rogerio.Cqrs\bin\Debug\net8.0\Rogerio.Cqrs.dll</HintPath>
    </Reference>
    <Reference Include="Rogerio.Data">
      <HintPath>..\..\..\..\RogerioFramework\RogerioIT\Rogerio.Data\bin\Debug\net8.0\Rogerio.Data.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
