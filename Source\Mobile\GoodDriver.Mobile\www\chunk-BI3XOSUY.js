import {
  __esm
} from "./chunk-4W6HR7MY.js";

// node_modules/@ionic/core/components/dir.js
var isRTL;
var init_dir = __esm({
  "node_modules/@ionic/core/components/dir.js"() {
    "use strict";
    isRTL = (hostEl) => {
      if (hostEl) {
        if (hostEl.dir !== "") {
          return hostEl.dir.toLowerCase() === "rtl";
        }
      }
      return (document === null || document === void 0 ? void 0 : document.dir.toLowerCase()) === "rtl";
    };
  }
});

export {
  isRTL,
  init_dir
};
/*! Bundled license information:

@ionic/core/components/dir.js:
  (*!
   * (C) Ionic http://ionicframework.com - MIT License
   *)
*/
//# sourceMappingURL=chunk-BI3XOSUY.js.map
