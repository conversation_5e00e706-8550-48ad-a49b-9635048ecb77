{"version": 3, "sources": ["src/app/pages/tabs/vehicles/vehicle-details/vehicle-details.page.scss"], "sourcesContent": ["ion-card {\n  margin-bottom: 24px;\n\n  .vehicle-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 4px;\n\n    ion-badge {\n      font-size: 12px;\n      padding: 4px 8px;\n    }\n  }\n\n  ion-card-title {\n    font-size: 24px;\n    font-weight: 600;\n  }\n\n  ion-card-subtitle {\n    font-size: 16px;\n    margin-top: 4px;\n  }\n}\n\nion-item {\n  --padding-start: 0;\n\n  ion-icon {\n    font-size: 24px;\n    margin-right: 16px;\n  }\n\n  h3 {\n    font-weight: 500;\n    margin-bottom: 4px;\n  }\n\n  p {\n    color: var(--ion-color-medium);\n  }\n}\n\n.action-buttons {\n  margin-top: 24px;\n\n  ion-button {\n    margin-bottom: 12px;\n  }\n}\n"], "mappings": ";AAAA;AACE,iBAAA;;AAEA,SAAA,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;;AAEA,SAAA,CANF,eAME;AACE,aAAA;AACA,WAAA,IAAA;;AAIJ,SAAA;AACE,aAAA;AACA,eAAA;;AAGF,SAAA;AACE,aAAA;AACA,cAAA;;AAIJ;AACE,mBAAA;;AAEA,SAAA;AACE,aAAA;AACA,gBAAA;;AAGF,SAAA;AACE,eAAA;AACA,iBAAA;;AAGF,SAAA;AACE,SAAA,IAAA;;AAIJ,CAAA;AACE,cAAA;;AAEA,CAHF,eAGE;AACE,iBAAA;;", "names": []}