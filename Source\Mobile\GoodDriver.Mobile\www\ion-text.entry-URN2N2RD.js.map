{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-text.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as Host } from './index-527b9e34.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport './index-cfd9c1f2.js';\nconst textCss = \":host(.ion-color){color:var(--ion-color-base)}\";\nconst IonTextStyle0 = textCss;\nconst Text = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.color = undefined;\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '0c2546ea3f24b0a6bfd606199441d0a4edfa4ca1',\n      class: createColorClasses(this.color, {\n        [mode]: true\n      })\n    }, h(\"slot\", {\n      key: 'b7623ccb06f9461090a1f33e9f85886c7a4d5eff'\n    }));\n  }\n};\nText.style = IonTextStyle0;\nexport { Text as ion_text };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,IAOM,SACA,eACA;AATN;AAAA;AAGA;AACA;AACA;AACA;AACA,IAAM,UAAU;AAChB,IAAM,gBAAgB;AACtB,IAAM,OAAO,MAAM;AAAA,MACjB,YAAY,SAAS;AACnB,yBAAiB,MAAM,OAAO;AAC9B,aAAK,QAAQ;AAAA,MACf;AAAA,MACA,SAAS;AACP,cAAM,OAAO,WAAW,IAAI;AAC5B,eAAO,EAAE,MAAM;AAAA,UACb,KAAK;AAAA,UACL,OAAO,mBAAmB,KAAK,OAAO;AAAA,YACpC,CAAC,IAAI,GAAG;AAAA,UACV,CAAC;AAAA,QACH,GAAG,EAAE,QAAQ;AAAA,UACX,KAAK;AAAA,QACP,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AACA,SAAK,QAAQ;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}