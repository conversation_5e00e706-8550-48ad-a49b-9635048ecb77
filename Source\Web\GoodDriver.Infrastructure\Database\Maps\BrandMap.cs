﻿using FluentNHibernate.Mapping;
using GoodDriver.Domain.Brands;
using GoodDriver.Domain.Users;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Infrastructure.Database.Maps
{
	public class BrandMap : ClassMap<Brand>
	{
        public BrandMap()
        {
			Id(u => u.Id).GeneratedBy.Identity();
			Map(a => a.Name).Not.Nullable();
			Map(a => a.CreatedOn).Not.Nullable();
			Map(a => a.UpdatedOn).Nullable();
			Map(a => a.Ico).Nullable();
		}
    }
}
