{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/index-18f31305.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index-a5d50daf.js';\nimport { MENU_BACK_BUTTON_PRIORITY } from './hardware-back-button-a7eb8233.js';\nimport { p as printIonWarning } from './index-cfd9c1f2.js';\nimport { c as componentOnReady } from './helpers-d94bc8ad.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport { c as createAnimation } from './animation-8b25e105.js';\n\n/**\n * baseAnimation\n * Base class which is extended by the various types. Each\n * type will provide their own animations for open and close\n * and registers itself with Menu.\n */\nconst baseAnimation = isIos => {\n  // https://material.io/guidelines/motion/movement.html#movement-movement-in-out-of-screen-bounds\n  // https://material.io/guidelines/motion/duration-easing.html#duration-easing-natural-easing-curves\n  /**\n   * \"Apply the sharp curve to items temporarily leaving the screen that may return\n   * from the same exit point. When they return, use the deceleration curve. On mobile,\n   * this transition typically occurs over 300ms\" -- MD Motion Guide\n   */\n  return createAnimation().duration(isIos ? 400 : 300);\n};\n\n/**\n * Menu Overlay Type\n * The menu slides over the content. The content\n * itself, which is under the menu, does not move.\n */\nconst menuOverlayAnimation = menu => {\n  let closedX;\n  let openedX;\n  const width = menu.width + 8;\n  const menuAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  if (menu.isEndSide) {\n    // right side\n    closedX = width + 'px';\n    openedX = '0px';\n  } else {\n    // left side\n    closedX = -width + 'px';\n    openedX = '0px';\n  }\n  menuAnimation.addElement(menu.menuInnerEl).fromTo('transform', `translateX(${closedX})`, `translateX(${openedX})`);\n  const mode = getIonMode(menu);\n  const isIos = mode === 'ios';\n  const opacity = isIos ? 0.2 : 0.25;\n  backdropAnimation.addElement(menu.backdropEl).fromTo('opacity', 0.01, opacity);\n  return baseAnimation(isIos).addAnimation([menuAnimation, backdropAnimation]);\n};\n\n/**\n * Menu Push Type\n * The content slides over to reveal the menu underneath.\n * The menu itself also slides over to reveal its bad self.\n */\nconst menuPushAnimation = menu => {\n  let contentOpenedX;\n  let menuClosedX;\n  const mode = getIonMode(menu);\n  const width = menu.width;\n  if (menu.isEndSide) {\n    contentOpenedX = -width + 'px';\n    menuClosedX = width + 'px';\n  } else {\n    contentOpenedX = width + 'px';\n    menuClosedX = -width + 'px';\n  }\n  const menuAnimation = createAnimation().addElement(menu.menuInnerEl).fromTo('transform', `translateX(${menuClosedX})`, 'translateX(0px)');\n  const contentAnimation = createAnimation().addElement(menu.contentEl).fromTo('transform', 'translateX(0px)', `translateX(${contentOpenedX})`);\n  const backdropAnimation = createAnimation().addElement(menu.backdropEl).fromTo('opacity', 0.01, 0.32);\n  return baseAnimation(mode === 'ios').addAnimation([menuAnimation, contentAnimation, backdropAnimation]);\n};\n\n/**\n * Menu Reveal Type\n * The content slides over to reveal the menu underneath.\n * The menu itself, which is under the content, does not move.\n */\nconst menuRevealAnimation = menu => {\n  const mode = getIonMode(menu);\n  const openedX = menu.width * (menu.isEndSide ? -1 : 1) + 'px';\n  const contentOpen = createAnimation().addElement(menu.contentEl) // REVIEW\n  .fromTo('transform', 'translateX(0px)', `translateX(${openedX})`);\n  return baseAnimation(mode === 'ios').addAnimation(contentOpen);\n};\nconst createMenuController = () => {\n  const menuAnimations = new Map();\n  const menus = [];\n  const open = async menu => {\n    const menuEl = await get(menu, true);\n    if (menuEl) {\n      return menuEl.open();\n    }\n    return false;\n  };\n  const close = async menu => {\n    const menuEl = await (menu !== undefined ? get(menu, true) : getOpen());\n    if (menuEl !== undefined) {\n      return menuEl.close();\n    }\n    return false;\n  };\n  const toggle = async menu => {\n    const menuEl = await get(menu, true);\n    if (menuEl) {\n      return menuEl.toggle();\n    }\n    return false;\n  };\n  const enable = async (shouldEnable, menu) => {\n    const menuEl = await get(menu);\n    if (menuEl) {\n      menuEl.disabled = !shouldEnable;\n    }\n    return menuEl;\n  };\n  const swipeGesture = async (shouldEnable, menu) => {\n    const menuEl = await get(menu);\n    if (menuEl) {\n      menuEl.swipeGesture = shouldEnable;\n    }\n    return menuEl;\n  };\n  const isOpen = async menu => {\n    if (menu != null) {\n      const menuEl = await get(menu);\n      // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n      return menuEl !== undefined && menuEl.isOpen();\n    } else {\n      const menuEl = await getOpen();\n      return menuEl !== undefined;\n    }\n  };\n  const isEnabled = async menu => {\n    const menuEl = await get(menu);\n    if (menuEl) {\n      return !menuEl.disabled;\n    }\n    return false;\n  };\n  /**\n   * Finds and returns the menu specified by \"menu\" if registered.\n   * @param menu - The side or ID of the desired menu\n   * @param logOnMultipleSideMenus - If true, this function will log a warning\n   * if \"menu\" is a side but multiple menus on the same side were found. Since this function\n   * is used in multiple places, we default this log to false so that the calling\n   * functions can choose whether or not it is appropriate to log this warning.\n   */\n  const get = async (menu, logOnMultipleSideMenus = false) => {\n    await waitUntilReady();\n    if (menu === 'start' || menu === 'end') {\n      // there could be more than one menu on the same side\n      // so first try to get the enabled one\n      const menuRefs = menus.filter(m => m.side === menu && !m.disabled);\n      if (menuRefs.length >= 1) {\n        if (menuRefs.length > 1 && logOnMultipleSideMenus) {\n          printIonWarning(`menuController queried for a menu on the \"${menu}\" side, but ${menuRefs.length} menus were found. The first menu reference will be used. If this is not the behavior you want then pass the ID of the menu instead of its side.`, menuRefs.map(m => m.el));\n        }\n        return menuRefs[0].el;\n      }\n      // didn't find a menu side that is enabled\n      // so try to get the first menu side found\n      const sideMenuRefs = menus.filter(m => m.side === menu);\n      if (sideMenuRefs.length >= 1) {\n        if (sideMenuRefs.length > 1 && logOnMultipleSideMenus) {\n          printIonWarning(`menuController queried for a menu on the \"${menu}\" side, but ${sideMenuRefs.length} menus were found. The first menu reference will be used. If this is not the behavior you want then pass the ID of the menu instead of its side.`, sideMenuRefs.map(m => m.el));\n        }\n        return sideMenuRefs[0].el;\n      }\n    } else if (menu != null) {\n      // the menuId was not left or right\n      // so try to get the menu by its \"id\"\n      return find(m => m.menuId === menu);\n    }\n    // return the first enabled menu\n    const menuEl = find(m => !m.disabled);\n    if (menuEl) {\n      return menuEl;\n    }\n    // get the first menu in the array, if one exists\n    return menus.length > 0 ? menus[0].el : undefined;\n  };\n  /**\n   * Get the instance of the opened menu. Returns `null` if a menu is not found.\n   */\n  const getOpen = async () => {\n    await waitUntilReady();\n    return _getOpenSync();\n  };\n  /**\n   * Get all menu instances.\n   */\n  const getMenus = async () => {\n    await waitUntilReady();\n    return getMenusSync();\n  };\n  /**\n   * Get whether or not a menu is animating. Returns `true` if any\n   * menu is currently animating.\n   */\n  const isAnimating = async () => {\n    await waitUntilReady();\n    return isAnimatingSync();\n  };\n  const registerAnimation = (name, animation) => {\n    menuAnimations.set(name, animation);\n  };\n  const _register = menu => {\n    if (menus.indexOf(menu) < 0) {\n      menus.push(menu);\n    }\n  };\n  const _unregister = menu => {\n    const index = menus.indexOf(menu);\n    if (index > -1) {\n      menus.splice(index, 1);\n    }\n  };\n  const _setOpen = async (menu, shouldOpen, animated, role) => {\n    if (isAnimatingSync()) {\n      return false;\n    }\n    if (shouldOpen) {\n      const openedMenu = await getOpen();\n      if (openedMenu && menu.el !== openedMenu) {\n        await openedMenu.setOpen(false, false);\n      }\n    }\n    return menu._setOpen(shouldOpen, animated, role);\n  };\n  const _createAnimation = (type, menuCmp) => {\n    const animationBuilder = menuAnimations.get(type); // TODO(FW-2832): type\n    if (!animationBuilder) {\n      throw new Error('animation not registered');\n    }\n    const animation = animationBuilder(menuCmp);\n    return animation;\n  };\n  const _getOpenSync = () => {\n    return find(m => m._isOpen);\n  };\n  const getMenusSync = () => {\n    return menus.map(menu => menu.el);\n  };\n  const isAnimatingSync = () => {\n    return menus.some(menu => menu.isAnimating);\n  };\n  const find = predicate => {\n    const instance = menus.find(predicate);\n    if (instance !== undefined) {\n      return instance.el;\n    }\n    return undefined;\n  };\n  const waitUntilReady = () => {\n    return Promise.all(Array.from(document.querySelectorAll('ion-menu')).map(menu => new Promise(resolve => componentOnReady(menu, resolve))));\n  };\n  registerAnimation('reveal', menuRevealAnimation);\n  registerAnimation('push', menuPushAnimation);\n  registerAnimation('overlay', menuOverlayAnimation);\n  doc === null || doc === void 0 ? void 0 : doc.addEventListener('ionBackButton', ev => {\n    const openMenu = _getOpenSync();\n    if (openMenu) {\n      ev.detail.register(MENU_BACK_BUTTON_PRIORITY, () => {\n        return openMenu.close();\n      });\n    }\n  });\n  return {\n    registerAnimation,\n    get,\n    getMenus,\n    getOpen,\n    isEnabled,\n    swipeGesture,\n    isAnimating,\n    isOpen,\n    enable,\n    toggle,\n    close,\n    open,\n    _getOpenSync,\n    _createAnimation,\n    _register,\n    _unregister,\n    _setOpen\n  };\n};\nconst menuController = /*@__PURE__*/createMenuController();\nexport { menuController as m };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAgBM,eAgBA,sBA4BA,mBAuBA,qBAOA,sBA2MA;AArSN;AAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AAQA,IAAM,gBAAgB,WAAS;AAQ7B,aAAO,gBAAgB,EAAE,SAAS,QAAQ,MAAM,GAAG;AAAA,IACrD;AAOA,IAAM,uBAAuB,UAAQ;AACnC,UAAI;AACJ,UAAI;AACJ,YAAM,QAAQ,KAAK,QAAQ;AAC3B,YAAM,gBAAgB,gBAAgB;AACtC,YAAM,oBAAoB,gBAAgB;AAC1C,UAAI,KAAK,WAAW;AAElB,kBAAU,QAAQ;AAClB,kBAAU;AAAA,MACZ,OAAO;AAEL,kBAAU,CAAC,QAAQ;AACnB,kBAAU;AAAA,MACZ;AACA,oBAAc,WAAW,KAAK,WAAW,EAAE,OAAO,aAAa,cAAc,OAAO,KAAK,cAAc,OAAO,GAAG;AACjH,YAAM,OAAO,WAAW,IAAI;AAC5B,YAAM,QAAQ,SAAS;AACvB,YAAM,UAAU,QAAQ,MAAM;AAC9B,wBAAkB,WAAW,KAAK,UAAU,EAAE,OAAO,WAAW,MAAM,OAAO;AAC7E,aAAO,cAAc,KAAK,EAAE,aAAa,CAAC,eAAe,iBAAiB,CAAC;AAAA,IAC7E;AAOA,IAAM,oBAAoB,UAAQ;AAChC,UAAI;AACJ,UAAI;AACJ,YAAM,OAAO,WAAW,IAAI;AAC5B,YAAM,QAAQ,KAAK;AACnB,UAAI,KAAK,WAAW;AAClB,yBAAiB,CAAC,QAAQ;AAC1B,sBAAc,QAAQ;AAAA,MACxB,OAAO;AACL,yBAAiB,QAAQ;AACzB,sBAAc,CAAC,QAAQ;AAAA,MACzB;AACA,YAAM,gBAAgB,gBAAgB,EAAE,WAAW,KAAK,WAAW,EAAE,OAAO,aAAa,cAAc,WAAW,KAAK,iBAAiB;AACxI,YAAM,mBAAmB,gBAAgB,EAAE,WAAW,KAAK,SAAS,EAAE,OAAO,aAAa,mBAAmB,cAAc,cAAc,GAAG;AAC5I,YAAM,oBAAoB,gBAAgB,EAAE,WAAW,KAAK,UAAU,EAAE,OAAO,WAAW,MAAM,IAAI;AACpG,aAAO,cAAc,SAAS,KAAK,EAAE,aAAa,CAAC,eAAe,kBAAkB,iBAAiB,CAAC;AAAA,IACxG;AAOA,IAAM,sBAAsB,UAAQ;AAClC,YAAM,OAAO,WAAW,IAAI;AAC5B,YAAM,UAAU,KAAK,SAAS,KAAK,YAAY,KAAK,KAAK;AACzD,YAAM,cAAc,gBAAgB,EAAE,WAAW,KAAK,SAAS,EAC9D,OAAO,aAAa,mBAAmB,cAAc,OAAO,GAAG;AAChE,aAAO,cAAc,SAAS,KAAK,EAAE,aAAa,WAAW;AAAA,IAC/D;AACA,IAAM,uBAAuB,MAAM;AACjC,YAAM,iBAAiB,oBAAI,IAAI;AAC/B,YAAM,QAAQ,CAAC;AACf,YAAM,OAAO,CAAM,SAAQ;AACzB,cAAM,SAAS,MAAM,IAAI,MAAM,IAAI;AACnC,YAAI,QAAQ;AACV,iBAAO,OAAO,KAAK;AAAA,QACrB;AACA,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,CAAM,SAAQ;AAC1B,cAAM,SAAS,MAAO,SAAS,SAAY,IAAI,MAAM,IAAI,IAAI,QAAQ;AACrE,YAAI,WAAW,QAAW;AACxB,iBAAO,OAAO,MAAM;AAAA,QACtB;AACA,eAAO;AAAA,MACT;AACA,YAAM,SAAS,CAAM,SAAQ;AAC3B,cAAM,SAAS,MAAM,IAAI,MAAM,IAAI;AACnC,YAAI,QAAQ;AACV,iBAAO,OAAO,OAAO;AAAA,QACvB;AACA,eAAO;AAAA,MACT;AACA,YAAM,SAAS,CAAO,cAAc,SAAS;AAC3C,cAAM,SAAS,MAAM,IAAI,IAAI;AAC7B,YAAI,QAAQ;AACV,iBAAO,WAAW,CAAC;AAAA,QACrB;AACA,eAAO;AAAA,MACT;AACA,YAAM,eAAe,CAAO,cAAc,SAAS;AACjD,cAAM,SAAS,MAAM,IAAI,IAAI;AAC7B,YAAI,QAAQ;AACV,iBAAO,eAAe;AAAA,QACxB;AACA,eAAO;AAAA,MACT;AACA,YAAM,SAAS,CAAM,SAAQ;AAC3B,YAAI,QAAQ,MAAM;AAChB,gBAAM,SAAS,MAAM,IAAI,IAAI;AAE7B,iBAAO,WAAW,UAAa,OAAO,OAAO;AAAA,QAC/C,OAAO;AACL,gBAAM,SAAS,MAAM,QAAQ;AAC7B,iBAAO,WAAW;AAAA,QACpB;AAAA,MACF;AACA,YAAM,YAAY,CAAM,SAAQ;AAC9B,cAAM,SAAS,MAAM,IAAI,IAAI;AAC7B,YAAI,QAAQ;AACV,iBAAO,CAAC,OAAO;AAAA,QACjB;AACA,eAAO;AAAA,MACT;AASA,YAAM,MAAM,CAAO,MAAM,yBAAyB,UAAU;AAC1D,cAAM,eAAe;AACrB,YAAI,SAAS,WAAW,SAAS,OAAO;AAGtC,gBAAM,WAAW,MAAM,OAAO,OAAK,EAAE,SAAS,QAAQ,CAAC,EAAE,QAAQ;AACjE,cAAI,SAAS,UAAU,GAAG;AACxB,gBAAI,SAAS,SAAS,KAAK,wBAAwB;AACjD,8BAAgB,6CAA6C,IAAI,eAAe,SAAS,MAAM,oJAAoJ,SAAS,IAAI,OAAK,EAAE,EAAE,CAAC;AAAA,YAC5Q;AACA,mBAAO,SAAS,CAAC,EAAE;AAAA,UACrB;AAGA,gBAAM,eAAe,MAAM,OAAO,OAAK,EAAE,SAAS,IAAI;AACtD,cAAI,aAAa,UAAU,GAAG;AAC5B,gBAAI,aAAa,SAAS,KAAK,wBAAwB;AACrD,8BAAgB,6CAA6C,IAAI,eAAe,aAAa,MAAM,oJAAoJ,aAAa,IAAI,OAAK,EAAE,EAAE,CAAC;AAAA,YACpR;AACA,mBAAO,aAAa,CAAC,EAAE;AAAA,UACzB;AAAA,QACF,WAAW,QAAQ,MAAM;AAGvB,iBAAO,KAAK,OAAK,EAAE,WAAW,IAAI;AAAA,QACpC;AAEA,cAAM,SAAS,KAAK,OAAK,CAAC,EAAE,QAAQ;AACpC,YAAI,QAAQ;AACV,iBAAO;AAAA,QACT;AAEA,eAAO,MAAM,SAAS,IAAI,MAAM,CAAC,EAAE,KAAK;AAAA,MAC1C;AAIA,YAAM,UAAU,MAAY;AAC1B,cAAM,eAAe;AACrB,eAAO,aAAa;AAAA,MACtB;AAIA,YAAM,WAAW,MAAY;AAC3B,cAAM,eAAe;AACrB,eAAO,aAAa;AAAA,MACtB;AAKA,YAAM,cAAc,MAAY;AAC9B,cAAM,eAAe;AACrB,eAAO,gBAAgB;AAAA,MACzB;AACA,YAAM,oBAAoB,CAAC,MAAM,cAAc;AAC7C,uBAAe,IAAI,MAAM,SAAS;AAAA,MACpC;AACA,YAAM,YAAY,UAAQ;AACxB,YAAI,MAAM,QAAQ,IAAI,IAAI,GAAG;AAC3B,gBAAM,KAAK,IAAI;AAAA,QACjB;AAAA,MACF;AACA,YAAM,cAAc,UAAQ;AAC1B,cAAM,QAAQ,MAAM,QAAQ,IAAI;AAChC,YAAI,QAAQ,IAAI;AACd,gBAAM,OAAO,OAAO,CAAC;AAAA,QACvB;AAAA,MACF;AACA,YAAM,WAAW,CAAO,MAAM,YAAY,UAAU,SAAS;AAC3D,YAAI,gBAAgB,GAAG;AACrB,iBAAO;AAAA,QACT;AACA,YAAI,YAAY;AACd,gBAAM,aAAa,MAAM,QAAQ;AACjC,cAAI,cAAc,KAAK,OAAO,YAAY;AACxC,kBAAM,WAAW,QAAQ,OAAO,KAAK;AAAA,UACvC;AAAA,QACF;AACA,eAAO,KAAK,SAAS,YAAY,UAAU,IAAI;AAAA,MACjD;AACA,YAAM,mBAAmB,CAAC,MAAM,YAAY;AAC1C,cAAM,mBAAmB,eAAe,IAAI,IAAI;AAChD,YAAI,CAAC,kBAAkB;AACrB,gBAAM,IAAI,MAAM,0BAA0B;AAAA,QAC5C;AACA,cAAM,YAAY,iBAAiB,OAAO;AAC1C,eAAO;AAAA,MACT;AACA,YAAM,eAAe,MAAM;AACzB,eAAO,KAAK,OAAK,EAAE,OAAO;AAAA,MAC5B;AACA,YAAM,eAAe,MAAM;AACzB,eAAO,MAAM,IAAI,UAAQ,KAAK,EAAE;AAAA,MAClC;AACA,YAAM,kBAAkB,MAAM;AAC5B,eAAO,MAAM,KAAK,UAAQ,KAAK,WAAW;AAAA,MAC5C;AACA,YAAM,OAAO,eAAa;AACxB,cAAM,WAAW,MAAM,KAAK,SAAS;AACrC,YAAI,aAAa,QAAW;AAC1B,iBAAO,SAAS;AAAA,QAClB;AACA,eAAO;AAAA,MACT;AACA,YAAM,iBAAiB,MAAM;AAC3B,eAAO,QAAQ,IAAI,MAAM,KAAK,SAAS,iBAAiB,UAAU,CAAC,EAAE,IAAI,UAAQ,IAAI,QAAQ,aAAW,iBAAiB,MAAM,OAAO,CAAC,CAAC,CAAC;AAAA,MAC3I;AACA,wBAAkB,UAAU,mBAAmB;AAC/C,wBAAkB,QAAQ,iBAAiB;AAC3C,wBAAkB,WAAW,oBAAoB;AACjD,cAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,iBAAiB,iBAAiB,QAAM;AACpF,cAAM,WAAW,aAAa;AAC9B,YAAI,UAAU;AACZ,aAAG,OAAO,SAAS,2BAA2B,MAAM;AAClD,mBAAO,SAAS,MAAM;AAAA,UACxB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,IAAM,iBAA8B,qCAAqB;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}