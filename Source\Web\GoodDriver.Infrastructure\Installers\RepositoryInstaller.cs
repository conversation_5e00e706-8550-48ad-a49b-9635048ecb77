﻿using GoodDriver.Domain.Users;
using GoodDriver.Infrastructure.Database.Repositories;
using Microsoft.Extensions.DependencyInjection;
using Rogerio.Cqrs.Domains;
using Rogerio.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Infrastructure.Installers
{
	public static class RepositoryInstaller
	{

		public static void Install(IServiceCollection services)
		{
			services.AddSingleton <IUserRepository, UserNHRepository>();
			//services.AddSingleton<IRepository<Domain.Brands.Brand>, Repository<Domain.Brands.Brand>>();
			//services.AddSingleton<IRepository<Domain.Journeys.Occurrence>, Repository<Domain.Journeys.Occurrence>>();
			//services.AddSingleton<IRepository<Domain.Users.User>, Repository<Domain.Users.User>>();
			//services.AddSingleton<IRepository<Domain.Users.UserInstitution>, Repository<Domain.Users.UserInstitution>>();
			//services.AddSingleton<IRepository<Domain.Users.UserRole>, Repository<Domain.Users.UserRole>>();
			//services.AddSingleton<IRepository<Domain.Users.UserPermission>, Repository<Domain.Users.UserPermission>>();
			//services.AddSingleton<IRepository<Domain.Users.UserGroup>, Repository<Domain.Users.UserGroup>>();
		}
	}
}
