﻿using Dapper;
using GoodDriver.ReadModels.Brands.Queries;
using GoodDriver.ReadModels.Brands.Result;
using Microsoft.Extensions.Configuration;
using Rogerio.Cqrs.Requests;
using Rogerio.Data;

namespace GoodDriver.ReadModels.Brands._0_Handler
{
    public class BrandQueryHandler : BaseDataA<PERSON>ess, IRequestHandler<BrandListQuery, BrandListResult>
    {
        public BrandQueryHandler(IConfiguration configuration) : base(configuration)
        {
        }

        public async Task<BrandListResult> HandleAsync(BrandListQuery query)
        {
            var sql = @"SELECT Id, Name, Ico from Brand";
            
            using(var connection = this.CreateConnection())
            {
                var result = (await connection.QueryAsync<BrandListResult.BrandListItemResult>(sql, query)).ToList();
                return new BrandListResult(result);
            }
        }
    }
}
