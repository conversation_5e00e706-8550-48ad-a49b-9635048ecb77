﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Migration.Schema
{
	[FluentMigrator.Migration(20250409210000)]
	public class CreateTableUser : FluentMigrator.Migration
	{
		public override void Down()
		{
			if (Schema.Table("User").Exists())
			{
				Delete.Table("User");
			}
		}

		public override void Up()
		{
			if (!Schema.Table("User").Exists())
			{
				Create.Table("User")
				.WithColumn("Id").AsGuid().PrimaryKey().NotNullable()
				.WithColumn("Name").AsString(50).NotNullable()
				.WithColumn("Phone").AsString(50).NotNullable()
				.WithColumn("Active").AsBoolean().NotNullable()
				.WithColumn("Email").AsString(100).NotNullable()
				.WithColumn("Password").AsString(128).Nullable()
				.WithColumn("Token").AsString(50).Nullable()
				.WithColumn("CreatedOn").AsDateTime().NotNullable()
				.WithColumn("UpdatedOn").AsDateTime().Nullable()
				.WithColumn("Status").AsString(50).NotNullable()
				.WithColumn("ImageUrl").AsString(200).Nullable()
				.WithColumn("LastAccess").AsDateTime().Nullable()
				.WithColumn("ZipCode").AsString(50).Nullable()
				.WithColumn("PasswordSalt").AsString(128).Nullable();
			}
		}
	}
}
