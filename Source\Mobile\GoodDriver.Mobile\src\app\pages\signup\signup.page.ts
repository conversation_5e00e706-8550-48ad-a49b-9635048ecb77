import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { CreateUserRequestDto } from 'src/app/core/dtos/user/create-user-requestDto';
import { UserService } from 'src/app/core/services/user.service';
import { ToastService } from 'src/app/core/services/toast.service';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';


@Component({
  selector: 'app-signup',
  templateUrl: './signup.page.html',
  styleUrls: ['./signup.page.scss'],
  standalone: true,
  imports: [
     CommonModule,
     FormsModule,
     ReactiveFormsModule,
     IonicModule,
  ],
})
export class SignupPage implements OnInit {
  signupForm: FormGroup;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private toastService: ToastService,
    private userService: UserService,
  ) {
    this.signupForm = this.formBuilder.group({
      name: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });
  }

  ngOnInit() {
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password')?.value;
    const confirmPassword = form.get('confirmPassword')?.value;

    if (password !== confirmPassword) {
      form.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }
    
    return null;
  }

  async signup() {
    if (this.signupForm.valid) {      
      const signupData = this.signupForm.value;

const request: CreateUserRequestDto = {
          name: signupData.name,
          phone: '',
          email: signupData.email,
          password: signupData.password,
          passwordConfirm: signupData.confirmPassword,
          informationResponsibilityChecked: true,
        };

        this.userService.create(request).subscribe({
          next: async (response) => {
            console.log('Cadastro realizado com sucesso!', response);
            this.toastService.showToast('Cadastro realizado com sucesso! Verifique sua caixa de email para concluir o cadastro', 'success');            
            this.router.navigate(['/login']);
          },
          error: (err) => {
            console.error('Erro no cadastro:', err);
            this.toastService.showToast('Falha no cadastro. Verifique se preencheu todos os dados corretamente.', 'danger');
          }
        });
      } else {
        console.log('Formulário inválido');
        this.toastService.showToast('Preencha todos os campos corretamente.', 'warning');
      }
  }

  goToLogin() {
    this.router.navigate(['/login']);
  }
}