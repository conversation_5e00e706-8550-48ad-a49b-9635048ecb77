﻿using GoodDriver.Common.TimeZones;
using GoodDriver.Common.Validation;
using Rogerio.Commom.Exceptions;
using System.Globalization;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;


namespace GoodDriver.Common
{
    public static class Util
    {
        public static bool IsNullOrEmpty(this string value)
        {
            return string.IsNullOrWhiteSpace(value);
        }

        public static bool IsEmpty<T>(this IList<T> list)
        {
            if (list == null || !list.Any())
                return true;

            return false;
        }

        public static string GetExceptionKey<T>(string methodName) where T : class
        {
            return $"{typeof(T).Name}.{methodName}";
        }

        public static T ParseEnum<T>(string value)
        {
            return (T)Enum.Parse(typeof(T), value, true);
        }

        //public static List<SelectListItem> GetEnumSelectList<T>(string selectedValue = null)
        //{

        //    var selectItems = (Enum.GetValues(typeof(T))
        //    .Cast<int>()
        //    .Select(e => new SelectListItem() { Text = Enum.GetName(typeof(T), e), Value = Enum.GetName(typeof(T), e), Selected = Enum.GetName(typeof(T), e) == selectedValue }))
        //    .ToList();

        //    return selectItems;
        //}

        public static IList<int> IntRangeToList(int startNumber, int endNumber)
        {
            var numbers = Enumerable.Range(startNumber, (endNumber - startNumber))
                           .Select(x => x)
                           .ToList();

            return numbers;
        }

        //public static IFileData ConvertToFileData(IFormFile fileData)
        //{
        //    if (fileData == null)
        //        return null;

        //    var stream = new FileStream(Path.GetTempFileName(), FileMode.Create);

        //    fileData.CopyTo(stream);

        //    return new FileData()
        //    {
        //        ContentType = fileData.ContentType,
        //        Name = fileData.FileName,
        //        Stream = stream
        //    };
        //}

        //public static IFileData PGLSToFileData(this IFormFile fileData)
        //{
        //    if (fileData == null)
        //        return null;

        //    var stream = new FileStream(Path.GetTempFileName(), FileMode.Create);

        //    fileData.CopyTo(stream);

        //    return new FileData()
        //    {
        //        ContentType = fileData.ContentType,
        //        Name = fileData.FileName,
        //        Stream = stream
        //    };
        //}


        public static DaysOfWeek DayOfWeekFromDate(DateTime date)
        {
            switch (date.DayOfWeek)
            {
                case DayOfWeek.Friday:
                    return DaysOfWeek.Friday;
                case DayOfWeek.Monday:
                    return DaysOfWeek.Monday;
                case DayOfWeek.Saturday:
                    return DaysOfWeek.Saturday;
                case DayOfWeek.Sunday:
                    return DaysOfWeek.Sunday;
                case DayOfWeek.Thursday:
                    return DaysOfWeek.Thursday;
                case DayOfWeek.Tuesday:
                    return DaysOfWeek.Tuesday;
                case DayOfWeek.Wednesday:
                    return DaysOfWeek.Wednesday;
            }

            return DaysOfWeek.Friday;
        }
        public static bool DateIsValid(DateTime? date)
        {
            if (!date.HasValue || date.Value.Year < 1753) return false;

            return true;
        }
        //public static IFileData FileFromBase64String(this string data, string contentType, FileStream fs, string fileName = null)
        //{
        //    if (string.IsNullOrEmpty(data)) throw new ArgumentNullException("data");
        //    byte[] bytes = Convert.FromBase64String(data);
        //    var file = new FileData();
        //    fs.Write(bytes, 0, bytes.Length);
        //    fs.Position = 0;
        //    file.Stream = fs;


        //    if (!string.IsNullOrEmpty(fileName))
        //    {
        //        file.Name = fileName;
        //        file.ContentType = contentType;
        //    }
        //    return file;
        //}

        public static string RemoveAccents(this string text)
        {
            StringBuilder sbReturn = new StringBuilder();
            var arrayText = text.Normalize(NormalizationForm.FormD).ToCharArray();
            foreach (char letter in arrayText)
            {
                if (CharUnicodeInfo.GetUnicodeCategory(letter) != UnicodeCategory.NonSpacingMark)
                    sbReturn.Append(letter);
            }
            return sbReturn.ToString();
        }

        public static bool IsLeapYear(int year)
        {
            var daysOfYear = new DateTime(year, 1, 1).DaysOfYear();
            return daysOfYear == 366;
        }

        public static DateTime LeapYearRoundDate(int year, int month, int day)
        {
            if (month == 2 && day == 29 && !IsLeapYear(year))
                return new DateTime(year, month, 28);

            return new DateTime(year, month, day);
        }

        public static bool DatetimeIsValid(int year, int month, int day)
        {
            DateTime newDate = new DateTime();
            return (DateTime.TryParse(string.Format("{0}-{1}-{2}", year, month, day), out newDate));
        }

        public static bool AnyAccents(this string text)
        {
            StringBuilder sbReturn = new StringBuilder();
            var arrayText = text.Normalize(NormalizationForm.FormD).ToCharArray();
            foreach (char letter in arrayText)
            {
                if (CharUnicodeInfo.GetUnicodeCategory(letter) == UnicodeCategory.NonSpacingMark)
                    return true;
            }

            return false;
        }

        public static string DisplayDayOfWeek(DaysOfWeek dayOfWeek)
        {
            switch (dayOfWeek)
            {
                case (DaysOfWeek.Monday):
                    return "Segunda-Feira";
                case (DaysOfWeek.Tuesday):
                    return "Terça-Feira";
                case (DaysOfWeek.Wednesday):
                    return "Quarta-Feira";
                case (DaysOfWeek.Thursday):
                    return "Quinta-Feira";
                case (DaysOfWeek.Friday):
                    return "Sexta-Feira";
                case (DaysOfWeek.Saturday):
                    return "Sábado";
                case (DaysOfWeek.Sunday):
                    return "Domingo";
            }

            return "seg";
        }

        public static string DisplayFull(DaysOfWeek? dayOfWeek)
        {
            if (dayOfWeek == null) return "";

            /** formatos: seg-sex
             *            seg, qua, sex
             *            seg-qua; qui-sex **/

            string text = string.Empty;
            int? start = null;
            int? finish = null;
            List<string> formateds = new List<string>();
            string notSync = string.Empty;

            for (int x = 1; x <= 128; x = x * 2)
            {
                if ((dayOfWeek & (DaysOfWeek)x) != 0)
                {
                    if (!start.HasValue)
                        start = x;
                    else
                        finish = x;
                }
                else if (start.HasValue && finish.HasValue)
                {
                    formateds.Add(Common.Util.DisplayDayOfWeek((DaysOfWeek)start) + "-" + Common.Util.DisplayDayOfWeek((DaysOfWeek)finish));
                    start = null; finish = null;
                }
                else if (start.HasValue)
                {
                    notSync += Common.Util.DisplayDayOfWeek((DaysOfWeek)start) + ", ";
                    start = null; finish = null;
                }
            }

            if (!string.IsNullOrEmpty(notSync))
            {
                notSync = notSync.Remove(notSync.Length - 2);
                formateds.Add(notSync);
            }

            if (formateds.Count > 0)
            {
                text = string.Join("; ", formateds.ToArray());
            }

            return text;
        }

        public static bool EmailIsValid(string email)
        {
            string validationModel = "^(([-.\\w]*)*@([0-9a-zA-Z][-\\w]*[0-9a-zA-Z]\\.)+[a-zA-Z]{2,9})$";
            if (!string.IsNullOrEmpty(email) && System.Text.RegularExpressions.Regex.IsMatch(email, validationModel))
            {
                return true;
            }
            else
            {
                return false;
            }

        }


        public static DateTime ToDateTime(this DateTime date)
        {
            return TimeZoneManager.ConvertToLocal(date).Value;
        }

        public static DateTime NowWithTimeMin()
        {
            DateTime now = DateTime.Now;
            return new DateTime(now.Year, now.Month, now.Day, 0, 0, 0, 0);
        }

        public static DateTime NowWithTimeMax()
        {
            DateTime nowMax = DateTime.MaxValue;
            DateTime now = DateTime.Now;
            return new DateTime(now.Year, now.Month, now.Day, nowMax.Hour, nowMax.Minute, nowMax.Second, nowMax.Millisecond);
        }

        public static DateTime StartOfWeek(this DateTime dt, DayOfWeek startOfWeek)
        {
            int diff = dt.DayOfWeek - startOfWeek;
            if (diff < 0)
            {
                diff += 7;
            }

            return dt.AddDays(-1 * diff).Date;
        }

        public static DateTime EndWeekDate()
        {
            DateTime nowMax = DateTime.MaxValue;
            DateTime now = DateTime.Now;
            return new DateTime(now.Year, now.Month, now.Day, nowMax.Hour, nowMax.Minute, nowMax.Second, nowMax.Millisecond);
        }

        public static DateTime OnlyDate(this DateTime now)
        {
            return new DateTime(now.Year, now.Month, now.Day, 0, 0, 0, 0);
        }

        public static string UnMaskPhone(string value)
        {
            try
            {
                value = value.Replace("(", "").Replace(")", "").Replace("-", "").Replace(" ", "");
            }
            catch (System.Exception)
            { }

            return value;
        }
        public static string FormatRG(this string RG)
        {
            if (RG == null)
                return null;

            return Convert.ToUInt64(RG).ToString(@"00\.000\.000\-00");
        }

        public static string FormatCPF(this string CPF)
        {
            if (CPF == null)
                return CPF;

            return Convert.ToUInt64(CPF).ToString(@"000\.000\.000\-00");
        }

        static string RemoveDiacritics(string text)
        {
            var normalizedString = text.Normalize(NormalizationForm.FormD);
            var stringBuilder = new StringBuilder(capacity: normalizedString.Length);

            for (int i = 0; i < normalizedString.Length; i++)
            {
                char c = normalizedString[i];
                var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
                if (unicodeCategory != UnicodeCategory.NonSpacingMark)
                {
                    stringBuilder.Append(c);
                }
            }

            return stringBuilder
                .ToString()
                .Normalize(NormalizationForm.FormC);
        }

        public static string NormalizeText(this string text)
        {
            return new string(RemoveDiacritics(text).ToUpperInvariant().Where(c => char.IsLetterOrDigit(c)).ToArray());
        }

        public static bool IsCpf(string cpf)
        {
            if (string.IsNullOrEmpty(cpf)) return false;

            Regex regularExpression = new Regex(@"[^0-9]");
            cpf = regularExpression.Replace(cpf, "");

            if (cpf.Length != 11) return false;

            bool igual = true;
            for (int i = 1; i < 11 && igual; i++)
                if (cpf[i] != cpf[0])
                    igual = false;

            if (igual || cpf == "12345678909")
                return false;

            int[] numeros = new int[11];
            for (int i = 0; i < 11; i++)
                numeros[i] = int.Parse(cpf[i].ToString());

            int soma = 0;
            for (int i = 0; i < 9; i++)
                soma += (10 - i) * numeros[i];

            int resultado = soma % 11;
            if (resultado == 1 || resultado == 0)

            {
                if (numeros[9] != 0)
                    return false;
            }
            else if (numeros[9] != 11 - resultado)
                return false;

            soma = 0;
            for (int i = 0; i < 10; i++)
                soma += (11 - i) * numeros[i];

            resultado = soma % 11;
            if (resultado == 1 || resultado == 0)
            {
                if (numeros[10] != 0)
                    return false;
            }
            else
            {
                if (numeros[10] != 11 - resultado)
                    return false;
            }

            return true;

        }

        public static string FormatCNPJOrCPF(this string document)
        {
            if (IsCpf(document))
                return FormatCPF(document);

            if (IsCNPJ(document))
                return FormatCNPJ(document);

            return document;
        }

        public static string FormatCNPJ(this string cnpj)
        {
            if (cnpj == null)
                return cnpj;

            if (!string.IsNullOrWhiteSpace(cnpj))
                return Convert.ToUInt64(cnpj).ToString(@"00\.000\.000\/0000\-00");

            return string.Empty;
        }

        public static string UnMaskNumber(this string documentNumber)
        {
            if (string.IsNullOrEmpty(documentNumber))
                return documentNumber;

            Regex regularExpression = new Regex(@"[^0-9]");
            var _documentNumber = regularExpression.Replace(documentNumber, "");
            return _documentNumber;
        }

        public static string UnMaskCEP(this string cep)
        {
            if (string.IsNullOrEmpty(cep))
                return cep;

            return Regex.Replace(cep, "[^0-9]", "");
        }

        public static string UnMaskDocument(this string document)
        {
            Regex regularExpression = new Regex(@"[^0-9]");

            return regularExpression.Replace((document ?? String.Empty), "");
        }

        public static bool IsCNPJ(string vrCNPJ)
        {
            if (string.IsNullOrEmpty(vrCNPJ))
                return false;

            string CNPJ = UnMaskDocument(vrCNPJ);

            int[] digitos, soma, resultado;
            int nrDig;
            string ftmt;
            bool[] CNPJOk;

            ftmt = "6543298765432";
            digitos = new int[14];
            soma = new int[2];
            soma[0] = 0;
            soma[1] = 0;
            resultado = new int[2];
            resultado[0] = 0;
            resultado[1] = 0;
            CNPJOk = new bool[2];
            CNPJOk[0] = false;
            CNPJOk[1] = false;


            try
            {

                for (nrDig = 0; nrDig < 14; nrDig++)
                {

                    digitos[nrDig] = int.Parse(CNPJ.Substring(nrDig, 1));

                    if (nrDig <= 11)

                        soma[0] += (digitos[nrDig] *

                          int.Parse(ftmt.Substring(

                          nrDig + 1, 1)));

                    if (nrDig <= 12)

                        soma[1] += (digitos[nrDig] *

                          int.Parse(ftmt.Substring(

                          nrDig, 1)));

                }



                for (nrDig = 0; nrDig < 2; nrDig++)
                {

                    resultado[nrDig] = (soma[nrDig] % 11);

                    if ((resultado[nrDig] == 0) || (
                         resultado[nrDig] == 1))
                        CNPJOk[nrDig] = (digitos[12 + nrDig] == 0);

                    else

                        CNPJOk[nrDig] = (

                        digitos[12 + nrDig] == (

                        11 - resultado[nrDig]));

                }

                return (CNPJOk[0] && CNPJOk[1]);
            }
            catch (Exception)
            {
                return false;
            }
        }

        public static bool IsValidCNS(this String cns)
        {
            if (cns == null || cns.Trim().Length != 15)
            {
                return false;
            }

            //1. Rotina de validação de Números que iniciam com 1 ou 2:
            if (cns.StartsWith("1") || cns.StartsWith("2"))
            {
                float soma;
                float resto, dv;
                String pis = "";
                String resultado = "";
                pis = cns.Substring(0, 11);

                soma = (Convert.ToInt32(pis.Substring(0, 1)) * 15) +
                        (Convert.ToInt32(pis.Substring(1, 1)) * 14) +
                        (Convert.ToInt32(pis.Substring(2, 1)) * 13) +
                        (Convert.ToInt32(pis.Substring(3, 1)) * 12) +
                        (Convert.ToInt32(pis.Substring(4, 1)) * 11) +
                        (Convert.ToInt32(pis.Substring(5, 1)) * 10) +
                        (Convert.ToInt32(pis.Substring(6, 1)) * 9) +
                        (Convert.ToInt32(pis.Substring(7, 1)) * 8) +
                        (Convert.ToInt32(pis.Substring(8, 1)) * 7) +
                        (Convert.ToInt32(pis.Substring(9, 1)) * 6) +
                        (Convert.ToInt32(pis.Substring(10, 1)) * 5);

                //1º teste
                resto = soma % 11;
                dv = 11 - resto;

                //2º teste
                if (dv == 11)
                {
                    dv = 0;
                }

                //3º teste
                if (dv == 10)
                {
                    soma += 2;

                    resto = soma % 11;
                    dv = 11 - resto;
                    resultado = pis + "001" + dv.ToString();
                }
                else
                {
                    resultado = pis + "000" + dv.ToString();
                }

                return cns.Equals(resultado);
            }


            //2. Rotina de validação de Números que iniciam com 7, 8 ou 9:
            if (cns.StartsWith("7") || cns.StartsWith("8") || cns.StartsWith("9"))
            {
                float resto, soma;

                soma = (Convert.ToInt32(cns.Substring(0, 1)) * 15) +
                        (Convert.ToInt32(cns.Substring(1, 1)) * 14) +
                        (Convert.ToInt32(cns.Substring(2, 1)) * 13) +
                        (Convert.ToInt32(cns.Substring(3, 1)) * 12) +
                        (Convert.ToInt32(cns.Substring(4, 1)) * 11) +
                        (Convert.ToInt32(cns.Substring(5, 1)) * 10) +
                        (Convert.ToInt32(cns.Substring(6, 1)) * 9) +
                        (Convert.ToInt32(cns.Substring(7, 1)) * 8) +
                        (Convert.ToInt32(cns.Substring(8, 1)) * 7) +
                        (Convert.ToInt32(cns.Substring(9, 1)) * 6) +
                        (Convert.ToInt32(cns.Substring(10, 1)) * 5) +
                        (Convert.ToInt32(cns.Substring(11, 1)) * 4) +
                        (Convert.ToInt32(cns.Substring(12, 1)) * 3) +
                        (Convert.ToInt32(cns.Substring(13, 1)) * 2) +
                        (Convert.ToInt32(cns.Substring(14, 1)) * 1);


                resto = soma % 11;

                return resto == 0;
            }

            //se for 3, 4, 5 ou 6 é inválido
            return false;
        }

        public static string GetApplicationDomain()
        {
            throw new NotImplementedException();
        }

        public static DateTime IncrementDaysAndHoursIntoDate(DateTime date, int days, int hours)
        {
            return date.AddDays(days).AddHours(hours);
        }

        public static string CombineUrl(string p, string v)
        {
            return p + v;
        }

        public static IList<string> GerarCompetencias(int intervaloAnos)
        {
            var list = new List<string>();
            var anoAtual = DateTime.Now.Year;
            var listaMeses = new List<string>() { "12", "11", "10", "09", "08", "07", "06", "05", "04", "03", "02", "01" };
            for (var i = anoAtual; i >= anoAtual - intervaloAnos; i--)
            {
                foreach (var item in listaMeses)
                {
                    list.Add(item + "/" + i.ToString());
                }
            }

            return list;
        }

        public static int? GetMonth(string mes)
        {
            switch (mes)
            {
                case "Janeiro":
                    return 1;
                case "Fevereiro":
                    return 2;
                case "Março":
                    return 3;
                case "Abril":
                    return 4;
                case "Maio":
                    return 5;
                case "Junho":
                    return 6;
                case "Julho":
                    return 7;
                case "Agosto":
                    return 8;
                case "Setembro":
                    return 9;
                case "Outubro":
                    return 10;
                case "Novembro":
                    return 11;
                case "Dezembro":
                    return 12;
                default:
                    return null;
            }
        }

        public static string GetMonth(int mes)
        {
            switch (mes)
            {
                case 1:
                    return "Janeiro";
                case 2:
                    return "Fevereiro";
                case 3:
                    return "Março";
                case 4:
                    return "Abril";
                case 5:
                    return "Maio";
                case 6:
                    return "Junho";
                case 7:
                    return "Julho";
                case 8:
                    return "Agosto";
                case 9:
                    return "Setembro";
                case 10:
                    return "Outubro";
                case 11:
                    return "Novembro";
                case 12:
                    return "Dezembro";
                default:
                    return "Mês";
            }
        }
        public static IEnumerable<string> AllMonths(bool includeEmpty)
        {
            if (includeEmpty)
                yield return "Mês";

            yield return "Janeiro";
            yield return "Fevereiro";
            yield return "Março";
            yield return "Abril";
            yield return "Maio";
            yield return "Junho";
            yield return "Julho";
            yield return "Agosto";
            yield return "Setembro";
            yield return "Outubro";
            yield return "Novembro";
            yield return "Dezembro";
        }

        public static IEnumerable<string> AllYears(bool includeEmpty, int interval)
        {
            if (includeEmpty)
                yield return "Ano";

            var anoAtual = DateTime.Now.Year;
            var ano = anoAtual - interval;

            while (anoAtual >= ano)
            {
                yield return System.Convert.ToString(anoAtual);
                anoAtual--;
            }
        }

        public static int? GetYear(this string ano)
        {
            try
            {
                if (ano == "Ano" || ano == null)
                    return null;

                return int.Parse(ano, CultureInfo.InvariantCulture);
            }
            catch
            {
                return null;
            }
        }

        public static int DaysOfYear(this DateTime date)
        {
            var days = 0;

            for (var i = 1; i <= 12; i++) days += DateTime.DaysInMonth(date.Year, i);

            return days;
        }

        public static string RemoveWhiteSpaceAndSpecialCharacters(this string inputString)
        {
            var output = Regex.Replace(inputString, "[^0-9a-zA-Z]+", "");
            return output;
        }

        public static string BreakStringPerCaracteres(int caracterPerLine, string text)
        {
            return Regex.Replace(text, "(.{" + caracterPerLine + "})", "$1" + Environment.NewLine);
        }

        public static string HtmlTagsClear(string text)
        {
            text = text.Replace("&nbsp;", "");
            string acceptable = "script|link|title";
            string stringPattern = @"</?(?(?=" + acceptable + @")notag|[a-zA-Z0-9]+)(?:\s[a-zA-Z0-9\-]+=?(?:(["",']?).*?\1?)?)*\s*/?>";
            return Regex.Replace(text, stringPattern, String.Empty);
        }

        //public static async Task<(string Type, Stream Content)> GetPdfToImage(string url)
        //{
        //    using (var httpClient = new HttpClient())
        //    {
        //        var data = await httpClient.GetByteArrayAsync(url);

        //        if (url.ToLower().EndsWith(".pdf"))
        //        {
        //            var content = PdfToImage(data);

        //            return (Conecti.Common.Util.GetContentType("image.png"), content);
        //        }

        //        return (Conecti.Common.Util.GetContentType(url), new MemoryStream(data));
        //    }
        //}

        //public static MemoryStream PdfToImage(byte[] pdfBytes)
        //{
        //    var memoryStream = new MemoryStream();
        //    var images = new MagickImageCollection();
        //    var backdropColor = MagickColors.White;

        //    using (IDocLib pdfLibrary = DocLib.Instance)
        //    {
        //        using (var docReader = pdfLibrary.GetDocReader(pdfBytes, new PageDimensions(1.0d)))
        //        {
        //            for (int page = 0; page < docReader.GetPageCount(); page++)
        //            {
        //                using (var pageReader = docReader.GetPageReader(page))
        //                {
        //                    var rawBytes = pageReader.GetImage();
        //                    rawBytes = RearrangeBytesToRGBA(rawBytes);
        //                    var width = pageReader.GetPageWidth();
        //                    var height = pageReader.GetPageHeight();

        //                    PixelReadSettings pixelReadSettings = new PixelReadSettings(width, height, StorageType.Char, PixelMapping.RGBA);
        //                    using (MagickImage imgPdfOverlay = new MagickImage(rawBytes, pixelReadSettings))
        //                    {
        //                        // turn transparent pixels into backdrop color using composite: http://www.imagemagick.org/Usage/compose/#compose
        //                        var image = new MagickImage(backdropColor, width, height);
        //                        image.Composite(imgPdfOverlay, CompositeOperator.Over);

        //                        images.Add(image);
        //                    }
        //                }
        //            }
        //        }
        //    }

        //    //combina as imagens
        //    using (var result = images.AppendVertically())
        //    {
        //        result.Write(memoryStream, MagickFormat.Png);
        //    }
        //    images.Dispose();
        //    memoryStream.Position = 0;

        //    return memoryStream;
        //}

        public static DateTime? CastStringToDatetime(string dateString)
        {
            if (dateString == null) return null;

            return Convert.ToDateTime(dateString);
        }

        private static byte[] RearrangeBytesToRGBA(byte[] BGRABytes)
        {
            var max = BGRABytes.Length;
            var RGBABytes = new byte[max];
            var idx = 0;
            byte r;
            byte g;
            byte b;
            byte a;
            while (idx < max)
            {
                b = BGRABytes[idx];
                g = BGRABytes[idx + 1];
                r = BGRABytes[idx + 2];
                a = BGRABytes[idx + 3];

                RGBABytes[idx] = r;
                RGBABytes[idx + 1] = g;
                RGBABytes[idx + 2] = b;
                RGBABytes[idx + 3] = a;

                idx += 4;
            }
            return RGBABytes;
        }

        public static string CastToSha256(string password)
        {
            var crypt = SHA256.Create();

            string hash = String.Empty;

            byte[] crypto = crypt.ComputeHash(Encoding.UTF8.GetBytes(password), 0, Encoding.UTF8.GetByteCount(password));

            foreach (byte bit in crypto)
            {
                hash += bit.ToString("x2");
            }

            return hash;
        }

        public static string CastPrimitiveListToGETParams(string propertyName, List<string> list)
        {
            var firstItem = list.FirstOrDefault();
            if (firstItem == null) return string.Empty;

            string paramsString = $"{propertyName}={firstItem}";
            list.Remove(firstItem);

            foreach (dynamic obj in list)
            {
                paramsString += $"&{propertyName}={obj}";
            }

            return paramsString;
        }

        public static decimal PercentCalculate(decimal value, decimal total)
        {
            if (total <= 0)
                return 0;

            var result = ((value / total) * 100);
            return result;
        }


        public static string TruncateString(string input, int maxLength)
        {
            if (input.Length <= maxLength)
                return input;

            return input.Substring(0, maxLength);
        }

        public static string GetBucket(string fileUrl)
        {
            string bucket = string.Empty;
            var parts = fileUrl.Split('/');
            if (parts.Length > 5)
                bucket = parts[4];
            else
            if (parts.Length > 4)
                bucket = parts[3];

            return bucket;
        }

        public static bool IsUrlValid(string urlString)
        {
            if (Uri.TryCreate(urlString, UriKind.Absolute, out Uri uriResult)
                && (uriResult.Scheme == Uri.UriSchemeHttp || uriResult.Scheme == Uri.UriSchemeHttps))
                return true;
            else
                return false;
        }

        public static void VerifyPassword(string password)
        {
            if (!PasswordIsValid(password))
                throw new BusinessException("User.InvalidPassword", "A sua senha deve ter pelo menos 8 caracteres, com ao menos um digito, uma letra em caixa alta e um caracter especial.");
        }

        public static bool PasswordIsValid(string password)
        {
            var validate = new StrongPasswordValidatorAttribute(8, true, true, false, true);
            return validate.IsValid(password);
        }

        public static string ApplyInputUnmaskRules(string value, string keyField)
        {

            var isPhoneNumber = keyField.ToString().Contains("PHONE");
            if (isPhoneNumber)
            {
                value = value.UnMaskNumber();
                return value;
            }
            var isCPF = keyField.ToString().Contains("CPF");
            if (isCPF)
            {
                value = value.UnMaskDocument();
                return value;
            }
            var isRG = keyField.ToString().Contains("RG");
            if (isRG)
            {
                value = value.UnMaskDocument();
                return value;
            }
            var isZipCode = keyField.ToString().Contains("CEP");
            if (isZipCode)
            {
                value = value.UnMaskCEP();
                return value;
            }

            return value;
        }
        public static List<T> GetAllConstants<T>(Type type)
        {
            return type
                .GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy)
                .Where(field => field.IsLiteral && !field.IsInitOnly && field.FieldType == typeof(T))
                .Select(field => (T)field.GetRawConstantValue())
                .ToList();
        }
        public static bool IsS3Url(string url)
        {
            return url.StartsWith("http://s3") || url.StartsWith("https://s3");
        }
    }
}
