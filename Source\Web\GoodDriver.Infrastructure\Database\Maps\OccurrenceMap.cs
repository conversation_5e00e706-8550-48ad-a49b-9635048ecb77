﻿using FluentNHibernate.Mapping;
using GoodDriver.Domain.Journeys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GoodDriver.Infrastructure.Database.Maps
{
	public class OccurrenceMap : ClassMap<Occurrence>
	{
        public OccurrenceMap()
        {
			Id(u => u.Id).GeneratedBy.Identity();
			Map(a => a.Name).Not.Nullable();			
			Map(a => a.Type).Not.Nullable();
			Map(a => a.Ico).Nullable();
			Map(a => a.DiscountScore).Not.Nullable();
			Map(a => a.CreatedOn).Not.Nullable();
			Map(a => a.UpdatedOn).Nullable();
		}
    }
}
