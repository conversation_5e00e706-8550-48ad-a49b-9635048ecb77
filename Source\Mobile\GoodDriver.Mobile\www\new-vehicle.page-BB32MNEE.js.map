{"version": 3, "sources": ["src/app/pages/tabs/vehicles/create-vehicle/new-vehicle.page.ts", "src/app/pages/tabs/vehicles/create-vehicle/new-vehicle.page.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { BrandService } from 'src/app/core/services/brand.service';\nimport { ModelService } from 'src/app/core/services/model.service';\nimport { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { ListBrandResponseDto } from 'src/app/core/dtos/brand/list-brand-responseDto';\nimport { IonicModule } from '@ionic/angular';\nimport { Brand } from 'src/app/core/models/brand.model';\nimport { CommonModule } from '@angular/common';\nimport { Model } from 'src/app/core/models/model.model';\nimport { ToastService } from 'src/app/core/services/toast.service';\nimport { v4 as uuidv4 } from 'uuid';\nimport { VehicleService } from 'src/app/core/services/vehicle.service';\nimport { SessionService } from 'src/app/core/services/session.service';\nimport { NetworkService } from 'src/app/core/services/network.service';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-new-vehicle',\n  templateUrl: './new-vehicle.page.html',\n  styleUrls: ['./new-vehicle.page.scss'],\n  standalone: true,\n  imports: [IonicModule, ReactiveFormsModule, CommonModule]\n})\nexport class NewVehiclePage implements OnInit, OnDestroy {\n\n  vehicleForm: FormGroup;\n  brands: Brand[] = [];\n  models: Model[] = [];\n  isSubmitting = false;\n  isOnline = true;\n\n  // Subscriptions\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private router: Router,\n    private brandService: BrandService,\n    private modelService: ModelService,\n    private fb: FormBuilder,\n    private toastService: ToastService,\n    private sessionService: SessionService,\n    private vehicleService: VehicleService,\n    private networkService: NetworkService\n  ) {\n    this.vehicleForm = this.fb.group({\n      brand: [null, Validators.required],\n      model: [null, Validators.required],\n      version: [null],// opcional\n      year: [null, [Validators.required, Validators.min(1990), Validators.max(2026)]],\n      plate: [null, [Validators.required]],\n      policy: [null] // opcional\n    });\n   }\n\n  ngOnInit() {\n    // Check network status\n    this.checkNetworkStatus();\n\n    // Subscribe to network status changes\n    this.subscriptions.push(\n      this.networkService.getOnlineStatus().subscribe(isOnline => {\n        this.isOnline = isOnline;\n        if (isOnline) {\n          // If we're back online and don't have brands loaded yet, load them\n          if (this.brands.length === 0) {\n            this.loadBrands();\n          }\n        }\n      })\n    );\n\n    // Load brands if online\n    if (this.isOnline) {\n      this.loadBrands();\n    }\n  }\n\n  ngOnDestroy() {\n    // Clean up subscriptions\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  /**\n   * Check the current network status\n   */\n  private checkNetworkStatus() {\n    this.isOnline = this.networkService.isOnlineNow();\n  }\n\n  async loadBrands() {\n    try {\n      // Check if we're online before trying to load brands\n      if (!this.isOnline) {\n        this.toastService.showToast('Sem conexão com a internet. Não é possível carregar as marcas de veículos.', 'warning');\n        return;\n      }\n\n      const data = await this.brandService.listAll();\n      this.brands = data;\n\n    } catch (err) {\n      console.error('Error to load brands:', err);\n      this.toastService.showToast('Erro ao carregar marcas de veículos', 'danger');\n    }\n  }\n\n  async onBrandChange(branchId: any) {\n    try {\n      // Check if we're online before trying to load models\n      if (!this.isOnline) {\n        this.toastService.showToast('Sem conexão com a internet. Não é possível carregar os modelos de veículos.', 'warning');\n        return;\n      }\n\n      this.vehicleForm.patchValue({ model: null, version: null });\n      const data = await this.modelService.getByBranch({ brandId: branchId });\n      this.models = data;\n    }\n    catch (err) {\n      console.error('Error to load models:', err);\n      this.toastService.showToast('Erro ao carregar modelos de veículos', 'danger');\n    }\n  }\n\n   async save() {\n    if (this.vehicleForm.invalid) {\n      this.toastService.showToast('Por favor, preencha todos os campos obrigatórios corretamente');\n      return;\n    }\n\n    // Check if we're online before trying to save\n    if (!this.isOnline) {\n      this.toastService.showToast('Sem conexão com a internet. Não é possível cadastrar o veículo.', 'warning');\n      return;\n    }\n\n    this.isSubmitting = true;\n\n    try {\n      const formValues = this.vehicleForm.value;\n      const userId = await this.sessionService.getUserId() || '';\n\n      // Find the selected brand and model to get their names\n      const selectedBrandId = formValues.brand;\n      const selectedModelId = formValues.model;\n\n      // Find the brand and model objects\n      const selectedBrand = this.brands.find(brand => brand.id === selectedBrandId);\n      const selectedModel = this.models.find(model => model.id === selectedModelId);\n\n      // Validate that we have brand and model information\n      if (!selectedBrand || !selectedModel) {\n        this.toastService.showToast('Informações de marca ou modelo incompletas. Verifique sua conexão.', 'warning');\n        return;\n      }\n\n      const vehicleData = {\n        vehicleId: uuidv4(),\n        userId: userId,\n        plate: formValues.plate,\n        year: formValues.year,\n        brandId: formValues.brand,\n        brandName: selectedBrand?.name || '',\n        modelId: formValues.model,\n        modelName: selectedModel?.name || '',\n        version: formValues.version,\n        policyNumber: formValues.policy\n      };\n\n      // const result = await this.vehicleService.create(vehicleData);\n      await this.vehicleService.createLocal(vehicleData);\n\n      this.toastService.showToast('Veículo cadastrado com sucesso!');\n      this.router.navigate(['/tabs/vehicles']);\n    } catch (error: any) {\n      console.error('Error saving vehicle:', error);\n      this.toastService.showToast(error.error?.message || 'Erro ao cadastrar veículo', 'danger');\n    } finally {\n      this.isSubmitting = false;\n    }\n  }\n\n  // This method is called when the model selection changes\n  onModelChange(_modelId: any) {\n    // Currently not implemented, but kept for future use\n  }\n\n}\n", "<ion-header>\n  <ion-toolbar>\n    <ion-title>Novo Veículo</ion-title>\n  </ion-toolbar>\n  <ion-buttons slot=\"start\">\n    <ion-back-button defaultHref=\"/tabs/vehicles\"></ion-back-button>\n  </ion-buttons>\n</ion-header>\n\n<ion-content class=\"ion-padding\">\n  <!-- Alerta de conexão com a internet -->\n  <ion-card color=\"warning\" *ngIf=\"!isOnline\">\n    <ion-card-header>\n      <ion-card-title>\n        <ion-icon name=\"wifi-outline\"></ion-icon>\n        Atenção\n      </ion-card-title>\n    </ion-card-header>\n    <ion-card-content>\n      <p>Para cadastrar um veículo, é necessário estar conectado à internet para obter as marcas e modelos atualizados.</p>\n      <p>Por favor, verifique sua conexão e tente novamente.</p>\n    </ion-card-content>\n  </ion-card>\n\n  <!-- Alerta informativo -->\n  <ion-card color=\"light\" *ngIf=\"isOnline\">\n    <ion-card-content>\n      <ion-icon name=\"information-circle-outline\" color=\"primary\"></ion-icon>\n      <span> É necessário estar conectado à internet para obter as marcas e modelos de veículos atualizados.</span>\n    </ion-card-content>\n  </ion-card>\n\n  <form [formGroup]=\"vehicleForm\">\n    <!-- Marca -->\n    <ion-item>\n      <ion-label position=\"stacked\">Marca</ion-label>\n      <ion-select formControlName=\"brand\" (ionChange)=\"onBrandChange($event.detail.value)\" interface=\"popover\">\n        <ion-select-option *ngFor=\"let brand of brands\" [value]=\"brand.id\">{{ brand.name }}</ion-select-option>\n      </ion-select>\n    </ion-item>\n\n    <!-- Modelo -->\n    <ion-item>\n      <ion-label position=\"stacked\">Modelo</ion-label>\n      <ion-select formControlName=\"model\" (ionChange)=\"onModelChange($event.detail.value)\" interface=\"popover\">\n        <ion-select-option *ngFor=\"let model of models\" [value]=\"model.id\">{{ model.name }}</ion-select-option>\n      </ion-select>\n    </ion-item>\n\n    <!-- Versão -->\n    <ion-item>\n      <ion-label position=\"stacked\">Versão</ion-label>\n      <ion-select formControlName=\"version\" interface=\"popover\">\n        <!-- <ion-select-option *ngFor=\"let versao of versoes\" [value]=\"versao.id\">{{ versao.nome }}</ion-select-option> -->\n      </ion-select>\n    </ion-item>\n\n    <!-- Ano -->\n    <ion-item>\n      <ion-label position=\"stacked\">Ano</ion-label>\n      <ion-input formControlName=\"year\" type=\"number\" placeholder=\"Ex: 2021\" min=\"1900\" max=\"2099\"></ion-input>\n    </ion-item>\n\n    <!-- Placa -->\n    <ion-item>\n      <ion-label position=\"stacked\">Placa</ion-label>\n      <ion-input formControlName=\"plate\" maxlength=\"8\" placeholder=\"AAA-0000\" inputmode=\"text\"></ion-input>\n      <ion-note slot=\"error\" *ngIf=\"vehicleForm.get('plate')?.hasError('pattern') && vehicleForm.get('plate')?.touched\">\n        Formato inválido. Use o formato AAA-0000\n      </ion-note>\n    </ion-item>\n\n    <!-- Apólice (opcional) -->\n    <ion-item>\n      <ion-label position=\"stacked\">Número da Apólice (opcional)</ion-label>\n      <ion-input formControlName=\"policy\" type=\"text\"></ion-input>\n    </ion-item>\n\n    <!-- Botão salvar -->\n    <ion-button expand=\"block\" class=\"ion-margin-top\" (click)=\"save()\" [disabled]=\"vehicleForm.invalid || isSubmitting || !isOnline\">\n      <ion-spinner name=\"crescent\" *ngIf=\"isSubmitting\"></ion-spinner>\n      <span *ngIf=\"!isSubmitting\">Salvar</span>\n    </ion-button>\n\n    <!-- Mensagem de erro quando offline -->\n    <div *ngIf=\"!isOnline\" class=\"offline-message\">\n      <ion-text color=\"danger\">\n        <p class=\"ion-text-center\">\n          <ion-icon name=\"wifi-outline\"></ion-icon>\n          Não é possível salvar sem conexão com a internet\n        </p>\n      </ion-text>\n    </div>\n  </form>\n</ion-content>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACWE,IAAA,yBAAA,GAAA,YAAA,EAAA,EAA4C,GAAA,iBAAA,EACzB,GAAA,gBAAA;AAEb,IAAA,oBAAA,GAAA,YAAA,EAAA;AACA,IAAA,iBAAA,GAAA,iBAAA;AACF,IAAA,uBAAA,EAAiB;AAEnB,IAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,GAAA;AACb,IAAA,iBAAA,GAAA,4HAAA;AAA8G,IAAA,uBAAA;AACjH,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,wDAAA;AAAmD,IAAA,uBAAA,EAAI,EACzC;;;;;AAIrB,IAAA,yBAAA,GAAA,YAAA,EAAA,EAAyC,GAAA,kBAAA;AAErC,IAAA,oBAAA,GAAA,YAAA,EAAA;AACA,IAAA,yBAAA,GAAA,MAAA;AAAO,IAAA,iBAAA,GAAA,8GAAA;AAA+F,IAAA,uBAAA,EAAO,EAC5F;;;;;AAQf,IAAA,yBAAA,GAAA,qBAAA,EAAA;AAAmE,IAAA,iBAAA,CAAA;AAAgB,IAAA,uBAAA;;;;AAAnC,IAAA,qBAAA,SAAA,SAAA,EAAA;AAAmB,IAAA,oBAAA;AAAA,IAAA,4BAAA,SAAA,IAAA;;;;;AAQnE,IAAA,yBAAA,GAAA,qBAAA,EAAA;AAAmE,IAAA,iBAAA,CAAA;AAAgB,IAAA,uBAAA;;;;AAAnC,IAAA,qBAAA,SAAA,SAAA,EAAA;AAAmB,IAAA,oBAAA;AAAA,IAAA,4BAAA,SAAA,IAAA;;;;;AAsBrE,IAAA,yBAAA,GAAA,YAAA,EAAA;AACE,IAAA,iBAAA,GAAA,+CAAA;AACF,IAAA,uBAAA;;;;;AAWA,IAAA,oBAAA,GAAA,eAAA,EAAA;;;;;AACA,IAAA,yBAAA,GAAA,MAAA;AAA4B,IAAA,iBAAA,GAAA,QAAA;AAAM,IAAA,uBAAA;;;;;AAIpC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA+C,GAAA,YAAA,EAAA,EACpB,GAAA,KAAA,EAAA;AAErB,IAAA,oBAAA,GAAA,YAAA,EAAA;AACA,IAAA,iBAAA,GAAA,gEAAA;AACF,IAAA,uBAAA,EAAI,EACK;;;AD3FjB,IAwBa;AAxBb;;;AAGA;AAEA;AAEA;AAGA;;;;;;;;;;;;AAcM,IAAO,kBAAP,MAAO,gBAAc;MAWzB,YACU,QACA,cACA,cACA,IACA,cACA,gBACA,gBACA,gBAA8B;AAP9B,aAAA,SAAA;AACA,aAAA,eAAA;AACA,aAAA,eAAA;AACA,aAAA,KAAA;AACA,aAAA,eAAA;AACA,aAAA,iBAAA;AACA,aAAA,iBAAA;AACA,aAAA,iBAAA;AAhBV,aAAA,SAAkB,CAAA;AAClB,aAAA,SAAkB,CAAA;AAClB,aAAA,eAAe;AACf,aAAA,WAAW;AAGH,aAAA,gBAAgC,CAAA;AAYtC,aAAK,cAAc,KAAK,GAAG,MAAM;UAC/B,OAAO,CAAC,MAAM,WAAW,QAAQ;UACjC,OAAO,CAAC,MAAM,WAAW,QAAQ;UACjC,SAAS,CAAC,IAAI;;UACd,MAAM,CAAC,MAAM,CAAC,WAAW,UAAU,WAAW,IAAI,IAAI,GAAG,WAAW,IAAI,IAAI,CAAC,CAAC;UAC9E,OAAO,CAAC,MAAM,CAAC,WAAW,QAAQ,CAAC;UACnC,QAAQ,CAAC,IAAI;;SACd;MACF;MAED,WAAQ;AAEN,aAAK,mBAAkB;AAGvB,aAAK,cAAc,KACjB,KAAK,eAAe,gBAAe,EAAG,UAAU,cAAW;AACzD,eAAK,WAAW;AAChB,cAAI,UAAU;AAEZ,gBAAI,KAAK,OAAO,WAAW,GAAG;AAC5B,mBAAK,WAAU;YACjB;UACF;QACF,CAAC,CAAC;AAIJ,YAAI,KAAK,UAAU;AACjB,eAAK,WAAU;QACjB;MACF;MAEA,cAAW;AAET,aAAK,cAAc,QAAQ,SAAO,IAAI,YAAW,CAAE;MACrD;;;;MAKQ,qBAAkB;AACxB,aAAK,WAAW,KAAK,eAAe,YAAW;MACjD;MAEM,aAAU;;AACd,cAAI;AAEF,gBAAI,CAAC,KAAK,UAAU;AAClB,mBAAK,aAAa,UAAU,6FAA8E,SAAS;AACnH;YACF;AAEA,kBAAM,OAAO,MAAM,KAAK,aAAa,QAAO;AAC5C,iBAAK,SAAS;UAEhB,SAAS,KAAK;AACZ,oBAAQ,MAAM,yBAAyB,GAAG;AAC1C,iBAAK,aAAa,UAAU,0CAAuC,QAAQ;UAC7E;QACF;;MAEM,cAAc,UAAa;;AAC/B,cAAI;AAEF,gBAAI,CAAC,KAAK,UAAU;AAClB,mBAAK,aAAa,UAAU,8FAA+E,SAAS;AACpH;YACF;AAEA,iBAAK,YAAY,WAAW,EAAE,OAAO,MAAM,SAAS,KAAI,CAAE;AAC1D,kBAAM,OAAO,MAAM,KAAK,aAAa,YAAY,EAAE,SAAS,SAAQ,CAAE;AACtE,iBAAK,SAAS;UAChB,SACO,KAAK;AACV,oBAAQ,MAAM,yBAAyB,GAAG;AAC1C,iBAAK,aAAa,UAAU,2CAAwC,QAAQ;UAC9E;QACF;;MAEO,OAAI;;AA7Hb;AA8HI,cAAI,KAAK,YAAY,SAAS;AAC5B,iBAAK,aAAa,UAAU,kEAA+D;AAC3F;UACF;AAGA,cAAI,CAAC,KAAK,UAAU;AAClB,iBAAK,aAAa,UAAU,kFAAmE,SAAS;AACxG;UACF;AAEA,eAAK,eAAe;AAEpB,cAAI;AACF,kBAAM,aAAa,KAAK,YAAY;AACpC,kBAAM,UAAS,MAAM,KAAK,eAAe,UAAS,MAAM;AAGxD,kBAAM,kBAAkB,WAAW;AACnC,kBAAM,kBAAkB,WAAW;AAGnC,kBAAM,gBAAgB,KAAK,OAAO,KAAK,WAAS,MAAM,OAAO,eAAe;AAC5E,kBAAM,gBAAgB,KAAK,OAAO,KAAK,WAAS,MAAM,OAAO,eAAe;AAG5E,gBAAI,CAAC,iBAAiB,CAAC,eAAe;AACpC,mBAAK,aAAa,UAAU,+EAAsE,SAAS;AAC3G;YACF;AAEA,kBAAM,cAAc;cAClB,WAAW,WAAM;cACjB;cACA,OAAO,WAAW;cAClB,MAAM,WAAW;cACjB,SAAS,WAAW;cACpB,YAAW,+CAAe,SAAQ;cAClC,SAAS,WAAW;cACpB,YAAW,+CAAe,SAAQ;cAClC,SAAS,WAAW;cACpB,cAAc,WAAW;;AAI3B,kBAAM,KAAK,eAAe,YAAY,WAAW;AAEjD,iBAAK,aAAa,UAAU,oCAAiC;AAC7D,iBAAK,OAAO,SAAS,CAAC,gBAAgB,CAAC;UACzC,SAAS,OAAY;AACnB,oBAAQ,MAAM,yBAAyB,KAAK;AAC5C,iBAAK,aAAa,YAAU,WAAM,UAAN,mBAAa,YAAW,gCAA6B,QAAQ;UAC3F;AACE,iBAAK,eAAe;UACtB;QACF;;;MAGA,cAAc,UAAa;MAE3B;;;uCAlKW,iBAAc,4BAAA,MAAA,GAAA,4BAAA,YAAA,GAAA,4BAAA,YAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,YAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,cAAA,CAAA;IAAA;oFAAd,iBAAc,WAAA,CAAA,CAAA,iBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,QAAA,OAAA,GAAA,CAAA,eAAA,gBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,WAAA,GAAA,MAAA,GAAA,CAAA,SAAA,SAAA,GAAA,MAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,YAAA,SAAA,GAAA,CAAA,mBAAA,SAAA,aAAA,WAAA,GAAA,WAAA,GAAA,CAAA,GAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,mBAAA,SAAA,aAAA,WAAA,GAAA,WAAA,GAAA,CAAA,mBAAA,WAAA,aAAA,SAAA,GAAA,CAAA,mBAAA,QAAA,QAAA,UAAA,eAAA,YAAA,OAAA,QAAA,OAAA,MAAA,GAAA,CAAA,mBAAA,SAAA,aAAA,KAAA,eAAA,YAAA,aAAA,MAAA,GAAA,CAAA,QAAA,SAAA,GAAA,MAAA,GAAA,CAAA,mBAAA,UAAA,QAAA,MAAA,GAAA,CAAA,UAAA,SAAA,GAAA,kBAAA,GAAA,SAAA,UAAA,GAAA,CAAA,QAAA,YAAA,GAAA,MAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,QAAA,cAAA,GAAA,CAAA,SAAA,OAAA,GAAA,CAAA,QAAA,8BAAA,SAAA,SAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,QAAA,OAAA,GAAA,CAAA,QAAA,UAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,SAAA,QAAA,GAAA,CAAA,GAAA,iBAAA,CAAA,GAAA,UAAA,SAAA,wBAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;ACxB3B,QAAA,yBAAA,GAAA,YAAA,EAAY,GAAA,aAAA,EACG,GAAA,WAAA;AACA,QAAA,iBAAA,GAAA,iBAAA;AAAY,QAAA,uBAAA,EAAY;AAErC,QAAA,yBAAA,GAAA,eAAA,CAAA;AACE,QAAA,oBAAA,GAAA,mBAAA,CAAA;AACF,QAAA,uBAAA,EAAc;AAGhB,QAAA,yBAAA,GAAA,eAAA,CAAA;AAEE,QAAA,qBAAA,GAAA,oCAAA,IAAA,GAAA,YAAA,CAAA,EAA4C,GAAA,oCAAA,GAAA,GAAA,YAAA,CAAA;AAqB5C,QAAA,yBAAA,GAAA,QAAA,CAAA,EAAgC,IAAA,UAAA,EAEpB,IAAA,aAAA,CAAA;AACsB,QAAA,iBAAA,IAAA,OAAA;AAAK,QAAA,uBAAA;AACnC,QAAA,yBAAA,IAAA,cAAA,CAAA;AAAoC,QAAA,qBAAA,aAAA,SAAA,yDAAA,QAAA;AAAA,iBAAa,IAAA,cAAA,OAAA,OAAA,KAAA;QAAkC,CAAA;AACjF,QAAA,qBAAA,IAAA,8CAAA,GAAA,GAAA,qBAAA,CAAA;AACF,QAAA,uBAAA,EAAa;AAIf,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,aAAA,CAAA;AACsB,QAAA,iBAAA,IAAA,QAAA;AAAM,QAAA,uBAAA;AACpC,QAAA,yBAAA,IAAA,cAAA,CAAA;AAAoC,QAAA,qBAAA,aAAA,SAAA,yDAAA,QAAA;AAAA,iBAAa,IAAA,cAAA,OAAA,OAAA,KAAA;QAAkC,CAAA;AACjF,QAAA,qBAAA,IAAA,8CAAA,GAAA,GAAA,qBAAA,CAAA;AACF,QAAA,uBAAA,EAAa;AAIf,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,aAAA,CAAA;AACsB,QAAA,iBAAA,IAAA,WAAA;AAAM,QAAA,uBAAA;AACpC,QAAA,oBAAA,IAAA,cAAA,EAAA;AAGF,QAAA,uBAAA;AAGA,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,aAAA,CAAA;AACsB,QAAA,iBAAA,IAAA,KAAA;AAAG,QAAA,uBAAA;AACjC,QAAA,oBAAA,IAAA,aAAA,EAAA;AACF,QAAA,uBAAA;AAGA,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,aAAA,CAAA;AACsB,QAAA,iBAAA,IAAA,OAAA;AAAK,QAAA,uBAAA;AACnC,QAAA,oBAAA,IAAA,aAAA,EAAA;AACA,QAAA,qBAAA,IAAA,qCAAA,GAAA,GAAA,YAAA,EAAA;AAGF,QAAA,uBAAA;AAGA,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,aAAA,CAAA;AACsB,QAAA,iBAAA,IAAA,oCAAA;AAA4B,QAAA,uBAAA;AAC1D,QAAA,oBAAA,IAAA,aAAA,EAAA;AACF,QAAA,uBAAA;AAGA,QAAA,yBAAA,IAAA,cAAA,EAAA;AAAkD,QAAA,qBAAA,SAAA,SAAA,uDAAA;AAAA,iBAAS,IAAA,KAAA;QAAM,CAAA;AAC/D,QAAA,qBAAA,IAAA,wCAAA,GAAA,GAAA,eAAA,EAAA,EAAkD,IAAA,iCAAA,GAAA,GAAA,QAAA,EAAA;AAEpD,QAAA,uBAAA;AAGA,QAAA,qBAAA,IAAA,gCAAA,GAAA,GAAA,OAAA,EAAA;AAQF,QAAA,uBAAA,EAAO;;;;AAlFoB,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,QAAA,CAAA,IAAA,QAAA;AAcF,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,QAAA;AAOnB,QAAA,oBAAA;AAAA,QAAA,qBAAA,aAAA,IAAA,WAAA;AAKqC,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,WAAA,IAAA,MAAA;AAQA,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,WAAA,IAAA,MAAA;AAsBf,QAAA,oBAAA,EAAA;AAAA,QAAA,qBAAA,UAAA,UAAA,IAAA,YAAA,IAAA,OAAA,MAAA,OAAA,OAAA,QAAA,SAAA,SAAA,QAAA,UAAA,IAAA,YAAA,IAAA,OAAA,MAAA,OAAA,OAAA,QAAA,QAAA;AAYyC,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,YAAA,IAAA,YAAA,WAAA,IAAA,gBAAA,CAAA,IAAA,QAAA;AACnC,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,YAAA;AACvB,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,CAAA,IAAA,YAAA;AAIH,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,CAAA,IAAA,QAAA;;sBD/DE,aAAW,WAAA,YAAA,SAAA,gBAAA,eAAA,cAAA,YAAA,WAAA,SAAA,UAAA,SAAA,UAAA,SAAA,WAAA,iBAAA,YAAA,SAAA,UAAA,YAAA,+BAAA,8BAAA,4BAAA,eAAA,iBAAA,iBAAE,qBAAmB,oBAAA,iBAAA,sBAAA,oBAAA,oBAAA,iBAAE,cAAY,SAAA,IAAA,GAAA,QAAA,CAAA,wjCAAA,EAAA,CAAA;AAEpD,IAAO,iBAAP;;0EAAO,gBAAc,CAAA;cAP1B;2BACW,mBAAiB,YAGf,MAAI,SACP,CAAC,aAAa,qBAAqB,YAAY,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,sxBAAA,EAAA,CAAA;;;;iFAE9C,gBAAc,EAAA,WAAA,kBAAA,UAAA,kEAAA,YAAA,GAAA,CAAA;IAAA,GAAA;;;", "names": []}