﻿using Dapper;
using GoodDriver.Contracts.Sync.Commands;
using Microsoft.Extensions.Configuration;
using Rogerio.Commom.Exceptions;
using Rogerio.Cqrs.Commands;
using Rogerio.Data;
using System.Data;

namespace GoodDriver.CommandHandlers.Sync.Handler
{
    public class SyncUserCommandHandler : BaseDataAccess, ICommandHandler<SyncUserCommand>
    {
        public SyncUserCommandHandler(IConfiguration configuration) : base(configuration)
        {
        }

        public async Task HandleAsync(SyncUserCommand command)
        {
            try
            {
                using (var connection = CreateConnection())
                {
                    // Check if the user exists in the AppSyncUser table
                    var existingUser = await connection.QueryFirstOrDefaultAsync<dynamic>(
                        "SELECT Id FROM AppSyncUser WHERE Id = @Id",
                        new { Id = Guid.Parse(command.UserId) });

                    if (existingUser != null)
                    {
                        // Update existing record
                        await connection.ExecuteAsync(
                            @"UPDATE AppSyncUser 
                            SET Name = @Name, 
                                Email = @Email, 
                                Phone = @Phone, 
                                Document = @Document, 
                                BirthDate = @BirthDate, 
                                DeviceId = @DeviceId, 
                                DeviceModel = @DeviceModel, 
                                DeviceOS = @DeviceOS, 
                                AppVersion = @AppVersion, 
                                SyncStatus = @SyncStatus, 
                                SyncDate = @SyncDate, 
                                ProcessedStatus = 'PENDING', 
                                ProcessedDate = NULL, 
                                ErrorMessage = NULL, 
                                UpdatedOn = @UpdatedOn 
                            WHERE Id = @Id",
                            new
                            {
                                Id = Guid.Parse(command.UserId),
                                command.Name,
                                command.Email,
                                command.Phone,
                                command.Document,
                                command.BirthDate,
                                command.DeviceId,
                                command.DeviceModel,
                                command.DeviceOS,
                                command.AppVersion,
                                command.SyncStatus,
                                SyncDate = DateTime.Now,
                                UpdatedOn = DateTime.Now
                            });
                    }
                    else
                    {
                        // Insert new record
                        await connection.ExecuteAsync(
                            @"INSERT INTO AppSyncUser 
                            (Id, Name, Email, Phone, Document, BirthDate, DeviceId, DeviceModel, DeviceOS, 
                             AppVersion, SyncStatus, SyncDate, ProcessedStatus, CreatedOn, UpdatedOn) 
                            VALUES 
                            (@Id, @Name, @Email, @Phone, @Document, @BirthDate, @DeviceId, @DeviceModel, @DeviceOS, 
                             @AppVersion, @SyncStatus, @SyncDate, 'PENDING', @CreatedOn, @UpdatedOn)",
                            new
                            {
                                Id = Guid.Parse(command.UserId),
                                command.Name,
                                command.Email,
                                command.Phone,
                                command.Document,
                                command.BirthDate,
                                command.DeviceId,
                                command.DeviceModel,
                                command.DeviceOS,
                                command.AppVersion,
                                command.SyncStatus,
                                SyncDate = DateTime.Now,
                                CreatedOn = DateTime.Now,
                                UpdatedOn = DateTime.Now
                            });
                    }
                }
            }
            catch (Exception ex)
            {
                throw new BusinessException("SYNC_USER_ERROR", $"Error synchronizing user data: {ex.Message}");
            }
        }
    }
}
