{"version": 3, "sources": ["node_modules/@ionic/core/components/status-tap.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { readTask, writeTask } from '@stencil/core/internal/client';\nimport { a as findClosestIonContent, s as scrollToTop } from './index8.js';\nimport { c as componentOnReady } from './helpers.js';\nconst startStatusTap = () => {\n  const win = window;\n  win.addEventListener('statusTap', () => {\n    readTask(() => {\n      const width = win.innerWidth;\n      const height = win.innerHeight;\n      const el = document.elementFromPoint(width / 2, height / 2);\n      if (!el) {\n        return;\n      }\n      const contentEl = findClosestIonContent(el);\n      if (contentEl) {\n        new Promise(resolve => componentOnReady(contentEl, resolve)).then(() => {\n          writeTask(async () => {\n            /**\n             * If scrolling and user taps status bar,\n             * only calling scrollToTop is not enough\n             * as engines like WebKit will jump the\n             * scroll position back down and complete\n             * any in-progress momentum scrolling.\n             */\n            contentEl.style.setProperty('--overflow', 'hidden');\n            await scrollToTop(contentEl, 300);\n            contentEl.style.removeProperty('--overflow');\n          });\n        });\n      }\n    });\n  });\n};\nexport { startStatusTap };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,IAMM;AANN;AAAA;AAGA;AACA;AACA;AACA,IAAM,iBAAiB,MAAM;AAC3B,YAAM,MAAM;AACZ,UAAI,iBAAiB,aAAa,MAAM;AACtC,iBAAS,MAAM;AACb,gBAAM,QAAQ,IAAI;AAClB,gBAAM,SAAS,IAAI;AACnB,gBAAM,KAAK,SAAS,iBAAiB,QAAQ,GAAG,SAAS,CAAC;AAC1D,cAAI,CAAC,IAAI;AACP;AAAA,UACF;AACA,gBAAM,YAAY,sBAAsB,EAAE;AAC1C,cAAI,WAAW;AACb,gBAAI,QAAQ,aAAW,iBAAiB,WAAW,OAAO,CAAC,EAAE,KAAK,MAAM;AACtE,wBAAU,MAAY;AAQpB,0BAAU,MAAM,YAAY,cAAc,QAAQ;AAClD,sBAAM,YAAY,WAAW,GAAG;AAChC,0BAAU,MAAM,eAAe,YAAY;AAAA,cAC7C,EAAC;AAAA,YACH,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA;AAAA;", "names": [], "x_google_ignoreList": [0]}