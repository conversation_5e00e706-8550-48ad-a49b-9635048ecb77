{"version": 3, "sources": ["node_modules/@capacitor/synapse/dist/synapse.mjs", "node_modules/@capacitor/geolocation/dist/esm/definitions.js", "node_modules/@capacitor/geolocation/dist/esm/index.js", "node_modules/@capacitor/motion/dist/esm/definitions.js", "node_modules/@capacitor/motion/dist/esm/index.js", "src/app/core/services/journeyinfo.service.ts", "src/app/core/services/journey-storage.service.ts"], "sourcesContent": ["function s(t) {\n  t.CapacitorUtils.Synapse = new Proxy({}, {\n    get(e, n) {\n      return new Proxy({}, {\n        get(w, o) {\n          return (c, p, r) => {\n            const i = t.Capacitor.Plugins[n];\n            if (i === void 0) {\n              r(new Error(`Capacitor plugin ${n} not found`));\n              return;\n            }\n            if (typeof i[o] != \"function\") {\n              r(new Error(`Method ${o} not found in Capacitor plugin ${n}`));\n              return;\n            }\n            (async () => {\n              try {\n                const a = await i[o](c);\n                p(a);\n              } catch (a) {\n                r(a);\n              }\n            })();\n          };\n        }\n      });\n    }\n  });\n}\nfunction u(t) {\n  t.CapacitorUtils.Synapse = new Proxy({}, {\n    get(e, n) {\n      return t.cordova.plugins[n];\n    }\n  });\n}\nfunction f(t = !1) {\n  typeof window > \"u\" || (window.CapacitorUtils = window.CapacitorUtils || {}, window.Capacitor !== void 0 && !t ? s(window) : window.cordova !== void 0 && u(window));\n}\nexport { f as exposeSynapse };", "export {};\n", "import { registerPlugin } from '@capacitor/core';\nimport { exposeSynapse } from '@capacitor/synapse';\nconst Geolocation = registerPlugin('Geolocation', {\n  web: () => import('./web').then(m => new m.GeolocationWeb())\n});\nexposeSynapse();\nexport * from './definitions';\nexport { Geolocation };\n", "export {};\n", "import { registerPlugin } from '@capacitor/core';\nconst Motion = registerPlugin('Motion', {\n  android: () => import('./web').then(m => new m.MotionWeb()),\n  ios: () => import('./web').then(m => new m.MotionWeb()),\n  web: () => import('./web').then(m => new m.MotionWeb())\n});\nexport * from './definitions';\nexport { Motion };\n", "import { Injectable } from '@angular/core';\r\nimport { Geolocation } from '@capacitor/geolocation';  // Se estiver usando Capacitor\r\nimport { IDataStorage } from 'src/app/core/storage/data-storage.interface';  // Seu serviço de armazenamento\r\nimport { JourneyInfo } from 'src/app/core/models/journeyInfo.model'  // Seu modelo JourneyInfo\r\nimport { DataStorageService } from './data-storage-service';\r\nimport { Motion } from '@capacitor/motion';\r\nimport { BehaviorSubject } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class JourneyInfoService {\r\n  private journeyId: string = '';\r\n  private trackingInterval: any = null;\r\n  private trackingStartTime: number = 0;\r\n  private totalTimeAboveSpeed = 0;\r\n  private storage: IDataStorage;\r\n\r\n  // Para detecção de freada brusca baseada em velocidade\r\n  private previousSpeed: number = 0;\r\n  private previousSpeedTime: number = 0;\r\n\r\n  // Observable para o status de rastreamento\r\n  private trackingActiveSubject = new BehaviorSubject<boolean>(false);\r\n  public trackingActive$ = this.trackingActiveSubject.asObservable();\r\n\r\n  constructor(private dataStorageService: DataStorageService) {\r\n    // Inicializa o serviço de dados (supondo que você tenha um serviço de dados como IndexedDBStorageService)\r\n    this.storage = this.dataStorageService;\r\n    \r\n  }\r\n\r\n  // Função para iniciar o rastreamento\r\n  startTracking(journeyId: string) {\r\n    this.trackingStartTime = Date.now();\r\n    this.totalTimeAboveSpeed = 0;  // Reseta o tempo acima da velocidade\r\n    this.journeyId = journeyId;\r\n\r\n    // Reseta valores para detecção de freada brusca\r\n    this.previousSpeed = 0;\r\n    this.previousSpeedTime = 0;\r\n\r\n    // Atualiza o estado de rastreamento\r\n    this.trackingActiveSubject.next(true);\r\n\r\n    this.trackingInterval = setInterval(async () => {\r\n      try {\r\n        console.log('Executando ciclo de rastreamento...');\r\n\r\n        const location = await this.getCurrentLocation();\r\n        if (location) {\r\n          console.log('Localização obtida:', location);\r\n\r\n          // Verifica eventos de direção com tratamento de erro individual\r\n          try {\r\n            console.log('Verificando freada brusca...');\r\n            const hardBreakDetected = await this.checkHardBreak(location);\r\n            console.log('Resultado checkHardBreak:', hardBreakDetected);\r\n          } catch (error) {\r\n            console.error('Erro em checkHardBreak:', error);\r\n          }\r\n\r\n          try {\r\n            console.log('Verificando alta velocidade...');\r\n            const highVelocityDetected = await this.checkHighVelocity(location);\r\n            console.log('Resultado checkHighVelocity:', highVelocityDetected);\r\n          } catch (error) {\r\n            console.error('Erro em checkHighVelocity:', error);\r\n          }\r\n\r\n          // Salva a localização periodicamente\r\n          try {\r\n            console.log('Salvando localização...');\r\n            await this.saveLocation(location);\r\n            console.log('Localização salva com sucesso');\r\n          } catch (error) {\r\n            console.error('Erro ao salvar localização:', error);\r\n          }\r\n        } else {\r\n          console.log('Não foi possível obter localização');\r\n        }\r\n      } catch (error) {\r\n        console.error('Erro no ciclo de rastreamento:', error);\r\n      }\r\n    }, 10000);  // A cada 10 segundos\r\n\r\n    console.log('Rastreamento iniciado para viagem:', journeyId);\r\n  }\r\n\r\n  // Função para parar o rastreamento\r\n  stopTracking(journeyId: string) {\r\n    if (this.trackingInterval) {\r\n      clearInterval(this.trackingInterval);  // Limpa o intervalo\r\n      this.trackingInterval = null;\r\n\r\n      // Atualiza o estado de rastreamento\r\n      this.trackingActiveSubject.next(false);\r\n\r\n      console.log('Rastreamento parado para viagem:', journeyId);\r\n    }\r\n  }\r\n\r\n  // Função para obter a localização atual\r\n  private async getCurrentLocation(): Promise<JourneyInfo | null> {\r\n    try {\r\n      const coordinates = await Geolocation.getCurrentPosition(\r\n        { enableHighAccuracy: true,\r\n          timeout: 10000\r\n         }\r\n      );  // Usando Capacitor\r\n      const location: JourneyInfo = {\r\n        id: this.generateUniqueId(),\r\n        journeyId: this.journeyId,\r\n        latitude: coordinates.coords.latitude,\r\n        longitude: coordinates.coords.longitude,\r\n        timestamp: new Date().toISOString(),  // Marca o horário atual\r\n      };\r\n      return location;\r\n    } catch (error) {\r\n      console.error('Erro ao obter a localização:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // Função para obter a velocidade atual (em km/h)\r\n  private async getCurrentSpeed(): Promise<number> {\r\n    try {\r\n      const position = await Geolocation.getCurrentPosition();  // Usando Capacitor\r\n      const speed = position.coords.speed ? position.coords.speed * 3.6 : 0;  // Converte de m/s para km/h ou assume 0 se for null\r\n      return speed;  // Retorna a velocidade em km/h\r\n    } catch (error) {\r\n      console.error('Erro ao obter a velocidade:', error);\r\n      return 0;  // Se der erro, assume velocidade 0\r\n    }\r\n  }\r\n\r\n  // Função para salvar a localização no banco de dados\r\n  private async saveLocation(location: JourneyInfo): Promise<void> {\r\n    try {\r\n      // Use the correct table name 'journeyInfo' instead of 'location'\r\n      await this.storage.insert('journeyInfo', location);\r\n      console.log('Localização salva:', location);\r\n    } catch (error) {\r\n      console.error('Erro ao salvar a localização:', error);\r\n    }\r\n  }\r\n\r\n  // Função para gerar um ID único para cada localização (pode ser melhorada)\r\n  private generateUniqueId(): string {\r\n    return Math.random().toString(36).substring(2, 15);\r\n  }\r\n\r\n  async checkHardBreak(location: JourneyInfo): Promise<boolean> {\r\n    try {\r\n      // Método 1: Detecção baseada em velocidade (mais confiável)\r\n      const currentSpeed = await this.getCurrentSpeed();\r\n      const currentTime = Date.now();\r\n\r\n      if (this.previousSpeed > 0 && this.previousSpeedTime > 0) {\r\n        const speedDifference = this.previousSpeed - currentSpeed; // Diferença de velocidade\r\n        const timeDifference = (currentTime - this.previousSpeedTime) / 1000; // Diferença de tempo em segundos\r\n\r\n        if (timeDifference > 0) {\r\n          const deceleration = speedDifference / timeDifference; // Desaceleração em km/h por segundo\r\n\r\n          console.log(`Velocidade anterior: ${this.previousSpeed} km/h, Atual: ${currentSpeed} km/h`);\r\n          console.log(`Desaceleração: ${deceleration} km/h/s`);\r\n\r\n          // Se a desaceleração for maior que 15 km/h por segundo, considera freada brusca\r\n          if (deceleration > 15) {\r\n            console.log('Freada brusca detectada por velocidade!');\r\n            location.occurrenceType = 'HardBreak';\r\n            await this.saveLocation(location);\r\n\r\n            // Atualiza valores para próxima verificação\r\n            this.previousSpeed = currentSpeed;\r\n            this.previousSpeedTime = currentTime;\r\n\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Atualiza valores para próxima verificação\r\n      this.previousSpeed = currentSpeed;\r\n      this.previousSpeedTime = currentTime;\r\n\r\n      // Método 2: Fallback usando sensor de movimento (com timeout)\r\n      return await this.checkHardBreakWithMotionSensor(location);\r\n\r\n    } catch (error) {\r\n      console.error('Erro em checkHardBreak:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  private async checkHardBreakWithMotionSensor(location: JourneyInfo): Promise<boolean> {\r\n    return new Promise(async (resolve) => {\r\n      let isResolved = false;\r\n      let listenerHandle: any = null;\r\n\r\n      try {\r\n        // Verifica se o Motion sensor está disponível\r\n        const isMotionAvailable = await this.isMotionSensorAvailable();\r\n        if (!isMotionAvailable) {\r\n          console.log('Sensor de movimento não disponível');\r\n          resolve(false);\r\n          return;\r\n        }\r\n\r\n        listenerHandle = await Motion.addListener('accel', (event) => {\r\n          const { acceleration } = event;\r\n\r\n          if (acceleration?.x && acceleration.x < -10) {\r\n            console.log('Freada brusca detectada por sensor de movimento!');\r\n\r\n            location.occurrenceType = 'HardBreak';\r\n            this.saveLocation(location);\r\n\r\n            // Cancela o listener para não continuar ouvindo\r\n            if (listenerHandle) {\r\n              listenerHandle.remove();\r\n            }\r\n\r\n            if (!isResolved) {\r\n              isResolved = true;\r\n              resolve(true); // Responde que detectou a freada\r\n            }\r\n          }\r\n        });\r\n\r\n        // Timeout para resolver a Promise após um tempo limite\r\n        setTimeout(() => {\r\n          if (!isResolved) {\r\n            isResolved = true;\r\n            if (listenerHandle) {\r\n              listenerHandle.remove(); // Remove o listener\r\n            }\r\n            resolve(false); // Não detectou freada brusca\r\n          }\r\n        }, 1000); // Aguarda apenas 1 segundo para o sensor de movimento\r\n\r\n      } catch (error) {\r\n        console.error('Erro ao configurar listener de aceleração:', error);\r\n        if (!isResolved) {\r\n          isResolved = true;\r\n          resolve(false);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  private async isMotionSensorAvailable(): Promise<boolean> {\r\n    try {\r\n      // Tenta criar um listener temporário para verificar se o sensor está disponível\r\n      const testListener = await Motion.addListener('accel', () => {});\r\n      if (testListener) {\r\n        testListener.remove();\r\n        return true;\r\n      }\r\n      return false;\r\n    } catch (error) {\r\n      console.error('Erro ao verificar disponibilidade do sensor de movimento:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n\r\n  async checkHighVelocity(location: JourneyInfo): Promise<boolean> {\r\n    const speedKmH = await this.getCurrentSpeed();\r\n\r\n     if (speedKmH > 120) { // Se a velocidade for maior que 120 km/h\r\n          console.log('Alta velocidade detectada!');\r\n          location.occurrenceType = 'Acceleration';  // Define o tipo de ocorrência como alta velocidade\r\n          this.saveLocation(location);\r\n          return true; // Retorna verdadeiro se a velocidade for maior que 120 km/h\r\n     }\r\n        return false; // Retorna falso se a velocidade for menor ou igual a 120 km/h\r\n  }\r\n}\r\n", "import { Injectable } from '@angular/core';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport { SessionService } from './session.service';\r\nimport { Journey } from '../models/journey.model';\r\nimport { DataStorageService } from './data-storage-service';\r\nimport { Platform } from '@ionic/angular'; // Para detecção da plataforma (dispositivo ou navegador)\r\nimport { JourneyInfo } from '../models/journeyInfo.model';\r\nimport { JourneyInfoService } from './journeyinfo.service';\r\nimport { VehicleService } from './vehicle.service';\r\nimport { NetworkService } from './network.service';\r\nimport { SyncStatus } from '../models/sync.model';\r\nimport { JourneySyncRequestDto } from '../dtos/journey/journeySyncRequestDto';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { ApiService } from './api.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class JourneyStorageService\r\n{\r\n    constructor(\r\n      private sessionService: SessionService,\r\n      private dataStorageService: DataStorageService,\r\n      private platform: Platform,\r\n      private journeyInfoService: JourneyInfoService,\r\n      private vehicleService: VehicleService,\r\n      private networkService: NetworkService,\r\n      private http: HttpClient,\r\n      private apiService: ApiService\r\n    ) {\r\n      this.isNative = this.platform.is('capacitor') || this.platform.is('cordova');\r\n    }\r\n\r\n  private readonly tableName = 'journeys';\r\n  private db: any; // Referência ao banco de dados (SQLite ou IndexedDB)\r\n  private isNative: boolean; // Variável para verificar se é dispositivo físico\r\n\r\n  async init()\r\n  {\r\n    await this.dataStorageService.init();\r\n  }\r\n\r\n  /**\r\n   * Starts a new journey if the user has at least one vehicle\r\n   * @returns The journey ID if successful, null if no vehicles exist\r\n   */\r\n  async startJourney(): Promise<string | null> {\r\n    const userId = await this.sessionService.getUserId();\r\n\r\n    // Check if the user has any vehicles\r\n    const hasVehicles = await this.vehicleService.hasVehicles(userId);\r\n    if (!hasVehicles) {\r\n      console.error('Cannot start journey: No vehicles registered for this user');\r\n      return null;\r\n    }\r\n\r\n    // Get the primary vehicle\r\n    const primaryVehicle = await this.vehicleService.getPrimaryVehicle(userId);\r\n    if (!primaryVehicle) {\r\n      console.error('Cannot start journey: No primary vehicle set');\r\n      return null;\r\n    }\r\n\r\n    const id = uuidv4();\r\n    const startDate = new Date().toISOString();\r\n\r\n    // Determine sync status based on network connectivity\r\n    const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingCreate : SyncStatus.PendingCreate;\r\n\r\n    // Create the journey with the primary vehicle ID\r\n    await this.dataStorageService.insert(this.tableName, {\r\n      id: id,\r\n      startDate: startDate,\r\n      userId: userId,\r\n      vehicleId: primaryVehicle.id,\r\n      syncStatus: syncStatus,\r\n      lastSyncDate: null\r\n    });\r\n\r\n    // Start tracking the journey\r\n    this.journeyInfoService.startTracking(id);\r\n    return id;\r\n  }\r\n\r\n  async endJourney(journeyId: string) {\r\n    this.journeyInfoService.stopTracking(journeyId);\r\n    const endDate = new Date().toISOString();\r\n\r\n    // Calcular a distância total da viagem\r\n    // Use the correct table name 'journeyInfo' instead of 'location'\r\n    const result = await this.dataStorageService.select('journeyInfo', `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);\r\n\r\n    // Handle the result properly for both IndexedDB and SQLite\r\n    let infosJourney: JourneyInfo[] = [];\r\n    if (Array.isArray(result)) {\r\n      infosJourney = result;\r\n    } else if (result) {\r\n      // If result is not an array but exists, try to convert it to an array\r\n      try {\r\n        infosJourney = Array.isArray(result) ? result : [];\r\n      } catch (error) {\r\n        console.error('Error converting journey info to array:', error);\r\n      }\r\n    }\r\n\r\n    let totalDistance = 0;\r\n\r\n    if (infosJourney && infosJourney.length > 1) {\r\n      for (let i = 0; i < infosJourney.length - 1; i++) {\r\n        const start = infosJourney[i];\r\n        const end = infosJourney[i + 1];\r\n        totalDistance += this.calculateDistance(\r\n          start.latitude, start.longitude,\r\n          end.latitude, end.longitude\r\n        );\r\n      }\r\n    }\r\n\r\n    // Determine sync status based on network connectivity\r\n    const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingUpdate : SyncStatus.PendingUpdate;\r\n\r\n    // Update the journey with the calculated distance\r\n    await this.dataStorageService.update(\r\n      this.tableName,\r\n      {\r\n        endDate: endDate,\r\n        distance: totalDistance,\r\n        syncStatus: syncStatus,\r\n        lastSyncDate: null\r\n      },\r\n      `id = '${journeyId}'`\r\n    );\r\n  }\r\n\r\n  async addLocation(journeyId: string, latitude: number, longitude: number) {\r\n    const id = uuidv4();\r\n    const timestamp = new Date().toISOString();\r\n\r\n    // Determine sync status based on network connectivity\r\n    const syncStatus = this.networkService.isOnlineNow() ? SyncStatus.PendingCreate : SyncStatus.PendingCreate;\r\n\r\n    // Use the correct table name 'journeyInfo' instead of 'location'\r\n    await this.dataStorageService.insert('journeyInfo', {\r\n      id: id,\r\n      journeyId: journeyId,\r\n      latitude: latitude,\r\n      longitude: longitude,\r\n      timestamp: timestamp,\r\n      syncStatus: syncStatus,\r\n      lastSyncDate: null\r\n    });\r\n  }\r\n\r\n  async closeConnection() {\r\n    // Fechar a conexão com o banco de dados se estiver usando SQLite\r\n    if (this.db) {\r\n      this.db.close();\r\n      this.db = null;\r\n     }\r\n  }\r\n\r\n  async getAllJourneys(): Promise<Journey[]>\r\n  {\r\n    let result = await this.dataStorageService.select(this.tableName, 'ORDER BY startDate DESC');\r\n\r\n    if(!result || result.length === 0)\r\n      return [];\r\n\r\n    for (let i = 0; i < result.length; i++) {\r\n      const journeyId = result[i].id;\r\n      // Use the correct table name 'journeyInfo' instead of 'location'\r\n      const infosJourney = await this.dataStorageService.select('journeyInfo', `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);\r\n\r\n      // Ensure we have an array of journey info objects\r\n      if (Array.isArray(infosJourney)) {\r\n        result[i].infos = infosJourney.filter((info: JourneyInfo) => info.journeyId === journeyId);\r\n      } else {\r\n        result[i].infos = [];\r\n      }\r\n    }\r\n\r\n    const journeys: Journey[] = result.map((data: any) => {\r\n      return {\r\n        id: data.id,\r\n        startDate: data.startDate,\r\n        endDate: data.endDate,\r\n        distance: data.distance,\r\n        userId: data.userId,\r\n        vehicleId: data.vehicleId,\r\n        infosJourney: data.infos,\r\n        syncStatus: data.syncStatus || SyncStatus.Synced,\r\n        lastSyncDate: data.lastSyncDate ? new Date(data.lastSyncDate) : undefined\r\n      };\r\n    });\r\n    return journeys;\r\n  }\r\n\r\n  async getAllJourneysByUser(userId: string): Promise<Journey[]>\r\n  {\r\n    // 1. Buscar dados do usuário no Local Storage\r\n    let result = await this.dataStorageService.select(this.tableName, ' ORDER BY startDate DESC');\r\n\r\n    // Filter results by userId\r\n    if (Array.isArray(result)) {\r\n      result = result.filter((data: any) => data.userId === userId);\r\n    } else {\r\n      return [];\r\n    }\r\n\r\n    if(!result || result.length === 0)\r\n      return [];\r\n\r\n    for (let i = 0; i < result.length; i++) {\r\n      const journeyId = result[i].id;\r\n      // Use the correct table name 'journeyInfo' instead of 'location'\r\n      const infosJourney = await this.dataStorageService.select('journeyInfo', `WHERE journeyId = '${journeyId}' ORDER BY timestamp ASC`);\r\n\r\n      // Ensure we have an array of journey info objects\r\n      if (Array.isArray(infosJourney)) {\r\n        result[i].infos = infosJourney.filter((info: JourneyInfo) => info.journeyId === journeyId);\r\n      } else {\r\n        result[i].infos = [];\r\n      }\r\n    }\r\n\r\n    const journeys: Journey[] = result.map((data: any) => {\r\n      return {\r\n        id: data.id,\r\n        startDate: data.startDate,\r\n        endDate: data.endDate,\r\n        distance: data.distance,\r\n        userId: data.userId,\r\n        vehicleId: data.vehicleId,\r\n        infosJourney: data.infos,\r\n        syncStatus: data.syncStatus || SyncStatus.Synced,\r\n        lastSyncDate: data.lastSyncDate ? new Date(data.lastSyncDate) : undefined\r\n      };\r\n    });\r\n    return journeys;\r\n  }\r\n\r\n  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {\r\n    const toRad = (value: number) => (value * Math.PI) / 180;\r\n\r\n    const R = 6371000; // Raio da Terra em metros\r\n    const dLat = toRad(lat2 - lat1);\r\n    const dLon = toRad(lon2 - lon1);\r\n\r\n    const a =\r\n      Math.sin(dLat / 2) * Math.sin(dLat / 2) +\r\n      Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) *\r\n      Math.sin(dLon / 2) * Math.sin(dLon / 2);\r\n\r\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\r\n\r\n    return R * c; // Distância em metros\r\n  }\r\n\r\n  /**\r\n   * Updates the sync status of a journey\r\n   * @param journey The journey to update\r\n   * @returns True if the update was successful\r\n   */\r\n  async updateJourneySync(journey: Journey): Promise<boolean> {\r\n    try {\r\n      // Convert journey to database format\r\n      const journeyForDb = {\r\n        ...journey,\r\n        lastSyncDate: journey.lastSyncDate ? journey.lastSyncDate.toISOString() : null\r\n      };\r\n\r\n      // Remove infosJourney property as it's not stored in the journey table\r\n      if (journeyForDb.infosJourney) {\r\n        delete journeyForDb.infosJourney;\r\n      }\r\n\r\n      await this.dataStorageService.update(\r\n        this.tableName,\r\n        journeyForDb,\r\n        `id = '${journey.id}'`\r\n      );\r\n\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Error updating journey sync status:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gets journeys that need to be synchronized\r\n   * @param userId The user ID\r\n   * @returns Array of journeys that need to be synchronized\r\n   */\r\n  async getPendingSyncJourneys(userId: string): Promise<Journey[]> {\r\n    const journeys = await this.getAllJourneysByUser(userId);\r\n    return journeys.filter(j =>\r\n      j.syncStatus === SyncStatus.PendingCreate ||\r\n      j.syncStatus === SyncStatus.PendingUpdate ||\r\n      j.syncStatus === SyncStatus.PendingDelete\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Sends a journey to the server for synchronization\r\n   * @param journey The journey to send\r\n   * @returns True if the journey was sent successfully\r\n   */\r\n  async sendJourneyToSync(journey: JourneySyncRequestDto): Promise<boolean> {\r\n    try {\r\n      const url = this.apiService.getUrl('journey/SyncJourney');\r\n      const result = await this.http.post(url, journey).toPromise();\r\n      return true; // Se chegou até aqui, a requisição foi bem-sucedida\r\n    } catch (error) {\r\n      console.error('Error sending journey to sync:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,EAAE,GAAG;AACZ,IAAE,eAAe,UAAU,IAAI,MAAM,CAAC,GAAG;AAAA,IACvC,IAAI,GAAG,GAAG;AACR,aAAO,IAAI,MAAM,CAAC,GAAG;AAAA,QACnB,IAAI,GAAG,GAAG;AACR,iBAAO,CAAC,GAAG,GAAG,MAAM;AAClB,kBAAM,IAAI,EAAE,UAAU,QAAQ,CAAC;AAC/B,gBAAI,MAAM,QAAQ;AAChB,gBAAE,IAAI,MAAM,oBAAoB,CAAC,YAAY,CAAC;AAC9C;AAAA,YACF;AACA,gBAAI,OAAO,EAAE,CAAC,KAAK,YAAY;AAC7B,gBAAE,IAAI,MAAM,UAAU,CAAC,kCAAkC,CAAC,EAAE,CAAC;AAC7D;AAAA,YACF;AACA,aAAC,MAAY;AACX,kBAAI;AACF,sBAAM,IAAI,MAAM,EAAE,CAAC,EAAE,CAAC;AACtB,kBAAE,CAAC;AAAA,cACL,SAAS,GAAG;AACV,kBAAE,CAAC;AAAA,cACL;AAAA,YACF,IAAG;AAAA,UACL;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;AACA,SAAS,EAAE,GAAG;AACZ,IAAE,eAAe,UAAU,IAAI,MAAM,CAAC,GAAG;AAAA,IACvC,IAAI,GAAG,GAAG;AACR,aAAO,EAAE,QAAQ,QAAQ,CAAC;AAAA,IAC5B;AAAA,EACF,CAAC;AACH;AACA,SAAS,EAAE,IAAI,OAAI;AACjB,SAAO,SAAS,QAAQ,OAAO,iBAAiB,OAAO,kBAAkB,CAAC,GAAG,OAAO,cAAc,UAAU,CAAC,IAAI,EAAE,MAAM,IAAI,OAAO,YAAY,UAAU,EAAE,MAAM;AACpK;AAtCA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAEM;AAFN,IAAAA,YAAA;AAAA;AAAA;AAAA;AACA;AAKA;AAJA,IAAM,cAAc,eAAe,eAAe;AAAA,MAChD,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,eAAe,CAAC;AAAA,IAC7D,CAAC;AACD,MAAc;AAAA;AAAA;;;ACLd,IAAAC,oBAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IACM;AADN,IAAAC,YAAA;AAAA;AAAA;AAAA;AAMA,IAAAC;AALA,IAAM,SAAS,eAAe,UAAU;AAAA,MACtC,SAAS,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,UAAU,CAAC;AAAA,MAC1D,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,UAAU,CAAC;AAAA,MACtD,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,UAAU,CAAC;AAAA,IACxD,CAAC;AAAA;AAAA;;;ACLD,IAWa;AAXb;;;;AACA,IAAAC;AAIA,IAAAA;AACA;;;AAKM,IAAO,sBAAP,MAAO,oBAAkB;MAe7B,YAAoB,oBAAsC;AAAtC,aAAA,qBAAA;AAdZ,aAAA,YAAoB;AACpB,aAAA,mBAAwB;AACxB,aAAA,oBAA4B;AAC5B,aAAA,sBAAsB;AAItB,aAAA,gBAAwB;AACxB,aAAA,oBAA4B;AAG5B,aAAA,wBAAwB,IAAI,gBAAyB,KAAK;AAC3D,aAAA,kBAAkB,KAAK,sBAAsB,aAAY;AAI9D,aAAK,UAAU,KAAK;MAEtB;;MAGA,cAAc,WAAiB;AAC7B,aAAK,oBAAoB,KAAK,IAAG;AACjC,aAAK,sBAAsB;AAC3B,aAAK,YAAY;AAGjB,aAAK,gBAAgB;AACrB,aAAK,oBAAoB;AAGzB,aAAK,sBAAsB,KAAK,IAAI;AAEpC,aAAK,mBAAmB,YAAY,MAAW;AAC7C,cAAI;AACF,oBAAQ,IAAI,qCAAqC;AAEjD,kBAAM,WAAW,MAAM,KAAK,mBAAkB;AAC9C,gBAAI,UAAU;AACZ,sBAAQ,IAAI,6BAAuB,QAAQ;AAG3C,kBAAI;AACF,wBAAQ,IAAI,8BAA8B;AAC1C,sBAAM,oBAAoB,MAAM,KAAK,eAAe,QAAQ;AAC5D,wBAAQ,IAAI,6BAA6B,iBAAiB;cAC5D,SAAS,OAAO;AACd,wBAAQ,MAAM,2BAA2B,KAAK;cAChD;AAEA,kBAAI;AACF,wBAAQ,IAAI,gCAAgC;AAC5C,sBAAM,uBAAuB,MAAM,KAAK,kBAAkB,QAAQ;AAClE,wBAAQ,IAAI,gCAAgC,oBAAoB;cAClE,SAAS,OAAO;AACd,wBAAQ,MAAM,8BAA8B,KAAK;cACnD;AAGA,kBAAI;AACF,wBAAQ,IAAI,+BAAyB;AACrC,sBAAM,KAAK,aAAa,QAAQ;AAChC,wBAAQ,IAAI,qCAA+B;cAC7C,SAAS,OAAO;AACd,wBAAQ,MAAM,qCAA+B,KAAK;cACpD;YACF,OAAO;AACL,sBAAQ,IAAI,gDAAoC;YAClD;UACF,SAAS,OAAO;AACd,oBAAQ,MAAM,kCAAkC,KAAK;UACvD;QACF,IAAG,GAAK;AAER,gBAAQ,IAAI,sCAAsC,SAAS;MAC7D;;MAGA,aAAa,WAAiB;AAC5B,YAAI,KAAK,kBAAkB;AACzB,wBAAc,KAAK,gBAAgB;AACnC,eAAK,mBAAmB;AAGxB,eAAK,sBAAsB,KAAK,KAAK;AAErC,kBAAQ,IAAI,oCAAoC,SAAS;QAC3D;MACF;;MAGc,qBAAkB;;AAC9B,cAAI;AACF,kBAAM,cAAc,MAAM,YAAY,mBACpC;cAAE,oBAAoB;cACpB,SAAS;aACT;AAEJ,kBAAM,WAAwB;cAC5B,IAAI,KAAK,iBAAgB;cACzB,WAAW,KAAK;cAChB,UAAU,YAAY,OAAO;cAC7B,WAAW,YAAY,OAAO;cAC9B,YAAW,oBAAI,KAAI,GAAG,YAAW;;;AAEnC,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,sCAAgC,KAAK;AACnD,mBAAO;UACT;QACF;;;MAGc,kBAAe;;AAC3B,cAAI;AACF,kBAAM,WAAW,MAAM,YAAY,mBAAkB;AACrD,kBAAM,QAAQ,SAAS,OAAO,QAAQ,SAAS,OAAO,QAAQ,MAAM;AACpE,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,+BAA+B,KAAK;AAClD,mBAAO;UACT;QACF;;;MAGc,aAAa,UAAqB;;AAC9C,cAAI;AAEF,kBAAM,KAAK,QAAQ,OAAO,eAAe,QAAQ;AACjD,oBAAQ,IAAI,4BAAsB,QAAQ;UAC5C,SAAS,OAAO;AACd,oBAAQ,MAAM,uCAAiC,KAAK;UACtD;QACF;;;MAGQ,mBAAgB;AACtB,eAAO,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;MACnD;MAEM,eAAe,UAAqB;;AACxC,cAAI;AAEF,kBAAM,eAAe,MAAM,KAAK,gBAAe;AAC/C,kBAAM,cAAc,KAAK,IAAG;AAE5B,gBAAI,KAAK,gBAAgB,KAAK,KAAK,oBAAoB,GAAG;AACxD,oBAAM,kBAAkB,KAAK,gBAAgB;AAC7C,oBAAM,kBAAkB,cAAc,KAAK,qBAAqB;AAEhE,kBAAI,iBAAiB,GAAG;AACtB,sBAAM,eAAe,kBAAkB;AAEvC,wBAAQ,IAAI,wBAAwB,KAAK,aAAa,iBAAiB,YAAY,OAAO;AAC1F,wBAAQ,IAAI,wBAAkB,YAAY,SAAS;AAGnD,oBAAI,eAAe,IAAI;AACrB,0BAAQ,IAAI,yCAAyC;AACrD,2BAAS,iBAAiB;AAC1B,wBAAM,KAAK,aAAa,QAAQ;AAGhC,uBAAK,gBAAgB;AACrB,uBAAK,oBAAoB;AAEzB,yBAAO;gBACT;cACF;YACF;AAGA,iBAAK,gBAAgB;AACrB,iBAAK,oBAAoB;AAGzB,mBAAO,MAAM,KAAK,+BAA+B,QAAQ;UAE3D,SAAS,OAAO;AACd,oBAAQ,MAAM,2BAA2B,KAAK;AAC9C,mBAAO;UACT;QACF;;MAEc,+BAA+B,UAAqB;;AAChE,iBAAO,IAAI,QAAQ,CAAO,YAAW;AACnC,gBAAI,aAAa;AACjB,gBAAI,iBAAsB;AAE1B,gBAAI;AAEF,oBAAM,oBAAoB,MAAM,KAAK,wBAAuB;AAC5D,kBAAI,CAAC,mBAAmB;AACtB,wBAAQ,IAAI,0CAAoC;AAChD,wBAAQ,KAAK;AACb;cACF;AAEA,+BAAiB,MAAM,OAAO,YAAY,SAAS,CAAC,UAAS;AAC3D,sBAAM,EAAE,aAAY,IAAK;AAEzB,qBAAI,6CAAc,MAAK,aAAa,IAAI,KAAK;AAC3C,0BAAQ,IAAI,kDAAkD;AAE9D,2BAAS,iBAAiB;AAC1B,uBAAK,aAAa,QAAQ;AAG1B,sBAAI,gBAAgB;AAClB,mCAAe,OAAM;kBACvB;AAEA,sBAAI,CAAC,YAAY;AACf,iCAAa;AACb,4BAAQ,IAAI;kBACd;gBACF;cACF,CAAC;AAGD,yBAAW,MAAK;AACd,oBAAI,CAAC,YAAY;AACf,+BAAa;AACb,sBAAI,gBAAgB;AAClB,mCAAe,OAAM;kBACvB;AACA,0BAAQ,KAAK;gBACf;cACF,GAAG,GAAI;YAET,SAAS,OAAO;AACd,sBAAQ,MAAM,oDAA8C,KAAK;AACjE,kBAAI,CAAC,YAAY;AACf,6BAAa;AACb,wBAAQ,KAAK;cACf;YACF;UACF,EAAC;QACH;;MAEc,0BAAuB;;AACnC,cAAI;AAEF,kBAAM,eAAe,MAAM,OAAO,YAAY,SAAS,MAAK;YAAE,CAAC;AAC/D,gBAAI,cAAc;AAChB,2BAAa,OAAM;AACnB,qBAAO;YACT;AACA,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,6DAA6D,KAAK;AAChF,mBAAO;UACT;QACF;;MAGM,kBAAkB,UAAqB;;AAC3C,gBAAM,WAAW,MAAM,KAAK,gBAAe;AAE1C,cAAI,WAAW,KAAK;AACf,oBAAQ,IAAI,4BAA4B;AACxC,qBAAS,iBAAiB;AAC1B,iBAAK,aAAa,QAAQ;AAC1B,mBAAO;UACZ;AACG,iBAAO;QACb;;;;uCA3QW,qBAAkB,mBAAA,kBAAA,CAAA;IAAA;2FAAlB,qBAAkB,SAAlB,oBAAkB,WAAA,YAFjB,OAAM,CAAA;AAEd,IAAO,qBAAP;;0EAAO,oBAAkB,CAAA;cAH9B;eAAW;UACV,YAAY;SACb;;;;;;;ACVD,IAmBa;AAnBb;;;;AACA;AASA;;;;;;;;;;AASM,IAAO,yBAAP,MAAO,uBAAqB;MAE9B,YACU,gBACA,oBACA,UACA,oBACA,gBACA,gBACA,MACA,YAAsB;AAPtB,aAAA,iBAAA;AACA,aAAA,qBAAA;AACA,aAAA,WAAA;AACA,aAAA,qBAAA;AACA,aAAA,iBAAA;AACA,aAAA,iBAAA;AACA,aAAA,OAAA;AACA,aAAA,aAAA;AAKK,aAAA,YAAY;AAHzB,aAAK,WAAW,KAAK,SAAS,GAAG,WAAW,KAAK,KAAK,SAAS,GAAG,SAAS;MAC7E;MAMI,OAAI;;AAER,gBAAM,KAAK,mBAAmB,KAAI;QACpC;;;;;;MAMM,eAAY;;AAChB,gBAAM,SAAS,MAAM,KAAK,eAAe,UAAS;AAGlD,gBAAM,cAAc,MAAM,KAAK,eAAe,YAAY,MAAM;AAChE,cAAI,CAAC,aAAa;AAChB,oBAAQ,MAAM,4DAA4D;AAC1E,mBAAO;UACT;AAGA,gBAAM,iBAAiB,MAAM,KAAK,eAAe,kBAAkB,MAAM;AACzE,cAAI,CAAC,gBAAgB;AACnB,oBAAQ,MAAM,8CAA8C;AAC5D,mBAAO;UACT;AAEA,gBAAM,KAAK,WAAM;AACjB,gBAAM,aAAY,oBAAI,KAAI,GAAG,YAAW;AAGxC,gBAAM,aAAa,KAAK,eAAe,YAAW,IAAK,WAAW,gBAAgB,WAAW;AAG7F,gBAAM,KAAK,mBAAmB,OAAO,KAAK,WAAW;YACnD;YACA;YACA;YACA,WAAW,eAAe;YAC1B;YACA,cAAc;WACf;AAGD,eAAK,mBAAmB,cAAc,EAAE;AACxC,iBAAO;QACT;;MAEM,WAAW,WAAiB;;AAChC,eAAK,mBAAmB,aAAa,SAAS;AAC9C,gBAAM,WAAU,oBAAI,KAAI,GAAG,YAAW;AAItC,gBAAM,SAAS,MAAM,KAAK,mBAAmB,OAAO,eAAe,sBAAsB,SAAS,0BAA0B;AAG5H,cAAI,eAA8B,CAAA;AAClC,cAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,2BAAe;UACjB,WAAW,QAAQ;AAEjB,gBAAI;AACF,6BAAe,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAA;YAClD,SAAS,OAAO;AACd,sBAAQ,MAAM,2CAA2C,KAAK;YAChE;UACF;AAEA,cAAI,gBAAgB;AAEpB,cAAI,gBAAgB,aAAa,SAAS,GAAG;AAC3C,qBAAS,IAAI,GAAG,IAAI,aAAa,SAAS,GAAG,KAAK;AAChD,oBAAM,QAAQ,aAAa,CAAC;AAC5B,oBAAM,MAAM,aAAa,IAAI,CAAC;AAC9B,+BAAiB,KAAK,kBACpB,MAAM,UAAU,MAAM,WACtB,IAAI,UAAU,IAAI,SAAS;YAE/B;UACF;AAGA,gBAAM,aAAa,KAAK,eAAe,YAAW,IAAK,WAAW,gBAAgB,WAAW;AAG7F,gBAAM,KAAK,mBAAmB,OAC5B,KAAK,WACL;YACE;YACA,UAAU;YACV;YACA,cAAc;aAEhB,SAAS,SAAS,GAAG;QAEzB;;MAEM,YAAY,WAAmB,UAAkB,WAAiB;;AACtE,gBAAM,KAAK,WAAM;AACjB,gBAAM,aAAY,oBAAI,KAAI,GAAG,YAAW;AAGxC,gBAAM,aAAa,KAAK,eAAe,YAAW,IAAK,WAAW,gBAAgB,WAAW;AAG7F,gBAAM,KAAK,mBAAmB,OAAO,eAAe;YAClD;YACA;YACA;YACA;YACA;YACA;YACA,cAAc;WACf;QACH;;MAEM,kBAAe;;AAEnB,cAAI,KAAK,IAAI;AACX,iBAAK,GAAG,MAAK;AACb,iBAAK,KAAK;UACX;QACH;;MAEM,iBAAc;;AAElB,cAAI,SAAS,MAAM,KAAK,mBAAmB,OAAO,KAAK,WAAW,yBAAyB;AAE3F,cAAG,CAAC,UAAU,OAAO,WAAW;AAC9B,mBAAO,CAAA;AAET,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,kBAAM,YAAY,OAAO,CAAC,EAAE;AAE5B,kBAAM,eAAe,MAAM,KAAK,mBAAmB,OAAO,eAAe,sBAAsB,SAAS,0BAA0B;AAGlI,gBAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,qBAAO,CAAC,EAAE,QAAQ,aAAa,OAAO,CAAC,SAAsB,KAAK,cAAc,SAAS;YAC3F,OAAO;AACL,qBAAO,CAAC,EAAE,QAAQ,CAAA;YACpB;UACF;AAEA,gBAAM,WAAsB,OAAO,IAAI,CAAC,SAAa;AACnD,mBAAO;cACL,IAAI,KAAK;cACT,WAAW,KAAK;cAChB,SAAS,KAAK;cACd,UAAU,KAAK;cACf,QAAQ,KAAK;cACb,WAAW,KAAK;cAChB,cAAc,KAAK;cACnB,YAAY,KAAK,cAAc,WAAW;cAC1C,cAAc,KAAK,eAAe,IAAI,KAAK,KAAK,YAAY,IAAI;;UAEpE,CAAC;AACD,iBAAO;QACT;;MAEM,qBAAqB,QAAc;;AAGvC,cAAI,SAAS,MAAM,KAAK,mBAAmB,OAAO,KAAK,WAAW,0BAA0B;AAG5F,cAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,qBAAS,OAAO,OAAO,CAAC,SAAc,KAAK,WAAW,MAAM;UAC9D,OAAO;AACL,mBAAO,CAAA;UACT;AAEA,cAAG,CAAC,UAAU,OAAO,WAAW;AAC9B,mBAAO,CAAA;AAET,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,kBAAM,YAAY,OAAO,CAAC,EAAE;AAE5B,kBAAM,eAAe,MAAM,KAAK,mBAAmB,OAAO,eAAe,sBAAsB,SAAS,0BAA0B;AAGlI,gBAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,qBAAO,CAAC,EAAE,QAAQ,aAAa,OAAO,CAAC,SAAsB,KAAK,cAAc,SAAS;YAC3F,OAAO;AACL,qBAAO,CAAC,EAAE,QAAQ,CAAA;YACpB;UACF;AAEA,gBAAM,WAAsB,OAAO,IAAI,CAAC,SAAa;AACnD,mBAAO;cACL,IAAI,KAAK;cACT,WAAW,KAAK;cAChB,SAAS,KAAK;cACd,UAAU,KAAK;cACf,QAAQ,KAAK;cACb,WAAW,KAAK;cAChB,cAAc,KAAK;cACnB,YAAY,KAAK,cAAc,WAAW;cAC1C,cAAc,KAAK,eAAe,IAAI,KAAK,KAAK,YAAY,IAAI;;UAEpE,CAAC;AACD,iBAAO;QACT;;MAEQ,kBAAkB,MAAc,MAAc,MAAc,MAAY;AAC9E,cAAM,QAAQ,CAAC,UAAmB,QAAQ,KAAK,KAAM;AAErD,cAAM,IAAI;AACV,cAAM,OAAO,MAAM,OAAO,IAAI;AAC9B,cAAM,OAAO,MAAM,OAAO,IAAI;AAE9B,cAAM,IACJ,KAAK,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC,IACtC,KAAK,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,IAAI,MAAM,IAAI,CAAC,IAC5C,KAAK,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC;AAExC,cAAM,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC;AAEvD,eAAO,IAAI;MACb;;;;;;MAOM,kBAAkB,SAAgB;;AACtC,cAAI;AAEF,kBAAM,eAAe,iCAChB,UADgB;cAEnB,cAAc,QAAQ,eAAe,QAAQ,aAAa,YAAW,IAAK;;AAI5E,gBAAI,aAAa,cAAc;AAC7B,qBAAO,aAAa;YACtB;AAEA,kBAAM,KAAK,mBAAmB,OAC5B,KAAK,WACL,cACA,SAAS,QAAQ,EAAE,GAAG;AAGxB,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,uCAAuC,KAAK;AAC1D,mBAAO;UACT;QACF;;;;;;;MAOM,uBAAuB,QAAc;;AACzC,gBAAM,WAAW,MAAM,KAAK,qBAAqB,MAAM;AACvD,iBAAO,SAAS,OAAO,OACrB,EAAE,eAAe,WAAW,iBAC5B,EAAE,eAAe,WAAW,iBAC5B,EAAE,eAAe,WAAW,aAAa;QAE7C;;;;;;;MAOM,kBAAkB,SAA8B;;AACpD,cAAI;AACF,kBAAM,MAAM,KAAK,WAAW,OAAO,qBAAqB;AACxD,kBAAM,SAAS,MAAM,KAAK,KAAK,KAAK,KAAK,OAAO,EAAE,UAAS;AAC3D,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,kCAAkC,KAAK;AACrD,kBAAM;UACR;QACF;;;;uCA3SW,wBAAqB,mBAAA,cAAA,GAAA,mBAAA,kBAAA,GAAA,mBAAA,QAAA,GAAA,mBAAA,kBAAA,GAAA,mBAAA,cAAA,GAAA,mBAAA,cAAA,GAAA,mBAAA,UAAA,GAAA,mBAAA,UAAA,CAAA;IAAA;8FAArB,wBAAqB,SAArB,uBAAqB,WAAA,YAFpB,OAAM,CAAA;AAEd,IAAO,wBAAP;;0EAAO,uBAAqB,CAAA;cAHjC;eAAW;UACV,YAAY;SACb;;;;;", "names": ["init_esm", "init_definitions", "init_esm", "init_definitions", "init_esm"], "x_google_ignoreList": [0, 1, 2, 3, 4]}