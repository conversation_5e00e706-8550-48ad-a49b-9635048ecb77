<ion-content class="login" [fullscreen]="true">
  <div class="login-header">
    <img src="assets/images/logo-small.svg" alt="Good Driver Logo" class="logo-small" />
    <h1>Good Driver</h1>
  </div>

  <form [formGroup]="loginForm" (ngSubmit)="login()">
    <div class="login-container">
      <h2>Entrar</h2>

      <ion-item class="custom-input">
        <ion-icon name="mail-outline" slot="start"></ion-icon>
        <ion-input type="email" formControlName="email" placeholder="Email"></ion-input>
      </ion-item>      

      <ion-item class="custom-input">
        <ion-icon name="lock-closed-outline" slot="start"></ion-icon>
        <ion-input type="password" formControlName="password" placeholder="Senha"></ion-input>
      </ion-item>

      <ion-button expand="full" type="submit" color="primary" class="login-btn">
        <ion-icon name="log-in-outline" slot="start"></ion-icon>
        Entrar
      </ion-button>

      <div class="links">
        <a [routerLink]="['/signup']" routerLinkActive="active-link">
          <ion-icon name="person-add-outline"></ion-icon> Criar conta
        </a>
        <a [routerLink]="['/recover']" routerLinkActive="active-link">
          <ion-icon name="help-circle-outline"></ion-icon> Esqueci a senha
        </a>
      </div>
    </div>
  </form>
</ion-content>
